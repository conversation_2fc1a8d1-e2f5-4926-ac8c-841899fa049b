package customer

import (
	"github.com/samber/lo"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"unicode/utf8"

	"github.com/golang/protobuf/jsonpb"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/gamma/api/typesv2"
	federal3 "github.com/epifi/gamma/vendorgateway/openbanking/customer/vkyc"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	employmentPb "github.com/epifi/gamma/api/employment"
	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	vendorfedral "github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/gamma/pkg/banking"
	"github.com/epifi/gamma/vendorgateway/config/genconf"
	"github.com/epifi/gamma/vendorgateway/federal"
	customerFederal "github.com/epifi/gamma/vendorgateway/openbanking/customer/federal"
	opfederal "github.com/epifi/gamma/vendorgateway/openbanking/federal"
	federal2 "github.com/epifi/gamma/vendorgateway/vendorapi/cryptor/federal"
)

var (
	CreateCustomerRequestIdNotFound rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(
			uint32(customer.CreateCustomerResponse_NO_REQUESTID),
			"Request id not found",
			"request id not found",
		)
	}

	CreateCustomerInProgress rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(uint32(customer.CreateCustomerResponse_CUSTOMER_CREATION_INPROGRESS),
			"Create customer in progress",
			"create customer is in progress",
		)
	}

	CreateCustomerFederalInternal rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(rpc.StatusAborted().Code, "federal internal error")
	}

	CreateCustomerInvalidContactDetails = rpc.NewStatusWithoutDebug(
		uint32(customer.CreateCustomerResponse_INVALID_INPUT), "Invalid Contact details",
	)
	CreateCustomerEmptyAddressLine = rpc.NewStatusWithoutDebug(
		uint32(customer.CreateCustomerResponse_INVALID_INPUT), "empty address line",
	)

	ErrEmptyAddressLine = errors.New("empty address line")
)

const (
	DefaultAnnualSalary  = "500000"
	DefaultReligion      = "OTH"
	DefaultCommunity     = "OTH"
	DefaultOccupation    = "SALAR"
	DefaultSolId         = "5555"
	NewSolId             = "5556"
	DefaultQualification = "NA"
	DefaultEmployment    = "Other"
	DefaultDesignation   = "Manager"
	CKYCVKYCSpouseOcc    = "CKYCVKYC"
	// MINEKYCSpouseOcc indicates OTP based Min KYC CIF creation
	MINEKYCSpouseOcc = "MINEKYC"
	// CKYCEKYCSpouseOcc indicates CKYC + EKYC onboarding
	CKYCEKYCSpouseOcc = "CKYCEKYC"
	// CKYCSpouseOcc indicates CKYC based full KYC
	CKYCSpouseOcc = "CKYC"
	// VKYCSpouseOcc indicates VKYC based CIF creation
	VKYCSpouseOcc = "VKYC"
	// MINKYCCONVERTSpouseOcc indicates min kyc dedupe customer converted to full kyc via VKYC
	MINKYCCONVERTSpouseOcc = "MINKYCCONVERT"
	BKYCSpouseOcc          = "BIOMETRICKYC"
)

var customerCreationErrorCodes = map[string]rpc.StatusFactory{
	"OBE0000": rpc.StatusOk,
	"OBE0001": rpc.StatusPermissionDenied,
	"OBE0002": rpc.StatusPermissionDenied,
	"OBE0003": rpc.StatusAlreadyExists,
	"OBE0004": CreateCustomerRequestIdNotFound,
	"OBE0007": CreateCustomerFederalInternal,
	"OBE0016": rpc.StatusInvalidArgument,
	"OBE0019": rpc.StatusInvalidArgument,
	"OBE0030": rpc.StatusAlreadyExists,
	"OBE0034": CreateCustomerInProgress,
	"OBE0050": rpc.StatusInvalidArgument,
	"OBE0051": rpc.StatusInvalidArgument,
	"OBE0052": rpc.StatusInvalidArgument,
	"OBE0059": rpc.StatusInvalidArgument,
}

var createCustMonitoredVendorErrorCodes = []string{
	"OBE0001",
	"OBE0002",
	"OBE0007",
}

const (
	MethodPost = "POST"

	InvalidAddressDetailsReason = "ctdp3059e: element '#xscd(/schemaelement::ob_cif_opening_req/" +
		"type::0/model::sequence/schemaelement::body/type::0/model::sequence/schemaelement::cif_" +
		"request/type::0/model::sequence/schemaelement::contact_details"
)

func (s *Service) NewFederalRequestWithCtx(ctx context.Context) vendorapi.SyncRequestFactory {
	return func(req proto.Message) vendorapi.SyncRequest {
		return s.NewFederalRequest(ctx, req, s.conf)
	}
}

// NewFederalRequest creates a new request for federal bank APIs depending on type of proto message.
// nolint: funlen
func (s *Service) NewFederalRequest(ctx context.Context, req proto.Message, dynConf *genconf.Config) vendorapi.SyncRequest {
	numbersRegexp := regexp.MustCompile(`[0-9]`)
	if err := registerVendorErrorCode(s.conf); err != nil {
		logger.ErrorNoCtx(fmt.Sprintf("Failed to register vendor error code: %v", err))
		return nil
	}

	switch v := req.(type) {
	case *customer.CreateLoanCustomerRequest:
		return &customerFederal.CreateLoanCustomerReq{
			Method:                         MethodPost,
			Req:                            req.(*customer.CreateLoanCustomerRequest),
			Url:                            dynConf.Application().CreateLoanCustomerURL(),
			Conf:                           s.conf.Application().Lending().PreApprovedLoan().FederalNTB(),
			FieldSanitizer:                 fieldSanitizer,
			UseOccupationTypeInCifCreation: dynConf.Flags().UseNewOccupationInCifCreation(),
		}
	case *customer.LoanCustomerCreationStatusRequest:
		return &customerFederal.LoanCustomerCreationStatusReq{
			Method: MethodPost,
			Req:    req.(*customer.LoanCustomerCreationStatusRequest),
			Url:    dynConf.Application().LoanCustomerCreationStatusURL(),
			Conf:   s.conf.Application().Lending().PreApprovedLoan().FederalNTB(),
		}
	case *customer.CreateCustomerRequest:
		return &createCustomerReq{
			method:                         MethodPost,
			req:                            req.(*customer.CreateCustomerRequest),
			url:                            dynConf.Application().CreateCustomerURL(),
			callBackUrl:                    dynConf.Application().CreateCustomerCallBackUrl(),
			UseNewSolID:                    dynConf.Flags().UseNewSolID(),
			useOccupationTypeInCifCreation: dynConf.Flags().UseNewOccupationInCifCreation(),
			conf:                           s.conf,
		}
	case *customer.CheckCustomerStatusRequest:
		return &customerFederal.CheckCustomerStatusReq{
			Method: MethodPost,
			Req:    req.(*customer.CheckCustomerStatusRequest),
			Url:    dynConf.Application().Federal().CustomerCreationEnquiryStatusURL,
		}
	case *customer.DedupeCheckRequest:
		return &customerFederal.DedupeRequest{
			Method: MethodPost,
			Req:    req.(*customer.DedupeCheckRequest),
			Url:    dynConf.Application().DedupeCheckURL(),
			Ctx:    ctx,
		}
	case *customer.FetchCustomerDetailsRequest:
		return &customerFederal.FetchCustomerDetailsReq{
			Method: MethodPost,
			Req:    req.(*customer.FetchCustomerDetailsRequest),
			Url:    dynConf.Application().FetchCustomerDetailsUrl(),
		}
	case *customer.EnquireVKYCStatusRequest:
		return &federal3.EnquireVkycReq{
			Method:  MethodPost,
			Req:     req.(*customer.EnquireVKYCStatusRequest),
			Url:     dynConf.Application().EnquireVKYCStatusUrl(),
			Secrets: dynConf.Secrets(),
		}
	case *customer.UpgradeKYCLevelRequest:
		return &customerFederal.UpgradeKycLevelRequest{
			Ctx: ctx,
			Url: dynConf.Application().Federal().CustomerDetailsInsertURL,
			Req: req.(*customer.UpgradeKYCLevelRequest),
		}
	case *customer.CreateCustomerForNonResidentRequest:
		return &customerFederal.CreateCustomerForNonResidentRequest{
			Ctx:            ctx,
			Url:            dynConf.Application().Federal().CreateCustomerForNonResidentURL,
			FieldSanitizer: fieldSanitizer,
			Req:            req.(*customer.CreateCustomerForNonResidentRequest),
			Secrets:        dynConf.Secrets(),
			NumbersRegexp:  numbersRegexp,
			TimeClient:     s.TimeClient,
		}
	case *customer.CheckCustomerStatusForNonResidentRequest:
		return &customerFederal.CheckCustomerStatusForNonResidentRequest{
			Ctx:     ctx,
			Url:     dynConf.Application().Federal().CheckCustomerStatusForNonResidentURL,
			Req:     req.(*customer.CheckCustomerStatusForNonResidentRequest),
			Secrets: dynConf.Secrets(),
		}
	default:
		logger.Error(ctx, fmt.Sprintf("Unsupported request type: %v", v))
		return nil
	}
}

func registerVendorErrorCode(dynConf *genconf.Config) error {
	err := vendorapi.MonitoredVendorErrorCodeRegistry.Register(dynConf.Application().FetchCustomerDetailsUrl(), customerFederal.FetchCustDetailsMonitoredVendorErrorCodes)
	if err != nil {
		return err
	}
	err = vendorapi.MonitoredVendorErrorCodeRegistry.Register(dynConf.Application().CreateCustomerURL(), createCustMonitoredVendorErrorCodes)
	if err != nil {
		return err
	}
	err = vendorapi.MonitoredVendorErrorCodeRegistry.Register(dynConf.Application().DedupeCheckURL(), customerFederal.DedupeMonitoredErrorCode)
	if err != nil {
		return err
	}
	return nil
}

// createCustomerReq provides functionality for creating an customer with Federal Bank's APIs.
type createCustomerReq struct {
	*federal.DefaultHeaderAdder
	*federal2.DefaultPGPSecuredExchange

	method                         string
	req                            *customer.CreateCustomerRequest
	url                            string
	callBackUrl                    string
	UseNewSolID                    bool
	useOccupationTypeInCifCreation bool
	conf                           *genconf.Config
}

func FormatDate(date *date.Date) string {
	d := fmt.Sprintf("%04v-%02v-%02v", date.GetYear(), date.GetMonth(), date.GetDay())
	return d
}

var empTypeToOccupationString = map[employmentPb.EmploymentType]string{
	employmentPb.EmploymentType_SELF_EMPLOYED: "SELFE",
	employmentPb.EmploymentType_SALARIED:      "SALAR",
	employmentPb.EmploymentType_RETIRED:       "RETIR",
	employmentPb.EmploymentType_OTHERS:        DefaultOccupation, // TODO: Federal occupation mapping to OTHER is not working in UAT, need to be fixed after discussion
	// If user did not give employment information, we will keep this as OTHER in customer creation
	employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED: DefaultOccupation,
	employmentPb.EmploymentType_BUSINESS_OWNER:              "SELFE",
	employmentPb.EmploymentType_FREELANCER:                  "SELFE",
	employmentPb.EmploymentType_WORKING_PROFESSIONAL:        "SELFE",
	employmentPb.EmploymentType_STUDENT:                     "STUDE",
	employmentPb.EmploymentType_HOMEMAKER:                   "HSEWF",
}

// Marshal provides the json for Federal bank's API call.
// nolint: funlen
func (c *createCustomerReq) Marshal() ([]byte, error) {
	request := c.req
	// Id proof mapping
	// TODO (keerthana) : To close on all the empty parameters and hard-coded params in following request
	proofEnumMapping := map[kycPb.IdProofType]string{
		kycPb.IdProofType_ID_PROOF_TYPE_UNSPECIFIED:           "GOVT", // Need to confirm if mpping is correct
		kycPb.IdProofType_PASSPORT:                            "PP",
		kycPb.IdProofType_VOTER_ID:                            "VTRID",
		kycPb.IdProofType_PAN:                                 "PAN",
		kycPb.IdProofType_DRIVING_LICENSE:                     "DL",
		kycPb.IdProofType_UID:                                 "AADHA", // Need to confirm if mpping is correct
		kycPb.IdProofType_NREGA_JOB_CARD:                      "NREGA",
		kycPb.IdProofType_NATIONAL_POPULATION_REGISTER_LETTER: "GOVT", // Need to confirm if mpping is correct
		kycPb.IdProofType_CKYC_RECORD:                         "GOVT", // Need to confirm if mpping is correct
	}

	employmentTypeMapping := map[employmentPb.EmploymentType]string{
		employmentPb.EmploymentType_SELF_EMPLOYED: "Self Employed",
		employmentPb.EmploymentType_SALARIED:      "Salaried",
		employmentPb.EmploymentType_RETIRED:       "Retired",
		employmentPb.EmploymentType_OTHERS:        "Other",
		// If user did not give employment information, we will keep this empty in customer creation
		employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED: DefaultEmployment,
		employmentPb.EmploymentType_BUSINESS_OWNER:              "Self Employed",
		employmentPb.EmploymentType_FREELANCER:                  "Self Employed",
		employmentPb.EmploymentType_WORKING_PROFESSIONAL:        "Self Employed",
		employmentPb.EmploymentType_STUDENT:                     "Unemployed",
		employmentPb.EmploymentType_HOMEMAKER:                   "Housewife",
	}

	name, err := fieldSanitizer.SanitiseFullName(request.GetName())
	if err != nil {
		return nil, err
	}
	personalDetails := &vendorfedral.PersonalDetails{
		Title:         opfederal.GetTitleForFederal(request.GetName().GetHonorific(), request.Gender.String()),
		FirstName:     name.GetFirstName(),
		MiddleName:    name.GetMiddleName(),
		LastName:      name.GetLastName(),
		FatherName:    fieldSanitizer.SanitiseNameComponent(request.GetFatherName()),
		MotherName:    fieldSanitizer.SanitiseNameComponent(request.GetMotherName()),
		DateOfBirth:   FormatDate(request.GetDateOfBirth()),
		Gender:        opfederal.GetGenderCode(request.GetGender().String()),
		MaritalStatus: "NA", // Not mandatory
		UidNo:         request.GetUidNo(),
	}

	commAddressLines := request.GetCurrentAddress().GetAddressLines()
	commAddressLines = append(commAddressLines, request.GetCurrentAddress().GetSublocality())
	house, place, err := customerFederal.GetHouseAndPlace(commAddressLines)
	if err != nil {
		return nil, err
	}

	communicationAddress := &vendorfedral.Address{
		House:      house,
		Place:      place,
		CityCd:     request.GetCurrentAddress().GetLocality(),
		State_Cd:   request.GetCurrentAddress().GetAdministrativeArea(),
		Country_Cd: request.GetCurrentAddress().GetRegionCode(),
		PinCode:    request.GetCurrentAddress().GetPostalCode(),
		LandLine:   request.GetPhoneNumber().ToString(),
	}

	permAddressLines := request.GetPermanentAddress().GetAddressLines()
	permAddressLines = append(permAddressLines, request.GetPermanentAddress().GetSublocality())
	house, place, err = customerFederal.GetHouseAndPlace(permAddressLines)
	if err != nil {
		return nil, err
	}

	permanentAddress := &vendorfedral.Address{
		House:      house,
		Place:      place,
		CityCd:     request.GetPermanentAddress().GetLocality(),
		State_Cd:   request.GetPermanentAddress().GetAdministrativeArea(),
		Country_Cd: request.GetPermanentAddress().GetRegionCode(),
		PinCode:    request.GetPermanentAddress().GetPostalCode(),
		LandLine:   request.GetPhoneNumber().ToString(),
	}

	contactDetails := &vendorfedral.ContactDetails{
		Mobile:               request.GetPhoneNumber().ToString(),
		Email:                request.GetEmail(),
		CommunicationAddress: communicationAddress,
		CaSameasPa:           opfederal.CheckIfAddressSame(request.GetPermanentAddress(), request.GetCurrentAddress()),
		PermanentAddress:     permanentAddress,
	}

	// evaluate tax slab by senior citizen status
	taxSlab := "TDSI"
	if banking.IsSeniorCitizen(request.GetDateOfBirth()) {
		taxSlab = "TDSIS"
	}

	employmentType := employmentTypeMapping[c.req.Type]
	annualIncome := fmt.Sprintf("%v", int(c.req.GetAnnualIncome()))
	if int(c.req.GetAnnualIncome()) == 0 {
		annualIncome = DefaultAnnualSalary
	}

	// All the hard_coded parameters hardcoded in Additional details needs to be given by federal.
	// TODO: followup with federal for the above
	spouseOcc := c.getSpouseOccupation(c.req.GetCustomerCreationFlow())
	if spouseOcc == "" {
		logger.ErrorNoCtx(fmt.Sprintf("no spouse occupation mapping for customer creation flow: %v", request.GetCustomerCreationFlow().String()))
		return nil, fmt.Errorf("invalid spouse occupation")
	}

	additionalDetails := &vendorfedral.AdditionalDetails{
		PanNo:            request.GetPanNumber(),
		AnnualIncome:     annualIncome,
		Religion:         DefaultReligion,
		Community:        DefaultCommunity,
		Qualification:    DefaultQualification,
		Occupation:       c.getOccupationCifString(),
		TaxSlab:          taxSlab,
		Employment:       employmentType,
		Designation:      DefaultDesignation,
		Form60:           "N",
		SpouseOccupation: spouseOcc,
	}

	if c.conf.Flags().UseNewFieldsInCifCreation() {
		disabilityType := "NR"
		if request.GetDisabilityType() == typesv2.DisabilityType_DISABILITY_TYPE_DIFFERENTLY_ABLED {
			disabilityType = "DA"
		}
		additionalDetails.CustomerStatus = disabilityType
		additionalDetails.Category = opfederal.CategoaryMapping[request.GetCategory()]

		additionalDetails.Occupation = opfederal.EmploymentTypeMapping[c.req.Type]
		occupation, ok := opfederal.OccTypeToOccupationString[c.req.GetOccupationType()]
		if !ok {
			return nil, status.Error(codes.InvalidArgument, "invalid occupation type")
		}
		additionalDetails.SubCategoryOccupation = occupation
	}

	proofOfIdentity := &vendorfedral.ProofDetails{
		Type:         proofEnumMapping[request.GetIdentityProof().GetType()],
		IdNumber:     fieldSanitizer.SanitiseIdNumber(request.GetIdentityProof().GetIdNumber()),
		IdIssueDate:  FormatDate(request.GetIdentityProof().GetIdIssueDate()),
		IdExpiryDate: FormatDate(request.GetIdentityProof().GetIdExpiryDate()),
	}

	proofOfAddress := &vendorfedral.ProofDetails{
		Type:         proofEnumMapping[request.GetAddressProof().GetType()],
		IdNumber:     fieldSanitizer.SanitiseIdNumber(request.GetAddressProof().GetIdNumber()),
		IdIssueDate:  FormatDate(request.GetAddressProof().GetIdIssueDate()),
		IdExpiryDate: FormatDate(request.GetAddressProof().GetIdExpiryDate()),
	}

	identificationDetails := &vendorfedral.IdentificationDetails{
		ProofOfIdentity: proofOfIdentity,
		ProofOfAddress:  proofOfAddress,
	}

	params := make(map[string]interface{})
	federal.AddCommonRequestParams(params)

	solId := request.GetSolId()
	if solId == "" {
		return nil, status.Error(codes.InvalidArgument, "empty sol id")
	}

	cifReq := &vendorfedral.Cif_Request{
		RespUrl:               c.callBackUrl,
		SenderCode:            fmt.Sprintf("%v", params["SenderCode"]),
		ServiceAccessId:       fmt.Sprintf("%v", params["ServiceAccessId"]),
		ServiceAccessCode:     fmt.Sprintf("%v", params["ServiceAccessCode"]),
		DeviceId:              request.GetDeviceDetails().GetDeviceId(),
		UserProfileId:         request.GetDeviceDetails().GetUserProfileId(),
		DeviceToken:           request.GetDeviceDetails().GetDeviceToken(),
		RequestId:             request.RequestId,
		SolId:                 solId,
		PersonalDetails:       personalDetails,
		ContactDetails:        contactDetails,
		AdditionalDetails:     additionalDetails,
		IdentificationDetails: identificationDetails,
		SignId:                request.GetSignImage(),
	}
	// Debugging logs in secure logging
	marshaller := protojson.MarshalOptions{AllowPartial: true}
	mreq, err := marshaller.Marshal(&vendorfedral.CreateCustomerRequest{CifRequest: cifReq})
	if err != nil {
		logger.SecureError(context.Background(), commonvgpb.Vendor_FEDERAL_BANK,
			fmt.Sprintf("request marshalling failed in customer creation, Request: %v", cifReq), zap.Error(err))
	}

	if c.conf.Flags().UseNewFieldsInCifCreation() {
		mreq, err = c.ConvertAndMarshal(&vendorfedral.CreateCustomerRequest{CifRequest: cifReq})
		logger.InfoNoCtx("Request to Federal Bank", zap.String("request", string(mreq)))
		if err != nil {
			logger.SecureError(context.Background(), commonvgpb.Vendor_FEDERAL_BANK,
				fmt.Sprintf("request marshalling failed in customer creation, Request: %v", cifReq), zap.Error(err))
		}
	}
	r, size := utf8.DecodeRuneInString(string(mreq))
	if r == utf8.RuneError {
		logger.SecureError(context.Background(), commonvgpb.Vendor_FEDERAL_BANK, "req utf8 decoding failed in customer creation",
			zap.Int(logger.LENGTH, size), zap.String(logger.REQUEST, string(mreq)))
	}

	return mreq, err
}

func (c *createCustomerReq) ConvertAndMarshal(protoReq *vendorfedral.CreateCustomerRequest) ([]byte, error) {
	cif := protoReq.GetCifRequest()
	CreateCustomerReqInVendorSpecifiedOrder := CreateCustomerRequestInVendorSpecifiedOrder{
		RespUrl:           cif.GetRespUrl(),
		SenderCode:        cif.GetSenderCode(),
		ServiceAccessId:   cif.GetServiceAccessId(),
		ServiceAccessCode: cif.GetServiceAccessCode(),
		DeviceId:          cif.GetDeviceId(),
		UserProfileId:     cif.GetUserProfileId(),
		DeviceToken:       cif.GetDeviceToken(),
		RequestId:         cif.GetRequestId(),
		SolId:             cif.GetSolId(),
		Personal_Details: PersonalDetails{
			Title:         cif.GetPersonalDetails().GetTitle(),
			FirstName:     cif.GetPersonalDetails().GetFirstName(),
			MiddleName:    cif.GetPersonalDetails().GetMiddleName(),
			LastName:      cif.GetPersonalDetails().GetLastName(),
			FatherName:    cif.GetPersonalDetails().GetFatherName(),
			MotherName:    cif.GetPersonalDetails().GetMotherName(),
			DateOfBirth:   cif.GetPersonalDetails().GetDateOfBirth(),
			Gender:        cif.GetPersonalDetails().GetGender(),
			MaritalStatus: cif.GetPersonalDetails().GetMaritalStatus(),
			Uid_No:        cif.GetPersonalDetails().GetUidNo(),
		},
		Contact_Details: ContactDetails{
			Mobile: cif.GetContactDetails().GetMobile(),
			Email:  cif.GetContactDetails().GetEmail(),
			Communication_Address: Address{
				House:      cif.GetContactDetails().GetCommunicationAddress().GetHouse(),
				Place:      cif.GetContactDetails().GetCommunicationAddress().GetPlace(),
				City_Cd:    cif.GetContactDetails().GetCommunicationAddress().GetCityCd(),
				State_Cd:   cif.GetContactDetails().GetCommunicationAddress().GetState_Cd(),
				Country_Cd: cif.GetContactDetails().GetCommunicationAddress().GetCountry_Cd(),
				PinCode:    cif.GetContactDetails().GetCommunicationAddress().GetPinCode(),
				LandLine:   cif.GetContactDetails().GetCommunicationAddress().GetLandLine(),
			},
			CA_Sameas_PA: cif.GetContactDetails().GetCaSameasPa(),
			Permanent_Address: Address{
				House:      cif.GetContactDetails().GetPermanentAddress().GetHouse(),
				Place:      cif.GetContactDetails().GetPermanentAddress().GetPlace(),
				City_Cd:    cif.GetContactDetails().GetPermanentAddress().GetCityCd(),
				State_Cd:   cif.GetContactDetails().GetPermanentAddress().GetState_Cd(),
				Country_Cd: cif.GetContactDetails().GetPermanentAddress().GetCountry_Cd(),
				PinCode:    cif.GetContactDetails().GetPermanentAddress().GetPinCode(),
				LandLine:   cif.GetContactDetails().GetPermanentAddress().GetLandLine(),
			},
		},
		Additional_Details: AdditionalDetails{
			AnnualIncome:          cif.GetAdditionalDetails().GetAnnualIncome(),
			PanNo:                 cif.GetAdditionalDetails().GetPanNo(),
			Religion:              cif.GetAdditionalDetails().GetReligion(),
			Community:             "NA",
			Qualification:         cif.GetAdditionalDetails().GetQualification(),
			Occupation:            cif.GetAdditionalDetails().GetOccupation(),
			Form60:                cif.GetAdditionalDetails().GetForm60(),
			TaxSlab:               cif.GetAdditionalDetails().GetTaxSlab(),
			Employment:            cif.GetAdditionalDetails().GetEmployment(),
			Designation:           cif.GetAdditionalDetails().GetDesignation(),
			SpouseOccupation:      cif.GetAdditionalDetails().GetSpouseOccupation(),
			CustomerStatus:        cif.GetAdditionalDetails().GetCustomerStatus(),
			Category:              cif.GetAdditionalDetails().GetCategory(),
			SubCategoryOccupation: cif.GetAdditionalDetails().GetSubCategoryOccupation(),
		},
		Identification_Details: IdentificationDetails{
			ProofOfIdentity: ProofDetails{
				Type:          cif.GetIdentificationDetails().GetProofOfIdentity().GetType(),
				Id_Number:     cif.GetIdentificationDetails().GetProofOfIdentity().GetIdNumber(),
				Id_IssueDate:  cif.GetIdentificationDetails().GetProofOfIdentity().GetIdIssueDate(),
				Id_ExpiryDate: cif.GetIdentificationDetails().GetProofOfIdentity().GetIdExpiryDate(),
			},
			ProofOfAddress: ProofDetails{
				Type:          cif.GetIdentificationDetails().GetProofOfAddress().GetType(),
				Id_Number:     cif.GetIdentificationDetails().GetProofOfAddress().GetIdNumber(),
				Id_IssueDate:  cif.GetIdentificationDetails().GetProofOfAddress().GetIdIssueDate(),
				Id_ExpiryDate: cif.GetIdentificationDetails().GetProofOfAddress().GetIdExpiryDate(),
			},
		},
	}
	wrappedRequest := map[string]interface{}{
		"Cif_Request": CreateCustomerReqInVendorSpecifiedOrder,
	}
	return json.Marshal(wrappedRequest)
}

// URL provides the URL to send the request to
func (c *createCustomerReq) URL() string {
	return c.url
}

// HTTPMethod returns the http method to use for the API call.
func (c *createCustomerReq) HTTPMethod() string {
	return c.method
}

// GetResponse returns Response struct that can deserialize the vendor response
func (c *createCustomerReq) GetResponse() vendorapi.Response {
	return &createCustomerRes{}
}

// This struct provides functionality for adapting to Federal bank's API.
type createCustomerRes struct {
}

// Unmarshal converts the response received from federal bank's create customer API into
// CreateCustomerResponse proto.
func (c *createCustomerRes) Unmarshal(b []byte) (proto.Message, error) {
	var m vendorfedral.CreateCustomerResponse
	unmarshaller := jsonpb.Unmarshaler{}
	err := unmarshaller.Unmarshal(bytes.NewBuffer(b), &m)
	if err != nil {
		logger.ErrorNoCtx(fmt.Sprintf("Could not parse customer creation ack response '%s'", string(b)))
		return nil, err
	}
	st := c.evaluateRPCStatus(m.GetResponseCode(), m.GetResponseReason())
	return &customer.CreateCustomerResponse{
		Status: st,
		VendorStatus: &commonvgpb.VendorStatus{
			Code:        m.GetResponseCode(),
			Description: m.GetResponseReason(),
		},
	}, nil
}

func (c *createCustomerRes) EvaluateAndGetMetricErrorCode(status *commonvgpb.VendorStatus) (bool, string) {
	if lo.Contains(createCustMonitoredVendorErrorCodes, status.GetCode()) {
		return true, status.GetCode()
	}
	return false, ""
}

func (c *createCustomerRes) evaluateRPCStatus(code, reason string) *rpc.Status {
	if strings.EqualFold(code, "OBE0007") &&
		strings.Contains(strings.ToLower(reason), InvalidAddressDetailsReason) {
		return CreateCustomerInvalidContactDetails
	}

	if statusFactory, ok := customerCreationErrorCodes[code]; ok {
		return statusFactory()
	}
	return rpc.StatusUnknown()
}

func (c *createCustomerReq) getSpouseOccupation(customerCreationFlow customer.CustomerCreationFlow) string {
	switch customerCreationFlow {
	case customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_VKYC_ONBOARDING_LSO:
		return CKYCVKYCSpouseOcc
	case customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_MIN_EKYC:
		return MINEKYCSpouseOcc
	case customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_VKYC:
		return VKYCSpouseOcc
	case customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_DEDUPE_MIN_KYC:
		return MINKYCCONVERTSpouseOcc
	case customer.CustomerCreationFlow_CUSTOMER_CREATION_FLOW_BKYC:
		return BKYCSpouseOcc
	}
	return ""
}

func (c *createCustomerReq) getOccupationCifString() string {
	occupation := DefaultOccupation
	switch {
	case c.useOccupationTypeInCifCreation:
		var ok bool
		occupation, ok = opfederal.OccTypeToOccupationString[c.req.GetOccupationType()]
		if !ok {
			logger.InfoNoCtx("falling back to employment to occupation string mapping", zap.String("occupation", c.req.GetOccupationType().String()))
			occupation = getOccCifStringFromEmpType(c.req.Type)
		}
	default:
		occupation = getOccCifStringFromEmpType(c.req.Type)
	}
	return occupation
}

func getOccCifStringFromEmpType(employmentType employmentPb.EmploymentType) string {
	occupation, ok := empTypeToOccupationString[employmentType]
	if !ok {
		logger.InfoNoCtx(fmt.Sprintf("no occupation found for employment type in vendorgateway: %v", employmentType.String()))
		occupation = DefaultOccupation
	}
	return occupation
}
