// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"encoding/json"
	"firebase.google.com/go"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"github.com/aws/aws-sdk-go-v2/service/sesv2"
	"github.com/epifi/be-common/api/vendorgateway"
	config2 "github.com/epifi/be-common/pkg/aws/v2/config"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	"github.com/epifi/be-common/pkg/crypto/rsa/v2"
	wire2 "github.com/epifi/be-common/pkg/crypto/wire"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc/interceptors/ratelimit/keygen"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/goxmldsig"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/syncwrapper/request"
	"github.com/epifi/be-common/pkg/vendorapi"
	genconf2 "github.com/epifi/be-common/pkg/vendorapi/config/genconf"
	"github.com/epifi/gamma/api/tokenizer"
	"github.com/epifi/gamma/api/vendorgateway/aa"
	bouncycastle2 "github.com/epifi/gamma/api/vendorgateway/bouncycastle"
	idfc3 "github.com/epifi/gamma/api/vendorgateway/idfc"
	"github.com/epifi/gamma/api/vendorgateway/tiering"
	"github.com/epifi/gamma/api/vendorgateway/wealth/cvl"
	"github.com/epifi/gamma/api/vendors/http"
	"github.com/epifi/gamma/api/vendors/sftp"
	"github.com/epifi/gamma/pkg/connectedaccount"
	tokenstore6 "github.com/epifi/gamma/pkg/tokenstore"
	config3 "github.com/epifi/gamma/vendorgateway-pci/config"
	aa2 "github.com/epifi/gamma/vendorgateway/aa"
	"github.com/epifi/gamma/vendorgateway/aa/analytics/ignosis"
	"github.com/epifi/gamma/vendorgateway/aml"
	"github.com/epifi/gamma/vendorgateway/appscreening/seon"
	"github.com/epifi/gamma/vendorgateway/billpayments"
	"github.com/epifi/gamma/vendorgateway/bouncycastle"
	"github.com/epifi/gamma/vendorgateway/ckyc"
	"github.com/epifi/gamma/vendorgateway/comms/acl"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/config/genconf"
	"github.com/epifi/gamma/vendorgateway/credit_report"
	tokenstore3 "github.com/epifi/gamma/vendorgateway/credit_report/tokenstore"
	creditcard2 "github.com/epifi/gamma/vendorgateway/creditcard"
	"github.com/epifi/gamma/vendorgateway/creditcard/saven"
	"github.com/epifi/gamma/vendorgateway/crm"
	"github.com/epifi/gamma/vendorgateway/crm/leadsquared"
	"github.com/epifi/gamma/vendorgateway/currencyinsights"
	"github.com/epifi/gamma/vendorgateway/cx/chatbot/livechatfallback/senseforth"
	"github.com/epifi/gamma/vendorgateway/cx/chatbot/nugget"
	"github.com/epifi/gamma/vendorgateway/cx/federal"
	attachment2 "github.com/epifi/gamma/vendorgateway/cx/federal/escalations/attachment"
	"github.com/epifi/gamma/vendorgateway/cx/freshchat"
	"github.com/epifi/gamma/vendorgateway/cx/freshdesk"
	"github.com/epifi/gamma/vendorgateway/cx/freshdesk/ticket/attachment"
	"github.com/epifi/gamma/vendorgateway/cx/inhouse"
	"github.com/epifi/gamma/vendorgateway/cx/ozonetel"
	"github.com/epifi/gamma/vendorgateway/cx/solutions"
	digilocker2 "github.com/epifi/gamma/vendorgateway/digilocker"
	"github.com/epifi/gamma/vendorgateway/dl"
	"github.com/epifi/gamma/vendorgateway/docs"
	"github.com/epifi/gamma/vendorgateway/ekyc"
	"github.com/epifi/gamma/vendorgateway/email"
	"github.com/epifi/gamma/vendorgateway/email/sendgrid"
	"github.com/epifi/gamma/vendorgateway/email/ses"
	"github.com/epifi/gamma/vendorgateway/employment"
	"github.com/epifi/gamma/vendorgateway/esign"
	"github.com/epifi/gamma/vendorgateway/extvalidate"
	"github.com/epifi/gamma/vendorgateway/fcm"
	"github.com/epifi/gamma/vendorgateway/fennel"
	"github.com/epifi/gamma/vendorgateway/fittt"
	"github.com/epifi/gamma/vendorgateway/fittt/roanuz/accesstoken"
	"github.com/epifi/gamma/vendorgateway/gplace"
	"github.com/epifi/gamma/vendorgateway/healthinsurance/riskcovry"
	idfc2 "github.com/epifi/gamma/vendorgateway/idfc"
	"github.com/epifi/gamma/vendorgateway/idvalidate"
	"github.com/epifi/gamma/vendorgateway/incomeestimator"
	"github.com/epifi/gamma/vendorgateway/interceptor/ratelimiter"
	"github.com/epifi/gamma/vendorgateway/interceptor/ratelimiter/namespace"
	"github.com/epifi/gamma/vendorgateway/investments/p2p"
	"github.com/epifi/gamma/vendorgateway/iplocation"
	"github.com/epifi/gamma/vendorgateway/itr"
	"github.com/epifi/gamma/vendorgateway/kyc/uqudo"
	idfc4 "github.com/epifi/gamma/vendorgateway/kyc/vkyc/idfc"
	"github.com/epifi/gamma/vendorgateway/lending/bre"
	mutualfund2 "github.com/epifi/gamma/vendorgateway/lending/collateral/mutualfund"
	"github.com/epifi/gamma/vendorgateway/lending/collections/credgenics"
	"github.com/epifi/gamma/vendorgateway/lending/creditcard"
	"github.com/epifi/gamma/vendorgateway/lending/creditline"
	"github.com/epifi/gamma/vendorgateway/lending/digitap"
	"github.com/epifi/gamma/vendorgateway/lending/lms/finflux"
	auth2 "github.com/epifi/gamma/vendorgateway/lending/lms/finflux/auth"
	"github.com/epifi/gamma/vendorgateway/lending/preapprovedloan"
	"github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/abfl"
	"github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/idfc"
	"github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/lenden"
	"github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/lentra"
	"github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/liquiloans"
	"github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/moneyview"
	tokenstore4 "github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/tokenstore"
	"github.com/epifi/gamma/vendorgateway/lending/securedloans/fiftyfin"
	"github.com/epifi/gamma/vendorgateway/lending/setu"
	"github.com/epifi/gamma/vendorgateway/liveness"
	"github.com/epifi/gamma/vendorgateway/location"
	"github.com/epifi/gamma/vendorgateway/merchantresolution"
	"github.com/epifi/gamma/vendorgateway/moengage"
	"github.com/epifi/gamma/vendorgateway/namecheck"
	employernamematch2 "github.com/epifi/gamma/vendorgateway/namecheck/employernamecategoriser"
	"github.com/epifi/gamma/vendorgateway/namecheck/employernamematch"
	"github.com/epifi/gamma/vendorgateway/ocr"
	"github.com/epifi/gamma/vendorgateway/offers/dreamfolks"
	"github.com/epifi/gamma/vendorgateway/offers/loylty"
	"github.com/epifi/gamma/vendorgateway/offers/poshvine"
	"github.com/epifi/gamma/vendorgateway/offers/qwikcilver"
	tokenstore2 "github.com/epifi/gamma/vendorgateway/offers/qwikcilver/tokenstore"
	"github.com/epifi/gamma/vendorgateway/offers/thriwe"
	"github.com/epifi/gamma/vendorgateway/offers/vistara"
	"github.com/epifi/gamma/vendorgateway/onsurity"
	"github.com/epifi/gamma/vendorgateway/openbanking/accounts"
	"github.com/epifi/gamma/vendorgateway/openbanking/auth"
	"github.com/epifi/gamma/vendorgateway/openbanking/auth/partnersdk"
	"github.com/epifi/gamma/vendorgateway/openbanking/bank_customer"
	"github.com/epifi/gamma/vendorgateway/openbanking/card"
	"github.com/epifi/gamma/vendorgateway/openbanking/customer"
	"github.com/epifi/gamma/vendorgateway/openbanking/deposit"
	"github.com/epifi/gamma/vendorgateway/openbanking/dispute"
	"github.com/epifi/gamma/vendorgateway/openbanking/enach"
	"github.com/epifi/gamma/vendorgateway/openbanking/lien"
	"github.com/epifi/gamma/vendorgateway/openbanking/payment"
	"github.com/epifi/gamma/vendorgateway/openbanking/payment/b2c"
	"github.com/epifi/gamma/vendorgateway/openbanking/payment/internationalfundtransfer"
	"github.com/epifi/gamma/vendorgateway/openbanking/savings"
	"github.com/epifi/gamma/vendorgateway/openbanking/shipping_preference"
	"github.com/epifi/gamma/vendorgateway/openbanking/standinginstruction"
	"github.com/epifi/gamma/vendorgateway/openbanking/upi"
	"github.com/epifi/gamma/vendorgateway/pan"
	"github.com/epifi/gamma/vendorgateway/parser"
	"github.com/epifi/gamma/vendorgateway/payment_gateway"
	"github.com/epifi/gamma/vendorgateway/phonenetwork"
	"github.com/epifi/gamma/vendorgateway/profilevalidation"
	"github.com/epifi/gamma/vendorgateway/risk"
	"github.com/epifi/gamma/vendorgateway/scienaptic"
	"github.com/epifi/gamma/vendorgateway/shipway"
	"github.com/epifi/gamma/vendorgateway/slack_bot"
	"github.com/epifi/gamma/vendorgateway/sms"
	"github.com/epifi/gamma/vendorgateway/stocks"
	"github.com/epifi/gamma/vendorgateway/stocks/catalog"
	"github.com/epifi/gamma/vendorgateway/stocks/catalog/bridgewise"
	"github.com/epifi/gamma/vendorgateway/stocks/catalog/nps"
	tokenstore5 "github.com/epifi/gamma/vendorgateway/stocks/tokenstore"
	tiering2 "github.com/epifi/gamma/vendorgateway/tiering"
	"github.com/epifi/gamma/vendorgateway/transactionmonitoring"
	"github.com/epifi/gamma/vendorgateway/userseg"
	v2_2 "github.com/epifi/gamma/vendorgateway/vendorapi/cryptor/federal/v2"
	"github.com/epifi/gamma/vendorgateway/vendorsse"
	"github.com/epifi/gamma/vendorgateway/vendorws"
	"github.com/epifi/gamma/vendorgateway/vkyc"
	"github.com/epifi/gamma/vendorgateway/vkyc/karza/authtoken"
	"github.com/epifi/gamma/vendorgateway/vkyccall"
	ckyc2 "github.com/epifi/gamma/vendorgateway/wealth/ckyc"
	"github.com/epifi/gamma/vendorgateway/wealth/digilocker"
	"github.com/epifi/gamma/vendorgateway/wealth/digio"
	"github.com/epifi/gamma/vendorgateway/wealth/inhouseocr"
	"github.com/epifi/gamma/vendorgateway/wealth/karza"
	"github.com/epifi/gamma/vendorgateway/wealth/kra"
	"github.com/epifi/gamma/vendorgateway/wealth/manch"
	"github.com/epifi/gamma/vendorgateway/wealth/mutualfund"
	"github.com/epifi/gamma/vendorgateway/wealth/mutualfund/analytics"
	"github.com/epifi/gamma/vendorgateway/wealth/mutualfund/holdingsimporter"
	"github.com/epifi/gamma/vendorgateway/wealth/mutualfund/morningstar"
	"github.com/epifi/gamma/vendorgateway/wealth/mutualfund/token/mf_central"
	"github.com/epifi/gamma/vendorgateway/wealth/nsdl"
	"github.com/epifi/gamma/vendorgateway/wealth/tokenstore"
	"github.com/epifi/gamma/vendorgateway/whatsapp"
	"github.com/epifi/gamma/vendorgateway/wire/providers"
	types2 "github.com/epifi/gamma/vendorgateway/wire/types"
	"github.com/epifi/gamma/vendorgateway/zenduty"
	"github.com/google/wire"
	sendgrid2 "github.com/sendgrid/sendgrid-go"
	"github.com/slack-go/slack"
	"github.com/slack-go/slack/socketmode"
	"go.uber.org/zap"
	"google.golang.org/api/option"
	"log"
	http2 "net/http"
	"os"
)

// Injectors from wire.go:

func InitializeSyncWrapperConsumerService() *vendorapi.SyncWrapperConsumer {
	syncWrapperConsumer := vendorapi.NewSyncWrapperConsumer()
	return syncWrapperConsumer
}

func InitializePartnerSDKService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *partnersdk.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := partnersdk.NewService(httpRequestHandler)
	return service
}

func InitializeStandingInstructionService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *standinginstruction.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := standinginstruction.NewService(httpRequestHandler)
	return service
}

func InitializeDummySMSService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *sms.Service {
	client := emptyHttpClientProvider()
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := sms.NewService(httpRequestHandler)
	return service
}

func InitializeSMSService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *sms.Service {
	client := providers.GetHttpClientForCommsVendors(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := sms.NewService(httpRequestHandler)
	return service
}

func InitializeGPlaceService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *gplace.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := gplace.NewService(httpRequestHandler)
	return service
}

func InitializeCustomerService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *customer.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	defaultTime := datetime.NewDefaultTime()
	service := customer.NewService(httpRequestHandler, gconf, defaultTime)
	return service
}

func InitializeSavingsService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *savings.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := savings.NewService(httpRequestHandler, gconf)
	return service
}

func InitializeShippingPreferenceService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *shipping_preference.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := shipping_preference.NewService(httpRequestHandler)
	return service
}

func InitializeVendorAuthService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *auth.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := auth.NewService(httpRequestHandler)
	return service
}

func InitializeBankCustomerService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *bank_customer.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := bank_customer.NewService(httpRequestHandler, conf)
	return service
}

func InitializePaymentService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *payment.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := payment.NewService(httpRequestHandler)
	return service
}

func InitializeCKYCService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *ckyc.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := ckyc.NewService(httpRequestHandler)
	return service
}

func InitializeEKYCService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *ekyc.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := ekyc.NewService(httpRequestHandler, conf)
	return service
}

func InitializeDOCSService(conf *config.Config, gconf *genconf.Config, vendorapiGConf *genconf2.Config) *docs.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGConf, string2)
	service := docs.NewService(httpRequestHandler, conf)
	return service
}

func InitializeLivenessService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *liveness.Service {
	client := http.NewKarzaHttpClient(conf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := liveness.NewService(httpRequestHandler, conf, gconf)
	return service
}

func InitializeEmploymentService(redisClient types.VendorgatewayRedisStore, conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *employment.Service {
	client := providers.GetHttpClientForEmploymentService(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	karzaGetPassbookHttpClient := providers.GetKarzaEPFPassbookHttpClient(gconf)
	karzaGetPassbookHttpRequestHandler := GetKarzaGetPassbookHttpRequestHandler(karzaGetPassbookHttpClient, signingContext, httpContentRedactor, gconf, vendorapiGconf)
	client2 := types.VendorgatewayRedisStoreRedisClientProvider(redisClient)
	redisTokenStore := tokenstore.NewRedisTokenStore(client2)
	service := employment.NewService(httpRequestHandler, karzaGetPassbookHttpRequestHandler, redisTokenStore, conf)
	return service
}

func InitializeTransactionMonitoringService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *transactionmonitoring.Service {
	client := providers.GetDronaPayHttpClient(conf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := transactionmonitoring.NewService(httpRequestHandler)
	return service
}

func InitializeCardProvisioningService(ctx context.Context, vgConfig *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config, tokenizerClient tokenizer.TokenizerClient) (*card.Service, error) {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	configConfig := nilVgPciConfigProvider()
	inMemoryCryptorStore, err := InitCryptors(ctx, vgConfig)
	if err != nil {
		return nil, err
	}
	factory := card.NewFactory(vgConfig, configConfig, inMemoryCryptorStore)
	service := card.NewService(httpRequestHandler, factory, tokenizerClient)
	return service, nil
}

func InitializeUqudoService(gconf *genconf.Config, vendorApiGConf *genconf2.Config) *uqudo.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorApiGConf, string2)
	service := uqudo.NewService(httpRequestHandler, gconf)
	return service
}

func InitializePANService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) (*pan.Service, error) {
	client := providers.GetPANServiceHttpClient(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	iSftp, err := getSftpClientForEpanFederal(conf)
	if err != nil {
		return nil, err
	}
	service := pan.NewService(httpRequestHandler, conf, gconf, iSftp)
	return service, nil
}

func InitializeUNNameCheckService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *namecheck.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := namecheck.NewService(httpRequestHandler, conf, gconf)
	return service
}

func InitializeEmployerNameMatchService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *employernamematch.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := employernamematch.NewService(httpRequestHandler, conf)
	return service
}

func InitializeEmployerNameCategoriserService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *employernamematch2.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := employernamematch2.NewService(httpRequestHandler, conf)
	return service
}

func InitializeSenseforthLiveChatFallbackService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *senseforth.Service {
	client := providers.GetHttpClientForCx(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := senseforth.NewService(httpRequestHandler)
	return service
}

func InitializeFreshdeskService(ctx context.Context, conf *config.Config, genConf *genconf.Config, config2 *genconf2.Config) *freshdesk.Service {
	client := providers.GetHttpClientForCx(genConf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(genConf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, config2, string2)
	downloader := getS3Downloader(ctx, conf)
	ticketAttachments := attachment.NewTicketAttachments(genConf, downloader)
	service := freshdesk.NewService(httpRequestHandler, downloader, genConf, ticketAttachments)
	return service
}

func InitializeCRMService(conf *config.Config, redisClient types.VendorgatewayRedisStore, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *crm.Service {
	client := providers.GetHttpClientForCrm(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	client2 := types.VendorgatewayRedisStoreRedisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client2)
	service := crm.NewService(httpRequestHandler, conf, redisCacheStorage)
	return service
}

func InitializeFreshChatServer(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *freshchat.Service {
	client := providers.GetHttpClientForCx(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := freshchat.NewService(httpRequestHandler)
	return service
}

func InitializeOzonetelService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *ozonetel.Service {
	client := providers.GetHttpClientForCx(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := ozonetel.NewService(httpRequestHandler)
	return service
}

func InitializeSolutionsService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *solutions.Service {
	client := providers.GetHttpClientForCx(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := solutions.NewService(httpRequestHandler)
	return service
}

func InitializeDepositService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *deposit.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := deposit.NewService(httpRequestHandler)
	return service
}

func InitializeB2CService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *b2c.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := b2c.NewService(httpRequestHandler)
	return service
}

func InitializeLeadSquaredService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *leadsquared.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := leadsquared.NewService(httpRequestHandler)
	return service
}

func InitializeLoyltyService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *loylty.Service {
	client := providers.GetHttpClientForLoylty(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	inMemoryCacheStore := loylty.NewInMemoryCache()
	service := loylty.NewService(httpRequestHandler, inMemoryCacheStore)
	return service
}

func InitializeQwikcilverService(redisClient types.VendorgatewayRedisStore, conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *qwikcilver.Service {
	client := providers.GetHttpClientForQwikcilver(conf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	client2 := types.VendorgatewayRedisStoreRedisClientProvider(redisClient)
	redisTokenStore := tokenstore2.NewRedisTokenStore(client2)
	clock := lock.NewRealClockProvider()
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client2, clock, uuidGenerator)
	configQwikcilver := qwikcliverConfProvider(conf)
	service := qwikcilver.NewService(httpRequestHandler, redisTokenStore, redisV9LockManager, configQwikcilver)
	return service
}

func InitializeVkycService(redisClient types.VendorgatewayRedisStore, conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *vkyc.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	inMemoryTokenStore := authtoken.NewInMemoryTokenStore()
	client2 := types.VendorgatewayRedisStoreRedisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client2)
	clock := lock.NewRealClockProvider()
	redisRwLock := lock.NewRedisRwLock(client2, clock)
	service := vkyc.NewService(httpRequestHandler, inMemoryTokenStore, redisCacheStorage, conf, gconf, redisRwLock)
	return service
}

func InitialiseFitttCricketServcie(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *fittt.Service {
	client := providers.InsecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	inMemoryTokenStore := accesstoken.NewInMemoryTokenStore()
	service := fittt.NewService(httpRequestHandler, inMemoryTokenStore)
	return service
}

func InitializeAccountsServer(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *accounts.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := accounts.NewService(httpRequestHandler, string2)
	return service
}

func InitializeWhatsAppService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *whatsapp.Service {
	client := providers.GetHttpClientForWhatsapp(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := whatsapp.NewService(httpRequestHandler)
	return service
}

func InitializeIpLocationService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *iplocation.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := iplocation.NewService(httpRequestHandler)
	return service
}

func InitializeAAService(aggregatorClient aa.AccountAggregatorClient, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *aa2.Service {
	client := providers.GetHttpClientForAA(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	cacheService := connectedaccount.NewCacheService(aggregatorClient)
	factory := aa2.NewFactory(cacheService, gconf)
	service := aa2.NewService(httpRequestHandler, factory)
	return service
}

func InitializeBcService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *bouncycastle.Service {
	client := providers.GetHttpClientForAA(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := bouncycastle.NewService(httpRequestHandler)
	return service
}

func InitializeWealthCvlService(conf *config.Config, wealthCvlClient cvl.CvlClient, gconf *genconf.Config, vendorapiGconf *genconf2.Config) (*kra.Service, error) {
	client := providers.GetHttpClientForWealth(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	inMemoryCacheService := cache.NewInMemoryCacheService()
	factory := kra.NewCvlKraFactory(wealthCvlClient, conf, inMemoryCacheService)
	downloadISftp, err := getSftpClientForCvlDownload(conf)
	if err != nil {
		return nil, err
	}
	uploadISftp, err := getSftpClientForCvlUpload(conf)
	if err != nil {
		return nil, err
	}
	service := kra.NewService(httpRequestHandler, factory, downloadISftp, uploadISftp, conf)
	return service, nil
}

func InitializeWealthNsdlService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *nsdl.Service {
	client := providers.GetHttpClientForWealth(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	nsdlFactory := nsdl.NewNsdlFactory(gconf)
	service := nsdl.NewService(httpRequestHandler, nsdlFactory, gconf)
	return service
}

func InitializeWealthMutualFundService(ctx context.Context, conf *config.Config, redisClient types.VendorgatewayRedisStore, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *mutualfund.Service {
	client := providers.GetInsecureHttpClientForWealth(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	downloader := getS3DownloaderForMutualFund(ctx, conf)
	client2 := types.VendorgatewayRedisStoreRedisClientProvider(redisClient)
	redisTokenStore := tokenstore.NewRedisTokenStore(client2)
	morningStarAccessTokenFetcher := morningstar.NewMorningStarAccessTokenFetcher(httpRequestHandler, conf, redisTokenStore)
	redisCacheStorage := cache.NewRedisCacheStorage(client2)
	mutualFundFactory := mutualfund.NewMutualFundFactory(conf, gconf, httpRequestHandler, downloader, morningStarAccessTokenFetcher, redisCacheStorage)
	service := mutualfund.NewService(httpRequestHandler, mutualFundFactory, conf)
	return service
}

func InitializeCreditReportService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *credit_report.Service {
	client := providers.GetHttpClientForExperian(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	cibilHttpRequestHandler := GetCibilHttpRequestHandler(signingContext, httpContentRedactor, gconf, vendorapiGconf)
	inMemoryCacheService := cache.NewInMemoryCacheService()
	tokenStoreManager := tokenstore3.NewTokenManager(gconf, inMemoryCacheService, httpRequestHandler)
	service := credit_report.NewService(httpRequestHandler, cibilHttpRequestHandler, gconf, tokenStoreManager)
	return service
}

func InitialiseShipwayService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *shipway.Service {
	client := providers.GetHttpClientForShipway(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := shipway.NewService(httpRequestHandler)
	return service
}

func InitializeWealthCkycService(conf *config.Config, bcClient bouncycastle2.BouncyCastleClient, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *ckyc2.Service {
	client := providers.GetHttpClientForWealth(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	ckycCryptor := ckyc2.NewCkycCryptor(bcClient, conf)
	secureExchange := ckyc2.NewSecureExchange(ckycCryptor)
	service := ckyc2.NewService(httpRequestHandler, secureExchange, conf)
	return service
}

func InitializeWealthManchService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *manch.Service {
	client := providers.GetInsecureHttpClientForWealth(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := manch.NewService(httpRequestHandler, conf)
	return service
}

func InitializeWealthKarzaService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *karza.Service {
	client := providers.GetInsecureHttpClientForWealth(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := karza.NewService(httpRequestHandler, conf)
	return service
}

func InitializeWealthDigioService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *digio.Service {
	client := providers.GetInsecureHttpClientForWealth(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := digio.NewService(httpRequestHandler, conf)
	return service
}

func InitializeDlAuthService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *dl.Service {
	client := providers.GetKarzaHttpClient(conf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := dl.NewService(httpRequestHandler, conf)
	return service
}

func InitializeParserService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *parser.Service {
	client := providers.GetVendorHttpClient(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := parser.NewService(httpRequestHandler)
	return service
}

func InitializeSeonService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *seon.Service {
	client := providers.GetHttpClientForSeon(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := seon.NewService(httpRequestHandler)
	return service
}

func InitializeIdValidateService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *idvalidate.Service {
	client := providers.GetKarzaHttpClient(conf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := idvalidate.NewService(httpRequestHandler, conf)
	return service
}

func InitializeWealthInhouseOCRService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *inhouseocr.Service {
	client := providers.GetHttpClientForWealth(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := inhouseocr.NewService(httpRequestHandler, conf)
	return service
}

func InitializeCXInHouseService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *inhouse.Service {
	client := providers.GetHttpClientForCx(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := inhouse.NewCXInHouseService(httpRequestHandler)
	return service
}

func InitializeWealthDigilockerService(redisClient types.VendorgatewayRedisStore, conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *digilocker.Service {
	client := providers.GetHttpClientForWealth(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	client2 := types.VendorgatewayRedisStoreRedisClientProvider(redisClient)
	redisTokenStore := tokenstore.NewRedisTokenStore(client2)
	service := digilocker.NewService(httpRequestHandler, conf, redisTokenStore)
	return service
}

func InitializeP2pInvestmentService(ctx context.Context, conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) (*p2p.Service, error) {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	iSftp, err := getSftpClientForP2PInvestmentDataDownload(conf)
	if err != nil {
		return nil, err
	}
	uploader := getS3Uploader(ctx, conf)
	downloader := getS3Downloader(ctx, conf)
	service := p2p.NewService(httpRequestHandler, conf, iSftp, uploader, downloader)
	return service, nil
}

func InitializeLocationService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *location.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := location.NewService(httpRequestHandler, conf, gconf)
	return service
}

func InitializeExternalValidateService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *extvalidate.Service {
	client := providers.GetKarzaHttpClient(conf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := extvalidate.NewService(httpRequestHandler, conf)
	return service
}

func InitializeUserSegmentationService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *userseg.Service {
	client := providers.GetVendorHttpClient(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := userseg.NewService(httpRequestHandler)
	return service
}

func InitialisePhoneNetworkService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *phonenetwork.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := phonenetwork.NewService(httpRequestHandler, conf)
	return service
}

func InitialiseCredgenicsService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *credgenics.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	inMemoryTokenStore := tokenstore4.NewInMemoryTokenStore()
	service := credgenics.NewService(httpRequestHandler, client, conf, inMemoryTokenStore)
	return service
}

func InitialiseFinfluxService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config, redisClient types.VendorgatewayRedisStore) *finflux.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	client2 := types.VendorgatewayRedisStoreRedisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client2)
	clock := lock.NewRealClockProvider()
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client2, clock, uuidGenerator)
	processorImpl := auth2.NewProcessorImpl(httpRequestHandler, conf, redisCacheStorage, redisV9LockManager)
	service := finflux.NewService(httpRequestHandler, conf, processorImpl)
	return service
}

func InitialisePreApprovedLoanService(ctx context.Context, conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) (*preapprovedloan.Service, error) {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	inMemoryCryptorStore, err := InitCryptors(ctx, conf)
	if err != nil {
		return nil, err
	}
	service := preapprovedloan.NewService(httpRequestHandler, client, conf, inMemoryCryptorStore)
	return service, nil
}

func InitialiseLiquiloansService(config2 *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *liquiloans.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	preApprovedLoan := staticPreapprovedLoanConfigProvider(config2)
	service := liquiloans.NewService(httpRequestHandler, client, preApprovedLoan)
	return service
}

func InitialiseSetuService(config2 *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *setu.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	configSetu := setuConfigProvider(gconf)
	service := setu.NewService(httpRequestHandler, client, configSetu)
	return service
}

func InitialiseMoneyviewService(config2 *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *moneyview.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	configMoneyview := moneyviewConfigProvider(gconf)
	inMemoryCacheService := cache.NewInMemoryCacheService()
	service := moneyview.NewService(httpRequestHandler, configMoneyview, inMemoryCacheService)
	return service
}

func InitialiseIdfcService(config2 *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *idfc.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	preApprovedLoan := preapprovedLoanConfigProvider(gconf)
	inMemoryTokenStore := tokenstore4.NewInMemoryTokenStore()
	service := idfc.NewService(httpRequestHandler, client, preApprovedLoan, inMemoryTokenStore)
	return service
}

func InitialiseLendenService(config2 *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *lenden.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	configLenden := lendenConfigProvider(gconf)
	service := lenden.NewService(httpRequestHandler, configLenden)
	return service
}

func InitialiseFiftyFinService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *fiftyfin.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	securedLoans := securedLoansConfigProvider(gconf, vendorapiGconf)
	service := fiftyfin.NewService(httpRequestHandler, client, securedLoans)
	return service
}

func InitialiseEsignService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *esign.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := esign.NewService(httpRequestHandler, client, conf)
	return service
}

func InitialiseLamfService(redisClient types.VendorgatewayRedisStore, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *mutualfund2.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	lendingMFCentralConfig := lendingMfCentralConfigProvider(gconf, vendorapiGconf)
	client2 := types.VendorgatewayRedisStoreRedisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client2)
	service := mutualfund2.NewService(httpRequestHandler, lendingMFCentralConfig, client, redisCacheStorage)
	return service
}

func InitialiseInhouseRiskService(dynConf *genconf.Config, config2 *genconf2.Config) *risk.Service {
	client := providers.SecureHttpClientProvider(dynConf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(dynConf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, config2, string2)
	service := risk.NewService(httpRequestHandler, dynConf)
	return service
}

func InitialiseBreService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *bre.Service {
	client := providers.InsecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := bre.NewBusinessRuleEngineService(httpRequestHandler, conf)
	return service
}

func InitialiseCreditCardService(c *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *creditcard.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	configConfig := nilVgPciConfigProvider()
	service := creditcard.NewCreditCardService(httpRequestHandler, c, configConfig)
	return service
}

func InitializeCurrencyInsightsService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *currencyinsights.Service {
	client := providers.GetHttpClientForVisa(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	factory := currencyinsights.NewCurrencyInsightFactory(gconf)
	service := currencyinsights.NewService(httpRequestHandler, gconf, factory)
	return service
}

func InitialiseCreditLineService(c *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *creditline.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := creditline.NewCreditlineService(httpRequestHandler, c)
	return service
}

func InitialiseUSStockService(c *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *stocks.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	connectionHandler := vendorws.NewConnectionHandler(c)
	vendorsseConnectionHandler := vendorsse.NewConnectionHandler(c)
	service := stocks.NewService(httpRequestHandler, c, connectionHandler, vendorsseConnectionHandler)
	return service
}

func InitialiseVgKeyGenerator() keygen.IKeyGenerator {
	initiateB2CPayNamespaceGenerator := namespace.NewInitiateB2CPayNamespaceGenerator()
	iFactory := namespace.NewDefaultFactory(initiateB2CPayNamespaceGenerator)
	iKeyGenerator := ratelimiter.NewVgKeyGenerator(iFactory)
	return iKeyGenerator
}

func InitialiseProfileValidationService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *profilevalidation.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := profilevalidation.NewService(httpRequestHandler, conf)
	return service
}

func InitializeAmlService(conf *config.Config, confs *genconf.Config, config2 *genconf2.Config) *aml.Service {
	client := providers.InsecureHttpClientProvider(confs)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(confs)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, config2, string2)
	amlFactory := aml.NewAmlFactory(conf)
	service := aml.NewService(httpRequestHandler, conf, amlFactory)
	return service
}

func InitializeInternationalfundtransferService(c *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *internationalfundtransfer.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := internationalfundtransfer.NewService(httpRequestHandler, c, gconf)
	return service
}

func InitializeRiskcovryService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *riskcovry.Service {
	client := providers.GetVendorHttpClient(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := riskcovry.NewService(httpRequestHandler)
	return service
}

func InitializeOnsurityService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *onsurity.Service {
	client := providers.GetVendorHttpClient(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := onsurity.NewService(httpRequestHandler)
	return service
}

func InitializeHoldingsImporterService(redisClient types.VendorgatewayRedisStore, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *holdingsimporter.Service {
	client := providers.GetInsecureHttpClientForWealth(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	httpRequestHandler := vendorApiPkgProvider(client, signingContext, httpContentRedactor, vendorapiGconf, gconf)
	client2 := types.VendorgatewayRedisStoreRedisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client2)
	holdingsImporterFactory := holdingsimporter.NewHoldingsImporterFactory(gconf, httpRequestHandler, redisCacheStorage)
	typesVerifyAndDecryptUrl := verifyAndDecryptUrl(gconf)
	verifyAndDecrypt := mf_central.NewVerifyAndDecrypt(httpRequestHandler, typesVerifyAndDecryptUrl)
	typesEncryptAndSignUrl := encryptAndSignUrl(gconf)
	encryptAndSign := mf_central.NewEncryptAndSign(httpRequestHandler, typesEncryptAndSignUrl)
	service := holdingsimporter.NewService(httpRequestHandler, holdingsImporterFactory, verifyAndDecrypt, encryptAndSign)
	return service
}

func vendorApiPkgProvider(cl vendorapi.HttpDoer, xmlSigner *dsig.SigningContext, h *httpcontentredactor.HTTPContentRedactor, vendorapiGconf *genconf2.Config, gconf *genconf.Config) *vendorapi.HTTPRequestHandler {
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(cl, xmlSigner, h, vendorapiGconf, string2)
	return httpRequestHandler
}

func InitalizeUsStockCatalogService(c *config.Config, redisClient types.VendorgatewayRedisStore, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *catalog.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	client2 := types.VendorgatewayRedisStoreRedisClientProvider(redisClient)
	redisTokenStore := tokenstore5.NewRedisTokenStore(client2)
	service := catalog.NewService(httpRequestHandler, c, redisTokenStore)
	return service
}

func InitializeMerchantResolutionService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *merchantresolution.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := merchantresolution.NewService(httpRequestHandler, conf)
	return service
}

func InitializeMFAnalyticsService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *analytics.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	mfAnalyticsFactory := analytics.NewMFAnalyticsFactory(gconf)
	service := analytics.NewService(httpRequestHandler, mfAnalyticsFactory)
	return service
}

func InitializeDreamfolksService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *dreamfolks.Service {
	client := providers.GetVendorHttpClient(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	configDreamfolks := dreamfolksConfProvider(gconf, vendorapiGconf)
	service := dreamfolks.NewService(httpRequestHandler, configDreamfolks)
	return service
}

func InitializeThriweService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *thriwe.Service {
	client := providers.GetVendorHttpClient(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	configThriwe := thriweConfProvider(gconf, vendorapiGconf)
	service := thriwe.NewService(httpRequestHandler, configThriwe)
	return service
}

func InitializeMoEngageService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *moengage.Service {
	client := providers.GetVendorHttpClient(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	moEngage := moengageConfProvider(gconf, vendorapiGconf)
	service := moengage.NewService(httpRequestHandler, moEngage)
	return service
}

func InitializePoshvineService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *poshvine.Service {
	client := providers.GetVendorHttpClient(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	configPoshvine := poshvineConfProvider(gconf, vendorapiGconf)
	service := poshvine.NewService(httpRequestHandler, configPoshvine)
	return service
}

func InitializeLienService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *lien.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := lien.NewService(httpRequestHandler, conf)
	return service
}

func InitialiseVistaraService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) (*vistara.Service, error) {
	iSftp, err := getSftpClientForVistara(conf)
	if err != nil {
		return nil, err
	}
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := vistara.NewService(iSftp, httpRequestHandler, conf)
	return service, nil
}

func InitializeTieringService(tieringClient tiering.TieringClient, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *tiering2.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	factory := tiering2.NewFactory(gconf)
	service := tiering2.NewService(httpRequestHandler, factory)
	return service
}

func InitializeSlackBotService(conf *config.Config) *slack_bot.Service {
	client := initSlackBotClient(conf)
	socketmodeClient := initSocketModeClient(client)
	service := slack_bot.NewService(conf, client, socketmodeClient)
	return service
}

func InitializeFennelFeatureStoreService(gconf *genconf.Config, vendorapiGconf *genconf2.Config, conf *config.Config) *fennel.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := fennel.NewService(httpRequestHandler, conf)
	return service
}

func InitializeScienapticService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *scienaptic.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := scienaptic.NewService(httpRequestHandler)
	return service
}

func InitialiseIncomeEstimatorService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *incomeestimator.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := incomeestimator.NewService(httpRequestHandler, gconf)
	return service
}

func InitialiseEnachService(gconf *genconf.Config, vendorapiGconf *genconf2.Config, c *config.Config) (*enach.Service, error) {
	iSftp, err := getSftpClientForEnachFederal(c)
	if err != nil {
		return nil, err
	}
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := enach.NewService(c, iSftp, httpRequestHandler)
	return service, nil
}

func InitializeIdfcService(config2 *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *idfc2.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	inMemoryTokenStore := tokenstore6.NewInMemoryTokenStore()
	genconfIdfc := idfcConfigProvider(gconf)
	service := idfc2.NewService(httpRequestHandler, client, inMemoryTokenStore, genconfIdfc)
	return service
}

func InitializeIdfcVkycService(idfcClient idfc3.IdfcClient, conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *idfc4.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := idfc4.NewService(httpRequestHandler, client, idfcClient, gconf)
	return service
}

func InitialiseLentra(config2 *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *lentra.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	inMemoryTokenStore := tokenstore6.NewInMemoryTokenStore()
	service := lentra.NewService(httpRequestHandler, client, config2, inMemoryTokenStore)
	return service
}

func InitialiseDisputeService(ctx context.Context, conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *dispute.Service {
	downloader := getS3Downloader(ctx, conf)
	disputeSFTP := disputeSftpConfProvider(conf)
	client := providers.GetDisputeServiceHttpClient(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := dispute.NewService(downloader, disputeSFTP, httpRequestHandler)
	return service
}

func InitialiseEmailService(ctx context.Context, conf *config.Config) *email.VgEmailService {
	iSendGridClient := getSendGridClient(conf)
	iSesClient := getSesClient(ctx, conf)
	awsSes := awsSesProvider(conf)
	vgEmailService := email.NewVgEmailService(iSendGridClient, iSesClient, awsSes)
	return vgEmailService
}

func InitialiseFcmService(conf *config.Config, gconf *genconf.Config) *fcm.Service {
	iFcmClient := getFirebaseMessagingClient(conf)
	string2 := fcmAnalyticsLabelProvider(conf)
	service := fcm.NewFCMService(iFcmClient, string2)
	return service
}

func InitialiseUpiService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config, eventBroker events.Broker) (*upi.Service, error) {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext, err := getXmlSigner(conf)
	if err != nil {
		return nil, err
	}
	int2 := syncWrapperTimeoutConfigProvider(conf)
	asyncReqIDHandler, err := asyncReqIdHandlerProvider(conf)
	if err != nil {
		return nil, err
	}
	asyncRequestHandler := asyncRequestHandlerProvider(client, signingContext, int2, asyncReqIDHandler, gconf, vendorapiGconf)
	upiListKeysHttpClient := providers.GetListKeysHttpClient(gconf)
	listKeyAsyncHandler := listKeysAsyncRequestHandlerProvider(upiListKeysHttpClient, signingContext, int2, asyncReqIDHandler, gconf, vendorapiGconf)
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := upi.NewService(asyncRequestHandler, listKeyAsyncHandler, httpRequestHandler, eventBroker)
	return service, nil
}

func InitialiseAbflService(config2 *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *abfl.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	preApprovedLoan := staticPreapprovedLoanConfigProvider(config2)
	inMemoryTokenStore := tokenstore4.NewInMemoryTokenStore()
	service := abfl.NewService(httpRequestHandler, client, preApprovedLoan, inMemoryTokenStore)
	return service
}

func InitializeITRService(config2 *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *itr.Service {
	client := providers.GetITRServiceHttpClient(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := itr.NewService(httpRequestHandler, config2, gconf)
	return service
}

func InitialiseOCRService(config2 *genconf.Config, config2_2 *genconf2.Config) *ocr.Service {
	client := providers.SecureHttpClientProvider(config2)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(config2)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, config2_2, string2)
	service := ocr.NewOCRService(httpRequestHandler, config2)
	return service
}

func InitialiseAclSftpService(gconf *genconf.Config, vendorApiGConf *genconf2.Config) (*acl.Service, error) {
	iSftp, err := getSftpClientForAcl(gconf)
	if err != nil {
		return nil, err
	}
	client := providers.GetVendorHttpClient(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorApiGConf, string2)
	service := acl.NewService(iSftp, httpRequestHandler, gconf)
	return service, nil
}

func InitializeVkycCallService(gconf *genconf.Config, vendorApiGConf *genconf2.Config) (*vkyccall.Service, error) {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorApiGConf, string2)
	service := vkyccall.NewService(gconf, httpRequestHandler)
	return service, nil
}

func InitializeZendutyService(gconf *genconf.Config, vendorApiGConf *genconf2.Config) (*zenduty.Service, error) {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorApiGConf, string2)
	service := zenduty.NewService(gconf, httpRequestHandler)
	return service, nil
}

func InitialiseDigitapService(config2 *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *digitap.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	configDigitap := digitapConfigProvider(gconf)
	service := digitap.NewService(httpRequestHandler, client, configDigitap)
	return service
}

func InitialiseDigilockerService(c *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *digilocker2.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	connectionHandler := vendorws.NewConnectionHandler(c)
	vendorsseConnectionHandler := vendorsse.NewConnectionHandler(c)
	service := digilocker2.NewService(httpRequestHandler, c, connectionHandler, vendorsseConnectionHandler)
	return service
}

func InitializeBillPaymentsService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *billpayments.Service {
	client := providers.GetVendorHttpClient(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	factory := billpayments.NewBillPaymentsFactory(gconf)
	service := billpayments.NewService(httpRequestHandler, gconf, factory)
	return service
}

func InitializeAaIgnosisService(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *ignosis.Service {
	client := providers.GetVendorHttpClient(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := ignosis.NewService(httpRequestHandler, gconf, client)
	return service
}

func InitalizeStockCatalogService(c *config.Config, redisClient types.VendorgatewayRedisStore, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *bridgewise.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	client2 := types.VendorgatewayRedisStoreRedisClientProvider(redisClient)
	redisTokenStore := tokenstore5.NewRedisTokenStore(client2)
	service := bridgewise.NewService(httpRequestHandler, c, redisTokenStore)
	return service
}

func InitializeNpsService(c *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *nps.Service {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := nps.NewService(httpRequestHandler, c)
	return service
}

func InitializeCreditCardV2Service(c *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) (*creditcard2.Service, error) {
	client := providers.SecureHttpClientProvider(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	jwtSigner, err := saven.NewJWTSigner(gconf)
	if err != nil {
		return nil, err
	}
	service := creditcard2.NewService(httpRequestHandler, c, gconf, jwtSigner)
	return service, nil
}

func InitializeFederalEscalationService(ctx context.Context, conf *config.Config, genConf *genconf.Config, config2 *genconf2.Config) *federal.Service {
	client := providers.GetHttpClientForFederalEscalationService(genConf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(genConf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, config2, string2)
	downloader := getS3Downloader(ctx, conf)
	ticketAttachments := attachment2.NewTicketAttachments(genConf, downloader)
	factory := federal.NewFederalEscalationFactory(genConf, ticketAttachments)
	service := federal.NewService(httpRequestHandler, genConf, factory, ticketAttachments)
	return service
}

func InitializeNuggetService(conf *config.Config, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *nugget.Service {
	client := providers.GetVendorHttpClient(gconf)
	signingContext := nilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	string2 := envProvider(gconf)
	httpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, string2)
	service := nugget.NewService(httpRequestHandler, gconf)
	return service
}

// wire.go:

// Returns the CAs used by the vendors
func getRootCAs(gconf *genconf.Config, vendorapiGconf *genconf2.Config) (rootCAs []string) {

	if remittanceFederalRootCa, ok := gconf.Secrets().Ids[config.RemittanceFederalRootCa]; ok {
		rootCAs = append(rootCAs, remittanceFederalRootCa)
	}
	if remittanceFederalRootCaV1, ok := gconf.Secrets().Ids[config.RemittanceFederalRootCaV1]; ok {
		rootCAs = append(rootCAs, remittanceFederalRootCaV1)
	}
	if remittanceFederalRootCaV2, ok := gconf.Secrets().Ids[config.RemittanceFederalRootCaV2]; ok {
		rootCAs = append(rootCAs, remittanceFederalRootCaV2)
	}

	if remittanceFederalIntermediateCa, ok := gconf.Secrets().Ids[config.RemittanceFederalIntermediateCa]; ok {
		rootCAs = append(rootCAs, remittanceFederalIntermediateCa)
	}
	if remittanceFederalIntermediateCaV1, ok := gconf.Secrets().Ids[config.RemittanceFederalIntermediateCaV1]; ok {
		rootCAs = append(rootCAs, remittanceFederalIntermediateCaV1)
	}
	if remittanceFederalIntermediateCaV2, ok := gconf.Secrets().Ids[config.RemittanceFederalIntermediateCaV2]; ok {
		rootCAs = append(rootCAs, remittanceFederalIntermediateCaV2)
	}
	return
}

func nilSigningContextProvider() *dsig.SigningContext {
	return nil
}

func getS3Downloader(ctx context.Context, conf *config.Config) *manager.Downloader {
	awsConf, err := config2.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		logger.Fatal("failed to initialise AWS config", zap.Error(err))
	}
	return manager.NewDownloader(s3.NewFromConfig(awsConf))
}

func getS3Uploader(ctx context.Context, conf *config.Config) *manager.Uploader {
	awsConf, err := config2.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		logger.Fatal("failed to initialise AWS config", zap.Error(err))
	}
	return manager.NewUploader(s3.NewFromConfig(awsConf))
}

var SecureHttpClientNilSignCtxWireSet = wire.NewSet(providers.SecureHttpClientProvider, nilSigningContextProvider)

func emptyHttpClientProvider() *http2.Client {
	return &http2.Client{}
}

func nilVgPciConfigProvider() *config3.Config {
	return nil
}

func qwikcliverConfProvider(conf *config.Config) *config.Qwikcilver {
	return conf.Application.Qwikcilver
}

func envProvider(gconf *genconf.Config) string {
	return gconf.Application().Environment()
}

func getSftpClientForCvlDownload(conf *config.Config) (kra.DownloadISftp, error) {
	sftpClientConfig, err := sftp.NewClientConfig(conf.Secrets.Ids[config.CvlSftpDownloadUser], conf.Secrets.Ids[config.CvlSftpDownloadPass], conf.Application.CvlKra.Host, conf.Application.CvlKra.Port, conf.Secrets.Ids[config.CvlSftpSshKey])
	if err != nil {
		return nil, err
	}
	return sftp.NewSftp(sftpClientConfig), err
}

func getSftpClientForCvlUpload(conf *config.Config) (kra.UploadISftp, error) {
	sftpClientConfig, err := sftp.NewClientConfig(conf.Application.CvlSecrets.GetSftpUploadUser(), conf.Application.CvlSecrets.GetSftpUploadPass(), conf.Application.CvlKra.Host, conf.Application.CvlKra.Port, conf.Secrets.Ids[config.CvlSftpSshKey])
	if err != nil {
		return nil, err
	}
	return sftp.NewSftp(sftpClientConfig), err
}

func getS3DownloaderForMutualFund(ctx context.Context, conf *config.Config) *manager.Downloader {
	awsConf, err := config2.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		logger.Fatal("failed to initialise AWS config", zap.Error(err))
	}

	s3Downloader := manager.NewDownloader(s3.NewFromConfig(awsConf, func(options *s3.Options) {
		options.UsePathStyle = true
	}))

	return s3Downloader
}

func getSftpClientForP2PInvestmentDataDownload(conf *config.Config) (sftp.ISftp, error) {
	sftpClientConfig, err := sftp.NewInsecureClientConfigWithTimeout(conf.Secrets.Ids[config.P2PInvestmentLiquiloansSftpUser], conf.Secrets.Ids[config.P2PInvestmentLiquiloansSftpPassword],
		conf.Application.Liquiloans.SftpHost, conf.Application.Liquiloans.SftpPort, conf.Application.Liquiloans.SftpTimeoutInSeconds)
	if err != nil {
		return nil, err
	}
	return sftp.NewSftp(sftpClientConfig), err
}

func getSftpClientForPreApprovedLoanFederalUpload(conf *genconf.Config) (sftp.ISftp, error) {
	sftpClientConfig, err := sftp.NewClientConfig(conf.Application().Lending().PreApprovedLoan().Federal().UserNameSftp, conf.Application().Lending().PreApprovedLoan().Federal().PasswordSftp, conf.Application().Lending().PreApprovedLoan().Federal().SftpHost, conf.Application().Lending().PreApprovedLoan().Federal().SftpPort, conf.Secrets().Ids[config.FederalSftpSshKey])
	if err != nil {
		return nil, err
	}
	return sftp.NewSftp(sftpClientConfig), err
}

func staticPreapprovedLoanConfigProvider(conf *config.Config) *config.PreApprovedLoan {
	return conf.Application.Lending.PreApprovedLoan
}

func preapprovedLoanConfigProvider(conf *genconf.Config) *genconf.PreApprovedLoan {
	return conf.Application().Lending().PreApprovedLoan()
}

func lendenConfigProvider(conf *genconf.Config) *config.Lenden {
	return conf.Application().Lending().PreApprovedLoan().Lenden()
}

func moneyviewConfigProvider(conf *genconf.Config) *config.Moneyview {
	return conf.Application().Lending().PreApprovedLoan().Moneyview()
}

func digitapConfigProvider(conf *genconf.Config) *config.Digitap {
	return conf.Application().Lending().PreApprovedLoan().Digitap()
}

func setuConfigProvider(conf *genconf.Config) *config.Setu {
	return conf.Application().Lending().PreApprovedLoan().Setu()
}

func securedLoansConfigProvider(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *config.SecuredLoans {
	return gconf.Application().Lending().SecuredLoans()
}

func lendingMfCentralConfigProvider(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *config.LendingMFCentralConfig {
	return gconf.Application().Lending().Collateral().LendingMFCentralConfig
}

func verifyAndDecryptUrl(gconf *genconf.Config) types2.VerifyAndDecryptUrl {
	return types2.VerifyAndDecryptUrl(gconf.Application().MFCentral().VerifyAndDecryptURL)
}

func encryptAndSignUrl(gconf *genconf.Config) types2.EncryptAndSignUrl {
	return types2.EncryptAndSignUrl(gconf.Application().MFCentral().EncryptAndSignURL)
}

func urlProvider(gconf *genconf.Config, vendorapiGconf *genconf2.Config) string {
	return gconf.Application().MFCentral().VerifyAndDecryptURL
}

func dreamfolksConfProvider(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *config.Dreamfolks {
	return gconf.Application().Dreamfolks()
}

func thriweConfProvider(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *config.Thriwe {
	return gconf.Application().Thriwe()
}

func moengageConfProvider(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *config.MoEngage {
	return gconf.Application().MoEngage()
}

func poshvineConfProvider(gconf *genconf.Config, vendorapiGconf *genconf2.Config) *config.Poshvine {
	return gconf.Application().Poshvine()
}

func getSftpClientForVistara(conf *config.Config) (sftp.ISftp, error) {
	var (
		sftpClientConfig *sftp.ClientConfig
		err              error
	)

	sftpClientConfig, err = sftp.NewClientConfigInsecure(conf.Application.Vistara.Sftp.User, conf.Application.Vistara.Sftp.Password, conf.Application.Vistara.Sftp.Host, conf.Application.Vistara.Sftp.Port)
	if err != nil {
		return nil, err
	}

	return sftp.NewSftp(sftpClientConfig), err
}

func initSlackBotClient(conf *config.Config) *slack.Client {

	if conf.Application.Environment == cfg.DevelopmentEnv || conf.Application.Environment == cfg.StagingEnv || conf.Application.Environment == cfg.ProductionEnv {
		slackTokens := conf.Application.SlackTokens

		client := slack.New(slackTokens.BotToken, slack.OptionDebug(true), slack.OptionAppLevelToken(slackTokens.AppToken))

		return client
	}

	return nil
}

func initSocketModeClient(client *slack.Client) *socketmode.Client {
	if client == nil {
		return nil
	}
	return socketmode.New(
		client, socketmode.OptionDebug(true), socketmode.OptionLog(log.New(os.Stdout, "socketmode: ", log.Lshortfile|log.LstdFlags)),
	)
}

func getSftpClientForEnachFederal(conf *config.Config) (sftp.ISftp, error) {
	var enachSftpSecrets config.SftpSecrets
	if err := json.Unmarshal([]byte(conf.Secrets.Ids[config.EpifiFederalEnachSftpSecrets]), &enachSftpSecrets); err != nil {
		return nil, fmt.Errorf("error unmarshalling sftp secrets json to struct, %w", err)
	}

	sftpClientConfig, err := sftp.NewClientConfigInsecureWithKeyExchangeAlgos(enachSftpSecrets.User, enachSftpSecrets.Password, conf.Application.EnachConfig.FederalConfig.SftpConn.Host, conf.Application.EnachConfig.FederalConfig.SftpConn.Port, []string{"diffie-hellman-group-exchange-sha256"})
	if err != nil {
		return nil, err
	}
	return sftp.NewSftp(sftpClientConfig), nil
}

func getSftpClientForEpanFederal(conf *config.Config) (sftp.ISftp, error) {
	var epanSftpSecrets config.SftpSecrets
	if err := json.Unmarshal([]byte(conf.Secrets.Ids[config.EpifiFederalEpanSftpSecrets]), &epanSftpSecrets); err != nil {
		return nil, fmt.Errorf("error unmarshalling sftp secrets json to struct, %w", err)
	}

	sftpClientConfig, err := sftp.NewClientConfigInsecureWithKeyExchangeAlgos(epanSftpSecrets.User, epanSftpSecrets.Password, conf.Application.EpanConfig.FederalConfig.SftpConn.Host, conf.Application.EpanConfig.FederalConfig.SftpConn.Port, []string{"diffie-hellman-group-exchange-sha256"})
	if err != nil {
		return nil, err
	}
	return sftp.NewSftp(sftpClientConfig), nil
}

func idfcConfigProvider(conf *genconf.Config) *genconf.Idfc {
	return conf.Application().Lending().PreApprovedLoan().Idfc()
}

func GetKarzaGetPassbookHttpRequestHandler(karzaGetPassbookHttpClient types2.KarzaGetPassbookHttpClient, xmlSigner *dsig.SigningContext,
	hTTPContentRedactor *httpcontentredactor.HTTPContentRedactor, conf *genconf.Config, vendorapiGconf *genconf2.Config) types2.KarzaGetPassbookHttpRequestHandler {
	client := types2.KarzaGetPassbookHttpClientProvider(karzaGetPassbookHttpClient)
	return vendorapi.New(client, xmlSigner, hTTPContentRedactor, vendorapiGconf, envProvider(conf))
}

func GetCibilHttpRequestHandler(xmlSigner *dsig.SigningContext,
	hTTPContentRedactor *httpcontentredactor.HTTPContentRedactor, conf *genconf.Config, vendorapiGconf *genconf2.Config) types2.CibilHttpRequestHandler {
	env := envProvider(conf)
	client := providers.GetHttpClientForCibil(conf)
	return vendorapi.New(client, xmlSigner, hTTPContentRedactor, vendorapiGconf, env)
}

func disputeSftpConfProvider(conf *config.Config) *config.DisputeSFTP {
	return conf.DisputeSFTP
}

func getSendGridClient(conf *config.Config) sendgrid.ISendGridClient {
	env := conf.Application.Environment
	if env == cfg.TestEnv {
		return sendgrid.NewMockSendGridSender()
	}
	return sendgrid2.NewSendClient(conf.Secrets.Ids[config.SendGridAPIKey])
}

func getSesClient(ctx context.Context, conf *config.Config) ses.ISesClient {
	env := conf.Application.Environment
	if env == cfg.TestEnv {
		return ses.NewMockSESSender()
	}
	awsConf, err := config2.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		logger.Fatal("failed to initialise AWS session", zap.Error(err))
	}
	return sesv2.NewFromConfig(awsConf)
}

func awsSesProvider(conf *config.Config) *config.AwsSes {
	return conf.AwsSes
}

func getFirebaseMessagingClient(conf *config.Config) fcm.IFcmClient {
	env := conf.Application.Environment
	if env == cfg.TestEnv {
		return fcm.NewMockFcmService()
	}
	opt := option.WithCredentialsJSON([]byte(conf.Secrets.Ids[config.FCMServiceAccountCredJson]))
	app, err := firebase.NewApp(context.Background(), nil, opt)
	if err != nil {
		logger.Fatal("cannot initialize firebase app instance", zap.Error(err))
	}

	msgClient, err := app.Messaging(context.Background())
	if err != nil {
		logger.Fatal("cannot get firebase messaging client instance", zap.Error(err))
	}
	return msgClient
}

func fcmAnalyticsLabelProvider(conf *config.Config) string {
	return conf.FcmAnalyticsLabel
}

// returns xml signer to sign an xml request to the vendor
func getXmlSigner(conf *config.Config) (*dsig.SigningContext, error) {
	ksWithValidity, fallbackKs, err := dsig.NewMemoryRSAKeyStoreWithValidity(map[string]*cfg.XMLDigitalSignatureSigningKeyParam{
		conf.Secrets.Ids[config.EpifiFederalUpiPrivateKey]: conf.XMLDigitalSignatureSigner.KeyParams[config.EpifiFederalUpiPrivateKey],
	}, conf.Secrets.Ids[config.EpifiFederalUPIFallbackPrivateKey])
	if err != nil {
		return nil, fmt.Errorf("failed to load xml signer key store: %w", err)
	}

	ctx, err := dsig.NewDefaultSigningCtxFromRsaKeyWithValidity(ksWithValidity, fallbackKs)
	if err != nil {
		return nil, err
	}
	ctx.Canonicalizer = dsig.MakeC14N10RecCanonicalizer()
	ctx.Prefix = ""

	ctx.KeyInfoType = dsig.PublicKey

	return ctx, nil
}

func syncWrapperTimeoutConfigProvider(conf *config.Config) int {
	return conf.Application.SyncWrapperTimeout
}

func asyncReqIdHandlerProvider(conf *config.Config) (request.AsyncReqIDHandler, error) {
	return request.NewAsyncReqIDHandler()
}

func asyncRequestHandlerProvider(client *http2.Client, xmlSigner *dsig.SigningContext, syncWrapperTimeoutInSeconds int, idHandler request.AsyncReqIDHandler, gconf *genconf.Config, vendorapiGconf *genconf2.Config) *vendorapi.AsyncRequestHandler {
	httpContentRedactor := httpcontentredactor.GetInstance()
	httpRequestHandler := vendorapi.New(client, xmlSigner, httpContentRedactor, vendorapiGconf, envProvider(gconf))
	asyncRequestHandler := vendorapi.NewAsyncRequestHandler(httpRequestHandler, syncWrapperTimeoutInSeconds, idHandler, httpContentRedactor)
	return asyncRequestHandler
}

func listKeysAsyncRequestHandlerProvider(client types2.UpiListKeysHttpClient, xmlSigner *dsig.SigningContext, syncWrapperTimeoutInSeconds int, idHandler request.AsyncReqIDHandler, gconf *genconf.Config, vendorapiGconf *genconf2.Config) upi.ListKeyAsyncHandler {
	httpContentRedactor := httpcontentredactor.GetInstance()
	httpRequestHandler := vendorapi.New(client, xmlSigner, httpContentRedactor, vendorapiGconf, envProvider(gconf))
	asyncRequestHandler := vendorapi.NewAsyncRequestHandler(httpRequestHandler, syncWrapperTimeoutInSeconds, idHandler, httpContentRedactor)
	return asyncRequestHandler
}

func InitCryptors(ctx context.Context, conf *config.Config) (*cryptormap.InMemoryCryptorStore, error) {
	var (
		rsaCryptor crypto.Cryptor
	)

	awsConfig, err := config2.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		return nil, fmt.Errorf("failed to initialise AWS config: %w", err)
	}

	es := wire2.InitializeInMemoryStore(secretsmanager.NewFromConfig(awsConfig))

	err = es.AddInternalPrivateKeyFromSecret(conf.PGPInMemoryEntityStoreParams.InternalEntity)
	if err != nil {
		return nil, err
	}

	err = es.AddExternalPublicKeysFromSecret(conf.PGPInMemoryEntityStoreParams.ExternalEntity)
	if err != nil {
		return nil, err
	}

	pgpCryptor := wire2.InitializePGPCryptor(es)

	if conf.Flags.EnableFederalCardDecryptionByFallbackKey {
		rsaCryptor = v2.NewDecryptOnlyCryptor([]string{conf.Secrets.Ids[config.EpifiFederalCardDataPrivateKey],
			conf.Secrets.Ids[config.EpifiFederalCardDataPrivateKeyFallBack]})
	} else {
		rsaCryptor = v2.NewDecryptOnlyCryptor([]string{conf.Secrets.Ids[config.EpifiFederalCardDataPrivateKey]})
	}
	if rsaCryptor == nil {
		return nil, fmt.Errorf("failed to create RSA cryptor")
	}
	cryptorStore := &cryptormap.InMemoryCryptorStore{}
	cryptorStore.AddCryptor(vendorgateway.Vendor_FEDERAL_BANK, vendorgateway.CryptorType_PGP, pgpCryptor)
	cryptorStore.AddCryptor(vendorgateway.Vendor_FEDERAL_BANK, vendorgateway.CryptorType_RSA, rsaCryptor)

	vgCryptorStore := &v2_2.VgCryptorStore{}
	vgCryptorStore.AddCryptor(vendorgateway.Vendor_FEDERAL_BANK, vendorgateway.CryptorType_PGP, pgpCryptor)
	vgCryptorStore.AddCryptor(vendorgateway.Vendor_FEDERAL_BANK, vendorgateway.CryptorType_RSA, rsaCryptor)
	v2_2.StoreCryptorStoreMap(vgCryptorStore)
	cryptormap.NewVendorCryptorMap(cryptorStore)

	return cryptorStore, nil
}

func getSftpClientForAcl(gconf *genconf.Config) (sftp.ISftp, error) {
	aclSftpSecrets := gconf.Application().AclSftp()
	sftpClientConfig, err := sftp.NewClientConfigInsecure(aclSftpSecrets.User(), gconf.Secrets().Ids[config.AclSftpSecretKey], aclSftpSecrets.Host(), aclSftpSecrets.Port())
	if err != nil {
		return nil, err
	}
	return sftp.NewSftp(sftpClientConfig), nil
}

func InitializePaymentGatewayService(gconf *genconf.Config, vendorapiGconf *genconf2.Config, conf *config.Config) *pg.Service {
	client := providers.GetHttpClientForRazorpay(gconf)
	signingContext := nilSigningContextProvider()

	httpContentRedactor := httpcontentredactor.GetInstance()
	razorpayHttpRequestHandler := vendorapi.New(client, signingContext, httpContentRedactor, vendorapiGconf, envProvider(gconf))
	service := pg.NewService(razorpayHttpRequestHandler, conf)
	return service
}
