// nolint: dupl
package saven

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"

	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	ccVgPb "github.com/epifi/gamma/api/vendorgateway/creditcard"
	ccVendorsPb "github.com/epifi/gamma/api/vendors/saven/creditcard"
)

// GenerateCreditCardSdkAuthTokenRequest represents a request to generate an authentication token
// for the credit card SDK. It implements enhanced security through JWT-based payload signing.
type GenerateCreditCardSdkAuthTokenRequest struct {
	Req     *ccVgPb.GenerateCreditCardSdkAuthTokenRequest
	Method  string
	ApiKey  string
	BaseUrl string
	Signer  *JWTSigner // JWT signer for securing the payload
}

func (c *GenerateCreditCardSdkAuthTokenRequest) HTTPMethod() string {
	return c.Method
}

func (c *GenerateCreditCardSdkAuthTokenRequest) URL() string {
	return c.BaseUrl + "/onb/handshake"
}

func (c *GenerateCreditCardSdkAuthTokenRequest) GetResponse() vendorapi.Response {
	return &GenerateCreditCardSdkAuthTokenResponse{}
}

func (c *GenerateCreditCardSdkAuthTokenRequest) SetAuth(r *http.Request) *http.Request {
	r.Header.Add("api-key", c.ApiKey)
	return r
}

// Marshal prepares the request for transmission by:
// 1. Converting our internal protobuf representation to the vendor's expected format
// 2. Marshaling the request to JSON
// 3. Signing the JSON payload with JWT to enhance security
//
// The JWT signing provides several security benefits:
// - Prevents tampering with the request payload
// - Adds time-bound validity through expiration claims
// - Prevents replay attacks by including unique identifiers
// - Ensures only authorized clients can make requests
func (c *GenerateCreditCardSdkAuthTokenRequest) Marshal() ([]byte, error) {
	if c.Req == nil {
		return nil, errors.New("request cannot be nil")
	}

	if c.Req.ApplicantType == ccEnumsV2Pb.CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_PRE_APPROVED_ONBOARDING &&
		c.Req.GetPreApprovedInfo() == nil {
		return nil, errors.New("pre_approved_info cannot be nil for pre-approved applicant")
	}

	applicationType, ok := applicantTypeEnumToStringMap[c.Req.GetApplicantType()]
	if !ok {
		return nil, errors.New(fmt.Sprintf("invalid application type: %s", c.Req.GetApplicantType().String()))
	}

	deviceDetailsStr, err := constructDeviceDetailsString(c.Req.GetDeviceInfo().GetDevice())
	if err != nil {
		return nil, errors.Wrap(err, "failed to construct device details string")
	}

	// Convert our internal protobuf to the vendor's expected format
	vgRequest := &ccVendorsPb.GenerateCreditCardSdkAuthTokenRequest{
		ApplicantType: applicationType,
		UserInfo: &ccVendorsPb.UserInfo{
			EmailAddress:   c.Req.GetUserInfo().GetEmailAddress(),
			PhoneNumber:    c.Req.GetUserInfo().GetPhoneNumber().ToSignedString(),
			PhoneType:      c.Req.GetUserInfo().GetPhoneType(),
			InternalUserId: c.Req.GetUserInfo().GetInternalUserId(),
		},
		DeviceInfo: &ccVendorsPb.DeviceInfo{
			FiAppVersion:  strconv.Itoa(int(c.Req.GetDeviceInfo().GetDevice().GetAppVersion())),
			DeviceId:      c.Req.GetDeviceInfo().GetDevice().GetDeviceId(),
			IpAddress:     c.Req.GetDeviceInfo().GetDevice().GetNetworkMetaData().GetIpV4(),
			Timestamp:     dateTimePkg.TimestampToString(timestamp.Now(), time.RFC3339Nano, dateTimePkg.IST),
			DeviceDetails: deviceDetailsStr,
		},
	}

	if c.Req.GetConsentInfo() != nil {
		vgRequest.ConsentInfo = &ccVendorsPb.ConsentInfo{
			Consents: make([]*ccVendorsPb.Consent, 0),
		}
		for _, consent := range c.Req.GetConsentInfo().GetConsents() {
			vgRequest.ConsentInfo.Consents = append(vgRequest.ConsentInfo.Consents, &ccVendorsPb.Consent{
				IsConsentRecorded: consent.GetIsConsentRecorded(),
				ConsentType:       consent.GetConsentType(),
				ConsentCategory:   consent.GetConsentCategory(),
			})
		}
	}

	if c.Req.GetPreApprovedInfo() != nil {
		vgRequest.PreApprovedInfo = &ccVendorsPb.PreApprovedInfo{
			PreApprovedLimit: strconv.FormatInt(moneyPkg.ConvertMoneyToIntUsingMultiplier(c.Req.GetPreApprovedInfo().GetPreApprovedLimit(), 1), 10),
			PreApprovedExp:   dateTimePkg.DateToString(c.Req.GetPreApprovedInfo().GetPreApprovedExp(), time.DateOnly, dateTimePkg.IST),
		}
	}

	if c.Req.GetPanInfo() != nil {
		vgRequest.PanInfo = &ccVendorsPb.PanInfo{
			PermanentAccountNumber: c.Req.GetPanInfo().GetPanNumber(),
			PanFullName:            c.Req.GetPanInfo().GetUserName().ToString(),
		}
	}

	// Marshal the request to JSON
	marshalledReq, err := protojson.Marshal(vgRequest)
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal request")
	}

	// Sign the JSON payload with JWT
	// This adds security by:
	// 1. Preventing tampering with the request payload
	// 2. Adding time-bound validity through expiration claims
	// 3. Preventing replay attacks by including unique identifiers (jti)
	// 4. Ensuring only authorized clients can make requests
	signedToken, err := c.Signer.SignPayload(string(marshalledReq))
	if err != nil {
		return nil, errors.Wrap(err, "failed to sign payload")
	}

	reqStruct := &ccVendorsPb.CreditCardSdkAuthTokenizedRequest{
		Token: signedToken,
	}
	reqJson, err := protojson.Marshal(reqStruct)
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal request")
	}
	return reqJson, nil
}

// constructDeviceDetailsString creates a formatted device details string from device information
// Format: "os;appVersion;model"
// Example: "ios;2.2091.018;iPhone12,1" or "android;2.2091.018;Pixel 7"
func constructDeviceDetailsString(device *commontypes.Device) (string, error) {
	if device == nil {
		return "", errors.New("device details cannot be empty")
	}

	// Determine OS type based on device type
	var osType string
	switch device.GetPlatform() {
	case commontypes.Platform_ANDROID:
		osType = "android"
	case commontypes.Platform_IOS:
		osType = "ios"
	default:
		return "", errors.New(fmt.Sprintf("unknown device platform: %s", device.GetPlatform().String()))
	}

	// Get app version as string
	appVersion := strconv.Itoa(int(device.GetAppVersion()))

	// Get device model, clean it if needed
	model := device.GetModel()
	// Remove any semicolons to prevent format issues
	model = strings.ReplaceAll(model, ";", ",")

	// Format as "os;appVersion;model"
	return fmt.Sprintf("%s;%s;%s", osType, appVersion, model), nil
}

type GenerateCreditCardSdkAuthTokenResponse struct {
}

func (r *GenerateCreditCardSdkAuthTokenResponse) Unmarshal(data []byte) (proto.Message, error) {
	if len(data) == 0 {
		return nil, errors.New("empty response data")
	}

	vgResponse := &ccVendorsPb.GenerateCreditCardSdkAuthTokenResponse{}
	if err := protojson.Unmarshal(data, vgResponse); err != nil {
		return nil, errors.Wrap(err, "failed to unmarshal response")
	}

	if vgResponse.GetError() != nil {
		return &ccVgPb.GenerateCreditCardSdkAuthTokenResponse{
			Status: rpc.StatusInternalWithDebugMsg(vgResponse.GetError().GetErrorMessage()),
		}, nil
	}

	if vgResponse.GetStatus() != successStatus {
		return &ccVgPb.GenerateCreditCardSdkAuthTokenResponse{
			Status: rpc.StatusInternalWithDebugMsg(vgResponse.GetStatus()),
		}, nil
	}

	if vgResponse.GetData() == nil {
		return nil, errors.New("response data is nil")
	}

	wfCreatedAt, err := dateTimePkg.ParseStringTimestampProtoInLocation(vgResponse.GetData().GetWorkflow().GetUpdatedAt(), time.RFC3339, dateTimePkg.IST)
	if err != nil {
		logger.ErrorNoCtx("failed to parse workflow created at time", zap.Error(err))
	}
	wfUpdatedAt, err := dateTimePkg.ParseStringTimestampProtoInLocation(vgResponse.GetData().GetWorkflow().GetUpdatedAt(), time.RFC3339, dateTimePkg.IST)
	if err != nil {
		logger.ErrorNoCtx("failed to parse workflow updated at time", zap.Error(err))
	}

	return &ccVgPb.GenerateCreditCardSdkAuthTokenResponse{
		Status:     rpc.StatusOk(),
		ModuleName: vgAuthModuleToEnumMap[vgResponse.GetData().GetModuleName()],
		AuthToken:  vgResponse.GetData().GetToken(),
		AdditionalInfo: &ccVgPb.TokenGenerationAdditionalInfo{
			WorkflowId:      vgResponse.GetData().GetWorkflow().GetWorkflowId(),
			UserLocalId:     vgResponse.GetData().GetWorkflow().GetUserLocalId(),
			ExternalUserId:  vgResponse.GetData().GetWorkflow().GetHandshakeUserLocalId(),
			WorkflowStatus:  onbWorkflowToOnbCardRequestStatusMap[vgResponse.GetData().GetWorkflow().GetWorkflowStatus()],
			WorkflowState:   ccOnbStageToOnbStageEnumMap[vgResponse.GetData().GetWorkflow().GetWorkflowState()],
			WorkflowMessage: vgResponse.GetData().GetWorkflow().GetWorkflowMessage(),
			CreatedAt:       wfCreatedAt,
			UpdatedAt:       wfUpdatedAt,
		},
	}, nil
}
