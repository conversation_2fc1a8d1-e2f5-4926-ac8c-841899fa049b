// nolint: dogsled
package pinot

import (
	"context"
	"errors"
	"flag"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"

	tieringPinotPb "github.com/epifi/gamma/api/tiering/pinot"
	"github.com/epifi/gamma/api/types"
	"github.com/epifi/gamma/tiering/pinot/dao"
	mocks2 "github.com/epifi/gamma/tiering/pinot/dao/mocks"
	"github.com/epifi/gamma/tiering/pinot/dao/model"
	"github.com/epifi/gamma/tiering/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, _, _, _, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestService_GetAverageEODBalanceInDateRange(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockPinotDao := mocks2.NewMockIEODBalanceJournalDao(ctrl)
	defer ctrl.Finish()

	fromTimestamp := timestampPb.New(time.Date(2023, 10, 15, 0, 0, 0, 0, time.UTC))
	toTimestamp := timestampPb.New(time.Date(2023, 11, 15, 0, 0, 0, 0, time.UTC))

	type mockStruct struct {
		mockPinotDao *mocks2.MockIEODBalanceJournalDao
	}
	type fields struct {
		pinotDao dao.IEODBalanceJournalDao
	}
	type args struct {
		ctx   context.Context
		req   *tieringPinotPb.GetAverageEODBalanceInDateRangeRequest
		mocks func(mockStruct *mockStruct)
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *tieringPinotPb.GetAverageEODBalanceInDateRangeResponse
		wantErr bool
	}{
		{
			name: "success",
			fields: fields{
				pinotDao: mockPinotDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPinotPb.GetAverageEODBalanceInDateRangeRequest{
					ActorId:       "actor-1",
					FromTimestamp: fromTimestamp,
					ToTimestamp:   toTimestamp,
				},
				mocks: func(m *mockStruct) {
					m.mockPinotDao.EXPECT().GetTotalSavingsBalanceInTimeRange(gomock.Any(), "actor-1", fromTimestamp.AsTime().Unix(), toTimestamp.AsTime().Unix()).
						Return(&model.EODBalanceAggregate{
							LedgerBalance:    300000,
							AvailableBalance: 300000,
							Count:            30,
						}, nil)
				},
			},
			want: &tieringPinotPb.GetAverageEODBalanceInDateRangeResponse{
				Status:     rpcPb.StatusOk(),
				AvgBalance: 10000,
				NumOfDays:  30,
			},
			wantErr: false,
		},
		{
			name: "error, record not found",
			fields: fields{
				pinotDao: mockPinotDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPinotPb.GetAverageEODBalanceInDateRangeRequest{
					ActorId:       "actor-1",
					FromTimestamp: fromTimestamp,
					ToTimestamp:   toTimestamp,
				},
				mocks: func(m *mockStruct) {
					m.mockPinotDao.EXPECT().GetTotalSavingsBalanceInTimeRange(gomock.Any(), "actor-1", fromTimestamp.AsTime().Unix(), toTimestamp.AsTime().Unix()).
						Return(nil, epifierrors.ErrRecordNotFound)
				},
			},
			want: &tieringPinotPb.GetAverageEODBalanceInDateRangeResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "error, random error",
			fields: fields{
				pinotDao: mockPinotDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPinotPb.GetAverageEODBalanceInDateRangeRequest{
					ActorId:       "actor-1",
					FromTimestamp: fromTimestamp,
					ToTimestamp:   toTimestamp,
				},
				mocks: func(m *mockStruct) {
					m.mockPinotDao.EXPECT().GetTotalSavingsBalanceInTimeRange(gomock.Any(), "actor-1", fromTimestamp.AsTime().Unix(), toTimestamp.AsTime().Unix()).
						Return(nil, errors.New("some random error"))
				},
			},
			want: &tieringPinotPb.GetAverageEODBalanceInDateRangeResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "success with valid timezone",
			fields: fields{
				pinotDao: mockPinotDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPinotPb.GetAverageEODBalanceInDateRangeRequest{
					ActorId:       "actor-1",
					FromTimestamp: fromTimestamp,
					ToTimestamp:   toTimestamp,
					Timezone:      "Asia/Kolkata",
				},
				mocks: func(m *mockStruct) {
					// Timestamps converted to Asia/Kolkata timezone and rounded to start of day
					// Original: 2023-10-15 00:00:00 UTC -> 2023-10-15 05:30:00 IST -> 2023-10-15 00:00:00 IST
					// Original: 2023-11-15 00:00:00 UTC -> 2023-11-15 05:30:00 IST -> 2023-11-15 00:00:00 IST
					istLocation, _ := time.LoadLocation("Asia/Kolkata")
					expectedFromTimestamp := time.Date(2023, 10, 15, 0, 0, 0, 0, istLocation).Unix()
					expectedToTimestamp := time.Date(2023, 11, 15, 0, 0, 0, 0, istLocation).Unix()

					m.mockPinotDao.EXPECT().GetTotalSavingsBalanceInTimeRange(gomock.Any(), "actor-1", expectedFromTimestamp, expectedToTimestamp).
						Return(&model.EODBalanceAggregate{
							LedgerBalance:    300000,
							AvailableBalance: 300000,
							Count:            30,
						}, nil)
				},
			},
			want: &tieringPinotPb.GetAverageEODBalanceInDateRangeResponse{
				Status:     rpcPb.StatusOk(),
				AvgBalance: 10000,
				NumOfDays:  30,
			},
			wantErr: false,
		},
		{
			name: "error with invalid timezone",
			fields: fields{
				pinotDao: mockPinotDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPinotPb.GetAverageEODBalanceInDateRangeRequest{
					ActorId:       "actor-1",
					FromTimestamp: fromTimestamp,
					ToTimestamp:   toTimestamp,
					Timezone:      "Invalid/Timezone",
				},
				mocks: func(m *mockStruct) {
					// No DAO call expected as timezone conversion should fail early
				},
			},
			want: &tieringPinotPb.GetAverageEODBalanceInDateRangeResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "success with empty timezone (backward compatibility)",
			fields: fields{
				pinotDao: mockPinotDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPinotPb.GetAverageEODBalanceInDateRangeRequest{
					ActorId:       "actor-1",
					FromTimestamp: fromTimestamp,
					ToTimestamp:   toTimestamp,
					Timezone:      "",
				},
				mocks: func(m *mockStruct) {
					// Should behave exactly like the original test case
					m.mockPinotDao.EXPECT().GetTotalSavingsBalanceInTimeRange(gomock.Any(), "actor-1", fromTimestamp.AsTime().Unix(), toTimestamp.AsTime().Unix()).
						Return(&model.EODBalanceAggregate{
							LedgerBalance:    300000,
							AvailableBalance: 300000,
							Count:            30,
						}, nil)
				},
			},
			want: &tieringPinotPb.GetAverageEODBalanceInDateRangeResponse{
				Status:     rpcPb.StatusOk(),
				AvgBalance: 10000,
				NumOfDays:  30,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				pinotDao: tt.fields.pinotDao,
			}
			m := &mockStruct{
				mockPinotDao: mockPinotDao,
			}
			tt.args.mocks(m)
			got, err := s.GetAverageEODBalanceInDateRange(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAverageEODBalanceInDateRange() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAverageEODBalanceInDateRange() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetAverageEODBalanceInDateRangeDayWise(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockPinotDao := mocks2.NewMockIEODBalanceJournalDao(ctrl)
	defer ctrl.Finish()

	fromTimestamp := timestampPb.New(time.Date(2023, 10, 15, 0, 0, 0, 0, time.UTC))
	toTimestamp := timestampPb.New(time.Date(2023, 11, 14, 0, 0, 0, 0, time.UTC))

	type mockStruct struct {
		mockPinotDao *mocks2.MockIEODBalanceJournalDao
	}
	type fields struct {
		pinotDao dao.IEODBalanceJournalDao
	}
	type args struct {
		ctx   context.Context
		req   *tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseRequest
		mocks func(mockStruct *mockStruct)
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse
		wantErr bool
	}{
		{
			name: "success",
			fields: fields{
				pinotDao: mockPinotDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseRequest{
					ActorId:       "actor-1",
					FromTimestamp: fromTimestamp,
					ToTimestamp:   toTimestamp,
				},
				mocks: func(m *mockStruct) {
					m.mockPinotDao.EXPECT().GetTotalSavingsBalanceInTimeRangeGroupByDate(gomock.Any(), "actor-1", fromTimestamp.AsTime().Unix(), toTimestamp.AsTime().Unix()).
						Return([]*model.EODBalanceAggregate{
							{
								LedgerBalance:    100000,
								AvailableBalance: 100000,
								Count:            1,
								CalDate:          timestampPb.New(time.Date(2023, time.October, 16, 0, 0, 0, 0, time.Local)).GetSeconds(),
							},
							{
								LedgerBalance:    400000,
								AvailableBalance: 400000,
								Count:            1,
								CalDate:          timestampPb.New(time.Date(2023, time.October, 17, 0, 0, 0, 0, time.Local)).GetSeconds(),
							},
						}, nil)
				},
			},
			want: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
				Status: rpcPb.StatusOk(),
				DayWiseEodBalance: []*tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate{
					{
						Balance: 100000,
						Date: &types.Date{
							Year:  2023,
							Month: 10,
							Day:   16,
						},
					},
					{
						Balance: 400000,
						Date: &types.Date{
							Year:  2023,
							Month: 10,
							Day:   17,
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "error, record not found",
			fields: fields{
				pinotDao: mockPinotDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseRequest{
					ActorId:       "actor-1",
					FromTimestamp: fromTimestamp,
					ToTimestamp:   toTimestamp,
				},
				mocks: func(m *mockStruct) {
					m.mockPinotDao.EXPECT().GetTotalSavingsBalanceInTimeRangeGroupByDate(gomock.Any(), "actor-1", fromTimestamp.AsTime().Unix(), toTimestamp.AsTime().Unix()).
						Return(nil, epifierrors.ErrRecordNotFound)
				},
			},
			want: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "error, random error",
			fields: fields{
				pinotDao: mockPinotDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseRequest{
					ActorId:       "actor-1",
					FromTimestamp: fromTimestamp,
					ToTimestamp:   toTimestamp,
				},
				mocks: func(m *mockStruct) {
					m.mockPinotDao.EXPECT().GetTotalSavingsBalanceInTimeRangeGroupByDate(gomock.Any(), "actor-1", fromTimestamp.AsTime().Unix(), toTimestamp.AsTime().Unix()).
						Return(nil, errors.New("some random error"))
				},
			},
			want: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "should give error when time is greater than 30 days",
			fields: fields{
				pinotDao: mockPinotDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseRequest{
					ActorId:       "actor-1",
					FromTimestamp: fromTimestamp,
					ToTimestamp:   timestampPb.New(fromTimestamp.AsTime().Add(40 * 24 * time.Hour)),
				},
				mocks: func(m *mockStruct) {
				},
			},
			want: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "success with valid timezone",
			fields: fields{
				pinotDao: mockPinotDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseRequest{
					ActorId:       "actor-1",
					FromTimestamp: fromTimestamp,
					ToTimestamp:   toTimestamp,
					Timezone:      "Asia/Kolkata",
				},
				mocks: func(m *mockStruct) {
					// Timestamps converted to Asia/Kolkata timezone and rounded to start of day
					istLocation, _ := time.LoadLocation("Asia/Kolkata")
					expectedFromTimestamp := time.Date(2023, 10, 15, 0, 0, 0, 0, istLocation).Unix()
					expectedToTimestamp := time.Date(2023, 11, 14, 0, 0, 0, 0, istLocation).Unix()

					m.mockPinotDao.EXPECT().GetTotalSavingsBalanceInTimeRangeGroupByDate(gomock.Any(), "actor-1", expectedFromTimestamp, expectedToTimestamp).
						Return([]*model.EODBalanceAggregate{
							{
								LedgerBalance:    100000,
								AvailableBalance: 100000,
								Count:            1,
								CalDate:          timestampPb.New(time.Date(2023, time.October, 16, 0, 0, 0, 0, istLocation)).GetSeconds(),
							},
							{
								LedgerBalance:    400000,
								AvailableBalance: 400000,
								Count:            1,
								CalDate:          timestampPb.New(time.Date(2023, time.October, 17, 0, 0, 0, 0, istLocation)).GetSeconds(),
							},
						}, nil)
				},
			},
			want: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
				Status: rpcPb.StatusOk(),
				DayWiseEodBalance: []*tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate{
					{
						Balance: 100000,
						Date: &types.Date{
							Year:  2023,
							Month: 10,
							Day:   16,
						},
					},
					{
						Balance: 400000,
						Date: &types.Date{
							Year:  2023,
							Month: 10,
							Day:   17,
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "error with invalid timezone",
			fields: fields{
				pinotDao: mockPinotDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseRequest{
					ActorId:       "actor-1",
					FromTimestamp: fromTimestamp,
					ToTimestamp:   toTimestamp,
					Timezone:      "Invalid/Timezone",
				},
				mocks: func(m *mockStruct) {
					// No DAO call expected as timezone conversion should fail early
				},
			},
			want: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "success with empty timezone (backward compatibility)",
			fields: fields{
				pinotDao: mockPinotDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseRequest{
					ActorId:       "actor-1",
					FromTimestamp: fromTimestamp,
					ToTimestamp:   toTimestamp,
					Timezone:      "",
				},
				mocks: func(m *mockStruct) {
					// Should behave exactly like the original test case
					m.mockPinotDao.EXPECT().GetTotalSavingsBalanceInTimeRangeGroupByDate(gomock.Any(), "actor-1", fromTimestamp.AsTime().Unix(), toTimestamp.AsTime().Unix()).
						Return([]*model.EODBalanceAggregate{
							{
								LedgerBalance:    100000,
								AvailableBalance: 100000,
								Count:            1,
								CalDate:          timestampPb.New(time.Date(2023, time.October, 16, 0, 0, 0, 0, time.Local)).GetSeconds(),
							},
							{
								LedgerBalance:    400000,
								AvailableBalance: 400000,
								Count:            1,
								CalDate:          timestampPb.New(time.Date(2023, time.October, 17, 0, 0, 0, 0, time.Local)).GetSeconds(),
							},
						}, nil)
				},
			},
			want: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
				Status: rpcPb.StatusOk(),
				DayWiseEodBalance: []*tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate{
					{
						Balance: 100000,
						Date: &types.Date{
							Year:  2023,
							Month: 10,
							Day:   16,
						},
					},
					{
						Balance: 400000,
						Date: &types.Date{
							Year:  2023,
							Month: 10,
							Day:   17,
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should give error when time is greater than 30 days with timezone",
			fields: fields{
				pinotDao: mockPinotDao,
			},
			args: args{
				ctx: context.Background(),
				req: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseRequest{
					ActorId:       "actor-1",
					FromTimestamp: fromTimestamp,
					ToTimestamp:   timestampPb.New(fromTimestamp.AsTime().Add(40 * 24 * time.Hour)),
					Timezone:      "Asia/Kolkata",
				},
				mocks: func(m *mockStruct) {
					// No DAO call expected as time range validation should fail
				},
			},
			want: &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				pinotDao: tt.fields.pinotDao,
			}
			m := &mockStruct{
				mockPinotDao: mockPinotDao,
			}
			tt.args.mocks(m)
			got, err := s.GetAverageEODBalanceInDateRangeDayWise(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAverageEODBalanceInDateRange() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAverageEODBalanceInDateRange() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_convertTimestampsToTimezone(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockPinotDao := mocks2.NewMockIEODBalanceJournalDao(ctrl)
	defer ctrl.Finish()

	service := &Service{
		pinotDao: mockPinotDao,
	}

	fromTime := time.Date(2023, 10, 15, 12, 30, 0, 0, time.UTC)
	toTime := time.Date(2023, 11, 15, 15, 45, 0, 0, time.UTC)
	ctx := context.Background()

	tests := []struct {
		name                 string
		timezone             string
		fromTime             time.Time
		toTime               time.Time
		wantErr              bool
		expectedFromTime     time.Time
		expectedToTime       time.Time
		expectedLocationName string
		expectNilLocation    bool
	}{
		{
			name:                 "empty timezone should return original timestamps",
			timezone:             "",
			fromTime:             fromTime,
			toTime:               toTime,
			wantErr:              false,
			expectedFromTime:     fromTime,
			expectedToTime:       toTime,
			expectedLocationName: "",
			expectNilLocation:    true,
		},
		{
			name:                 "valid timezone should convert timestamps correctly",
			timezone:             "Asia/Kolkata",
			fromTime:             fromTime,
			toTime:               toTime,
			wantErr:              false,
			expectedFromTime:     fromTime.In(mustLoadLocation("Asia/Kolkata")),
			expectedToTime:       toTime.In(mustLoadLocation("Asia/Kolkata")),
			expectedLocationName: "Asia/Kolkata",
			expectNilLocation:    false,
		},
		{
			name:                 "invalid timezone should return error",
			timezone:             "Invalid/Timezone",
			fromTime:             fromTime,
			toTime:               toTime,
			wantErr:              true,
			expectedFromTime:     time.Time{},
			expectedToTime:       time.Time{},
			expectedLocationName: "",
			expectNilLocation:    true,
		},
		{
			name:                 "different timezone should work correctly",
			timezone:             "America/New_York",
			fromTime:             fromTime,
			toTime:               toTime,
			wantErr:              false,
			expectedFromTime:     fromTime.In(mustLoadLocation("America/New_York")),
			expectedToTime:       toTime.In(mustLoadLocation("America/New_York")),
			expectedLocationName: "America/New_York",
			expectNilLocation:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			convertedFrom, convertedTo, location, err := service.convertTimestampsToTimezone(
				ctx, tt.timezone, tt.fromTime, tt.toTime,
			)

			if (err != nil) != tt.wantErr {
				t.Errorf("convertTimestampsToTimezone() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !convertedFrom.Equal(tt.expectedFromTime) {
				t.Errorf("expected from timestamp %v, got %v", tt.expectedFromTime, convertedFrom)
			}

			if !convertedTo.Equal(tt.expectedToTime) {
				t.Errorf("expected to timestamp %v, got %v", tt.expectedToTime, convertedTo)
			}

			if tt.expectNilLocation {
				require.Nil(t, location)
			} else {
				require.NotNil(t, location)
				assert.Equal(t, tt.expectedLocationName, location.String())
			}
		})
	}
}

// Helper function to load timezone location for test setup
func mustLoadLocation(name string) *time.Location {
	loc, err := time.LoadLocation(name)
	if err != nil {
		panic(err)
	}
	return loc
}
