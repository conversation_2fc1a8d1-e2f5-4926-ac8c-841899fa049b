package pinot

// nolint: goimports
import (
	"context"
	"errors"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	tieringPinotPb "github.com/epifi/gamma/api/tiering/pinot"
	"github.com/epifi/gamma/api/types"
	"github.com/epifi/gamma/tiering/helper"
	"github.com/epifi/gamma/tiering/pinot/dao"
)

type Service struct {
	tieringPinotPb.UnimplementedEODBalanceServer
	pinotDao dao.IEODBalanceJournalDao
}

func NewService(
	pinotDao dao.IEODBalanceJournalDao,
) *Service {
	return &Service{
		pinotDao: pinotDao,
	}
}

func (s *Service) GetAverageEODBalanceInDateRange(ctx context.Context, req *tieringPinotPb.GetAverageEODBalanceInDateRangeRequest) (*tieringPinotPb.GetAverageEODBalanceInDateRangeResponse, error) {
	actorId := req.GetActorId()
	timezone := req.GetTimezone()

	// Get timestamps
	fromTimestamp := req.GetFromTimestamp().AsTime()
	toTimestamp := req.GetToTimestamp().AsTime()

	// Convert timestamps to specified timezone if provided
	convertedFromTimestamp, convertedToTimestamp, _, err := s.convertTimestampsToTimezone(ctx, timezone, fromTimestamp, toTimestamp)
	if err != nil {
		return &tieringPinotPb.GetAverageEODBalanceInDateRangeResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	fromTimestampRoundedToStartOfTheDay := helper.RoundDownToStartOfTheDay(convertedFromTimestamp).Unix()
	toTimestampRoundedToStartOfTheDay := helper.RoundDownToStartOfTheDay(convertedToTimestamp).Unix()

	balAggregate, getPinotDaoErr := s.pinotDao.GetTotalSavingsBalanceInTimeRange(ctx, actorId, fromTimestampRoundedToStartOfTheDay, toTimestampRoundedToStartOfTheDay)
	if getPinotDaoErr != nil {
		if errors.Is(getPinotDaoErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "no EOD balance records found for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(getPinotDaoErr))
			return &tieringPinotPb.GetAverageEODBalanceInDateRangeResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error fetching EOD balance aggregate from pinot", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(getPinotDaoErr))
		return &tieringPinotPb.GetAverageEODBalanceInDateRangeResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	return &tieringPinotPb.GetAverageEODBalanceInDateRangeResponse{
		Status:     rpcPb.StatusOk(),
		AvgBalance: balAggregate.AvailableBalance / float64(balAggregate.Count),
		NumOfDays:  balAggregate.Count,
	}, nil
}

func (s *Service) GetAverageEODBalanceInDateRangeDayWise(ctx context.Context, req *tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseRequest) (*tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse, error) {
	actorId := req.GetActorId()
	timezone := req.GetTimezone()

	// Get timestamps
	fromTimestamp := req.GetFromTimestamp().AsTime()
	toTimestamp := req.GetToTimestamp().AsTime()

	// Convert timestamps to specified timezone if provided
	convertedFromTimestamp, convertedToTimestamp, location, err := s.convertTimestampsToTimezone(ctx, timezone, fromTimestamp, toTimestamp)
	if err != nil {
		return &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	if convertedToTimestamp.Sub(convertedFromTimestamp) > 30*24*time.Hour {
		logger.Error(ctx, "max 30 days is allowed")
		return &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	fromTimestampRoundedToStartOfTheDay := helper.RoundDownToStartOfTheDay(convertedFromTimestamp).Unix()
	toTimestampRoundedToStartOfTheDay := helper.RoundDownToStartOfTheDay(convertedToTimestamp).Unix()
	balAggregateDayWise, getPinotDaoErr := s.pinotDao.GetTotalSavingsBalanceInTimeRangeGroupByDate(ctx, actorId, fromTimestampRoundedToStartOfTheDay, toTimestampRoundedToStartOfTheDay)
	if getPinotDaoErr != nil {
		if errors.Is(getPinotDaoErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "no EOD balance records found for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(getPinotDaoErr))
			return &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error fetching EOD balance aggregate from pinot", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(getPinotDaoErr))
		return &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	eodBalanceByDate := []*tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate{}
	for _, balAggregate := range balAggregateDayWise {
		date := dateTimePkg.TimestampToDateInLoc(&timestampPb.Timestamp{
			Seconds: balAggregate.CalDate,
		}, location)
		eodBalanceByDate = append(eodBalanceByDate, &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate{
			Balance: balAggregate.AvailableBalance,
			Date: &types.Date{
				Year:  date.GetYear(),
				Month: date.GetMonth(),
				Day:   date.GetDay(),
			},
		})
	}

	return &tieringPinotPb.GetAverageEODBalanceInDateRangeDayWiseResponse{
		Status:            rpcPb.StatusOk(),
		DayWiseEodBalance: eodBalanceByDate,
	}, nil

}

// convertTimestampsToTimezone converts the given timestamps to the specified timezone.
// Returns the converted timestamps, the location object, and any error.
// If timezone is empty, returns original timestamps unchanged.
func (s *Service) convertTimestampsToTimezone(
	ctx context.Context,
	timezone string,
	fromTimestamp, toTimestamp time.Time,
) (time.Time, time.Time, *time.Location, error) {
	// If no timezone provided, return timestamps unchanged
	if timezone == "" {
		return fromTimestamp, toTimestamp, nil, nil
	}

	// Load timezone location
	location, err := time.LoadLocation(timezone)
	if err != nil {
		logger.Error(ctx, "invalid timezone provided", zap.String("timezone", timezone), zap.Error(err))
		return time.Time{}, time.Time{}, nil, err
	}

	// Convert timestamps to the specified timezone
	convertedFromTimestamp := fromTimestamp.In(location)
	convertedToTimestamp := toTimestamp.In(location)

	return convertedFromTimestamp, convertedToTimestamp, location, nil
}
