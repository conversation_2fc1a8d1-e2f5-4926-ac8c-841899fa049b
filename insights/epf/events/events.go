package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

const (
	EpfSuccessfulImportEvent = "EpfSuccessfulImport"
)

type EpfSuccessfulImport struct {
	EventType       string
	EventId         string
	ActorId         string
	ProspectId      string
	Timestamp       time.Time
	UanNumber       string
	ClientRequestId string
}

func NewEpfSuccessfulImport(actorId, uanNumber, clientRequestId string) *EpfSuccessfulImport {
	return &EpfSuccessfulImport{
		ActorId:         actorId,
		EventType:       events.EventTrack,
		EventId:         uuid.New().String(),
		Timestamp:       time.Now(),
		UanNumber:       uanNumber,
		ClientRequestId: clientRequestId,
	}
}

func (c *EpfSuccessfulImport) GetEventType() string {
	return c.EventType
}

func (c *EpfSuccessfulImport) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *EpfSuccessfulImport) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *EpfSuccessfulImport) GetEventId() string {
	return c.EventId
}

func (c *EpfSuccessfulImport) GetUserId() string {
	return c.ActorId
}

func (c *EpfSuccessfulImport) GetProspectId() string {
	return c.ProspectId
}

func (c *EpfSuccessfulImport) GetEventName() string {
	return EpfSuccessfulImportEvent
}
