package processor

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	moneyPb "github.com/epifi/be-common/pkg/money"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	orderPb "github.com/epifi/gamma/api/order"
	vgAccountsPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	iftPkg "github.com/epifi/gamma/pkg/internationalfundtransfer"
	"github.com/epifi/gamma/scripts/ift/ift_recon/reconcilation/model"
)

type InwardSwiftTxnReconProcessor struct {
	vgAccountsClient vgAccountsPb.AccountsClient
	orderClient      orderPb.OrderServiceClient
}

func NewInwardSwiftTxnReconProcessor(vgAccountsClient vgAccountsPb.AccountsClient, orderClient orderPb.OrderServiceClient) *InwardSwiftTxnReconProcessor {
	return &InwardSwiftTxnReconProcessor{
		vgAccountsClient: vgAccountsClient,
		orderClient:      orderClient,
	}
}

func (p *InwardSwiftTxnReconProcessor) Reconcile(ctx context.Context) (*model.ReconResult, error) {
	fromDate := time.Now().AddDate(0, 0, -3)
	toDate := time.Now().AddDate(0, 0, -1)
	accountTxns, err := p.getInwardPoolAccountStatement(ctx, fromDate, toDate)
	if err != nil {
		logger.Error(ctx, "error while fetching account statement", zap.Error(err))
		return nil, fmt.Errorf("error while fetching account statement: %w", err)
	}
	swiftTxns := getSwiftTxnsFromInwardAccountTxns(ctx, accountTxns)
	if len(swiftTxns) == 0 {
		logger.Info(ctx, "no swift transactions found in account statement")
		return &model.ReconResult{
			Message: ":test_tube: No swift transactions found in account statement",
			Err:     nil,
		}, nil
	}

	userTxns := getUserTxnsFromInwardAccountTxns(ctx, accountTxns)
	if len(userTxns) == 0 {
		logger.Info(ctx, "no user transactions found in account statement")
		return &model.ReconResult{
			Message: ":test_tube: No user transactions found in account statement",
			Err:     nil,
		}, nil
	}
	totalSwiftAmount, err := getTotalAmount(swiftTxns)
	if err != nil {
		logger.Error(ctx, "error while calculating total swift amount", zap.Error(err))
		return nil, fmt.Errorf("error while calculating total swift amount: %w", err)
	}
	totalUserAmount, err := getTotalAmount(userTxns)
	if err != nil {
		logger.Error(ctx, "error while calculating total user amount", zap.Error(err))
		return nil, fmt.Errorf("error while calculating total user amount: %w", err)
	}

	logger.Info(ctx, "total swift amount", zap.Any("totalSwiftAmount", moneyPb.ToDecimal(totalSwiftAmount)))
	logger.Info(ctx, "total user amount", zap.Any("totalUserAmount", moneyPb.ToDecimal(totalUserAmount)))
	// convert date to timestamp
	fromDateTimestamp := timestamppb.New(fromDate)
	toDateTimestamp := timestamppb.New(toDate)
	orderRes, err := p.orderClient.GetOrdersForActor(ctx, &orderPb.GetOrdersForActorRequest{
		ActorId:         iftPkg.AlpacaActorID,
		TransactionType: orderPb.GetOrdersForActorRequest_DEBIT,
		FromTime:        fromDateTimestamp,
		ToTime:          toDateTimestamp,
		FieldMask:       []orderPb.OrderFieldMask{orderPb.OrderFieldMask_ID, orderPb.OrderFieldMask_AMOUNT},
		SortBy:          orderPb.OrderFieldMask_CREATED_AT,
		StatusFilters:   []orderPb.GetOrdersForActorRequest_OrderStatusFilter{orderPb.GetOrdersForActorRequest_SUCCESS},
		PageSize:        30,
		Offset:          0,
		Workflows: []orderPb.OrderWorkflow{
			orderPb.OrderWorkflow_NO_OP,
		},
	})
	if rpcErr := epifigrpc.RPCError(orderRes, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching orders for actor", zap.Error(rpcErr))
		return nil, fmt.Errorf("error while fetching orders for actor: %w", rpcErr)
	}
	totalOrderAmount := moneyPb.ZeroINR().GetPb()
	for _, order := range orderRes.GetOrders() {
		logger.Info(ctx, " processing order", zap.Any("order", order))
		totalOrderAmount, err = moneyPb.Sum(totalOrderAmount, order.GetAmount())
		if err != nil {
			logger.Error(ctx, "error while calculating total swift amount", zap.Error(err))
			return nil, fmt.Errorf("error while calculating total swift amount: %w", err)
		}
	}
	logger.Info(ctx, "total order amount", zap.Any("totalOrderAmount", moneyPb.ToDecimal(totalOrderAmount)))

	return &model.ReconResult{
		Message: "",
		Err:     nil,
	}, nil
}

func getTotalAmount(transactions []*vgAccountsPb.TransactionV1) (*money.Money, error) {
	var (
		totalAmount = moneyPb.ZeroINR().GetPb()
		err         error
	)
	for _, transaction := range transactions {
		totalAmount, err = moneyPb.Sum(totalAmount, transaction.GetAmount())
		if err != nil {
			return nil, err
		}
	}
	return totalAmount, err
}

func getUserTxnsFromInwardAccountTxns(ctx context.Context, transactions []*vgAccountsPb.TransactionV1) []*vgAccountsPb.TransactionV1 {
	// assuming that all debit type transactions belong to user
	userTxns := make([]*vgAccountsPb.TransactionV1, 0)
	for _, transaction := range transactions {
		if transaction.GetTransactionType() == vgAccountsPb.TransactionType_DEBIT {
			userTxns = append(userTxns, transaction)
		}
	}
	logTransactions(ctx, userTxns)
	return userTxns
}

func getSwiftTxnsFromInwardAccountTxns(ctx context.Context, transactions []*vgAccountsPb.TransactionV1) []*vgAccountsPb.TransactionV1 {
	// assuming that all credit type transactions belong to swift
	swiftTxns := make([]*vgAccountsPb.TransactionV1, 0)
	for _, transaction := range transactions {
		if transaction.GetTransactionType() == vgAccountsPb.TransactionType_CREDIT {
			swiftTxns = append(swiftTxns, transaction)
		}
	}
	logTransactions(ctx, swiftTxns)
	return swiftTxns
}

func (p *InwardSwiftTxnReconProcessor) getInwardPoolAccountStatement(ctx context.Context, fromDate time.Time, toDate time.Time) ([]*vgAccountsPb.TransactionV1, error) {
	getAccountStatementRequest := &vgAccountsPb.GetAccountStatementRequest{
		Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		AccountNumber: iftPkg.USStocksInwardRemittancePoolAccountNumber,
		FromDate: &date.Date{
			Year:  safeInt32(fromDate.Year()),
			Month: safeInt32(int(fromDate.Month())),
			Day:   safeInt32(fromDate.Day()),
		},
		ToDate: &date.Date{
			// Safe conversion from int to int32 using helper function
			Year:  safeInt32(toDate.Year()),
			Month: safeInt32(int(toDate.Month())),
			Day:   safeInt32(toDate.Day()),
		},
		ApiType: vgAccountsPb.GetAccountStatementRequest_API_TYPE_STATEMENT_DR_API,
	}
	return getPoolAccountStatement(ctx, p.vgAccountsClient, getAccountStatementRequest)
}

func getPoolAccountStatement(ctx context.Context, vgAccountsClient vgAccountsPb.AccountsClient, getAccountStatementRequest *vgAccountsPb.GetAccountStatementRequest) ([]*vgAccountsPb.TransactionV1, error) {
	logger.Info(ctx, "getAccountStatementRequest", zap.Any("getAccountStatementRequest", getAccountStatementRequest))
	lastReadPageNo := int32(0)
	transactionData := make([]*vgAccountsPb.TransactionV1, 0)
	for {
		getAccountStatementRequest.PageNumber = lastReadPageNo
		getAccountStatementByDrApiResponse, err := vgAccountsClient.GetAccountStatementByDrApi(ctx,
			getAccountStatementRequest)
		if err = epifigrpc.RPCError(getAccountStatementByDrApiResponse, err); err != nil {
			logger.Error(ctx, fmt.Sprint("error while invoking GetAccountStatementByDrApi rpc", zap.Error(err)))
			return nil, fmt.Errorf("error while invoking GetAccountStatementByDrApi rpc: %w", err)
		}
		transactionData = append(transactionData, getAccountStatementByDrApiResponse.GetTransactionData()...)
		if !getAccountStatementByDrApiResponse.GetHasMoreRows() {
			logger.Info(ctx, " no more rows to read from account statement", zap.Int32("lastReadPageNo", lastReadPageNo))
			break
		}
		lastReadPageNo = getAccountStatementByDrApiResponse.GetPageNumber()
	}
	logger.Info(ctx, "total transactions fetched from account statement", zap.Int("totalTransactions", len(transactionData)))
	logTransactions(ctx, transactionData)
	// todo: filter out duplicate transactions if any
	return transactionData, nil
}

func logTransactions(ctx context.Context, transactions []*vgAccountsPb.TransactionV1) {
	for i, transaction := range transactions {
		logger.Info(ctx, fmt.Sprintf("%v. transaction", i), zap.Any("transaction", transaction))
	}
}
