package main

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/user"
)

// AdHocJobUpdateKycName is a powerful tool; use with caution
type <PERSON>H<PERSON><PERSON>obUpdate<PERSON>yc<PERSON>ame struct {
	userClient user.UsersClient
}

func (a *AdHocJobUpdateKycName) DoJob(ctx context.Context, req *JobRequest) error {
	var (
		// Hard-coded it so that there's an explicit record of when/who/why updated the kyc name
		// Debit card name for this user is updated as per KYC name through dev action update_user_profile_name
		// list of user ids for which kyc name is updated:
		// 9ef685be-3867-4899-932d-030be3a3cf64, https://epifi.slack.com/archives/C0101A42ZFW/p1735201675419529
		// 5edbc54a-a4c4-469f-82bb-01e9b8402077, https://monorail.pointz.in/p/fi-app/issues/detail?id=93023
		// 7b0009ae-b4e7-4f41-9665-a716f0997d34 https://monorail.pointz.in/p/fi-app/issues/detail?id=97184
		userId = "7b0009ae-b4e7-4f41-9665-a716f0997d34"
	)

	userResp, err := a.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_Id{
			Id: userId,
		},
	})
	if te := epifigrpc.RPCError(userResp, err); te != nil {
		if err = epifigrpc.RPCError(userResp, err); err != nil {
			return fmt.Errorf("error in GetUser: %w", err)
		}
		return nil
	}
	kycNameBefore := userResp.GetUser().GetProfile().GetKycName()
	userResp.GetUser().GetProfile().KycName = userResp.GetUser().GetProfile().GetDebitCardName()

	userUpdResp, err := a.userClient.UpdateUser(ctx, &user.UpdateUserRequest{
		User:       userResp.GetUser(),
		UpdateMask: []user.UserFieldMask{user.UserFieldMask_KYC_NAME},
	})
	if err = epifigrpc.RPCError(userUpdResp, err); err != nil {
		return fmt.Errorf("error in UpdateUser: %w", err)
	}
	logger.Info(ctx, fmt.Sprintf("kycNameBefore: %v , kycNameAfter: %v", kycNameBefore, userUpdResp.GetUser().GetProfile().GetKycName()))
	return nil
}
