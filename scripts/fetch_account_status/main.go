package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	accountsOperStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	actorPb "github.com/epifi/gamma/api/actor"
	beSavingsPb "github.com/epifi/gamma/api/savings"

	"github.com/gocarina/gocsv"
	"go.uber.org/zap"
)

var (
	actorIdsPathFlag = flag.String("actorIdsPath", "", "file path for CSV containing actor IDs")
	actorIdsFlag     = flag.String("actorIds", "", "comma-separated list of actor IDs")

	// clients
	savingsClient beSavingsPb.SavingsClient
	actorClient   actorPb.ActorClient
)

const (
	sleepDuration = time.Second
	batchSize     = 2
)

// ActorIdCsv represents the structure of the CSV file
type ActorIdCsv struct {
	ActorId string `csv:"actor_id"`
}

func getActorIdsAfterCleaning(actorIdsUncleaned string) []string {
	actorIds := make([]string, 0)
	actorIdsCleaned := strings.Split(actorIdsUncleaned, ",")
	for _, actorId := range actorIdsCleaned {
		cleanedActorId := strings.TrimSpace(actorId)
		cleanedActorId = strings.Trim(cleanedActorId, "\n")
		actorIds = append(actorIds, cleanedActorId)
	}

	return actorIds
}

// nolint:funlen
func main() {
	flag.Parse()

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()

	var actorIdList []string

	// Determine input source using switch instead of if-else chain
	switch {
	case *actorIdsPathFlag != "":
		// Process CSV file input
		actorIdFile, err := os.OpenFile(filepath.Clean(*actorIdsPathFlag), os.O_RDONLY, os.ModePerm)
		if err != nil {
			logger.Fatal("failed to open actor ID file", zap.Error(err))
		}

		defer func() {
			closeErr := actorIdFile.Close()
			if closeErr != nil {
				logger.ErrorNoCtx("error in closing actorIdFile", zap.Error(closeErr))
			}
		}()

		var actorIds []*ActorIdCsv
		if err = gocsv.UnmarshalFile(actorIdFile, &actorIds); err != nil {
			logger.Fatal("error in Unmarshal the file", zap.Error(err))
		}

		if len(actorIds) == 0 {
			logger.Fatal("no actor IDs extracted from the CSV file")
		}

		// Extract actor IDs from CSV
		for _, actorIdFromCsv := range actorIds {
			actorIdList = append(actorIdList, actorIdFromCsv.ActorId)
		}

		logger.InfoNoCtx("total records read from csv", zap.Int("records", len(actorIdList)))

	case *actorIdsFlag != "":
		// Process from command line input as comma-separated IDs
		actorIdList = getActorIdsAfterCleaning(*actorIdsFlag)
		logger.InfoNoCtx("total records from command line", zap.Int("records", len(actorIdList)))

	default:
		logger.Fatal("Either -actorIds or -actorIdsPath must be provided")
	}

	if len(actorIdList) == 0 {
		logger.InfoNoCtx("exiting the script since no actor IDs were extracted")
		return
	}

	// Initialize connections
	savingsConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	accountConn := epifigrpc.NewConnByService(cfg.ACCOUNTS_SERVICE)

	// Initialize clients
	savingsClient = beSavingsPb.NewSavingsClient(savingsConn)
	actorClient = actorPb.NewActorClient(actorConn)
	accountsClient := accountsOperStatusPb.NewOperationalStatusServiceClient(accountConn)

	// Ensure connections are closed when the function ends
	defer func() {
		epifigrpc.CloseConn(savingsConn)
		epifigrpc.CloseConn(actorConn)
		epifigrpc.CloseConn(accountConn)
	}()

	var failedActorIds []string
	var accountDoesNotExistActorIds []string
	var statusString []string

	for idx, actorId := range actorIdList {
		if idx%50 == 0 {
			logger.InfoNoCtx(fmt.Sprintf("index checkpoint for actor-id (idx modulo 50), idx: %d", idx))
		}

		// Sleep based on batch size to avoid overwhelming services
		if (idx+1)%batchSize == 0 {
			time.Sleep(sleepDuration)
		}

		ctx := epificontext.CtxWithActorId(context.Background(), actorId)
		actorResp, actorErr := actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
		if actorErr = epifigrpc.RPCError(actorResp, actorErr); actorErr != nil {
			logger.Error(ctx, "error fetching actor by id", zap.Error(actorErr))
			failedActorIds = append(failedActorIds, actorId)
			continue
		}
		// Get savings account by user id
		savingsAccResp, savingsAccErr := savingsClient.GetAccount(ctx, &beSavingsPb.GetAccountRequest{
			Identifier: &beSavingsPb.GetAccountRequest_PrimaryUserId{PrimaryUserId: actorResp.GetActor().GetEntityId()}})
		if savingsAccErr != nil || savingsAccResp.GetAccount() == nil {
			logger.Error(ctx, "error fetching savings account by user id", zap.Error(savingsAccErr))
			failedActorIds = append(failedActorIds, actorId)
			continue
		}
		// Get Balance by savings account id
		accStatusResp, accStatusErr := accountsClient.GetOperationalStatus(ctx, &accountsOperStatusPb.GetOperationalStatusRequest{
			DataFreshness: accountsOperStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE,
			AccountIdentifier: &accountsOperStatusPb.GetOperationalStatusRequest_SavingsAccountId{
				SavingsAccountId: savingsAccResp.GetAccount().GetId(),
			},
		})
		if rpcErr := epifigrpc.RPCError(accStatusResp, accStatusErr); rpcErr != nil {
			logger.Error(ctx, "error fetching account status", zap.Error(rpcErr))
			failedActorIds = append(failedActorIds, actorId)
			continue
		}
		var lienPresent string
		if accStatusResp.GetOperationalStatusInfo().GetTotalLienMarking().GetUnits() != 0 {
			lienPresent = "TRUE"
		} else {
			lienPresent = "FALSE"
		}
		statusString = append(statusString,
			actorId+" freeze raw codes: "+strings.Join(accStatusResp.GetOperationalStatusInfo().GetVendorResponse().GetFederalAccountStatusEnquiryResponse().GetFreezeReasonCodes(), ", ")+" freeze status: "+accStatusResp.GetOperationalStatusInfo().GetFreezeStatus().String()+" account status: "+accStatusResp.GetOperationalStatusInfo().GetOperationalStatus().String()+" account open date: "+accStatusResp.GetOperationalStatusInfo().GetAccountOpenedAt().String()+" account close date: "+accStatusResp.GetOperationalStatusInfo().GetAccountClosedAt().String()+" lien present: "+lienPresent)
		statusString = append(statusString, "kycReviewDate: "+datetime.DateToDDMMYYYY(accStatusResp.GetOperationalStatusInfo().GetKYCComplianceInfo().GetKYCReviewDate()))
		statusString = append(statusString, "kycDueDate: "+datetime.DateToDDMMYYYY(accStatusResp.GetOperationalStatusInfo().GetKYCComplianceInfo().GetKYCDueDate()))
		statusString = append(statusString, "kycGracePeriodDate: "+datetime.DateToDDMMYYYY(accStatusResp.GetOperationalStatusInfo().GetKYCComplianceInfo().GetKYCGracePeriodDate()))
	}

	fmt.Println("actor status output")
	fmt.Println("--------------------")
	fmt.Println()
	for _, i2 := range statusString {
		fmt.Println(i2)
	}
	fmt.Println()
	fmt.Println("--------------------")
	logger.InfoNoCtx(fmt.Sprintf("Process failed for actor id: %v ", failedActorIds))
	fmt.Println("--------------------")
	logger.InfoNoCtx(fmt.Sprintf("Account not present for: %v ", accountDoesNotExistActorIds))
}
