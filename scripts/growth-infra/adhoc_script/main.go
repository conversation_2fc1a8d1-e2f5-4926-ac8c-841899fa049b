// nolint:gosec
package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/user"

	"github.com/epifi/gamma/scripts/growth-infra/adhoc_script/config"
)

var (
	jobName = flag.String("JobName", "", "job name, refer to jobNames for accepted values")
	input1  = flag.String("input1", "", "first input needed by the script")
	input2  = flag.String("input2", "", "second input needed by the script")
)

// nolint:funlen
func main() {
	flag.Parse()

	var (
		env string
		err error
	)

	env, err = cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	// load config
	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
	}

	ctx := context.Background()

	// rewards db conn
	db, err := storagev2.NewPostgresDBWithConfig(conf.RewardsDb, false)
	if err != nil {
		logger.Panic("failed to initialize rewards db", zap.Error(err))
	}

	var jobTypeToProcessorMap = map[JobName]JobProcessor{
		UPDATE_REWARD_EXPIRY: &JobUpdateRewardExpiry{
			conf:         conf,
			rewardDBConn: db,
		},
		INSERT_BACKDATED_SEGMENT_INSTANCE: &JobInsertBackdatedSegmentInstance{
			conf: conf,
		},
		SET_REWARD_EXPIRY: &JobSetRewardExpiry{
			conf:         conf,
			rewardDBConn: db,
		},
		UPDATE_REWARD_STATUS_TO_EXPIRY: &JobUpdateRewardStatusToExpiry{
			rewardDBConn: db,
		},
		PUBLISH_TIERING_REWARD_SUMMARY_EMAIL: &JobPublishTieringRewardSummaryEmail{
			conf:         conf,
			rewardDBConn: db,
		},
	}

	job := *jobName

	jobProcessor := jobTypeToProcessorMap[JobName(job)]
	if jobProcessor == nil {
		logger.Panic("invalid job name", zap.String("job", job))
	}

	fmt.Println("---------------------------------- JOB START ----------------------------------")
	if err = jobProcessor.DoJob(ctx, &JobRequest{
		Args1: *input1,
		Args2: *input2,
	}); err != nil {
		logger.Error(ctx, fmt.Sprintf("error in job: %v", job), zap.Error(err))
		defer os.Exit(1)
		return
	}
	fmt.Println("---------------------------------- JOB END ------------------------------------")
}

func setCommsPreference() {
	userServiceConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(userServiceConn)
	userClient := user.NewUsersClient(userServiceConn)

	ctx, cancelFunc := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancelFunc()

	actorIds := []string{
		"AC220417EsLYpQXIR329BIzk3aYW6g==",
		"AC2301215NMPuvt9S3W2Y+LCWvzGEg==",
		"ACWJZ6bRf9T2KaUrbxmXrEng230422==",
		"AC220212tBLHIk9cQc2OT3bcZAV5TQ==",
		"ACVGeFUCC8RkyMdU+HhSmnww230827==",
		"AC220522SS/mebj9R0eCAvKk08vSuQ==",
		"AC2301215NMPuvt9S3W2Y+LCWvzGEg==",
		"AC230124NHqS/5RiRd+ix5PZyVtjjw==",
		"AC221221M2vRUz/7QRS6cmTyMQTKqQ==",
		"AC210714VQ13OQAJS5qPBAcoLMviFQ==",
		"AC4B7o3Wusn3240804",
		"AC210809IbEkHJJmSx+RjmpsYcYXNA==",
		"AC210621XaLELkcsSZqVWon8Ph/tJw==",
		"AC2206017WwlBCxWRzq/Fpr9f7sHFA==",
		"AC220320mdQj+ry9RWu6LXdZypIfLQ==",
		"AC230130Tp7qvvsNTei9kNovFziLqw==",
		"AC210621XaLELkcsSZqVWon8Ph/tJw==",
		"ACWJZ6bRf9T2KaUrbxmXrEng230422==",
		"AC210914qMdhCqO3Ssyw9uJRST2wCw==",
	}

	commsPreference := buildCommsPreference(user.CommsSignal_OFF)
	for _, actorId := range actorIds {
		setUserPreferenceResp, err := userClient.SetUserPreferences(ctx, &user.SetUserPreferencesRequest{
			ActorId: actorId,
			Preferences: []*user.PreferenceTypeValuePair{
				{
					PreferenceType: user.PreferenceType_PREFERENCE_TYPE_APP_COMMS,
					PreferenceValue: &user.PreferenceValue{
						PrefVal: &user.PreferenceValue_AppCommsPreference{
							AppCommsPreference: commsPreference,
						},
					},
				},
			},
		})
		if te := epifigrpc.RPCError(setUserPreferenceResp, err); te != nil {
			logger.Error(ctx, "unable to set user comms preference user", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(te))
		}
	}

	logger.Info(ctx, "successfully set user comms preference")
}

func buildCommsPreference(commsSignal user.CommsSignal) *user.CommsPreference {
	return &user.CommsPreference{
		CommsPreferenceInfos: []*user.CommsPreferenceInfo{
			{
				Area:     comms.Area_ALL,
				Medium:   comms.Medium_NOTIFICATION,
				Feature:  comms.Feature_PROMOTIONS,
				Signal:   commsSignal,
				Category: comms.Category_CATEGORY_PROMOTIONAL,
			},
			{
				Area:     comms.Area_ALL,
				Medium:   comms.Medium_SMS,
				Feature:  comms.Feature_PROMOTIONS,
				Signal:   commsSignal,
				Category: comms.Category_CATEGORY_PROMOTIONAL,
			},
			{
				Area:     comms.Area_ALL,
				Medium:   comms.Medium_WHATSAPP,
				Feature:  comms.Feature_PROMOTIONS,
				Signal:   commsSignal,
				Category: comms.Category_CATEGORY_PROMOTIONAL,
			},
			{
				Area:     comms.Area_ALL,
				Medium:   comms.Medium_EMAIL,
				Feature:  comms.Feature_PROMOTIONS,
				Signal:   commsSignal,
				Category: comms.Category_CATEGORY_PROMOTIONAL,
			},
		},
	}
}
