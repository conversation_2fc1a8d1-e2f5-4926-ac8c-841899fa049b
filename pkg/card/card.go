// nolint:gocritic
package card

import (
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	colorPkg "github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"

	"github.com/epifi/gamma/api/frontend/deeplink"
	fireflyFePb "github.com/epifi/gamma/api/frontend/firefly"
	externalPb "github.com/epifi/gamma/api/tiering/external"
	dcScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/debitcard"
	"github.com/epifi/gamma/api/typesv2/ui"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

var (
	chargesV1LaunchDate, _  = datetime.ParseStringTimeStampProto("2006-01-02", "2023-02-10")
	chargesV2LaunchDate, _  = datetime.ParseStringTimeStampProto("2006-01-02", "2023-10-25")
	chargesV3LaunchDate, _  = datetime.ParseStringTimeStampProto("2006-01-02", "2024-02-23")
	chargesV4LaunchDate, _  = datetime.ParseStringTimeStampProto("2006-01-02", "2024-08-08")
	ActualPhysicalDCCharges = &moneyPb.Money{
		CurrencyCode: "INR",
		Units:        399,
	}
)

// GetPhysicalCardChargesAtTime will fetch the charges for physical card at a particular time for a particular tier. These charges will not
// include GST. If needed, the caller should add a 18% GST on top of the amount returned from this function
func GetPhysicalCardChargesAtTime(time *timestamp.Timestamp, tier externalPb.Tier) *moneyPb.Money {
	switch {
	case !datetime.IsBefore(time, chargesV4LaunchDate):
		switch tier {
		case externalPb.Tier_TIER_FI_BASIC,
			externalPb.Tier_TIER_FI_REGULAR,
			externalPb.Tier_TIER_FI_PLUS,
			externalPb.Tier_TIER_FI_SALARY_LITE,
			externalPb.Tier_TIER_FI_AA_SALARY_BAND_1,
			externalPb.Tier_TIER_FI_AA_SALARY_BAND_2:
			return &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        299,
			}
		case externalPb.Tier_TIER_FI_INFINITE,
			externalPb.Tier_TIER_FI_SALARY,
			externalPb.Tier_TIER_FI_AA_SALARY_BAND_3:
			return &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        199,
				Nanos:        0,
			}
		default:
			return nil
		}
	case !datetime.IsBefore(time, chargesV3LaunchDate):
		switch tier {
		case externalPb.Tier_TIER_FI_REGULAR, externalPb.Tier_TIER_FI_BASIC,
			externalPb.Tier_TIER_FI_PLUS, externalPb.Tier_TIER_FI_AA_SALARY_BAND_1:
			return &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        299,
			}
		case externalPb.Tier_TIER_FI_INFINITE,
			externalPb.Tier_TIER_FI_SALARY,
			externalPb.Tier_TIER_FI_AA_SALARY_BAND_2,
			externalPb.Tier_TIER_FI_AA_SALARY_BAND_3,
			externalPb.Tier_TIER_FI_SALARY_LITE:
			return &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        199,
				Nanos:        0,
			}
		default:
			return nil
		}
	case !datetime.IsBefore(time, chargesV2LaunchDate):
		switch tier {
		case externalPb.Tier_TIER_FI_INFINITE,
			externalPb.Tier_TIER_FI_SALARY,
			externalPb.Tier_TIER_FI_AA_SALARY_BAND_2,
			externalPb.Tier_TIER_FI_AA_SALARY_BAND_3,
			externalPb.Tier_TIER_FI_SALARY_LITE,
			externalPb.Tier_TIER_FI_PLUS:
			return &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        0,
			}
		default:
			return &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        199,
			}
		}
	case !datetime.IsBefore(time, chargesV1LaunchDate):
		return &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        0,
		}
	default:
		return &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        0,
		}
	}
}

func GetAtmLocatorScreenDeeplink() *deeplink.Deeplink {
	return deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_ATM_LOCATOR_SCREEN, &dcScreenOptionsPb.AtmLocatorScreenOptions{
		TopNavBar: &dcScreenOptionsPb.AtmLocatorScreenOptions_NavBar{
			Title: commontypes.GetTextFromStringFontColourFontStyle(atmLocatorScreenTitleText, colorPkg.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_M),
			DrawableProperties: &fireflyFePb.DrawableProperties{
				BgColor: widget.GetBlockBackgroundColour(colorPkg.ColorDarkBase),
				CornerProperty: &fireflyFePb.CornerProperty{
					BottomStartCorner: atmLocatorScreenNavBarCornerRadius,
					BottomEndCorner:   atmLocatorScreenNavBarCornerRadius,
				},
			},
		},
		LocationAccessPopup: &dcScreenOptionsPb.AtmLocatorScreenOptions_LocationAccessPopup{
			TopIcon:        commontypes.GetVisualElementFromUrlHeightAndWidth(locationPopupTopIcon, locationPopupTopIconHeightAndWeight, locationPopupTopIconHeightAndWeight),
			ClosePopupIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(locationPopupCloseIcon, locationPopupCloseIconHeightAndWeight, locationPopupCloseIconHeightAndWeight),
			Title:          commontypes.GetTextFromStringFontColourFontStyle(locationPopupTitleText, colorPkg.ColorDarkLayer2, commontypes.FontStyle_SUBTITLE_L),
			SubTitle:       commontypes.GetTextFromStringFontColourFontStyle(locationPopupDescriptionText, colorPkg.ColorDarkLayer2, commontypes.FontStyle_BODY_S),
			PrimaryBtn: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(locationPopupBtnText, colorPkg.ColorLightPrimaryAction, commontypes.FontStyle_BUTTON_M)).
				WithContainerProperties(&ui.IconTextComponent_ContainerProperties{
					BgColor:       colorPkg.ColorSnow,
					Height:        locationPopupBtnHeight,
					Width:         locationPopupBtnWidth,
					CornerRadius:  locationPopupBtnCornerRadius,
					LeftPadding:   locationPopupBtnLeftAndRightPaddings,
					RightPadding:  locationPopupBtnLeftAndRightPaddings,
					TopPadding:    locationPopupBtnBottomAndTopPaddings,
					BottomPadding: locationPopupBtnBottomAndTopPaddings,
					ShadowHeight:  locationPopupBtnShadowHeight,
					ShadowColor:   onyxColor,
				}),
		},
	})
}

func GetCardUsageScreenDl(cardId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CARD_USAGE_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CardUsageScreenOptions{
			CardUsageScreenOptions: &deeplink.CardUsageScreenOptions{
				CardId: cardId,
			},
		},
	}
}

func GetCardLimitsScreenDl(cardId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CARD_LIMITS_HOME_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CardLimitHomeScreenOptions{
			CardLimitHomeScreenOptions: &deeplink.CardLimitHomeScreenOptions{
				CardId: cardId,
			},
		},
	}
}
