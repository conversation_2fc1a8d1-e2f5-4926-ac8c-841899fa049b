Application:
  Environment: "test"
  Name: "employment"

EmploymentRedisOptions:
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 3
  HystrixCommand:
    CommandName: "user_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 100ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

EmploymentRedisOptionsV2:
  Addrs:
    - "localhost:6379"
  RedisDB: 3
  Hystrix:
    CommandName: "user_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 100ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

Tracing:
  Enable: false
EnableHomeUpdateEmploymentNoticeBar: true
CompanyMappingFilePath: "stubs/user_company_mappings.csv"
NameMatchThresholdScore: 2
URLValidationRegex: (?:http(s)?://)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\(\)\*\+,;=.]+
EmailValidationRegex: (?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|\"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*\")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])
DomainNameRegex: ^(?:https?:\/\/)?(?:[^@\/\n]+@)?(?:www\.)?([^:\/\n]+)
AllowedDomainsForPersonalProfile:
  - "linkedin"
  - "behance"
  - "dribbble"
  - "fiverr"
  - "toptal"
  - "freelancer"
  - "upwork"
EmploymentVerificationProcessMaxDuration: 60s

EmploymentVerificationPublisher:
  QueueName: "employment-verification-queue"

EmploymentVerificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "employment-verification-queue"
  RetryStrategy: #retrying for ~85 mins
    ExponentialBackOff:
      BaseInterval: 5
      MaxAttempts: 10
      TimeUnit: "Second"

LinkedinVerificationPublisher:
  QueueName: "linkedin-verification-queue"

IncomeUpdatePublisher:
  TopicName: "income-update-event-topic"

LinkedinVerificationSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "linkedin-verification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 3
      TimeUnit: "Second"

UpdateEmploymentPublisher:
  TopicName: "employment-update-topic"
CollectOccupation:
  MinAndroidVersion: 2
  MinIOSVersion: 1
  FallbackToEnableFeature: true
  DisableFeature: false

EmployerPiMappingUpdateEventSqsPublisher:
  QueueName: "employer-pi-mapping-update-event-queue"

EmploymentProofUiView:
  Infos:
    - Type: "COMPANY_NAME"
      DisplayString: "Company Name"
      ToolTip: "Please enter full company name, without abbreviations 🙏"
      Manadatory: true
    - Type: "PERSONAL_PROFILE"
      DisplayString: "URL showing your work details"
      ToolTip: "Ex: Linkedin, Behance, Dribbble, Fiverr, Toptal, Freelancer, Upwork"
      Manadatory: true
    - Type: "GSTIN_NUMBER"
      DisplayString: "Enter GSTIN Number (optional)"
      ToolTip: ""
      Manadatory: false
    - Type: "ENROLLMENT_NUMBER"
      DisplayString: "Enter Enrollment Number (optional)"
      ToolTip: ""
      Manadatory: false
    - Type: "STUDENT_EMAIL_ID"
      DisplayString: "Enter Student Email ID (optional)"
      ToolTip: ""
      Manadatory: false
EmploymentTypeUiViewAED:
  HeaderDisplayString: "Employment Type"
  EmploymentTypes:
    - Type: "SALARIED"
      DisplayString: "Salaried"
      EnableEmploymentType:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
      MaxNormalIncome: ***********
      AnnualSalaryConstraint:
        - MinAge: 0
          MaxAge: 200
          AnnualSalaryOptions:
            - DisplayString: "Less than 4,000 AED (Below ₹1L)"
              MinValue: 0
              MaxValue: 100000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 0
                MaxValue: 4000
                CurrencyCode: "AED"
            - DisplayString: "4,000 - 22,000 AED (₹1L-₹5L)"
              MinValue: 100000
              MaxValue: 500000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 4000
                MaxValue: 22000
                CurrencyCode: "AED"
            - DisplayString: "22,000 - 44,000 AED (₹5L-₹10L)"
              MinValue: 500000
              MaxValue: 1000000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 22000
                MaxValue: 44000
                CurrencyCode: "AED"
            - DisplayString: "44,000 - 110,000 AED (₹10L-₹25L)"
              MinValue: 1000000
              MaxValue: 2500000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 44000
                MaxValue: 110000
                CurrencyCode: "AED"
            - DisplayString: "110,000 - 440,000 AED (₹25L-₹1Cr)"
              MinValue: 2500000
              MaxValue: 10000000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 110000
                MaxValue: 440000
                CurrencyCode: "AED"
            - DisplayString: "440,000 + AED (Above ₹1Cr)"
              MinValue: 10000000
              MaxValue: ********
              CurrencyCode: "INR"
              Alternate:
                MinValue: 440000
                MaxValue: 1100000
                CurrencyCode: "AED"
      SupportedOccupationTypes:
        - "ALL"
      RequiresEmployer: true
      RequiresOccupation: true
    - Type: "SELF_EMPLOYED"
      DisplayString: "Self-Employed/Business Owner"
      EnableEmploymentType:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
      MaxNormalIncome: ***********
      AnnualSalaryConstraint:
        - MinAge: 0
          MaxAge: 200
          AnnualSalaryOptions:
            - DisplayString: "Less than 4,000 AED (Below ₹1L)"
              MinValue: 0
              MaxValue: 100000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 0
                MaxValue: 4000
                CurrencyCode: "AED"
            - DisplayString: "4,000 - 22,000 AED (₹1L-₹5L)"
              MinValue: 100000
              MaxValue: 500000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 4000
                MaxValue: 22000
                CurrencyCode: "AED"
            - DisplayString: "22,000 - 44,000 AED (₹5L-₹10L)"
              MinValue: 500000
              MaxValue: 1000000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 22000
                MaxValue: 44000
                CurrencyCode: "AED"
            - DisplayString: "44,000 - 110,000 AED (₹10L-₹25L)"
              MinValue: 1000000
              MaxValue: 2500000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 44000
                MaxValue: 110000
                CurrencyCode: "AED"
            - DisplayString: "110,000 - 440,000 AED (₹25L-₹1Cr)"
              MinValue: 2500000
              MaxValue: 10000000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 110000
                MaxValue: 440000
                CurrencyCode: "AED"
            - DisplayString: "440,000 + AED (Above ₹1Cr)"
              MinValue: 10000000
              MaxValue: ********
              CurrencyCode: "INR"
              Alternate:
                MinValue: 440000
                MaxValue: 1100000
                CurrencyCode: "AED"
      SupportedOccupationTypes:
        - "ALL"
      RequiresEmployer: false
      RequiresOccupation: true
    - Type: "RETIRED"
      DisplayString: "Retired"
      EnableEmploymentType:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
      MaxNormalIncome: 4000000
      AnnualSalaryConstraint:
        - MinAge: 0
          MaxAge: 200
          AnnualSalaryOptions:
            - DisplayString: "Less than 4,000 AED (Below ₹1L)"
              MinValue: 0
              MaxValue: 100000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 0
                MaxValue: 4000
                CurrencyCode: "AED"
            - DisplayString: "4,000 - 22,000 AED (₹1L-₹5L)"
              MinValue: 100000
              MaxValue: 500000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 4000
                MaxValue: 22000
                CurrencyCode: "AED"
            - DisplayString: "22,000 - 44,000 AED (₹5L-₹10L)"
              MinValue: 500000
              MaxValue: 1000000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 22000
                MaxValue: 44000
                CurrencyCode: "AED"
            - DisplayString: "44,000 - 110,000 AED (₹10L-₹25L)"
              MinValue: 1000000
              MaxValue: 2500000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 44000
                MaxValue: 110000
                CurrencyCode: "AED"
            - DisplayString: "110,000 - 440,000 AED (₹25L-₹1Cr)"
              MinValue: 2500000
              MaxValue: 10000000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 110000
                MaxValue: 440000
                CurrencyCode: "AED"
            - DisplayString: "440,000 + AED (Above ₹1Cr)"
              MinValue: 10000000
              MaxValue: ********
              CurrencyCode: "INR"
              Alternate:
                MinValue: 440000
                MaxValue: 1100000
                CurrencyCode: "AED"
      SupportedOccupationTypes:
        - "OCCUPATION_TYPE_RETIRED"
      RequiresEmployer: false
      RequiresOccupation: false
    - Type: "FREELANCER"
      DisplayString: "Freelancer"
      EnableEmploymentType:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
      MaxNormalIncome: ***********
      AnnualSalaryConstraint:
        - MinAge: 0
          MaxAge: 200
          AnnualSalaryOptions:
            - DisplayString: "Less than 4,000 AED (Below ₹1L)"
              MinValue: 0
              MaxValue: 100000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 0
                MaxValue: 4000
                CurrencyCode: "AED"
            - DisplayString: "4,000 - 22,000 AED (₹1L-₹5L)"
              MinValue: 100000
              MaxValue: 500000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 4000
                MaxValue: 22000
                CurrencyCode: "AED"
            - DisplayString: "22,000 - 44,000 AED (₹5L-₹10L)"
              MinValue: 500000
              MaxValue: 1000000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 22000
                MaxValue: 44000
                CurrencyCode: "AED"
            - DisplayString: "44,000 - 110,000 AED (₹10L-₹25L)"
              MinValue: 1000000
              MaxValue: 2500000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 44000
                MaxValue: 110000
                CurrencyCode: "AED"
            - DisplayString: "110,000 - 440,000 AED (₹25L-₹1Cr)"
              MinValue: 2500000
              MaxValue: 10000000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 110000
                MaxValue: 440000
                CurrencyCode: "AED"
            - DisplayString: "440,000 + AED (Above ₹1Cr)"
              MinValue: 10000000
              MaxValue: ********
              CurrencyCode: "INR"
              Alternate:
                MinValue: 440000
                MaxValue: 1100000
                CurrencyCode: "AED"
      SupportedOccupationTypes:
        - "ALL"
      RequiresEmployer: false
      RequiresOccupation: true
    - Type: "STUDENT"
      DisplayString: "Student"
      EnableEmploymentType:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
      MaxNormalIncome: 1000000
      AnnualSalaryConstraint:
        - MinAge: 0
          MaxAge: 200
          AnnualSalaryOptions:
            - DisplayString: "Less than 4,000 AED (Below ₹1L)"
              MinValue: 0
              MaxValue: 100000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 0
                MaxValue: 4000
                CurrencyCode: "AED"
            - DisplayString: "4,000 - 22,000 AED (₹1L-₹5L)"
              MinValue: 100000
              MaxValue: 500000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 4000
                MaxValue: 22000
                CurrencyCode: "AED"
            - DisplayString: "22,000 - 44,000 AED (₹5L-₹10L)"
              MinValue: 500000
              MaxValue: 1000000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 22000
                MaxValue: 44000
                CurrencyCode: "AED"
            - DisplayString: "44,000 - 110,000 AED (₹10L-₹25L)"
              MinValue: 1000000
              MaxValue: 2500000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 44000
                MaxValue: 110000
                CurrencyCode: "AED"
            - DisplayString: "110,000 - 440,000 AED (₹25L-₹1Cr)"
              MinValue: 2500000
              MaxValue: 10000000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 110000
                MaxValue: 440000
                CurrencyCode: "AED"
            - DisplayString: "440,000 + AED (Above ₹1Cr)"
              MinValue: 10000000
              MaxValue: ********
              CurrencyCode: "INR"
              Alternate:
                MinValue: 440000
                MaxValue: 1100000
                CurrencyCode: "AED"
      SupportedOccupationTypes:
        - "OCCUPATION_TYPE_STUDENT"
      RequiresEmployer: false
      RequiresOccupation: false
    - Type: "HOMEMAKER"
      DisplayString: "Homemaker"
      EnableEmploymentType:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
      MaxNormalIncome: 1000000
      AnnualSalaryConstraint:
        - MinAge: 0
          MaxAge: 200
          AnnualSalaryOptions:
            - DisplayString: "Less than 4,000 AED (Below ₹1L)"
              MinValue: 0
              MaxValue: 100000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 0
                MaxValue: 4000
                CurrencyCode: "AED"
            - DisplayString: "4,000 - 22,000 AED (₹1L-₹5L)"
              MinValue: 100000
              MaxValue: 500000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 4000
                MaxValue: 22000
                CurrencyCode: "AED"
            - DisplayString: "22,000 - 44,000 AED (₹5L-₹10L)"
              MinValue: 500000
              MaxValue: 1000000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 22000
                MaxValue: 44000
                CurrencyCode: "AED"
            - DisplayString: "44,000 - 110,000 AED (₹10L-₹25L)"
              MinValue: 1000000
              MaxValue: 2500000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 44000
                MaxValue: 110000
                CurrencyCode: "AED"
            - DisplayString: "110,000 - 440,000 AED (₹25L-₹1Cr)"
              MinValue: 2500000
              MaxValue: 10000000
              CurrencyCode: "INR"
              Alternate:
                MinValue: 110000
                MaxValue: 440000
                CurrencyCode: "AED"
            - DisplayString: "440,000 + AED (Above ₹1Cr)"
              MinValue: 10000000
              MaxValue: ********
              CurrencyCode: "INR"
              Alternate:
                MinValue: 440000
                MaxValue: 1100000
                CurrencyCode: "AED"
      SupportedOccupationTypes:
        - "OCCUPATION_TYPE_HOMEMAKER"
      RequiresEmployer: false
      RequiresOccupation: false
  DefaultType: "SALARIED"
AnnualSalaryUIView2:
  Hint: "Enter Annual Income"
  Title: "Enter Annual Income"
  MaxAmount: 10000000
  IntervalAmount: 10000
AnnualSalaryUIViewAED:
  Title: "Annual Income"
  AnnualSalaryOptions:
    - DisplayString: "Less than 4,000 AED (Below ₹1L)"
      MinValue: 0
      MaxValue: 100000
      CurrencyCode: "INR"
      Alternate:
        MinValue: 0
        MaxValue: 4000
        CurrencyCode: "AED"
    - DisplayString: "4,000 - 22,000 AED (₹1L-₹5L)"
      MinValue: 100000
      MaxValue: 500000
      CurrencyCode: "INR"
      Alternate:
        MinValue: 4000
        MaxValue: 22000
        CurrencyCode: "AED"
    - DisplayString: "22,000 - 44,000 AED (₹5L-₹10L)"
      MinValue: 500000
      MaxValue: 1000000
      CurrencyCode: "INR"
      Alternate:
        MinValue: 22000
        MaxValue: 44000
        CurrencyCode: "AED"
    - DisplayString: "44,000 - 110,000 AED (₹10L-₹25L)"
      MinValue: 1000000
      MaxValue: 2500000
      CurrencyCode: "INR"
      Alternate:
        MinValue: 44000
        MaxValue: 110000
        CurrencyCode: "AED"
    - DisplayString: "110,000 - 440,000 AED (₹25L-₹1Cr)"
      MinValue: 2500000
      MaxValue: 10000000
      CurrencyCode: "INR"
      Alternate:
        MinValue: 110000
        MaxValue: 440000
        CurrencyCode: "AED"
    - DisplayString: "440,000 + AED (Above ₹1Cr)"
      MinValue: 10000000
      MaxValue: ********
      CurrencyCode: "INR"
      Alternate:
        MinValue: 440000
        MaxValue: 1100000
        CurrencyCode: "AED"
IncomeDiscrepancyView:
  WarningText: "<font color=#A73F4B>The above details are not in usual range. Please update income or check the box below </font>"
  ConsentText: "<font color=#646464>These details are correct already. I’ll send <b>Proof of Income</b> to <EMAIL> from my Fi-registered email within 30 days. Accepted proofs include ITR, 6 mo Payslip, 6 mo Bank Statement, or Form 16</font>"

OccupationTypeUiView:
  HeaderDisplayString: "Occupation"
  DefaultType: "OCCUPATION_TYPE_ENGINEERING"
  Infos:
    - BeIdentifier: "OCCUPATION_TYPE_LEGAL_AND_JUDICIARY"
      IsEnabled:
        IsEnableOnAndroid: false
        MinAndroidVersion: 2000
        IsEnableOnIos: false
        MinIosVersion: 2000
    - BeIdentifier: "OCCUPATION_TYPE_SOFTWARE_AND_IT"
      IsEnabled:
        IsEnableOnAndroid: false
        MinAndroidVersion: 2000
        IsEnableOnIos: false
        MinIosVersion: 2000
    - BeIdentifier: "OCCUPATION_TYPE_ENGINEERING"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_HEALTHCARE"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_ACADEMIA"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_BANKING"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_CHARTERED_ACCOUNTANT"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_PUBLIC_SERVICES"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_MERCHANT_AND_TRADE"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_NEWS_AND_MEDIA"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_BUSINESS"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_AVIATION"
      IsEnabled:
        IsEnableOnAndroid: false
        MinAndroidVersion: 2000
        IsEnableOnIos: false
        MinIosVersion: 2000
    - BeIdentifier: "OCCUPATION_TYPE_REAL_ESTATE_AND_INFRASTRUCTURE"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_DEFENCE_AND_LAW_ENFORCEMENT"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_MARKETING_AND_SALES"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_OTHERS"
      IsEnabled:
        IsEnableOnAndroid: false
        MinAndroidVersion: 2000
        IsEnableOnIos: false
        MinIosVersion: 2000
    - BeIdentifier: "OCCUPATION_TYPE_ENTERTAINMENT"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_CRYPTO_TRADING"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_LUXURY_CAR_DEALER"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_SCRAP_DEALER"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_STUDENT"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_HOMEMAKER"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_RETIRED"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_JUDGE"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_ADVOCATE"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_FUND_MANAGEMENT"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_DIPLOMAT"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
    - BeIdentifier: "OCCUPATION_TYPE_AGRICULTURE"
      IsEnabled:
        IsEnableOnAndroid: true
        MinAndroidVersion: 0
        IsEnableOnIos: true
        MinIosVersion: 0
ESHostUrl: "http://localhost:9200/"

EmploymentDetailsUpdateConfig:
  IsEnabled: true
  PermissibleUpdateInterval: "4380h" # 6 months

EpifiDb:
  DbType: "CRDB"
  AppName: "user"
  StatementTimeout: 5m
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi_test"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: false
    DBResolverList:
      - Alias: "user_properties_pgdb"
        DbDsn:
          DbType: "PGDB"
          AppName: "user"
          Host: "localhost"
          Port: 5432
          Username: "root"
          Password: ""
          Name: "user_properties_test"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "{\"username\": \"root\", \"password\": \"\"}"

EnableSaNewFields:
  MinAndroidVersion: 1
  MinIOSVersion: 1
  FallbackToEnableFeature: false
  DisableFeature: true
