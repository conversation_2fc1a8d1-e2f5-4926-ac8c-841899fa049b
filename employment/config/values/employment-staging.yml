Application:
  Environment: "staging"
  Name: "employment"

EmploymentRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 12
    PoolSize: 20 # this is set to twice the size of default connection pool size
    MinIdleConns: 10 # this allows min. number of conn to be readily available
  HystrixCommand:
    CommandName: "user_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 500ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

EmploymentRedisOptionsV2:
  Addrs:
    - "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
  RedisDB: 12
  Hystrix:
    CommandName: "user_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80
  EnableSecureRedis: true

EnableHomeUpdateEmploymentNoticeBar: true
CompanyMappingFilePath: "stubs/user_company_mappings.csv"
NameMatchThresholdScore: 2
URLValidationRegex: (?:http(s)?://)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\(\)\*\+,;=.]+
EmailValidationRegex: (?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|\"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*\")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])
DomainNameRegex: ^(?:https?:\/\/)?(?:[^@\/\n]+@)?(?:www\.)?([^:\/\n]+)
AllowedDomainsForPersonalProfile:
  - "linkedin"
  - "behance"
  - "dribbble"
  - "fiverr"
  - "toptal"
  - "freelancer"
  - "upwork"
EmploymentVerificationProcessMaxDuration: 60s

EmploymentVerificationPublisher:
  QueueName: "staging-employment-verification-queue"

EmploymentVerificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-employment-verification-queue"
  RetryStrategy: #retrying for ~85 mins
    ExponentialBackOff:
      BaseInterval: 5
      MaxAttempts: 10
      TimeUnit: "Second"

LinkedinVerificationPublisher:
  QueueName: "staging-linkedin-verification-queue"

IncomeUpdatePublisher:
  TopicName: "staging-income-update-event-topic"

LinkedinVerificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-linkedin-verification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 3
      TimeUnit: "Second"

UpdateEmploymentPublisher:
  TopicName: "staging-employment-update-topic"

EmployerPiMappingUpdateEventSqsPublisher:
  QueueName: "staging-employer-pi-mapping-update-event-queue"

EnableSaNewFields:
  MinAndroidVersion: 1
  MinIOSVersion: 1
  FallbackToEnableFeature: false
  DisableFeature: true
