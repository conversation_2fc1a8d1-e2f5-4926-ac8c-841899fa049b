Application:
  Environment: "prod"
  Name: "employment"

EmploymentRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.prod-user-service-redis.iqb2bw.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 3
  ClientName: employment
  HystrixCommand:
    CommandName: "user_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 2500
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 30

EmploymentRedisOptionsV2:
  Addrs:
    - "master.prod-user-service-redis.iqb2bw.aps1.cache.amazonaws.com:6379"
  RedisDB: 3
  HystrixCommand:
    CommandName: "user_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 2500
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 30
  EnableSecureRedis: true

EmploymentDataCacheConfig:
  IsCacheEnabled: true
  CacheTTL: "15m"

EnableHomeUpdateEmploymentNoticeBar: true
AllowUserIfCompanyNotEpfoRegistered: false
AllowNonSalariedUsersByDomainName: false
CompanyMappingFilePath: "stubs/user_company_mappings.csv"
NameMatchThresholdScore: 2
URLValidationRegex: (?:http(s)?://)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\(\)\*\+,;=.]+
EmailValidationRegex: (?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|\"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*\")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])
DomainNameRegex: ^(?:https?:\/\/)?(?:[^@\/\n]+@)?(?:www\.)?([^:\/\n]+)
AllowedDomainsForPersonalProfile:
  - "linkedin"
  - "behance"
  - "dribbble"
  - "fiverr"
  - "toptal"
  - "freelancer"
  - "upwork"
EmploymentVerificationProcessMaxDuration: 60s

EmploymentVerificationPublisher:
  QueueName: "prod-employment-verification-queue"

EmploymentVerificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-employment-verification-queue"
  RetryStrategy: #retrying for ~85 mins
    ExponentialBackOff:
      BaseInterval: 5
      MaxAttempts: 10
      TimeUnit: "Second"

LinkedinVerificationPublisher:
  QueueName: "prod-linkedin-verification-queue"

IncomeUpdatePublisher:
  TopicName: "prod-income-update-event-topic"

EmployerPiMappingUpdateEventSqsPublisher:
  QueueName: "prod-employer-pi-mapping-update-event-queue"

LinkedinVerificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-linkedin-verification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 3
      TimeUnit: "Second"

UpdateEmploymentPublisher:
  TopicName: "prod-employment-update-topic"
AnnualSalaryUIView:
  Title: "Annual Income"
  AnnualSalaryOptions:
    - DisplayString: "₹0-1 Lakhs"
      MinValue: 0
      MaxValue: 100000
      CurrencyCode: INR
    - DisplayString: "₹1-5 Lakhs"
      MinValue: 100000
      MaxValue: 500000
      CurrencyCode: INR
    - DisplayString: "₹5-10 Lakhs"
      MinValue: 500000
      MaxValue: 1000000
      CurrencyCode: INR
    - DisplayString: "₹10-25 Lakhs"
      MinValue: 1000000
      MaxValue: 2500000
      CurrencyCode: INR
    - DisplayString: "₹25-50 Lakhs"
      MinValue: 2500000
      MaxValue: 5000000
      CurrencyCode: INR
    - DisplayString: "₹50 Lakhs -1 Crore"
      MinValue: 5000000
      MaxValue: ********
      CurrencyCode: INR
    - DisplayString: "Above ₹1 Crore"
      MinValue: ********
      MaxValue: 25000000
      CurrencyCode: INR
EmploymentProofUiView:
  Infos:
    - Type: "COMPANY_NAME"
      DisplayString: "Company Name"
      ToolTip: "Please enter full company name, without abbreviations 🙏"
      Manadatory: true
    - Type: "PERSONAL_PROFILE"
      DisplayString: "URL showing your work details"
      ToolTip: "Ex: Linkedin, Behance, Dribbble, Fiverr, Toptal, Freelancer, Upwork"
      Manadatory: true
    - Type: "GSTIN_NUMBER"
      DisplayString: "Enter GSTIN Number (optional)"
      ToolTip: ""
      Manadatory: false
    - Type: "ENROLLMENT_NUMBER"
      DisplayString: "Enter Enrollment Number (optional)"
      ToolTip: ""
      Manadatory: false
    - Type: "STUDENT_EMAIL_ID"
      DisplayString: "Enter Student Email ID (optional)"
      ToolTip: ""
      Manadatory: false
AnnualSalaryUIView2:
  Hint: "Enter Annual Income"
  Title: "Enter Annual Income"
  MaxAmount: ********
  IntervalAmount: 10000
IncomeDiscrepancyView:
  WarningText: "<font color=#A73F4B>The above details are not in usual range. Please update income or check the box below </font>"
  ConsentText: "<font color=#646464>These details are correct already. I’ll send <b>Proof of Income</b> to <EMAIL> from my Fi-registered email within 30 days. Accepted proofs include ITR, 6 mo Payslip, 6 mo Bank Statement, or Form 16</font>"

CollectOccupation:
  MinAndroidVersion: 240
  MinIOSVersion: 335
  FallbackToEnableFeature: false
  DisableFeature: false
ESHostUrl: "https://vpc-prod-search-access-bans26ocf6pstioabfospdyrxi.ap-south-1.es.amazonaws.com"

EnableRueidisRedisClient: true

EmploymentDetailsUpdateConfig:
  IsEnabled: true
  PermissibleUpdateInterval: "24h" # 6 months

EmployerDataCacheConfig:
  IsCachingEnabled: false
  Prefix: "EMPLOYMENT:EMPLOYERID:"
  CacheTTl: "24h"

EnableSaNewFields:
  MinAndroidVersion: 100000
  MinIOSVersion: 100000
  FallbackToEnableFeature: false
  DisableFeature: true
