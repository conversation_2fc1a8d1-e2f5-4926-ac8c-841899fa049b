Application:
  Environment: "uat"
  Name: "employment"

EmploymentRedisOptions:
  ClientName: employment
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 12
  HystrixCommand:
    CommandName: "user_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 100ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

EmploymentRedisOptionsV2:
  Addrs:
    - "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
  RedisDB: 12
  Hystrix:
    CommandName: "user_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 100ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80
  EnableSecureRedis: true

EnableHomeUpdateEmploymentNoticeBar: true
CompanyMappingFilePath: "stubs/user_company_mappings.csv"
NameMatchThresholdScore: 2
URLValidationRegex: (?:http(s)?://)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\(\)\*\+,;=.]+
EmailValidationRegex: (?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|\"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*\")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])
DomainNameRegex: ^(?:https?:\/\/)?(?:[^@\/\n]+@)?(?:www\.)?([^:\/\n]+)
AllowedDomainsForPersonalProfile:
  - "linkedin"
  - "behance"
  - "dribbble"
  - "fiverr"
  - "toptal"
  - "freelancer"
  - "upwork"
EmploymentVerificationProcessMaxDuration: 60s

EmploymentVerificationPublisher:
  QueueName: "uat-employment-verification-queue"

EmploymentVerificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-employment-verification-queue"
  RetryStrategy: #retrying for ~85 mins
    ExponentialBackOff:
      BaseInterval: 5
      MaxAttempts: 10
      TimeUnit: "Second"

LinkedinVerificationPublisher:
  QueueName: "uat-linkedin-verification-queue"

IncomeUpdatePublisher:
  TopicName: "uat-income-update-event-topic"

LinkedinVerificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-linkedin-verification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 3
      TimeUnit: "Second"

UpdateEmploymentPublisher:
  TopicName: "uat-employment-update-topic"

EmployerPiMappingUpdateEventSqsPublisher:
  QueueName: "uat-employer-pi-mapping-update-event-queue"

EnableSaNewFields:
  MinAndroidVersion: 1
  MinIOSVersion: 1
  FallbackToEnableFeature: false
  DisableFeature: true
