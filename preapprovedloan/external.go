package preapprovedloan

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/pagination"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	types2 "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/owner"
	"github.com/epifi/gamma/preapprovedloan/helper"
	ldProviders "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers"
	multiDbProvider "github.com/epifi/gamma/preapprovedloan/multidb_provider"
	recommendationProvider "github.com/epifi/gamma/preapprovedloan/recommendation_engine/provider"
)

const (
	maxPageSize = 100
)

func (s *Service) GetLoanAccountDetails(ctx context.Context, req *preApprovedLoanPb.GetLoanAccountDetailsRequest) (*preApprovedLoanPb.GetLoanAccountDetailsResponse, error) {
	res := &preApprovedLoanPb.GetLoanAccountDetailsResponse{}
	var las []*preApprovedLoanPb.LoanAccount
	var lds []*preApprovedLoanPb.LoanDetail
	var err error

	if req.GetLoanAccountIds() != nil && len(req.GetLoanAccountIds()) > 0 {
		las, err = s.loanAccountsDao.GetByIds(ctx, req.GetLoanAccountIds())
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error while fetching loan accounts from db by ids", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	} else {
		// vendor is sent as unspecified so it calls the multiDB dao which will check by actor id in all the DBs
		// newCtx with ownership of epifi_tech helps in multidb to search for active loan accounts across every vendor instead of just one
		newCtx := epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_TECH)
		las, err = s.loanAccountsDao.GetActiveLoanAccountsByActorIdAndVendor(newCtx, req.GetActorId(), preApprovedLoanPb.Vendor_VENDOR_UNSPECIFIED)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error while fetching loan accounts from db by actor_id and vendor", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	for _, la := range las {
		loanInstallmentInfo, liiErr := s.loanInstallmentInfoDao.GetByActiveAccountId(ctx, la.GetId())
		if liiErr != nil && !errors.Is(liiErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error while fetching loan installment info from db", zap.Error(liiErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		loanActivities, laErr := s.loanActivityDao.GetByAccountIdAndTypesAndCount(ctx, la.GetId(), []preApprovedLoanPb.LoanActivityType{
			preApprovedLoanPb.LoanActivityType_LOAN_ACTIVITY_TYPE_EMI,
		}, 1)
		if laErr != nil && !errors.Is(laErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error while fetching loan offer from db", zap.Error(laErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		bankAccountDetails, badErr := s.rpcHelper.GetMandateBankAccountDetails(ctx, la)
		if badErr != nil {
			logger.Error(ctx, "error while fetching bank account details", zap.Error(err))
		}
		if loanActivities != nil {
			lds = append(lds, fillLoanDetailByLoanAccount(la, loanInstallmentInfo, loanActivities[0], bankAccountDetails))
		} else {
			lds = append(lds, fillLoanDetailByLoanAccount(la, loanInstallmentInfo, nil, bankAccountDetails))
		}
	}

	res.LoanDetails = lds
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func fillLoanDetailByLoanAccount(account *preApprovedLoanPb.LoanAccount, lii *preApprovedLoanPb.LoanInstallmentInfo, la *preApprovedLoanPb.LoanActivity, bankAccountDetails *types2.BankAccountDetails) *preApprovedLoanPb.LoanDetail {
	return &preApprovedLoanPb.LoanDetail{
		Id:            account.GetId(),
		Status:        account.GetStatus(),
		Name:          account.GetDetails().GetLoanName(),
		VendorDetails: getVendorDetails(account.GetVendor()),
		AmountInfo: &preApprovedLoanPb.AmountInfo{
			LoanAmount:         account.GetLoanAmountInfo().GetLoanAmount(),
			DisbursedAmount:    account.GetLoanAmountInfo().GetDisbursedAmount(),
			OutstandingAmount:  account.GetLoanAmountInfo().GetOutstandingAmount(),
			TotalPayableAmount: account.GetLoanAmountInfo().GetTotalPayableAmount(),
		},
		CreationTime: account.GetCreatedAt(),
		ClosureTime:  datetime.DateToTimestamp(lii.GetEndDate(), datetime.IST),
		InstallmentDetail: &preApprovedLoanPb.InstallmentDetail{
			NextEmiAmount:           lii.GetDetails().GetNextEmiAmount(),
			PrevPaidInstallmentTime: la.GetCreatedAt(),
			NextDueInstallmentTime:  datetime.DateToTimestamp(lii.GetNextInstallmentDate(), datetime.IST),
		},
		BankAccountDetails: bankAccountDetails,
	}
}

func getVendorDetails(vendor preApprovedLoanPb.Vendor) *preApprovedLoanPb.VendorDetail {
	res := &preApprovedLoanPb.VendorDetail{
		Name: vendor,
	}
	switch vendor {
	case preApprovedLoanPb.Vendor_FEDERAL:
		res.Icon = "https://epifi-icons.pointz.in/preapprovedloan/external_federal_bank_icon.png"
	default:
		return res
	}
	return res
}

func (s *Service) GetLoanOffers(ctx context.Context, req *preApprovedLoanPb.GetLoanOffersRequest) (*preApprovedLoanPb.GetLoanOffersResponse, error) {
	res := &preApprovedLoanPb.GetLoanOffersResponse{}
	var (
		lo    *preApprovedLoanPb.LoanOffer
		loErr error
	)
	if req.GetLoanHeader().GetLoanProgram() != preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		lo, loErr = s.loanOffersDao.GetLatestByActorIdVendorAndLoanProgram(ctx, req.GetActorId(), req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	} else {
		// vendor is sent as unspecified so it calls the multiDB dao which will check by actor id in all the DBs
		lo, loErr = s.loanOffersDao.GetLatestByActorIdAndVendor(ctx, req.GetActorId(), preApprovedLoanPb.Vendor_VENDOR_UNSPECIFIED)
	}
	if loErr != nil && !errors.Is(loErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching loan offer from db by actor_id and vendor", zap.Error(loErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if lo != nil {
		loClone, ok := proto.Clone(lo).(*preApprovedLoanPb.LoanOffer)
		if !ok {
			logger.Error(ctx, "unable to clone loan offer")
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res.LoanOffers = append(res.GetLoanOffers(), fillOfferByLoanOffer(lo))
		res.StoredLoanOffers = append(res.GetStoredLoanOffers(), loClone)
	}
	if req.GetGetBestOffer() {
		reRes, reErr := s.recommendationEngine.GetCombinedRankedLoanOptions(ctx, &recommendationProvider.GetCombinedRankedLoanOptionsRequest{
			ActorId: req.GetActorId(),
		})
		if reErr != nil {
			logger.Error(ctx, "error while fetching best loan offer", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(reErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		} else {
			for _, opt := range reRes.LoanOptions {
				if opt.GetLoanOffer() != nil {
					res.BestOffer = opt.GetLoanOffer()
					break
				}
			}
		}
	}
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func fillOfferByLoanOffer(lo *preApprovedLoanPb.LoanOffer) *preApprovedLoanPb.Offer {
	lc := helper.NewLoanCalculator(lo)
	return &preApprovedLoanPb.Offer{
		Constraints: &preApprovedLoanPb.Offer_Constraints{
			MinTenureMonths: lc.GetMinOfferTenureInMonths(),
			MaxTenureMonths: lo.GetOfferConstraints().GetMaxTenureMonths(),
			MinLoanAmount:   lc.GetMinLoanAmount(),
			MaxLoanAmount:   lo.GetOfferConstraints().GetMaxLoanAmount(),
			MaxEmiAmount:    lo.GetOfferConstraints().GetMaxEmiAmount(),
		},
		ProcessingInfo: &preApprovedLoanPb.Offer_ProcessingInfo{
			GstPercentage:           lo.GetProcessingInfo().GetGst(),
			InterestRatePercentage:  lc.GetInterestRate(),
			ProcessingFeePercentage: lc.GetProcessingFeeRate(),
		},
		VendorDetail: getVendorDetails(lo.GetVendor()),
		ValidTill:    lo.GetValidTill(),
		IsActive:     lo.IsActiveNow(),
	}
}

func (s *Service) GetRecentLoanApplicationDetails(ctx context.Context, req *preApprovedLoanPb.GetRecentLoanApplicationDetailsRequest) (*preApprovedLoanPb.GetRecentLoanApplicationDetailsResponse, error) {
	res := &preApprovedLoanPb.GetRecentLoanApplicationDetailsResponse{}

	// vendor is sent as unspecified so it calls the multiDB dao which will check by actor id in all the DBs
	loanRequests, err := s.loanRequestsDao.GetByActorIdAndVendorAndStatus(ctx, req.GetActorId(), preApprovedLoanPb.Vendor_VENDOR_UNSPECIFIED, []preApprovedLoanPb.LoanRequestStatus{})
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in loanRequestsDao.GetByActorIdAndVendorAndStatus")
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if len(loanRequests) > 0 {
		res.Applications = append(res.GetApplications(), fillApplicationByLoanRequest(loanRequests[0]))
	}
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) EstimateCreditUtilised(ctx context.Context, req *preApprovedLoanPb.EstimateCreditUtilisedRequest) (*preApprovedLoanPb.EstimateCreditUtilisedResponse, error) {
	res := &preApprovedLoanPb.EstimateCreditUtilisedResponse{}

	// Fetch recent loan application requests
	palRes, rpcErr := s.GetRecentLoanApplicationDetails(ctx, &preApprovedLoanPb.GetRecentLoanApplicationDetailsRequest{ActorId: req.GetActorId()})
	if err := epifigrpc.RPCError(palRes, rpcErr); err != nil {
		// Throw error if it is not record not found
		if !palRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error fetching loan application requests", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	loanReqs, err := s.loanRequestsDao.GetByActorIdAndVendorAndStatus(ctx, req.GetActorId(), req.GetVendor(), nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no loan requests found", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		res.CreditUtilised = money.ZeroINR().GetPb()
		res.Status = rpcPb.StatusOk()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error fetching loan request", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanRequest := loanReqs[0]

	lrs, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, loanRequest.GetId(), preApprovedLoanPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, preApprovedLoanPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_CHECK)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "liveness not completed", zap.String(logger.ID, loanRequest.GetId()))
		res.Status = rpcPb.StatusOk()
		res.CreditUtilised = money.ZeroINR().GetPb()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error fetching loan step", zap.String(logger.ID, loanRequest.GetId()), zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// if liveness step is not success, return 0 amount
	if lrs.GetStatus() != preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS {
		res.CreditUtilised = money.ZeroINR().GetPb()
		res.Status = rpcPb.StatusOk()
		return res, nil
	}

	res.Status = rpcPb.StatusOk()
	res.CreditUtilised = loanRequest.GetDetails().GetLoanInfo().GetAmount()
	return res, nil
}

func fillApplicationByLoanRequest(lr *preApprovedLoanPb.LoanRequest) *preApprovedLoanPb.Application {
	status, substatus := getApplicationStatusSubstatusByLoanRequestSubStatus(lr.GetSubStatus())
	return &preApprovedLoanPb.Application{
		VendorDetail: getVendorDetails(lr.GetVendor()),
		LoanInfo: &preApprovedLoanPb.LoanApplicationInfo{
			Amount:          lr.GetDetails().GetLoanInfo().GetAmount(),
			TenureInMonths:  lr.GetDetails().GetLoanInfo().GetTenureInMonths(),
			DisbursalAmount: lr.GetDetails().GetLoanInfo().GetDisbursalAmount(),
			InterestRate:    lr.GetDetails().GetLoanInfo().GetInterestRate(),
			EmiAmount:       lr.GetDetails().GetLoanInfo().GetEmiAmount(),
			Deductions: &preApprovedLoanPb.LoanApplicationInfo_Deductions{
				TotalDeductions: lr.GetDetails().GetLoanInfo().GetDeductions().GetTotalDeductions(),
				Gst:             lr.GetDetails().GetLoanInfo().GetDeductions().GetGst(),
				ProcessingFee:   lr.GetDetails().GetLoanInfo().GetDeductions().GetProcessingFee(),
				AdvanceInterest: lr.GetDetails().GetLoanInfo().GetDeductions().GetAdvanceInterest(),
			},
			TotalPayable: lr.GetDetails().GetLoanInfo().GetTotalPayable(),
		},
		CompletedAt: lr.GetCompletedAt(),
		CreatedAt:   lr.GetCreatedAt(),
		Status:      status,
		SubStatus:   substatus,
	}
}

// nolint: funlen
func getApplicationStatusSubstatusByLoanRequestSubStatus(lrSubstatus preApprovedLoanPb.LoanRequestSubStatus) (preApprovedLoanPb.LoanApplicationStatus, preApprovedLoanPb.LoanApplicationSubStatus) {
	var status preApprovedLoanPb.LoanApplicationStatus
	var substatus preApprovedLoanPb.LoanApplicationSubStatus
	switch lrSubstatus {
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CREATED:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_CREATED
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_UNSPECIFIED
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_VKYC,
		preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_VKYC,
		preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_VKYC_IN_PROGRESS:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_IN_PROGRESS
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_VKYC
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS,
		preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS_AFTER_VKYC,
		preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_LIVENESS,
		preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS_IN_PROGRESS:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_IN_PROGRESS
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_LIVENESS
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_FACE_MATCH,
		preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_FACE_MATCH_IN_PROGRESS,
		preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_FACE_MATCH:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_IN_PROGRESS
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_FACEMATCH
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_MANUAL_REVIEW,
		preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_MANUAL_REVIEW,
		preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_MANUAL_REVIEW_IN_PROGRESS:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_IN_PROGRESS
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_MANUAL_REVIEW
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_KFS_AT_BANK,
		preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_KFS,
		preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_KFS_IN_PROGRESS,
		preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_KFS:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_IN_PROGRESS
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_ESIGN
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_PROFILE_VALIDATION,
		preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_PROFILE_VALIDATION:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_IN_PROGRESS
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_PROFILE_VALIDATION
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_VKYC:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_FAILED
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_VKYC
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_LIVENESS:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_FAILED
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_LIVENESS
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_FACE_MATCH:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_FAILED
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_FACEMATCH
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_MANUAL_REVIEW:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_FAILED
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_MANUAL_REVIEW
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_KFS:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_FAILED
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_ESIGN
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_PROFILE_VALIDATION:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_FAILED
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_PROFILE_VALIDATION
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_INITIATED_IN_PROGRESS,
		preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_INITIATED_AT_VENDOR:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_IN_PROGRESS
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_APPLICATION_SUBMITTED
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_SUCCESS:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_ACCOUNT_CREATED
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_UNSPECIFIED
	case preApprovedLoanPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CANCELLED:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_CANCELLED
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_UNSPECIFIED
	default:
		status = preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_UNSPECIFIED
		substatus = preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_UNSPECIFIED
	}
	return status, substatus
}

func (s *Service) GetLoanTransactions(ctx context.Context, req *preApprovedLoanPb.GetLoanTransactionsRequest) (*preApprovedLoanPb.GetLoanTransactionsResponse, error) {
	res := &preApprovedLoanPb.GetLoanTransactionsResponse{}

	pageSize := req.GetPageContext().GetPageSize()
	maxPageSize := uint32(maxPageSize)
	if pageSize > maxPageSize {
		pageSize = maxPageSize
	}

	pageToken, pageTokenErr := pagination.GetPageToken(req.GetPageContext())
	if pageTokenErr != nil {
		logger.Error(ctx, "Unable to fetch pageToken from request", zap.Error(pageTokenErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	var las []*preApprovedLoanPb.LoanAccount
	var err error
	if len(req.GetLoanAccountIds()) > 0 {
		las, err = s.loanAccountsDao.GetByIds(ctx, req.GetLoanAccountIds())
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error while fetching loan accounts from db by ids", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	} else {
		// vendor is sent as unspecified so it calls the multiDB dao which will check by actor id in all the DBs
		las, err = s.loanAccountsDao.GetByActorIdAndVendor(ctx, req.GetActorId(), preApprovedLoanPb.Vendor_VENDOR_UNSPECIFIED)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error while fetching loan accounts from db by actor_id and vendor", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}
	var laIds []string
	laIdToLaMap := make(map[string]*preApprovedLoanPb.LoanAccount)
	for _, la := range las {
		laIds = append(laIds, la.GetId())
		laIdToLaMap[la.GetId()] = la
	}
	lActs, pageContext, lActsErr := s.loanActivityDao.GetByAccountIdsPaginated(ctx, laIds, pageToken, pageSize)
	if lActsErr != nil && !errors.Is(lActsErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching loan activities from db by account_ids paginated", zap.Error(lActsErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	for _, lAct := range lActs {
		res.Transactions = append(res.GetTransactions(), fillTransactionFromLoanActivity(laIdToLaMap[lAct.GetLoanAccountId()], lAct))
	}
	res.Status = rpcPb.StatusOk()
	res.PageContext = pageContext
	return res, nil
}

func fillTransactionFromLoanActivity(la *preApprovedLoanPb.LoanAccount, lAct *preApprovedLoanPb.LoanActivity) *preApprovedLoanPb.Transaction {
	return &preApprovedLoanPb.Transaction{
		LoanName:      la.GetDetails().GetLoanName(),
		LoanAccountId: lAct.GetLoanAccountId(),
		TxnTime:       lAct.GetCreatedAt(),
		TxnType:       lAct.GetType(),
		// TODO(@prasoon): Remove hard coding
		TxnStatus:      preApprovedLoanPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_SUCCESS,
		Amount:         lAct.GetDetails().GetAmount(),
		VendorDetails:  getVendorDetails(la.GetVendor()),
		Utr:            lAct.GetReferenceId(),
		LoanActivityId: lAct.GetId(),
	}
}

// getOptimisticLoanAmountAvailed returns loan amount availed by actor including active loan requests where user has completed all their necessary actions
func (s *Service) getOptimisticLoanAmountAvailed(loanApplication *preApprovedLoanPb.Application) *moneyPb.Money {
	// Return zero value instead of nil for subtraction purpose
	zeroVal := money.ZeroINR().GetPb()
	switch loanApplication.GetStatus() {
	case preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_ACCOUNT_CREATED:
		return loanApplication.GetLoanInfo().GetAmount()
	case preApprovedLoanPb.LoanApplicationStatus_LOAN_APPLICATION_STATUS_IN_PROGRESS:
		switch loanApplication.GetSubStatus() {
		// TODO(akk) - check if these are sub statuses are extensive for the use case
		case preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_APPLICATION_SUBMITTED,
			preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_ESIGN,
			preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_DISBURSED,
			preApprovedLoanPb.LoanApplicationSubStatus_LOAN_APPLICATION_SUB_STATUS_PROFILE_VALIDATION:
			return loanApplication.GetLoanInfo().GetAmount()
		default:
			return zeroVal
		}
	default:
		return zeroVal
	}
}

func getTimeRemainingString(startTime time.Time, endTime time.Time) string {
	hrs := int(startTime.Sub(endTime).Hours())
	if hrs < 24 {
		msg := "hour"
		if hrs > 1 {
			msg += "s"
		} else if hrs == 0 {
			return fmt.Sprintf("%d minute", int(startTime.Sub(endTime).Minutes()))
		}
		return fmt.Sprintf("%d %s", hrs, msg)
	}
	days := hrs / 24
	msg := "day"
	if days > 1 {
		msg += "s"
	}
	return fmt.Sprintf("%d %s", days, msg)
}

// nolint: funlen
func (s *Service) GetEarlySalaryDetails(ctx context.Context, req *preApprovedLoanPb.GetEarlySalaryDetailsRequest) (*preApprovedLoanPb.GetEarlySalaryDetailsResponse, error) {

	return s.GetEarlySalaryDetailsV2(ctx, req)
	res := &preApprovedLoanPb.GetEarlySalaryDetailsResponse{}
	res.Status = rpcPb.StatusOk()
	// As per service definition, it is specifically only used for early salary. If in case, it is not defined in loan header,
	// to ensure its validity, we hard code it to Early Salary
	req.GetLoanHeader().LoanProgram = preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY

	accounts, accountErr := s.loanAccountsDao.GetByActorIdVendorAndLoanProgram(ctx, req.GetActorId(), preApprovedLoanPb.Vendor_VENDOR_UNSPECIFIED, req.GetLoanHeader().GetLoanProgram())
	if accountErr != nil && !errors.Is(accountErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error to get loan account by actorId, vendor and loan program ", zap.Error(accountErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if accountErr == nil && len(accounts) > 0 {
		for _, account := range accounts {
			if account.GetStatus() == preApprovedLoanPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
				// Case when in active loan state
				lii, liiErr := s.loanInstallmentInfoDao.GetByActiveAccountId(ctx, account.GetId())
				if liiErr != nil {
					logger.Error(ctx, "error to get loan installment info by active account id", zap.Error(liiErr))
					res.Status = rpcPb.StatusInternal()
					return res, nil
				}
				res.RepaymentDetails = &preApprovedLoanPb.GetEarlySalaryDetailsResponse_RepaymentDetails{
					DueDate:   lii.GetNextInstallmentDate(),
					DueAmount: account.GetLoanAmountInfo().GetOutstandingAmount(),
				}
				res.SalaryState = preApprovedLoanPb.GetEarlySalaryDetailsResponse_ACTIVE
				res.BannerDetails = &preApprovedLoanPb.GetEarlySalaryDetailsResponse_BannerDetails{}
				return res, nil
			}
		}
	}

	// check for salary program active or not
	isActiveSalary, isActiveSalaryErr := s.rpcHelper.IsFullSalaryProgramActive(ctx, req.GetActorId())
	if isActiveSalaryErr != nil {
		logger.Error(ctx, "failed to get if is active salary user", zap.Error(isActiveSalaryErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if !isActiveSalary {
		res.SalaryState = preApprovedLoanPb.GetEarlySalaryDetailsResponse_INELIGIBLE
		res.BannerDetails = &preApprovedLoanPb.GetEarlySalaryDetailsResponse_BannerDetails{
			BottomMsg: "Not Available",
		}
		return res, nil
	}
	// check for salary count
	salaryDetails, salaryDetailsErr := s.getSalaryDetailsForPastNMonths(ctx, req.GetActorId(), s.conf.EarlySalary.MinSalaryCredits)
	if salaryDetailsErr != nil {
		logger.Error(ctx, "failed to get salary details of user salary user", zap.Error(salaryDetailsErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if len(salaryDetails) < s.conf.EarlySalary.MinSalaryCredits {
		res.SalaryState = preApprovedLoanPb.GetEarlySalaryDetailsResponse_INELIGIBLE
		if len(salaryDetails) == 0 {
			res.BannerDetails = &preApprovedLoanPb.GetEarlySalaryDetailsResponse_BannerDetails{
				BottomMsg: fmt.Sprintf("Unlocks with %d salaries", s.conf.EarlySalary.MinSalaryCredits),
			}
		} else {
			res.BannerDetails = &preApprovedLoanPb.GetEarlySalaryDetailsResponse_BannerDetails{
				BottomMsg: fmt.Sprintf("Unlocks soon"),
			}
		}
		return res, nil
	}

	eligibleTime := datetime.TimestampToTime(salaryDetails[0].GetTxnTimestamp()).In(datetime.IST).AddDate(0, 0, s.conf.EarlySalary.MinDaysPostSalaryCredit)
	if datetime.IsBefore(timestampPb.New(time.Now().In(datetime.IST)),
		timestampPb.New(eligibleTime)) {
		// ineligible due to loan not active till now
		currTime := time.Now().In(datetime.IST)
		res.BannerDetails = &preApprovedLoanPb.GetEarlySalaryDetailsResponse_BannerDetails{
			BottomMsg: fmt.Sprintf("Opens in %s", getTimeRemainingString(eligibleTime, currTime)),
		}
		res.SalaryState = preApprovedLoanPb.GetEarlySalaryDetailsResponse_INELIGIBLE
		return res, nil
	}
	// Get active loanOffer for given actorID, vendor from db
	loanOffer, loErr := s.loanOffersDao.GetActiveOfferByActorIdVendorAndLoanProgram(ctx, req.GetActorId(), preApprovedLoanPb.Vendor_VENDOR_UNSPECIFIED, req.GetLoanHeader().GetLoanProgram())
	if loErr != nil && !errors.Is(loErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error to get loan offer by actorId, vendor and loan program ", zap.Error(accountErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if loErr == nil && loanOffer != nil {
		if loanOffer.IsActiveNow() {
			// case when in eligible for early salary state
			res.BannerDetails = &preApprovedLoanPb.GetEarlySalaryDetailsResponse_BannerDetails{}
			res.SalaryState = preApprovedLoanPb.GetEarlySalaryDetailsResponse_ELIGIBLE
			return res, nil
		}
	}

	// InEligible case
	res.SalaryState = preApprovedLoanPb.GetEarlySalaryDetailsResponse_INELIGIBLE
	res.BannerDetails = &preApprovedLoanPb.GetEarlySalaryDetailsResponse_BannerDetails{
		BottomMsg: "Not Available",
	}
	return res, nil
}

func (s *Service) GetEarlySalaryDetailsV2(ctx context.Context, req *preApprovedLoanPb.GetEarlySalaryDetailsRequest) (*preApprovedLoanPb.GetEarlySalaryDetailsResponse, error) {
	res := &preApprovedLoanPb.GetEarlySalaryDetailsResponse{}
	res.Status = rpcPb.StatusOk()
	// As per service definition, it is specifically only used for early salary. If in case, it is not defined in loan header,
	// to ensure its validity, we hard code it to Early Salary
	req.GetLoanHeader().LoanProgram = preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2

	accounts, accountErr := s.loanAccountsDao.GetByActorIdVendorAndLoanProgram(ctx, req.GetActorId(), preApprovedLoanPb.Vendor_VENDOR_UNSPECIFIED, req.GetLoanHeader().GetLoanProgram())
	if accountErr != nil && !errors.Is(accountErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error to get loan account by actorId, vendor and loan program ", zap.Error(accountErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if accountErr == nil && len(accounts) > 0 {
		for _, account := range accounts {
			if account.GetStatus() == preApprovedLoanPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
				// Case when in active loan state
				res.SalaryState = preApprovedLoanPb.GetEarlySalaryDetailsResponse_ACTIVE
				res.BannerDetails = &preApprovedLoanPb.GetEarlySalaryDetailsResponse_BannerDetails{}
				return res, nil
			}
		}
	}

	recommendationResponse, recommendationErr := s.recommendationEngine.GetCombinedRankedLoanOptions(ctx, &recommendationProvider.GetCombinedRankedLoanOptionsRequest{
		ActorId: req.GetActorId(),
	})
	if recommendationErr != nil {
		logger.Error(ctx, "failed to get CombinedRankedLoanOptions for user", zap.Error(recommendationErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	loanOptions := recommendationResponse.LoanOptions

	for _, priorityLoanOption := range loanOptions {
		if priorityLoanOption.GetLoanHeader().GetLoanProgram() == preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2 {
			res.BannerDetails = &preApprovedLoanPb.GetEarlySalaryDetailsResponse_BannerDetails{}
			res.SalaryState = preApprovedLoanPb.GetEarlySalaryDetailsResponse_ELIGIBLE
			return res, nil
		}
	}

	// InEligible case
	res.SalaryState = preApprovedLoanPb.GetEarlySalaryDetailsResponse_INELIGIBLE
	res.BannerDetails = &preApprovedLoanPb.GetEarlySalaryDetailsResponse_BannerDetails{
		BottomMsg: "Not Available",
	}
	return res, nil
}

// GetLoanUserDetails is external rpc to fetch all data stored for an applicant during a loan journey. Journey could be loan eligibility or loan application.
// according to request type, fetches user's data from Loan DBs and returns that to the client
func (s *Service) GetLoanUserDetails(ctx context.Context, req *preApprovedLoanPb.GetLoanUserDetailsRequest) (*preApprovedLoanPb.GetLoanUserDetailsResponse, error) {
	res := &preApprovedLoanPb.GetLoanUserDetailsResponse{
		Status: rpcPb.StatusOk(),
	}
	if req.GetOwner() != commontypes.Owner_OWNER_UNSPECIFIED {
		ow, err := owner.GetOwnershipFromOwner(req.GetOwner())
		if err != nil {
			logger.Error(ctx, "error in converting owner to ownership", zap.Error(err), zap.String("owner", req.GetOwner().String()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			return &preApprovedLoanPb.GetLoanUserDetailsResponse{Status: rpcPb.StatusInternal()}, nil
		}
		ctx = epificontext.WithOwnership(ctx, ow)
	}

	eligDetailsToFetch := []ldProviders.UserKycDetailType{}
	appDetailsToFetch := []ldProviders.UserKycDetailType{}

	switch req.GetLoanUserDetailsType() {
	case preApprovedLoanPb.LoanUserDetailsType_LOAN_USER_DETAILS_TYPE_KYC:
		eligDetailsToFetch = append(eligDetailsToFetch,
			ldProviders.UserKycDetailTypePan,
			ldProviders.UserKycDetailTypeDob,
		)
		appDetailsToFetch = append(appDetailsToFetch,
			ldProviders.UserKycDetailTypePanName,
			ldProviders.UserKycDetailTypeFathersName,
			ldProviders.UserKycDetailTypeMothersName,
		)
	case preApprovedLoanPb.LoanUserDetailsType_LOAN_USER_DETAILS_TYPE_PERMANENT_ADDRESS:
		appDetailsToFetch = append(appDetailsToFetch,
			ldProviders.UserKycDetailTypePermanentAddress,
		)
	default:
		logger.Error(ctx, "LoanUserDetailsType handling not found", zap.String("LoanUserDetailsType", req.GetLoanUserDetailsType().String()))
		res.Status = rpcPb.StatusInvalidArgument()
	}

	kycDetails, kycDetailsErr := s.getLoanUserKycDetails(ctx, &getLoanUserKycDetailsRequest{
		actorId:            req.GetActorId(),
		eligDetailsToFetch: eligDetailsToFetch,
		appDetailsToFetch:  appDetailsToFetch,
	})
	if kycDetailsErr != nil {
		if errors.Is(epifierrors.ErrRecordNotFound, kycDetailsErr) {
			logger.Error(ctx, "no records found for getLoanUserKycDetails", zap.Error(kycDetailsErr))
			res.Status = rpcPb.StatusRecordNotFound()
			return res, nil
		}
		logger.Error(ctx, "failed to getLoanUserKycDetails", zap.Error(kycDetailsErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	res.LoanUserDetails = &preApprovedLoanPb.GetLoanUserDetailsResponse_KycDetails{
		KycDetails: kycDetails,
	}
	return res, nil
}

type getLoanUserKycDetailsRequest struct {
	actorId            string
	appDetailsToFetch  []ldProviders.UserKycDetailType
	eligDetailsToFetch []ldProviders.UserKycDetailType
}

// nolint: funlen
func (s *Service) getLoanUserKycDetails(ctx context.Context, req *getLoanUserKycDetailsRequest) (*preApprovedLoanPb.LoanUserKycDetails, error) {
	dataRes, dataErr := s.multiDbProvider.CheckAndGetLoanRequestsForActor(ctx, &multiDbProvider.CheckAndGetLoanRequestsForActorRequest{
		ActorId: req.actorId,
		Types: []preApprovedLoanPb.LoanRequestType{
			preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
			preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY,
		},
		Statuses: nil,
		// To fetch from loan requests from all the preapproved loan dbs
		LoanPrograms:      nil,
		CompletedAtWithin: nil,
	})
	if dataErr != nil {
		logger.Error(ctx, "failed to fetch landing page data from multiDB provider", zap.Error(dataErr))
		return nil, errors.Wrap(dataErr, "failed to fetch landing page data from multiDB provider")
	}
	if len(dataRes.Requests) == 0 {
		logger.Error(ctx, "no loan requests found for this actor")
		return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "no loan requests found for this actor")
	}
	res := &preApprovedLoanPb.LoanUserKycDetails{}
	var latestLrApplication, latestLrEligibility *preApprovedLoanPb.LoanRequest
	for _, lr := range dataRes.Requests {
		if latestLrEligibility == nil && lr.GetType() == preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY {
			latestLrEligibility = lr
			continue
		}
		if latestLrApplication == nil && lr.GetType() == preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION {
			latestLrApplication = lr
			continue
		}
		if latestLrEligibility != nil && latestLrApplication != nil {
			break
		}
	}

	if len(req.eligDetailsToFetch) > 0 {
		eligUserDetailsProvider, eligProviderErr := s.loanDataProvider.FetchUserDetailsProvider(ctx, &preApprovedLoanPb.LoanHeader{
			LoanProgram: latestLrEligibility.GetLoanProgram(),
			Vendor:      latestLrEligibility.GetVendor(),
		})
		if eligProviderErr != nil {
			logger.Error(ctx, "failed to fetch user details provider", zap.Error(eligProviderErr))
			return nil, errors.Wrap(eligProviderErr, "failed to fetch user details provider")
		}

		if eligUserDetailsProvider == nil {
			logger.Error(ctx, "no eligibility user details provider found for this loan header")
			return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "no eligibility user details provider found for this loan header")
		}

		userKycData, userKycDataErr := eligUserDetailsProvider.FetchUserKycDetails(ctx, &ldProviders.FetchUserKycDetailsRequest{
			LrId:               latestLrEligibility.GetId(),
			KycDetailsRequired: req.eligDetailsToFetch,
		})
		if userKycDataErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get FetchUserKycDetails, error: %v", userKycDataErr))
		}
		for _, kycDetail := range req.eligDetailsToFetch {
			v := userKycData.KycDetailsMap[kycDetail]
			switch kycDetail {
			case ldProviders.UserKycDetailTypePan:
				res.Pan = v.(string)
			case ldProviders.UserKycDetailTypeDob:
				res.Dob = v.(*date.Date)
			default:
				logger.Error(ctx, "unsupported kyc detail type for eligibility", zap.String("kycDetail", string(kycDetail)))
			}
		}
	}

	if len(req.appDetailsToFetch) > 0 {
		appUserDetailsProvider, appProviderErr := s.loanDataProvider.FetchUserDetailsProvider(ctx, &preApprovedLoanPb.LoanHeader{
			LoanProgram: latestLrApplication.GetLoanProgram(),
			Vendor:      latestLrApplication.GetVendor(),
		})
		if appProviderErr != nil {
			logger.Error(ctx, "failed to fetch user details provider", zap.Error(appProviderErr))
			return nil, errors.Wrap(appProviderErr, "failed to fetch user details provider")
		}
		if appUserDetailsProvider == nil {
			logger.Error(ctx, "no application user details provider found for this loan header")
			return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "no user details provider found for this loan header")
		}

		userKycData, userKycDataErr := appUserDetailsProvider.FetchUserKycDetails(ctx, &ldProviders.FetchUserKycDetailsRequest{
			LrId:               latestLrApplication.GetId(),
			KycDetailsRequired: req.appDetailsToFetch,
		})
		if userKycDataErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get FetchUserKycDetails, error: %v", userKycDataErr))
		}
		for _, kycDetail := range req.appDetailsToFetch {
			v := userKycData.KycDetailsMap[kycDetail]
			switch kycDetail {
			case ldProviders.UserKycDetailTypePanName:
				res.PanName = v.(*commontypes.Name)
			case ldProviders.UserKycDetailTypeFathersName:
				res.FathersName = v.(*commontypes.Name)
			case ldProviders.UserKycDetailTypeMothersName:
				res.MothersName = v.(*commontypes.Name)
			case ldProviders.UserKycDetailTypePermanentAddress:
				res.PermanentAddress = v.(*types2.PostalAddress)
			default:
				logger.Error(ctx, "unsupported kyc detail type for eligibility", zap.String("kycDetail", string(kycDetail)))
			}
		}
	}
	return res, nil
}

func (s *Service) GetLoansUserStatus(ctx context.Context, req *preApprovedLoanPb.GetLoansUserStatusRequest) (*preApprovedLoanPb.GetLoansUserStatusResponse, error) {
	dataExistenceRes := s.dataExistenceManager.GetOrRefreshLoanDataExistenceCache(ctx, req.GetActorId())
	loanRequests, err := s.multiDbProvider.CheckAndGetLoanRequestsForActor(ctx, &multiDbProvider.CheckAndGetLoanRequestsForActorRequest{
		ActorId:            req.GetActorId(),
		Statuses:           append(NonTerminalLrStatuses, preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS),
		LoanPrograms:       []preApprovedLoanPb.LoanProgram{}, // leaving this empty here to fetch for all the loan programs
		Types:              []preApprovedLoanPb.LoanRequestType{preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION},
		OwnershipFilterMap: dataExistenceRes.GetDataExistenceMap(),
	})
	switch {
	case err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "error while getting loan requests for the user", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &preApprovedLoanPb.GetLoansUserStatusResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		// if record not found, user is not an active loans user, return from here
		return &preApprovedLoanPb.GetLoansUserStatusResponse{
			Status:          rpcPb.StatusOk(),
			LoansUserStatus: preApprovedLoanPb.LoansUserStatus_LOANS_USER_STATUS_NOT_AN_ACTIVE_LOANS_USER,
		}, nil
	}

	// loan request found in one of the above statuses, we will not allow user data change/ savings account closure for these users.
	// if all the loan_requests are in success status and corresponding loan accounts are closed, we can allow data change for the user
	for _, lr := range loanRequests.Requests {
		if lr.GetStatus() == preApprovedLoanPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS {
			// fetch the loan account for this successful loan application and check it's status
			loanAccount, err := s.loanAccountsDao.GetById(epificontext.WithOwnership(ctx, helper.GetPalOwnership(lr.GetVendor())), lr.GetLoanAccountId())
			if err != nil {
				logger.Error(ctx, "error while getting loan account for a successful loan application", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, lr.GetLoanAccountId()), zap.String(logger.ACTOR_ID_V2, lr.GetActorId()))
				return &preApprovedLoanPb.GetLoansUserStatusResponse{
					Status: rpcPb.StatusInternal(),
				}, nil
			}

			loanInstallmentInfo, loanInstallmentInfoErr := s.loanInstallmentInfoDao.GetByActiveAccountId(
				epificontext.WithOwnership(ctx, helper.GetPalOwnership(lr.GetVendor())), lr.GetLoanAccountId())
			if loanInstallmentInfoErr != nil && !storage.IsRecordNotFoundError(loanInstallmentInfoErr) {
				logger.Error(ctx, "failed to fetch loan installment from GetByActiveAccountId", zap.Error(loanInstallmentInfoErr))
				return &preApprovedLoanPb.GetLoansUserStatusResponse{
					Status: rpcPb.StatusInternal(),
				}, nil
			}

			isLoanOverDueStatus := false
			if loanInstallmentInfo != nil {
				emiTimelineType, _ := helper.GetEmiTimelineDetails(s.DynConf, loanInstallmentInfo.GetDetails().GetGracePeriod(), loanInstallmentInfo.GetNextInstallmentDate(), lr.GetVendor())
				if emiTimelineType == helper.EmiTimelineType_LATE_DPD || emiTimelineType == helper.EmiTimelineType_EARLY_DPD {
					isLoanOverDueStatus = true
				}
			}

			// if any of the loan account is not closed, no need to check for other accounts, return from here
			if loanAccount.GetStatus() != preApprovedLoanPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED {
				return &preApprovedLoanPb.GetLoansUserStatusResponse{
					Status:          rpcPb.StatusOk(),
					LoansUserStatus: preApprovedLoanPb.LoansUserStatus_LOANS_USER_STATUS_ACTIVE_LOAN_ACCOUNT,
					ActivatedAt:     lr.GetCreatedAt(),
					IsLoanOverdue:   isLoanOverDueStatus,
				}, nil
			}
		} else {
			// loan request is in non-terminal state, set the status to active loan application
			return &preApprovedLoanPb.GetLoansUserStatusResponse{
				Status:          rpcPb.StatusOk(),
				LoansUserStatus: preApprovedLoanPb.LoansUserStatus_LOANS_USER_STATUS_ACTIVE_LOAN_APPLICATION,
				ActivatedAt:     lr.GetCreatedAt(),
			}, nil
		}
	}

	// all loan accounts are in closed status, set the appropriate user status
	return &preApprovedLoanPb.GetLoansUserStatusResponse{
		Status:          rpcPb.StatusOk(),
		LoansUserStatus: preApprovedLoanPb.LoansUserStatus_LOANS_USER_STATUS_CLOSED_LOAN_ACCOUNT,
	}, nil
}

func (s *Service) GetFederalLoanCustomerDetails(ctx context.Context, req *preApprovedLoanPb.GetFederalLoanCustomerDetailsRequest) (*preApprovedLoanPb.GetFederalLoanCustomerDetailsResponse, error) {
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(preApprovedLoanPb.Vendor_FEDERAL))
	loecs, err := s.loecDao.GetActiveLoecsByActorIdLoanProgramsAndStatuses(ctx, req.GetActorId(), []preApprovedLoanPb.LoanProgram{preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB},
		[]preApprovedLoanPb.LoanOfferEligibilityCriteriaStatus{preApprovedLoanPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED},
		0, true)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching active loec by actor_id, loan_programs and statuses", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &preApprovedLoanPb.GetFederalLoanCustomerDetailsResponse{Status: rpcPb.StatusInternal()}, nil
	}
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "no loec found for the actor_id", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &preApprovedLoanPb.GetFederalLoanCustomerDetailsResponse{Status: rpcPb.StatusRecordNotFound()}, nil
	}
	if len(loecs) == 0 {
		logger.Error(ctx, "loecs length is zero", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &preApprovedLoanPb.GetFederalLoanCustomerDetailsResponse{Status: rpcPb.StatusRecordNotFound()}, nil
	}
	latestLoec := loecs[0]

	if latestLoec.GetDataRequirementDetails().GetVendorBreReferenceId() == "" {
		logger.Error(ctx, "vendor bre ref number is empty in loec", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &preApprovedLoanPb.GetFederalLoanCustomerDetailsResponse{Status: rpcPb.StatusFailedPrecondition()}, nil
	}

	return &preApprovedLoanPb.GetFederalLoanCustomerDetailsResponse{
		Status:       rpcPb.StatusOk(),
		BreRefNumber: latestLoec.GetDataRequirementDetails().GetVendorBreReferenceId(),
	}, nil
}
