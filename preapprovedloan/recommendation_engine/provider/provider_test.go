// nolint: errcheck
package provider

import (
	"context"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	savingsMock "github.com/epifi/gamma/api/savings/mocks"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/risk/enums"
	profilePb "github.com/epifi/gamma/api/risk/profile"
	profileMocks "github.com/epifi/gamma/api/risk/profile/mocks"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	onbMock "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/preapprovedloan/config/common"
	"github.com/epifi/gamma/preapprovedloan/config/genconf"
	"github.com/epifi/gamma/preapprovedloan/helper"
	multiDbProvider "github.com/epifi/gamma/preapprovedloan/multidb_provider"
	mockMultiDBProvider "github.com/epifi/gamma/preapprovedloan/multidb_provider/mocks"
	eeFactoryMock "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/mocks"
	eeProviders "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers"
	eeMock "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/mocks"
	priorityProviderFactory "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider"
	ppFactoryMock "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/mocks"
	ppMock "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/providers/mocks"
	"github.com/epifi/gamma/preapprovedloan/test"
)

var (
	dynConf *genconf.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	var teardown func()
	_, dynConf, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

var (
	activeLoanOfferFed = &palPb.LoanOffer{
		Id:            "loan-offer-id-7",
		ActorId:       "actor-1",
		VendorOfferId: "vendor-offer-id-7",
		Vendor:        palPb.Vendor_FEDERAL,
		OfferConstraints: &palPb.OfferConstraints{
			MaxEmiAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        6000,
			},
			MaxLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        216000,
			},
			MaxTenureMonths: 36,
			MinLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        10000,
			},
		},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-2",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		ValidTill:                      timestamppb.New(time.Now().AddDate(0, 0, 1)),
		LoanOfferType:                  palPb.LoanOfferType_LOAN_OFFER_TYPE_PRE_QUALIFIED,
	}
	activeLoanOfferLL = &palPb.LoanOffer{
		Id:            "loan-offer-id-8",
		ActorId:       "actor-1",
		VendorOfferId: "vendor-offer-id-8",
		Vendor:        palPb.Vendor_LIQUILOANS,
		OfferConstraints: &palPb.OfferConstraints{
			MaxEmiAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        6000,
			},
			MaxLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        216000,
			},
			MaxTenureMonths: 36,
			MinLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        10000,
			},
		},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-3",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		ValidTill:                      timestamppb.New(time.Now().AddDate(0, 0, 1)),
		LoanOfferType:                  palPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT,
	}
	activeLoanOfferLdc1 = &palPb.LoanOffer{
		Id:            "loan-offer-id-9",
		ActorId:       "actor-1",
		VendorOfferId: "vendor-offer-id-9",
		Vendor:        palPb.Vendor_LENDEN,
		OfferConstraints: &palPb.OfferConstraints{
			MaxEmiAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        6000,
			},
			MaxLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        216000,
			},
			MaxTenureMonths: 36,
			MinLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        10000,
			},
		},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-4",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		ValidTill:                      timestamppb.New(time.Now().AddDate(0, 0, 1)),
		LoanOfferType:                  palPb.LoanOfferType_LOAN_OFFER_TYPE_HARD,
	}
	activeLoanOfferLdc2 = &palPb.LoanOffer{
		Id:            "loan-offer-id-10",
		ActorId:       "actor-1",
		VendorOfferId: "vendor-offer-id-10",
		Vendor:        palPb.Vendor_LENDEN,
		OfferConstraints: &palPb.OfferConstraints{
			MaxEmiAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        8000,
			},
			MaxLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        316000,
			},
			MaxTenureMonths: 48,
			MinLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        20000,
			},
		},
		ProcessingInfo:                 &palPb.OfferProcessingInfo{},
		LoanOfferEligibilityCriteriaId: "loan-offer-eligibility-criteria-id-3",
		LoanProgram:                    palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		ValidTill:                      timestamppb.New(time.Now().AddDate(0, 0, 1)),
		LoanOfferType:                  palPb.LoanOfferType_LOAN_OFFER_TYPE_HARD,
	}

	federalPreApprovedLh = &palPb.LoanHeader{
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		Vendor:      palPb.Vendor_FEDERAL,
	}
	llPreApprovedLh = &palPb.LoanHeader{
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		Vendor:      palPb.Vendor_LIQUILOANS,
	}
	ldcPreApprovedLh = &palPb.LoanHeader{
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		Vendor:      palPb.Vendor_LENDEN,
	}

	federalLoec = &palPb.LoanOfferEligibilityCriteria{Id: "loec-fed-1", Vendor: palPb.Vendor_FEDERAL, LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN}
	llLoec      = &palPb.LoanOfferEligibilityCriteria{Id: "loec-ll-1", Vendor: palPb.Vendor_LIQUILOANS, LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN}

	activeLoanOfferFederalNtb = &palPb.LoanOffer{
		Id:            "loan-offer-id-fed-ntb",
		ActorId:       "actor-1",
		VendorOfferId: "vendor-offer-id-fed-ntb",
		Vendor:        palPb.Vendor_FEDERAL,
		LoanProgram:   palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB,
		OfferConstraints: &palPb.OfferConstraints{
			MaxLoanAmount: &moneyPb.Money{CurrencyCode: "INR", Units: 100000},
		},
	}
	activeLoanOfferAbflRtd = &palPb.LoanOffer{
		Id:            "loan-offer-id-abfl-rtd",
		ActorId:       "actor-1",
		VendorOfferId: "vendor-offer-id-abfl-rtd",
		Vendor:        palPb.Vendor_ABFL,
		LoanProgram:   palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		OfferConstraints: &palPb.OfferConstraints{
			MaxLoanAmount: &moneyPb.Money{CurrencyCode: "INR", Units: 100000},
		},
	}
	activeLoanOfferSgRtd = &palPb.LoanOffer{
		Id:            "loan-offer-id-sg-rtd",
		ActorId:       "actor-1",
		VendorOfferId: "vendor-offer-id-sg-rtd",
		Vendor:        palPb.Vendor_STOCK_GUARDIAN_LSP,
		LoanProgram:   palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		OfferConstraints: &palPb.OfferConstraints{
			MaxLoanAmount: &moneyPb.Money{CurrencyCode: "INR", Units: 100000},
		},
	}
	activeLoanOfferMvRtd = &palPb.LoanOffer{
		Id:            "loan-offer-id-mv-rtd",
		ActorId:       "actor-1",
		VendorOfferId: "vendor-offer-id-mv-rtd",
		Vendor:        palPb.Vendor_MONEYVIEW,
		LoanProgram:   palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		OfferConstraints: &palPb.OfferConstraints{
			MaxLoanAmount: &moneyPb.Money{CurrencyCode: "INR", Units: 100000},
		},
	}
	activeLoanOfferLendenRtd = &palPb.LoanOffer{
		Id:            "loan-offer-id-lenden-rtd",
		ActorId:       "actor-1",
		VendorOfferId: "vendor-offer-id-lenden-rtd",
		Vendor:        palPb.Vendor_LENDEN,
		LoanProgram:   palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		OfferConstraints: &palPb.OfferConstraints{
			MaxLoanAmount: &moneyPb.Money{CurrencyCode: "INR", Units: 100000},
		},
	}

	federalNtbLh = &palPb.LoanHeader{
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB,
		Vendor:      palPb.Vendor_FEDERAL,
	}
	abflRtdLh = &palPb.LoanHeader{
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		Vendor:      palPb.Vendor_ABFL,
	}
	sgRtdLh = &palPb.LoanHeader{
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		Vendor:      palPb.Vendor_STOCK_GUARDIAN_LSP,
	}
	mvRtdLh = &palPb.LoanHeader{
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		Vendor:      palPb.Vendor_MONEYVIEW,
	}
	lendenRtdLh = &palPb.LoanHeader{
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		Vendor:      palPb.Vendor_LENDEN,
	}
)

func TestLoanOptionRecommender_GetRecommendedLoanOption(t *testing.T) {
	type args struct {
		ctx context.Context
		req *GetRecommendedLoanOptionRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(
			ctrl *gomock.Controller,
			onbClientMock *onbMock.MockOnboardingClient,
			profileClientMock *profileMocks.MockProfileClient,
			multiDbProviderMock *mockMultiDBProvider.MockIMultiDbProvider,
			priorityProviderFactoryMock *ppFactoryMock.MockIPriorityProviderFactory,
			eligibilityEvaluatorFactoryMock *eeFactoryMock.MockEligibilityEvaluatorFactory,
			savingsClientMock *savingsMock.MockSavingsClient,
		)
		want    *GetRecommendedLoanOptionResponse
		wantErr bool
	}{
		{
			name: "return error when onboarding service fails",
			args: args{
				ctx: context.Background(),
				req: &GetRecommendedLoanOptionRequest{ActorId: "actor-1"},
			},
			setupMocks: func(ctrl *gomock.Controller, onbClientMock *onbMock.MockOnboardingClient, profileClientMock *profileMocks.MockProfileClient, multiDbProviderMock *mockMultiDBProvider.MockIMultiDbProvider, priorityProviderFactoryMock *ppFactoryMock.MockIPriorityProviderFactory, eligibilityEvaluatorFactoryMock *eeFactoryMock.MockEligibilityEvaluatorFactory, savingsClientMock *savingsMock.MockSavingsClient) {
				savingsClientMock.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil)
				onbClientMock.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: "actor-1",
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{Status: rpc.StatusInternal()}, nil)
			},
			wantErr: true,
		},
		{
			name: "do not return any offer if user is risky",
			args: args{
				ctx: context.Background(),
				req: &GetRecommendedLoanOptionRequest{ActorId: "actor-1"},
			},
			setupMocks: func(ctrl *gomock.Controller, onbClientMock *onbMock.MockOnboardingClient, profileClientMock *profileMocks.MockProfileClient, multiDbProviderMock *mockMultiDBProvider.MockIMultiDbProvider, priorityProviderFactoryMock *ppFactoryMock.MockIPriorityProviderFactory, eligibilityEvaluatorFactoryMock *eeFactoryMock.MockEligibilityEvaluatorFactory, savingsClientMock *savingsMock.MockSavingsClient) {
				savingsClientMock.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil)
				onbClientMock.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: "actor-1",
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				profileClientMock.EXPECT().GetUserProfile(gomock.Any(), &profilePb.GetUserProfileRequest{
					ActorId: "actor-1",
				}).Return(&profilePb.GetUserProfileResponse{
					Status: rpc.StatusOk(),
					AccountsInfo: []*profilePb.AccountInfo{
						{
							PresentFreezeStatus: enums.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE,
						},
					},
				}, nil)
			},
			want: &GetRecommendedLoanOptionResponse{
				ActiveOffer: nil,
				Eligibility: nil,
			},
		},
		{
			name: "return no offer when no active offers exist",
			args: args{
				ctx: context.Background(),
				req: &GetRecommendedLoanOptionRequest{ActorId: "actor-1"},
			},
			setupMocks: func(ctrl *gomock.Controller, onbClientMock *onbMock.MockOnboardingClient, profileClientMock *profileMocks.MockProfileClient, multiDbProviderMock *mockMultiDBProvider.MockIMultiDbProvider, priorityProviderFactoryMock *ppFactoryMock.MockIPriorityProviderFactory, eligibilityEvaluatorFactoryMock *eeFactoryMock.MockEligibilityEvaluatorFactory, savingsClientMock *savingsMock.MockSavingsClient) {
				savingsClientMock.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil)
				onbClientMock.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: "actor-1",
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				profileClientMock.EXPECT().GetUserProfile(gomock.Any(), &profilePb.GetUserProfileRequest{
					ActorId: "actor-1",
				}).Return(&profilePb.GetUserProfileResponse{
					Status: rpc.StatusOk(),
				}, nil)

				multiDbProviderMock.EXPECT().CheckAndGetLoanOffersForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanOffersForActorRequest{
					ActorId:      "actor-1",
					LoanPrograms: helper.GetLoanProgramsForLoanType(palPb.LoanType_LOAN_TYPE_PERSONAL),
				}).Return(&multiDbProvider.CheckAndGetLoanOffersForActorResponse{
					Offers: []*palPb.LoanOffer{}}, nil)

				priorityProviderMock := ppMock.NewMockIPriorityProvider(ctrl)
				priorityProviderMock.EXPECT().GetLoanHeaderPrioritisation(gomock.Any(), gomock.Any()).Return([]*palPb.LoanHeader{}, nil)
				priorityProviderFactoryMock.EXPECT().GetPriorityProvider(gomock.Any(), &priorityProviderFactory.GetPriorityProviderFactoryRequest{
					IsNonFiCoreUser: false,
				}).Return(priorityProviderMock, nil)
			},
			want: &GetRecommendedLoanOptionResponse{
				ActiveOffer: nil,
				Eligibility: nil,
			},
		},
		{
			name: "return recommended loan offer for fi core user",
			args: args{
				ctx: context.Background(),
				req: &GetRecommendedLoanOptionRequest{ActorId: "actor-1"},
			},
			setupMocks: func(ctrl *gomock.Controller, onbClientMock *onbMock.MockOnboardingClient, profileClientMock *profileMocks.MockProfileClient, multiDbProviderMock *mockMultiDBProvider.MockIMultiDbProvider, priorityProviderFactoryMock *ppFactoryMock.MockIPriorityProviderFactory, eligibilityEvaluatorFactoryMock *eeFactoryMock.MockEligibilityEvaluatorFactory, savingsClientMock *savingsMock.MockSavingsClient) {
				savingsClientMock.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil)
				onbClientMock.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: "actor-1",
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				profileClientMock.EXPECT().GetUserProfile(gomock.Any(), &profilePb.GetUserProfileRequest{
					ActorId: "actor-1",
				}).Return(&profilePb.GetUserProfileResponse{
					Status: rpc.StatusOk(),
				}, nil)

				multiDbProviderMock.EXPECT().CheckAndGetLoanOffersForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanOffersForActorRequest{
					ActorId:      "actor-1",
					LoanPrograms: helper.GetLoanProgramsForLoanType(palPb.LoanType_LOAN_TYPE_PERSONAL),
				}).Return(&multiDbProvider.CheckAndGetLoanOffersForActorResponse{
					Offers: []*palPb.LoanOffer{activeLoanOfferFed, activeLoanOfferLL}}, nil)

				priorityProviderMock := ppMock.NewMockIPriorityProvider(ctrl)
				priorityProviderMock.EXPECT().GetLoanHeaderPrioritisation(gomock.Any(), gomock.Any()).Return([]*palPb.LoanHeader{
					federalPreApprovedLh,
					llPreApprovedLh,
				}, nil)
				priorityProviderFactoryMock.EXPECT().GetPriorityProvider(gomock.Any(), &priorityProviderFactory.GetPriorityProviderFactoryRequest{
					IsNonFiCoreUser: false,
				}).Return(priorityProviderMock, nil)

				federalEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				federalEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), federalPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: true,
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(federalPreApprovedLh, false).Return(federalEligibilityEvaluatorMock, nil)

				llEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				llEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), llPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: true,
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(llPreApprovedLh, false).Return(llEligibilityEvaluatorMock, nil)

			},
			want: &GetRecommendedLoanOptionResponse{
				ActiveOffer: activeLoanOfferFed,
			},
		},
		{
			name: "return recommended loan offer for non fi core user",
			args: args{
				ctx: context.Background(),
				req: &GetRecommendedLoanOptionRequest{ActorId: "actor-1"},
			},
			setupMocks: func(ctrl *gomock.Controller, onbClientMock *onbMock.MockOnboardingClient, profileClientMock *profileMocks.MockProfileClient, multiDbProviderMock *mockMultiDBProvider.MockIMultiDbProvider, priorityProviderFactoryMock *ppFactoryMock.MockIPriorityProviderFactory, eligibilityEvaluatorFactoryMock *eeFactoryMock.MockEligibilityEvaluatorFactory, savingsClientMock *savingsMock.MockSavingsClient) {
				savingsClientMock.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, status.Error(codes.NotFound, ""))
				onbClientMock.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: "actor-1",
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       &rpc.Status{Code: uint32(codes.OK)},
					IsFiLiteUser: true,
				}, nil)
				profileClientMock.EXPECT().GetUserProfile(gomock.Any(), &profilePb.GetUserProfileRequest{
					ActorId: "actor-1",
				}).Return(&profilePb.GetUserProfileResponse{
					Status: &rpc.Status{Code: uint32(codes.OK)},
				}, nil).AnyTimes()

				multiDbProviderMock.EXPECT().CheckAndGetLoanOffersForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanOffersForActorRequest{
					ActorId:      "actor-1",
					LoanPrograms: helper.GetLoanProgramsForLoanType(palPb.LoanType_LOAN_TYPE_PERSONAL),
				}).Return(&multiDbProvider.CheckAndGetLoanOffersForActorResponse{
					Offers: []*palPb.LoanOffer{activeLoanOfferFed, activeLoanOfferLL}}, nil)

				priorityProviderMock := ppMock.NewMockIPriorityProvider(ctrl)
				priorityProviderMock.EXPECT().GetLoanHeaderPrioritisation(gomock.Any(), gomock.Any()).Return([]*palPb.LoanHeader{
					llPreApprovedLh,
					federalPreApprovedLh,
				}, nil)
				priorityProviderFactoryMock.EXPECT().GetPriorityProvider(gomock.Any(), &priorityProviderFactory.GetPriorityProviderFactoryRequest{
					IsNonFiCoreUser: true,
				}).Return(priorityProviderMock, nil)

				federalEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				federalEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), federalPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: true,
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(federalPreApprovedLh, true).Return(federalEligibilityEvaluatorMock, nil)

				llEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				llEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), llPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: true,
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(llPreApprovedLh, true).Return(llEligibilityEvaluatorMock, nil)

			},
			want: &GetRecommendedLoanOptionResponse{
				ActiveOffer: activeLoanOfferLL,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			savingsClientMock := savingsMock.NewMockSavingsClient(ctrl)
			onbClientMock := onbMock.NewMockOnboardingClient(ctrl)
			profileClientMock := profileMocks.NewMockProfileClient(ctrl)
			multiDBProviderMock := mockMultiDBProvider.NewMockIMultiDbProvider(ctrl)
			priorityProviderFactoryMock := ppFactoryMock.NewMockIPriorityProviderFactory(ctrl)
			eligibilityEvaluatorFactoryMock := eeFactoryMock.NewMockEligibilityEvaluatorFactory(ctrl)

			tt.setupMocks(ctrl, onbClientMock, profileClientMock, multiDBProviderMock, priorityProviderFactoryMock, eligibilityEvaluatorFactoryMock, savingsClientMock)

			r := &LoanOptionRecommender{
				onbClient:                   onbClientMock,
				rpcHelper:                   helper.NewRpcHelper(nil, nil, nil, savingsClientMock, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, profileClientMock, nil, nil, nil, nil, nil, nil, nil, onbClientMock, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil),
				multiDbProvider:             multiDBProviderMock,
				priorityProviderFactory:     priorityProviderFactoryMock,
				eligibilityEvaluatorFactory: eligibilityEvaluatorFactoryMock,
				vendorProgramConfig:         dynConf.VendorProgramLevelFeature(),
				savingsClient:               savingsClientMock,
				lopeOverrideConfig:          dynConf.LopeOverrideConfig(),
			}

			got, err := r.GetRecommendedLoanOption(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRecommendedLoanOption() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
			}
			if cmp.Diff(got, tt.want, opts...) != "" {
				t.Errorf("GetRecommendedLoanOption() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLoanOptionRecommender_GetRankedLoanOptions(t *testing.T) {
	type args struct {
		ctx context.Context
		req *GetRankedLoanOptionsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(ctrl *gomock.Controller, onbClientMock *onbMock.MockOnboardingClient, profileClientMock *profileMocks.MockProfileClient, multiDbProviderMock *mockMultiDBProvider.MockIMultiDbProvider, priorityProviderFactoryMock *ppFactoryMock.MockIPriorityProviderFactory, eligibilityEvaluatorFactoryMock *eeFactoryMock.MockEligibilityEvaluatorFactory, savingsClientMock *savingsMock.MockSavingsClient)
		want       *GetRankedLoanOptionsResponse
		wantErr    bool
	}{
		{
			name: "return ranked loan offers for fi core user",
			args: args{
				ctx: context.Background(),
				req: &GetRankedLoanOptionsRequest{ActorId: "actor-1"},
			},
			setupMocks: func(ctrl *gomock.Controller, onbClientMock *onbMock.MockOnboardingClient, profileClientMock *profileMocks.MockProfileClient, multiDbProviderMock *mockMultiDBProvider.MockIMultiDbProvider, priorityProviderFactoryMock *ppFactoryMock.MockIPriorityProviderFactory, eligibilityEvaluatorFactoryMock *eeFactoryMock.MockEligibilityEvaluatorFactory, savingsClientMock *savingsMock.MockSavingsClient) {
				savingsClientMock.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil)
				onbClientMock.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: "actor-1",
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)

				profileClientMock.EXPECT().GetUserProfile(gomock.Any(), &profilePb.GetUserProfileRequest{
					ActorId: "actor-1",
				}).Return(&profilePb.GetUserProfileResponse{
					Status: rpc.StatusOk(),
				}, nil)

				multiDbProviderMock.EXPECT().CheckAndGetLoanOffersForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanOffersForActorRequest{
					ActorId:      "actor-1",
					LoanPrograms: helper.GetLoanProgramsForLoanType(palPb.LoanType_LOAN_TYPE_PERSONAL),
				}).Return(&multiDbProvider.CheckAndGetLoanOffersForActorResponse{
					Offers: []*palPb.LoanOffer{activeLoanOfferFed, activeLoanOfferLL}}, nil)

				priorityProviderMock := ppMock.NewMockIPriorityProvider(ctrl)
				priorityProviderMock.EXPECT().GetLoanHeaderPrioritisation(gomock.Any(), gomock.Any()).Return([]*palPb.LoanHeader{
					federalPreApprovedLh,
					llPreApprovedLh,
				}, nil)
				priorityProviderFactoryMock.EXPECT().GetPriorityProvider(gomock.Any(), &priorityProviderFactory.GetPriorityProviderFactoryRequest{
					IsNonFiCoreUser: false,
				}).Return(priorityProviderMock, nil)

				federalEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				federalEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), federalPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: true,
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(federalPreApprovedLh, false).Return(federalEligibilityEvaluatorMock, nil)

				llEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				llEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), llPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: true,
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(llPreApprovedLh, false).Return(llEligibilityEvaluatorMock, nil)

			},
			want: &GetRankedLoanOptionsResponse{
				ActiveOffers: []*palPb.LoanOffer{activeLoanOfferFed, activeLoanOfferLL},
			},
		},
		{
			name: "return only ranked loan hard offers and skip all other offer types if there's even one hard offer",
			args: args{
				ctx: context.Background(),
				req: &GetRankedLoanOptionsRequest{ActorId: "actor-1"},
			},
			setupMocks: func(ctrl *gomock.Controller, onbClientMock *onbMock.MockOnboardingClient, profileClientMock *profileMocks.MockProfileClient, multiDbProviderMock *mockMultiDBProvider.MockIMultiDbProvider, priorityProviderFactoryMock *ppFactoryMock.MockIPriorityProviderFactory, eligibilityEvaluatorFactoryMock *eeFactoryMock.MockEligibilityEvaluatorFactory, savingsClientMock *savingsMock.MockSavingsClient) {
				savingsClientMock.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil)
				onbClientMock.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: "actor-1",
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)

				profileClientMock.EXPECT().GetUserProfile(gomock.Any(), &profilePb.GetUserProfileRequest{
					ActorId: "actor-1",
				}).Return(&profilePb.GetUserProfileResponse{
					Status: rpc.StatusOk(),
				}, nil)

				multiDbProviderMock.EXPECT().CheckAndGetLoanOffersForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanOffersForActorRequest{
					ActorId:      "actor-1",
					LoanPrograms: helper.GetLoanProgramsForLoanType(palPb.LoanType_LOAN_TYPE_PERSONAL),
				}).Return(&multiDbProvider.CheckAndGetLoanOffersForActorResponse{
					Offers: []*palPb.LoanOffer{activeLoanOfferFed, activeLoanOfferLL, activeLoanOfferLdc1, activeLoanOfferLdc2}}, nil)

				priorityProviderMock := ppMock.NewMockIPriorityProvider(ctrl)
				priorityProviderMock.EXPECT().GetLoanHeaderPrioritisation(gomock.Any(), gomock.Any()).Return([]*palPb.LoanHeader{
					federalPreApprovedLh,
					llPreApprovedLh,
					ldcPreApprovedLh,
				}, nil)
				priorityProviderFactoryMock.EXPECT().GetPriorityProvider(gomock.Any(), &priorityProviderFactory.GetPriorityProviderFactoryRequest{
					IsNonFiCoreUser: false,
				}).Return(priorityProviderMock, nil)

				federalEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				federalEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), federalPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: true,
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(federalPreApprovedLh, false).Return(federalEligibilityEvaluatorMock, nil)

				llEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				llEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), llPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: false,
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(llPreApprovedLh, false).Return(llEligibilityEvaluatorMock, nil)

				ldcEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				ldcEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), ldcPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: true,
				}, nil).Times(2)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(ldcPreApprovedLh, false).Return(ldcEligibilityEvaluatorMock, nil)

			},
			want: &GetRankedLoanOptionsResponse{
				ActiveOffers: []*palPb.LoanOffer{activeLoanOfferLdc1, activeLoanOfferLdc2},
			},
		},
		{
			name: "return only ranked loan offers and skip eligibility even if it is present",
			args: args{
				ctx: context.Background(),
				req: &GetRankedLoanOptionsRequest{ActorId: "actor-1"},
			},
			setupMocks: func(ctrl *gomock.Controller, onbClientMock *onbMock.MockOnboardingClient, profileClientMock *profileMocks.MockProfileClient, multiDbProviderMock *mockMultiDBProvider.MockIMultiDbProvider, priorityProviderFactoryMock *ppFactoryMock.MockIPriorityProviderFactory, eligibilityEvaluatorFactoryMock *eeFactoryMock.MockEligibilityEvaluatorFactory, savingsClientMock *savingsMock.MockSavingsClient) {
				savingsClientMock.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil)
				onbClientMock.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: "actor-1",
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)

				profileClientMock.EXPECT().GetUserProfile(gomock.Any(), &profilePb.GetUserProfileRequest{
					ActorId: "actor-1",
				}).Return(&profilePb.GetUserProfileResponse{
					Status: rpc.StatusOk(),
				}, nil)

				multiDbProviderMock.EXPECT().CheckAndGetLoanOffersForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanOffersForActorRequest{
					ActorId:      "actor-1",
					LoanPrograms: helper.GetLoanProgramsForLoanType(palPb.LoanType_LOAN_TYPE_PERSONAL),
				}).Return(&multiDbProvider.CheckAndGetLoanOffersForActorResponse{
					Offers: []*palPb.LoanOffer{activeLoanOfferFed, activeLoanOfferLL}}, nil)

				priorityProviderMock := ppMock.NewMockIPriorityProvider(ctrl)
				priorityProviderMock.EXPECT().GetLoanHeaderPrioritisation(gomock.Any(), gomock.Any()).Return([]*palPb.LoanHeader{
					federalPreApprovedLh, llPreApprovedLh,
				}, nil)
				priorityProviderFactoryMock.EXPECT().GetPriorityProvider(gomock.Any(), &priorityProviderFactory.GetPriorityProviderFactoryRequest{
					IsNonFiCoreUser: false,
				}).Return(priorityProviderMock, nil)

				federalEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				federalEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), federalPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: true,
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(federalPreApprovedLh, false).Return(federalEligibilityEvaluatorMock, nil)

				llEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				llEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), llPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: false,
					CheckEligibility: &eeProviders.CheckEligibility{
						ShouldCheckEligibility: true,
						LoanHeader:             llPreApprovedLh,
					},
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(llPreApprovedLh, false).Return(llEligibilityEvaluatorMock, nil)
			},
			want: &GetRankedLoanOptionsResponse{
				ActiveOffers: []*palPb.LoanOffer{activeLoanOfferFed},
			},
		},
		{
			name: "return first eligibility if no loan offers are present",
			args: args{
				ctx: context.Background(),
				req: &GetRankedLoanOptionsRequest{ActorId: "actor-1"},
			},
			setupMocks: func(ctrl *gomock.Controller, onbClientMock *onbMock.MockOnboardingClient, profileClientMock *profileMocks.MockProfileClient, multiDbProviderMock *mockMultiDBProvider.MockIMultiDbProvider, priorityProviderFactoryMock *ppFactoryMock.MockIPriorityProviderFactory, eligibilityEvaluatorFactoryMock *eeFactoryMock.MockEligibilityEvaluatorFactory, savingsClientMock *savingsMock.MockSavingsClient) {
				savingsClientMock.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil)
				onbClientMock.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: "actor-1",
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				profileClientMock.EXPECT().GetUserProfile(gomock.Any(), &profilePb.GetUserProfileRequest{
					ActorId: "actor-1",
				}).Return(&profilePb.GetUserProfileResponse{
					Status: rpc.StatusOk(),
				}, nil)

				multiDbProviderMock.EXPECT().CheckAndGetLoanOffersForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanOffersForActorRequest{
					ActorId:      "actor-1",
					LoanPrograms: helper.GetLoanProgramsForLoanType(palPb.LoanType_LOAN_TYPE_PERSONAL),
				}).Return(&multiDbProvider.CheckAndGetLoanOffersForActorResponse{
					Offers: []*palPb.LoanOffer{}}, nil)

				priorityProviderMock := ppMock.NewMockIPriorityProvider(ctrl)
				priorityProviderMock.EXPECT().GetLoanHeaderPrioritisation(gomock.Any(), gomock.Any()).Return([]*palPb.LoanHeader{
					federalPreApprovedLh, llPreApprovedLh,
				}, nil)
				priorityProviderFactoryMock.EXPECT().GetPriorityProvider(gomock.Any(), &priorityProviderFactory.GetPriorityProviderFactoryRequest{
					IsNonFiCoreUser: false,
				}).Return(priorityProviderMock, nil)

				federalEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				federalEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), federalPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: false,
					CheckEligibility: &eeProviders.CheckEligibility{
						ShouldCheckEligibility: true,
						LoanHeader:             federalPreApprovedLh,
					},
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(federalPreApprovedLh, false).Return(federalEligibilityEvaluatorMock, nil)

				llEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				llEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), llPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: false,
					CheckEligibility: &eeProviders.CheckEligibility{
						ShouldCheckEligibility: true,
						LoanHeader:             llPreApprovedLh,
					},
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(llPreApprovedLh, false).Return(llEligibilityEvaluatorMock, nil)
			},
			want: &GetRankedLoanOptionsResponse{
				Eligibility: &Eligibility{
					CheckEligibility: true,
					LoanHeader:       federalPreApprovedLh,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			savingsClientMock := savingsMock.NewMockSavingsClient(ctrl)
			onbClientMock := onbMock.NewMockOnboardingClient(ctrl)
			profileClientMock := profileMocks.NewMockProfileClient(ctrl)
			multiDBProviderMock := mockMultiDBProvider.NewMockIMultiDbProvider(ctrl)
			priorityProviderFactoryMock := ppFactoryMock.NewMockIPriorityProviderFactory(ctrl)
			eligibilityEvaluatorFactoryMock := eeFactoryMock.NewMockEligibilityEvaluatorFactory(ctrl)

			tt.setupMocks(ctrl, onbClientMock, profileClientMock, multiDBProviderMock, priorityProviderFactoryMock, eligibilityEvaluatorFactoryMock, savingsClientMock)

			r := &LoanOptionRecommender{
				onbClient:                   onbClientMock,
				rpcHelper:                   helper.NewRpcHelper(nil, nil, nil, savingsClientMock, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, profileClientMock, nil, nil, nil, nil, nil, nil, nil, onbClientMock, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil),
				multiDbProvider:             multiDBProviderMock,
				priorityProviderFactory:     priorityProviderFactoryMock,
				eligibilityEvaluatorFactory: eligibilityEvaluatorFactoryMock,
				vendorProgramConfig:         dynConf.VendorProgramLevelFeature(),
				savingsClient:               savingsClientMock,
				lopeOverrideConfig:          dynConf.LopeOverrideConfig(),
			}

			got, err := r.GetRankedLoanOptions(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRankedLoanOptions() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRankedLoanOptions() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLoanOptionRecommender_GetCombinedRankedLoanOptions(t *testing.T) {
	type args struct {
		ctx context.Context
		req *GetCombinedRankedLoanOptionsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(ctrl *gomock.Controller, onbClientMock *onbMock.MockOnboardingClient, profileClientMock *profileMocks.MockProfileClient, multiDbProviderMock *mockMultiDBProvider.MockIMultiDbProvider, priorityProviderFactoryMock *ppFactoryMock.MockIPriorityProviderFactory, eligibilityEvaluatorFactoryMock *eeFactoryMock.MockEligibilityEvaluatorFactory, savingsClientMock *savingsMock.MockSavingsClient)
		want       *GetCombinedRankedLoanOptionsResponse
		wantErr    bool
	}{
		{
			name: "return ranked loan offers for fi core user",
			args: args{
				ctx: context.Background(),
				req: &GetCombinedRankedLoanOptionsRequest{ActorId: "actor-1"},
			},
			setupMocks: func(ctrl *gomock.Controller, onbClientMock *onbMock.MockOnboardingClient, profileClientMock *profileMocks.MockProfileClient, multiDbProviderMock *mockMultiDBProvider.MockIMultiDbProvider, priorityProviderFactoryMock *ppFactoryMock.MockIPriorityProviderFactory, eligibilityEvaluatorFactoryMock *eeFactoryMock.MockEligibilityEvaluatorFactory, savingsClientMock *savingsMock.MockSavingsClient) {
				savingsClientMock.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil)
				onbClientMock.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: "actor-1",
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)

				profileClientMock.EXPECT().GetUserProfile(gomock.Any(), &profilePb.GetUserProfileRequest{
					ActorId: "actor-1",
				}).Return(&profilePb.GetUserProfileResponse{
					Status: rpc.StatusOk(),
				}, nil)

				multiDbProviderMock.EXPECT().CheckAndGetLoanOffersForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanOffersForActorRequest{
					ActorId:      "actor-1",
					LoanPrograms: helper.GetLoanProgramsForLoanType(palPb.LoanType_LOAN_TYPE_PERSONAL),
				}).Return(&multiDbProvider.CheckAndGetLoanOffersForActorResponse{
					Offers: []*palPb.LoanOffer{activeLoanOfferFed, activeLoanOfferLL}}, nil)

				priorityProviderMock := ppMock.NewMockIPriorityProvider(ctrl)
				priorityProviderMock.EXPECT().GetLoanHeaderPrioritisation(gomock.Any(), gomock.Any()).Return([]*palPb.LoanHeader{
					federalPreApprovedLh,
					llPreApprovedLh,
				}, nil)
				priorityProviderFactoryMock.EXPECT().GetPriorityProvider(gomock.Any(), &priorityProviderFactory.GetPriorityProviderFactoryRequest{
					IsNonFiCoreUser: false,
				}).Return(priorityProviderMock, nil)

				federalEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				federalEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), federalPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: true,
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(federalPreApprovedLh, false).Return(federalEligibilityEvaluatorMock, nil)

				llEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				llEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), llPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: true,
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(llPreApprovedLh, false).Return(llEligibilityEvaluatorMock, nil)

			},
			want: &GetCombinedRankedLoanOptionsResponse{
				LoanOptions: []*palPb.LoanOption{
					{LoanOptionType: &palPb.LoanOption_LoanOffer{LoanOffer: activeLoanOfferFed}},
					{LoanOptionType: &palPb.LoanOption_LoanOffer{LoanOffer: activeLoanOfferLL}},
				},
			},
		},
		{
			name: "return eligibilities if no loan offers are present",
			args: args{
				ctx: context.Background(),
				req: &GetCombinedRankedLoanOptionsRequest{ActorId: "actor-1"},
			},
			setupMocks: func(ctrl *gomock.Controller, onbClientMock *onbMock.MockOnboardingClient, profileClientMock *profileMocks.MockProfileClient, multiDbProviderMock *mockMultiDBProvider.MockIMultiDbProvider, priorityProviderFactoryMock *ppFactoryMock.MockIPriorityProviderFactory, eligibilityEvaluatorFactoryMock *eeFactoryMock.MockEligibilityEvaluatorFactory, savingsClientMock *savingsMock.MockSavingsClient) {
				savingsClientMock.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil)
				onbClientMock.EXPECT().GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{
					ActorId: "actor-1",
					Feature: onbPb.Feature_FEATURE_SA,
				}).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				profileClientMock.EXPECT().GetUserProfile(gomock.Any(), &profilePb.GetUserProfileRequest{
					ActorId: "actor-1",
				}).Return(&profilePb.GetUserProfileResponse{
					Status: rpc.StatusOk(),
				}, nil)

				multiDbProviderMock.EXPECT().CheckAndGetLoanOffersForActor(gomock.Any(), &multiDbProvider.CheckAndGetLoanOffersForActorRequest{
					ActorId:      "actor-1",
					LoanPrograms: helper.GetLoanProgramsForLoanType(palPb.LoanType_LOAN_TYPE_PERSONAL),
				}).Return(&multiDbProvider.CheckAndGetLoanOffersForActorResponse{
					Offers: []*palPb.LoanOffer{}}, nil)

				priorityProviderMock := ppMock.NewMockIPriorityProvider(ctrl)
				priorityProviderMock.EXPECT().GetLoanHeaderPrioritisation(gomock.Any(), gomock.Any()).Return([]*palPb.LoanHeader{
					federalPreApprovedLh, llPreApprovedLh,
				}, nil)
				priorityProviderFactoryMock.EXPECT().GetPriorityProvider(gomock.Any(), &priorityProviderFactory.GetPriorityProviderFactoryRequest{
					IsNonFiCoreUser: false,
				}).Return(priorityProviderMock, nil)

				federalEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				federalEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), federalPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: false,
					CheckEligibility: &eeProviders.CheckEligibility{
						ShouldCheckEligibility: true,
						LoanHeader:             federalPreApprovedLh,
					},
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(federalPreApprovedLh, false).Return(federalEligibilityEvaluatorMock, nil)

				llEligibilityEvaluatorMock := eeMock.NewMockILoansEligibilityProvider(ctrl)
				llEligibilityEvaluatorMock.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), llPreApprovedLh).Return(&eeProviders.EvaluateLoanEligibilityResponse{
					IsOfferAvailable: false,
					CheckEligibility: &eeProviders.CheckEligibility{
						ShouldCheckEligibility: true,
						LoanHeader:             llPreApprovedLh,
					},
				}, nil)
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(llPreApprovedLh, false).Return(llEligibilityEvaluatorMock, nil)
			},
			want: &GetCombinedRankedLoanOptionsResponse{
				LoanOptions: []*palPb.LoanOption{
					{LoanOptionType: &palPb.LoanOption_EligibilityHeader{EligibilityHeader: federalPreApprovedLh}},
					{LoanOptionType: &palPb.LoanOption_EligibilityHeader{EligibilityHeader: llPreApprovedLh}},
				},
			},
		},
		{
			name: "show only lenden offers when hard offers are present",
			args: args{
				ctx: context.Background(),
				req: &GetCombinedRankedLoanOptionsRequest{ActorId: "actor-1"},
			},
			setupMocks: func(ctrl *gomock.Controller, onbClientMock *onbMock.MockOnboardingClient, profileClientMock *profileMocks.MockProfileClient, multiDbProviderMock *mockMultiDBProvider.MockIMultiDbProvider, priorityProviderFactoryMock *ppFactoryMock.MockIPriorityProviderFactory, eligibilityEvaluatorFactoryMock *eeFactoryMock.MockEligibilityEvaluatorFactory, savingsClientMock *savingsMock.MockSavingsClient) {
				savingsClientMock.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(nil, nil)
				onbClientMock.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{Status: &rpc.Status{Code: uint32(codes.OK)}, IsFiLiteUser: false}, nil)
				profileClientMock.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&profilePb.GetUserProfileResponse{Status: &rpc.Status{Code: uint32(codes.OK)}}, nil)

				allOffers := []*palPb.LoanOffer{activeLoanOfferFed, activeLoanOfferLL, activeLoanOfferLdc1, activeLoanOfferLdc2, activeLoanOfferFederalNtb, activeLoanOfferAbflRtd, activeLoanOfferSgRtd, activeLoanOfferMvRtd, activeLoanOfferLendenRtd}
				multiDbProviderMock.EXPECT().CheckAndGetLoanOffersForActor(gomock.Any(), gomock.Any()).Return(&multiDbProvider.CheckAndGetLoanOffersForActorResponse{Offers: allOffers}, nil)

				priorityProviderMock := ppMock.NewMockIPriorityProvider(ctrl)
				prioritizedLoanHeaders := []*palPb.LoanHeader{federalNtbLh, abflRtdLh, sgRtdLh, mvRtdLh, lendenRtdLh, federalPreApprovedLh, llPreApprovedLh, ldcPreApprovedLh}
				priorityProviderMock.EXPECT().GetLoanHeaderPrioritisation(gomock.Any(), gomock.Any()).Return(prioritizedLoanHeaders, nil)
				priorityProviderFactoryMock.EXPECT().GetPriorityProvider(gomock.Any(), gomock.Any()).Return(priorityProviderMock, nil)

				mockEE := eeMock.NewMockILoansEligibilityProvider(ctrl)
				mockEE.EXPECT().EvaluateLoanEligibility(gomock.Any(), gomock.Any(), gomock.Any()).Return(&eeProviders.EvaluateLoanEligibilityResponse{IsOfferAvailable: true}, nil).AnyTimes()
				eligibilityEvaluatorFactoryMock.EXPECT().GetEligibilityEvaluatorProvider(gomock.Any(), gomock.Any()).Return(mockEE, nil).AnyTimes()
			},
			want: &GetCombinedRankedLoanOptionsResponse{
				LoanOptions: []*palPb.LoanOption{
					{LoanOptionType: &palPb.LoanOption_LoanOffer{LoanOffer: activeLoanOfferLdc1}},
					{LoanOptionType: &palPb.LoanOption_LoanOffer{LoanOffer: activeLoanOfferLdc2}},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			savingsClientMock := savingsMock.NewMockSavingsClient(ctrl)
			onbClientMock := onbMock.NewMockOnboardingClient(ctrl)
			profileClientMock := profileMocks.NewMockProfileClient(ctrl)
			multiDBProviderMock := mockMultiDBProvider.NewMockIMultiDbProvider(ctrl)
			priorityProviderFactoryMock := ppFactoryMock.NewMockIPriorityProviderFactory(ctrl)
			eligibilityEvaluatorFactoryMock := eeFactoryMock.NewMockEligibilityEvaluatorFactory(ctrl)

			tt.setupMocks(ctrl, onbClientMock, profileClientMock, multiDBProviderMock, priorityProviderFactoryMock, eligibilityEvaluatorFactoryMock, savingsClientMock)

			conf, _ := genconf.NewConfig()
			conf.Set(true, false, []string{"SgEtbNewEligibilityFlow", "IsAllowed"})
			conf.Set(false, false, []string{"LopeOverrideConfig", "ShowSgEligibilityOverAbflSoftOffer"})
			conf.Set(true, false, []string{"LopeOverrideConfig", "ShowSgEligibilityOverMvSoftOffer"})
			conf.Set([]*common.LoanOptionPriority{
				{Vendor: "FEDERAL", LoanProgram: "LOAN_PROGRAM_PRE_APPROVED_LOAN"},
				{Vendor: "LIQUILOANS", LoanProgram: "LOAN_PROGRAM_PRE_APPROVED_LOAN"},
			}, false, []string{"LoanPriorityOrderFiCore"})

			r := &LoanOptionRecommender{
				onbClient:                   onbClientMock,
				rpcHelper:                   helper.NewRpcHelper(nil, nil, nil, savingsClientMock, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, profileClientMock, nil, nil, nil, nil, nil, nil, nil, onbClientMock, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil),
				multiDbProvider:             multiDBProviderMock,
				priorityProviderFactory:     priorityProviderFactoryMock,
				eligibilityEvaluatorFactory: eligibilityEvaluatorFactoryMock,
				vendorProgramConfig:         conf.VendorProgramLevelFeature(),
				savingsClient:               savingsClientMock,
				lopeOverrideConfig:          conf.LopeOverrideConfig(),
			}

			got, err := r.GetCombinedRankedLoanOptions(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCombinedRankedLoanOptions() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("GetCombinedRankedLoanOptions() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
