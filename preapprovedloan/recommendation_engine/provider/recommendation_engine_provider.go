package provider

import (
	"context"
	"errors"
	"fmt"
	"strings"

	pkgErrors "github.com/pkg/errors"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/syncmap"
	"github.com/epifi/gamma/preapprovedloan/config/common"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/savings"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	"github.com/epifi/gamma/preapprovedloan/helper"
	multiDbProvider "github.com/epifi/gamma/preapprovedloan/multidb_provider"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers"
	priorityProviderFactory "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider"
	pb "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/providers"
)

type LoanOptionRecommender struct {
	multiDbProvider             multiDbProvider.IMultiDbProvider
	priorityProviderFactory     priorityProviderFactory.IPriorityProviderFactory
	eligibilityEvaluatorFactory eligibility_evaluator.EligibilityEvaluatorFactory
	rpcHelper                   *helper.RpcHelper
	onbClient                   onbPb.OnboardingClient
	vendorProgramConfig         *genconf.VendorProgramLevelFeature
	savingsClient               savings.SavingsClient
	lopeOverrideConfig          *genconf.LopeOverrideConfig
}

func NewLoanOptionRecommender(
	multiDbProvider multiDbProvider.IMultiDbProvider,
	priorityProviderFactory priorityProviderFactory.IPriorityProviderFactory,
	eligibilityEvaluatorFactory eligibility_evaluator.EligibilityEvaluatorFactory,
	rpcHelper *helper.RpcHelper,
	onbClient onbPb.OnboardingClient,
	vendorProgramConfig *genconf.VendorProgramLevelFeature,
	savingsClient savings.SavingsClient,
	lopeOverrideConfig *genconf.LopeOverrideConfig,
) *LoanOptionRecommender {
	return &LoanOptionRecommender{
		multiDbProvider:             multiDbProvider,
		priorityProviderFactory:     priorityProviderFactory,
		eligibilityEvaluatorFactory: eligibilityEvaluatorFactory,
		rpcHelper:                   rpcHelper,
		onbClient:                   onbClient,
		vendorProgramConfig:         vendorProgramConfig,
		savingsClient:               savingsClient,
		lopeOverrideConfig:          lopeOverrideConfig,
	}
}

func (r *LoanOptionRecommender) GetRecommendedLoanOption(ctx context.Context, req *GetRecommendedLoanOptionRequest) (*GetRecommendedLoanOptionResponse, error) {
	rankedLoanOffers, highestRankedEligibility, err := r.getRankedLoanOptionsForActor(ctx, req.ActorId)
	if err != nil {
		return nil, pkgErrors.Wrap(err, "error in getting ranked loan options for actor")
	}

	// prioritize loan offers over eligibility
	if len(rankedLoanOffers) > 0 {
		return &GetRecommendedLoanOptionResponse{
			ActiveOffer: rankedLoanOffers[0],
		}, nil
	}

	return &GetRecommendedLoanOptionResponse{
		Eligibility: highestRankedEligibility,
	}, nil
}

func (r *LoanOptionRecommender) GetRankedLoanOptions(ctx context.Context, req *GetRankedLoanOptionsRequest) (*GetRankedLoanOptionsResponse, error) {
	rankedLoanOffers, highestRankedEligibility, err := r.getRankedLoanOptionsForActor(ctx, req.ActorId)
	if err != nil {
		return nil, pkgErrors.Wrap(err, "error in getting ranked loan options for actor")
	}

	// prioritize loan offers over eligibility
	if len(rankedLoanOffers) > 0 {
		return &GetRankedLoanOptionsResponse{
			ActiveOffers: rankedLoanOffers,
		}, nil
	}

	return &GetRankedLoanOptionsResponse{
		Eligibility: highestRankedEligibility,
	}, nil
}

func (r *LoanOptionRecommender) getRankedLoanOptionsForActor(ctx context.Context, actorId string) ([]*palPb.LoanOffer, *Eligibility, error) {
	offersMap := make(map[string][]*palPb.LoanOffer)

	riskyUser, isNonFiCoreUser, userErr := r.checkUserProperty(ctx, actorId)
	if userErr != nil {
		return nil, nil, userErr
	}
	if riskyUser {
		return nil, nil, nil
	}

	origOwnership := epificontext.OwnershipFromContext(ctx)
	defer func() {
		ctx = epificontext.WithOwnership(ctx, origOwnership)
	}()
	// setting ownership to zero value so that CheckAndGetLoanOffersForActor will check for offers in all DBs
	ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_TECH)
	// fetching all active offers for non risky users from different Dbs and storing in map[vendor:loanProgram]
	offers, offerErr := r.multiDbProvider.CheckAndGetLoanOffersForActor(ctx, &multiDbProvider.CheckAndGetLoanOffersForActorRequest{
		ActorId:      actorId,
		LoanPrograms: helper.GetLoanProgramsForLoanType(palPb.LoanType_LOAN_TYPE_PERSONAL),
	})
	if offerErr != nil && !errors.Is(offerErr, epifierrors.ErrRecordNotFound) {
		return nil, nil, pkgErrors.Wrap(offerErr, "error fetching pl loan offer for actor")
	}
	if offers != nil {
		for _, lo := range offers.Offers {
			key := helper.GetRecommendationEngineOffersKey(lo.GetVendor(), lo.GetLoanProgram())
			offersMap[key] = append(offersMap[key], lo)
		}
	}

	priorityProvider, err := r.priorityProviderFactory.GetPriorityProvider(ctx, &priorityProviderFactory.GetPriorityProviderFactoryRequest{
		IsNonFiCoreUser: isNonFiCoreUser,
	})
	if err != nil {
		return nil, nil, pkgErrors.Wrap(offerErr, "error fetching priority provider factory")
	}

	loanHeaderPrioritisationRequest := pb.GetLoanHeaderPrioritisationRequest{
		ShouldCheckAvailability: true,
		ActorId:                 actorId,
	}
	loanHeaders, err := priorityProvider.GetLoanHeaderPrioritisation(ctx, loanHeaderPrioritisationRequest)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get loan header prioritisation error : %w", err)
	}

	// storing responses from different eligibility evaluator provides in sync map[vendor:loanProgram] []*LoanOfferEligibilityStatus
	loesMap := &syncmap.Map[string, []*LoanOfferEligibilityStatus]{}

	grp, grpCtx := errgroup.WithContext(ctx)
	for _, loanHeader := range loanHeaders {
		lh := loanHeader

		grp.Go(func() error {
			isDowntime := r.vendorProgramConfig.DownTimeConfig().Get(lh.GetVendor().String()).IsDownNow()
			if isDowntime {
				return nil
			}

			provider, err := r.eligibilityEvaluatorFactory.GetEligibilityEvaluatorProvider(lh, isNonFiCoreUser)
			if err != nil {
				return err
			}

			key := helper.GetRecommendationEngineOffersKey(lh.GetVendor(), lh.GetLoanProgram())
			var loes []*LoanOfferEligibilityStatus
			// check if the offers returned from the db are valid
			if los, found := offersMap[key]; found {
				for _, lo := range los {
					loeRes, loeErr := provider.EvaluateLoanEligibility(grpCtx, &providers.EvaluateLoanEligibilityRequest{
						LoanOffer:       lo,
						ActorId:         actorId,
						IsNonFiCoreUser: isNonFiCoreUser,
					}, lh)
					if loeErr != nil {
						return loeErr
					}
					if loeRes != nil {
						loes = append(loes, &LoanOfferEligibilityStatus{
							LoanOffer:              lo,
							IsOfferAvailable:       loeRes.IsLoanOfferAvailable(),
							IsEligibilityAvailable: loeRes.ShouldCheckLoanEligibility(),
							EligibilityLoanHeader:  loeRes.GetEligibilityLoanHeader(),
						})
					}
				}
			} else { // check if the loan header has a valid eligibility
				loeRes, loeErr := provider.EvaluateLoanEligibility(grpCtx, &providers.EvaluateLoanEligibilityRequest{
					ActorId:         actorId,
					IsNonFiCoreUser: isNonFiCoreUser,
				}, lh)
				if loeErr != nil {
					return loeErr
				}
				if loeRes != nil {
					loes = []*LoanOfferEligibilityStatus{
						{
							IsEligibilityAvailable: loeRes.ShouldCheckLoanEligibility(),
							EligibilityLoanHeader:  loeRes.GetEligibilityLoanHeader(),
						},
					}
				}
				if lh.GetVendor() == palPb.Vendor_STOCK_GUARDIAN_LSP && lh.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION {
					keyAbfl := helper.GetRecommendationEngineOffersKey(palPb.Vendor_ABFL, palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION)
					_, foundAbflOffer := offersMap[keyAbfl]
					if loeRes.ShouldCheckLoanEligibility() && foundAbflOffer {
						logger.Info(ctx, "found  SG eligibilty when ABFL offer was present in recommendation engine", zap.String(logger.ACTOR_ID_V2, actorId))
					}
				}
			}
			loesMap.Store(key, loes)
			return nil
		})
	}
	if grpWaitErr := grp.Wait(); grpWaitErr != nil {
		return nil, nil, pkgErrors.Wrap(grpWaitErr, "error in grp executions for eligibility evaluator")
	}

	return r.getRankedLoanOptionsUsingEligibilityMap(loanHeaders, loesMap, isNonFiCoreUser)
}

// getRankedLoanOptionsUsingEligibilityMap returns a ranked list of loan offers and highest ranked eligibility based on the loes map
// Consider the loan headers in the below order
// 1. LH1: No offer and eligibility true
// 2. LH2: Offer present and eligibility false
// 3. LH3: Offer present and eligibility false
// In this case we want to return LH2, LH3 offers and LH1 eligibility
func (r *LoanOptionRecommender) getRankedLoanOptionsUsingEligibilityMap(loanHeaders []*palPb.LoanHeader, loesMap *syncmap.Map[string, []*LoanOfferEligibilityStatus], isNonFiCoreUser bool) ([]*palPb.LoanOffer, *Eligibility, error) {
	var (
		loanOffers      = make([]*palPb.LoanOffer, 0)
		loanEligibility *Eligibility
	)

	for _, lh := range loanHeaders {
		key := helper.GetRecommendationEngineOffersKey(lh.GetVendor(), lh.GetLoanProgram())
		loes, found := loesMap.Load(key)
		if !found {
			continue
		}
		for _, loe := range loes {
			// collect all available offers
			if loe.IsOfferAvailable {
				loanOffers = append(loanOffers, loe.LoanOffer)
			}
			// set eligibility only if it is not set already
			if loanEligibility == nil && loe.IsEligibilityAvailable {
				eligibilityLoanHeader := loe.EligibilityLoanHeader
				// for non fi core user, eligibility check should be done using a common loan header
				// so that the subsequent RPCs will have the same loan header in the request params
				if isNonFiCoreUser {
					eligibilityLoanHeader = &palPb.LoanHeader{
						LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY,
						Vendor:      palPb.Vendor_EPIFI_TECH,
					}
				}

				loanEligibility = &Eligibility{
					CheckEligibility: loe.IsEligibilityAvailable,
					LoanHeader:       eligibilityLoanHeader,
				}
			}
		}
	}

	// Filter offers to keep only HARD offers from the same vendor if any HARD offer exists
	// Picks the first priority vendor if multiple HARD offer vendors are present in the list
	loanOffers = r.filterHardOffers(loanOffers)

	return loanOffers, loanEligibility, nil
}

func (r *LoanOptionRecommender) checkUserProperty(ctx context.Context, actorId string) (bool, bool, error) {
	userFeatRes, err := helper.GetUserFeatureProperty(ctx, actorId, r.onbClient, r.savingsClient)
	if err != nil {
		return false, false, fmt.Errorf("failed to get user feature property, err: %w", err)
	}

	if userFeatRes.IsFiSAHolder {
		isRiskyUser, riskErr := r.rpcHelper.IsRiskyUser(ctx, actorId)
		if riskErr != nil {
			return false, false, fmt.Errorf("%w. failed to check risk profile for user", riskErr)
		}
		return isRiskyUser, false, nil
	}
	return false, !userFeatRes.IsFiSAHolder, nil
}

// filterHardOffers filters offers to keep only HARD offers from the same vendor if any HARD offer exists
// Picks the first priority vendor if multiple HARD offer vendors are present in the list
func (r *LoanOptionRecommender) filterHardOffers(loanOffers []*palPb.LoanOffer) []*palPb.LoanOffer {
	// First, check if any HARD offer is present, pick the first priority vendor
	hasHardOffer := false
	var hardOfferVendor palPb.Vendor

	for _, offer := range loanOffers {
		if offer.GetLoanOfferType() == palPb.LoanOfferType_LOAN_OFFER_TYPE_HARD {
			hasHardOffer = true
			hardOfferVendor = offer.GetVendor()
			break
		}
	}

	// If a HARD offer exists, filter to keep only HARD offers from the same vendor
	if hasHardOffer {
		var filteredOffers []*palPb.LoanOffer
		for _, offer := range loanOffers {
			if offer.GetLoanOfferType() == palPb.LoanOfferType_LOAN_OFFER_TYPE_HARD && offer.GetVendor() == hardOfferVendor {
				filteredOffers = append(filteredOffers, offer)
			}
		}
		return filteredOffers
	}

	// If no HARD offer, return original list
	return loanOffers
}

// filterHardOfferOptions filters offers to keep only HARD offers from the same vendor if any HARD offer exists
// Picks the first priority vendor if multiple HARD offer vendors are present in the list
func (r *LoanOptionRecommender) filterHardOfferOptions(loanOptions []*palPb.LoanOption) []*palPb.LoanOption {
	// First, check if any HARD offer is present, pick the first priority vendor
	hasHardOffer := false
	var hardOfferVendor palPb.Vendor

	for _, lo := range loanOptions {
		if lo.GetLoanOffer().GetLoanOfferType() == palPb.LoanOfferType_LOAN_OFFER_TYPE_HARD {
			hasHardOffer = true
			hardOfferVendor = lo.GetLoanHeader().GetVendor()
			break
		}
	}

	// If a HARD offer exists, filter to keep only HARD offers from the same vendor
	if hasHardOffer {
		var filteredOptions []*palPb.LoanOption
		for _, lo := range loanOptions {
			if lo.GetLoanOffer().GetLoanOfferType() == palPb.LoanOfferType_LOAN_OFFER_TYPE_HARD && lo.GetLoanHeader().GetVendor() == hardOfferVendor {
				filteredOptions = append(filteredOptions, lo)
			}
		}
		return filteredOptions
	}

	// If no HARD offer, return original list
	return loanOptions
}

func (r *LoanOptionRecommender) GetCombinedRankedLoanOptions(ctx context.Context, req *GetCombinedRankedLoanOptionsRequest) (*GetCombinedRankedLoanOptionsResponse, error) {
	loanOptionList, err := r.getLoanOptionAccToLoanHeaderPriority(ctx, req.ActorId)
	if err != nil {
		logger.Error(ctx, "error in getting loanOptions", zap.Error(err))
		return nil, err
	}
	// Filter offers to keep only HARD offers from the same vendor if any HARD offer exists
	// Picks the first priority vendor if multiple HARD offer vendors are present in the list
	loanOptionList = r.filterHardOfferOptions(loanOptionList)
	// for users coming first time having no offers and no loec returning empty loan Option list
	return &GetCombinedRankedLoanOptionsResponse{
		LoanOptions: loanOptionList,
	}, nil

}

// Helper function to get a consistent ID from LoanOption for tracking
func getLoanOptionID(option *palPb.LoanOption) string {
	switch option.GetLoanOptionType().(type) {
	case *palPb.LoanOption_LoanOffer:
		return option.GetLoanOffer().GetId()
	case *palPb.LoanOption_EligibilityHeader:
		return strings.Join([]string{option.GetEligibilityHeader().GetVendor().String(), option.GetEligibilityHeader().GetLoanProgram().String()}, ":")
	}
	return "" // Return empty if no ID can be found
}

func (r *LoanOptionRecommender) reOrderLoanOptionBasedOnLoanPriority(options []*palPb.LoanOption, isNonFiCore bool) []*palPb.LoanOption {
	loanPriorityList := r.getLoanPriorityList(isNonFiCore)
	finalLoanOptions := make([]*palPb.LoanOption, 0)
	addedOptionIDs := make(map[string]bool) // To track added options by their ID

	// Pass 1: Add options based on priority
	for _, lp := range loanPriorityList {
		for _, option := range options {
			optionID := getLoanOptionID(option)
			if optionID == "" || addedOptionIDs[optionID] { // Skip if no ID or already added
				continue
			}

			matchesPriority := false
			switch option.GetLoanOptionType().(type) {
			case *palPb.LoanOption_LoanOffer:
				if lp.IsOffer && option.GetLoanOffer() != nil &&
					option.GetLoanOffer().GetVendor().String() == lp.Vendor &&
					option.GetLoanOffer().GetLoanProgram().String() == lp.LoanProgram {
					if lp.LoanOfferType == "" || option.GetLoanOffer().GetLoanOfferType().String() == lp.LoanOfferType {
						matchesPriority = true
					}
				}
			case *palPb.LoanOption_EligibilityHeader:
				if lp.IsEligibility && option.GetEligibilityHeader() != nil &&
					option.GetEligibilityHeader().GetVendor().String() == lp.Vendor &&
					option.GetEligibilityHeader().GetLoanProgram().String() == lp.LoanProgram {
					matchesPriority = true
				}
			}

			if matchesPriority {
				finalLoanOptions = append(finalLoanOptions, option)
				addedOptionIDs[optionID] = true
			}
		}
	}

	// Pass 2: Add any remaining options that weren't prioritized but are unique
	for _, option := range options {
		optionID := getLoanOptionID(option)
		if optionID != "" && !addedOptionIDs[optionID] {
			finalLoanOptions = append(finalLoanOptions, option)
			addedOptionIDs[optionID] = true // Mark as added to avoid duplicates from this pass if any
		}
	}

	return finalLoanOptions
}

func (r *LoanOptionRecommender) getLoanPriorityList(isNonFiCore bool) []*common.LoanOptionPriority {
	if isNonFiCore {
		return r.lopeOverrideConfig.LoanPriorityOrderNonFiCore()
	}
	return r.lopeOverrideConfig.LoanPriorityOrderFiCore()
}

// Used to keep a track we don't add repeated LoanOption while filtering
// true mean, a duplicate is found
// false mean, no duplicate is found and we add it in map.
func checkLoanOptionDuplicate(option *palPb.LoanOption, checker map[string]*palPb.LoanOption) bool {
	idToCheck := getLoanOptionID(option)

	if idToCheck == "" {
		return false
	}

	if _, found := checker[idToCheck]; found {
		return true // Duplicate found
	}

	// Not a duplicate, add to checker map
	checker[idToCheck] = option
	return false // Not a duplicate
}

func createLoanOption(res *providers.EvaluateLoanEligibilityResponse, loanOffer *palPb.LoanOffer) *palPb.LoanOption {
	if res.IsLoanOfferAvailable() {
		return &palPb.LoanOption{
			LoanOptionType: &palPb.LoanOption_LoanOffer{LoanOffer: loanOffer},
		}
	}

	return &palPb.LoanOption{
		LoanOptionType: &palPb.LoanOption_EligibilityHeader{
			EligibilityHeader: res.GetEligibilityLoanHeader(),
		},
	}
}

func (r *LoanOptionRecommender) getLoanOptionAccToLoanHeaderPriority(ctx context.Context, actorId string) ([]*palPb.LoanOption, error) {
	offersMap := make(map[string][]*palPb.LoanOffer)

	riskyUser, isNonFiCoreUser, userErr := r.checkUserProperty(ctx, actorId)
	if userErr != nil {
		return nil, userErr
	}
	if riskyUser {
		return nil, nil
	}

	origOwnership := epificontext.OwnershipFromContext(ctx)
	defer func() {
		ctx = epificontext.WithOwnership(ctx, origOwnership)
	}()
	// setting ownership to zero value so that CheckAndGetLoanOffersForActor will check for offers in all DBs
	ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_TECH)
	// fetching all active offers for non risky users from different Dbs and storing in map[vendor:loanProgram]
	offers, offerErr := r.multiDbProvider.CheckAndGetLoanOffersForActor(ctx, &multiDbProvider.CheckAndGetLoanOffersForActorRequest{
		ActorId:      actorId,
		LoanPrograms: helper.GetLoanProgramsForLoanType(palPb.LoanType_LOAN_TYPE_PERSONAL),
	})
	if offerErr != nil && !errors.Is(offerErr, epifierrors.ErrRecordNotFound) {
		return nil, pkgErrors.Wrap(offerErr, "error fetching pl loan offer for actor")
	}
	if offers != nil {
		for _, lo := range offers.Offers {
			key := helper.GetRecommendationEngineOffersKey(lo.GetVendor(), lo.GetLoanProgram())
			offersMap[key] = append(offersMap[key], lo)
		}
	}

	priorityProvider, err := r.priorityProviderFactory.GetPriorityProvider(ctx, &priorityProviderFactory.GetPriorityProviderFactoryRequest{
		IsNonFiCoreUser: isNonFiCoreUser,
	})
	if err != nil {
		return nil, pkgErrors.Wrap(err, "error fetching priority provider factory")
	}

	loanHeaderPrioritisationRequest := pb.GetLoanHeaderPrioritisationRequest{
		ShouldCheckAvailability: true,
		ActorId:                 actorId,
	}
	loanHeaders, err := priorityProvider.GetLoanHeaderPrioritisation(ctx, loanHeaderPrioritisationRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to get loan header prioritisation error : %w", err)
	}

	combinedLoanOptions := make([]*palPb.LoanOption, 0)
	loanOptionDedupeChecker := make(map[string]*palPb.LoanOption)
	for _, lh := range loanHeaders {

		isDowntime := r.vendorProgramConfig.DownTimeConfig().Get(lh.GetVendor().String()).IsDownNow()
		if isDowntime {
			continue
		}

		provider, elErr := r.eligibilityEvaluatorFactory.GetEligibilityEvaluatorProvider(lh, isNonFiCoreUser)
		if elErr != nil {
			return nil, elErr
		}

		key := helper.GetRecommendationEngineOffersKey(lh.GetVendor(), lh.GetLoanProgram())

		if los, found := offersMap[key]; found {
			for _, lo := range los {
				loeRes, loeErr := provider.EvaluateLoanEligibility(ctx, &providers.EvaluateLoanEligibilityRequest{
					LoanOffer:       lo,
					ActorId:         actorId,
					IsNonFiCoreUser: isNonFiCoreUser,
				}, lh)
				if loeErr != nil {
					return nil, loeErr
				}
				if loeRes != nil && (loeRes.IsLoanOfferAvailable() || loeRes.ShouldCheckLoanEligibility()) {
					loanOption := createLoanOption(loeRes, lo)
					if !checkLoanOptionDuplicate(loanOption, loanOptionDedupeChecker) {
						combinedLoanOptions = append(combinedLoanOptions, loanOption)
					}
				}
			}
		} else {
			loeRes, loeErr := provider.EvaluateLoanEligibility(ctx, &providers.EvaluateLoanEligibilityRequest{
				ActorId:         actorId,
				IsNonFiCoreUser: isNonFiCoreUser,
			}, lh)
			if loeErr != nil {
				return nil, loeErr
			}
			if loeRes != nil && loeRes.ShouldCheckLoanEligibility() {
				loanOption := createLoanOption(loeRes, nil)
				if !checkLoanOptionDuplicate(loanOption, loanOptionDedupeChecker) {
					combinedLoanOptions = append(combinedLoanOptions, loanOption)
				}
			}
		}

	}

	newLoanOptionList := r.reOrderLoanOptionBasedOnLoanPriority(combinedLoanOptions, isNonFiCoreUser)
	return newLoanOptionList, nil
}
