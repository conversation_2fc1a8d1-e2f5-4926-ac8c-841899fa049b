// Code generated by MockGen. DO NOT EDIT.
// Source: provider.go

// Package mock_provider is a generated GoMock package.
package mock_provider

import (
	context "context"
	reflect "reflect"

	provider "github.com/epifi/gamma/preapprovedloan/recommendation_engine/provider"
	gomock "github.com/golang/mock/gomock"
)

// MockILoanRecommendationEngine is a mock of ILoanRecommendationEngine interface.
type MockILoanRecommendationEngine struct {
	ctrl     *gomock.Controller
	recorder *MockILoanRecommendationEngineMockRecorder
}

// MockILoanRecommendationEngineMockRecorder is the mock recorder for MockILoanRecommendationEngine.
type MockILoanRecommendationEngineMockRecorder struct {
	mock *MockILoanRecommendationEngine
}

// NewMockILoanRecommendationEngine creates a new mock instance.
func NewMockILoanRecommendationEngine(ctrl *gomock.Controller) *MockILoanRecommendationEngine {
	mock := &MockILoanRecommendationEngine{ctrl: ctrl}
	mock.recorder = &MockILoanRecommendationEngineMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockILoanRecommendationEngine) EXPECT() *MockILoanRecommendationEngineMockRecorder {
	return m.recorder
}

// GetCombinedRankedLoanOptions mocks base method.
func (m *MockILoanRecommendationEngine) GetCombinedRankedLoanOptions(arg0 context.Context, arg1 *provider.GetCombinedRankedLoanOptionsRequest) (*provider.GetCombinedRankedLoanOptionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCombinedRankedLoanOptions", arg0, arg1)
	ret0, _ := ret[0].(*provider.GetCombinedRankedLoanOptionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCombinedRankedLoanOptions indicates an expected call of GetCombinedRankedLoanOptions.
func (mr *MockILoanRecommendationEngineMockRecorder) GetCombinedRankedLoanOptions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCombinedRankedLoanOptions", reflect.TypeOf((*MockILoanRecommendationEngine)(nil).GetCombinedRankedLoanOptions), arg0, arg1)
}

// GetRankedLoanOptions mocks base method.
func (m *MockILoanRecommendationEngine) GetRankedLoanOptions(arg0 context.Context, arg1 *provider.GetRankedLoanOptionsRequest) (*provider.GetRankedLoanOptionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRankedLoanOptions", arg0, arg1)
	ret0, _ := ret[0].(*provider.GetRankedLoanOptionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRankedLoanOptions indicates an expected call of GetRankedLoanOptions.
func (mr *MockILoanRecommendationEngineMockRecorder) GetRankedLoanOptions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRankedLoanOptions", reflect.TypeOf((*MockILoanRecommendationEngine)(nil).GetRankedLoanOptions), arg0, arg1)
}

// GetRecommendedLoanOption mocks base method.
func (m *MockILoanRecommendationEngine) GetRecommendedLoanOption(arg0 context.Context, arg1 *provider.GetRecommendedLoanOptionRequest) (*provider.GetRecommendedLoanOptionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecommendedLoanOption", arg0, arg1)
	ret0, _ := ret[0].(*provider.GetRecommendedLoanOptionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecommendedLoanOption indicates an expected call of GetRecommendedLoanOption.
func (mr *MockILoanRecommendationEngineMockRecorder) GetRecommendedLoanOption(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendedLoanOption", reflect.TypeOf((*MockILoanRecommendationEngine)(nil).GetRecommendedLoanOption), arg0, arg1)
}
