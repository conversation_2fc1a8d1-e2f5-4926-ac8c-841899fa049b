// Code generated by MockGen. DO NOT EDIT.
// Source: dao.go

// Package mock_dao is a generated GoMock package.
package mock_dao

import (
	context "context"
	reflect "reflect"
	time "time"

	rpc "github.com/epifi/be-common/api/rpc"
	pagination "github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	preapprovedloan "github.com/epifi/gamma/api/preapprovedloan"
	enums "github.com/epifi/gamma/api/preapprovedloan/enums"
	gomock "github.com/golang/mock/gomock"
	date "google.golang.org/genproto/googleapis/type/date"
)

// MockLoanOfferEligibilityCriteriaDao is a mock of LoanOfferEligibilityCriteriaDao interface.
type MockLoanOfferEligibilityCriteriaDao struct {
	ctrl     *gomock.Controller
	recorder *MockLoanOfferEligibilityCriteriaDaoMockRecorder
}

// MockLoanOfferEligibilityCriteriaDaoMockRecorder is the mock recorder for MockLoanOfferEligibilityCriteriaDao.
type MockLoanOfferEligibilityCriteriaDaoMockRecorder struct {
	mock *MockLoanOfferEligibilityCriteriaDao
}

// NewMockLoanOfferEligibilityCriteriaDao creates a new mock instance.
func NewMockLoanOfferEligibilityCriteriaDao(ctrl *gomock.Controller) *MockLoanOfferEligibilityCriteriaDao {
	mock := &MockLoanOfferEligibilityCriteriaDao{ctrl: ctrl}
	mock.recorder = &MockLoanOfferEligibilityCriteriaDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoanOfferEligibilityCriteriaDao) EXPECT() *MockLoanOfferEligibilityCriteriaDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockLoanOfferEligibilityCriteriaDao) Create(ctx context.Context, loanOfferEligibility *preapprovedloan.LoanOfferEligibilityCriteria) (*preapprovedloan.LoanOfferEligibilityCriteria, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, loanOfferEligibility)
	ret0, _ := ret[0].(*preapprovedloan.LoanOfferEligibilityCriteria)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockLoanOfferEligibilityCriteriaDaoMockRecorder) Create(ctx, loanOfferEligibility interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockLoanOfferEligibilityCriteriaDao)(nil).Create), ctx, loanOfferEligibility)
}

// CreateBatch mocks base method.
func (m *MockLoanOfferEligibilityCriteriaDao) CreateBatch(ctx context.Context, loanOfferEligibilityList []*preapprovedloan.LoanOfferEligibilityCriteria, batchSize int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBatch", ctx, loanOfferEligibilityList, batchSize)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBatch indicates an expected call of CreateBatch.
func (mr *MockLoanOfferEligibilityCriteriaDaoMockRecorder) CreateBatch(ctx, loanOfferEligibilityList, batchSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBatch", reflect.TypeOf((*MockLoanOfferEligibilityCriteriaDao)(nil).CreateBatch), ctx, loanOfferEligibilityList, batchSize)
}

// GetActiveLoecsByActorIdLoanProgramsAndStatuses mocks base method.
func (m *MockLoanOfferEligibilityCriteriaDao) GetActiveLoecsByActorIdLoanProgramsAndStatuses(ctx context.Context, actorId string, loanPrograms []preapprovedloan.LoanProgram, statuses []preapprovedloan.LoanOfferEligibilityCriteriaStatus, updatedWithin time.Duration, sortByDesc bool) ([]*preapprovedloan.LoanOfferEligibilityCriteria, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveLoecsByActorIdLoanProgramsAndStatuses", ctx, actorId, loanPrograms, statuses, updatedWithin, sortByDesc)
	ret0, _ := ret[0].([]*preapprovedloan.LoanOfferEligibilityCriteria)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveLoecsByActorIdLoanProgramsAndStatuses indicates an expected call of GetActiveLoecsByActorIdLoanProgramsAndStatuses.
func (mr *MockLoanOfferEligibilityCriteriaDaoMockRecorder) GetActiveLoecsByActorIdLoanProgramsAndStatuses(ctx, actorId, loanPrograms, statuses, updatedWithin, sortByDesc interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveLoecsByActorIdLoanProgramsAndStatuses", reflect.TypeOf((*MockLoanOfferEligibilityCriteriaDao)(nil).GetActiveLoecsByActorIdLoanProgramsAndStatuses), ctx, actorId, loanPrograms, statuses, updatedWithin, sortByDesc)
}

// GetByActorIdAndVendorAndStatus mocks base method.
func (m *MockLoanOfferEligibilityCriteriaDao) GetByActorIdAndVendorAndStatus(ctx context.Context, actorId string, vendor preapprovedloan.Vendor, statuses []preapprovedloan.LoanOfferEligibilityCriteriaStatus, sortByDesc bool) ([]*preapprovedloan.LoanOfferEligibilityCriteria, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorIdAndVendorAndStatus", ctx, actorId, vendor, statuses, sortByDesc)
	ret0, _ := ret[0].([]*preapprovedloan.LoanOfferEligibilityCriteria)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdAndVendorAndStatus indicates an expected call of GetByActorIdAndVendorAndStatus.
func (mr *MockLoanOfferEligibilityCriteriaDaoMockRecorder) GetByActorIdAndVendorAndStatus(ctx, actorId, vendor, statuses, sortByDesc interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdAndVendorAndStatus", reflect.TypeOf((*MockLoanOfferEligibilityCriteriaDao)(nil).GetByActorIdAndVendorAndStatus), ctx, actorId, vendor, statuses, sortByDesc)
}

// GetByActorIdLoanProgramsAndStatuses mocks base method.
func (m *MockLoanOfferEligibilityCriteriaDao) GetByActorIdLoanProgramsAndStatuses(ctx context.Context, actorId string, loanPrograms []preapprovedloan.LoanProgram, statuses []preapprovedloan.LoanOfferEligibilityCriteriaStatus, updatedWithin time.Duration, sortByDesc bool) ([]*preapprovedloan.LoanOfferEligibilityCriteria, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorIdLoanProgramsAndStatuses", ctx, actorId, loanPrograms, statuses, updatedWithin, sortByDesc)
	ret0, _ := ret[0].([]*preapprovedloan.LoanOfferEligibilityCriteria)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdLoanProgramsAndStatuses indicates an expected call of GetByActorIdLoanProgramsAndStatuses.
func (mr *MockLoanOfferEligibilityCriteriaDaoMockRecorder) GetByActorIdLoanProgramsAndStatuses(ctx, actorId, loanPrograms, statuses, updatedWithin, sortByDesc interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdLoanProgramsAndStatuses", reflect.TypeOf((*MockLoanOfferEligibilityCriteriaDao)(nil).GetByActorIdLoanProgramsAndStatuses), ctx, actorId, loanPrograms, statuses, updatedWithin, sortByDesc)
}

// GetById mocks base method.
func (m *MockLoanOfferEligibilityCriteriaDao) GetById(ctx context.Context, id string) (*preapprovedloan.LoanOfferEligibilityCriteria, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*preapprovedloan.LoanOfferEligibilityCriteria)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockLoanOfferEligibilityCriteriaDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockLoanOfferEligibilityCriteriaDao)(nil).GetById), ctx, id)
}

// GetByVendorRequestIdAndLoanProgram mocks base method.
func (m *MockLoanOfferEligibilityCriteriaDao) GetByVendorRequestIdAndLoanProgram(ctx context.Context, id string, loanProgram preapprovedloan.LoanProgram) (*preapprovedloan.LoanOfferEligibilityCriteria, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByVendorRequestIdAndLoanProgram", ctx, id, loanProgram)
	ret0, _ := ret[0].(*preapprovedloan.LoanOfferEligibilityCriteria)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByVendorRequestIdAndLoanProgram indicates an expected call of GetByVendorRequestIdAndLoanProgram.
func (mr *MockLoanOfferEligibilityCriteriaDaoMockRecorder) GetByVendorRequestIdAndLoanProgram(ctx, id, loanProgram interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByVendorRequestIdAndLoanProgram", reflect.TypeOf((*MockLoanOfferEligibilityCriteriaDao)(nil).GetByVendorRequestIdAndLoanProgram), ctx, id, loanProgram)
}

// Update mocks base method.
func (m *MockLoanOfferEligibilityCriteriaDao) Update(ctx context.Context, loanOfferEligibility *preapprovedloan.LoanOfferEligibilityCriteria, updateMasks []preapprovedloan.LoanOfferEligibilityCriteriaFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, loanOfferEligibility, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockLoanOfferEligibilityCriteriaDaoMockRecorder) Update(ctx, loanOfferEligibility, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockLoanOfferEligibilityCriteriaDao)(nil).Update), ctx, loanOfferEligibility, updateMasks)
}

// MockLoanRequestsDao is a mock of LoanRequestsDao interface.
type MockLoanRequestsDao struct {
	ctrl     *gomock.Controller
	recorder *MockLoanRequestsDaoMockRecorder
}

// MockLoanRequestsDaoMockRecorder is the mock recorder for MockLoanRequestsDao.
type MockLoanRequestsDaoMockRecorder struct {
	mock *MockLoanRequestsDao
}

// NewMockLoanRequestsDao creates a new mock instance.
func NewMockLoanRequestsDao(ctrl *gomock.Controller) *MockLoanRequestsDao {
	mock := &MockLoanRequestsDao{ctrl: ctrl}
	mock.recorder = &MockLoanRequestsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoanRequestsDao) EXPECT() *MockLoanRequestsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockLoanRequestsDao) Create(ctx context.Context, request *preapprovedloan.LoanRequest) (*preapprovedloan.LoanRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, request)
	ret0, _ := ret[0].(*preapprovedloan.LoanRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockLoanRequestsDaoMockRecorder) Create(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockLoanRequestsDao)(nil).Create), ctx, request)
}

// GetByActorIdAndVendorAndStatus mocks base method.
func (m *MockLoanRequestsDao) GetByActorIdAndVendorAndStatus(ctx context.Context, actorId string, vendor preapprovedloan.Vendor, statuses []preapprovedloan.LoanRequestStatus) ([]*preapprovedloan.LoanRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorIdAndVendorAndStatus", ctx, actorId, vendor, statuses)
	ret0, _ := ret[0].([]*preapprovedloan.LoanRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdAndVendorAndStatus indicates an expected call of GetByActorIdAndVendorAndStatus.
func (mr *MockLoanRequestsDaoMockRecorder) GetByActorIdAndVendorAndStatus(ctx, actorId, vendor, statuses interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdAndVendorAndStatus", reflect.TypeOf((*MockLoanRequestsDao)(nil).GetByActorIdAndVendorAndStatus), ctx, actorId, vendor, statuses)
}

// GetByActorIdTypesStatusAndLoanProgram mocks base method.
func (m *MockLoanRequestsDao) GetByActorIdTypesStatusAndLoanProgram(ctx context.Context, actorId string, types []preapprovedloan.LoanRequestType, statuses []preapprovedloan.LoanRequestStatus, loanPrograms []preapprovedloan.LoanProgram, completedAtWithin, updatedAtWithin *time.Duration) ([]*preapprovedloan.LoanRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorIdTypesStatusAndLoanProgram", ctx, actorId, types, statuses, loanPrograms, completedAtWithin, updatedAtWithin)
	ret0, _ := ret[0].([]*preapprovedloan.LoanRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdTypesStatusAndLoanProgram indicates an expected call of GetByActorIdTypesStatusAndLoanProgram.
func (mr *MockLoanRequestsDaoMockRecorder) GetByActorIdTypesStatusAndLoanProgram(ctx, actorId, types, statuses, loanPrograms, completedAtWithin, updatedAtWithin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdTypesStatusAndLoanProgram", reflect.TypeOf((*MockLoanRequestsDao)(nil).GetByActorIdTypesStatusAndLoanProgram), ctx, actorId, types, statuses, loanPrograms, completedAtWithin, updatedAtWithin)
}

// GetByActorIdVendorStatusAndLoanProgram mocks base method.
func (m *MockLoanRequestsDao) GetByActorIdVendorStatusAndLoanProgram(ctx context.Context, actorId string, vendor preapprovedloan.Vendor, statuses []preapprovedloan.LoanRequestStatus, loanProgram preapprovedloan.LoanProgram) ([]*preapprovedloan.LoanRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorIdVendorStatusAndLoanProgram", ctx, actorId, vendor, statuses, loanProgram)
	ret0, _ := ret[0].([]*preapprovedloan.LoanRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdVendorStatusAndLoanProgram indicates an expected call of GetByActorIdVendorStatusAndLoanProgram.
func (mr *MockLoanRequestsDaoMockRecorder) GetByActorIdVendorStatusAndLoanProgram(ctx, actorId, vendor, statuses, loanProgram interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdVendorStatusAndLoanProgram", reflect.TypeOf((*MockLoanRequestsDao)(nil).GetByActorIdVendorStatusAndLoanProgram), ctx, actorId, vendor, statuses, loanProgram)
}

// GetByClientRequestId mocks base method.
func (m *MockLoanRequestsDao) GetByClientRequestId(ctx context.Context, crId string) (*preapprovedloan.LoanRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByClientRequestId", ctx, crId)
	ret0, _ := ret[0].(*preapprovedloan.LoanRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByClientRequestId indicates an expected call of GetByClientRequestId.
func (mr *MockLoanRequestsDaoMockRecorder) GetByClientRequestId(ctx, crId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByClientRequestId", reflect.TypeOf((*MockLoanRequestsDao)(nil).GetByClientRequestId), ctx, crId)
}

// GetById mocks base method.
func (m *MockLoanRequestsDao) GetById(ctx context.Context, id string) (*preapprovedloan.LoanRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*preapprovedloan.LoanRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockLoanRequestsDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockLoanRequestsDao)(nil).GetById), ctx, id)
}

// GetByLoanAccountIdAndType mocks base method.
func (m *MockLoanRequestsDao) GetByLoanAccountIdAndType(ctx context.Context, loanAccountId string, loanReqType preapprovedloan.LoanRequestType) ([]*preapprovedloan.LoanRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByLoanAccountIdAndType", ctx, loanAccountId, loanReqType)
	ret0, _ := ret[0].([]*preapprovedloan.LoanRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByLoanAccountIdAndType indicates an expected call of GetByLoanAccountIdAndType.
func (mr *MockLoanRequestsDaoMockRecorder) GetByLoanAccountIdAndType(ctx, loanAccountId, loanReqType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByLoanAccountIdAndType", reflect.TypeOf((*MockLoanRequestsDao)(nil).GetByLoanAccountIdAndType), ctx, loanAccountId, loanReqType)
}

// GetByOrchId mocks base method.
func (m *MockLoanRequestsDao) GetByOrchId(ctx context.Context, orchId string) (*preapprovedloan.LoanRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByOrchId", ctx, orchId)
	ret0, _ := ret[0].(*preapprovedloan.LoanRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByOrchId indicates an expected call of GetByOrchId.
func (mr *MockLoanRequestsDaoMockRecorder) GetByOrchId(ctx, orchId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByOrchId", reflect.TypeOf((*MockLoanRequestsDao)(nil).GetByOrchId), ctx, orchId)
}

// GetByVendorReqId mocks base method.
func (m *MockLoanRequestsDao) GetByVendorReqId(ctx context.Context, vendorReqId string) (*preapprovedloan.LoanRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByVendorReqId", ctx, vendorReqId)
	ret0, _ := ret[0].(*preapprovedloan.LoanRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByVendorReqId indicates an expected call of GetByVendorReqId.
func (mr *MockLoanRequestsDaoMockRecorder) GetByVendorReqId(ctx, vendorReqId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByVendorReqId", reflect.TypeOf((*MockLoanRequestsDao)(nil).GetByVendorReqId), ctx, vendorReqId)
}

// GetNonTerminalByActorId mocks base method.
func (m *MockLoanRequestsDao) GetNonTerminalByActorId(ctx context.Context, actorId string, filterOptions ...storagev2.FilterOption) ([]*preapprovedloan.LoanRequest, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNonTerminalByActorId", varargs...)
	ret0, _ := ret[0].([]*preapprovedloan.LoanRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNonTerminalByActorId indicates an expected call of GetNonTerminalByActorId.
func (mr *MockLoanRequestsDaoMockRecorder) GetNonTerminalByActorId(ctx, actorId interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNonTerminalByActorId", reflect.TypeOf((*MockLoanRequestsDao)(nil).GetNonTerminalByActorId), varargs...)
}

// GetNonTerminalByActorIdAndVendor mocks base method.
func (m *MockLoanRequestsDao) GetNonTerminalByActorIdAndVendor(ctx context.Context, actorId string, vendor preapprovedloan.Vendor, filterOptions ...storagev2.FilterOption) (*preapprovedloan.LoanRequest, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, vendor}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNonTerminalByActorIdAndVendor", varargs...)
	ret0, _ := ret[0].(*preapprovedloan.LoanRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNonTerminalByActorIdAndVendor indicates an expected call of GetNonTerminalByActorIdAndVendor.
func (mr *MockLoanRequestsDaoMockRecorder) GetNonTerminalByActorIdAndVendor(ctx, actorId, vendor interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, vendor}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNonTerminalByActorIdAndVendor", reflect.TypeOf((*MockLoanRequestsDao)(nil).GetNonTerminalByActorIdAndVendor), varargs...)
}

// GetNonTerminalByActorIdVendorAndLoanProgram mocks base method.
func (m *MockLoanRequestsDao) GetNonTerminalByActorIdVendorAndLoanProgram(ctx context.Context, actorId string, vendor preapprovedloan.Vendor, loanProgram preapprovedloan.LoanProgram, filterOptions ...storagev2.FilterOption) (*preapprovedloan.LoanRequest, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, vendor, loanProgram}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNonTerminalByActorIdVendorAndLoanProgram", varargs...)
	ret0, _ := ret[0].(*preapprovedloan.LoanRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNonTerminalByActorIdVendorAndLoanProgram indicates an expected call of GetNonTerminalByActorIdVendorAndLoanProgram.
func (mr *MockLoanRequestsDaoMockRecorder) GetNonTerminalByActorIdVendorAndLoanProgram(ctx, actorId, vendor, loanProgram interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, vendor, loanProgram}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNonTerminalByActorIdVendorAndLoanProgram", reflect.TypeOf((*MockLoanRequestsDao)(nil).GetNonTerminalByActorIdVendorAndLoanProgram), varargs...)
}

// GetTerminalByActorIdAndVendor mocks base method.
func (m *MockLoanRequestsDao) GetTerminalByActorIdAndVendor(ctx context.Context, actorId string, vendor preapprovedloan.Vendor, filterOptions ...storagev2.FilterOption) ([]*preapprovedloan.LoanRequest, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, vendor}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTerminalByActorIdAndVendor", varargs...)
	ret0, _ := ret[0].([]*preapprovedloan.LoanRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTerminalByActorIdAndVendor indicates an expected call of GetTerminalByActorIdAndVendor.
func (mr *MockLoanRequestsDaoMockRecorder) GetTerminalByActorIdAndVendor(ctx, actorId, vendor interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, vendor}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTerminalByActorIdAndVendor", reflect.TypeOf((*MockLoanRequestsDao)(nil).GetTerminalByActorIdAndVendor), varargs...)
}

// Update mocks base method.
func (m *MockLoanRequestsDao) Update(ctx context.Context, request *preapprovedloan.LoanRequest, updateMasks []preapprovedloan.LoanRequestFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, request, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockLoanRequestsDaoMockRecorder) Update(ctx, request, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockLoanRequestsDao)(nil).Update), ctx, request, updateMasks)
}

// MockLoanOffersDao is a mock of LoanOffersDao interface.
type MockLoanOffersDao struct {
	ctrl     *gomock.Controller
	recorder *MockLoanOffersDaoMockRecorder
}

// MockLoanOffersDaoMockRecorder is the mock recorder for MockLoanOffersDao.
type MockLoanOffersDaoMockRecorder struct {
	mock *MockLoanOffersDao
}

// NewMockLoanOffersDao creates a new mock instance.
func NewMockLoanOffersDao(ctrl *gomock.Controller) *MockLoanOffersDao {
	mock := &MockLoanOffersDao{ctrl: ctrl}
	mock.recorder = &MockLoanOffersDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoanOffersDao) EXPECT() *MockLoanOffersDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockLoanOffersDao) Create(ctx context.Context, loanOffer *preapprovedloan.LoanOffer) (*preapprovedloan.LoanOffer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, loanOffer)
	ret0, _ := ret[0].(*preapprovedloan.LoanOffer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockLoanOffersDaoMockRecorder) Create(ctx, loanOffer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockLoanOffersDao)(nil).Create), ctx, loanOffer)
}

// DeactivateLoanOffer mocks base method.
func (m *MockLoanOffersDao) DeactivateLoanOffer(ctx context.Context, loanOfferId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeactivateLoanOffer", ctx, loanOfferId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeactivateLoanOffer indicates an expected call of DeactivateLoanOffer.
func (mr *MockLoanOffersDaoMockRecorder) DeactivateLoanOffer(ctx, loanOfferId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeactivateLoanOffer", reflect.TypeOf((*MockLoanOffersDao)(nil).DeactivateLoanOffer), ctx, loanOfferId)
}

// GetActiveOfferByActorIdAndVendor mocks base method.
func (m *MockLoanOffersDao) GetActiveOfferByActorIdAndVendor(ctx context.Context, actorId string, vendor preapprovedloan.Vendor) (*preapprovedloan.LoanOffer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveOfferByActorIdAndVendor", ctx, actorId, vendor)
	ret0, _ := ret[0].(*preapprovedloan.LoanOffer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveOfferByActorIdAndVendor indicates an expected call of GetActiveOfferByActorIdAndVendor.
func (mr *MockLoanOffersDaoMockRecorder) GetActiveOfferByActorIdAndVendor(ctx, actorId, vendor interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveOfferByActorIdAndVendor", reflect.TypeOf((*MockLoanOffersDao)(nil).GetActiveOfferByActorIdAndVendor), ctx, actorId, vendor)
}

// GetActiveOfferByActorIdVendorAndLoanProgram mocks base method.
func (m *MockLoanOffersDao) GetActiveOfferByActorIdVendorAndLoanProgram(ctx context.Context, actorId string, vendor preapprovedloan.Vendor, loanProgram preapprovedloan.LoanProgram) (*preapprovedloan.LoanOffer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveOfferByActorIdVendorAndLoanProgram", ctx, actorId, vendor, loanProgram)
	ret0, _ := ret[0].(*preapprovedloan.LoanOffer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveOfferByActorIdVendorAndLoanProgram indicates an expected call of GetActiveOfferByActorIdVendorAndLoanProgram.
func (mr *MockLoanOffersDaoMockRecorder) GetActiveOfferByActorIdVendorAndLoanProgram(ctx, actorId, vendor, loanProgram interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveOfferByActorIdVendorAndLoanProgram", reflect.TypeOf((*MockLoanOffersDao)(nil).GetActiveOfferByActorIdVendorAndLoanProgram), ctx, actorId, vendor, loanProgram)
}

// GetActiveOffersByActorIdAndLoanPrograms mocks base method.
func (m *MockLoanOffersDao) GetActiveOffersByActorIdAndLoanPrograms(ctx context.Context, actorId string, loanPrograms []preapprovedloan.LoanProgram) ([]*preapprovedloan.LoanOffer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveOffersByActorIdAndLoanPrograms", ctx, actorId, loanPrograms)
	ret0, _ := ret[0].([]*preapprovedloan.LoanOffer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveOffersByActorIdAndLoanPrograms indicates an expected call of GetActiveOffersByActorIdAndLoanPrograms.
func (mr *MockLoanOffersDaoMockRecorder) GetActiveOffersByActorIdAndLoanPrograms(ctx, actorId, loanPrograms interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveOffersByActorIdAndLoanPrograms", reflect.TypeOf((*MockLoanOffersDao)(nil).GetActiveOffersByActorIdAndLoanPrograms), ctx, actorId, loanPrograms)
}

// GetById mocks base method.
func (m *MockLoanOffersDao) GetById(ctx context.Context, id string) (*preapprovedloan.LoanOffer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*preapprovedloan.LoanOffer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockLoanOffersDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockLoanOffersDao)(nil).GetById), ctx, id)
}

// GetByOfferEligibilityCriteriaId mocks base method.
func (m *MockLoanOffersDao) GetByOfferEligibilityCriteriaId(ctx context.Context, loecId string, count int, orderByDesc bool) ([]*preapprovedloan.LoanOffer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByOfferEligibilityCriteriaId", ctx, loecId, count, orderByDesc)
	ret0, _ := ret[0].([]*preapprovedloan.LoanOffer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByOfferEligibilityCriteriaId indicates an expected call of GetByOfferEligibilityCriteriaId.
func (mr *MockLoanOffersDaoMockRecorder) GetByOfferEligibilityCriteriaId(ctx, loecId, count, orderByDesc interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByOfferEligibilityCriteriaId", reflect.TypeOf((*MockLoanOffersDao)(nil).GetByOfferEligibilityCriteriaId), ctx, loecId, count, orderByDesc)
}

// GetByVendorAndVendorOfferId mocks base method.
func (m *MockLoanOffersDao) GetByVendorAndVendorOfferId(ctx context.Context, vendor preapprovedloan.Vendor, vendorOfferId string) (*preapprovedloan.LoanOffer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByVendorAndVendorOfferId", ctx, vendor, vendorOfferId)
	ret0, _ := ret[0].(*preapprovedloan.LoanOffer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByVendorAndVendorOfferId indicates an expected call of GetByVendorAndVendorOfferId.
func (mr *MockLoanOffersDaoMockRecorder) GetByVendorAndVendorOfferId(ctx, vendor, vendorOfferId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByVendorAndVendorOfferId", reflect.TypeOf((*MockLoanOffersDao)(nil).GetByVendorAndVendorOfferId), ctx, vendor, vendorOfferId)
}

// GetLatestByActorIdAndVendor mocks base method.
func (m *MockLoanOffersDao) GetLatestByActorIdAndVendor(ctx context.Context, actorId string, vendor preapprovedloan.Vendor) (*preapprovedloan.LoanOffer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestByActorIdAndVendor", ctx, actorId, vendor)
	ret0, _ := ret[0].(*preapprovedloan.LoanOffer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestByActorIdAndVendor indicates an expected call of GetLatestByActorIdAndVendor.
func (mr *MockLoanOffersDaoMockRecorder) GetLatestByActorIdAndVendor(ctx, actorId, vendor interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestByActorIdAndVendor", reflect.TypeOf((*MockLoanOffersDao)(nil).GetLatestByActorIdAndVendor), ctx, actorId, vendor)
}

// GetLatestByActorIdVendorAndLoanProgram mocks base method.
func (m *MockLoanOffersDao) GetLatestByActorIdVendorAndLoanProgram(ctx context.Context, actorId string, vendor preapprovedloan.Vendor, loanProgram preapprovedloan.LoanProgram) (*preapprovedloan.LoanOffer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestByActorIdVendorAndLoanProgram", ctx, actorId, vendor, loanProgram)
	ret0, _ := ret[0].(*preapprovedloan.LoanOffer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestByActorIdVendorAndLoanProgram indicates an expected call of GetLatestByActorIdVendorAndLoanProgram.
func (mr *MockLoanOffersDaoMockRecorder) GetLatestByActorIdVendorAndLoanProgram(ctx, actorId, vendor, loanProgram interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestByActorIdVendorAndLoanProgram", reflect.TypeOf((*MockLoanOffersDao)(nil).GetLatestByActorIdVendorAndLoanProgram), ctx, actorId, vendor, loanProgram)
}

// Update mocks base method.
func (m *MockLoanOffersDao) Update(ctx context.Context, loanOffer *preapprovedloan.LoanOffer, updateMasks []preapprovedloan.LoanOfferFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, loanOffer, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockLoanOffersDaoMockRecorder) Update(ctx, loanOffer, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockLoanOffersDao)(nil).Update), ctx, loanOffer, updateMasks)
}

// MockLoanAccountsDao is a mock of LoanAccountsDao interface.
type MockLoanAccountsDao struct {
	ctrl     *gomock.Controller
	recorder *MockLoanAccountsDaoMockRecorder
}

// MockLoanAccountsDaoMockRecorder is the mock recorder for MockLoanAccountsDao.
type MockLoanAccountsDaoMockRecorder struct {
	mock *MockLoanAccountsDao
}

// NewMockLoanAccountsDao creates a new mock instance.
func NewMockLoanAccountsDao(ctrl *gomock.Controller) *MockLoanAccountsDao {
	mock := &MockLoanAccountsDao{ctrl: ctrl}
	mock.recorder = &MockLoanAccountsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoanAccountsDao) EXPECT() *MockLoanAccountsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockLoanAccountsDao) Create(ctx context.Context, account *preapprovedloan.LoanAccount) (*preapprovedloan.LoanAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, account)
	ret0, _ := ret[0].(*preapprovedloan.LoanAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockLoanAccountsDaoMockRecorder) Create(ctx, account interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockLoanAccountsDao)(nil).Create), ctx, account)
}

// GetActiveAccountIdsByLoanProgramAndInstDueDateRange mocks base method.
func (m *MockLoanAccountsDao) GetActiveAccountIdsByLoanProgramAndInstDueDateRange(ctx context.Context, startDate, endDate *date.Date, loanProgram preapprovedloan.LoanProgram, filterOptions ...storagev2.FilterOption) ([]string, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, startDate, endDate, loanProgram}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetActiveAccountIdsByLoanProgramAndInstDueDateRange", varargs...)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveAccountIdsByLoanProgramAndInstDueDateRange indicates an expected call of GetActiveAccountIdsByLoanProgramAndInstDueDateRange.
func (mr *MockLoanAccountsDaoMockRecorder) GetActiveAccountIdsByLoanProgramAndInstDueDateRange(ctx, startDate, endDate, loanProgram interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, startDate, endDate, loanProgram}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveAccountIdsByLoanProgramAndInstDueDateRange", reflect.TypeOf((*MockLoanAccountsDao)(nil).GetActiveAccountIdsByLoanProgramAndInstDueDateRange), varargs...)
}

// GetActiveLoanAccountsByActorIdAndVendor mocks base method.
func (m *MockLoanAccountsDao) GetActiveLoanAccountsByActorIdAndVendor(ctx context.Context, actorId string, vendor preapprovedloan.Vendor) ([]*preapprovedloan.LoanAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveLoanAccountsByActorIdAndVendor", ctx, actorId, vendor)
	ret0, _ := ret[0].([]*preapprovedloan.LoanAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveLoanAccountsByActorIdAndVendor indicates an expected call of GetActiveLoanAccountsByActorIdAndVendor.
func (mr *MockLoanAccountsDaoMockRecorder) GetActiveLoanAccountsByActorIdAndVendor(ctx, actorId, vendor interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveLoanAccountsByActorIdAndVendor", reflect.TypeOf((*MockLoanAccountsDao)(nil).GetActiveLoanAccountsByActorIdAndVendor), ctx, actorId, vendor)
}

// GetByAccountNumberAndIfsc mocks base method.
func (m *MockLoanAccountsDao) GetByAccountNumberAndIfsc(ctx context.Context, loanAccountNumber, ifsc string) (*preapprovedloan.LoanAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAccountNumberAndIfsc", ctx, loanAccountNumber, ifsc)
	ret0, _ := ret[0].(*preapprovedloan.LoanAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAccountNumberAndIfsc indicates an expected call of GetByAccountNumberAndIfsc.
func (mr *MockLoanAccountsDaoMockRecorder) GetByAccountNumberAndIfsc(ctx, loanAccountNumber, ifsc interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAccountNumberAndIfsc", reflect.TypeOf((*MockLoanAccountsDao)(nil).GetByAccountNumberAndIfsc), ctx, loanAccountNumber, ifsc)
}

// GetByAccountNumberAndVendor mocks base method.
func (m *MockLoanAccountsDao) GetByAccountNumberAndVendor(ctx context.Context, loanAccountNumber string, vendor preapprovedloan.Vendor) (*preapprovedloan.LoanAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAccountNumberAndVendor", ctx, loanAccountNumber, vendor)
	ret0, _ := ret[0].(*preapprovedloan.LoanAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAccountNumberAndVendor indicates an expected call of GetByAccountNumberAndVendor.
func (mr *MockLoanAccountsDaoMockRecorder) GetByAccountNumberAndVendor(ctx, loanAccountNumber, vendor interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAccountNumberAndVendor", reflect.TypeOf((*MockLoanAccountsDao)(nil).GetByAccountNumberAndVendor), ctx, loanAccountNumber, vendor)
}

// GetByAccountType mocks base method.
func (m *MockLoanAccountsDao) GetByAccountType(ctx context.Context, accountType preapprovedloan.LoanType) ([]*preapprovedloan.LoanAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAccountType", ctx, accountType)
	ret0, _ := ret[0].([]*preapprovedloan.LoanAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAccountType indicates an expected call of GetByAccountType.
func (mr *MockLoanAccountsDaoMockRecorder) GetByAccountType(ctx, accountType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAccountType", reflect.TypeOf((*MockLoanAccountsDao)(nil).GetByAccountType), ctx, accountType)
}

// GetByActorIdAndVendor mocks base method.
func (m *MockLoanAccountsDao) GetByActorIdAndVendor(ctx context.Context, actorId string, vendor preapprovedloan.Vendor) ([]*preapprovedloan.LoanAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorIdAndVendor", ctx, actorId, vendor)
	ret0, _ := ret[0].([]*preapprovedloan.LoanAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdAndVendor indicates an expected call of GetByActorIdAndVendor.
func (mr *MockLoanAccountsDaoMockRecorder) GetByActorIdAndVendor(ctx, actorId, vendor interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdAndVendor", reflect.TypeOf((*MockLoanAccountsDao)(nil).GetByActorIdAndVendor), ctx, actorId, vendor)
}

// GetByActorIdStatusAndLoanProgram mocks base method.
func (m *MockLoanAccountsDao) GetByActorIdStatusAndLoanProgram(ctx context.Context, actorId string, accountStatus []preapprovedloan.LoanAccountStatus, loanPrograms []preapprovedloan.LoanProgram) ([]*preapprovedloan.LoanAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorIdStatusAndLoanProgram", ctx, actorId, accountStatus, loanPrograms)
	ret0, _ := ret[0].([]*preapprovedloan.LoanAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdStatusAndLoanProgram indicates an expected call of GetByActorIdStatusAndLoanProgram.
func (mr *MockLoanAccountsDaoMockRecorder) GetByActorIdStatusAndLoanProgram(ctx, actorId, accountStatus, loanPrograms interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdStatusAndLoanProgram", reflect.TypeOf((*MockLoanAccountsDao)(nil).GetByActorIdStatusAndLoanProgram), ctx, actorId, accountStatus, loanPrograms)
}

// GetByActorIdVendorAndLoanProgram mocks base method.
func (m *MockLoanAccountsDao) GetByActorIdVendorAndLoanProgram(ctx context.Context, actorId string, vendor preapprovedloan.Vendor, loanProgram preapprovedloan.LoanProgram) ([]*preapprovedloan.LoanAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorIdVendorAndLoanProgram", ctx, actorId, vendor, loanProgram)
	ret0, _ := ret[0].([]*preapprovedloan.LoanAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdVendorAndLoanProgram indicates an expected call of GetByActorIdVendorAndLoanProgram.
func (mr *MockLoanAccountsDaoMockRecorder) GetByActorIdVendorAndLoanProgram(ctx, actorId, vendor, loanProgram interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdVendorAndLoanProgram", reflect.TypeOf((*MockLoanAccountsDao)(nil).GetByActorIdVendorAndLoanProgram), ctx, actorId, vendor, loanProgram)
}

// GetById mocks base method.
func (m *MockLoanAccountsDao) GetById(ctx context.Context, id string) (*preapprovedloan.LoanAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*preapprovedloan.LoanAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockLoanAccountsDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockLoanAccountsDao)(nil).GetById), ctx, id)
}

// GetByIds mocks base method.
func (m *MockLoanAccountsDao) GetByIds(ctx context.Context, ids []string) ([]*preapprovedloan.LoanAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIds", ctx, ids)
	ret0, _ := ret[0].([]*preapprovedloan.LoanAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIds indicates an expected call of GetByIds.
func (mr *MockLoanAccountsDaoMockRecorder) GetByIds(ctx, ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIds", reflect.TypeOf((*MockLoanAccountsDao)(nil).GetByIds), ctx, ids)
}

// GetIdsByPartnerLms mocks base method.
func (m *MockLoanAccountsDao) GetIdsByPartnerLms(ctx context.Context, lmsPartner enums.LmsPartner, filterOptions ...storagev2.FilterOption) ([]string, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, lmsPartner}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIdsByPartnerLms", varargs...)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIdsByPartnerLms indicates an expected call of GetIdsByPartnerLms.
func (mr *MockLoanAccountsDaoMockRecorder) GetIdsByPartnerLms(ctx, lmsPartner interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, lmsPartner}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIdsByPartnerLms", reflect.TypeOf((*MockLoanAccountsDao)(nil).GetIdsByPartnerLms), varargs...)
}

// GetLoanAccountByPartnerLmsAndPartnerLmsLoanId mocks base method.
func (m *MockLoanAccountsDao) GetLoanAccountByPartnerLmsAndPartnerLmsLoanId(ctx context.Context, lmsPartner enums.LmsPartner, partnerLMSloanId string) (*preapprovedloan.LoanAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoanAccountByPartnerLmsAndPartnerLmsLoanId", ctx, lmsPartner, partnerLMSloanId)
	ret0, _ := ret[0].(*preapprovedloan.LoanAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanAccountByPartnerLmsAndPartnerLmsLoanId indicates an expected call of GetLoanAccountByPartnerLmsAndPartnerLmsLoanId.
func (mr *MockLoanAccountsDaoMockRecorder) GetLoanAccountByPartnerLmsAndPartnerLmsLoanId(ctx, lmsPartner, partnerLMSloanId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanAccountByPartnerLmsAndPartnerLmsLoanId", reflect.TypeOf((*MockLoanAccountsDao)(nil).GetLoanAccountByPartnerLmsAndPartnerLmsLoanId), ctx, lmsPartner, partnerLMSloanId)
}

// GetOrCreate mocks base method.
func (m *MockLoanAccountsDao) GetOrCreate(ctx context.Context, account *preapprovedloan.LoanAccount) (*preapprovedloan.LoanAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrCreate", ctx, account)
	ret0, _ := ret[0].(*preapprovedloan.LoanAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrCreate indicates an expected call of GetOrCreate.
func (mr *MockLoanAccountsDaoMockRecorder) GetOrCreate(ctx, account interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrCreate", reflect.TypeOf((*MockLoanAccountsDao)(nil).GetOrCreate), ctx, account)
}

// Update mocks base method.
func (m *MockLoanAccountsDao) Update(ctx context.Context, account *preapprovedloan.LoanAccount, updateMasks []preapprovedloan.LoanAccountFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, account, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockLoanAccountsDaoMockRecorder) Update(ctx, account, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockLoanAccountsDao)(nil).Update), ctx, account, updateMasks)
}

// MockLoanStepExecutionsDao is a mock of LoanStepExecutionsDao interface.
type MockLoanStepExecutionsDao struct {
	ctrl     *gomock.Controller
	recorder *MockLoanStepExecutionsDaoMockRecorder
}

// MockLoanStepExecutionsDaoMockRecorder is the mock recorder for MockLoanStepExecutionsDao.
type MockLoanStepExecutionsDaoMockRecorder struct {
	mock *MockLoanStepExecutionsDao
}

// NewMockLoanStepExecutionsDao creates a new mock instance.
func NewMockLoanStepExecutionsDao(ctrl *gomock.Controller) *MockLoanStepExecutionsDao {
	mock := &MockLoanStepExecutionsDao{ctrl: ctrl}
	mock.recorder = &MockLoanStepExecutionsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoanStepExecutionsDao) EXPECT() *MockLoanStepExecutionsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockLoanStepExecutionsDao) Create(ctx context.Context, stepExecution *preapprovedloan.LoanStepExecution) (*preapprovedloan.LoanStepExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, stepExecution)
	ret0, _ := ret[0].(*preapprovedloan.LoanStepExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockLoanStepExecutionsDaoMockRecorder) Create(ctx, stepExecution interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockLoanStepExecutionsDao)(nil).Create), ctx, stepExecution)
}

// GetActiveByRefIdFlowAndName mocks base method.
func (m *MockLoanStepExecutionsDao) GetActiveByRefIdFlowAndName(ctx context.Context, refId string, flow preapprovedloan.LoanStepExecutionFlow, name preapprovedloan.LoanStepExecutionStepName) (*preapprovedloan.LoanStepExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveByRefIdFlowAndName", ctx, refId, flow, name)
	ret0, _ := ret[0].(*preapprovedloan.LoanStepExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveByRefIdFlowAndName indicates an expected call of GetActiveByRefIdFlowAndName.
func (mr *MockLoanStepExecutionsDaoMockRecorder) GetActiveByRefIdFlowAndName(ctx, refId, flow, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveByRefIdFlowAndName", reflect.TypeOf((*MockLoanStepExecutionsDao)(nil).GetActiveByRefIdFlowAndName), ctx, refId, flow, name)
}

// GetByActorId mocks base method.
func (m *MockLoanStepExecutionsDao) GetByActorId(ctx context.Context, actorId string) ([]*preapprovedloan.LoanStepExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", ctx, actorId)
	ret0, _ := ret[0].([]*preapprovedloan.LoanStepExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockLoanStepExecutionsDaoMockRecorder) GetByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockLoanStepExecutionsDao)(nil).GetByActorId), ctx, actorId)
}

// GetById mocks base method.
func (m *MockLoanStepExecutionsDao) GetById(ctx context.Context, id string, filterOptions ...storagev2.FilterOption) (*preapprovedloan.LoanStepExecution, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, id}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetById", varargs...)
	ret0, _ := ret[0].(*preapprovedloan.LoanStepExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockLoanStepExecutionsDaoMockRecorder) GetById(ctx, id interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, id}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockLoanStepExecutionsDao)(nil).GetById), varargs...)
}

// GetByOrchId mocks base method.
func (m *MockLoanStepExecutionsDao) GetByOrchId(ctx context.Context, orchId string) (*preapprovedloan.LoanStepExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByOrchId", ctx, orchId)
	ret0, _ := ret[0].(*preapprovedloan.LoanStepExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByOrchId indicates an expected call of GetByOrchId.
func (mr *MockLoanStepExecutionsDaoMockRecorder) GetByOrchId(ctx, orchId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByOrchId", reflect.TypeOf((*MockLoanStepExecutionsDao)(nil).GetByOrchId), ctx, orchId)
}

// GetByRefIdAndFlowAndName mocks base method.
func (m *MockLoanStepExecutionsDao) GetByRefIdAndFlowAndName(ctx context.Context, refId string, flow preapprovedloan.LoanStepExecutionFlow, name preapprovedloan.LoanStepExecutionStepName) (*preapprovedloan.LoanStepExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByRefIdAndFlowAndName", ctx, refId, flow, name)
	ret0, _ := ret[0].(*preapprovedloan.LoanStepExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRefIdAndFlowAndName indicates an expected call of GetByRefIdAndFlowAndName.
func (mr *MockLoanStepExecutionsDaoMockRecorder) GetByRefIdAndFlowAndName(ctx, refId, flow, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRefIdAndFlowAndName", reflect.TypeOf((*MockLoanStepExecutionsDao)(nil).GetByRefIdAndFlowAndName), ctx, refId, flow, name)
}

// GetByRefIdAndStatuses mocks base method.
func (m *MockLoanStepExecutionsDao) GetByRefIdAndStatuses(ctx context.Context, refId string, statuses []preapprovedloan.LoanStepExecutionStatus) ([]*preapprovedloan.LoanStepExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByRefIdAndStatuses", ctx, refId, statuses)
	ret0, _ := ret[0].([]*preapprovedloan.LoanStepExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRefIdAndStatuses indicates an expected call of GetByRefIdAndStatuses.
func (mr *MockLoanStepExecutionsDaoMockRecorder) GetByRefIdAndStatuses(ctx, refId, statuses interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRefIdAndStatuses", reflect.TypeOf((*MockLoanStepExecutionsDao)(nil).GetByRefIdAndStatuses), ctx, refId, statuses)
}

// GetLatestByRefIdAndFlow mocks base method.
func (m *MockLoanStepExecutionsDao) GetLatestByRefIdAndFlow(ctx context.Context, refId string, flow preapprovedloan.LoanStepExecutionFlow) (*preapprovedloan.LoanStepExecution, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestByRefIdAndFlow", ctx, refId, flow)
	ret0, _ := ret[0].(*preapprovedloan.LoanStepExecution)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestByRefIdAndFlow indicates an expected call of GetLatestByRefIdAndFlow.
func (mr *MockLoanStepExecutionsDaoMockRecorder) GetLatestByRefIdAndFlow(ctx, refId, flow interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestByRefIdAndFlow", reflect.TypeOf((*MockLoanStepExecutionsDao)(nil).GetLatestByRefIdAndFlow), ctx, refId, flow)
}

// Update mocks base method.
func (m *MockLoanStepExecutionsDao) Update(ctx context.Context, stepExecution *preapprovedloan.LoanStepExecution, updateMasks []preapprovedloan.LoanStepExecutionFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, stepExecution, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockLoanStepExecutionsDaoMockRecorder) Update(ctx, stepExecution, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockLoanStepExecutionsDao)(nil).Update), ctx, stepExecution, updateMasks)
}

// MockLoanInstallmentInfoDao is a mock of LoanInstallmentInfoDao interface.
type MockLoanInstallmentInfoDao struct {
	ctrl     *gomock.Controller
	recorder *MockLoanInstallmentInfoDaoMockRecorder
}

// MockLoanInstallmentInfoDaoMockRecorder is the mock recorder for MockLoanInstallmentInfoDao.
type MockLoanInstallmentInfoDaoMockRecorder struct {
	mock *MockLoanInstallmentInfoDao
}

// NewMockLoanInstallmentInfoDao creates a new mock instance.
func NewMockLoanInstallmentInfoDao(ctrl *gomock.Controller) *MockLoanInstallmentInfoDao {
	mock := &MockLoanInstallmentInfoDao{ctrl: ctrl}
	mock.recorder = &MockLoanInstallmentInfoDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoanInstallmentInfoDao) EXPECT() *MockLoanInstallmentInfoDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockLoanInstallmentInfoDao) Create(ctx context.Context, installmentInfo *preapprovedloan.LoanInstallmentInfo) (*preapprovedloan.LoanInstallmentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, installmentInfo)
	ret0, _ := ret[0].(*preapprovedloan.LoanInstallmentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockLoanInstallmentInfoDaoMockRecorder) Create(ctx, installmentInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockLoanInstallmentInfoDao)(nil).Create), ctx, installmentInfo)
}

// GetByAccountIdAndStatuses mocks base method.
func (m *MockLoanInstallmentInfoDao) GetByAccountIdAndStatuses(ctx context.Context, accountId string, statuses []preapprovedloan.LoanInstallmentInfoStatus) ([]*preapprovedloan.LoanInstallmentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAccountIdAndStatuses", ctx, accountId, statuses)
	ret0, _ := ret[0].([]*preapprovedloan.LoanInstallmentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAccountIdAndStatuses indicates an expected call of GetByAccountIdAndStatuses.
func (mr *MockLoanInstallmentInfoDaoMockRecorder) GetByAccountIdAndStatuses(ctx, accountId, statuses interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAccountIdAndStatuses", reflect.TypeOf((*MockLoanInstallmentInfoDao)(nil).GetByAccountIdAndStatuses), ctx, accountId, statuses)
}

// GetByActiveAccountId mocks base method.
func (m *MockLoanInstallmentInfoDao) GetByActiveAccountId(ctx context.Context, accountId string) (*preapprovedloan.LoanInstallmentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActiveAccountId", ctx, accountId)
	ret0, _ := ret[0].(*preapprovedloan.LoanInstallmentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActiveAccountId indicates an expected call of GetByActiveAccountId.
func (mr *MockLoanInstallmentInfoDaoMockRecorder) GetByActiveAccountId(ctx, accountId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActiveAccountId", reflect.TypeOf((*MockLoanInstallmentInfoDao)(nil).GetByActiveAccountId), ctx, accountId)
}

// GetById mocks base method.
func (m *MockLoanInstallmentInfoDao) GetById(ctx context.Context, id string) (*preapprovedloan.LoanInstallmentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*preapprovedloan.LoanInstallmentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockLoanInstallmentInfoDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockLoanInstallmentInfoDao)(nil).GetById), ctx, id)
}

// GetByNextInstallmentDateAndStatus mocks base method.
func (m *MockLoanInstallmentInfoDao) GetByNextInstallmentDateAndStatus(ctx context.Context, nextInstallmentDate *time.Time, status preapprovedloan.LoanInstallmentInfoStatus) ([]*preapprovedloan.LoanInstallmentInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByNextInstallmentDateAndStatus", ctx, nextInstallmentDate, status)
	ret0, _ := ret[0].([]*preapprovedloan.LoanInstallmentInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByNextInstallmentDateAndStatus indicates an expected call of GetByNextInstallmentDateAndStatus.
func (mr *MockLoanInstallmentInfoDaoMockRecorder) GetByNextInstallmentDateAndStatus(ctx, nextInstallmentDate, status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByNextInstallmentDateAndStatus", reflect.TypeOf((*MockLoanInstallmentInfoDao)(nil).GetByNextInstallmentDateAndStatus), ctx, nextInstallmentDate, status)
}

// Update mocks base method.
func (m *MockLoanInstallmentInfoDao) Update(ctx context.Context, installmentInfo *preapprovedloan.LoanInstallmentInfo, updateMasks []preapprovedloan.LoanInstallmentInfoFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, installmentInfo, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockLoanInstallmentInfoDaoMockRecorder) Update(ctx, installmentInfo, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockLoanInstallmentInfoDao)(nil).Update), ctx, installmentInfo, updateMasks)
}

// MockLoanInstallmentPayoutDao is a mock of LoanInstallmentPayoutDao interface.
type MockLoanInstallmentPayoutDao struct {
	ctrl     *gomock.Controller
	recorder *MockLoanInstallmentPayoutDaoMockRecorder
}

// MockLoanInstallmentPayoutDaoMockRecorder is the mock recorder for MockLoanInstallmentPayoutDao.
type MockLoanInstallmentPayoutDaoMockRecorder struct {
	mock *MockLoanInstallmentPayoutDao
}

// NewMockLoanInstallmentPayoutDao creates a new mock instance.
func NewMockLoanInstallmentPayoutDao(ctrl *gomock.Controller) *MockLoanInstallmentPayoutDao {
	mock := &MockLoanInstallmentPayoutDao{ctrl: ctrl}
	mock.recorder = &MockLoanInstallmentPayoutDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoanInstallmentPayoutDao) EXPECT() *MockLoanInstallmentPayoutDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockLoanInstallmentPayoutDao) Create(ctx context.Context, installmentPayout *preapprovedloan.LoanInstallmentPayout) (*preapprovedloan.LoanInstallmentPayout, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, installmentPayout)
	ret0, _ := ret[0].(*preapprovedloan.LoanInstallmentPayout)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockLoanInstallmentPayoutDaoMockRecorder) Create(ctx, installmentPayout interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockLoanInstallmentPayoutDao)(nil).Create), ctx, installmentPayout)
}

// GetById mocks base method.
func (m *MockLoanInstallmentPayoutDao) GetById(ctx context.Context, id string) (*preapprovedloan.LoanInstallmentPayout, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*preapprovedloan.LoanInstallmentPayout)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockLoanInstallmentPayoutDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockLoanInstallmentPayoutDao)(nil).GetById), ctx, id)
}

// GetByLoanInstallmentInfoId mocks base method.
func (m *MockLoanInstallmentPayoutDao) GetByLoanInstallmentInfoId(ctx context.Context, id string, filterOptions ...storagev2.FilterOption) ([]*preapprovedloan.LoanInstallmentPayout, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, id}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByLoanInstallmentInfoId", varargs...)
	ret0, _ := ret[0].([]*preapprovedloan.LoanInstallmentPayout)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByLoanInstallmentInfoId indicates an expected call of GetByLoanInstallmentInfoId.
func (mr *MockLoanInstallmentPayoutDaoMockRecorder) GetByLoanInstallmentInfoId(ctx, id interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, id}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByLoanInstallmentInfoId", reflect.TypeOf((*MockLoanInstallmentPayoutDao)(nil).GetByLoanInstallmentInfoId), varargs...)
}

// GetByLoanInstallmentInfoIds mocks base method.
func (m *MockLoanInstallmentPayoutDao) GetByLoanInstallmentInfoIds(ctx context.Context, ids []string) ([]*preapprovedloan.LoanInstallmentPayout, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByLoanInstallmentInfoIds", ctx, ids)
	ret0, _ := ret[0].([]*preapprovedloan.LoanInstallmentPayout)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByLoanInstallmentInfoIds indicates an expected call of GetByLoanInstallmentInfoIds.
func (mr *MockLoanInstallmentPayoutDaoMockRecorder) GetByLoanInstallmentInfoIds(ctx, ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByLoanInstallmentInfoIds", reflect.TypeOf((*MockLoanInstallmentPayoutDao)(nil).GetByLoanInstallmentInfoIds), ctx, ids)
}

// GetCountByInstallmentInfoIds mocks base method.
func (m *MockLoanInstallmentPayoutDao) GetCountByInstallmentInfoIds(ctx context.Context, installmentIds []string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCountByInstallmentInfoIds", ctx, installmentIds)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCountByInstallmentInfoIds indicates an expected call of GetCountByInstallmentInfoIds.
func (mr *MockLoanInstallmentPayoutDaoMockRecorder) GetCountByInstallmentInfoIds(ctx, installmentIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCountByInstallmentInfoIds", reflect.TypeOf((*MockLoanInstallmentPayoutDao)(nil).GetCountByInstallmentInfoIds), ctx, installmentIds)
}

// Update mocks base method.
func (m *MockLoanInstallmentPayoutDao) Update(ctx context.Context, installmentPayout *preapprovedloan.LoanInstallmentPayout, updateMasks []preapprovedloan.LoanInstallmentPayoutFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, installmentPayout, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockLoanInstallmentPayoutDaoMockRecorder) Update(ctx, installmentPayout, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockLoanInstallmentPayoutDao)(nil).Update), ctx, installmentPayout, updateMasks)
}

// MockLoanActivityDao is a mock of LoanActivityDao interface.
type MockLoanActivityDao struct {
	ctrl     *gomock.Controller
	recorder *MockLoanActivityDaoMockRecorder
}

// MockLoanActivityDaoMockRecorder is the mock recorder for MockLoanActivityDao.
type MockLoanActivityDaoMockRecorder struct {
	mock *MockLoanActivityDao
}

// NewMockLoanActivityDao creates a new mock instance.
func NewMockLoanActivityDao(ctrl *gomock.Controller) *MockLoanActivityDao {
	mock := &MockLoanActivityDao{ctrl: ctrl}
	mock.recorder = &MockLoanActivityDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoanActivityDao) EXPECT() *MockLoanActivityDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockLoanActivityDao) Create(ctx context.Context, activity *preapprovedloan.LoanActivity) (*preapprovedloan.LoanActivity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, activity)
	ret0, _ := ret[0].(*preapprovedloan.LoanActivity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockLoanActivityDaoMockRecorder) Create(ctx, activity interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockLoanActivityDao)(nil).Create), ctx, activity)
}

// GetByAccountIdAndReferenceId mocks base method.
func (m *MockLoanActivityDao) GetByAccountIdAndReferenceId(ctx context.Context, accountId, referenceId string) (*preapprovedloan.LoanActivity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAccountIdAndReferenceId", ctx, accountId, referenceId)
	ret0, _ := ret[0].(*preapprovedloan.LoanActivity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAccountIdAndReferenceId indicates an expected call of GetByAccountIdAndReferenceId.
func (mr *MockLoanActivityDaoMockRecorder) GetByAccountIdAndReferenceId(ctx, accountId, referenceId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAccountIdAndReferenceId", reflect.TypeOf((*MockLoanActivityDao)(nil).GetByAccountIdAndReferenceId), ctx, accountId, referenceId)
}

// GetByAccountIdAndTypesAndCount mocks base method.
func (m *MockLoanActivityDao) GetByAccountIdAndTypesAndCount(ctx context.Context, accountId string, types []preapprovedloan.LoanActivityType, count int) ([]*preapprovedloan.LoanActivity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAccountIdAndTypesAndCount", ctx, accountId, types, count)
	ret0, _ := ret[0].([]*preapprovedloan.LoanActivity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAccountIdAndTypesAndCount indicates an expected call of GetByAccountIdAndTypesAndCount.
func (mr *MockLoanActivityDaoMockRecorder) GetByAccountIdAndTypesAndCount(ctx, accountId, types, count interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAccountIdAndTypesAndCount", reflect.TypeOf((*MockLoanActivityDao)(nil).GetByAccountIdAndTypesAndCount), ctx, accountId, types, count)
}

// GetByAccountIdsPaginated mocks base method.
func (m *MockLoanActivityDao) GetByAccountIdsPaginated(ctx context.Context, accountIds []string, pageToken *pagination.PageToken, pageSize uint32) ([]*preapprovedloan.LoanActivity, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAccountIdsPaginated", ctx, accountIds, pageToken, pageSize)
	ret0, _ := ret[0].([]*preapprovedloan.LoanActivity)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByAccountIdsPaginated indicates an expected call of GetByAccountIdsPaginated.
func (mr *MockLoanActivityDaoMockRecorder) GetByAccountIdsPaginated(ctx, accountIds, pageToken, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAccountIdsPaginated", reflect.TypeOf((*MockLoanActivityDao)(nil).GetByAccountIdsPaginated), ctx, accountIds, pageToken, pageSize)
}

// GetByAccountIdsandCount mocks base method.
func (m *MockLoanActivityDao) GetByAccountIdsandCount(ctx context.Context, accountIds []string, count int) ([]*preapprovedloan.LoanActivity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByAccountIdsandCount", ctx, accountIds, count)
	ret0, _ := ret[0].([]*preapprovedloan.LoanActivity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAccountIdsandCount indicates an expected call of GetByAccountIdsandCount.
func (mr *MockLoanActivityDaoMockRecorder) GetByAccountIdsandCount(ctx, accountIds, count interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAccountIdsandCount", reflect.TypeOf((*MockLoanActivityDao)(nil).GetByAccountIdsandCount), ctx, accountIds, count)
}

// GetById mocks base method.
func (m *MockLoanActivityDao) GetById(ctx context.Context, id string) (*preapprovedloan.LoanActivity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*preapprovedloan.LoanActivity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockLoanActivityDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockLoanActivityDao)(nil).GetById), ctx, id)
}

// GetByReferenceIdAndType mocks base method.
func (m *MockLoanActivityDao) GetByReferenceIdAndType(ctx context.Context, referenceId string, activityType preapprovedloan.LoanActivityType) (*preapprovedloan.LoanActivity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByReferenceIdAndType", ctx, referenceId, activityType)
	ret0, _ := ret[0].(*preapprovedloan.LoanActivity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByReferenceIdAndType indicates an expected call of GetByReferenceIdAndType.
func (mr *MockLoanActivityDaoMockRecorder) GetByReferenceIdAndType(ctx, referenceId, activityType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByReferenceIdAndType", reflect.TypeOf((*MockLoanActivityDao)(nil).GetByReferenceIdAndType), ctx, referenceId, activityType)
}

// Update mocks base method.
func (m *MockLoanActivityDao) Update(ctx context.Context, account *preapprovedloan.LoanActivity, updateMasks []preapprovedloan.LoanActivityFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, account, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockLoanActivityDaoMockRecorder) Update(ctx, account, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockLoanActivityDao)(nil).Update), ctx, account, updateMasks)
}

// MockLoanPaymentRequestsDao is a mock of LoanPaymentRequestsDao interface.
type MockLoanPaymentRequestsDao struct {
	ctrl     *gomock.Controller
	recorder *MockLoanPaymentRequestsDaoMockRecorder
}

// MockLoanPaymentRequestsDaoMockRecorder is the mock recorder for MockLoanPaymentRequestsDao.
type MockLoanPaymentRequestsDaoMockRecorder struct {
	mock *MockLoanPaymentRequestsDao
}

// NewMockLoanPaymentRequestsDao creates a new mock instance.
func NewMockLoanPaymentRequestsDao(ctrl *gomock.Controller) *MockLoanPaymentRequestsDao {
	mock := &MockLoanPaymentRequestsDao{ctrl: ctrl}
	mock.recorder = &MockLoanPaymentRequestsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoanPaymentRequestsDao) EXPECT() *MockLoanPaymentRequestsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockLoanPaymentRequestsDao) Create(ctx context.Context, loanPaymentRequest *preapprovedloan.LoanPaymentRequest) (*preapprovedloan.LoanPaymentRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, loanPaymentRequest)
	ret0, _ := ret[0].(*preapprovedloan.LoanPaymentRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockLoanPaymentRequestsDaoMockRecorder) Create(ctx, loanPaymentRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockLoanPaymentRequestsDao)(nil).Create), ctx, loanPaymentRequest)
}

// GetByAccountIdAndStatuses mocks base method.
func (m *MockLoanPaymentRequestsDao) GetByAccountIdAndStatuses(ctx context.Context, accountId string, statuses []preapprovedloan.LoanPaymentRequestStatus, filterOptions ...storagev2.FilterOption) ([]*preapprovedloan.LoanPaymentRequest, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, accountId, statuses}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByAccountIdAndStatuses", varargs...)
	ret0, _ := ret[0].([]*preapprovedloan.LoanPaymentRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAccountIdAndStatuses indicates an expected call of GetByAccountIdAndStatuses.
func (mr *MockLoanPaymentRequestsDaoMockRecorder) GetByAccountIdAndStatuses(ctx, accountId, statuses interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, accountId, statuses}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAccountIdAndStatuses", reflect.TypeOf((*MockLoanPaymentRequestsDao)(nil).GetByAccountIdAndStatuses), varargs...)
}

// GetByActorIdAndStatuses mocks base method.
func (m *MockLoanPaymentRequestsDao) GetByActorIdAndStatuses(ctx context.Context, actorId string, statuses []preapprovedloan.LoanPaymentRequestStatus, filterOptions ...storagev2.FilterOption) ([]*preapprovedloan.LoanPaymentRequest, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, statuses}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByActorIdAndStatuses", varargs...)
	ret0, _ := ret[0].([]*preapprovedloan.LoanPaymentRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdAndStatuses indicates an expected call of GetByActorIdAndStatuses.
func (mr *MockLoanPaymentRequestsDaoMockRecorder) GetByActorIdAndStatuses(ctx, actorId, statuses interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, statuses}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdAndStatuses", reflect.TypeOf((*MockLoanPaymentRequestsDao)(nil).GetByActorIdAndStatuses), varargs...)
}

// GetById mocks base method.
func (m *MockLoanPaymentRequestsDao) GetById(ctx context.Context, id string) (*preapprovedloan.LoanPaymentRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*preapprovedloan.LoanPaymentRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockLoanPaymentRequestsDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockLoanPaymentRequestsDao)(nil).GetById), ctx, id)
}

// GetByOrchId mocks base method.
func (m *MockLoanPaymentRequestsDao) GetByOrchId(ctx context.Context, orchId string) (*preapprovedloan.LoanPaymentRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByOrchId", ctx, orchId)
	ret0, _ := ret[0].(*preapprovedloan.LoanPaymentRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByOrchId indicates an expected call of GetByOrchId.
func (mr *MockLoanPaymentRequestsDaoMockRecorder) GetByOrchId(ctx, orchId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByOrchId", reflect.TypeOf((*MockLoanPaymentRequestsDao)(nil).GetByOrchId), ctx, orchId)
}

// GetByTimeRange mocks base method.
func (m *MockLoanPaymentRequestsDao) GetByTimeRange(ctx context.Context, from, to time.Time, statuses []preapprovedloan.LoanPaymentRequestStatus, types []preapprovedloan.LoanPaymentRequestType) ([]*preapprovedloan.LoanPaymentRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByTimeRange", ctx, from, to, statuses, types)
	ret0, _ := ret[0].([]*preapprovedloan.LoanPaymentRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByTimeRange indicates an expected call of GetByTimeRange.
func (mr *MockLoanPaymentRequestsDaoMockRecorder) GetByTimeRange(ctx, from, to, statuses, types interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByTimeRange", reflect.TypeOf((*MockLoanPaymentRequestsDao)(nil).GetByTimeRange), ctx, from, to, statuses, types)
}

// GetLatestByAccountIdAndStatuses mocks base method.
func (m *MockLoanPaymentRequestsDao) GetLatestByAccountIdAndStatuses(ctx context.Context, accountId string, statuses []preapprovedloan.LoanPaymentRequestStatus) (*preapprovedloan.LoanPaymentRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestByAccountIdAndStatuses", ctx, accountId, statuses)
	ret0, _ := ret[0].(*preapprovedloan.LoanPaymentRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestByAccountIdAndStatuses indicates an expected call of GetLatestByAccountIdAndStatuses.
func (mr *MockLoanPaymentRequestsDaoMockRecorder) GetLatestByAccountIdAndStatuses(ctx, accountId, statuses interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestByAccountIdAndStatuses", reflect.TypeOf((*MockLoanPaymentRequestsDao)(nil).GetLatestByAccountIdAndStatuses), ctx, accountId, statuses)
}

// Update mocks base method.
func (m *MockLoanPaymentRequestsDao) Update(ctx context.Context, loanPaymentRequest *preapprovedloan.LoanPaymentRequest, updateMasks []preapprovedloan.LoanPaymentRequestFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, loanPaymentRequest, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockLoanPaymentRequestsDaoMockRecorder) Update(ctx, loanPaymentRequest, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockLoanPaymentRequestsDao)(nil).Update), ctx, loanPaymentRequest, updateMasks)
}

// MockLoanApplicantDao is a mock of LoanApplicantDao interface.
type MockLoanApplicantDao struct {
	ctrl     *gomock.Controller
	recorder *MockLoanApplicantDaoMockRecorder
}

// MockLoanApplicantDaoMockRecorder is the mock recorder for MockLoanApplicantDao.
type MockLoanApplicantDaoMockRecorder struct {
	mock *MockLoanApplicantDao
}

// NewMockLoanApplicantDao creates a new mock instance.
func NewMockLoanApplicantDao(ctrl *gomock.Controller) *MockLoanApplicantDao {
	mock := &MockLoanApplicantDao{ctrl: ctrl}
	mock.recorder = &MockLoanApplicantDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoanApplicantDao) EXPECT() *MockLoanApplicantDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockLoanApplicantDao) Create(ctx context.Context, loanApplicant *preapprovedloan.LoanApplicant) (*preapprovedloan.LoanApplicant, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, loanApplicant)
	ret0, _ := ret[0].(*preapprovedloan.LoanApplicant)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockLoanApplicantDaoMockRecorder) Create(ctx, loanApplicant interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockLoanApplicantDao)(nil).Create), ctx, loanApplicant)
}

// GetBatchByVendorApplicantIds mocks base method.
func (m *MockLoanApplicantDao) GetBatchByVendorApplicantIds(ctx context.Context, vendorApplicantIds []string) ([]*preapprovedloan.LoanApplicant, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBatchByVendorApplicantIds", ctx, vendorApplicantIds)
	ret0, _ := ret[0].([]*preapprovedloan.LoanApplicant)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBatchByVendorApplicantIds indicates an expected call of GetBatchByVendorApplicantIds.
func (mr *MockLoanApplicantDaoMockRecorder) GetBatchByVendorApplicantIds(ctx, vendorApplicantIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBatchByVendorApplicantIds", reflect.TypeOf((*MockLoanApplicantDao)(nil).GetBatchByVendorApplicantIds), ctx, vendorApplicantIds)
}

// GetByActorId mocks base method.
func (m *MockLoanApplicantDao) GetByActorId(ctx context.Context, actorId string) (*preapprovedloan.LoanApplicant, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", ctx, actorId)
	ret0, _ := ret[0].(*preapprovedloan.LoanApplicant)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockLoanApplicantDaoMockRecorder) GetByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockLoanApplicantDao)(nil).GetByActorId), ctx, actorId)
}

// GetByActorIdAndVendorAndLoanProgramAndProgramVersion mocks base method.
func (m *MockLoanApplicantDao) GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx context.Context, actorId string, vendor preapprovedloan.Vendor, loanProgram preapprovedloan.LoanProgram, programVersion enums.LoanProgramVersion) (*preapprovedloan.LoanApplicant, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorIdAndVendorAndLoanProgramAndProgramVersion", ctx, actorId, vendor, loanProgram, programVersion)
	ret0, _ := ret[0].(*preapprovedloan.LoanApplicant)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdAndVendorAndLoanProgramAndProgramVersion indicates an expected call of GetByActorIdAndVendorAndLoanProgramAndProgramVersion.
func (mr *MockLoanApplicantDaoMockRecorder) GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, actorId, vendor, loanProgram, programVersion interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdAndVendorAndLoanProgramAndProgramVersion", reflect.TypeOf((*MockLoanApplicantDao)(nil).GetByActorIdAndVendorAndLoanProgramAndProgramVersion), ctx, actorId, vendor, loanProgram, programVersion)
}

// GetById mocks base method.
func (m *MockLoanApplicantDao) GetById(ctx context.Context, id string) (*preapprovedloan.LoanApplicant, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*preapprovedloan.LoanApplicant)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockLoanApplicantDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockLoanApplicantDao)(nil).GetById), ctx, id)
}

// GetByVendorRequestId mocks base method.
func (m *MockLoanApplicantDao) GetByVendorRequestId(ctx context.Context, id string) (*preapprovedloan.LoanApplicant, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByVendorRequestId", ctx, id)
	ret0, _ := ret[0].(*preapprovedloan.LoanApplicant)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByVendorRequestId indicates an expected call of GetByVendorRequestId.
func (mr *MockLoanApplicantDaoMockRecorder) GetByVendorRequestId(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByVendorRequestId", reflect.TypeOf((*MockLoanApplicantDao)(nil).GetByVendorRequestId), ctx, id)
}

// Update mocks base method.
func (m *MockLoanApplicantDao) Update(ctx context.Context, loanApplicant *preapprovedloan.LoanApplicant, updateMasks []preapprovedloan.LoanApplicantFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, loanApplicant, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockLoanApplicantDaoMockRecorder) Update(ctx, loanApplicant, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockLoanApplicantDao)(nil).Update), ctx, loanApplicant, updateMasks)
}

// MockPartnerLmsUserDao is a mock of PartnerLmsUserDao interface.
type MockPartnerLmsUserDao struct {
	ctrl     *gomock.Controller
	recorder *MockPartnerLmsUserDaoMockRecorder
}

// MockPartnerLmsUserDaoMockRecorder is the mock recorder for MockPartnerLmsUserDao.
type MockPartnerLmsUserDaoMockRecorder struct {
	mock *MockPartnerLmsUserDao
}

// NewMockPartnerLmsUserDao creates a new mock instance.
func NewMockPartnerLmsUserDao(ctrl *gomock.Controller) *MockPartnerLmsUserDao {
	mock := &MockPartnerLmsUserDao{ctrl: ctrl}
	mock.recorder = &MockPartnerLmsUserDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPartnerLmsUserDao) EXPECT() *MockPartnerLmsUserDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockPartnerLmsUserDao) Create(ctx context.Context, partnerLmsUser *preapprovedloan.PartnerLmsUser) (*preapprovedloan.PartnerLmsUser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, partnerLmsUser)
	ret0, _ := ret[0].(*preapprovedloan.PartnerLmsUser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockPartnerLmsUserDaoMockRecorder) Create(ctx, partnerLmsUser interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockPartnerLmsUserDao)(nil).Create), ctx, partnerLmsUser)
}

// GetByLmsPartnerAndActorId mocks base method.
func (m *MockPartnerLmsUserDao) GetByLmsPartnerAndActorId(ctx context.Context, lmsPartner enums.LmsPartner, actorId string) (*preapprovedloan.PartnerLmsUser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByLmsPartnerAndActorId", ctx, lmsPartner, actorId)
	ret0, _ := ret[0].(*preapprovedloan.PartnerLmsUser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByLmsPartnerAndActorId indicates an expected call of GetByLmsPartnerAndActorId.
func (mr *MockPartnerLmsUserDaoMockRecorder) GetByLmsPartnerAndActorId(ctx, lmsPartner, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByLmsPartnerAndActorId", reflect.TypeOf((*MockPartnerLmsUserDao)(nil).GetByLmsPartnerAndActorId), ctx, lmsPartner, actorId)
}

// GetByLmsPartnerAndLmsPartnerUserId mocks base method.
func (m *MockPartnerLmsUserDao) GetByLmsPartnerAndLmsPartnerUserId(ctx context.Context, lmsPartner enums.LmsPartner, lmsPartnerUserId string) (*preapprovedloan.PartnerLmsUser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByLmsPartnerAndLmsPartnerUserId", ctx, lmsPartner, lmsPartnerUserId)
	ret0, _ := ret[0].(*preapprovedloan.PartnerLmsUser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByLmsPartnerAndLmsPartnerUserId indicates an expected call of GetByLmsPartnerAndLmsPartnerUserId.
func (mr *MockPartnerLmsUserDaoMockRecorder) GetByLmsPartnerAndLmsPartnerUserId(ctx, lmsPartner, lmsPartnerUserId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByLmsPartnerAndLmsPartnerUserId", reflect.TypeOf((*MockPartnerLmsUserDao)(nil).GetByLmsPartnerAndLmsPartnerUserId), ctx, lmsPartner, lmsPartnerUserId)
}

// Update mocks base method.
func (m *MockPartnerLmsUserDao) Update(ctx context.Context, partnerLmsUser *preapprovedloan.PartnerLmsUser, updateMasks []enums.PartnerLmsUserFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, partnerLmsUser, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockPartnerLmsUserDaoMockRecorder) Update(ctx, partnerLmsUser, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockPartnerLmsUserDao)(nil).Update), ctx, partnerLmsUser, updateMasks)
}

// MockMandateRequestDao is a mock of MandateRequestDao interface.
type MockMandateRequestDao struct {
	ctrl     *gomock.Controller
	recorder *MockMandateRequestDaoMockRecorder
}

// MockMandateRequestDaoMockRecorder is the mock recorder for MockMandateRequestDao.
type MockMandateRequestDaoMockRecorder struct {
	mock *MockMandateRequestDao
}

// NewMockMandateRequestDao creates a new mock instance.
func NewMockMandateRequestDao(ctrl *gomock.Controller) *MockMandateRequestDao {
	mock := &MockMandateRequestDao{ctrl: ctrl}
	mock.recorder = &MockMandateRequestDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMandateRequestDao) EXPECT() *MockMandateRequestDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockMandateRequestDao) Create(ctx context.Context, mandateRequest *preapprovedloan.MandateRequest) (*preapprovedloan.MandateRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, mandateRequest)
	ret0, _ := ret[0].(*preapprovedloan.MandateRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockMandateRequestDaoMockRecorder) Create(ctx, mandateRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockMandateRequestDao)(nil).Create), ctx, mandateRequest)
}

// GetByActorIdAndVendorAndLoanProgramPaginated mocks base method.
func (m *MockMandateRequestDao) GetByActorIdAndVendorAndLoanProgramPaginated(ctx context.Context, actorId string, vendor preapprovedloan.Vendor, loanProgram preapprovedloan.LoanProgram, pageToken *pagination.PageToken, pageSize uint32) ([]*preapprovedloan.MandateRequest, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorIdAndVendorAndLoanProgramPaginated", ctx, actorId, vendor, loanProgram, pageToken, pageSize)
	ret0, _ := ret[0].([]*preapprovedloan.MandateRequest)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByActorIdAndVendorAndLoanProgramPaginated indicates an expected call of GetByActorIdAndVendorAndLoanProgramPaginated.
func (mr *MockMandateRequestDaoMockRecorder) GetByActorIdAndVendorAndLoanProgramPaginated(ctx, actorId, vendor, loanProgram, pageToken, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdAndVendorAndLoanProgramPaginated", reflect.TypeOf((*MockMandateRequestDao)(nil).GetByActorIdAndVendorAndLoanProgramPaginated), ctx, actorId, vendor, loanProgram, pageToken, pageSize)
}

// GetById mocks base method.
func (m *MockMandateRequestDao) GetById(ctx context.Context, id string) (*preapprovedloan.MandateRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*preapprovedloan.MandateRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockMandateRequestDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockMandateRequestDao)(nil).GetById), ctx, id)
}

// GetByVendorMandateId mocks base method.
func (m *MockMandateRequestDao) GetByVendorMandateId(ctx context.Context, vendorMandateId string) (*preapprovedloan.MandateRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByVendorMandateId", ctx, vendorMandateId)
	ret0, _ := ret[0].(*preapprovedloan.MandateRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByVendorMandateId indicates an expected call of GetByVendorMandateId.
func (mr *MockMandateRequestDaoMockRecorder) GetByVendorMandateId(ctx, vendorMandateId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByVendorMandateId", reflect.TypeOf((*MockMandateRequestDao)(nil).GetByVendorMandateId), ctx, vendorMandateId)
}

// GetLatestByActorIdAndVendorAndLoanProgram mocks base method.
func (m *MockMandateRequestDao) GetLatestByActorIdAndVendorAndLoanProgram(ctx context.Context, actorId string, vendor preapprovedloan.Vendor, loanProgram preapprovedloan.LoanProgram) (*preapprovedloan.MandateRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestByActorIdAndVendorAndLoanProgram", ctx, actorId, vendor, loanProgram)
	ret0, _ := ret[0].(*preapprovedloan.MandateRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestByActorIdAndVendorAndLoanProgram indicates an expected call of GetLatestByActorIdAndVendorAndLoanProgram.
func (mr *MockMandateRequestDaoMockRecorder) GetLatestByActorIdAndVendorAndLoanProgram(ctx, actorId, vendor, loanProgram interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestByActorIdAndVendorAndLoanProgram", reflect.TypeOf((*MockMandateRequestDao)(nil).GetLatestByActorIdAndVendorAndLoanProgram), ctx, actorId, vendor, loanProgram)
}

// UpdateById mocks base method.
func (m *MockMandateRequestDao) UpdateById(ctx context.Context, mandateRequest *preapprovedloan.MandateRequest, updateMasks []preapprovedloan.MandateRequestFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateById", ctx, mandateRequest, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateById indicates an expected call of UpdateById.
func (mr *MockMandateRequestDaoMockRecorder) UpdateById(ctx, mandateRequest, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateById", reflect.TypeOf((*MockMandateRequestDao)(nil).UpdateById), ctx, mandateRequest, updateMasks)
}

// MockPreEligibilityOfferDao is a mock of PreEligibilityOfferDao interface.
type MockPreEligibilityOfferDao struct {
	ctrl     *gomock.Controller
	recorder *MockPreEligibilityOfferDaoMockRecorder
}

// MockPreEligibilityOfferDaoMockRecorder is the mock recorder for MockPreEligibilityOfferDao.
type MockPreEligibilityOfferDaoMockRecorder struct {
	mock *MockPreEligibilityOfferDao
}

// NewMockPreEligibilityOfferDao creates a new mock instance.
func NewMockPreEligibilityOfferDao(ctrl *gomock.Controller) *MockPreEligibilityOfferDao {
	mock := &MockPreEligibilityOfferDao{ctrl: ctrl}
	mock.recorder = &MockPreEligibilityOfferDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPreEligibilityOfferDao) EXPECT() *MockPreEligibilityOfferDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockPreEligibilityOfferDao) Create(ctx context.Context, offer *preapprovedloan.PreEligibilityOffer) (*preapprovedloan.PreEligibilityOffer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, offer)
	ret0, _ := ret[0].(*preapprovedloan.PreEligibilityOffer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockPreEligibilityOfferDaoMockRecorder) Create(ctx, offer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockPreEligibilityOfferDao)(nil).Create), ctx, offer)
}

// GetActiveOfferByActorId mocks base method.
func (m *MockPreEligibilityOfferDao) GetActiveOfferByActorId(ctx context.Context, actorId string) (*preapprovedloan.PreEligibilityOffer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveOfferByActorId", ctx, actorId)
	ret0, _ := ret[0].(*preapprovedloan.PreEligibilityOffer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveOfferByActorId indicates an expected call of GetActiveOfferByActorId.
func (mr *MockPreEligibilityOfferDaoMockRecorder) GetActiveOfferByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveOfferByActorId", reflect.TypeOf((*MockPreEligibilityOfferDao)(nil).GetActiveOfferByActorId), ctx, actorId)
}

// GetByLoanRequestId mocks base method.
func (m *MockPreEligibilityOfferDao) GetByLoanRequestId(ctx context.Context, lrId string) (*preapprovedloan.PreEligibilityOffer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByLoanRequestId", ctx, lrId)
	ret0, _ := ret[0].(*preapprovedloan.PreEligibilityOffer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByLoanRequestId indicates an expected call of GetByLoanRequestId.
func (mr *MockPreEligibilityOfferDaoMockRecorder) GetByLoanRequestId(ctx, lrId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByLoanRequestId", reflect.TypeOf((*MockPreEligibilityOfferDao)(nil).GetByLoanRequestId), ctx, lrId)
}
