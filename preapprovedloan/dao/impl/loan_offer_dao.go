//nolint:dupl
package impl

import (
	"context"

	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	storage "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"gorm.io/gorm"

	preapprovedloanPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/dao/model"
	"github.com/epifi/gamma/preapprovedloan/helper"
	wireTypes "github.com/epifi/gamma/preapprovedloan/wire/types"
)

type CrdbLoanOfferDao struct {
	dbResourceProvider *storage.DBResourceProvider[*gorm.DB]
	idGen              idgen.IdGenerator
	cache              cache.CacheStorage
}

var (
	maxLoanAmount int64 = 500000
)

var _ dao.LoanOffersDao = &CrdbLoanOfferDao{}

var LoanOffersDaoWireSet = wire.NewSet(NewCrdbLoanOfferDao, wire.Bind(new(dao.LoanOffersDao), new(*CrdbLoanOfferDao)))

var loanOfferColumnNamesMap = map[preapprovedloanPb.LoanOfferFieldMask]string{
	preapprovedloanPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_VENDOR_OFFER_ID:                    "vendor_offer_id",
	preapprovedloanPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_OFFER_CONSTRAINTS:                  "offer_constraints",
	preapprovedloanPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_DEACTIVATED_AT:                     "deactivated_at",
	preapprovedloanPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_UPDATED_AT:                         "updated_at",
	preapprovedloanPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_LOAN_OFFER_ELIGIBILITY_CRITERIA_ID: "loan_offer_eligibility_criteria_id",
	preapprovedloanPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_LOAN_PROGRAM:                       "loan_program",
	preapprovedloanPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_LAST_VIEWED_AT:                     "last_viewed_at",
}

func NewCrdbLoanOfferDao(dbResourceProvider *storage.DBResourceProvider[*gorm.DB], idGen idgen.IdGenerator, cache wireTypes.LendingCacheStorage) *CrdbLoanOfferDao {
	return &CrdbLoanOfferDao{
		dbResourceProvider: dbResourceProvider,
		idGen:              idGen,
		cache:              cache,
	}
}

func (c *CrdbLoanOfferDao) Create(ctx context.Context, loanOffer *preapprovedloanPb.LoanOffer) (*preapprovedloanPb.LoanOffer, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanOfferDao", "Create", time.Now())
	db, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}
	id, err := c.idGen.Get(idgen.PreApprovedLoanOffer)
	if err != nil {
		return nil, fmt.Errorf("preapprovedloan offer id generation failed: %w", err)
	}
	if loanOffer.GetVendor() == preapprovedloanPb.Vendor_FEDERAL {
		maxAmount := loanOffer.GetOfferConstraints().GetMaxLoanAmount().GetUnits()
		if maxAmount >= maxLoanAmount {
			loanOffer.OfferConstraints.MaxLoanAmount.Units = maxLoanAmount
			loanOffer.OfferConstraints.MaxLoanAmount.Nanos = 0
		}
	}
	if loanOffer.GetLoanOfferType() == preapprovedloanPb.LoanOfferType_LOAN_OFFER_TYPE_UNSPECIFIED {
		loanOffer.LoanOfferType = preapprovedloanPb.LoanOfferType_LOAN_OFFER_TYPE_SOFT
	}
	loanOfferModel := model.NewLoanOffer(loanOffer)
	loanOfferModel.Id = id
	err = db.Create(loanOfferModel).Error
	if err != nil {
		if storage.IsViolatingUniqueConstraint(err, "loan_offers_unique_idx_vendor_offer_id_vendor") {
			return nil, errors.Wrap(epifierrors.ErrAlreadyExists, fmt.Sprintf("creating loan offer already exists, vendor: %v , vendor-offer-id: %v ", loanOffer.GetVendor(), loanOffer.GetVendorOfferId()))
		}
		return nil, fmt.Errorf("error in creating loan offer: %w", err)
	}

	// intentionally not returning error as we should not block the loan offer creation due to cache deletion failure
	deleteCacheKeysBestEffort(ctx, c.cache, helper.GetLoanDataExistenceCacheKey(loanOffer.GetActorId()))

	return loanOfferModel.GetProto(), nil
}

func (c *CrdbLoanOfferDao) Update(ctx context.Context, loanOffer *preapprovedloanPb.LoanOffer, updateMasks []preapprovedloanPb.LoanOfferFieldMask) error {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanOfferDao", "Update", time.Now())
	if loanOffer.GetId() == "" {
		return errors.New("primary identifier can't be empty for an update operation")
	}
	if len(updateMasks) == 0 {
		return errors.New("update mask can't be empty")
	}
	loanOfferModel := model.NewLoanOffer(loanOffer)
	updateColumns := c.selectedColumnsForUpdate(updateMasks)
	db, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}
	whereClause := &model.LoanOffer{
		Id: loanOffer.GetId(),
	}
	res := db.Model(loanOfferModel).Where(whereClause).Select(updateColumns).Updates(loanOfferModel)
	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}
	return nil
}

func (c *CrdbLoanOfferDao) GetById(ctx context.Context, id string) (*preapprovedloanPb.LoanOffer, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanOfferDao", "GetById", time.Now())
	if id == "" {
		return nil, errors.New("id can't be blank")
	}
	var loanOfferModel model.LoanOffer
	db, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}
	if err := db.Where("id = ?", id).First(&loanOfferModel).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return loanOfferModel.GetProto(), nil
}

func (c *CrdbLoanOfferDao) GetActiveOfferByActorIdAndVendor(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor) (*preapprovedloanPb.LoanOffer, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanOfferDao", "GetActiveOfferByActorIdAndVendor", time.Now())
	if actorId == "" {
		return nil, errors.New("actor id can't be blank")
	}
	if vendor == preapprovedloanPb.Vendor_VENDOR_UNSPECIFIED {
		return nil, errors.New("vendor can't be unspecified")
	}

	var loanOfferModel model.LoanOffer
	db, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}

	if err := db.Where("actor_id = ?", actorId).
		Where("vendor = ?", vendor).
		Where("deactivated_at IS NULL").
		Where("valid_till > ?", time.Now()).
		Order("created_at desc").
		First(&loanOfferModel).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return loanOfferModel.GetProto(), nil
}

func (c *CrdbLoanOfferDao) GetLatestByActorIdAndVendor(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor) (*preapprovedloanPb.LoanOffer, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanOfferDao", "GetLatestByActorIdAndVendor", time.Now())
	if actorId == "" {
		return nil, errors.New("actor id can't be blank")
	}
	if vendor == preapprovedloanPb.Vendor_VENDOR_UNSPECIFIED {
		return nil, fmt.Errorf("vendor can't be unspecified")
	}

	d, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}
	var loanOfferModel model.LoanOffer
	query := d.Where("actor_id = ?", actorId).Where("vendor = ?", vendor).Order("created_at desc")
	if err := query.First(&loanOfferModel).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return loanOfferModel.GetProto(), nil
}

// DeactivateLoanOffer DeactivateLoanOffer marks the latest loan offer for an actor as deactivated
func (c *CrdbLoanOfferDao) DeactivateLoanOffer(ctx context.Context, loanOfferId string) error {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanOfferDao", "DeactivateLoanOffer", time.Now())
	if loanOfferId == "" {
		return errors.New("primary identifier can't be empty for deactivate operation")
	}
	deactivatedTime := time.Now()
	loanOfferModel := model.LoanOffer{
		DeactivatedAt: &deactivatedTime,
	}

	db, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}
	whereClause := &model.LoanOffer{
		Id: loanOfferId,
	}
	res := db.Model(loanOfferModel).Where(whereClause).Updates(loanOfferModel)
	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}
	return nil
}

func (c *CrdbLoanOfferDao) GetByOfferEligibilityCriteriaId(ctx context.Context, loecId string, count int, orderByDesc bool) ([]*preapprovedloanPb.LoanOffer, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanOfferDao", "GetByOfferEligibilityCriteriaId", time.Now())
	if loecId == "" {
		return nil, errors.New("loec Id can't be blank")
	}
	d, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}
	loanOffersModels := make([]*model.LoanOffer, 0)
	query := d.Where("loan_offer_eligibility_criteria_id = ?", loecId)
	if orderByDesc {
		query = query.Order("created_at desc")
	}
	if count > 0 {
		query = query.Limit(count)
	}
	if err := query.Find(&loanOffersModels).Error; err != nil {
		return nil, fmt.Errorf("failed to get loan offers by loec id: %s, %w", loecId, err)
	}
	if len(loanOffersModels) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	var loanOffers []*preapprovedloanPb.LoanOffer
	for _, laModel := range loanOffersModels {
		loanOffers = append(loanOffers, laModel.GetProto())
	}
	return loanOffers, nil
}

func (c *CrdbLoanOfferDao) GetActiveOffersByActorIdAndLoanPrograms(ctx context.Context, actorId string, loanPrograms []preapprovedloanPb.LoanProgram) ([]*preapprovedloanPb.LoanOffer, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanOfferDao", "GetActiveOffersByActorIdAndLoanPrograms", time.Now())
	if actorId == "" {
		return nil, errors.New("actor id can't be blank")
	}

	loanOfferModels := make([]*model.LoanOffer, 0)
	db, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}

	db = db.Where("actor_id = ?", actorId).
		Where("deactivated_at IS NULL").
		Where("valid_till > ?", time.Now())
	if len(loanPrograms) > 0 {
		db = db.Where("loan_program IN (?)", loanPrograms)
	}

	if err := db.Order("created_at desc").Find(&loanOfferModels).Error; err != nil {
		return nil, fmt.Errorf("failed to get loan offers by actor id: %s, and loan programs: %v, %w", actorId, loanPrograms, err)
	}
	if len(loanOfferModels) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	loanOffers := make([]*preapprovedloanPb.LoanOffer, 0, len(loanOfferModels))
	for _, loModel := range loanOfferModels {
		loanOffers = append(loanOffers, loModel.GetProto())
	}
	return loanOffers, nil
}

func (c *CrdbLoanOfferDao) GetActiveOfferByActorIdVendorAndLoanProgram(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor, loanProgram preapprovedloanPb.LoanProgram) (*preapprovedloanPb.LoanOffer, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanOfferDao", "GetActiveOfferByActorIdVendorAndLoanProgram", time.Now())
	if actorId == "" {
		return nil, errors.New("actor id can't be blank")
	}
	if vendor == preapprovedloanPb.Vendor_VENDOR_UNSPECIFIED {
		return nil, errors.New("vendor can't be unspecified")
	}
	if loanProgram == preapprovedloanPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		return nil, errors.New("loan program can't be unspecified")
	}
	var loanOfferModel model.LoanOffer
	db, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}

	if err := db.Where("actor_id = ?", actorId).
		Where("vendor = ?", vendor).
		Where("loan_program = ?", loanProgram).
		Where("deactivated_at IS NULL").
		Where("valid_till > ?", time.Now()).
		Order("created_at desc").
		First(&loanOfferModel).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return loanOfferModel.GetProto(), nil
}

func (c *CrdbLoanOfferDao) GetLatestByActorIdVendorAndLoanProgram(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor, loanProgram preapprovedloanPb.LoanProgram) (*preapprovedloanPb.LoanOffer, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanOfferDao", "GetLatestByActorIdVendorAndLoanProgram", time.Now())
	if actorId == "" {
		return nil, errors.New("actor id can't be blank")
	}
	if vendor == preapprovedloanPb.Vendor_VENDOR_UNSPECIFIED {
		return nil, fmt.Errorf("vendor can't be unspecified")
	}
	if loanProgram == preapprovedloanPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		return nil, errors.New("loan program can't be unspecified")
	}

	d, err := getConnFromContextOrProvider(ctx, c.dbResourceProvider)
	if err != nil {
		return nil, fmt.Errorf("error in getConnFromContextOrProvider, %w", err)
	}
	var loanOfferModel model.LoanOffer
	query := d.Where("actor_id = ?", actorId).
		Where("vendor = ?", vendor).
		Where("loan_program = ?", loanProgram).
		Order("created_at desc")
	if err := query.First(&loanOfferModel).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return loanOfferModel.GetProto(), nil
}

func (c *CrdbLoanOfferDao) selectedColumnsForUpdate(updateMasks []preapprovedloanPb.LoanOfferFieldMask) []string {
	var selectColumns []string
	for _, field := range updateMasks {
		selectColumns = append(selectColumns, loanOfferColumnNamesMap[field])
	}
	return selectColumns
}
