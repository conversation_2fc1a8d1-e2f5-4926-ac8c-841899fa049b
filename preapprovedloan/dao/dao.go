//go:generate mockgen -source=dao.go -destination=mocks/mock_dao.go
//go:generate dao_metrics_gen .

package dao

import (
	"context"
	"time"

	"google.golang.org/genproto/googleapis/type/date"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	preapprovedloanPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
)

type LoanOfferEligibilityCriteriaDao interface {
	Create(ctx context.Context, loanOfferEligibility *preapprovedloanPb.LoanOfferEligibilityCriteria) (*preapprovedloanPb.LoanOfferEligibilityCriteria, error)
	CreateBatch(ctx context.Context, loanOfferEligibilityList []*preapprovedloanPb.LoanOfferEligibilityCriteria, batchSize int) error
	Update(ctx context.Context, loanOfferEligibility *preapprovedloanPb.LoanOfferEligibilityCriteria, updateMasks []preapprovedloanPb.LoanOfferEligibilityCriteriaFieldMask) error
	GetById(ctx context.Context, id string) (*preapprovedloanPb.LoanOfferEligibilityCriteria, error)
	GetByActorIdAndVendorAndStatus(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor, statuses []preapprovedloanPb.LoanOfferEligibilityCriteriaStatus, sortByDesc bool) ([]*preapprovedloanPb.LoanOfferEligibilityCriteria, error)
	GetByActorIdLoanProgramsAndStatuses(ctx context.Context, actorId string, loanPrograms []preapprovedloanPb.LoanProgram, statuses []preapprovedloanPb.LoanOfferEligibilityCriteriaStatus, updatedWithin time.Duration, sortByDesc bool) ([]*preapprovedloanPb.LoanOfferEligibilityCriteria, error)
	GetByVendorRequestIdAndLoanProgram(ctx context.Context, id string, loanProgram preapprovedloanPb.LoanProgram) (*preapprovedloanPb.LoanOfferEligibilityCriteria, error)
	GetActiveLoecsByActorIdLoanProgramsAndStatuses(ctx context.Context, actorId string, loanPrograms []preapprovedloanPb.LoanProgram, statuses []preapprovedloanPb.LoanOfferEligibilityCriteriaStatus, updatedWithin time.Duration, sortByDesc bool) ([]*preapprovedloanPb.LoanOfferEligibilityCriteria, error)
}

type LoanRequestsDao interface {
	Create(ctx context.Context, request *preapprovedloanPb.LoanRequest) (*preapprovedloanPb.LoanRequest, error)
	Update(ctx context.Context, request *preapprovedloanPb.LoanRequest, updateMasks []preapprovedloanPb.LoanRequestFieldMask) error
	GetById(ctx context.Context, id string) (*preapprovedloanPb.LoanRequest, error)
	GetByActorIdAndVendorAndStatus(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor, statuses []preapprovedloanPb.LoanRequestStatus) ([]*preapprovedloanPb.LoanRequest, error)
	GetByActorIdTypesStatusAndLoanProgram(ctx context.Context, actorId string, types []preapprovedloanPb.LoanRequestType, statuses []preapprovedloanPb.LoanRequestStatus, loanPrograms []preapprovedloanPb.LoanProgram, completedAtWithin *time.Duration, updatedAtWithin *time.Duration) ([]*preapprovedloanPb.LoanRequest, error)
	// Deprecated: GetByActorIdVendorStatusAndLoanProgram is deprecated in favour of GetByActorIdTypesStatusAndLoanProgram
	GetByActorIdVendorStatusAndLoanProgram(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor, statuses []preapprovedloanPb.LoanRequestStatus, loanProgram preapprovedloanPb.LoanProgram) ([]*preapprovedloanPb.LoanRequest, error)
	GetByOrchId(ctx context.Context, orchId string) (*preapprovedloanPb.LoanRequest, error)
	GetByLoanAccountIdAndType(ctx context.Context, loanAccountId string, loanReqType preapprovedloanPb.LoanRequestType) ([]*preapprovedloanPb.LoanRequest, error)
	GetTerminalByActorIdAndVendor(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor, filterOptions ...storagev2.FilterOption) ([]*preapprovedloanPb.LoanRequest, error)
	GetNonTerminalByActorIdAndVendor(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor, filterOptions ...storagev2.FilterOption) (*preapprovedloanPb.LoanRequest, error)
	GetNonTerminalByActorIdVendorAndLoanProgram(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor, loanProgram preapprovedloanPb.LoanProgram, filterOptions ...storagev2.FilterOption) (*preapprovedloanPb.LoanRequest, error)
	GetByClientRequestId(ctx context.Context, crId string) (*preapprovedloanPb.LoanRequest, error)
	GetByVendorReqId(ctx context.Context, vendorReqId string) (*preapprovedloanPb.LoanRequest, error)
	GetNonTerminalByActorId(ctx context.Context, actorId string, filterOptions ...storagev2.FilterOption) ([]*preapprovedloanPb.LoanRequest, error)
}

type LoanOffersDao interface {
	Create(ctx context.Context, loanOffer *preapprovedloanPb.LoanOffer) (*preapprovedloanPb.LoanOffer, error)
	Update(ctx context.Context, loanOffer *preapprovedloanPb.LoanOffer, updateMasks []preapprovedloanPb.LoanOfferFieldMask) error
	GetById(ctx context.Context, id string) (*preapprovedloanPb.LoanOffer, error)
	GetActiveOfferByActorIdAndVendor(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor) (*preapprovedloanPb.LoanOffer, error)
	DeactivateLoanOffer(ctx context.Context, loanOfferId string) error
	GetLatestByActorIdAndVendor(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor) (*preapprovedloanPb.LoanOffer, error)
	GetByOfferEligibilityCriteriaId(ctx context.Context, loecId string, count int, orderByDesc bool) ([]*preapprovedloanPb.LoanOffer, error)
	GetActiveOffersByActorIdAndLoanPrograms(ctx context.Context, actorId string, loanPrograms []preapprovedloanPb.LoanProgram) ([]*preapprovedloanPb.LoanOffer, error)
	// Deprecated: GetActiveOfferByActorIdVendorAndLoanProgram is deprecated in favour of GetActiveOffersByActorIdAndLoanPrograms
	GetActiveOfferByActorIdVendorAndLoanProgram(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor, loanProgram preapprovedloanPb.LoanProgram) (*preapprovedloanPb.LoanOffer, error)
	GetLatestByActorIdVendorAndLoanProgram(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor, loanProgram preapprovedloanPb.LoanProgram) (*preapprovedloanPb.LoanOffer, error)
}

type LoanAccountsDao interface {
	GetOrCreate(ctx context.Context, account *preapprovedloanPb.LoanAccount) (*preapprovedloanPb.LoanAccount, error)
	Update(ctx context.Context, account *preapprovedloanPb.LoanAccount, updateMasks []preapprovedloanPb.LoanAccountFieldMask) error
	GetById(ctx context.Context, id string) (*preapprovedloanPb.LoanAccount, error)
	GetByAccountNumberAndIfsc(ctx context.Context, loanAccountNumber string, ifsc string) (*preapprovedloanPb.LoanAccount, error)
	GetByActorIdAndVendor(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor) ([]*preapprovedloanPb.LoanAccount, error)
	// GetActiveLoanAccountsByActorIdAndVendor returns all the active loan accounts of user using their actor id
	GetActiveLoanAccountsByActorIdAndVendor(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor) ([]*preapprovedloanPb.LoanAccount, error)
	GetByIds(ctx context.Context, ids []string) ([]*preapprovedloanPb.LoanAccount, error)
	GetByActorIdStatusAndLoanProgram(ctx context.Context, actorId string, accountStatus []preapprovedloanPb.LoanAccountStatus, loanPrograms []preapprovedloanPb.LoanProgram) ([]*preapprovedloanPb.LoanAccount, error)
	// Deprecated: GetByActorIdVendorAndLoanProgram is deprecated in favour of GetByActorIdStatusAndLoanProgram
	GetByActorIdVendorAndLoanProgram(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor, loanProgram preapprovedloanPb.LoanProgram) ([]*preapprovedloanPb.LoanAccount, error)
	GetByAccountType(ctx context.Context, accountType preapprovedloanPb.LoanType) ([]*preapprovedloanPb.LoanAccount, error)
	GetByAccountNumberAndVendor(ctx context.Context, loanAccountNumber string, vendor preapprovedloanPb.Vendor) (*preapprovedloanPb.LoanAccount, error)
	Create(ctx context.Context, account *preapprovedloanPb.LoanAccount) (*preapprovedloanPb.LoanAccount, error)
	GetLoanAccountByPartnerLmsAndPartnerLmsLoanId(ctx context.Context, lmsPartner enums.LmsPartner, partnerLMSloanId string) (*preapprovedloanPb.LoanAccount, error)
	// GetIdsByPartnerLms returns all the loan account ids with the given partner lms and other optional filters
	GetIdsByPartnerLms(ctx context.Context, lmsPartner enums.LmsPartner, filterOptions ...storagev2.FilterOption) ([]string, error)
	// GetActiveAccountIdsByLoanProgramAndInstDueDateRange returns all the active loan account ids with given loan program where there is at least one installment due in the given date range
	// startDate and endDate both are inclusive
	GetActiveAccountIdsByLoanProgramAndInstDueDateRange(ctx context.Context, startDate *date.Date, endDate *date.Date, loanProgram preapprovedloanPb.LoanProgram, filterOptions ...storagev2.FilterOption) ([]string, error)
}

type LoanStepExecutionsDao interface {
	Create(ctx context.Context, stepExecution *preapprovedloanPb.LoanStepExecution) (*preapprovedloanPb.LoanStepExecution, error)
	Update(ctx context.Context, stepExecution *preapprovedloanPb.LoanStepExecution, updateMasks []preapprovedloanPb.LoanStepExecutionFieldMask) error
	GetById(ctx context.Context, id string, filterOptions ...storagev2.FilterOption) (*preapprovedloanPb.LoanStepExecution, error)
	GetByActorId(ctx context.Context, actorId string) ([]*preapprovedloanPb.LoanStepExecution, error)
	GetByOrchId(ctx context.Context, orchId string) (*preapprovedloanPb.LoanStepExecution, error)
	GetLatestByRefIdAndFlow(ctx context.Context, refId string, flow preapprovedloanPb.LoanStepExecutionFlow) (*preapprovedloanPb.LoanStepExecution, error)
	GetByRefIdAndFlowAndName(ctx context.Context, refId string, flow preapprovedloanPb.LoanStepExecutionFlow, name preapprovedloanPb.LoanStepExecutionStepName) (*preapprovedloanPb.LoanStepExecution, error)
	GetByRefIdAndStatuses(ctx context.Context, refId string, statuses []preapprovedloanPb.LoanStepExecutionStatus) ([]*preapprovedloanPb.LoanStepExecution, error)
	GetActiveByRefIdFlowAndName(ctx context.Context, refId string, flow preapprovedloanPb.LoanStepExecutionFlow, name preapprovedloanPb.LoanStepExecutionStepName) (*preapprovedloanPb.LoanStepExecution, error)
}

type LoanInstallmentInfoDao interface {
	Create(ctx context.Context, installmentInfo *preapprovedloanPb.LoanInstallmentInfo) (*preapprovedloanPb.LoanInstallmentInfo, error)
	Update(ctx context.Context, installmentInfo *preapprovedloanPb.LoanInstallmentInfo, updateMasks []preapprovedloanPb.LoanInstallmentInfoFieldMask) error
	GetById(ctx context.Context, id string) (*preapprovedloanPb.LoanInstallmentInfo, error)
	GetByActiveAccountId(ctx context.Context, accountId string) (*preapprovedloanPb.LoanInstallmentInfo, error)
	GetByAccountIdAndStatuses(ctx context.Context, accountId string, statuses []preapprovedloanPb.LoanInstallmentInfoStatus) ([]*preapprovedloanPb.LoanInstallmentInfo, error)
	GetByNextInstallmentDateAndStatus(ctx context.Context, nextInstallmentDate *time.Time, status preapprovedloanPb.LoanInstallmentInfoStatus) ([]*preapprovedloanPb.LoanInstallmentInfo, error)
}

type LoanInstallmentPayoutDao interface {
	Create(ctx context.Context, installmentPayout *preapprovedloanPb.LoanInstallmentPayout) (*preapprovedloanPb.LoanInstallmentPayout, error)
	Update(ctx context.Context, installmentPayout *preapprovedloanPb.LoanInstallmentPayout, updateMasks []preapprovedloanPb.LoanInstallmentPayoutFieldMask) error
	GetById(ctx context.Context, id string) (*preapprovedloanPb.LoanInstallmentPayout, error)
	GetByLoanInstallmentInfoId(ctx context.Context, id string, filterOptions ...storagev2.FilterOption) ([]*preapprovedloanPb.LoanInstallmentPayout, error)
	GetByLoanInstallmentInfoIds(ctx context.Context, ids []string) ([]*preapprovedloanPb.LoanInstallmentPayout, error)
	GetCountByInstallmentInfoIds(ctx context.Context, installmentIds []string) (int64, error)
}

type LoanActivityDao interface {
	Create(ctx context.Context, activity *preapprovedloanPb.LoanActivity) (*preapprovedloanPb.LoanActivity, error)
	Update(ctx context.Context, account *preapprovedloanPb.LoanActivity, updateMasks []preapprovedloanPb.LoanActivityFieldMask) error
	GetById(ctx context.Context, id string) (*preapprovedloanPb.LoanActivity, error)
	GetByReferenceIdAndType(ctx context.Context, referenceId string, activityType preapprovedloanPb.LoanActivityType) (*preapprovedloanPb.LoanActivity, error)
	// TODO(vikas): Add UTs
	GetByAccountIdAndReferenceId(ctx context.Context, accountId string, referenceId string) (*preapprovedloanPb.LoanActivity, error)
	GetByAccountIdAndTypesAndCount(ctx context.Context, accountId string, types []preapprovedloanPb.LoanActivityType, count int) ([]*preapprovedloanPb.LoanActivity, error)
	GetByAccountIdsandCount(ctx context.Context, accountIds []string, count int) ([]*preapprovedloanPb.LoanActivity, error)
	GetByAccountIdsPaginated(ctx context.Context, accountIds []string, pageToken *pagination.PageToken, pageSize uint32) ([]*preapprovedloanPb.LoanActivity, *rpcPb.PageContextResponse, error)
}

type LoanPaymentRequestsDao interface {
	Create(ctx context.Context, loanPaymentRequest *preapprovedloanPb.LoanPaymentRequest) (*preapprovedloanPb.LoanPaymentRequest, error)
	Update(ctx context.Context, loanPaymentRequest *preapprovedloanPb.LoanPaymentRequest, updateMasks []preapprovedloanPb.LoanPaymentRequestFieldMask) error
	GetById(ctx context.Context, id string) (*preapprovedloanPb.LoanPaymentRequest, error)
	GetByOrchId(ctx context.Context, orchId string) (*preapprovedloanPb.LoanPaymentRequest, error)
	GetLatestByAccountIdAndStatuses(ctx context.Context, accountId string, statuses []preapprovedloanPb.LoanPaymentRequestStatus) (*preapprovedloanPb.LoanPaymentRequest, error)
	GetByAccountIdAndStatuses(ctx context.Context, accountId string, statuses []preapprovedloanPb.LoanPaymentRequestStatus, filterOptions ...storagev2.FilterOption) ([]*preapprovedloanPb.LoanPaymentRequest, error)
	GetByActorIdAndStatuses(ctx context.Context, actorId string, statuses []preapprovedloanPb.LoanPaymentRequestStatus, filterOptions ...storagev2.FilterOption) ([]*preapprovedloanPb.LoanPaymentRequest, error)
	// GetByTimeRange returns all the loan payment requests with created_at between from time (inclusive) and to time
	// (exclusive) and with status in statuses and type in types
	GetByTimeRange(
		ctx context.Context,
		from time.Time,
		to time.Time,
		statuses []preapprovedloanPb.LoanPaymentRequestStatus,
		types []preapprovedloanPb.LoanPaymentRequestType,
	) ([]*preapprovedloanPb.LoanPaymentRequest, error)
}

type LoanApplicantDao interface {
	Create(ctx context.Context, loanApplicant *preapprovedloanPb.LoanApplicant) (*preapprovedloanPb.LoanApplicant, error)
	Update(ctx context.Context, loanApplicant *preapprovedloanPb.LoanApplicant, updateMasks []preapprovedloanPb.LoanApplicantFieldMask) error
	GetById(ctx context.Context, id string) (*preapprovedloanPb.LoanApplicant, error)
	GetByActorId(ctx context.Context, actorId string) (*preapprovedloanPb.LoanApplicant, error)
	GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor, loanProgram preapprovedloanPb.LoanProgram, programVersion enums.LoanProgramVersion) (*preapprovedloanPb.LoanApplicant, error)
	GetByVendorRequestId(ctx context.Context, id string) (*preapprovedloanPb.LoanApplicant, error)
	GetBatchByVendorApplicantIds(ctx context.Context, vendorApplicantIds []string) ([]*preapprovedloanPb.LoanApplicant, error)
}

type PartnerLmsUserDao interface {
	Create(ctx context.Context, partnerLmsUser *preapprovedloanPb.PartnerLmsUser) (*preapprovedloanPb.PartnerLmsUser, error)
	GetByLmsPartnerAndActorId(ctx context.Context, lmsPartner enums.LmsPartner, actorId string) (*preapprovedloanPb.PartnerLmsUser, error)
	GetByLmsPartnerAndLmsPartnerUserId(ctx context.Context, lmsPartner enums.LmsPartner, lmsPartnerUserId string) (*preapprovedloanPb.PartnerLmsUser, error)
	Update(ctx context.Context, partnerLmsUser *preapprovedloanPb.PartnerLmsUser, updateMasks []enums.PartnerLmsUserFieldMask) error
}

type MandateRequestDao interface {
	// Create inserts a new MandateRequest.
	Create(ctx context.Context, mandateRequest *preapprovedloanPb.MandateRequest) (*preapprovedloanPb.MandateRequest, error)
	// UpdateById updates an existing MandateRequest by ID.
	UpdateById(ctx context.Context, mandateRequest *preapprovedloanPb.MandateRequest, updateMasks []preapprovedloanPb.MandateRequestFieldMask) error
	// GetById retrieves a MandateRequest by ID.
	GetById(ctx context.Context, id string) (*preapprovedloanPb.MandateRequest, error)
	// GetByActorIdAndVendorAndLoanProgramPaginated retrieves paginated MandateRequests by actor ID, vendor, and loan program.
	GetByActorIdAndVendorAndLoanProgramPaginated(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor, loanProgram preapprovedloanPb.LoanProgram, pageToken *pagination.PageToken, pageSize uint32) ([]*preapprovedloanPb.MandateRequest, *rpcPb.PageContextResponse, error)
	// GetLatestByActorIdAndVendorAndLoanProgram retrieves the latest MandateRequest by actor ID, vendor, and loan program.
	GetLatestByActorIdAndVendorAndLoanProgram(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor, loanProgram preapprovedloanPb.LoanProgram) (*preapprovedloanPb.MandateRequest, error)
	// GetByVendorMandateId retrieves a MandateRequest by vendor mandate ID.
	GetByVendorMandateId(ctx context.Context, vendorMandateId string) (*preapprovedloanPb.MandateRequest, error)
}

type PreEligibilityOfferDao interface {
	Create(ctx context.Context, offer *preapprovedloanPb.PreEligibilityOffer) (*preapprovedloanPb.PreEligibilityOffer, error)
	GetActiveOfferByActorId(ctx context.Context, actorId string) (*preapprovedloanPb.PreEligibilityOffer, error)
	GetByLoanRequestId(ctx context.Context, lrId string) (*preapprovedloanPb.PreEligibilityOffer, error)
}
