package developer

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/developer"
	"github.com/epifi/gamma/preapprovedloan/dao"
	palHelper "github.com/epifi/gamma/preapprovedloan/helper"
	dao2 "github.com/epifi/gamma/preapprovedloan/secured_loans/dao"

	"gorm.io/gorm"
)

type PreApprovedLoanDevEntity struct {
	developer.UnimplementedDevPreApprovedLoanServer
	fac *DevFactory

	loanRequestDao                  dao.LoanRequestsDao
	loanAccountDao                  dao.LoanAccountsDao
	loanOfferDao                    dao.LoanOffersDao
	loanStepDao                     dao.LoanStepExecutionsDao
	loanOfferEligibilityCriteriaDao dao.LoanOfferEligibilityCriteriaDao
	loanActivityDao                 dao.LoanActivityDao
	loanInstallmentInfoDao          dao.LoanInstallmentInfoDao
	loanInstallmentPayoutDao        dao.LoanInstallmentPayoutDao
	loanPaymentRequestDao           dao.LoanPaymentRequestsDao
	partnerLmsUserDao               dao.PartnerLmsUserDao
	fetchedAssetDao                 dao2.FetchedAssetDao
	mandateRequestDao               dao.MandateRequestDao
}

func NewPreApprovedLoanDevEntity(
	fac *DevFactory,
	loanRequestDao dao.LoanRequestsDao,
	loanAccountDao dao.LoanAccountsDao,
	loanOfferDao dao.LoanOffersDao,
	loanStepDao dao.LoanStepExecutionsDao,
	loanOfferEligibilityCriteriaDao dao.LoanOfferEligibilityCriteriaDao,
	loanActivityDao dao.LoanActivityDao,
	loanInstallmentInfoDao dao.LoanInstallmentInfoDao,
	loanInstallmentPayoutDao dao.LoanInstallmentPayoutDao,
	partnerLmsUsersDao dao.PartnerLmsUserDao,
	loanPaymentRequestDao dao.LoanPaymentRequestsDao,
	fetchedAssetDao dao2.FetchedAssetDao,
	mandateRequestsDao dao.MandateRequestDao,
) *PreApprovedLoanDevEntity {
	return &PreApprovedLoanDevEntity{
		fac:                             fac,
		loanRequestDao:                  loanRequestDao,
		loanAccountDao:                  loanAccountDao,
		loanOfferDao:                    loanOfferDao,
		loanStepDao:                     loanStepDao,
		loanOfferEligibilityCriteriaDao: loanOfferEligibilityCriteriaDao,
		loanActivityDao:                 loanActivityDao,
		loanInstallmentInfoDao:          loanInstallmentInfoDao,
		loanInstallmentPayoutDao:        loanInstallmentPayoutDao,
		loanPaymentRequestDao:           loanPaymentRequestDao,
		partnerLmsUserDao:               partnerLmsUsersDao,
		fetchedAssetDao:                 fetchedAssetDao,
		mandateRequestDao:               mandateRequestsDao,
	}
}

func (c *PreApprovedLoanDevEntity) GetEntityList(ctx context.Context, req *cxDsPb.GetEntityListRequest) (*cxDsPb.GetEntityListResponse, error) {
	return &cxDsPb.GetEntityListResponse{
		Status: rpcPb.StatusOk(),
		EntityList: []string{
			developer.PreApprovedLoanEntity_LOAN_REQUESTS.String(),
			developer.PreApprovedLoanEntity_LOAN_OFFERS.String(),
			developer.PreApprovedLoanEntity_LOAN_ACCOUNTS.String(),
			developer.PreApprovedLoanEntity_LOAN_STEP_EXECUTIONS.String(),
			developer.PreApprovedLoanEntity_LOAN_OFFER_ELIGIBILITY_CRITERIA.String(),
			developer.PreApprovedLoanEntity_LOAN_INSTALLMENT_INFO.String(),
			developer.PreApprovedLoanEntity_LOAN_INSTALLMENT_PAYOUT.String(),
			developer.PreApprovedLoanEntity_LOAN_ACTIVITY.String(),
			developer.PreApprovedLoanEntity_LOAN_PAYMENT_REQUEST.String(),
			developer.PreApprovedLoanEntity_LOAN_APPLICANTS.String(),
			developer.PreApprovedLoanEntity_PARTNER_LMS_USER.String(),
			developer.PreApprovedLoanEntity_FETCHED_ASSET.String(),
			developer.PreApprovedLoanEntity_MANDATE_REQUESTS.String(),
			developer.PreApprovedLoanEntity_PRE_ELIGIBILITY_OFFER.String(),
		},
	}, nil
}

func (c *PreApprovedLoanDevEntity) GetParameterList(ctx context.Context, req *cxDsPb.GetParameterListRequest) (*cxDsPb.GetParameterListResponse, error) {
	ent, ok := developer.PreApprovedLoanEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetParameterListResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in preapprovedloan"),
		}, nil
	}
	paramFetcher, err := c.fac.getParameterListImpl(developer.PreApprovedLoanEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available in preapprovedloan")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	paramList, err := paramFetcher.FetchParamList(ctx, developer.PreApprovedLoanEntity(ent))
	if err != nil {
		logger.Error(ctx, "unable to fetch parameter list")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetParameterListResponse{
		Status:        rpcPb.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (c *PreApprovedLoanDevEntity) GetData(ctx context.Context, req *cxDsPb.GetDataRequest) (*cxDsPb.GetDataResponse, error) {
	ent, ok := developer.PreApprovedLoanEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetDataResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in preapprovedloan"),
		}, nil
	}
	dataFetcher, err := c.fac.getDataImpl(developer.PreApprovedLoanEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available in preapprovedloan")
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	jsonResp, err := dataFetcher.FetchData(ctx, developer.PreApprovedLoanEntity(ent), req.GetFilters())
	if err != nil {
		logger.Error(ctx, "unable to get data")
		if errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &cxDsPb.GetDataResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &cxDsPb.GetDataResponse{
		Status:       rpcPb.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}

func (c *PreApprovedLoanDevEntity) UpdateLoanStepStatus(ctx context.Context, req *developer.UpdateLoanStepStatusRequest) (*developer.UpdateLoanStepStatusResponse, error) {
	lse, lseErr := c.loanStepDao.GetById(ctx, req.GetLoanStepId())
	if lseErr != nil {
		return &developer.UpdateLoanStepStatusResponse{Status: rpcPb.StatusInternal()}, nil
	}

	lr, lrErr := c.loanRequestDao.GetById(ctx, lse.GetRefId())
	if lrErr != nil {
		return &developer.UpdateLoanStepStatusResponse{Status: rpcPb.StatusInternal()}, nil
	}
	ctx = epificontext.WithOwnership(ctx, palHelper.GetPalOwnership(lr.GetVendor()))

	lse.Status = req.GetLoanStepStatus()
	updateErr := c.loanStepDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
	})
	if updateErr != nil {
		return &developer.UpdateLoanStepStatusResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &developer.UpdateLoanStepStatusResponse{Status: rpcPb.StatusOk()}, nil
}

//nolint:funlen
func (s *PreApprovedLoanDevEntity) CreateLoanOffer(ctx context.Context, req *developer.CreateLoanOfferRequest) (*developer.CreateLoanOfferResponse, error) {
	var loanOffer *palPb.LoanOffer
	loanOffer = &palPb.LoanOffer{
		ActorId:       req.GetActorId(),
		VendorOfferId: uuid.NewString(),
		Vendor:        req.GetLoanHeader().GetVendor(),
		OfferConstraints: &palPb.OfferConstraints{
			MaxLoanAmount:   money.ParseFloat(1000000, money.RupeeCurrencyCode),
			MaxEmiAmount:    money.ParseFloat(100000, money.RupeeCurrencyCode),
			MaxTenureMonths: 48,
		},
		ProcessingInfo: &palPb.OfferProcessingInfo{
			Gst: 18,
			InterestRate: []*palPb.RangeData{
				{
					Start: money.ParseFloat(1000, money.RupeeCurrencyCode).GetUnits(),
					End:   money.ParseFloat(1000000, money.RupeeCurrencyCode).GetUnits(),
					Value: &palPb.RangeData_Percentage{
						Percentage: 13,
					},
				},
			},
			ProcessingFee: []*palPb.RangeData{
				{
					Start: money.ParseFloat(1000, money.RupeeCurrencyCode).GetUnits(),
					End:   money.ParseFloat(1000000, money.RupeeCurrencyCode).GetUnits(),
					Value: &palPb.RangeData_Percentage{
						Percentage: 1.2,
					},
				},
			},
			ApplicationId: idgen.RandAlphaNumericString(25),
		},
		ValidSince:  timestampPb.Now(),
		ValidTill:   timestampPb.New(time.Unix(time.Now().Add(time.Hour*24).Unix(), 0)),
		LoanProgram: req.GetLoanHeader().GetLoanProgram(),
	}

	switch req.GetLoanHeader().GetVendor() {
	case palPb.Vendor_ABFL:
		ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_LOANS_ABFL)
	case palPb.Vendor_LIQUILOANS:
		ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_LIQUILOANS_PL)
	case palPb.Vendor_IDFC:
		ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_IDFC_PL)
	case palPb.Vendor_FEDERAL:
		ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_FEDERAL_BANK)
	case palPb.Vendor_MONEYVIEW:
		ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_MONEYVIEW_PL)
	case palPb.Vendor_EPIFI_TECH:
		ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_TECH_V2)
	case palPb.Vendor_STOCK_GUARDIAN_LSP:
		ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP)
	default:
		logger.Error(ctx, "unable to find the vendor")
		return &developer.CreateLoanOfferResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	_, err := s.loanOfferDao.Create(ctx, loanOffer)
	if err != nil {
		logger.Error(ctx, "unable to create the loan offer", zap.Error(err))
		return &developer.CreateLoanOfferResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &developer.CreateLoanOfferResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}
