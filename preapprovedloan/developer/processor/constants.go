package processor

const (
	ActorId                    = "actor_id"
	ActorIdLabel               = "Actor ID"
	OfferId                    = "offer_id"
	OfferIDLabel               = "Offer ID"
	marshalErr                 = "{\"error\": \"error in marshaling struct to json\"}"
	LoanAccountId              = "loan_account_id"
	LoanRequestId              = "loan_request_id"
	VendorRequestId            = "vendor_request_id"
	LoanAccountIdLabel         = "Loan Account Id"
	LoanInstallmentInfoId      = "loan_installment_info_id"
	LoanInstallmentInfoIdLabel = "Loan Installment Info Id"
	Vendor                     = "Vendor"
	LoanProgram                = "Loan Program"
	AccountNumber              = "account_number"
	AccountNumberLabel         = "Account Number"
	ApplicantId                = "applicant_id"
	ApplicantIdLabel           = "Applicant ID"
	Lender                     = "Lender"
	LmsPartner                 = "lms_partner"
	LmsPartnerLabel            = "Lms Partner"
	LoanRequestTypeLabel       = "Loan Request Type"
	LoanRequestType            = "loan_request_type"
	Id                         = "id"
	IdLabel                    = "Id"
	OrchId                     = "orch_id"
	OrchIdLabel                = "Orch Id"
	Type                       = "type"
	TypeLabel                  = "Type"
	LoanStepExecutionId        = "loan_step_execution_id"
	LoanStepExecutionIdLabel   = "Loan Step Execution Id"
	ProgramVersion             = "program_version"
	ProgramVersionLabel        = "Program Version"
	VendorMandateIdLabel       = "Vendor Mandate Id"
	VendorMandateId            = "vendor_mandate_id"
)
