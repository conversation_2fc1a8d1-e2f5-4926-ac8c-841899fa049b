package lenden

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/gamma/api/frontend/analytics"
	"github.com/epifi/gamma/api/frontend/deeplink"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"
)

type Provider struct {
	*baseprovider.Provider
}

func NewLendenProvider(baseProvider *baseprovider.Provider) *Provider {
	return &Provider{Provider: baseProvider}
}

var _ provider.IDeeplinkProvider = &Provider{}

func (p *Provider) GetLoanHeader() *palFeEnumsPb.LoanHeader {
	return &palFeEnumsPb.LoanHeader{
		LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		Vendor:      palFeEnumsPb.Vendor_LENDEN,
	}
}

// START: Lenden KYC
func (p *Provider) getKycWebViewScreenDeeplink(lh *palFeEnumsPb.LoanHeader, loanRequestId string, entryUrl string, backOff uint32) (*deeplink.Deeplink, error) {
	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN, &palTypesPb.LoansWebviewWithStatusPollScreen{
		LoanHeader:              lh,
		EntryUrl:                entryUrl,
		RetryBackoff:            backOff,
		PageTitle:               commontypes.GetPlainStringText("Lenden Kyc").WithFontStyle(commontypes.FontStyle_HEADLINE_M).WithFontColor("#313234"),
		LoanRequestId:           loanRequestId,
		AnalyticsScreenName:     analytics.AnalyticsScreenName_RE_KYC_SCREEN, // TODO: is it needed to update
		ShouldOpenInExternalTab: false,
		// KYC process for Lenden involves the user performing liveness using their device camera
		CameraPermissionRequired: true,
	})
}

// Digilocker screen is used as kyc init screen.
func (p *Provider) GetKycIntroScreenDeeplink(ctx context.Context, lh *palFeEnumsPb.LoanHeader, loanRequestId, lseId, entryUrl string, retryBackoff uint32, showKycFailureBanner bool, _ commontypes.Platform) (*deeplinkPb.Deeplink, error) {
	webviewDl, webviewDlErr := p.getKycWebViewScreenDeeplink(lh, loanRequestId, entryUrl, retryBackoff)
	if webviewDlErr != nil {
		return nil, errors.Wrap(webviewDlErr, "error getting abfl kyc webview screen deeplink")
	}

	var components []*palTypesPb.LoansInfoScreenOptions_Component
	if showKycFailureBanner {
		components = []*palTypesPb.LoansInfoScreenOptions_Component{
			{
				Component: &palTypesPb.LoansInfoScreenOptions_Component_BannerInfo{BannerInfo: &palTypesPb.BannerWithLeftIconAndBottomRightCta{
					LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/digilocker-abfl-banner-i-icon.png").WithProperties(&commontypes.VisualElementProperties{
						Width:  24,
						Height: 24,
					}),
					Title: &ui.IconTextComponent{Texts: []*commontypes.Text{
						commontypes.GetTextFromStringFontColourFontStyle(
							"Something went wrong with your KYC",
							"#6A6D70",
							commontypes.FontStyle_BODY_XS,
						),
					},
						ContainerProperties: &ui.IconTextComponent_ContainerProperties{
							BgColor: "#EFF2F6",
						},
					},
					BgColor: "#EFF2F6",
				}},
			},
		}
	}

	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_INFO_SCREEN, &palTypesPb.LoansInfoScreenOptions{
		LoanHeader:    lh,
		LoanRequestId: loanRequestId,
		BgColor:       "#FFFFFF",
		CenterElement: &widget.VisualElementTitleSubtitleElement{
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/digilocker-abfl-icon.png").WithProperties(&commontypes.VisualElementProperties{
				Width:  280,
				Height: 280,
			}),
			TitleText: commontypes.GetTextFromStringFontColourFontStyle(
				"Let’s verify your KYC",
				"#313234",
				commontypes.FontStyle_HEADLINE_L,
			),
			SubtitleText: commontypes.GetTextFromStringFontColourFontStyle(
				"You’ll be redirected to our partner's KYC verification screen",
				"#929599",
				commontypes.FontStyle_BODY_S,
			),
		},
		ContinueButton: &deeplinkPb.Button{
			Text:        helper.GetText("Continue", "#FFFFFF", "#00BB98", commontypes.FontStyle_BUTTON_M),
			WrapContent: false,
			Padding: &deeplinkPb.Button_Padding{
				LeftPadding:   24,
				RightPadding:  24,
				TopPadding:    12,
				BottomPadding: 12,
			},
			Margin: &deeplinkPb.Button_Margin{
				LeftMargin:   24,
				RightMargin:  24,
				BottomMargin: 16,
			},
			Cta: &deeplinkPb.Cta{
				Text:         "Continue",
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				Type:         deeplinkPb.Cta_DONE,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Deeplink:     webviewDl,
			},
		},
		LseId:      lseId,
		Components: components,
	})
	return dl, dlErr
}

// END: Lenden KYC

// Start: Esign screens
func (p *Provider) GetInitiateESignScreen(lh *palFeEnumsPb.LoanHeader, loanRequestId string, documentUrl string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_INITIATE_ESIGN_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanInitiateEsignScreenOptions{
			PreApprovedLoanInitiateEsignScreenOptions: &deeplinkPb.PreApprovedLoanInitiateEsignScreenOptions{
				IconUrl:  "https://epifi-icons.pointz.in/preapprovedloan/initiate_esign_for_sg.png",
				Title:    "All set!\nReview & confirm\nthe loan agreement",
				SubTitle: "This is required by our partner-regulated entity",
				Continue: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_CUSTOM,
					Text:         "View agreement",
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
					Deeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_E_SIGN_VIEW_DOCUMENT_SCREEN,
						ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanESignViewDocumentScreenOptions{
							PreApprovedLoanESignViewDocumentScreenOptions: &deeplinkPb.PreApprovedLoanESignViewDocumentScreenOptions{
								Agree: &deeplinkPb.Cta{
									Type:         deeplinkPb.Cta_CUSTOM,
									Text:         "Continue",
									DisplayTheme: deeplinkPb.Cta_PRIMARY,
									Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
								},
								LoanHeader:    lh,
								LoanRequestId: loanRequestId,
								DocumentUrl:   documentUrl,
								LoanDocType:   palFeEnumsPb.LoanDocType_LOAN_DOC_TYPE_KFS, // will be used by client to forward it in getDeepLink rpc
							},
						},
					},
				},
				LoanRequestId: loanRequestId,
				LoanHeader:    lh,
				ToolbarTitle:  helper.GetText("Instant Loan", "#383838", "", commontypes.FontStyle_HEADLINE_2),
			},
		},
	}
}

// End: Esign screens

// MANDATE Screens

// GetLoansMandateSetupScreen generates the webview screen with url received from vendor
func (p *Provider) GetLoansMandateSetupScreen(ctx context.Context, lh *palFeEnumsPb.LoanHeader, actorId string, loanRequestId string, lseId string, params *provider.LoansMandateSetupScreenParams) (*deeplinkPb.Deeplink, error) {
	dl, err := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN, &palTypesPb.LoansWebviewWithStatusPollScreen{
		LoanHeader:               lh,
		EntryUrl:                 params.WebViewParams.EntryUrl,
		RetryBackoff:             60_000,
		PageTitle:                commontypes.GetPlainStringText("Lenden Mandate Setup").WithFontStyle(commontypes.FontStyle_HEADLINE_M).WithFontColor("#313234"),
		LoanRequestId:            loanRequestId,
		AnalyticsScreenName:      analytics.AnalyticsScreenName_PL_MANDATE_SETUP_SCREEN,
		ShouldOpenInExternalTab:  false,
		CameraPermissionRequired: false,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error while generating loans mandate setup screen deeplink")
	}
	return dl, nil
}

func getMandateBankAccountDetails(accDetails *palPb.MandateData_BankingDetails_AccountDetails) *palTypesPb.MandateBankAccountDetails {
	return &palTypesPb.MandateBankAccountDetails{
		AccountNumber:     accDetails.GetAccountNumber(),
		AccountHolderName: accDetails.GetAccountHolderName(),
		IfscCode:          accDetails.GetIfscCode(),
		BankName:          accDetails.GetBankName(),
	}
}

// GetLoansMandateInitiateScreenV2 generates the mandate initiation screen v2
func (p *Provider) GetLoansMandateInitiateScreenV2(ctx context.Context, lh *palFeEnumsPb.LoanHeader, loanRequestId string, lseId string, params *provider.LoansMandateInitiateScreenV2Params) (*deeplinkPb.Deeplink, error) {
	defaultAccountDetails := params.DefaultMandateBankAccDetails
	var accountDetailsView *palTypesPb.AccountDetailsView
	if defaultAccountDetails != nil {
		bankAcc := fmt.Sprintf("%s a/c", defaultAccountDetails.GetBankName())
		lastFourAccDigit := defaultAccountDetails.GetAccountNumber()[len(defaultAccountDetails.GetAccountNumber())-4:]
		maskedAccNumber := fmt.Sprintf("••%s", lastFourAccDigit)
		var changeButton *ui.IconTextComponent
		if params.AllowUserToChangeDefaultAccount {
			// check if deeplink is hardcoded on the client?
			changeButton = ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Change", colors.ColorLightPrimaryAction, commontypes.FontStyle_BUTTON_S)).WithContainerProperties(&ui.IconTextComponent_ContainerProperties{
				BgColor: "#EFF2F6",
			})
		} else {
			changeButton = ui.NewITC() // no change button
		}
		accountDetailsView = &palTypesPb.AccountDetailsView{
			RawAccountDetails: getMandateBankAccountDetails(defaultAccountDetails),
			AccountDisplayInfo: &palTypesPb.AccountDisplayInfo{
				DisplayInfo: &palTypesPb.AccountDisplayInfo_LeftVisualInfo{
					LeftVisualInfo: &palTypesPb.LeftVisualElementWithTextInfoAndCta{
						LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(p.GetBankLogo(ctx, defaultAccountDetails), 48, 48),
						Text:     helper.GetText(bankAcc, "#313234", "", commontypes.FontStyle_SUBTITLE_S),
						SubText:  helper.GetText(maskedAccNumber, "#929599", "", commontypes.FontStyle_SUBTITLE_XS),
						Action:   changeButton,
						BgColor:  &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#EFF2F6"}},
					},
				},
			},
		}
		if params.AllowUserToChangeDefaultAccount && params.AlternateAccountsScreenDeeplink != nil {
			accountDetailsView.GetAccountDisplayInfo().GetLeftVisualInfo().GetAction().Deeplink = params.AlternateAccountsScreenDeeplink
		}
	} else {
		accountDetailsView = nil
	}

	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_MANDATE_INITIATE_SCREEN_V2, &palTypesPb.LoansMandateInitiateV2ScreenOptions{
		LoanHeader:    lh,
		LoanRequestId: loanRequestId,
		LseId:         lseId,
		TopComponent: &widget.VisualElementTitleSubtitleElement{
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/lending/mandate_logo.png").WithProperties(&commontypes.VisualElementProperties{
				Width:  200,
				Height: 200,
			}),
			TitleText: commontypes.GetTextFromStringFontColourFontStyle(
				"Set up AutoPay now\nand forget about your EMIs",
				"#333333",
				commontypes.FontStyle_HEADLINE_L,
			),
			SubtitleText: commontypes.GetTextFromStringFontColourFontStyle(
				"Automatic repayments, no stress",
				"#8D8D8D",
				commontypes.FontStyle_HEADLINE_S,
			),
		},
		MiddleComponents: []*palTypesPb.LoansMandateInitiateV2ScreenOptions_MiddleComponent{
			{
				Component: &palTypesPb.LoansMandateInitiateV2ScreenOptions_MiddleComponent_KeyValueRowsComponent{
					KeyValueRowsComponent: &palTypesPb.KeyValueRowsComponent{
						ItemList: &palTypesPb.KeyValueRowsComponent_ItemList{
							Items: []*palTypesPb.KeyValueRow{
								{
									Key:   ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/lending/mandate_1.png", 40, 40),
									Value: ui.NewITC().WithTexts(helper.GetText("Add your bank account details", "#6A6D70", "", commontypes.FontStyle_HEADLINE_S)),
								},
								{
									Key:   ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/lending/mandate_2.png", 40, 40),
									Value: ui.NewITC().WithTexts(helper.GetText("We’ll send you ₹1 to verify it", "#6A6D70", "", commontypes.FontStyle_HEADLINE_S)),
								},
								{
									Key:   ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/lending/mandate_3.png", 40, 40),
									Value: ui.NewITC().WithTexts(helper.GetText("That’s it! AutoPay will be ready", "#6A6D70", "", commontypes.FontStyle_HEADLINE_S)),
								},
							},
						},
					},
				},
			},
		},
		AccountDetailsComponent: &palTypesPb.LoansMandateInitiateV2ScreenOptions_AccountDetailsComponent{
			TitleText:                       helper.GetText("Disbursal and EMI account", "#929599", "", commontypes.FontStyle_SUBTITLE_XS),
			SelectedOrDefaultAccountDetails: accountDetailsView,
		},
		Cta: &deeplinkPb.Button{
			Text: helper.GetText("Set up AutoPay", colors.ColorSnow, "#00B899", commontypes.FontStyle_BUTTON_M),
			Padding: &deeplinkPb.Button_Padding{
				LeftPadding:   24,
				RightPadding:  24,
				TopPadding:    12,
				BottomPadding: 12,
			},
			Cta: &deeplinkPb.Cta{
				Text:         "Set up AutoPay",
				Type:         deeplinkPb.Cta_CONTINUE,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				// TODO(Brijesh): Discuss AA behavior with Vikas? Discuss with Ganesh/Suraj on what happens when this is sent in
				/*
					DONE:
						- IN AA FLOW:
						     - Pass ADDBANKING deeplink in from activity -> will be routed to Addbanking details screen
						     - Dont pass deeplink from AddBanking RPC response -> will be routed to mandate setup screen
						- IN NON AA FLOW:
							- Dont pass ADDBANKING deeplink in from activity so it will -> will be routed to mandate setup screen
				*/
				Deeplink: params.NextDeeplink,
			},
		},
		ScreenName:   analytics.AnalyticsScreenName_PL_INITIATE_MANDATE_V2_SCREEN,
		ToolbarTitle: helper.GetText("Instant Loan", "#383838", "", commontypes.FontStyle_HEADLINE_2),
	})
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "error while generating mandate init v2 deeplink")
	}
	return dl, nil
}

// END: mandate

func (p *Provider) GetBankingDetailsScreenDeeplink(lh *palFeEnumsPb.LoanHeader, requestId string, lseId string, _ palFeEnumsPb.SearchIfscType, _ palTypesPb.BankingDetailsBottomSheetType, accountNumberSuffix string) (*deeplinkPb.Deeplink, error) {
	inputFields := []*palTypesPb.PlBankingDetailsScreenOptions_Field{
		{
			InputType: palTypesPb.PlBankingDetailsScreenOptions_Field_INPUT_TYPE_DROPDOWN,
			Hint:      helper.GetText("IFSC code", "#B9B9B9", "", commontypes.FontStyle_SUBTITLE_1),
			FieldType: palTypesPb.PlBankingDetailsScreenOptions_Field_FIELD_TYPE_IFSC,
		},
		{
			InputType: palTypesPb.PlBankingDetailsScreenOptions_Field_INPUT_TYPE_PLAIN_TEXT,
			Hint:      helper.GetText("BANK NAME (FROM YOUR IFSC CODE)", "#B9B9B9", "", commontypes.FontStyle_SUBTITLE_1),
			FieldType: palTypesPb.PlBankingDetailsScreenOptions_Field_FIELD_TYPE_BANK_NAME,
		},
	}
	var (
		accountNumberInputField     *palTypesPb.PlBankingDetailsScreenOptions_Field
		accountHolderNameInputField *palTypesPb.PlBankingDetailsScreenOptions_Field
	)
	if accountNumberSuffix != "" {
		// For users whose Account Aggregator data was pulled and sent to LDC for LDC's hard offer generation,
		// the partial account number (suffix) and the holder name present with us already.
		// LDC allows user to set up the loan repayment mandate on this account only.
		// Hence, we nudge users to enter this account number by showing them the suffix we already have.
		// and don't send the account holder name field as we can fill it from the AA data we already have.
		accountNumberInputField = &palTypesPb.PlBankingDetailsScreenOptions_Field{
			InputType:   palTypesPb.PlBankingDetailsScreenOptions_Field_INPUT_TYPE_PLAIN_TEXT,
			Hint:        helper.GetText("Account Number", "#B9B9B9", "", commontypes.FontStyle_SUBTITLE_1),
			FieldType:   palTypesPb.PlBankingDetailsScreenOptions_Field_FIELD_TYPE_ACCOUNT_NUMBER_PREFIX,
			ValueSuffix: commontypes.GetTextFromStringFontColourFontStyle(accountNumberSuffix, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_1),
		}
		inputFields = append(inputFields, accountNumberInputField)
	} else {
		// For users who don't have a Fi-Federal bank account and whose Account Aggregator data was not needed
		// by LDC for offer generation, we ask them to enter both the full account number and the holder name.
		accountNumberInputField = &palTypesPb.PlBankingDetailsScreenOptions_Field{
			InputType: palTypesPb.PlBankingDetailsScreenOptions_Field_INPUT_TYPE_PLAIN_TEXT,
			Hint:      helper.GetText("Account Number", "#B9B9B9", "", commontypes.FontStyle_SUBTITLE_1),
			FieldType: palTypesPb.PlBankingDetailsScreenOptions_Field_FIELD_TYPE_ACCOUNT_NUMBER,
		}
		inputFields = append(inputFields, accountNumberInputField)
		accountHolderNameInputField = &palTypesPb.PlBankingDetailsScreenOptions_Field{
			InputType: palTypesPb.PlBankingDetailsScreenOptions_Field_INPUT_TYPE_PLAIN_TEXT,
			Hint:      helper.GetText("Account Holder Name", "#B9B9B9", "", commontypes.FontStyle_SUBTITLE_1),
			FieldType: palTypesPb.PlBankingDetailsScreenOptions_Field_FIELD_TYPE_ACCOUNT_HOLDER_NAME,
		}
		inputFields = append(inputFields, accountHolderNameInputField)
	}

	screenOptions := &palTypesPb.PlBankingDetailsScreenOptions{
		LoanHeader:    lh,
		LoanRequestId: requestId,
		LseId:         lseId,
		Title:         helper.GetText("Provide account details", "#313234", "", commontypes.FontStyle_HEADLINE_L),
		Desc:          helper.GetText("You will receive the loan amount in this\nbank account.", "#929599", "", commontypes.FontStyle_BODY_3_PARA),
		Fields:        inputFields,
		ConfirmDetailsButton: &deeplinkPb.Button{
			Text:        helper.GetText("Confirm Account Details", "#FFFFFF", "#00B899", commontypes.FontStyle_BUTTON_M),
			WrapContent: false,
			Padding: &deeplinkPb.Button_Padding{
				LeftPadding:   12,
				RightPadding:  24,
				TopPadding:    12,
				BottomPadding: 24,
			},
			Margin: &deeplinkPb.Button_Margin{
				LeftMargin:   12,
				RightMargin:  32,
				BottomMargin: 15,
				TopMargin:    28,
			},
			Cta: &deeplinkPb.Cta{
				Type:   deeplinkPb.Cta_CUSTOM,
				Status: deeplinkPb.Cta_CTA_STATUS_ENABLED,
			},
		},
	}
	dl, err := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_PL_BANKING_DETAILS_SCREEN, screenOptions)
	if err != nil {
		return nil, errors.New("error in getting deeplink v3 for bank details screen")
	}
	return dl, nil
}

// nolint:ineffassign
func (a *Provider) GetPaymentTerminalScreenDeeplink(ctx context.Context, req *provider.PaymentTerminalScreenDlReq) (*deeplinkPb.Deeplink, error) {
	bottomCtaDl := &deeplinkPb.Cta{
		Type: deeplinkPb.Cta_DONE,
		Text: "Ok, got it",
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_LOANS_OVERVIEW_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&palTypesPb.LoansOverviewScreenOptions{
				LoanId:     req.LoanAccountId,
				LoanHeader: req.LoanHeader,
			}),
		},
		DisplayTheme: deeplinkPb.Cta_SECONDARY,
		Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
	}

	screenOption := &deeplinkPb.PreApprovedLoanActivityStatusScreenOptions{}

	switch req.PaymentStatus {
	case palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_SUCCESS:
		switch req.PaymentType {
		case palPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE:
			screenOption = &deeplinkPb.PreApprovedLoanActivityStatusScreenOptions{
				StatusInfo: &deeplinkPb.InfoItem{
					Icon:  "https://epifi-icons.pointz.in/preapprovedloan/loan_activity_status_success.png",
					Title: "Payment successful!",
					Desc:  "We’ve started the process of closing your loan account",
				},
				MoreDetails: []*deeplinkPb.InfoItem{
					{
						Icon:  "https://epifi-icons.pointz.in/preapprovedloan/loan_activity_status_clipboard.png",
						Title: "Your loan account will be closed later today",
					},
				},
				Cta:        bottomCtaDl,
				LoanHeader: req.LoanHeader,
			}
		default:
			screenOption = &deeplinkPb.PreApprovedLoanActivityStatusScreenOptions{
				StatusInfo: &deeplinkPb.InfoItem{
					Icon:  "https://epifi-icons.pointz.in/preapprovedloan/loan_activity_status_success.png",
					Title: "Payment successful!",
				},
				MoreDetails: []*deeplinkPb.InfoItem{
					{
						Icon:  "https://epifi-icons.pointz.in/preapprovedloan/loan_activity_status_clipboard.png",
						Title: "This payment will show up in the transaction history soon",
					},
				},
				Cta:        bottomCtaDl,
				LoanHeader: req.LoanHeader,
			}
		}
	default:
		screenOption = &deeplinkPb.PreApprovedLoanActivityStatusScreenOptions{
			StatusInfo: &deeplinkPb.InfoItem{
				Icon:  "https://epifi-icons.pointz.in/preapprovedloan/loan_activity_status_failure.png",
				Title: "Payment failed",
				Desc:  "Don't worry, your money is safe. It will return to your account in 2-3 business days.",
			},
			Cta:        bottomCtaDl,
			LoanHeader: req.LoanHeader,
		}
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_ACTIVITY_STATUS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanActivityStatusScreenOptions{
			PreApprovedLoanActivityStatusScreenOptions: screenOption,
		},
	}, nil
}
