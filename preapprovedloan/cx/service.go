// nolint: funlen,dupl
package cx

import (
	"context"
	"errors"
	"fmt"
	"time"

	errors2 "github.com/pkg/errors"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	rpcPb "github.com/epifi/be-common/api/rpc"
	types2 "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	pQueuePb "github.com/epifi/gamma/api/persistentqueue"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palCxPb "github.com/epifi/gamma/api/preapprovedloan/cx"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	segmentPb "github.com/epifi/gamma/api/segment"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/persistentqueue"
	"github.com/epifi/gamma/preapprovedloan/config/genconf"
	"github.com/epifi/gamma/preapprovedloan/cx/factory"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/helper"
	loanDataProvider "github.com/epifi/gamma/preapprovedloan/loan_data_provider"
	multiDbProvider "github.com/epifi/gamma/preapprovedloan/multidb_provider"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/provider"
	recommendationProvider "github.com/epifi/gamma/preapprovedloan/recommendation_engine/provider"
)

type Service struct {
	palCxPb.UnimplementedCxServer
	loanOfferEligibilityCriteriaDao dao.LoanOfferEligibilityCriteriaDao
	loanOffersDao                   dao.LoanOffersDao
	loanRequestsDao                 dao.LoanRequestsDao
	loanAccountsDao                 dao.LoanAccountsDao
	loanStepExecutionsDao           dao.LoanStepExecutionsDao
	loanInstallmentInfoDao          dao.LoanInstallmentInfoDao
	loanInstallmentPayoutDao        dao.LoanInstallmentPayoutDao
	loanActivityDao                 dao.LoanActivityDao
	loanApplicantDao                dao.LoanApplicantDao
	paymentClient                   paymentPb.PaymentClient
	persistentQueue                 persistentqueue.PersistentQueue
	rpcHelper                       *helper.RpcHelper
	dbResourceProvider              *storage.DBResourceProvider[*gorm.DB]
	epifiCRDB                       types2.EpifiCRDB
	loanDataProvider                loanDataProvider.IFactory
	segmentClient                   segmentPb.SegmentationServiceClient
	cxDataProviderFactory           *factory.ProviderFactory
	gconf                           *genconf.Config
	multiDbProvider                 multiDbProvider.IMultiDbProvider
	recommendationEngine            recommendationProvider.ILoanRecommendationEngine
}

func NewService(
	loanOfferEligibilityCriteriaDao dao.LoanOfferEligibilityCriteriaDao,
	loanOffersDao dao.LoanOffersDao,
	loanRequestsDao dao.LoanRequestsDao,
	loanAccountsDao dao.LoanAccountsDao,
	loanStepExecutionsDao dao.LoanStepExecutionsDao,
	loanInstallmentInfoDao dao.LoanInstallmentInfoDao,
	loanInstallmentPayoutDao dao.LoanInstallmentPayoutDao,
	loanActivityDao dao.LoanActivityDao,
	loanApplicantDao dao.LoanApplicantDao,
	paymentClient paymentPb.PaymentClient,
	persistentQueue persistentqueue.PersistentQueue,
	rpcHelper *helper.RpcHelper,
	dbResourceProvider *storage.DBResourceProvider[*gorm.DB],
	epifiCRDB types2.EpifiCRDB,
	loanDataProvider loanDataProvider.IFactory,
	segmentClient segmentPb.SegmentationServiceClient,
	cxDataProviderFactory *factory.ProviderFactory,
	gconf *genconf.Config,
	multiDBDataProvider multiDbProvider.IMultiDbProvider,
	recommendationEngine recommendationProvider.ILoanRecommendationEngine,
) *Service {
	return &Service{
		loanOfferEligibilityCriteriaDao: loanOfferEligibilityCriteriaDao,
		loanOffersDao:                   loanOffersDao,
		loanRequestsDao:                 loanRequestsDao,
		loanAccountsDao:                 loanAccountsDao,
		loanStepExecutionsDao:           loanStepExecutionsDao,
		loanInstallmentInfoDao:          loanInstallmentInfoDao,
		loanInstallmentPayoutDao:        loanInstallmentPayoutDao,
		loanActivityDao:                 loanActivityDao,
		loanApplicantDao:                loanApplicantDao,
		paymentClient:                   paymentClient,
		persistentQueue:                 persistentQueue,
		rpcHelper:                       rpcHelper,
		dbResourceProvider:              dbResourceProvider,
		epifiCRDB:                       epifiCRDB,
		loanDataProvider:                loanDataProvider,
		segmentClient:                   segmentClient,
		cxDataProviderFactory:           cxDataProviderFactory,
		gconf:                           gconf,
		multiDbProvider:                 multiDBDataProvider,
		recommendationEngine:            recommendationEngine,
	}
}

func (s *Service) SubmitManualReview(ctx context.Context, req *palCxPb.SubmitManualReviewRequest) (*palCxPb.SubmitManualReviewResponse, error) {
	res := &palCxPb.SubmitManualReviewResponse{}

	var loanStepExecutionEntry *palPb.LoanStepExecution
	var fetchError error
	// empty orch in case of FM only review cases
	if req.GetOrchId() == "" {
		loanStepExecutionEntry, fetchError = s.fetchLoanStepForQueuePayload(ctx, req.GetActorId())
	} else {
		loanStepExecutionEntry, fetchError = s.loanStepExecutionsDao.GetByOrchId(ctx, req.GetOrchId())
	}
	if fetchError != nil {
		logger.Error(ctx, "error while fetching loan step execution from db", zap.Error(fetchError))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanRequestEntry, fetchLrError := s.loanRequestsDao.GetById(ctx, loanStepExecutionEntry.GetRefId())
	if fetchLrError != nil {
		logger.Error(ctx, "error while fetching loan request from db", zap.Error(fetchLrError))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(loanRequestEntry.GetVendor()))
	// check for if loan application is already in terminal case. Do not update anything in that case.
	if loanRequestEntry.GetCompletedAt() != nil {
		if delErr := s.deleteElememtFromQueue(ctx, loanStepExecutionEntry); delErr != nil {
			logger.Error(ctx, "unable to delete element from PQ", zap.Error(delErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res.Status = rpcPb.StatusOk()
		return res, nil
	}

	if loanStepExecutionEntry.GetDetails().GetManualReviewStepData() == nil {
		loanStepExecutionEntry.Details = &palPb.LoanStepExecutionDetails{
			Details: &palPb.LoanStepExecutionDetails_ManualReviewStepData{
				ManualReviewStepData: &palPb.ManualReviewStepData{}},
		}
	}
	loanStepExecutionEntry.GetDetails().GetManualReviewStepData().ReviewerDetails = &palPb.ManualReviewStepData_ReviewerDetails{
		Email:      req.GetReviewerDetails().GetEmail(),
		ReviewedAt: req.GetReviewerDetails().GetReviewedAt(),
	}
	if req.GetVerdict() == palCxPb.Verdict_VERDICT_PASS {
		loanStepExecutionEntry.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	} else {
		loanStepExecutionEntry.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
		loanStepExecutionEntry.GetDetails().GetManualReviewStepData().Reason = req.GetReviewerDetails().GetReason()
	}

	loanStepExecutionEntry.CompletedAt = timestamppb.New(time.Now().In(datetime.IST))
	updateError := s.loanStepExecutionsDao.Update(ctx, loanStepExecutionEntry,
		[]palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_COMPLETED_AT,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		})
	if updateError != nil {
		logger.Error(ctx, "error while updating loan step execution from db", zap.Error(updateError))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	var payloadType pQueuePb.PayloadType
	switch loanStepExecutionEntry.GetStepName() {
	case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_FACE_MATCH:
		payloadType = pQueuePb.PayloadType_PAYLOAD_TYPE_FACEMATCH_PRE_APPROVED_LOAN_APPLICATION
	case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_VENDOR_REVIEW:
		payloadType = pQueuePb.PayloadType_PAYLOAD_TYPE_PAL_APPLICATION_LIVENESS_FEDERAL_REVIEW
	case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_CHECK:
		payloadType = pQueuePb.PayloadType_PAYLOAD_TYPE_LIVENESS_PRE_APPROVED_LOAN_APPLICATION
	default:
		logger.Error(ctx, "Unknown loanStep")
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	pqElements := s.GetQueueElementsBypayloadType(ctx, req.GetActorId(), payloadType, loanStepExecutionEntry.GetStepName())

	for _, pqElement := range pqElements {
		// comparing reference Id for liveness case as we are sending orchId as refId during the  flow of inserting element in pq for federal application
		if payloadType == pQueuePb.PayloadType_PAYLOAD_TYPE_PAL_APPLICATION_LIVENESS_FEDERAL_REVIEW && pqElement.GetPayload().GetLivenessPayload().GetReferenceId() != req.GetOrchId() {
			continue
		}
		// for other payload types we do not get reference id in the request as it was not populated while inserting the pq element
		// so deleting all the elements with the given payload type and actor id so that they will not be surfaced to the agent again even after the review
		err := s.persistentQueue.DeleteElement(ctx, pqElement)
		if err != nil {
			logger.Error(ctx, "failed to set Deleted at from payload type preapprovedLoan", zap.Error(err))
		}
	}

	res.Status = rpcPb.StatusOk()
	return res, nil
}

// nolint:gocritic
func (s *Service) deleteElememtFromQueue(ctx context.Context, loanStepExecutionEntry *palPb.LoanStepExecution) error {
	var payloadType pQueuePb.PayloadType
	switch loanStepExecutionEntry.GetStepName() {
	case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_FACE_MATCH:
		payloadType = pQueuePb.PayloadType_PAYLOAD_TYPE_FACEMATCH_PRE_APPROVED_LOAN_APPLICATION
	case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_VENDOR_REVIEW:
		payloadType = pQueuePb.PayloadType_PAYLOAD_TYPE_PAL_APPLICATION_LIVENESS_FEDERAL_REVIEW
	case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_CHECK:
		payloadType = pQueuePb.PayloadType_PAYLOAD_TYPE_LIVENESS_PRE_APPROVED_LOAN_APPLICATION
	default:
		return errors.New("unknown loanStep")
	}

	pqElements := s.GetQueueElementsBypayloadType(ctx, loanStepExecutionEntry.GetActorId(), payloadType, loanStepExecutionEntry.GetStepName())

	for _, pqElement := range pqElements {
		var reqId string
		if payloadType == pQueuePb.PayloadType_PAYLOAD_TYPE_PAL_APPLICATION_LIVENESS_FEDERAL_REVIEW {
			// fetching reference Id for liveness case as we are sending orchId as refId during the  flow of inserting element in pq
			reqId = pqElement.GetPayload().GetLivenessPayload().GetReferenceId()
		} else if payloadType == pQueuePb.PayloadType_PAYLOAD_TYPE_LIVENESS_PRE_APPROVED_LOAN_APPLICATION ||
			payloadType == pQueuePb.PayloadType_PAYLOAD_TYPE_FACEMATCH_PRE_APPROVED_LOAN_APPLICATION {
			mrLivenessLoanStep, err := s.fetchLoanStepForQueuePayload(ctx, pqElement.GetActorID())
			if err != nil {
				logger.Error(ctx, "failed to fetch loanStep for queue payload", zap.Error(err))
				continue
			}
			reqId = mrLivenessLoanStep.GetOrchId()
		} else {
			// default case
			logger.Error(ctx, "unknown PQ element payload type")
			continue
		}
		if loanStepExecutionEntry.GetOrchId() == reqId {
			err := s.persistentQueue.DeleteElement(ctx, pqElement)
			if err != nil {
				logger.Error(ctx, "failed to set Deleted at from payload type preapprovedLoan", zap.Error(err))
			}
			break
		}
	}
	return nil
}

func (s *Service) GetLoanOfferSummary(ctx context.Context, req *palCxPb.GetLoanOfferSummaryRequest) (*palCxPb.GetLoanOfferSummaryResponse, error) {
	res := &palCxPb.GetLoanOfferSummaryResponse{}

	loanOffer, err := s.loanOffersDao.GetActiveOfferByActorIdAndVendor(ctx, req.GetActorId(), req.GetVendor())
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		res.Status = rpcPb.StatusOk()
		return res, nil
	}
	if err != nil {
		logger.Error(ctx, "failed to get loan offer by id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	res.LoanOffer = &palCxPb.GetLoanOfferSummaryResponse_LoanOfferForCX{
		VendorOfferId:    loanOffer.GetVendorOfferId(),
		Vendor:           loanOffer.GetVendor(),
		OfferConstraints: loanOffer.GetOfferConstraints(),
		ProcessingInfo:   loanOffer.GetProcessingInfo(),
	}

	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetLoanRequestSummary(ctx context.Context, req *palCxPb.GetLoanRequestSummaryRequest) (*palCxPb.GetLoanRequestSummaryResponse, error) {
	res := &palCxPb.GetLoanRequestSummaryResponse{}
	res.LoanRequests = make([]*palCxPb.GetLoanRequestSummaryResponse_LoanRequestForCX, 0)

	loanRequests, err := s.loanRequestsDao.GetByActorIdAndVendorAndStatus(ctx, req.GetActorId(), req.GetVendor(), nil)
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		res.Status = rpcPb.StatusOk()
		return res, nil
	}
	if err != nil {
		logger.Error(ctx, "failed to get loan request by id and request statuses", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	for _, singleLoanRequest := range loanRequests {
		res.LoanRequests = append(res.LoanRequests, &palCxPb.GetLoanRequestSummaryResponse_LoanRequestForCX{
			OfferId:         singleLoanRequest.GetOfferId(),
			LoanAccountId:   singleLoanRequest.GetLoanAccountId(),
			VendorRequestId: singleLoanRequest.GetVendorRequestId(),
			Vendor:          singleLoanRequest.GetVendor(),
			Details:         singleLoanRequest.GetDetails(),
			Type:            singleLoanRequest.GetType(),
			Status:          singleLoanRequest.GetStatus(),
			SubStatus:       singleLoanRequest.GetSubStatus(),
		})
	}

	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetLoanAccountSummary(ctx context.Context, req *palCxPb.GetLoanAccountSummaryRequest) (*palCxPb.GetLoanAccountSummaryResponse, error) {
	res := &palCxPb.GetLoanAccountSummaryResponse{}
	res.LoanAccounts = make([]*palCxPb.GetLoanAccountSummaryResponse_LoanAccountForCX, 0)

	loanAccounts, err := s.loanAccountsDao.GetByActorIdAndVendor(ctx, req.GetActorId(), req.GetVendor())
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		res.Status = rpcPb.StatusOk()
		return res, nil
	}
	if err != nil {
		logger.Error(ctx, "failed to get loan accounts by actor id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	for _, singleLoanAccount := range loanAccounts {
		res.LoanAccounts = append(res.LoanAccounts, &palCxPb.GetLoanAccountSummaryResponse_LoanAccountForCX{
			AccountNumber:  singleLoanAccount.GetAccountNumber(),
			Vendor:         singleLoanAccount.GetVendor(),
			IfscCode:       singleLoanAccount.GetIfscCode(),
			LoanType:       singleLoanAccount.GetLoanType(),
			LoanAmountInfo: singleLoanAccount.GetLoanAmountInfo(),
			LoanEndDate:    singleLoanAccount.GetLoanEndDate(),
			MaturityDate:   singleLoanAccount.GetMaturityDate(),
			Details:        singleLoanAccount.GetDetails(),
			Status:         singleLoanAccount.GetStatus(),
			LoanProgram:    singleLoanAccount.GetLoanProgram(),
		})
	}
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) getLoanUserDetailsForFiftyfin(ctx context.Context, req *palCxPb.GetLoanUserDetailsRequest) (*palCxPb.GetLoanUserDetailsResponse, error) {
	offer, err := s.loanOffersDao.GetLatestByActorIdAndVendor(ctx, req.GetActorId(), palPb.Vendor_FIFTYFIN)
	if err != nil {
		logger.Error(ctx, "error while fetching latest offer for fiftyfin", zap.Error(err))
		return &palCxPb.GetLoanUserDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	respOffer := &palCxPb.GetLoanUserDetailsResponse_LoanOfferDetails{
		LoanOfferId:    offer.GetId(),
		MinLoanAmount:  money.FromPaisa(helper.GetMinLoanAmountInPaisa(offer)),
		MaxLoanAmount:  offer.GetOfferConstraints().GetMaxLoanAmount(),
		MaxEmiAmount:   offer.GetOfferConstraints().GetMaxEmiAmount(),
		Interest:       offer.GetProcessingInfo().GetInterestRate()[0].GetPercentage(),
		MinTenure:      offer.GetOfferConstraints().GetMinTenureMonths(),
		MaxTenure:      offer.GetOfferConstraints().GetMaxTenureMonths(),
		OfferStartDate: datetime.TimestampToDateInLoc(offer.GetValidSince(), datetime.IST),
		OfferEndDate:   datetime.TimestampToDateInLoc(offer.GetValidTill(), datetime.IST),
		Vendor:         offer.GetVendor().String(),
		LoanProgram:    offer.GetLoanProgram().String(),
		AdditionalConstraints: &palCxPb.GetLoanUserDetailsResponse_LoanOfferDetails_FiftyfinLamfConstraintInfo{
			FiftyfinLamfConstraintInfo: offer.GetOfferConstraints().GetFiftyfinLamfConstraintInfo(),
		},
	}

	lrs, lrsError := s.loanRequestsDao.GetByActorIdAndVendorAndStatus(ctx, req.GetActorId(), palPb.Vendor_FIFTYFIN, nil)
	if lrsError != nil && !errors.Is(lrsError, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching loan request table", zap.Error(lrsError))
		return &palCxPb.GetLoanUserDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	loanApplicationDetails := &palCxPb.GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails{}
	if lrs != nil {
		loanApplicationDetails.LoanApplicationStatus = lrs[0].GetStatus().String()
		loanApplicationDetails.LoanApplicationSubStatus = lrs[0].GetSubStatus().String()
		loanApplicationDetails.LoanAmountApplied = lrs[0].GetDetails().GetLoanInfo().GetAmount()
		loanApplicationDetails.TenureAppliedMonths = lrs[0].GetDetails().GetLoanInfo().GetTenureInMonths()
		loanApplicationDetails.Vendor = lrs[0].GetVendor().String()
		loanApplicationDetails.LoanProgram = lrs[0].GetLoanProgram().String()

		lse, lseErr := s.loanStepExecutionsDao.GetLatestByRefIdAndFlow(ctx, lrs[0].GetId(),
			palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION)
		if lseErr != nil && !errors.Is(lseErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error while fetching from loan step execution table", zap.Error(lseErr), zap.Any(logger.LOAN_REQUEST_ID, lrs[0].GetId()))
			return &palCxPb.GetLoanUserDetailsResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}

		if lse != nil {
			loanApplicationDetails.LoanApplicationSubStatus = lse.GetStepName().String() + ":" + lse.GetStatus().String()
			if lse.GetSubStatus() != palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED {
				loanApplicationDetails.LoanApplicationStatus += ":" + lse.GetSubStatus().String()
			}
		}
	}

	var eligiblityLr, applicationLr []*palPb.LoanRequest
	for _, lr := range lrs {
		if lr.GetType() == palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY {
			eligiblityLr = append(eligiblityLr, lr)
		} else if lr.GetType() == palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION {
			applicationLr = append(applicationLr, lr)
		}
	}

	loanApplicant, err := s.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, req.GetActorId(), palPb.Vendor_FIFTYFIN, palPb.LoanProgram_LOAN_PROGRAM_LAMF, enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching from loan applicant", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, req.GetActorId()))
		return &palCxPb.GetLoanUserDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	var loanApplicants []*palPb.LoanApplicant
	if loanApplicant != nil {
		loanApplicants = append(loanApplicants, loanApplicant)
	}

	isUserSuggestedLamf, err := s.isUserSuggestedLamf(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error while checking if user is suggested lamf", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, req.GetActorId()))
		return &palCxPb.GetLoanUserDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	return &palCxPb.GetLoanUserDetailsResponse{
		Status: rpcPb.StatusOk(),
		CurrentMonthUserLoanEligibilityDetails: &palCxPb.GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails{
			IsUserSuggestedForLoan: isUserSuggestedLamf,
			ReasonForIneligibility: "NA",
		},
		CurrentUserLoanApplicationDetails: loanApplicationDetails,
		LoanOfferDetails: []*palCxPb.GetLoanUserDetailsResponse_LoanOfferDetails{
			respOffer,
		},
		EligibilityLoanRequests: eligiblityLr,
		ApplicationLoanRequests: applicationLr,
		Applicants:              loanApplicants,
	}, nil
}

func (s *Service) isUserSuggestedLamf(ctx context.Context, actorId string) (bool, error) {
	releasedSegments := s.gconf.LamfConfig().ReleaseSegmentDetails().Expression()
	segRes, err := s.segmentClient.IsMemberOfExpressions(ctx, &segmentPb.IsMemberOfExpressionsRequest{
		ActorId:              actorId,
		SegmentIdExpressions: []string{releasedSegments},
	})
	if rpcErr := epifigrpc.RPCError(segRes, err); rpcErr != nil {
		return false, fmt.Errorf("error in segmentClient.IsMemberOfExpressions rpc : %w", rpcErr)
	}
	if segRes == nil ||
		segRes.GetSegmentExpressionMembershipMap()[releasedSegments].GetSegmentExpressionStatus() != segmentPb.SegmentExpressionStatus_OK ||
		!segRes.GetSegmentExpressionMembershipMap()[releasedSegments].GetIsActorMember() {
		return false, nil
	}
	return true, nil
}

// GetLoanUserDetails RPC to get user details to be shown on sherlock to our agents. The RPC returns information such as
// if a user is eligible for loan or not. If the user has an active offer, the RPC returns details about the offer as well.
func (s *Service) GetLoanUserDetails(ctx context.Context, req *palCxPb.GetLoanUserDetailsRequest) (*palCxPb.GetLoanUserDetailsResponse, error) {
	// handle fiftyfin separately for now
	// TODO: refactor later to use some common parts
	if req.GetLoanHeader().GetVendor() == palPb.Vendor_FIFTYFIN {
		return s.getLoanUserDetailsForFiftyfin(ctx, req)
	}
	res := &palCxPb.GetLoanUserDetailsResponse{
		CurrentMonthUserLoanEligibilityDetails: &palCxPb.GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails{
			ReasonForIneligibility: "NA",
		},
		CurrentUserLoanApplicationDetails: &palCxPb.GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails{},
		Status:                            rpc.StatusOk(),
	}
	var err error
	// TODO(divas): remove multi dao dependency and check for all offer using multiDBDataProvider
	// fetch all LOEC for the actor across all loan programs and vendors supported by PL. These LOECs are fetched in
	// descending order of their creation meaning the LOEC created at the last by user/system will be the first element
	// in the list.
	// Note that vendor is sent as unspecified so it calls the multiDB dao which will check by actor id in all the DBs
	_, loecsErr := s.loanOfferEligibilityCriteriaDao.GetByActorIdAndVendorAndStatus(ctx, req.GetActorId(),
		palPb.Vendor_VENDOR_UNSPECIFIED, []palPb.LoanOfferEligibilityCriteriaStatus{
			palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED,
			palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED,
			palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED,
		}, true)
	// ignoring the return statements here to send the other available data
	if loecsErr != nil && !errors.Is(loecsErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to fetch from loanOfferEligibility dao by actor id", zap.Error(loecsErr))
		return nil, fmt.Errorf("error while fetching loanOfferEligibilityCriteria by actor id: %w", loecsErr)
	}
	if loecsErr != nil && errors.Is(loecsErr, epifierrors.ErrRecordNotFound) {
		res.GetCurrentMonthUserLoanEligibilityDetails().ReasonForIneligibility = "User hasn't started the journey yet"
		return res, nil
	}

	_, activeLoecsErr := s.loanOfferEligibilityCriteriaDao.GetActiveLoecsByActorIdLoanProgramsAndStatuses(ctx, req.GetActorId(), nil, nil, 0, true)
	if activeLoecsErr != nil && !errors.Is(activeLoecsErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to fetch active loanOfferEligibilityCriteria by actor id", zap.Error(activeLoecsErr))
		return nil, fmt.Errorf("error while fetching active loanOfferEligibilityCriteria by actor id: %w", activeLoecsErr)
	}
	if activeLoecsErr != nil && errors.Is(activeLoecsErr, epifierrors.ErrRecordNotFound) {
		res.GetCurrentMonthUserLoanEligibilityDetails().ReasonForIneligibility = "Eligibility is expired, user can try again"
	}

	res, err = s.hasCheckedEligibility(ctx, req.GetActorId(), res)
	if err != nil {
		logger.Error(ctx, "failed to check if user has checked loan eligibility", zap.Error(err))
	}

	combinedRankedLoanOptionRes, err := s.recommendationEngine.GetCombinedRankedLoanOptions(ctx, &provider.GetCombinedRankedLoanOptionsRequest{
		ActorId: req.GetActorId(),
	})
	if err != nil {
		return nil, fmt.Errorf("error while fetching ranked loan options: %w", err)
	}

	var loanOfferDetails []*palCxPb.GetLoanUserDetailsResponse_LoanOfferDetails
	for _, option := range combinedRankedLoanOptionRes.LoanOptions {
		if option.GetLoanOffer() == nil {
			continue
		}
		lo := option.GetLoanOffer()
		loanOfferDetails = append(loanOfferDetails, &palCxPb.GetLoanUserDetailsResponse_LoanOfferDetails{
			LoanOfferId:    lo.GetId(),
			MinLoanAmount:  lo.GetOfferConstraints().GetMinLoanAmount(),
			MaxLoanAmount:  lo.GetOfferConstraints().GetMaxLoanAmount(),
			MaxEmiAmount:   lo.GetOfferConstraints().GetMaxEmiAmount(),
			Interest:       lo.GetProcessingInfo().GetInterestRate()[0].GetPercentage(),
			MinTenure:      lo.GetOfferConstraints().GetMinTenureMonths(),
			MaxTenure:      lo.GetOfferConstraints().GetMaxTenureMonths(),
			OfferStartDate: datetimePkg.TimeToDateInLoc(lo.GetValidSince().AsTime(), datetimePkg.IST),
			OfferEndDate:   datetimePkg.TimeToDateInLoc(lo.GetValidTill().AsTime(), datetimePkg.IST),
			Vendor:         lo.GetVendor().String(),
			LoanProgram:    lo.GetLoanProgram().String(),
			LoanOfferType:  lo.GetLoanOfferType(),
		})
	}
	if len(loanOfferDetails) > 0 {
		res.CurrentMonthUserLoanEligibilityDetails.IsUserSuggestedForLoan = true
		res.CurrentMonthUserLoanEligibilityDetails.ReasonForIneligibility = "NA"
	}
	res.LoanOfferDetails = loanOfferDetails
	lrs, lrsError := s.loanRequestsDao.GetByActorIdAndVendorAndStatus(ctx, req.GetActorId(), palPb.Vendor_VENDOR_UNSPECIFIED, nil)
	if lrsError != nil && !errors.Is(lrsError, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to fetch from loanRequests dao", zap.Error(lrsError))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if lrs != nil {
		if lrs[0].GetVendor() != palPb.Vendor_FIFTYFIN {
			res.GetCurrentUserLoanApplicationDetails().LoanApplicationStatus = lrs[0].GetStatus().String()
			res.GetCurrentUserLoanApplicationDetails().LoanApplicationSubStatus = lrs[0].GetSubStatus().String()
			res.GetCurrentUserLoanApplicationDetails().LoanAmountApplied = lrs[0].GetDetails().GetLoanInfo().GetAmount()
			res.GetCurrentUserLoanApplicationDetails().TenureAppliedMonths = lrs[0].GetDetails().GetLoanInfo().GetTenureInMonths()
			res.GetCurrentUserLoanApplicationDetails().Vendor = lrs[0].GetVendor().String()
			res.GetCurrentUserLoanApplicationDetails().LoanProgram = lrs[0].GetLoanProgram().String()

			if lrs[0].GetVendor() != palPb.Vendor_FEDERAL {
				lse, err := s.loanStepExecutionsDao.GetLatestByRefIdAndFlow(ctx, lrs[0].GetId(),
					palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION)
				if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
					logger.Error(ctx, "failed to fetch latest lse for lr", zap.Error(err))
					res.Status = rpcPb.StatusInternal()
					return res, nil
				}

				if lse != nil {
					res.GetCurrentUserLoanApplicationDetails().LoanApplicationSubStatus = lse.GetStepName().String() + ":" + lse.GetStatus().String()
					if lse.GetSubStatus() != palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED {
						res.GetCurrentUserLoanApplicationDetails().LoanApplicationStatus += ":" + lse.GetSubStatus().String()
					}
				}
			}
		}
	}

	var eligiblityLr, applicationLr []*palPb.LoanRequest
	for _, lr := range lrs {
		if lr.GetVendor() != palPb.Vendor_FIFTYFIN {
			if lr.GetType() == palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY {
				eligiblityLr = append(eligiblityLr, lr)
			} else if lr.GetType() == palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION {
				applicationLr = append(applicationLr, lr)
			}
		}
	}
	res.EligibilityLoanRequests = eligiblityLr
	if len(applicationLr) > 0 {
		res.CurrentMonthUserLoanEligibilityDetails.IsUserSuggestedForLoan = true
		res.CurrentMonthUserLoanEligibilityDetails.ReasonForIneligibility = "NA"
	}
	res.ApplicationLoanRequests = applicationLr

	loanApplicant, err := s.loanApplicantDao.GetByActorId(ctx, req.GetActorId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching from loan applicant", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, req.GetActorId()))
		return &palCxPb.GetLoanUserDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	var loanApplicants []*palPb.LoanApplicant
	if loanApplicant != nil {
		loanApplicants = append(loanApplicants, loanApplicant)
	}
	res.Applicants = loanApplicants

	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) hasCheckedEligibility(ctx context.Context, actorId string, res *palCxPb.GetLoanUserDetailsResponse) (*palCxPb.GetLoanUserDetailsResponse, error) {
	lrs, err := s.loanRequestsDao.GetByActorIdAndVendorAndStatus(ctx, actorId, palPb.Vendor_VENDOR_UNSPECIFIED, nil)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("failed to fetch eligibility check LR, %w", err)
	}
	var eligibilityLr *palPb.LoanRequest
	for _, lr := range lrs {
		if lr.GetType() == palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY {
			eligibilityLr = lr
			break
		}
	}

	if eligibilityLr == nil {
		return res, nil
	}

	lse, err := s.loanStepExecutionsDao.GetLatestByRefIdAndFlow(ctx, eligibilityLr.GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("failed to fetch loan step executions by ref ID, %w", err)
	}

	if lse != nil {
		res.GetCurrentUserLoanApplicationDetails().LoanApplicationStatus = eligibilityLr.GetStatus().String()
		res.GetCurrentUserLoanApplicationDetails().LoanApplicationSubStatus = lse.GetStepName().String() + ":" + lse.GetStatus().String()
	}

	if eligibilityLr.Status == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED ||
		eligibilityLr.Status == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED {
		res.GetCurrentMonthUserLoanEligibilityDetails().ReasonForIneligibility = "User checked loan eligibility and got rejected"
		return res, nil
	}

	if eligibilityLr.Status == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED ||
		eligibilityLr.Status == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING ||
		eligibilityLr.Status == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED ||
		eligibilityLr.Status == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION ||
		eligibilityLr.Status == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED {
		res.GetCurrentMonthUserLoanEligibilityDetails().ReasonForIneligibility = "User initiated loan eligibility check but hasn't completed"
		return res, nil
	}

	res.GetCurrentUserLoanApplicationDetails().LoanApplicationStatus = lrs[0].GetStatus().String()
	res.GetCurrentUserLoanApplicationDetails().LoanApplicationSubStatus = lrs[0].GetSubStatus().String()
	res.GetCurrentUserLoanApplicationDetails().LoanAmountApplied = lrs[0].GetDetails().GetLoanInfo().GetAmount()
	res.GetCurrentUserLoanApplicationDetails().TenureAppliedMonths = lrs[0].GetDetails().GetLoanInfo().GetTenureInMonths()
	res.GetCurrentUserLoanApplicationDetails().Vendor = lrs[0].GetVendor().String()
	res.GetCurrentUserLoanApplicationDetails().LoanProgram = lrs[0].GetLoanProgram().String()

	return res, nil
}

func getMfPledgeDetails(pledgeDetails *palPb.PledgeDetails) []*palCxPb.GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails {
	var mfPledgeDetails []*palCxPb.GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails
	for _, detail := range pledgeDetails.GetMutualFunds().GetSchemes() {
		mfPledgeDetails = append(mfPledgeDetails, &palCxPb.GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails{
			Isin:  detail.GetIsin(),
			Units: detail.GetQuantity(),
		})
	}
	return mfPledgeDetails
}
func getLoanProgramsFromGetLandingPageDataForActorRequest(req *palCxPb.GetLoanDetailsRequest) []palPb.LoanProgram {
	if req.GetLoanHeader().GetLoanProgram() != palPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		return []palPb.LoanProgram{req.GetLoanHeader().GetLoanProgram()}
	}
	return helper.GetLoanProgramsForLoanType(palPb.LoanType_LOAN_TYPE_PERSONAL)
}

func (s *Service) GetLoanDetails(ctx context.Context, req *palCxPb.GetLoanDetailsRequest) (*palCxPb.GetLoanDetailsResponse, error) {
	res := &palCxPb.GetLoanDetailsResponse{
		Status: rpc.StatusOk(),
	}
	getLoanAccountsPayload := &multiDbProvider.CheckAndGetLoanAccountsForActorRequest{ActorId: req.GetActorId()}
	loanProgramsList := getLoanProgramsFromGetLandingPageDataForActorRequest(req)

	getLoanAccountsPayload.LoanPrograms = loanProgramsList
	loanAccounts, loanAccountsError := s.multiDbProvider.CheckAndGetLoanAccountsForActor(ctx, getLoanAccountsPayload)
	if errors.Is(loanAccountsError, epifierrors.ErrRecordNotFound) {
		return res, nil
	}
	if loanAccountsError != nil {
		logger.Error(ctx, "failed to fetch from loanAccounts dao", zap.Error(loanAccountsError))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	for _, la := range loanAccounts.Accounts {
		ld := &palCxPb.GetLoanDetailsResponse_LoanDetailsForCx{}
		ld.LoanAccountNumber = la.GetAccountNumber()
		ld.LoanOpenDate = la.GetCreatedAt()
		ld.LoanAmount = la.GetLoanAmountInfo().GetLoanAmount()
		ld.InterestRate = la.GetDetails().GetInterestRate()
		ld.TenureInMonths = la.GetDetails().GetTenureInMonths()
		ld.OutstandingAmount = la.GetLoanAmountInfo().GetOutstandingAmount()
		ld.PreClosureFee = money.ZeroINR().GetPb()
		ld.Vendor = la.GetVendor().String()

		bankAccountDetails, bankAccountDetailsErr := s.getBankingDetailsFromAccountId(ctx, la.GetId())
		if bankAccountDetailsErr != nil {
			logger.Error(ctx, "failed to get banking details", zap.Error(bankAccountDetailsErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		} else {
			ld.BankAccountDetails = bankAccountDetails
		}

		if len(la.GetDetails().GetCollateralDetails().GetMfPledgeDetails().GetMutualFunds().GetSchemes()) != 0 {
			ld.MfPledgeDetails = getMfPledgeDetails(la.GetDetails().GetCollateralDetails().GetMfPledgeDetails())
		}

		lrs, lrError := s.loanRequestsDao.GetByLoanAccountIdAndType(ctx, la.GetId(), palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION)
		if lrError != nil {
			logger.Error(ctx, "failed to fetch from loanRequests dao", zap.Error(lrError))
		} else if lrs != nil {
			ld.Gst = lrs[0].GetDetails().GetLoanInfo().GetDeductions().GetGst()
			ld.BrokenPeriodInterest = lrs[0].GetDetails().GetLoanInfo().GetDeductions().GetAdvanceInterest()
			ld.ProcessingFee = lrs[0].GetDetails().GetLoanInfo().GetDeductions().GetProcessingFee()
			ld.LoanProgram = lrs[0].GetLoanProgram().String()
		}

		lii, liiError := s.loanInstallmentInfoDao.GetByActiveAccountId(ctx, la.GetId())
		if liiError != nil && !errors.Is(liiError, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed to fetch from loanInstallmentInfos dao", zap.Error(liiError))
		} else if lii != nil {
			nextEmiDate := lii.GetNextInstallmentDate()
			for emiLeft := 0; emiLeft < 5 && datetime.IsAfter(datetime.DateToTimestamp(lii.GetEndDate(), datetime.IST), datetime.DateToTimestamp(nextEmiDate, datetime.IST)); emiLeft++ {
				upcomingEmi := &palCxPb.GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi{
					NextEmiDate:       nextEmiDate,
					LoanAccountNumber: la.GetAccountNumber(),
					Amount:            lii.GetDetails().GetNextEmiAmount(),
				}
				ld.LoanUpcomingEmis = append(ld.LoanUpcomingEmis, upcomingEmi)
				nextEmiDate = datetime.TimeToDateInLoc(datetime.DateToTime(nextEmiDate, datetime.IST).AddDate(0, 1, 0).In(datetime.IST), datetime.IST)
			}
		}

		lActs, lActsErr := s.loanActivityDao.GetByAccountIdAndTypesAndCount(ctx, la.GetId(), nil, 5)
		if lActsErr != nil && !errors.Is(lActsErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed to fetch from loanActivities dao", zap.Error(lActsErr))
		}
		for _, lAct := range lActs {
			txnRes, txnErr := s.paymentClient.GetTransaction(ctx, &paymentPb.GetTransactionRequest{
				Identifier: &paymentPb.GetTransactionRequest_TransactionId{
					TransactionId: lAct.GetReferenceId(),
				},
				GetReqInfo: false,
			})
			if te := epifigrpc.RPCError(txnRes, txnErr); te != nil {
				logger.Error(ctx, "failed to fetch transaction from transaction from transaction id", zap.Error(txnErr))
				continue
			}
			utr, utrErr := s.rpcHelper.GetUtr(ctx, txnRes.GetTransaction())
			if utrErr != nil || utr == "" {
				logger.Error(ctx, "Failed to fetch UTR", zap.Error(utrErr))
				continue
			}

			txn := &palCxPb.GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction{
				Timestamp:         helper.GetTransactionTimestampWithBestEffort(txnRes.GetTransaction()),
				TransactionType:   lAct.GetType().String(),
				Status:            txnRes.GetTransaction().GetStatus().String(),
				OrderId:           txnRes.GetTransaction().GetOrderId(),
				FiUtrNumber:       utr,
				LoanAccountNumber: la.GetAccountNumber(),
				Amount:            lAct.GetDetails().GetAmount(),
				ModeOfPayment:     txnRes.GetTransaction().GetPaymentProtocol().String(),
			}

			if lAct.GetDetails().GetActivityTypeDetails() != nil && lAct.GetDetails().GetInstallmentPayoutDetails() != nil {
				lip, _ := s.loanInstallmentPayoutDao.GetById(ctx, lAct.GetDetails().GetInstallmentPayoutDetails().GetId())
				if lip != nil {
					txn.Charges = lip.GetDetails().GetPenaltyAmount()
				}
			}
			ld.LoanPastTransactions = append(ld.LoanPastTransactions, txn)
		}

		// loan repayment account details for ATL users
		if len(lrs) > 0 {
			// For acquire to lend users, return the banking details
			loanStep, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, lrs[0].GetId(),
				palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
				palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_BANKING_DETAILS)
			if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "error in fetching loan step from loanReqId", zap.Error(err))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}

			ld.MandateAccount = &palCxPb.MandateAccount{
				MaskedAccNo: getMaskedAccNo(loanStep.GetDetails().GetOnboardingData().GetBankingDetails().GetAccountNumber()),
				BankName:    loanStep.GetDetails().GetOnboardingData().GetBankingDetails().GetBankName(),
			}
		}
		ld.LoanAccount = la
		res.LoanDetails = append(res.LoanDetails, ld)
	}

	return res, nil
}

func (s *Service) getBankingDetailsFromAccountId(ctx context.Context, loanAccountId string) (*typesPb.BankAccountDetails, error) {
	loanRequests, err := s.loanRequestsDao.GetByLoanAccountIdAndType(ctx, loanAccountId, palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION)
	if err != nil {
		return nil, errors2.Wrap(err, "failed to get loan request by account ID")
	}

	if len(loanRequests) == 0 {
		return nil, errors2.New("GetLoanDefaultDetails: empty loan requests")
	}

	loanStep, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, loanRequests[0].GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_BANKING_DETAILS)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, errors2.Wrap(err, "error in fetching loan step from loanReqId")
	}

	// For A2L, banking details is stored in onb data object, if available then return it
	if loanStep.GetDetails().GetOnboardingData().GetBankingDetails() != nil {
		return &typesPb.BankAccountDetails{
			AccountNumber: getMaskedAccNo(loanStep.GetDetails().GetOnboardingData().GetBankingDetails().GetAccountNumber()),
			Ifsc:          loanStep.GetDetails().GetOnboardingData().GetBankingDetails().GetIfscCode(),
			BankName:      loanStep.GetDetails().GetOnboardingData().GetBankingDetails().GetBankName(),
		}, nil
	}

	// For alternate account flow, banking details is stored in mandate data object
	// this is only stored for PL program
	loanStep, err = s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, loanRequests[0].GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, errors2.Wrap(err, "error in fetching loan step from loanReqId")
	}
	if loanStep.GetDetails().GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed() != nil {
		return &typesPb.BankAccountDetails{
			AccountNumber: getMaskedAccNo(loanStep.GetDetails().GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed().GetAccountNumber()),
			Ifsc:          loanStep.GetDetails().GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed().GetIfscCode(),
			BankName:      loanStep.GetDetails().GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed().GetBankName(),
		}, nil
	}
	return nil, errors.New("banking data is not available or default Federal banking details is used")
}

// nolint: funlen
func (s *Service) GetQueueElements(ctx context.Context, req *palCxPb.GetQueueElementsRequest) (*palCxPb.GetQueueElementsResponse, error) {
	persistentQueue, err := s.getPQFromDBConn(ctx, req.GetPayloadType())
	if err != nil {
		logger.Error(ctx, "failed to get PQ from DB Conn", zap.Error(err))
		return nil, err
	}
	elements, err := persistentQueue.GetElements(ctx, req.GetPayloadType(), int(req.GetLimit()), int(req.GetPageNum()), req.GetFromTime(), req.GetToTime(), true, nil)
	res := &palCxPb.GetQueueElementsResponse{}
	if rpc.StatusFromError(err).IsRecordNotFound() {
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	}
	if err != nil {
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	var payloads []*palCxPb.GetQueueElementsResponse_QueueElement
	for _, element := range elements {
		var currentPayload *palCxPb.GetQueueElementsResponse_QueueElement
		switch req.GetPayloadType() {
		case pQueuePb.PayloadType_PAYLOAD_TYPE_PAL_APPLICATION_LIVENESS_FEDERAL_REVIEW:
			currentPayload = &palCxPb.GetQueueElementsResponse_QueueElement{
				Id: element.GetID(),
				Payload: &palCxPb.GetQueueElementsResponse_QueueElement_LivenessReview{
					LivenessReview: &pQueuePb.LivenessReview{
						ActorId:       element.GetActorID(),
						RequestId:     element.GetPayload().GetLivenessPayload().GetRequestId(),
						VideoLocation: element.GetPayload().GetLivenessPayload().GetVideoLocation(),
						CreatedAt:     element.GetCreatedAt(),
						ReferenceId:   element.GetPayload().GetLivenessPayload().GetReferenceId(),
					},
				},
			}
		case pQueuePb.PayloadType_PAYLOAD_TYPE_LIVENESS_PRE_APPROVED_LOAN_APPLICATION:
			livenessPayload, err := s.convertToLivenessReviewPayload(ctx, element)
			if err != nil {
				if errors.Is(err, epifierrors.ErrRecordNotFound) {
					// there can be cases where the element is in persistent queue and the LSE status is not yet updated to in review
					// in such cases we cannot fetch the LSE based on the IN_REVIEW status to populate the ref id
					// we ignore such elements until the status gets updated to IN_REVIEW so that we can map the liveness payload to LSE
					// and mark the LSE as success based on the ref id populated when the review is submitted
					// TODO(sharath): remove manual review handling from lending and let the review submissions go to Liveness service or Auth orchestrator
					logger.Info(ctx, "corresponding lse is not found", zap.Error(err), zap.String(logger.ACTOR_ID_V2, element.GetActorID()))
					continue
				} else {
					logger.Error(ctx, "failed to convert to liveness payload", zap.Error(err), zap.String(logger.ACTOR_ID_V2, element.GetActorID()))
					continue
				}
			}
			currentPayload = &palCxPb.GetQueueElementsResponse_QueueElement{
				Id: element.GetID(),
				Payload: &palCxPb.GetQueueElementsResponse_QueueElement_LivenessReview{
					LivenessReview: livenessPayload,
				},
			}

		case pQueuePb.PayloadType_PAYLOAD_TYPE_FACEMATCH_PRE_APPROVED_LOAN_APPLICATION:
			fmPayload, err := s.convertToFMReviewPayload(ctx, element)
			if err != nil {
				if errors.Is(err, epifierrors.ErrRecordNotFound) {
					// there can be cases where the element is in persistent queue and the LSE status is not yet updated to in review
					// in such cases we cannot fetch the LSE based on the IN_REVIEW status to populate the ref id
					// we ignore such elements until the status gets updated to IN_REVIEW so that we can map the liveness payload to LSE
					// and mark the LSE as success based on the ref id populated when the review is submitted
					// TODO(sharath): remove manual review handling from lending and let the review submissions go to Liveness service or Auth orchestrator
					logger.Info(ctx, "corresponding lse is not found", zap.String(logger.ACTOR_ID_V2, element.GetActorID()))
					continue
				} else {
					logger.Error(ctx, "failed to convert to facematch payload", zap.Error(err), zap.String(logger.ACTOR_ID_V2, element.GetActorID()))
					continue
				}
			}
			currentPayload = &palCxPb.GetQueueElementsResponse_QueueElement{
				Id: element.GetID(),
				Payload: &palCxPb.GetQueueElementsResponse_QueueElement_FacematchReview{
					FacematchReview: fmPayload,
				},
			}
		default:
			logger.Error(ctx, "Unknown payload type received", zap.String("PayloadType", req.GetPayloadType().String()))
			return &palCxPb.GetQueueElementsResponse{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("unhandled payload type: %v", req.GetPayloadType().String())),
			}, nil
		}
		payloads = append(payloads, currentPayload)
	}
	return &palCxPb.GetQueueElementsResponse{
		Status:   rpc.StatusOk(),
		Elements: payloads,
	}, nil
}

func (s *Service) GetQueueElementsBypayloadType(ctx context.Context, actorId string, payloadType pQueuePb.PayloadType, stepName palPb.LoanStepExecutionStepName) []*persistentqueue.QueueElement {

	persistentQueue, err := s.getPQFromDBConn(ctx, payloadType)
	if err != nil {
		logger.Error(ctx, "failed to get PQ from DB Conn", zap.Error(err))
		return nil
	}

	pq, pqError := persistentQueue.GetElementsByActorIdPayloadType(ctx, actorId, payloadType)
	if pqError != nil {
		logger.Error(ctx, "failed to get pq element from actorId and payload type", zap.Error(pqError), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	var fmPq []*persistentqueue.QueueElement
	if stepName == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_CHECK {
		fmPq, pqError = persistentQueue.GetElementsByActorIdPayloadType(ctx, actorId, pQueuePb.PayloadType_PAYLOAD_TYPE_FACEMATCH_PRE_APPROVED_LOAN_APPLICATION)
		if pqError != nil {
			logger.Error(ctx, "failed to get pq element from actorId and payload type", zap.Error(pqError))
		}
	}
	pq = append(pq, fmPq...)
	return pq
}

func (s *Service) getPQFromDBConn(_ context.Context, payload pQueuePb.PayloadType) (persistentqueue.PersistentQueue, error) {
	switch payload {
	case pQueuePb.PayloadType_PAYLOAD_TYPE_PAL_APPLICATION_LIVENESS_FEDERAL_REVIEW:
		return s.persistentQueue, nil
	case pQueuePb.PayloadType_PAYLOAD_TYPE_LIVENESS_PRE_APPROVED_LOAN_APPLICATION, pQueuePb.PayloadType_PAYLOAD_TYPE_FACEMATCH_PRE_APPROVED_LOAN_APPLICATION:
		return persistentqueue.NewPersistentQueue(s.epifiCRDB), nil
	default:
		return nil, fmt.Errorf("unknown PQ payload")
	}
}

func (s *Service) convertToLivenessReviewPayload(ctx context.Context, element *persistentqueue.QueueElement) (*pQueuePb.LivenessReview, error) {

	mrLivenessLoanStep, err := s.fetchLoanStepForQueuePayload(ctx, element.GetActorID())
	if err != nil {
		return nil, err
	}
	return &pQueuePb.LivenessReview{
		ActorId:       element.GetActorID(),
		RequestId:     element.GetPayload().GetLivenessAndFacematchReviewPayload().GetLivenessRequestId(),
		VideoLocation: element.GetPayload().GetLivenessAndFacematchReviewPayload().GetVideoLocation(),
		CreatedAt:     element.GetPayload().GetLivenessAndFacematchReviewPayload().GetCreatedAt(),
		ReferenceId:   mrLivenessLoanStep.GetOrchId(),
	}, nil

}
func (s *Service) convertToFMReviewPayload(ctx context.Context, element *persistentqueue.QueueElement) (*pQueuePb.FacematchReview, error) {

	mrFacematchLoanStep, err := s.fetchLoanStepForQueuePayload(ctx, element.GetActorID())
	if err != nil {
		return nil, err
	}
	return &pQueuePb.FacematchReview{
		ActorId:            element.GetActorID(),
		RequestId:          element.GetPayload().GetLivenessAndFacematchReviewPayload().GetFacematchRequestId(),
		CreatedAt:          element.GetPayload().GetLivenessAndFacematchReviewPayload().GetCreatedAt(),
		ReferenceId:        mrFacematchLoanStep.GetOrchId(),
		VideoFrame:         element.GetPayload().GetLivenessAndFacematchReviewPayload().GetVideoFrame(),
		Base64KycImageData: element.GetPayload().GetLivenessAndFacematchReviewPayload().GetKycImage(),
	}, nil

}

func (s *Service) fetchLoanStepForQueuePayload(ctx context.Context, actorId string) (*palPb.LoanStepExecution, error) {
	activeLr, err := s.loanRequestsDao.GetNonTerminalByActorIdAndVendor(ctx, actorId, palPb.Vendor_VENDOR_UNSPECIFIED)
	if err != nil {
		return nil, errors2.Wrap(err, "failed to get active loan requests by actor ID")
	}
	lse, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, activeLr.GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_CHECK)
	if err != nil {
		return nil, errors2.Wrap(err, "failed to get lse by ref id")
	}
	if lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS &&
		lse.GetSubStatus() == palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANUAL_REVIEW {
		return lse, nil
	}
	return nil, epifierrors.ErrRecordNotFound
}

func (s *Service) MarkLoanRequestCancel(ctx context.Context, req *palCxPb.MarkLoanRequestCancelRequest) (*palCxPb.MarkLoanRequestCancelResponse, error) {
	res := &palCxPb.MarkLoanRequestCancelResponse{
		Status: rpc.StatusOk(),
	}
	lr, err := s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "failed to get loan request by id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	lse, lseErr := s.loanStepExecutionsDao.GetByRefIdAndStatuses(ctx, lr.GetId(), []palPb.LoanStepExecutionStatus{})
	if lseErr != nil && !errors.Is(lseErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to get lse by ref id", zap.Error(lseErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(lr.GetVendor()))

	if len(lse) > 0 {
		lse[0].Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CANCELLED
		lse[0].CompletedAt = timestamppb.Now()
		lseUpdateErr := s.loanStepExecutionsDao.Update(ctx, lse[0], []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_COMPLETED_AT,
		})
		if lseUpdateErr != nil {
			logger.Error(ctx, "failed to update lse status", zap.Error(lseUpdateErr))
			res.Status = rpcPb.StatusInternal()
		}
	}

	lr.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED
	lr.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CANCELLED
	lr.CompletedAt = timestamppb.Now()

	updateErr := s.loanRequestsDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
	})
	if updateErr != nil {
		logger.Error(ctx, "failed to update loan request", zap.Error(updateErr))
		res.Status = rpcPb.StatusInternal()
	}

	return res, nil
}

func getMaskedAccNo(accNo string) string {
	if len(accNo) > 4 {
		return accNo[len(accNo)-4:]
	}
	return accNo
}

func (s *Service) GetForeclosureDetails(ctx context.Context, req *palCxPb.GetForeclosureRequest) (*palCxPb.GetForeclosureResponse, error) {
	loanAccount, err := s.loanAccountsDao.GetByAccountNumberAndVendor(ctx, req.GetLoanAccountNumber(), req.GetLoanHeader().GetVendor())
	if err != nil {
		logger.Error(ctx, "failed to get loan accounts", zap.Error(err))
		return &palCxPb.GetForeclosureResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// if the loan account is closed then we should not hit vendor to get the amount
	if loanAccount.GetStatus() == palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED {
		return &palCxPb.GetForeclosureResponse{
			Status: rpc.StatusOk(),
			TotalOutstandingAmt: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        0,
				Nanos:        0,
			},
		}, nil
	}

	loanForeClosureProvider, err := s.loanDataProvider.FetchLoanForeClosureDetailsProvider(ctx, req.GetLoanHeader(), loanAccount.GetLmsPartner())
	if err != nil || loanForeClosureProvider == nil {
		logger.Error(ctx, "failed to fetch loan data provider", zap.Error(err))
		return &palCxPb.GetForeclosureResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	foreClosureDetails, foreClosureDetailsErr := loanForeClosureProvider.FetchLoanPreClosureDetailsFromVendor(ctx, loanAccount)
	if foreClosureDetailsErr != nil {
		logger.Error(ctx, "unable to get the foreclosure details", zap.Error(foreClosureDetailsErr))
		return &palCxPb.GetForeclosureResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	return &palCxPb.GetForeclosureResponse{
		Status:                  rpc.StatusOk(),
		TotalOutstandingAmt:     foreClosureDetails.LoanPreCloseAmount,
		PrincipalOutstandingAmt: foreClosureDetails.LoanPrincipalOutstandingAmount,
		InterestOutstandingAmt:  foreClosureDetails.LoanInterestOutstandingAmount,
		PenaltyAmt:              foreClosureDetails.LoanPenaltyAmount,
		FeesAmt:                 foreClosureDetails.LoanFeesAmount,
		OtherCharges:            foreClosureDetails.LoanOtherCharges,
	}, nil
}

func (s *Service) GetLoanRequestAdditionalDetails(ctx context.Context, req *palCxPb.GetLoanRequestAdditionalDetailsRequest) (*palCxPb.GetLoanRequestAdditionalDetailsResponse, error) {
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	dataProvider := s.cxDataProviderFactory.GetProvider(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	resp, err := dataProvider.GetLoanRequestAdditionalDetails(ctx, req)
	if err != nil {
		logger.Error(ctx, "failed to get loan request additional details", zap.Error(err), zap.String(logger.REQUEST_ID, req.GetLoanRequestId()))
		return &palCxPb.GetLoanRequestAdditionalDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	resp.Status = rpc.StatusOk()
	return resp, nil
}

func (s *Service) GetLoanAccountAdditionalDetails(ctx context.Context, req *palCxPb.GetLoanAccountAdditionalDetailsRequest) (*palCxPb.GetLoanAccountAdditionalDetailsResponse, error) {
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	dataProvider := s.cxDataProviderFactory.GetProvider(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	resp, err := dataProvider.GetLoanAccountAdditionalDetails(ctx, req)
	if err != nil {
		logger.Error(ctx, "failed to get loan account additional details", zap.Error(err), zap.String(logger.ACCOUNT_ID, req.GetAccountId()))
		return &palCxPb.GetLoanAccountAdditionalDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	bankAccountDetails, bankAccountDetailsErr := s.getBankingDetailsFromAccountId(ctx, req.GetAccountId())
	if bankAccountDetailsErr != nil {
		logger.Error(ctx, "failed to get banking details", zap.Error(bankAccountDetailsErr), zap.String(logger.ACCOUNT_ID, req.GetAccountId()))
	} else {
		resp.BankAccountDetails = bankAccountDetails
	}
	resp.Status = rpc.StatusOk()
	return resp, nil
}

// updateLoecExpiryInternal updates the expired_at field of a loan offer eligibility criteria
func (s *Service) updateLoecExpiryInternal(ctx context.Context, loecIds []string, vendor palPb.Vendor) (int32, error) {
	var rowsAffected int32
	var expiredTime = timestamppb.Now()
	for _, loecId := range loecIds {
		// Set ownership based on vendor
		ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(vendor))

		// Create LOEC proto with ID and expired_at timestamp
		loec := &palPb.LoanOfferEligibilityCriteria{
			Id:        loecId,
			ExpiredAt: expiredTime,
		}

		// Update using the existing Update method with EXPIRED_AT field mask
		err := s.loanOfferEligibilityCriteriaDao.Update(ctx, loec, []palPb.LoanOfferEligibilityCriteriaFieldMask{
			palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_EXPIRED_AT,
		})
		if err != nil {
			if errors.Is(err, epifierrors.ErrRowNotUpdated) {
				continue
			}
			return rowsAffected, errors2.Wrap(err, fmt.Sprintf("failed to update expired_at for loec %s", loecId))
		}
		rowsAffected++
	}
	return rowsAffected, nil
}

// ExpireLoec updates the expired_at timestamp for the given LOEC IDs
func (s *Service) ExpireLoec(ctx context.Context, req *palCxPb.ExpireLoecRequest) (*palCxPb.ExpireLoecResponse, error) {
	if len(req.GetLoecIds()) == 0 {
		logger.Error(ctx, "loec_ids cannot be empty")
		return &palCxPb.ExpireLoecResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	vendor := req.GetVendor()
	if vendor == palPb.Vendor_VENDOR_UNSPECIFIED {
		logger.Error(ctx, "vendor cannot be unspecified")
		return &palCxPb.ExpireLoecResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	rowsAffected, err := s.updateLoecExpiryInternal(ctx, req.GetLoecIds(), vendor)
	if err != nil {
		logger.Error(ctx, "error updating LOEC expiry", zap.Error(err))
		return &palCxPb.ExpireLoecResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &palCxPb.ExpireLoecResponse{
		Status:       rpcPb.StatusOk(),
		RowsAffected: rowsAffected,
	}, nil
}
