package activity

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	palEnumsPb "github.com/epifi/gamma/api/preapprovedloan/enums"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/feature/release"
	calculatorTypes "github.com/epifi/gamma/preapprovedloan/calculator/types"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	palEvents "github.com/epifi/gamma/preapprovedloan/events"
	reProvider "github.com/epifi/gamma/preapprovedloan/recommendation_engine/provider"
)

func (p *Processor) CreateLoanPaymentRequest(ctx context.Context, req *palActivityPb.CreateLoanPaymentRequestRequest) (*palActivityPb.CreateLoanPaymentRequestResponse, error) {
	res := &palActivityPb.CreateLoanPaymentRequestResponse{
		ResponseHeader: &activityPb.ResponseHeader{},
	}

	// fetching loan account to get the actor Id
	loanAccount, laErr := p.loanAccountDao.GetById(ctx, req.GetAccountId())
	if laErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get the loan account, err :%v", laErr))
	}

	var parentId string
	parentLpr, parentLprErr := p.loanPaymentRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
	if parentLprErr != nil && !errors.Is(parentLprErr, epifierrors.ErrRecordNotFound) {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get the parent lpr ,err: %v", parentLprErr))
	}
	if parentLpr != nil {
		parentId = parentLpr.GetId()
	}

	orchId := req.GetOrchId()

	if req.GetOrchId() == "" {
		orchId = uuid.New().String()
	} else {
		// check if any non-terminal loan payment request with the same orch id already exists in the system
		lpr, lprErr := p.loanPaymentRequestDao.GetByOrchId(ctx, req.GetOrchId())
		if lprErr != nil && !errors.Is(lprErr, epifierrors.ErrRecordNotFound) {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch already existing lpr, err: %v", lprErr))
		}
		if lprErr == nil {
			res.LoanPaymentRequest = lpr
			return res, nil
		}
	}

	// create lpr if we got no record in the system
	loanPaymentRequest, err := p.loanPaymentRequestDao.Create(ctx,
		&palPb.LoanPaymentRequest{
			ActorId:   loanAccount.GetActorId(),
			AccountId: req.GetAccountId(),
			Amount:    req.GetAmount(),
			OrchId:    orchId,
			Type:      req.GetType(),
			Status:    palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
			ParentId:  parentId,
		},
	)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to create a loan payment request, err :%v", err))
	}

	res.LoanPaymentRequest = loanPaymentRequest
	return res, nil
}

func (p *Processor) CreateLoec(ctx context.Context, req *palActivityPb.CreateLoecRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		var (
			res = &palActivityPb.PalActivityResponse{
				LoanStep: lse,
			}
			lg = activity.GetLogger(ctx)
		)

		_, loecErr := p.loanOfferEligibilityCriteriaDao.Create(ctx, req.GetLoec())
		if loecErr != nil {
			lg.Error("failed to create a loec", zap.Error(loecErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to create a loec, err :%v", loecErr))
		}

		return res, nil
	})
	return actRes, actErr
}

func (p *Processor) CreateLoanActivityEntry(ctx context.Context, req *palActivityPb.CreateLoanActivityEntryRequest) error {
	_, err := p.loanActivityDao.Create(ctx, req.GetLoanActivityEntry())
	if err != nil {
		// there is already entry in db so we will return it as success
		if errors.Is(err, epifierrors.ErrDuplicateEntry) {
			return nil
		}
		logger.Error(ctx, "failed to create loan activity entry", zap.String(logger.ACCOUNT_ID, req.GetLoanActivityEntry().GetLoanAccountId()), zap.Error(err))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error ocured craeting entry in loan activity: %v", err))
	}
	return nil
}

// nolint:funlen,gocritic,dupl
func (p *Processor) GetAlternateOfferOrFailNextAction(ctx context.Context, req *palActivityPb.UpdateLRNextActionRequest) (*palActivityPb.PalActivityResponse, error) {
	lr, err := p.loanRequestDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		return nil, GetActivityErrFromDaoError(err)
	}

	// default failure screens
	nextAction, naErr := deeplink.GetApplicationFailureDeeplink(deeplink.GetFeLoanHeaderByBeLoanHeader(&palPb.LoanHeader{
		LoanProgram: lr.GetLoanProgram(),
		Vendor:      lr.GetVendor(),
	}), req.GetLoanStep(), stagePb.Status_FAILED)
	if naErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting failure deeplink, err: %v", naErr))
	}
	lr.NextAction = nextAction
	// check if user needs to be taken to second look, i.e. another offer maybe from a different vendor + loan program or same.
	isSecondLookNextAction := false
	key := lr.GetVendor().String() + ":" + lr.GetLoanProgram().String()
	lseKey := req.GetLoanStep().GetStepName().String() + ":" + req.GetLoanStep().GetStatus().String() + ":" + req.GetLoanStep().GetSubStatus().String()
	val := p.config.SecondLookNextOfferConfig().Get(key)
	if val != nil && lo.Contains(val.LoanSteps().ToStringArray(), lseKey) {
		logger.Info(ctx, fmt.Sprintf("user entered in second look flow, actor_id: %s", lr.GetActorId()))
		newCtx := epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_TECH)
		reResponse, reErr := p.recommendationEngine.GetCombinedRankedLoanOptions(newCtx, &reProvider.GetCombinedRankedLoanOptionsRequest{
			ActorId: lr.GetActorId(),
		})
		if reErr != nil {
			p.pushSecondLookApiResponseEvent(ctx, lr.GetActorId(), palEvents.Failure, palEvents.EventSubstatusSecondLookApiFailure, key, req.GetLoanStep().GetStepName().String(), req.GetLoanStep().GetStatus().String(), req.GetLoanStep().GetSubStatus().String(), lr.GetId(), "", "")
			logger.Error(ctx, fmt.Sprintf("unable to get offer from recommendation engine, actor_id: %s, err: %v", lr.GetActorId(), reErr))
			// not returning from here, so we can use the default deeplink we had set above to update in LR next action
		} else if reResponse != nil && len(reResponse.LoanOptions) > 0 {
			nextLoanHeader := reResponse.LoanOptions[0].GetLoanHeader()
			offerOwnership := nextLoanHeader.GetVendor().String() + ":" + nextLoanHeader.GetLoanProgram().String()
			if nextLoanHeader.GetVendor() == lr.GetVendor() && nextLoanHeader.GetLoanProgram() == lr.GetLoanProgram() {
				// here new offer is for same vendor and program for which this application got cancelled, hence ignore
				p.pushSecondLookApiResponseEvent(ctx, lr.GetActorId(), palEvents.Failure, palEvents.EventSubstatusSecondLookSameOffer, key, req.GetLoanStep().GetStepName().String(), req.GetLoanStep().GetStatus().String(), req.GetLoanStep().GetSubStatus().String(), lr.GetId(), offerOwnership, reResponse.LoanOptions[0].GetLoanOffer().GetId())
				logger.Info(ctx, fmt.Sprintf("user has same offer as before, not entering in second look flow, actor_id: %s, new_offer_id: %s", lr.GetActorId(), reResponse.LoanOptions[0].GetLoanOffer().GetId()))
			} else {
				enableSecondLook, enableSecondLookErr := p.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_LOANS_SECOND_LOOK_V1).WithActorId(lr.GetActorId()))
				if enableSecondLookErr != nil {
					return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in evaluating second look feature flag, err: %v", enableSecondLookErr))
				}
				if enableSecondLook {
					if secondLookNA, secondLookErr := baseprovider.CreateNewSecondLookPollingScreen(lr); secondLookErr == nil {
						lr.NextAction = secondLookNA
						isSecondLookNextAction = true
						// Push the SecondLookV1 event
						p.pushSecondLookV1Event(ctx, lr.GetActorId(), palEvents.Success, palEvents.EventSubstatusSecondLookV1OfferShown, reResponse.LoanOptions[0].GetLoanOffer().GetId(), offerOwnership, lr.GetId(), key)
					} else {
						// Push the SecondLookV1 failure event for polling screen error
						p.pushSecondLookV1Event(ctx, lr.GetActorId(), palEvents.Failure, palEvents.EventSubstatusSecondLookV1PollingScreenError, reResponse.LoanOptions[0].GetLoanOffer().GetId(), offerOwnership, lr.GetId(), key)
						logger.Error(ctx, fmt.Sprintf("error in getting second look poll screen, actor_id: %s, err: %v", lr.GetActorId(), secondLookErr))
						return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting second look poll screen, err: %v", secondLookErr))
					}
				} else {
					lr.NextAction = &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
					}
				}
			}
		} else {
			p.pushSecondLookApiResponseEvent(ctx, lr.GetActorId(), palEvents.Failure, palEvents.EventSubstatusSecondLookOfferNotFound, key, req.GetLoanStep().GetStepName().String(), req.GetLoanStep().GetStatus().String(), req.GetLoanStep().GetSubStatus().String(), lr.GetId(), "", "")
			logger.Info(ctx, fmt.Sprintf("user entered in second look flow, but has a no offer, actor_id: %s", lr.GetActorId()))
		}
	}

	// if second look next action not set and next action we got from any workflow stage perform response is not nil, override default with this next action value
	if !isSecondLookNextAction && req.GetNextAction() != nil {
		lr.NextAction = req.GetNextAction()
	}

	updateErr := p.loanRequestDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
	})
	if updateErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr update, err: %v", updateErr))
	}
	return nil, nil
}

func (p *Processor) pushApiResponseEventForSecondLook(ctx context.Context, actorId string, apiName string, status string, substatus string, offerId string, loanRequestId string, flowName string, ownership string) {
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		p.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewDiscoveryEvent(actorId, apiName, status, substatus, offerId, loanRequestId, flowName, ownership, palEvents.EventFlowNameSecondLook, palEvents.EventFlowNameSecondLook, true))
	})
}

func (p *Processor) pushSecondLookApiResponseEvent(ctx context.Context, actorId string, status string, substatus string, pastLoanOwnership string, pastLrStepName string, pastLrStatus string, pastLrSubstatus string, loanRequestId string, newOfferOwnership string, offerId string) {
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		p.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewSecondLookApiResponseEvent(actorId, status, substatus, pastLoanOwnership, pastLrStepName, pastLrStatus, pastLrSubstatus, loanRequestId, newOfferOwnership, offerId))
	})

}

func (p *Processor) pushSecondLookV1Event(ctx context.Context, actorId string, status string, substatus string, newOfferId string, newOfferOwnership string, pastLrId string, pasOfferOwnership string) {

	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		p.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewSecondLookV1Event(actorId, status, substatus, newOfferId, newOfferOwnership, pastLrId, palEvents.LoanApplicationFlow, pasOfferOwnership))
	})

}

// if the previously selected loan amount lies within the constraints of the new offer, then we calculate the minimum EMI for that loan amount, if not, we calculate the minimum EMI based on the valid amount that is closest to the previously selected amount
// if the selected amount is greater than the offer max amount, then this should be offer max amount.
// if the selected amount is less than the offer min amount, then this should be offer min amount.
func (p *Processor) getMinEmiAmountFromOldLrAmountAndNewOffer(ctx context.Context, lrAmount *moneyPb.Money, lrTenure int32, newOffer *palPb.LoanOffer) (*moneyPb.Money, bool, error) {
	defaultValueCalculator := p.calculatorFactory.GetDefaultValueCalculator(ctx, newOffer)
	var sameLoanOfferAmountTenure bool
	var newLoanAmount *moneyPb.Money
	switch {
	case moneyPkg.Compare(lrAmount, defaultValueCalculator.GetMinLoanAmount()) < 0:
		newLoanAmount = defaultValueCalculator.GetMinLoanAmount()
	case moneyPkg.Compare(lrAmount, newOffer.GetOfferConstraints().GetMaxLoanAmount()) > 0:
		newLoanAmount = newOffer.GetOfferConstraints().GetMaxLoanAmount()
	default:
		newLoanAmount = lrAmount
		if lrTenure <= newOffer.GetOfferConstraints().GetMaxTenureMonths() && lrTenure >= newOffer.GetOfferConstraints().GetMinTenureMonths() {
			sameLoanOfferAmountTenure = true
		}
	}
	calculator, calculatorErr := p.calculatorFactory.GetCalculator(
		ctx,
		calculatorTypes.NewRequest(
			palEnumsPb.CalculatorAccuracy_CALCULATOR_ACCURACY_ACCURATE,
			newLoanAmount,
			newOffer.GetOfferConstraints().GetMaxTenureMonths(),
			datetime.TimeToDateInLoc(time.Now(), datetime.IST),
			newOffer,
			nil,
		),
	)
	if calculatorErr != nil {
		return nil, false, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting calculator, err: %v", calculatorErr))
	}
	return calculator.GetEmiAmount(), sameLoanOfferAmountTenure, nil
}
