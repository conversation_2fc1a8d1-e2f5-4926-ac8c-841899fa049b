package stockguardian

import (
	"context"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/epifigrpc"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	sgLmsPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/lms"
	"github.com/epifi/gamma/preapprovedloan/calculator/types"
	"github.com/epifi/gamma/preapprovedloan/calculator/utils"
)

const (
	productId            = "PERSONAL_LOAN_FI_1"
	productIdEarlySalary = "EARLY_SALARY_FI_1"
)

type Provider struct {
	sgLmsClient sgLmsPb.LmsClient
}

func NewProvider(sgLmsClient sgLmsPb.LmsClient) *Provider {
	return &Provider{
		sgLmsClient: sgLmsClient,
	}
}

var _ types.CalculatorProvider = &Provider{}

// nolint: funlen
func (p *Provider) GetCalculator(
	ctx context.Context,
	req *types.Request,
) (types.Calculator, error) {
	interestRates := req.GetLoanOffer().GetProcessingInfo().GetInterestRate()
	if len(interestRates) == 0 {
		return nil, errors.New("interest rate not found in loan offer")
	}
	interestRatePercentage := interestRates[0].GetPercentage()
	product := getProductIdBasedOnLoanProgram(req.GetLoanOffer().GetLoanProgram())
	processingFees := req.GetLoanOffer().GetProcessingInfo().GetProcessingFee()
	if len(processingFees) == 0 {
		return nil, errors.New("processing fee not found in loan offer")
	}
	// e.g.: 2 (for 2% processing fee)
	processingFeeInPercentage := processingFees[0].GetPercentage()
	// e.g.: 18 (for 18% GST on processing fee)
	processingFeeGstPercentage := req.GetLoanOffer().GetProcessingInfo().GetGst()
	processingFeesIncludingGst := utils.AddPercentage(processingFeeInPercentage, processingFeeGstPercentage)

	calculateLoanScheduleRes, err := p.sgLmsClient.CalculateLoanSchedule(ctx,
		&sgLmsPb.CalculateLoanScheduleRequest{
			// TODO(sharath): get this from config after Anupam creates the config
			ProductId:       product,
			PrincipalAmount: req.GetLoanAmount(),
			// TODO: change logic if we support repayment frequency other than monthly
			TenureInMonths:                      req.GetTenureInMonths(),
			InterestRate:                        interestRatePercentage,
			ExpectedDisbursementDate:            req.GetLoanIssueDate(),
			ProcessingFeePercentageIncludingGst: processingFeesIncludingGst,
		},
	)
	if err = epifigrpc.RPCError(calculateLoanScheduleRes, err); err != nil {
		return nil, errors.Wrap(err, "failed to trigger sg lms calculate loan schedule")
	}

	if len(calculateLoanScheduleRes.GetInstallments()) == 0 {
		return nil, errors.New("no installments found in calculate loan schedule response")
	}

	if moneyPkg.IsZero(calculateLoanScheduleRes.GetTotalRepaymentExpected()) {
		return nil, errors.New("total repayment expected is zero")
	}

	totalPayable, err := getTotalPayable(calculateLoanScheduleRes)
	if err != nil {
		return nil, errors.Wrap(err, "failed to calculate total payable")
	}

	totalDeductions, err := getTotalDeductions(req.GetLoanAmount(), calculateLoanScheduleRes)
	if err != nil {
		return nil, errors.Wrap(err, "failed to calculate total deductions")

	}

	return &calculator{
		loanOffer:        req.GetLoanOffer(),
		loanAmount:       req.GetLoanAmount(),
		tenureInMonths:   req.GetTenureInMonths(),
		loanIssueDate:    req.GetLoanIssueDate(),
		calculateLoanRes: calculateLoanScheduleRes,
		totalPayable:     totalPayable,
		totalDeductions:  totalDeductions,
	}, nil
}

func getProductIdBasedOnLoanProgram(program palPb.LoanProgram) string {
	switch program {
	case palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:
		return productIdEarlySalary
	default:
		return productId
	}
}
