package session

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	authPb "github.com/epifi/gamma/api/auth"
	sessionPb "github.com/epifi/gamma/api/auth/session"
	"github.com/epifi/gamma/auth/config/genconf"
	wireTypes "github.com/epifi/gamma/auth/wire/types"
)

const (
	authSessionCachePrefixKey = "authSession:"
)

type SessionManager struct {
	sessionPb.UnimplementedSessionManagerServer
	config              *genconf.Config
	timeImpl            datetime.Time
	authClient          authPb.AuthClient
	sessionCacheStorage wireTypes.SessionAuthCacheStorage
}

func NewSessionManager(config *genconf.Config, timeImpl datetime.Time, authClient authPb.AuthClient, sessionCacheStorage wireTypes.SessionAuthCacheStorage) *SessionManager {
	return &SessionManager{
		config:              config,
		timeImpl:            timeImpl,
		authClient:          authClient,
		sessionCacheStorage: sessionCacheStorage,
	}
}

func (s *SessionManager) GetSessionLoginUrl(ctx context.Context, req *sessionPb.GetSessionLoginUrlRequest) (*sessionPb.GetSessionLoginUrlResponse, error) {
	if !s.config.NetworthMcpConfig().EnableSessionSigning() {
		return &sessionPb.GetSessionLoginUrlResponse{
			Status:          rpc.StatusOk(),
			SessionLoginUrl: s.config.NetworthMcpConfig().LoginUrl() + req.GetSessionId(),
		}, nil
	}
	sessionId := req.GetSessionId()
	timestamp := s.timeImpl.Now().Unix()

	// payload as sessionId|timestamp
	payload := fmt.Sprintf("%s|%d", sessionId, timestamp)

	signingKey := s.config.AuthSecrets().SessionSigningKeyNwMcp
	if signingKey == "" {
		logger.Error(ctx, "missing login session url signing key")
		return &sessionPb.GetSessionLoginUrlResponse{
			Status: rpc.StatusInternalWithDebugMsg("missing login session url signing key"),
		}, nil
	}
	h := hmac.New(sha256.New, []byte(signingKey))
	h.Write([]byte(payload))
	signature := h.Sum(nil)

	// encoding signature as base64
	encodedSignature := base64.URLEncoding.EncodeToString(signature)
	signedParams := fmt.Sprintf("%s.%s", payload, encodedSignature)

	token := url.QueryEscape(signedParams)
	loginUrl := s.config.NetworthMcpConfig().LoginUrl() + token

	return &sessionPb.GetSessionLoginUrlResponse{
		Status:          rpc.StatusOk(),
		SessionLoginUrl: loginUrl,
	}, nil
}

func (s *SessionManager) CreateSession(ctx context.Context, req *sessionPb.CreateSessionRequest) (*sessionPb.CreateSessionResponse, error) {
	err := s.validateCreateSessionRequest(req)
	if err != nil {
		logger.Info(ctx, "validate failed for create session request", zap.Error(err))
		return &sessionPb.CreateSessionResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}
	sessionId := req.GetSessionId()
	if s.config.NetworthMcpConfig().EnableSessionSigning() {
		sessionId, err = s.verifyAndGetSessionId(req.GetSessionId())
		if err != nil {
			logger.Info(ctx, "verification of signed session id failed", zap.Error(err))
			return &sessionPb.CreateSessionResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("verification of signed session id failed"),
			}, nil
		}
	}
	now := s.timeImpl.Now()
	createTokenRes, err := s.authClient.CreateToken(ctx, &authPb.CreateTokenRequest{
		ActorId:     req.GetActorId(),
		TokenType:   authPb.TokenType_NETWORTH_MCP_ACCESS_TOKEN,
		Device:      req.GetDevice(),
		PhoneNumber: req.GetPhoneNumber(),
	})
	if rpcErr := epifigrpc.RPCError(createTokenRes, err); rpcErr != nil {
		logger.Debug(ctx, "create token failed for create session", zap.Error(rpcErr))
		return &sessionPb.CreateSessionResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to create token for session"),
		}, nil
	}

	sessionDetails := &sessionPb.SessionDetails{
		SessionId:   sessionId,
		ActorId:     req.GetActorId(),
		AccessToken: createTokenRes.GetToken(),
		CreatedAt:   timestamppb.New(now),
		ExpiresAt:   timestamppb.New(now.Add(time.Second * time.Duration(s.config.Application().NetworthMcpAccessTokenSigningMethod.Duration))),
	}
	marshaledSessionDetails, err := proto.Marshal(sessionDetails)
	if err != nil {
		logger.Error(ctx, "failed to proto marshal session details", zap.Error(err))
		return &sessionPb.CreateSessionResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to proto marshal session details"),
		}, nil
	}

	sessionKey := s.getCacheKey(sessionId)
	cacheTTL := time.Second * time.Duration(s.config.Application().NetworthMcpAccessTokenSigningMethod.Duration)
	err = s.sessionCacheStorage.Set(ctx, sessionKey, string(marshaledSessionDetails), cacheTTL)
	if err != nil {
		logger.Error(ctx, "failed to cache session details", zap.Error(err))
		return &sessionPb.CreateSessionResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to set cache session details"),
		}, nil
	}
	return &sessionPb.CreateSessionResponse{
		Status:         rpc.StatusOk(),
		SessionDetails: sessionDetails,
	}, nil
}

func (s *SessionManager) ValidateSession(ctx context.Context, req *sessionPb.ValidateSessionRequest) (*sessionPb.ValidateSessionResponse, error) {
	sessionKey := s.getCacheKey(req.GetSessionId())
	cachedVal, err := s.sessionCacheStorage.Get(ctx, sessionKey)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to get session details from redis", zap.Error(err))
		return &sessionPb.ValidateSessionResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to get session details from redis"),
		}, nil
	}
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Debug(ctx, "session details not found in cache", zap.String("session_id", req.GetSessionId()))
		return &sessionPb.ValidateSessionResponse{
			Status: rpc.NewStatusWithoutDebug(uint32(sessionPb.ValidateSessionResponse_SESSION_EXPIRED), "session id not found in redis cache"),
		}, nil
	}

	sessionDetails := &sessionPb.SessionDetails{}
	if err = proto.Unmarshal([]byte(cachedVal), sessionDetails); err != nil {
		logger.Error(ctx, "failed to unmarshal session details", zap.Error(err))
		return &sessionPb.ValidateSessionResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to unmarshal session details"),
		}, nil
	}

	validateTokenRes, err := s.authClient.ValidateToken(ctx, &authPb.ValidateTokenRequest{
		Token:     sessionDetails.GetAccessToken(),
		TokenType: authPb.TokenType_NETWORTH_MCP_ACCESS_TOKEN,
	})
	if rpcErr := epifigrpc.RPCError(validateTokenRes, err); rpcErr != nil {
		switch validateTokenRes.GetStatus().GetCode() {
		case uint32(authPb.ValidateTokenResponse_NOT_FOUND), uint32(authPb.ValidateTokenResponse_TOKEN_INVALID), uint32(authPb.ValidateTokenResponse_TOKEN_EXPIRY):
			logger.Debug(ctx, "validate token expired", zap.String("session_id", req.GetSessionId()))
			return &sessionPb.ValidateSessionResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(sessionPb.ValidateSessionResponse_SESSION_EXPIRED), "session expired"),
			}, nil
		}
		logger.Error(ctx, "failed to validate token", zap.Error(rpcErr), zap.String("session_id", req.GetSessionId()))
		return &sessionPb.ValidateSessionResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to validate token for session"),
		}, nil
	}
	return &sessionPb.ValidateSessionResponse{
		Status:         rpc.StatusOk(),
		SessionDetails: sessionDetails,
	}, nil
}

func (s *SessionManager) validateCreateSessionRequest(req *sessionPb.CreateSessionRequest) error {
	if req.GetDevice().GetDeviceId() == "" {
		return fmt.Errorf("device id is empty for create session")
	}
	if req.GetPhoneNumber().GetNationalNumber() == 0 {
		return fmt.Errorf("invalid phone number for create session")
	}
	return nil
}

// verifyAndGetSessionId verifies a signed session parameter and extracts the session ID
func (s *SessionManager) verifyAndGetSessionId(signedSessionId string) (string, error) {
	signedParam, err := url.QueryUnescape(signedSessionId)
	if err != nil {
		return "", errors.Wrap(err, "failed to unescape signed session id")
	}

	// split signed params into payload and signature
	signedParamParts := strings.Split(signedParam, ".")
	if len(signedParamParts) != 2 {
		return "", errors.New("invalid signed param len not equal to 2")
	}
	payload := signedParamParts[0]
	providedSignature := signedParamParts[1]

	// decoding the provided signature
	decodedSignature, err := base64.URLEncoding.DecodeString(providedSignature)
	if err != nil {
		return "", errors.Wrap(err, "failed to decode provided signed payload")
	}

	// generating expected signature
	signingKey := s.config.AuthSecrets().SessionSigningKeyNwMcp
	if signingKey == "" {
		return "", errors.New("missing login session url signing key")
	}
	h := hmac.New(sha256.New, []byte(signingKey))
	h.Write([]byte(payload))
	expectedSignature := h.Sum(nil)

	// verifying decoded signature and expected signature should match
	if !hmac.Equal(decodedSignature, expectedSignature) {
		return "", errors.New("decoded signature does not match with expected signature")
	}

	// payload parts: sessionId|timestamp
	payloadParts := strings.Split(payload, "|")
	if len(payloadParts) != 2 {
		return "", errors.New("invalid payload len not equal to 2")
	}
	sessionId := payloadParts[0]
	return sessionId, nil
}

func (s *SessionManager) getCacheKey(sessionId string) string {
	return authSessionCachePrefixKey + sessionId
}
