package session

import (
	"context"
	"flag"
	"os"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	mock_cache "github.com/epifi/be-common/pkg/cache/mocks"
	mockDatetime "github.com/epifi/be-common/pkg/datetime/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"

	authPb "github.com/epifi/gamma/api/auth"
	mockAuth "github.com/epifi/gamma/api/auth/mocks"
	sessionPb "github.com/epifi/gamma/api/auth/session"
	"github.com/epifi/gamma/auth/config/genconf"
	"github.com/epifi/gamma/auth/test"
)

var (
	conf *genconf.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, teardown = test.InitTestServerWithoutDBConn()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mocks struct {
	mockTime       *mockDatetime.MockTime
	mockAuthClient *mockAuth.MockAuthClient
	mockCache      *mock_cache.MockCacheStorage
}

func initMocks(ctrl *gomock.Controller) *mocks {
	return &mocks{
		mockTime:       mockDatetime.NewMockTime(ctrl),
		mockAuthClient: mockAuth.NewMockAuthClient(ctrl),
		mockCache:      mock_cache.NewMockCacheStorage(ctrl),
	}
}

func TestSessionManager_GetSessionLoginUrl(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name       string
		args       *sessionPb.GetSessionLoginUrlRequest
		setupMocks func(f *mocks)
		wantUrl    string
	}{
		{
			name: "successfully generate login url",
			args: &sessionPb.GetSessionLoginUrlRequest{SessionId: "session_id"},
			setupMocks: func(f *mocks) {
				f.mockTime.EXPECT().Now().Return(time.Date(2925, 6, 15, 0, 0, 0, 0, time.Local))
			},
			wantUrl: "http://localhost:3000/wealth-mcp-login?token=session_id%7C30151161000.TMKa5i8wPVo2NOKY5aSM08KLafCky7Z_AmB9a9cokVE%3D",
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			m := initMocks(ctl)
			tt.setupMocks(m)
			mgr := NewSessionManager(conf, m.mockTime, m.mockAuthClient, m.mockCache)
			resp, err := mgr.GetSessionLoginUrl(context.Background(), tt.args)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if resp.GetSessionLoginUrl() != tt.wantUrl {
				t.Errorf("SessionLoginUrl = %q, want %q", resp.GetSessionLoginUrl(), tt.wantUrl)
			}
		})
	}
}

func TestSessionManager_CreateSession(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name       string
		req        *sessionPb.CreateSessionRequest
		setupMocks func(f *mocks)
		want       *sessionPb.CreateSessionResponse
		wantErr    bool
	}{
		{
			name: "empty device id, invalid arguments",
			req: &sessionPb.CreateSessionRequest{
				SessionId:   "session_id%7C30151161000.TMKa5i8wPVo2NOKY5aSM08KLafCky7Z_AmB9a9cokVE%3D",
				ActorId:     "actor_id",
				PhoneNumber: &common.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999},
			},
			setupMocks: func(f *mocks) {},
			want: &sessionPb.CreateSessionResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "empty phone number, invalid arguments",
			req: &sessionPb.CreateSessionRequest{
				SessionId: "session_id%7C30151161000.TMKa5i8wPVo2NOKY5aSM08KLafCky7Z_AmB9a9cokVE%3D",
				ActorId:   "actor_id",
				Device:    &common.Device{DeviceId: "device_id"},
			},
			setupMocks: func(f *mocks) {},
			want: &sessionPb.CreateSessionResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "CreateToken returns error",
			req: &sessionPb.CreateSessionRequest{
				SessionId:   "session_id%7C30151161000.TMKa5i8wPVo2NOKY5aSM08KLafCky7Z_AmB9a9cokVE%3D",
				ActorId:     "actor_id",
				Device:      &common.Device{DeviceId: "device_id"},
				PhoneNumber: &common.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999},
			},
			setupMocks: func(f *mocks) {
				f.mockTime.EXPECT().Now().Return(time.Date(2024, 6, 15, 0, 0, 0, 0, time.Local))
				f.mockAuthClient.EXPECT().CreateToken(gomock.Any(), &authPb.CreateTokenRequest{
					ActorId:     "actor_id",
					TokenType:   authPb.TokenType_NETWORTH_MCP_ACCESS_TOKEN,
					Device:      &common.Device{DeviceId: "device_id"},
					PhoneNumber: &common.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999},
				}).Return(&authPb.CreateTokenResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: &sessionPb.CreateSessionResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to create token for session"),
			},
			wantErr: false,
		},
		{
			name: "cache set error",
			req: &sessionPb.CreateSessionRequest{
				SessionId:   "session_id%7C30151161000.TMKa5i8wPVo2NOKY5aSM08KLafCky7Z_AmB9a9cokVE%3D",
				ActorId:     "actor_id",
				Device:      &common.Device{DeviceId: "device_id"},
				PhoneNumber: &common.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999},
			},
			setupMocks: func(f *mocks) {
				f.mockTime.EXPECT().Now().Return(time.Date(2024, 6, 15, 0, 0, 0, 0, time.Local))
				f.mockAuthClient.EXPECT().CreateToken(gomock.Any(), &authPb.CreateTokenRequest{
					ActorId:     "actor_id",
					TokenType:   authPb.TokenType_NETWORTH_MCP_ACCESS_TOKEN,
					Device:      &common.Device{DeviceId: "device_id"},
					PhoneNumber: &common.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999},
				}).Return(&authPb.CreateTokenResponse{
					Status: rpc.StatusOk(),
					Token:  "sample_token",
				}, nil)
				f.mockCache.EXPECT().Set(gomock.Any(), "authSession:session_id", gomock.Any(), gomock.Any()).Return(errors.New("cache error"))
			},
			want: &sessionPb.CreateSessionResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to set cache session details"),
			},
			wantErr: false,
		},
		{
			name: "successfully create session",
			req: &sessionPb.CreateSessionRequest{
				SessionId:   "session_id%7C30151161000.TMKa5i8wPVo2NOKY5aSM08KLafCky7Z_AmB9a9cokVE%3D",
				ActorId:     "actor_id",
				Device:      &common.Device{DeviceId: "device_id"},
				PhoneNumber: &common.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999},
			},
			setupMocks: func(f *mocks) {
				f.mockTime.EXPECT().Now().Return(time.Date(2024, 6, 15, 0, 0, 0, 0, time.Local))
				f.mockAuthClient.EXPECT().CreateToken(gomock.Any(), &authPb.CreateTokenRequest{
					ActorId:     "actor_id",
					TokenType:   authPb.TokenType_NETWORTH_MCP_ACCESS_TOKEN,
					Device:      &common.Device{DeviceId: "device_id"},
					PhoneNumber: &common.PhoneNumber{CountryCode: 91, NationalNumber: 9999999999},
				}).Return(&authPb.CreateTokenResponse{
					Status: rpc.StatusOk(),
					Token:  "sample_token",
				}, nil)
				f.mockCache.EXPECT().Set(gomock.Any(), "authSession:session_id", gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &sessionPb.CreateSessionResponse{
				Status: rpc.StatusOk(),
				SessionDetails: &sessionPb.SessionDetails{
					SessionId:   "session_id",
					ActorId:     "actor_id",
					AccessToken: "sample_token",
					CreatedAt:   timestamppb.New(time.Date(2024, 6, 15, 0, 0, 0, 0, time.Local)),
					ExpiresAt:   timestamppb.New(time.Date(2024, 6, 15, 0, 30, 0, 0, time.Local)),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			m := initMocks(ctl)
			tt.setupMocks(m)
			mgr := NewSessionManager(conf, m.mockTime, m.mockAuthClient, m.mockCache)
			got, err := mgr.CreateSession(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateSession() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("mismatch \n(-want +got):\n%s\n", diff)
			}
		})
	}
}

func TestSessionManager_ValidateSession(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name       string
		req        *sessionPb.ValidateSessionRequest
		setupMocks func(f *mocks)
		want       *sessionPb.ValidateSessionResponse
		wantErr    bool
	}{
		{
			name: "session found and token valid",
			req:  &sessionPb.ValidateSessionRequest{SessionId: "session_id"},
			setupMocks: func(f *mocks) {
				// Simulate cache hit with valid session details
				validSession := &sessionPb.SessionDetails{
					SessionId:   "session_id",
					ActorId:     "actor_id",
					AccessToken: "access_token",
				}
				bytes, _ := proto.Marshal(validSession)
				f.mockCache.EXPECT().Get(gomock.Any(), "authSession:session_id").Return(string(bytes), nil)
				f.mockAuthClient.EXPECT().ValidateToken(gomock.Any(), gomock.Any()).Return(&authPb.ValidateTokenResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &sessionPb.ValidateSessionResponse{
				Status: rpc.StatusOk(),
				SessionDetails: &sessionPb.SessionDetails{
					SessionId:   "session_id",
					ActorId:     "actor_id",
					AccessToken: "access_token",
				},
			},
			wantErr: false,
		},
		{
			name: "session not found in cache",
			req:  &sessionPb.ValidateSessionRequest{SessionId: "missing_session"},
			setupMocks: func(f *mocks) {
				f.mockCache.EXPECT().Get(gomock.Any(), "authSession:missing_session").Return("", epifierrors.ErrRecordNotFound)
			},
			want: &sessionPb.ValidateSessionResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(sessionPb.ValidateSessionResponse_SESSION_EXPIRED), "session id not found in redis cache"),
			},
			wantErr: false,
		},
		{
			name: "failed to get from redis, error",
			req:  &sessionPb.ValidateSessionRequest{SessionId: "session_id"},
			setupMocks: func(f *mocks) {
				f.mockCache.EXPECT().Get(gomock.Any(), "authSession:session_id").Return("", errors.New("error"))
			},
			want: &sessionPb.ValidateSessionResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to get session details from redis"),
			},
			wantErr: false,
		},
		{
			name: "token invalid/expired",
			req:  &sessionPb.ValidateSessionRequest{SessionId: "session_id"},
			setupMocks: func(f *mocks) {
				validSession := &sessionPb.SessionDetails{
					SessionId:   "session_id",
					ActorId:     "actor_id",
					AccessToken: "access_token",
				}
				bytes, _ := proto.Marshal(validSession)
				f.mockCache.EXPECT().Get(gomock.Any(), "authSession:session_id").Return(string(bytes), nil)
				f.mockAuthClient.EXPECT().ValidateToken(gomock.Any(), gomock.Any()).Return(&authPb.ValidateTokenResponse{
					Status: &rpc.Status{Code: uint32(authPb.ValidateTokenResponse_TOKEN_INVALID)},
				}, nil)
			},
			want: &sessionPb.ValidateSessionResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(sessionPb.ValidateSessionResponse_SESSION_EXPIRED), "session expired"),
			},
			wantErr: false,
		},
		{
			name: "token validation returns internal error",
			req:  &sessionPb.ValidateSessionRequest{SessionId: "session_id"},
			setupMocks: func(f *mocks) {
				validSession := &sessionPb.SessionDetails{
					SessionId:   "session_id",
					ActorId:     "actor_id",
					AccessToken: "access_token",
				}
				bytes, _ := proto.Marshal(validSession)
				f.mockCache.EXPECT().Get(gomock.Any(), "authSession:session_id").Return(string(bytes), nil)
				f.mockAuthClient.EXPECT().ValidateToken(gomock.Any(), gomock.Any()).Return(&authPb.ValidateTokenResponse{Status: rpc.StatusInternal()}, nil)
			},
			want: &sessionPb.ValidateSessionResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to validate token for session"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			m := initMocks(ctl)
			tt.setupMocks(m)
			mgr := NewSessionManager(conf, m.mockTime, m.mockAuthClient, m.mockCache)
			got, err := mgr.ValidateSession(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateSession() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("mismatch \n(-want +got):\n%s\n", diff)
			}
		})
	}
}
