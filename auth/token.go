package auth

import (
	"google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"reflect"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/nulltypes"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/afu"
	authNotificationPb "github.com/epifi/gamma/api/auth/notification"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/auth/config/genconf"
	"github.com/epifi/gamma/auth/dao/model"
	"github.com/epifi/gamma/pkg/obfuscator"
)

type signingMethod struct {
	// Duration of the token's validity
	duration time.Duration
	// Inactivity timer after which the token would be marked as inactive/expired
	inactivityTimer time.Duration
	// Active key
	activeKey []byte
	// Retired key
	retiredKey []byte
	// flag that denotes if retired key is present or not
	isRetiredKeyPresent bool
	// Hashing algo used in JWT signing
	algo string
}

type tokenClaims struct {
	IsSavingsAccountCreated commontypes.BooleanEnum `json:"is_savings_account_created"`
	IsNonResidentUser       commontypes.BooleanEnum `json:"is_non_resident_user"`
	jwt.RegisteredClaims
}

type signTokenRequest struct {
	id                string
	tokenType         authPb.TokenType
	isSACreated       commontypes.BooleanEnum
	isNonResidentUser commontypes.BooleanEnum
}

// nolint: funlen
func initTokenConfigMap(conf *genconf.Config) {
	signingMethods = map[authPb.TokenType]signingMethod{
		authPb.TokenType_ACCESS_TOKEN: {
			duration:            time.Second * time.Duration(conf.Application().UserAccessTokenSigningMethod.Duration),
			inactivityTimer:     time.Second * time.Duration(conf.Application().UserAccessTokenSigningMethod.InActivityTimer),
			activeKey:           []byte(conf.AuthSecrets().ActiveUserAccessTokenSigningKey),
			retiredKey:          []byte(conf.AuthSecrets().RetiredUserAccessTokenSigningKey),
			isRetiredKeyPresent: conf.Application().UserAccessTokenSigningMethod.IsRetiredKeyPresent,
			algo:                conf.Application().UserAccessTokenSigningMethod.Algo,
		},
		authPb.TokenType_REFRESH_TOKEN: {
			duration:            time.Second * time.Duration(conf.Application().UserRefreshTokenSigningMethod.Duration),
			inactivityTimer:     time.Second * time.Duration(conf.Application().UserRefreshTokenSigningMethod.InActivityTimer),
			activeKey:           []byte(conf.AuthSecrets().ActiveUserRefreshTokenSigningKey),
			retiredKey:          []byte(conf.AuthSecrets().RetiredUserRefreshTokenSigningKey),
			isRetiredKeyPresent: conf.Application().UserRefreshTokenSigningMethod.IsRetiredKeyPresent,
			algo:                conf.Application().UserRefreshTokenSigningMethod.Algo,
		},
		authPb.TokenType_WAITLIST_ACCESS_TOKEN: {
			duration:            time.Second * time.Duration(conf.Application().WaitlistUserAccessTokenSigningMethod.Duration),
			inactivityTimer:     time.Second * time.Duration(conf.Application().WaitlistUserAccessTokenSigningMethod.InActivityTimer),
			activeKey:           []byte(conf.AuthSecrets().ActiveWaitlistUserAccessTokenSigningKey),
			retiredKey:          []byte(conf.AuthSecrets().RetiredWaitlistUserAccessTokenSigningKey),
			isRetiredKeyPresent: conf.Application().UserAccessTokenSigningMethod.IsRetiredKeyPresent,
			algo:                conf.Application().UserAccessTokenSigningMethod.Algo,
		},
		authPb.TokenType_APP_INSIGHTS_ACCESS_TOKEN: {
			duration:            time.Second * time.Duration(conf.Application().AppInsightsAccessTokenSigningMethod.Duration),
			inactivityTimer:     time.Second * time.Duration(conf.Application().AppInsightsAccessTokenSigningMethod.InActivityTimer),
			activeKey:           []byte(conf.AuthSecrets().ActiveAppInsightsAccessTokenSigningKey),
			isRetiredKeyPresent: conf.Application().AppInsightsAccessTokenSigningMethod.IsRetiredKeyPresent,
			algo:                conf.Application().AppInsightsAccessTokenSigningMethod.Algo,
		},
		authPb.TokenType_CHATBOT_ACCESS_TOKEN: {
			duration:            time.Second * time.Duration(conf.Application().ChatbotAccessTokenSigningMethod.Duration),
			inactivityTimer:     time.Second * time.Duration(conf.Application().ChatbotAccessTokenSigningMethod.InActivityTimer),
			activeKey:           []byte(conf.AuthSecrets().ActiveChatbotAccessTokenSigningKey),
			isRetiredKeyPresent: conf.Application().ChatbotAccessTokenSigningMethod.IsRetiredKeyPresent,
			algo:                conf.Application().ChatbotAccessTokenSigningMethod.Algo,
		},
		authPb.TokenType_WEB_LITE_ACCESS_TOKEN: {
			duration:            time.Second * time.Duration(conf.Application().WebLiteAccessTokenSigningMethod.Duration),
			inactivityTimer:     time.Second * time.Duration(conf.Application().WebLiteAccessTokenSigningMethod.InActivityTimer),
			activeKey:           []byte(conf.AuthSecrets().ActiveWebLiteAccessTokenSigningKey),
			isRetiredKeyPresent: conf.Application().WebLiteAccessTokenSigningMethod.IsRetiredKeyPresent,
			algo:                conf.Application().WebLiteAccessTokenSigningMethod.Algo,
		},
		authPb.TokenType_GENIE_REFRESH_TOKEN: {
			duration:            time.Second * time.Duration(conf.Application().GenieRefreshTokenSigningMethod.Duration),
			inactivityTimer:     time.Second * time.Duration(conf.Application().GenieRefreshTokenSigningMethod.InActivityTimer),
			activeKey:           []byte(conf.AuthSecrets().ActiveUserRefreshTokenSigningKey),
			retiredKey:          []byte(conf.AuthSecrets().RetiredUserRefreshTokenSigningKey),
			isRetiredKeyPresent: conf.Application().GenieRefreshTokenSigningMethod.IsRetiredKeyPresent,
			algo:                conf.Application().GenieRefreshTokenSigningMethod.Algo,
		},
		authPb.TokenType_GENIE_ACCESS_TOKEN: {
			duration:            time.Second * time.Duration(conf.Application().GenieAccessTokenSigningMethod.Duration),
			inactivityTimer:     time.Second * time.Duration(conf.Application().GenieAccessTokenSigningMethod.InActivityTimer),
			activeKey:           []byte(conf.AuthSecrets().ActiveUserAccessTokenSigningKey),
			retiredKey:          []byte(conf.AuthSecrets().RetiredUserAccessTokenSigningKey),
			isRetiredKeyPresent: conf.Application().GenieAccessTokenSigningMethod.IsRetiredKeyPresent,
			algo:                conf.Application().GenieAccessTokenSigningMethod.Algo,
		},
		authPb.TokenType_WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_REFRESH_TOKEN: {
			duration:            time.Second * time.Duration(conf.Application().WebCABTRefreshTokenSigningMethod.Duration),
			inactivityTimer:     time.Second * time.Duration(conf.Application().WebCABTRefreshTokenSigningMethod.InActivityTimer),
			activeKey:           []byte(conf.AuthSecrets().ActiveUserRefreshTokenSigningKey),
			retiredKey:          []byte(conf.AuthSecrets().RetiredUserRefreshTokenSigningKey),
			isRetiredKeyPresent: conf.Application().WebCABTRefreshTokenSigningMethod.IsRetiredKeyPresent,
			algo:                conf.Application().WebCABTRefreshTokenSigningMethod.Algo,
		},
		authPb.TokenType_WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_ACCESS_TOKEN: {
			duration:            time.Second * time.Duration(conf.Application().WebCABTAccessTokenSigningMethod.Duration),
			inactivityTimer:     time.Second * time.Duration(conf.Application().WebCABTAccessTokenSigningMethod.InActivityTimer),
			activeKey:           []byte(conf.AuthSecrets().ActiveUserAccessTokenSigningKey),
			retiredKey:          []byte(conf.AuthSecrets().RetiredUserAccessTokenSigningKey),
			isRetiredKeyPresent: conf.Application().WebCABTAccessTokenSigningMethod.IsRetiredKeyPresent,
			algo:                conf.Application().WebCABTAccessTokenSigningMethod.Algo,
		},
		authPb.TokenType_RISK_OUTCALL_WEBFORM_ACCESS_TOKEN: {
			duration:            time.Second * time.Duration(conf.Application().RiskOutcallWebformAccessTokenSigningMethod.Duration),
			inactivityTimer:     time.Second * time.Duration(conf.Application().RiskOutcallWebformAccessTokenSigningMethod.InActivityTimer),
			activeKey:           []byte(conf.AuthSecrets().ActiveUserAccessTokenSigningKey),
			retiredKey:          []byte(conf.AuthSecrets().RetiredUserAccessTokenSigningKey),
			isRetiredKeyPresent: conf.Application().RiskOutcallWebformAccessTokenSigningMethod.IsRetiredKeyPresent,
			algo:                conf.Application().RiskOutcallWebformAccessTokenSigningMethod.Algo,
		},
		authPb.TokenType_WEB_ACCESS_TOKEN: {
			duration:            time.Second * time.Duration(conf.Application().WebLiteAccessTokenSigningMethod.Duration),
			inactivityTimer:     time.Second * time.Duration(conf.Application().WebLiteAccessTokenSigningMethod.InActivityTimer),
			activeKey:           []byte(conf.AuthSecrets().ActiveWebLiteAccessTokenSigningKey),
			isRetiredKeyPresent: conf.Application().WebLiteAccessTokenSigningMethod.IsRetiredKeyPresent,
			algo:                conf.Application().WebLiteAccessTokenSigningMethod.Algo,
		},
		authPb.TokenType_NETWORTH_MCP_ACCESS_TOKEN: {
			duration:            time.Second * time.Duration(conf.Application().NetworthMcpAccessTokenSigningMethod.Duration),
			inactivityTimer:     time.Second * time.Duration(conf.Application().NetworthMcpAccessTokenSigningMethod.InActivityTimer),
			activeKey:           []byte(conf.AuthSecrets().ActiveNwMcpAccessTokenSigningKey),
			isRetiredKeyPresent: conf.Application().NetworthMcpAccessTokenSigningMethod.IsRetiredKeyPresent,
			algo:                conf.Application().NetworthMcpAccessTokenSigningMethod.Algo,
		},
	}
}

var (
	signingMethods map[authPb.TokenType]signingMethod

	statusTokenInvalid rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(
			uint32(authPb.ValidateTokenResponse_TOKEN_INVALID),
			"Invalid token",
			"Invalid format or token expired",
		)
	}
	statusDeviceIdMismatch rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(
			uint32(authPb.ValidateTokenResponse_DEVICE_ID_MISMATCH),
			"Device ID mismatch",
			"Device ID from request didn't match with existing one",
		)
	}
	statusTokenExpiry rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(
			uint32(authPb.ValidateTokenResponse_TOKEN_EXPIRY),
			"Token is expired",
			"Input token is not valid - use a new token",
		)
	}

	updateTokenReqStatusMap = map[authPb.UpdateTokenRequest_TokenStatus]model.TokenStatus{
		authPb.UpdateTokenRequest_DELETE: model.TokenDeleted,
		authPb.UpdateTokenRequest_REVOKE: model.TokenRevoked,
	}

	/*
	 * Errors
	 */
	errInvalidIdentifier     = fmt.Errorf("unknown request identifier")
	errUnexpectedSigningAlgo = fmt.Errorf("unexpected signing algo")
)

// signToken creates a token by signing the input data to generating a token
// Validity of the signed token and key used to sign the token is decided by the tokenType parameter value
//
//	Note:
//	1. There is no expiry mechanism build into this function that would expire previously generated tokens
//	2. Previously generated tokens will be valid until the signing key is changed.
//	3. Caller function has to take this into account and add additional business logic to manage expiry mechanism.
//	One way to implement this is to maintain token identifier(Example: signing timestamp) in the persistent data-store
//	and verify it when the token is presented
//	4. Signing is not the same as encryption. Signing only ensures the data integrity and verify the signing
//	authority. `data` is not encrypted and can be read by anyone with access to the token. Do not provide
//	sensitive information as part of `data`
func (s *Service) signToken(req *signTokenRequest) (signedToken string, err error) {
	sm, ok := signingMethods[req.tokenType]
	if !ok {
		err = fmt.Errorf("token Type not implemented: %v", req.tokenType)
		return
	}
	// Get the signing algo
	alg := jwt.GetSigningMethod(sm.algo)
	if alg == nil {
		err = fmt.Errorf("couldn't find signing method: %v", alg)
		return
	}

	// Create a new token object, specifying signing method and the inputClaim you would like it to contain.
	// TODO(pruthvi): Add kid as a claim
	tNow := s.TimeClient.Now()
	claims := &tokenClaims{
		IsSavingsAccountCreated: req.isSACreated,
		IsNonResidentUser:       req.isNonResidentUser,
		RegisteredClaims: jwt.RegisteredClaims{
			Subject:   req.id,
			ID:        req.id,
			NotBefore: jwt.NewNumericDate(tNow),
			ExpiresAt: jwt.NewNumericDate(tNow.Add(sm.duration)),
		},
	}

	// create a new token
	token := jwt.NewWithClaims(alg, claims)
	signedToken, err = token.SignedString(sm.activeKey)
	if err != nil {
		err = fmt.Errorf("error signing token: %w", err)
		return
	}
	return
}

// Verify a JWT token and return the subject. subject is the identifier of token.
// In our case, it's the ID field in model.TokenStore.
func verifyToken(signedToken string, tokenType authPb.TokenType) (*tokenClaims, error) {
	var (
		token *jwt.Token
		err   error
	)
	sm, ok := signingMethods[tokenType]
	if !ok {
		return nil, fmt.Errorf("token Type not implemented: %v", tokenType)
	}

	// Get the signing algo
	alg := jwt.GetSigningMethod(sm.algo)
	if alg == nil {
		return nil, fmt.Errorf("couldn't find signing method: %v", alg)
	}

	// Parse the token.  Load the key from command line option
	claims := &tokenClaims{}
	if token, err = jwt.ParseWithClaims(signedToken, claims, claimsParsingKeyFunc(alg, sm.activeKey)); err != nil {
		// if fallback to retried key not required return early
		if !isSignatureErr(err) || !sm.isRetiredKeyPresent {
			return nil, err
		}
		// try parsing with retired key as a fallback
		if token, err = jwt.ParseWithClaims(signedToken, claims, claimsParsingKeyFunc(alg, sm.retiredKey)); err != nil {
			return nil, fmt.Errorf("unable to verify with retired key: %w", err)
		}
	}
	if !token.Valid || claims.RegisteredClaims.Subject == "" || claims.RegisteredClaims.ID == "" {
		return nil, errors.New("token is invalid")
	}
	// returning token id stored in the subject field of the claims
	return claims, nil
}

//nolint:unparam
func getIdFromClaims(claims *tokenClaims) (string, error) {
	return claims.ID, nil
}

//nolint:unparam
func getIdFromToken(signedToken string, tokenType authPb.TokenType) (string, error) {
	claims, err := verifyToken(signedToken, tokenType)
	if err != nil {
		return "", err
	}
	return getIdFromClaims(claims)
}

func isSignatureErr(err error) bool {
	return errors.Is(err, jwt.ErrSignatureInvalid) || errors.Is(err, jwt.ErrTokenSignatureInvalid)
}

func claimsParsingKeyFunc(alg jwt.SigningMethod, key []byte) func(t *jwt.Token) (interface{}, error) {
	return func(t *jwt.Token) (interface{}, error) {
		// Validate if the algo is what you expect
		if !reflect.DeepEqual(alg, t.Method) {
			return nil, fmt.Errorf("%w: signing algo got: %v expected :%v", errUnexpectedSigningAlgo, t.Header["alg"], alg)
		}
		return key, nil
	}
}

// nolint: funlen
func (s *Service) CreateToken(ctx context.Context, req *authPb.CreateTokenRequest) (*authPb.CreateTokenResponse, error) {
	if s.shouldEnableTokenManager(ctx, req.GetActorId(), obfuscator.HashedPhoneNum(req.GetPhoneNumber())) {
		createTokenRes, err := s.tokenManager.CreateToken(ctx, req)
		s.tryPublishTokenCreationEvent(ctx, createTokenRes, req.GetActorId(), req.GetTokenType())
		return createTokenRes, err
	}
	res := &authPb.CreateTokenResponse{}
	actorId := req.GetActorId()
	tokenId := uuid.New().String()
	token, err := s.signToken(&signTokenRequest{
		id:                tokenId,
		tokenType:         req.GetTokenType(),
		isSACreated:       req.GetAccessLevelFlags().GetIsAccountCreated(),
		isNonResidentUser: req.GetIsNonResidentUser(),
	})
	if err != nil {
		logger.Error(ctx, "error in generating a token", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	var deviceRegStatus authPb.DeviceRegistrationStatus
	// override registration status if user access is revoked or account is not yet created
	if req.GetAccessLevelFlags() != nil && req.GetAccessLevelFlags().GetIsAccessRevoked() == commontypes.BooleanEnum_TRUE {
		deviceRegStatus = authPb.DeviceRegistrationStatus_UNREGISTERED
	}

	// We're not setting DeviceRegistrationStatus when Auth Factor Update
	// flow in active. It's to restrict the access scope for the token
	// generated for updating the auth factor.
	if req.GetAuthFactorUpdateId() == "" && deviceRegStatus == authPb.DeviceRegistrationStatus_DEVICE_STATUS_UNSPECIFIED {
		deviceRegStatus, err = s.deviceRegStatus(ctx, actorId, req.GetDevice().GetDeviceId())
		if err != nil {
			res.Status = rpc.StatusInternal()
			res.Status.SetDebugMessage(err.Error())
			return res, nil
		}
	}

	lastActivity := time.Now()
	tokenEntry := &model.TokenStore{
		ID:                       tokenId,
		Status:                   model.TokenActive,
		Token:                    token,
		TokenType:                req.GetTokenType(),
		LastActivity:             lastActivity,
		PhoneNumber:              req.GetPhoneNumber(),
		ActorID:                  actorId,
		Device:                   req.GetDevice(),
		Email:                    req.GetEmail(),
		DeviceRegistrationStatus: deviceRegStatus,
		AuthFactorUpdateId:       nulltypes.NewNullString(req.GetAuthFactorUpdateId()),
	}

	goroutine.Run(ctx, 20*time.Second, func(ctx context.Context) {
		if req.GetTokenType() == authPb.TokenType_REFRESH_TOKEN {
			_, _ = s.tokenStoresDao.ExpireTokenByPhoneNumber(ctx, authPb.TokenType_REFRESH_TOKEN, model.TokenExpired, req.GetPhoneNumber(), authPb.TokenDeletionReason_TOKEN_DELETION_REASON_TOKEN_REFRESH, tokenId)
			// Adding this as a hot fix for peak traffic handling. Minerva PGDB CPU usage is hitting 100% during peak traffic.
			// Top queries are StoreToken and ExpireTokenByActorId. Removing ExpireTokenByActorId query will not impact
			// business logic but might have a user with multiple active access tokens at a time.
		} else if req.GetTokenType() != authPb.TokenType_ACCESS_TOKEN {
			_, _ = s.tokenStoresDao.ExpireTokenByActorId(ctx, actorId, tokenId, req.GetTokenType(), authPb.TokenDeletionReason_TOKEN_DELETION_REASON_TOKEN_REFRESH, s.deviceRegStatusesToExpire(deviceRegStatus)...)
		}
	})

	if err := s.tokenStoresDao.StoreToken(ctx, tokenEntry, &model.StoreTokenParams{
		IsUnOnboardedUser: isUnOnboardedUserFlag(deviceRegStatus, req.GetAccessLevelFlags()),
	}); err != nil {
		logger.Error(ctx, "Error in storing token in database", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Status = rpc.StatusOk()
	res.Token = token
	res.DeviceRegStatus = deviceRegStatus
	res.TokenMetadata = &authPb.TokenMetadata{
		DeviceRegStatus: deviceRegStatus,
	}
	return res, nil
}

func (s *Service) tryPublishTokenCreationEvent(ctx context.Context, createTokenRes *authPb.CreateTokenResponse, actorId string, tokenType authPb.TokenType) {
	goroutine.Run(ctx, 20*time.Second, func(ctx context.Context) {
		if !createTokenRes.GetStatus().IsSuccess() {
			return
		}
		// ignoring refresh token for now as actorId may not be present
		if tokenType == authPb.TokenType_REFRESH_TOKEN {
			return
		}
		_, err := s.authTokenCreationPublisher.Publish(ctx, &authNotificationPb.AuthTokenCreationEvent{
			ActorId:   actorId,
			TokenType: tokenType,
			CreatedAt: timestamppb.Now(),
		})
		if err != nil {
			logger.Error(ctx, "error in publishing token creation event", zap.Error(err))
		}
	})
}

// isUnOnboardedUserFlag returns true for unOnboarded users
// We are considering users whose device registration is not done or account not created as unOnboarded users
// We upgrade token using actorID in these cases which will fail if we stop DB writes. For these users, we create DB entries to avoid drop offs in onboarding funnel
func isUnOnboardedUserFlag(deviceReg authPb.DeviceRegistrationStatus, flags *authPb.AccessLevelFlags) commontypes.BooleanEnum {
	if deviceReg == authPb.DeviceRegistrationStatus_UNREGISTERED || !flags.GetIsAccountCreated().ToBool() {
		return commontypes.BooleanEnum_TRUE
	}
	return commontypes.BooleanEnum_FALSE
}

//nolint:funlen
func (s *Service) ValidateToken(ctx context.Context, req *authPb.ValidateTokenRequest) (*authPb.ValidateTokenResponse, error) {
	res := &authPb.ValidateTokenResponse{}

	// to avoid getting inapplicable error logs
	if req.GetToken() == "" {
		logger.Info(ctx, "empty token")
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	}
	// TODO(pruthvi): Generalise ValidateToken to isAuthorised
	claims, err := verifyToken(req.GetToken(), req.GetTokenType())
	if err != nil {
		res.Status = getStatusFromJwtError(ctx, req.GetToken(), req.GetTokenType(), req.GetDevice().GetDeviceId(), err)
		return res, nil
	}
	// We are not aware whether the subject present in the token is actor id or hashed phone number, so we send the subject value for both
	if s.shouldEnableTokenManager(ctx, claims.Subject, claims.Subject) {
		return s.tokenManager.ValidateToken(ctx, req)
	}
	id, err := getIdFromClaims(claims)
	if err != nil {
		logger.Error(ctx, "error to get token ID from claims", zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return res, nil
	}
	isSACreated := claims.IsSavingsAccountCreated.ToBool()
	tokenEntry, err := s.tokenStoresDao.GetTokenById(ctx, id)
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			res.Status = rpc.StatusRecordNotFound()
		} else {
			logger.Error(ctx, "Error in fetching the token by id", zap.Error(err))
			res.Status = rpc.StatusInternal()
		}
		return res, nil
	}

	if time.Since(tokenEntry.LastActivity) > signingMethods[req.TokenType].inactivityTimer {
		res.Status = statusTokenExpiry()
		return res, nil
	}

	// Actor is created only after phone + email is created
	// Therefore when refresh token is validated and if actor wasn't created yet, actorID can be nil
	if tokenEntry.ActorID != "" {
		res.ActorId = tokenEntry.ActorID
	}

	// validate Device id if it's set in request.
	// TODO(sakthi) verify if any other info from Device has to be verified here
	if req.Device != nil && tokenEntry.Device.GetDeviceId() != req.Device.GetDeviceId() {
		res.Status = statusDeviceIdMismatch()
		return res, nil
	}

	if res.DeviceRegStatus, err = s.checkAFUAndDeleteToken(ctx, tokenEntry); err != nil && !storagev2.IsRecordNotFoundError(err) {
		logger.Error(ctx, "error in check afu and delete token", zap.Error(err),
			zap.String("tokenID", tokenEntry.ID),
		)
	}

	res.IsSavingsAccountCreated = isSACreated
	res.PhoneNumber = tokenEntry.PhoneNumber
	res.AuthFactorUpdateId = tokenEntry.AuthFactorUpdateId.GetValue()
	res.Status = rpc.StatusOk()
	res.Email = tokenEntry.Email
	res.TokenDetails = convertTokenDetailsModelToProto(tokenEntry)
	res.IsNonResidentUser = claims.IsNonResidentUser
	return res, nil
}

func (s *Service) GetDeviceDetails(ctx context.Context, req *authPb.GetDeviceDetailsRequest) (*authPb.GetDeviceDetailsResponse, error) {
	token, err := s.tokenStoresDao.GetLatestTokenByActorIdAndTokenType(ctx, req.GetActorId(), authPb.TokenType_ACCESS_TOKEN, true)
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			// get device registration as a fallback
			devReg, getDevRegErr := s.devRegDao.GetDeviceRegistration(ctx, req.GetActorId(), "", &model.OptionalParams{
				UseCache: true,
			})
			if getDevRegErr != nil {
				logger.Error(ctx, "error in fetching device registration", zap.Error(getDevRegErr))
			} else {
				return &authPb.GetDeviceDetailsResponse{
					Status:                   rpc.StatusOk(),
					Device:                   devReg.GetDevice(),
					DeviceRegistrationStatus: devReg.GetStatus(),
					ResponseVersion:          authPb.GetDeviceDetailsResponse_VERSION_V2,
				}, nil
			}
			return &authPb.GetDeviceDetailsResponse{
				Status: rpc.NewStatus(uint32(authPb.GetDeviceDetailsResponse_NOT_FOUND),
					"No record found for the given actor", err.Error()),
			}, nil
		}
		logger.Error(ctx, "Failed to fetch token", zap.String(logger.ACTOR_ID_V2, req.ActorId), zap.Error(err))
		return &authPb.GetDeviceDetailsResponse{
			Status: rpc.NewStatus(uint32(authPb.GetDeviceDetailsResponse_INTERNAL),
				"", err.Error()),
		}, nil
	}

	return &authPb.GetDeviceDetailsResponse{
		Status:                   rpc.StatusOk(),
		Device:                   token.Device,
		DeviceRegistrationStatus: token.DeviceRegistrationStatus,
		ResponseVersion:          authPb.GetDeviceDetailsResponse_VERSION_V2,
	}, nil
}

func (s *Service) CreateHandshakeToken(ctx context.Context, req *authPb.CreateHandshakeTokenRequest) (*authPb.CreateHandshakeTokenResponse, error) {
	var (
		token, email, actorId string
		err                   error
		phoneNumber           *commontypes.PhoneNumber
		device                *commontypes.Device
	)
	switch req.GetTokenType() {
	case authPb.TokenType_BKYC_HANDSHAKE_TOKEN:
		token, err = s.createBKYCHandshakeToken(req.GetBkycTokenMetadata())
		email = req.GetBkycTokenMetadata().GetEmail()
		device = req.GetBkycTokenMetadata().GetDevice()
		phoneNumber = req.GetBkycTokenMetadata().GetPhoneNumber()
		actorId = req.GetBkycTokenMetadata().GetRequesterActorId()
	default:
		return &authPb.CreateHandshakeTokenResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("unimplemented token type"),
		}, nil
	}
	if err != nil {
		logger.Error(ctx, "error in generating a token", zap.Error(err))
		return &authPb.CreateHandshakeTokenResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	lastActivity := time.Now()
	tokenEntry := &model.TokenStore{
		ID:           uuid.New().String(),
		Status:       model.TokenActive,
		Token:        token,
		Email:        email,
		TokenType:    req.GetTokenType(),
		LastActivity: lastActivity,
		PhoneNumber:  phoneNumber,
		ActorID:      actorId,
		Device:       device,
	}

	if err := s.tokenStoresDao.StoreToken(ctx, tokenEntry); err != nil {
		logger.Error(ctx, "error in storing token in database", zap.Error(err))
		return &authPb.CreateHandshakeTokenResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &authPb.CreateHandshakeTokenResponse{
		Status: rpc.StatusOk(),
		Token:  token,
	}, nil
}

func (s *Service) createBKYCHandshakeToken(metadata *authPb.BKYCTokenMetadata) (string, error) {
	type bkycClaims struct {
		jwt.RegisteredClaims
		RequesterActorId string `json:"requester_actor_id"`
		ProviderActorId  string `json:"provider_actor_id"`
	}
	timeNow := s.TimeClient.Now()
	claims := &bkycClaims{
		ProviderActorId:  metadata.GetProviderActorId(),
		RequesterActorId: metadata.GetRequesterActorId(),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(timeNow.Add(time.Second * time.Duration(s.Conf.Application().BKYCHandshakeTokenSigningMethod.Duration))),
			NotBefore: jwt.NewNumericDate(timeNow),
			IssuedAt:  jwt.NewNumericDate(timeNow),
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS512, claims)
	signedString, err := token.SignedString([]byte(s.Conf.AuthSecrets().BKYCHandshakeTokenSigningKey))
	if err != nil {
		return "", err
	}
	return signedString, nil
}

func (s *Service) VerifyHandshakeToken(ctx context.Context, req *authPb.VerifyHandshakeTokenRequest) (*authPb.VerifyHandshakeTokenResponse, error) {
	var key string
	switch req.GetTokenType() {
	case authPb.TokenType_BKYC_HANDSHAKE_TOKEN:
		key = s.Conf.AuthSecrets().BKYCHandshakeTokenSigningKey
	default:
		return &authPb.VerifyHandshakeTokenResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("unimplemented token type"),
		}, nil
	}
	claims := jwt.MapClaims{}
	_, err := jwt.ParseWithClaims(req.GetToken(), claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(key), nil
	})
	if err != nil {
		logger.Error(ctx, "error in validating token", zap.Error(err))
		return &authPb.VerifyHandshakeTokenResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	requesterActorId, ok := claims["requester_actor_id"].(string)
	if !ok {
		logger.Error(ctx, "error in casting requester actor id", zap.Error(err))
		return &authPb.VerifyHandshakeTokenResponse{
			Status: rpc.StatusInternalWithDebugMsg("requester actor id not able to be parsed"),
		}, nil
	}
	providerActorId, ok := claims["provider_actor_id"].(string)
	if !ok {
		logger.Error(ctx, "error in casting requester actor id", zap.Error(err))
		return &authPb.VerifyHandshakeTokenResponse{
			Status: rpc.StatusInternalWithDebugMsg("requester actor id not able to be parsed"),
		}, nil
	}
	return &authPb.VerifyHandshakeTokenResponse{
		Status:           rpc.StatusOk(),
		RequesterActorId: requesterActorId,
		ProviderActorId:  providerActorId,
	}, nil
}

func (s *Service) checkAFUAndDeleteToken(ctx context.Context, tokenEntry *model.TokenStore) (authPb.DeviceRegistrationStatus, error) {
	afuId := tokenEntry.AuthFactorUpdateId.GetValue()
	// validate pre conditions
	if afuId == "" || tokenEntry.DeviceRegistrationStatus == authPb.DeviceRegistrationStatus_REGISTERED {
		return tokenEntry.DeviceRegistrationStatus, nil
	}

	// get AFU record
	afuRecord, err := s.GetAFUByID(ctx, afuId)
	if err != nil {
		return 0, err
	}

	// validate overall status
	if afuRecord.OverallStatus != afu.OverallStatus_OVERALL_STATUS_COMPLETED {
		// if AFU is not complete, let things be as they're
		return tokenEntry.DeviceRegistrationStatus, nil
	}

	if _, err = s.tokenStoresDao.ExpireToken(ctx, tokenEntry.ID, authPb.TokenDeletionReason_TOKEN_DELETION_REASON_REONBOARDING_TOKEN_UPGRADE); err != nil {
		logger.Error(ctx, "error in expiring token", zap.Error(err))
		return 0, err
	}
	return authPb.DeviceRegistrationStatus_REGISTERED, nil
}

// deleteToken deletes the low level access token and thus prescribing client to create new token with the updated device registration status
func (s *Service) deleteToken(ctx context.Context, accessToken string) error {
	// get id from token
	id, err := getIdFromToken(accessToken, authPb.TokenType_ACCESS_TOKEN)
	if err != nil {
		logger.Info(ctx, "error in verifyToken", zap.Error(err))
		return fmt.Errorf("error in verifyToken: %w", err)
	}

	// delete current token
	// and thus prescribing client to create new token with the updated device registration status
	if _, err = s.tokenStoresDao.ExpireToken(ctx, id, authPb.TokenDeletionReason_TOKEN_DELETION_REASON_REONBOARDING_TOKEN_UPGRADE); err != nil {
		logger.Error(ctx, "error in deleting token")
		return err
	}

	return nil
}

// deviceRegStatus gets device registration status from device_registrations table
func (s *Service) deviceRegStatus(ctx context.Context, actorId, deviceId string) (authPb.DeviceRegistrationStatus, error) {
	record, err := s.devRegDao.GetDeviceRegistration(ctx, "", deviceId)
	if storagev2.IsRecordNotFoundError(err) {
		return authPb.DeviceRegistrationStatus_UNREGISTERED, nil
	}
	if err != nil {
		logger.Error(ctx, "error in get device registration by device id", zap.Error(err))
		return authPb.DeviceRegistrationStatus_DEVICE_STATUS_UNSPECIFIED, err
	}
	if record.GetActorId() == actorId {
		return authPb.DeviceRegistrationStatus_REGISTERED, nil
	}
	return authPb.DeviceRegistrationStatus_UNREGISTERED, nil
}

// deviceRegStatusesToExpire returns eligible device registration
// statuses for expiry for the given actor. We want to expire access
// tokens by DeviceRegistrationStatus levels. If we're creating an access
// token with a REGISTERED device, we'll expire all old access tokens for
// the actor. But, when we create an access token with device status UNREGISTERED,
// we don't want to expire token with device status REGISTERED. It's because
// we don't want to log out the users yet until they verify the device as well.
func (s *Service) deviceRegStatusesToExpire(status authPb.DeviceRegistrationStatus) []authPb.DeviceRegistrationStatus {
	toExpire := []authPb.DeviceRegistrationStatus{authPb.DeviceRegistrationStatus_DEVICE_STATUS_UNSPECIFIED}
	switch status {
	case authPb.DeviceRegistrationStatus_UNREGISTERED:
		return append(toExpire, authPb.DeviceRegistrationStatus_UNREGISTERED)
	case authPb.DeviceRegistrationStatus_REGISTERED:
		return append(toExpire, authPb.DeviceRegistrationStatus_UNREGISTERED, authPb.DeviceRegistrationStatus_REGISTERED)
	}
	return toExpire
}

func (s *Service) SignOut(ctx context.Context, req *authPb.SignOutRequest) (*authPb.SignOutResponse, error) {
	if req.GetActor().GetType() == types.Actor_KYC_AGENT {
		return s.signOutKycAgent(ctx, req)
	}

	response := &authPb.SignOutResponse{Status: rpc.StatusOk()}

	if _, err := s.tokenStoresDao.ExpireTokenByActorId(ctx, req.GetActor().GetId(), "", authPb.TokenType_ACCESS_TOKEN, authPb.TokenDeletionReason_TOKEN_DELETION_REASON_SIGN_OUT); err != nil {
		logger.Error(ctx, "failed to expire old access token of user", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActor().GetId()))
		if storagev2.IsRecordNotFoundError(err) {
			response.Status = rpc.StatusRecordNotFound()
		} else {
			response.Status = rpc.StatusInternal()
		}
	}
	// Get phone number
	user, err := s.UserClient.GetUser(ctx,
		&userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_Id{
				Id: req.GetActor().GetEntityId()}})
	if err != nil {
		logger.Error(ctx, "failed to get phone number of user",
			zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActor().GetId()))
		// Return in case of error as we need this information to Delete Refresh Token, so no point to proceed.
		if storagev2.IsRecordNotFoundError(err) {
			return &authPb.SignOutResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		} else {
			return &authPb.SignOutResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}
	}

	if _, err := s.tokenStoresDao.ExpireTokenByPhoneNumber(ctx, authPb.TokenType_REFRESH_TOKEN,
		model.TokenExpired, user.GetUser().GetProfile().GetPhoneNumber(), authPb.TokenDeletionReason_TOKEN_DELETION_REASON_SIGN_OUT); err != nil {
		logger.Error(ctx, "failed to expire old refresh token of user", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActor().GetId()))
		if storagev2.IsRecordNotFoundError(err) {
			response.Status = rpc.StatusRecordNotFound()
		} else {
			response.Status = rpc.StatusInternal()
		}
	}
	return response, nil
}

func (s *Service) UpdateToken(ctx context.Context, req *authPb.UpdateTokenRequest) (*authPb.UpdateTokenResponse, error) {
	if s.shouldEnableTokenManager(ctx, req.GetActorId(), obfuscator.HashedPhoneNum(req.GetPhoneNumber())) {
		return s.tokenManager.UpdateToken(ctx, req)
	}
	var err error

	if len(req.TokenTypes) == 0 {
		return &authPb.UpdateTokenResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("token type required"),
		}, nil
	}

	if req.GetTokenUpdationReason() == authPb.TokenDeletionReason_TOKEN_DELETION_REASON_UNSPECIFIED {
		return &authPb.UpdateTokenResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("token updation reason required"),
		}, nil
	}

	switch req.Identifier.(type) {
	case *authPb.UpdateTokenRequest_PhoneNumber:
		for _, tokenType := range req.TokenTypes {
			tokenStatus := updateTokenReqStatusMap[req.Status]
			_, err = s.tokenStoresDao.ExpireTokenByPhoneNumber(ctx, tokenType, tokenStatus, req.GetPhoneNumber(), req.GetTokenUpdationReason())
			if err != nil {
				break
			}
		}
	case *authPb.UpdateTokenRequest_TokenId:
		logger.Info(ctx, "deleting token by id")
		token, gErr := s.tokenStoresDao.GetTokenById(ctx, req.GetTokenId())
		if gErr != nil {
			if storagev2.IsRecordNotFoundError(gErr) {
				return &authPb.UpdateTokenResponse{
					Status: rpc.StatusOk(),
				}, nil
			}
			logger.Error(ctx, "error in fetching tokens by id", zap.Error(gErr))
		}
		_, err = s.tokenStoresDao.ExpireToken(ctx, token.ID, req.GetTokenUpdationReason())
	case *authPb.UpdateTokenRequest_ActorId:
		if _, err = s.tokenStoresDao.ExpireTokenByActorId(ctx, req.GetActorId(), "", authPb.TokenType_ACCESS_TOKEN, req.GetTokenUpdationReason()); err != nil {
			break
		}
	default:
		err = errInvalidIdentifier
	}

	if err != nil {
		logger.Error(ctx, "error in update token", zap.Error(err))
		return &authPb.UpdateTokenResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &authPb.UpdateTokenResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) signOutKycAgent(ctx context.Context, req *authPb.SignOutRequest) (*authPb.SignOutResponse, error) {
	respStatus := rpc.StatusOk()
	if err := s.expireTokenByActorId(ctx, req.GetActor().GetId(), authPb.TokenType_GENIE_ACCESS_TOKEN); err != nil {
		respStatus = rpc.StatusInternalWithDebugMsg(err.Error())
	}
	if !respStatus.IsSuccess() {
		return &authPb.SignOutResponse{
			Status: respStatus,
		}, nil
	}

	if err := s.expireTokenByActorId(ctx, req.GetActor().GetId(), authPb.TokenType_GENIE_REFRESH_TOKEN); err != nil {
		respStatus = rpc.StatusInternalWithDebugMsg(err.Error())
	}

	return &authPb.SignOutResponse{
		Status: respStatus,
	}, nil
}

func (s *Service) expireTokenByActorId(ctx context.Context, actorId string, tokenType authPb.TokenType) error {
	if _, err := s.tokenStoresDao.ExpireTokenByActorId(ctx, actorId, "", tokenType, authPb.TokenDeletionReason_TOKEN_DELETION_REASON_SIGN_OUT); err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			return nil
		}
		logger.Error(ctx, fmt.Sprintf("failed to expire old tokens of user token type %v", tokenType.String()), zap.Error(err))
		return err
	}
	return nil
}

func getStatusFromJwtError(ctx context.Context, signedToken string, tokenType authPb.TokenType, deviceId string, err error) *rpc.Status {
	switch {
	case errors.Is(err, jwt.ErrTokenExpired):
		return statusTokenExpiry()
	default:
		logger.Error(ctx, fmt.Sprintf("error in verify token type: %v", tokenType.String()),
			zap.Error(err),
			zap.Int(logger.LENGTH, len(signedToken)),
			zap.Int(logger.COUNT, len(strings.Split(signedToken, "."))),
			zap.String(logger.DEVICE_ID, obfuscator.Hashed(deviceId)),
			zap.String(logger.TOKEN, obfuscator.Hashed(signedToken)))
		return statusTokenInvalid()
	}
}

func (s *Service) shouldEnableTokenManager(ctx context.Context, actorId string, hashedPhNumber string) bool {
	switch s.Conf.TokenManagerConfig().Enable() {
	case commontypes.BooleanEnum_TRUE.String():
		return true
	case "INTERNAL":
		if lo.Contains(s.Conf.TokenManagerConfig().WhitelistedActorIds(), actorId) || lo.Contains(s.Conf.TokenManagerConfig().WhitelistedPhoneNumberHashes(), hashedPhNumber) {
			logger.Info(ctx, "user whitelisted for token manager")
			return true
		}
		return false
	default:
		return false
	}
}
