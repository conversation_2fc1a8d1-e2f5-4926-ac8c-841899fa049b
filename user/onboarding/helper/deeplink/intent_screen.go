// nolint:dupl
package deeplink

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strings"
	"sync"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/data_structs/roarray"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"

	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/product"
	types "github.com/epifi/gamma/api/typesv2"
	feOnbPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
	"github.com/epifi/gamma/user/config/genconf"
)

const CreditCardCampaignKey = "campaign"

type Proc struct {
	userClient      user.UsersClient
	userGroupClient userGroupPb.GroupClient
	onbConf         *genconf.OnboardingConfig
	productClient   product.ProductClient
}

func NewProc(userClient user.UsersClient, userGroupClient userGroupPb.GroupClient, onbConf *genconf.OnboardingConfig,
	productClient product.ProductClient) *Proc {
	return &Proc{
		userClient:      userClient,
		userGroupClient: userGroupClient,
		onbConf:         onbConf,
		productClient:   productClient,
	}
}

var (
	intentSelectionScreenOptions = &feOnbPb.OnboardingIntentSelectionScreenOptions{
		Title:          commontypes.GetTextFromStringFontColourFontStyle("What do you want to start with?", "#313234", commontypes.FontStyle_DISPLAY_XL),
		Subtitle:       commontypes.GetTextFromStringFontColourFontStyle("You can always try more features later", "#929599", commontypes.FontStyle_BODY_S),
		BgColor:        widget.GetBlockBackgroundColour("#FFFFFF"),
		AdditionalInfo: commontypes.GetTextFromStringFontColourFontStyle("*Available on select Fi Plans.\nWe partner with regulated entities to provide above products", "#B2B5B9", commontypes.FontStyle_SUBTITLE_2XS),
		Ctas: []*dlPb.Cta{
			{
				Type:         dlPb.Cta_CONTINUE,
				Text:         "Next",
				DisplayTheme: dlPb.Cta_PRIMARY,
			},
		},
	}

	intentToProductMap = map[onbPb.OnboardingIntent]product.ProductType{
		onbPb.OnboardingIntent_ONBOARDING_INTENT_PERSONAL_LOANS:          product.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
		onbPb.OnboardingIntent_ONBOARDING_INTENT_CREDIT_CARD:             product.ProductType_PRODUCT_TYPE_CREDIT_CARD,
		onbPb.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT: product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
		onbPb.OnboardingIntent_ONBOARDING_INTENT_DEBIT_CARD:              product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
		onbPb.OnboardingIntent_ONBOARDING_INTENT_WEALTH_ANALYSER:         product.ProductType_PRODUCT_TYPE_WEALTH_ANALYSER,
	}

	intentOrder = []onbPb.OnboardingIntent{
		onbPb.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT,
		onbPb.OnboardingIntent_ONBOARDING_INTENT_DEBIT_CARD,
		onbPb.OnboardingIntent_ONBOARDING_INTENT_PERSONAL_LOANS,
		onbPb.OnboardingIntent_ONBOARDING_INTENT_CREDIT_CARD,
		onbPb.OnboardingIntent_ONBOARDING_INTENT_NET_WORTH,
		onbPb.OnboardingIntent_ONBOARDING_INTENT_WEALTH_ANALYSER,
		onbPb.OnboardingIntent_ONBOARDING_INTENT_FI_LITE,
	}
)

func (s *Proc) GetIntentSelectionDL(ctx context.Context, actorId string, entryPoint string) (*dlPb.Deeplink, error) {
	if actorId == "" {
		return nil, epifierrors.ErrInvalidArgument
	}
	getUser, getErr := s.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err := epifigrpc.RPCError(getUser, getErr); err != nil {
		logger.Error(ctx, "error while getting user by actor id", zap.Error(err))
		return nil, err
	}

	screenOptions := intentSelectionScreenOptions
	intentDetails, additionalIntentDetails, errIntent := s.getIntentDetailsListFromConfig(ctx, actorId, s.onbConf.IntentSelectionConfigV2(), getUser.GetUser().GetProfile())
	if errIntent != nil {
		logger.Error(ctx, "failure while getting intent details", zap.Error(errIntent))
		return nil, errIntent
	}

	screenOptions.Intents = intentDetails
	screenOptions.AdditionalIntentDetails = additionalIntentDetails
	screenOptions.EntryPoint = entryPoint

	return &dlPb.Deeplink{
		Screen:          dlPb.Screen_ONBOARDING_INTENT_SELECTION,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(screenOptions),
	}, nil
}

// nolint:funlen
func (s *Proc) getIntentDetailsListFromConfig(ctx context.Context, actorId string, intentConf *genconf.IntentSelectionConfigV2, userProfile *user.Profile) ([]*feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails, *feOnbPb.AdditionalIntentDetails, error) {
	var (
		intentDetails           []*feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails
		additionalIntentDetails *feOnbPb.AdditionalIntentDetails
		configMap               = intentConf.IntentConfigMap()
	)

	for _, currentIntent := range intentOrder {
		currentIntentConfig, ok := configMap.Load(currentIntent.String())
		if !ok {
			logger.Info(ctx, fmt.Sprintf("invalid OnboardingIntent enum string : %v", currentIntent))
			continue
		}

		intentRolloutPercentage := currentIntentConfig.RolloutPercentage()
		intentFeatureConfig := currentIntentConfig.FeatureConfig()
		intentUserGroups := currentIntentConfig.AllowedUserGroups()

		if !apputils.IsFeatureEnabledFromCtxDynamic(ctx, intentFeatureConfig) {
			_, version := epificontext.AppPlatformAndVersion(ctx)
			logger.Info(ctx, "app version not satisfied for user", zap.String(logger.FEATURE, currentIntent.String()), zap.Int("USER_APP_VERSION", version), zap.String("CONFIG_APP_VERSION", fmt.Sprintf("%+v", intentFeatureConfig)))
			continue
		}

		featureName := "feature_intent_" + strings.ToLower(currentIntent.String())

		if !CheckReleaseStickinessConstraint(ctx, actorId, featureName, intentRolloutPercentage) {
			logger.Info(ctx, "rollout percentage not satisfied for user", zap.String(logger.FEATURE, currentIntent.String()))
			continue
		}

		if !s.checkUserGroupMapping(ctx, intentUserGroups, userProfile) {
			logger.Info(ctx, "user group not satisfied for user", zap.String(logger.FEATURE, currentIntent.String()))
			continue
		}

		intentScreenOptions, getIntentDetailsErr := s.getIntentDetailsFromOnbIntent(ctx, actorId, currentIntent)
		if getIntentDetailsErr != nil {
			return nil, nil, getIntentDetailsErr
		}

		if currentIntent == onbPb.OnboardingIntent_ONBOARDING_INTENT_FI_LITE {
			intentDetails = append(intentDetails, intentScreenOptions)
		} else {
			if productType, found := intentToProductMap[currentIntent]; found {
				getStatusResp, errGetStatus := s.productClient.GetProductsStatus(ctx, &product.GetProductsStatusRequest{
					ActorId:      actorId,
					ProductTypes: []product.ProductType{productType},
				})
				rpcErr := epifigrpc.RPCError(getStatusResp, errGetStatus)
				if rpcErr != nil {
					// Keeping the error non-blocking
					logger.Error(ctx, "failed while getting product status", zap.String(logger.FEATURE, productType.String()), zap.Error(rpcErr))
				}

				if getStatusResp.GetProductInfoMap()[productType.String()].GetProductStatus() == product.ProductStatus_PRODUCT_STATUS_ONBOARDING_FAILURE {
					logger.Info(ctx, "Intent is disabled for the user", zap.String(logger.FEATURE, productType.String()), zap.String("ProductStatus", getStatusResp.GetProductInfoMap()[productType.String()].GetProductStatus().String()))
					intentScreenOptions.IsIntentDisabled = true
					intentScreenOptions.FailureMessage = &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("Currently Unavailable", "#929599", commontypes.FontStyle_BODY_S),
						},
						RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/onboarding/info_icon.png", 12, 12),
						RightImgTxtPadding: 3,
					}
				}

				intentDetails = append(intentDetails, intentScreenOptions)
			}
		}
	}

	if len(intentDetails) == 0 {
		logger.Info(ctx, "no intents were passed in the configs")
		return nil, nil, epifierrors.ErrFailedPrecondition
	}

	return intentDetails, additionalIntentDetails, nil
}

// nolint:funlen
func (s *Proc) checkUserGroupMapping(ctx context.Context, allowedGroups roarray.ROArray[string], userProfile *user.Profile) bool {
	// if the allowed user groups list is empty, allow all user groups
	if allowedGroups.Len() == 0 {
		logger.Info(ctx, "intent selection rollout: true")
		return true
	}

	var (
		emailIdentifier = &userGroupPb.IdentifierValue{
			Identifier: &userGroupPb.IdentifierValue_Email{
				Email: userProfile.GetEmail(),
			},
		}
		phoneIdentifier = &userGroupPb.IdentifierValue{
			Identifier: &userGroupPb.IdentifierValue_PhoneNumber{
				PhoneNumber: userProfile.GetPhoneNumber(),
			},
		}
	)

	for idx := 0; idx < allowedGroups.Len(); idx++ {
		var (
			grp                 = allowedGroups.At(idx)
			ug                  = commontypes.UserGroup(commontypes.UserGroup_value[grp])
			wg                  = sync.WaitGroup{}
			mappingFoundByEmail = false
			mappingFoundByPhone = false
		)
		wg.Add(2)

		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			groupRes, errResp := s.userGroupClient.CheckMapping(ctx, &userGroupPb.CheckMappingRequest{
				UserGroup:       ug,
				IdentifierValue: emailIdentifier,
			})
			if err := epifigrpc.RPCError(groupRes, errResp); err != nil {
				if groupRes.GetStatus().IsRecordNotFound() {
					return
				}
				logger.Error(ctx, "error in fetching user group mapping", zap.Error(err))
				return
			}
			mappingFoundByEmail = true
		})

		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			defer wg.Done()
			groupRes, errResp := s.userGroupClient.CheckMapping(ctx, &userGroupPb.CheckMappingRequest{
				UserGroup:       ug,
				IdentifierValue: phoneIdentifier,
			})
			if err := epifigrpc.RPCError(groupRes, errResp); err != nil {
				if groupRes.GetStatus().IsRecordNotFound() {
					return
				}
				logger.Error(ctx, "error in fetching user group mapping", zap.Error(err))
				return
			}
			mappingFoundByPhone = true
		})

		wg.Wait()
		if mappingFoundByEmail || mappingFoundByPhone {
			logger.Info(ctx, "intent selection rollout: true")
			return true
		}
	}
	logger.Info(ctx, "intent selection rollout: not in user groups")
	return false
}

func CheckReleaseStickinessConstraint(ctx context.Context, actorId, feature string, rolloutPercentage int) bool {
	featureAndActor := fmt.Sprintf("%v%v", feature, actorId)
	num, err := release.GetHashNum(featureAndActor)
	if err != nil {
		logger.Error(ctx, "error while generating hash for actor", zap.Error(err))
		return false
	}
	logger.Info(ctx, fmt.Sprintf("check %v release stickiness: %v %v, %v, %v", feature, actorId, num, num%100,
		rolloutPercentage))
	if num%100 < uint64(rolloutPercentage) {
		logger.Info(ctx, fmt.Sprintf("%v ReleaseStickinessConstraint evaluation returned true", feature))
		return true
	}
	return false
}

func (s *Proc) getIntentDetailsFromOnbIntent(ctx context.Context, actorId string, onbIntent onbPb.OnboardingIntent) (*feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails, error) {
	switch onbIntent {
	case onbPb.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT:
		return s.getSAIntentDetails(), nil
	case onbPb.OnboardingIntent_ONBOARDING_INTENT_CREDIT_CARD:
		return s.getCCIntentDetails(ctx, actorId), nil
	case onbPb.OnboardingIntent_ONBOARDING_INTENT_PERSONAL_LOANS:
		return s.getPLIntentDetails(), nil
	case onbPb.OnboardingIntent_ONBOARDING_INTENT_NET_WORTH:
		return s.getNetWorthIntentDetails(), nil
	case onbPb.OnboardingIntent_ONBOARDING_INTENT_WEALTH_ANALYSER:
		return s.getWealthAnalyserIntentDetails(), nil
	case onbPb.OnboardingIntent_ONBOARDING_INTENT_DEBIT_CARD:
		return s.getDCIntentDetails(), nil
	case onbPb.OnboardingIntent_ONBOARDING_INTENT_FI_LITE:
		return s.getUpiPaymentsIntentDetails(), nil
	}

	return nil, fmt.Errorf("intent details not found for : %s", onbIntent.String())
}

func (s *Proc) getSAIntentDetails() *feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails {
	return &feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails{
		IntentIdentifier: onbPb.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT.String(),
		Title:            commontypes.GetTextFromStringFontColourFontStyle("Federal Bank Savings a/c", "#313234", commontypes.FontStyle_HEADLINE_M),
		Icon:             commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/blue_bank.png", 80, 80).WithImageType(commontypes.ImageType_PNG),
		Tags: []*feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails_Tag{
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("3% back on spends", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("International Debit Card", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("Easy FDs & Auto-Invest rules", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
		},
	}
}

func (s *Proc) getDCIntentDetails() *feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails {
	return &feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails{
		IntentIdentifier: onbPb.OnboardingIntent_ONBOARDING_INTENT_DEBIT_CARD.String(),
		Title:            commontypes.GetTextFromStringFontColourFontStyle("Zero Forex Debit Card", "#313234", commontypes.FontStyle_HEADLINE_M),
		Icon:             commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/card-images/0forex-green-card-icon.png", 80, 80).WithImageType(commontypes.ImageType_PNG),
		Tags: []*feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails_Tag{
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("Save 4% on global spends", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("Works in 180+ countries", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("Save ₹10,000+ with travel offers", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
		},
	}
}

func (s *Proc) getCCIntentDetails(ctx context.Context, actorId string) *feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails {
	cardProgram, err := s.getCardProgramForActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error in get card program for actor", zap.Error(err))
	}

	switch cardProgram.GetCardProgramType() {
	case types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED:
		return &feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails{
			IntentIdentifier: onbPb.OnboardingIntent_ONBOARDING_INTENT_CREDIT_CARD.String(),
			Title:            commontypes.GetTextFromStringFontColourFontStyle("Credit Card", "#313234", commontypes.FontStyle_HEADLINE_M),
			Icon:             commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/credit-card/magnifi-card.png", 80, 80).WithImageType(commontypes.ImageType_PNG),
			Tags: []*feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails_Tag{
				{
					Text:    commontypes.GetTextFromStringFontColourFontStyle("Lifetime free", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
					BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
				},
				{
					Text:    commontypes.GetTextFromStringFontColourFontStyle("20% Cashback every weekend", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
					BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
				},
				{
					Text:    commontypes.GetTextFromStringFontColourFontStyle("5% back on weekend spends**", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
					BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
				},
			},
		}
	default:
		return &feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails{
			IntentIdentifier: onbPb.OnboardingIntent_ONBOARDING_INTENT_CREDIT_CARD.String(),
			Title:            commontypes.GetTextFromStringFontColourFontStyle("Credit Card", "#313234", commontypes.FontStyle_HEADLINE_M),
			Icon:             commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/credit_card_black.png", 80, 80).WithImageType(commontypes.ImageType_PNG),
			Tags: []*feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails_Tag{
				{
					Text:    commontypes.GetTextFromStringFontColourFontStyle("Unlimited 3% back*", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
					BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
				},
				{
					Text:    commontypes.GetTextFromStringFontColourFontStyle("Vouchers worth ₹4,250*", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
					BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
				},
				{
					Text:    commontypes.GetTextFromStringFontColourFontStyle("0% forex markup", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
					BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
				},
			},
		}
	}
}

func (s *Proc) getUpiPaymentsIntentDetails() *feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails {
	return &feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails{
		IntentIdentifier: onbPb.OnboardingIntent_ONBOARDING_INTENT_FI_LITE.String(),
		Title:            commontypes.GetTextFromStringFontColourFontStyle("UPI Payments", "#313234", commontypes.FontStyle_HEADLINE_M),
		Icon:             commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/pay/upi_pay_intent_option.png", 80, 80).WithImageType(commontypes.ImageType_PNG),
		Tags: []*feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails_Tag{
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("Fast & secure UPI Payments", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("Spends Analyser", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
		},
	}
}

func (s *Proc) getPLIntentDetails() *feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails {
	return &feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails{
		IntentIdentifier: onbPb.OnboardingIntent_ONBOARDING_INTENT_PERSONAL_LOANS.String(),
		Title:            commontypes.GetTextFromStringFontColourFontStyle("Instant Loans", "#313234", commontypes.FontStyle_HEADLINE_M),
		Icon:             commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/moneybag.png", 80, 80).WithImageType(commontypes.ImageType_PNG),
		Tags: []*feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails_Tag{
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("Interest rate starts @ 11% p.a.", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("100% paperless", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("No pre-closure fees*", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
		},
	}
}

func (s *Proc) getNetWorthIntentDetails() *feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails {
	return &feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails{
		IntentIdentifier: onbPb.OnboardingIntent_ONBOARDING_INTENT_NET_WORTH.String(),
		Title:            commontypes.GetTextFromStringFontColourFontStyle("Track and Analyse Investments", "#313234", commontypes.FontStyle_HEADLINE_M),
		Icon:             commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/net_worth_island.png", 80, 80).WithImageType(commontypes.ImageType_PNG),
		Tags: []*feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails_Tag{
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("Net Worth Analyser", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("Mutual Fund Analyser", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("View your loans", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
		},
	}

}

// TODO(Ayush): Finalise UI with design team and remove todo
func (s *Proc) getWealthAnalyserIntentDetails() *feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails {
	return &feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails{
		IntentIdentifier: onbPb.OnboardingIntent_ONBOARDING_INTENT_WEALTH_ANALYSER.String(),
		Title:            commontypes.GetTextFromStringFontColourFontStyle("Analyse your money", "#313234", commontypes.FontStyle_HEADLINE_M),
		Icon:             commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/wealth_analyser_intent.png", 80, 80).WithImageType(commontypes.ImageType_PNG),
		Tags: []*feOnbPb.OnboardingIntentSelectionScreenOptions_IntentDetails_Tag{
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("Free Investment Reports", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("Daily Portfolio Tracker", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
			{
				Text:    commontypes.GetTextFromStringFontColourFontStyle("Free Credit Score", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
				BgColor: widget.GetBlockBackgroundColour("#E4F1F5"),
			},
		},
	}
}

func (s *Proc) getCardProgramForActor(ctx context.Context, actorId string) (*types.CardProgram, error) {
	var (
		fiLiteCardNames = []string{
			ffPkg.UnsecuredCardName,
			ffPkg.MassCardName,
		}
		unsecuredCardProgram = &types.CardProgram{
			CardProgramVendor: types.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL,
			CardProgramSource: types.CardProgramSource_CARD_PROGRAM_SOURCE_REALTIME_BRE,
			CardProgramType:   types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED,
			CardProgramOrigin: types.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE,
		}
		massUnsecuredCardProgram = &types.CardProgram{
			CardProgramVendor: types.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL,
			CardProgramSource: types.CardProgramSource_CARD_PROGRAM_SOURCE_REALTIME_BRE,
			CardProgramType:   types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED,
			CardProgramOrigin: types.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE,
		}
		cardNameToProgramMap = map[string]*types.CardProgram{
			ffPkg.UnsecuredCardName: unsecuredCardProgram,
			ffPkg.MassCardName:      massUnsecuredCardProgram,
		}
	)

	userRes, rpcErr := s.userClient.GetUser(ctx, &user.GetUserRequest{Identifier: &user.GetUserRequest_ActorId{ActorId: actorId}})
	if err := epifigrpc.RPCError(userRes, rpcErr); err != nil {
		return nil, rpcErr
	}

	campaign, ok := userRes.GetUser().GetAcquisitionInfo().GetAttributionDetails().GetAppsflyerAttributionData().AsMap()[CreditCardCampaignKey].(string)
	if !ok {
		logger.Info(ctx, "campaign field not found in appsflyer payload")
		return unsecuredCardProgram, nil
	}

	for _, cardName := range fiLiteCardNames {
		if strings.Contains(strings.ToLower(campaign), strings.ToLower(cardName)) {
			return cardNameToProgramMap[cardName], nil
		}
	}
	logger.Info(ctx, "no fi lite card names found in campaign")
	return unsecuredCardProgram, nil
}
