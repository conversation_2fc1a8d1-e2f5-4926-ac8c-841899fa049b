// nolint
package stageproc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/waitgroup"

	"github.com/epifi/gamma/user/onboarding/helper"

	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/bankcust"
	bcPb "github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/employment"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	inAppReferralPb "github.com/epifi/gamma/api/inappreferral"
	inAppReferralEnumPb "github.com/epifi/gamma/api/inappreferral/enums"
	"github.com/epifi/gamma/api/kyc"
	scrnrPb "github.com/epifi/gamma/api/screener"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	usersPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	userLocationPb "github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/obfuscator"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	vgCustomerPb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	"github.com/epifi/gamma/pkg/feature/release"
	releaseGenConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	obfPkg "github.com/epifi/gamma/pkg/obfuscator"
	"github.com/epifi/gamma/pkg/vkyc"
	"github.com/epifi/gamma/user/config/genconf"
	userEvents "github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding/dao"
)

type ctxConsumerKey string

const (
	CtxConsumerKey ctxConsumerKey = "SyncOnboardingConsumer"
)

var (
	/*
		Orchestrator action errors
	*/
	// NoActionError: stage is marked SUCCESS by orchestrator if stage processor
	// returns this and the stage processor is never called again
	NoActionError = errors.New("stage success, no more actions required")

	// SkipStageError is similar to NoActionError, stage is marked SKIPPED by
	// orchestrator if stage processor returns this and processor is never called again
	SkipStageError = errors.New("skip stage permanently")

	// NoActionSkipStatusUpdateError stage processor returns this when it has no further action, yet.
	// further stages can be evaluated. However, the orchestrator will call the processor again in next call.
	NoActionSkipStatusUpdateError = errors.New("no action, skip update status update in db")

	// ErrStageSuccessWithAction : stage is marked SUCCESS by orchestrator if stage processor
	// returns this and expects a transition deeplink which needs to be shown before moving to the next stage
	ErrStageSuccessWithAction = errors.New("stage success, showing transition screen without any actions")

	/*
		Common stage errors
	*/
	ErrDOBPANNotFound     = errors.New("dob or pan not found in user profile")
	ErrKYCRecordNotFound  = errors.New("kyc record not found")
	ErrBalanceCheckFailed = errors.New("balance check failed")

	// common function errors
	stageAfterAppScreeningErr = errors.New("current stage is/after app screening")
	notSaOnbStageErr          = errors.New("current stage not in onboarding stages")

	FeatureToStagesOrderMap = map[onbPb.Feature][]onbPb.OnboardingStage{
		onbPb.Feature_FEATURE_SA:                    SAOnboardingStagesOrder,
		onbPb.Feature_FEATURE_FI_LITE:               SAOnboardingStagesOrder,
		onbPb.Feature_FEATURE_CC:                    CCOnboardingStagesOrder,
		onbPb.Feature_FEATURE_PL:                    PLOnboardingStagesOrder,
		onbPb.Feature_FEATURE_UPI_TPAP:              UpiTpapOnboardingStagesOrder,
		onbPb.Feature_FEATURE_NON_RESIDENT_SA:       NonResidentSavingsAccountOnboardingStagesOrder,
		onbPb.Feature_FEATURE_NON_RESIDENT_SA_QATAR: QatarNonResidentSavingsAccountOnboardingStagesOrder,
		onbPb.Feature_FEATURE_WEALTH_ANALYSER:       WealthAnalyserOnboardingStagesOrder,
	}

	CCOnboardingStagesOrder = []onbPb.OnboardingStage{
		onbPb.OnboardingStage_INTENT_SELECTION,
		onbPb.OnboardingStage_REFERRAL_FINITE_CODE,
		onbPb.OnboardingStage_TNC_CONSENT,
		onbPb.OnboardingStage_PERMISSION,
		onbPb.OnboardingStage_LOCATION_CHECK,
		onbPb.OnboardingStage_FI_LITE_RISK_SCREENING,
		onbPb.OnboardingStage_CREDIT_CARD_ONBOARDING_STATUS_CHECK,
	}

	PLOnboardingStagesOrder = []onbPb.OnboardingStage{
		onbPb.OnboardingStage_INTENT_SELECTION,
		onbPb.OnboardingStage_TNC_CONSENT,
		onbPb.OnboardingStage_PERMISSION,
		onbPb.OnboardingStage_LOCATION_CHECK,
		onbPb.OnboardingStage_FI_LITE_RISK_SCREENING,
		onbPb.OnboardingStage_PL_ONBOARDING_STATUS_CHECK,
	}

	UpiTpapOnboardingStagesOrder = []onbPb.OnboardingStage{
		onbPb.OnboardingStage_INTENT_SELECTION,
		onbPb.OnboardingStage_REFERRAL_FINITE_CODE,
		onbPb.OnboardingStage_TNC_CONSENT,
		onbPb.OnboardingStage_PERMISSION,
		onbPb.OnboardingStage_LOCATION_CHECK,
		onbPb.OnboardingStage_DEVICE_REGISTRATION,
	}

	QatarNonResidentSavingsAccountOnboardingStagesOrder = []onbPb.OnboardingStage{
		onbPb.OnboardingStage_TNC_CONSENT,
		onbPb.OnboardingStage_PERMISSION,
		onbPb.OnboardingStage_LOCATION_CHECK,
		onbPb.OnboardingStage_SAVINGS_INTRO_CONSENT,
		onbPb.OnboardingStage_DOB_AND_PAN,
		onbPb.OnboardingStage_EMPLOYMENT_VERIFICATION,
		onbPb.OnboardingStage_PASSPORT_VERIFICATION,
		onbPb.OnboardingStage_ENSURE_KYC_AVAILABILITY,
		onbPb.OnboardingStage_MOTHER_FATHER_NAME,
		onbPb.OnboardingStage_KYC_DEDUPE_CHECK,
		onbPb.OnboardingStage_COUNTRY_ID_VERIFICATION,
		onbPb.OnboardingStage_LIVENESS,
		onbPb.OnboardingStage_NON_RESIDENT_ONBOARDING_CROSS_DATA_VALIDATION,
		onbPb.OnboardingStage_COMMUNICATION_ADDRESS,
		onbPb.OnboardingStage_UPDATE_CUSTOMER_DETAILS,
		onbPb.OnboardingStage_UN_NAME_CHECK,
		onbPb.OnboardingStage_DEVICE_REGISTRATION,
		onbPb.OnboardingStage_CONFIRM_CARD_MAILING_ADDRESS,
		onbPb.OnboardingStage_VKYC,
		onbPb.OnboardingStage_RISK_SCREENING,
		onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK,
		onbPb.OnboardingStage_CUSTOMER_CREATION,
		onbPb.OnboardingStage_ACCOUNT_CREATION,
		onbPb.OnboardingStage_NRO_ACCOUNT_CREATION,
		onbPb.OnboardingStage_CARD_CREATION,
		onbPb.OnboardingStage_DEBIT_CARD_PIN_SETUP,
		onbPb.OnboardingStage_ONBOARDING_COMPLETE,
	}

	NonResidentSavingsAccountOnboardingStagesOrder = []onbPb.OnboardingStage{
		onbPb.OnboardingStage_TNC_CONSENT,
		onbPb.OnboardingStage_PERMISSION,
		onbPb.OnboardingStage_LOCATION_CHECK,
		onbPb.OnboardingStage_SAVINGS_INTRO_CONSENT,
		onbPb.OnboardingStage_DOB_AND_PAN,
		onbPb.OnboardingStage_EMPLOYMENT_VERIFICATION,
		onbPb.OnboardingStage_PASSPORT_VERIFICATION,
		onbPb.OnboardingStage_ENSURE_KYC_AVAILABILITY,
		onbPb.OnboardingStage_MOTHER_FATHER_NAME,
		onbPb.OnboardingStage_KYC_DEDUPE_CHECK,
		onbPb.OnboardingStage_COUNTRY_ID_VERIFICATION,
		onbPb.OnboardingStage_LIVENESS,
		onbPb.OnboardingStage_NON_RESIDENT_ONBOARDING_CROSS_DATA_VALIDATION,
		onbPb.OnboardingStage_COMMUNICATION_ADDRESS,
		onbPb.OnboardingStage_UPDATE_CUSTOMER_DETAILS,
		onbPb.OnboardingStage_UN_NAME_CHECK,
		onbPb.OnboardingStage_DEVICE_REGISTRATION,
		onbPb.OnboardingStage_CONFIRM_CARD_MAILING_ADDRESS,
		onbPb.OnboardingStage_VKYC,
		onbPb.OnboardingStage_RISK_SCREENING,
		onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK,
		onbPb.OnboardingStage_CUSTOMER_CREATION,
		onbPb.OnboardingStage_ACCOUNT_CREATION,
		onbPb.OnboardingStage_NRO_ACCOUNT_CREATION,
		onbPb.OnboardingStage_CARD_CREATION,
		onbPb.OnboardingStage_DEBIT_CARD_PIN_SETUP,
		onbPb.OnboardingStage_ONBOARDING_COMPLETE,
	}
	SAOnboardingStagesOrder = []onbPb.OnboardingStage{
		onbPb.OnboardingStage_INTENT_SELECTION,
		onbPb.OnboardingStage_REFERRAL_FINITE_CODE,
		onbPb.OnboardingStage_TNC_CONSENT,
		onbPb.OnboardingStage_PERMISSION,
		onbPb.OnboardingStage_LOCATION_CHECK,
		onbPb.OnboardingStage_FI_LITE_RISK_SCREENING,
		onbPb.OnboardingStage_INITIATE_CREDIT_REPORT_FETCH,
		onbPb.OnboardingStage_SAVINGS_INTRO_CONSENT,
		onbPb.OnboardingStage_DOB_AND_PAN,
		onbPb.OnboardingStage_INITIATE_CKYC,
		onbPb.OnboardingStage_CREDIT_REPORT_VERIFICATION,
		onbPb.OnboardingStage_EMPLOYMENT_VERIFICATION,
		onbPb.OnboardingStage_INCOME_ESTIMATE_CHECK,
		onbPb.OnboardingStage_LENDABILITY_CHECK,
		onbPb.OnboardingStage_INSTALLED_APPS_CHECK,
		onbPb.OnboardingStage_CREDIT_REPORT_CHECK,
		onbPb.OnboardingStage_CONNECTED_ACCOUNTS,
		onbPb.OnboardingStage_WORK_EMAIL_VERIFICATION,
		onbPb.OnboardingStage_UAN_PRESENCE_CHECK,
		onbPb.OnboardingStage_ITR_INTIMATION_VERIFICATION,
		onbPb.OnboardingStage_APP_SCREENING,
		onbPb.OnboardingStage_DEDUPE_CHECK,
		onbPb.OnboardingStage_MOTHER_FATHER_NAME,
		onbPb.OnboardingStage_BKYC,
		onbPb.OnboardingStage_CKYC,
		onbPb.OnboardingStage_EKYC,
		onbPb.OnboardingStage_ENSURE_KYC_AVAILABILITY,
		onbPb.OnboardingStage_LIVENESS,
		onbPb.OnboardingStage_UPDATE_CUSTOMER_DETAILS,
		onbPb.OnboardingStage_PAN_NAME_CHECK,
		onbPb.OnboardingStage_RISK_SCREENING,
		onbPb.OnboardingStage_KYC_NAME_DOB_VALIDATION,
		onbPb.OnboardingStage_AADHAR_MOBILE_VALIDATION,
		onbPb.OnboardingStage_KYC_DEDUPE_CHECK,
		onbPb.OnboardingStage_VKYC,
		onbPb.OnboardingStage_UN_NAME_CHECK,
		onbPb.OnboardingStage_CONFIRM_CARD_MAILING_ADDRESS,
		onbPb.OnboardingStage_OPEN_MIN_BALANCE_ACCOUNT,
		onbPb.OnboardingStage_DEVICE_REGISTRATION,
		onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK,
		onbPb.OnboardingStage_PRE_ACCOUNT_CREATION_ADD_MONEY,
		onbPb.OnboardingStage_UPDATE_PROFILE_DETAILS,
		onbPb.OnboardingStage_CUSTOMER_CREATION,
		onbPb.OnboardingStage_ACCOUNT_CREATION,
		onbPb.OnboardingStage_CARD_CREATION,
		onbPb.OnboardingStage_DEBIT_CARD_PIN_SETUP,
		onbPb.OnboardingStage_OPTIONAL_VKYC,
		onbPb.OnboardingStage_ADD_MONEY,
		onbPb.OnboardingStage_ORDER_PHYSICAL_CARD,
		onbPb.OnboardingStage_OPTIONAL_VKYC,
		onbPb.OnboardingStage_SOFT_INTENT_SELECTION,
		onbPb.OnboardingStage_ONBOARDING_COMPLETE,
	}

	WealthAnalyserOnboardingStagesOrder = []onbPb.OnboardingStage{
		onbPb.OnboardingStage_TNC_CONSENT,
		onbPb.OnboardingStage_INITIATE_CREDIT_REPORT_FETCH,
		onbPb.OnboardingStage_SMS_PARSER_CONSENT,
		onbPb.OnboardingStage_WAIT_FOR_AUTO_PAN,
		onbPb.OnboardingStage_WEALTH_ANALYSER_ONBOARDING_STATUS_CHECK, // handles MF import check as well
		onbPb.OnboardingStage_WEALTH_BUILDER_CONNECTED_ACCOUNTS_FLOW,
		onbPb.OnboardingStage_WEALTH_ANALYSER_ONBOARDING_COMPLETE,
	}

	screenerStages = []onbPb.OnboardingStage{
		onbPb.OnboardingStage_CREDIT_REPORT_VERIFICATION,
		onbPb.OnboardingStage_EMPLOYMENT_VERIFICATION,
		onbPb.OnboardingStage_INCOME_ESTIMATE_CHECK,
		onbPb.OnboardingStage_LENDABILITY_CHECK,
		onbPb.OnboardingStage_CREDIT_REPORT_CHECK,
		onbPb.OnboardingStage_WORK_EMAIL_VERIFICATION,
		onbPb.OnboardingStage_UAN_PRESENCE_CHECK,
		onbPb.OnboardingStage_ITR_INTIMATION_VERIFICATION,
		onbPb.OnboardingStage_CONNECTED_ACCOUNTS,
		onbPb.OnboardingStage_APP_SCREENING,
	}

	skippableScreenerStages = []onbPb.OnboardingStage{
		onbPb.OnboardingStage_WORK_EMAIL_VERIFICATION,
		onbPb.OnboardingStage_UAN_PRESENCE_CHECK,
		onbPb.OnboardingStage_ITR_INTIMATION_VERIFICATION,
		onbPb.OnboardingStage_CONNECTED_ACCOUNTS,
	}

	redirectionMap = map[onboarding.FeatureOnboardingEntryPoint]*deeplinkPb.Deeplink{
		onboarding.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_CC: {
			Screen: deeplinkPb.Screen_CREDIT_CARD_DASHBOARD_SCREEN,
		},
		onboarding.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_DC: {
			Screen: deeplinkPb.Screen_CARD_HOME_SCREEN,
		},
		onboarding.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_MF: {
			Screen: deeplinkPb.Screen_MUTUAL_FUND_COLLECTIONS_LANDING_SCREEN,
		},
		onboarding.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_JUMP: {
			Screen: deeplinkPb.Screen_P2P_INVESTMENT_DASHBOARD_SCREEN,
		},
		onboarding.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_FD: {
			Screen: deeplinkPb.Screen_DEPOSIT_LANDING_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_DepositAccountLandingScreenOption{
				DepositAccountLandingScreenOption: &deeplinkPb.DepositAccountLandingScreenOptions{
					DepositType: accounts.Type_FIXED_DEPOSIT,
				},
			},
		},
		onboarding.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_SD: {
			Screen: deeplinkPb.Screen_DEPOSIT_LANDING_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_DepositAccountLandingScreenOption{
				DepositAccountLandingScreenOption: &deeplinkPb.DepositAccountLandingScreenOptions{
					DepositType: accounts.Type_SMART_DEPOSIT,
				},
			},
		},
		onboarding.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_AUTO_PAY: {
			Screen: deeplinkPb.Screen_FIT_ALL_COLLECTIONS_PAGE,
			ScreenOptions: &deeplinkPb.Deeplink_FitAllCollectionsPageScreenOptions{
				FitAllCollectionsPageScreenOptions: &deeplinkPb.FitAllCollectionsPageScreenOptions{
					CollectionType: "COLLECTION_TYPE_AUTO_PAY",
				},
			},
		},
		onboarding.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_PL: {
			Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
		},
	}

	redirectionActionAfterOnbComplete = func(entryPoint onboarding.FeatureOnboardingEntryPoint) *deeplinkPb.Deeplink {
		/*
			dl, ok := redirectionMap[entryPoint]
			if !ok {
			}
			return dl

		*/
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_HOME,
		}
	}
)

// getStageStatus extracts OnboardingState of input OnboardingStage from OnboardingDetails
func getStageStatus(onb *onbPb.OnboardingDetails, stage onbPb.OnboardingStage) onbPb.OnboardingState {
	return onb.GetStageDetails().GetStageMapping()[stage.String()].GetState()
}

func getKYCLevel(ctx context.Context, bankCustClient bcPb.BankCustomerServiceClient, kycClient kyc.KycClient, actorId, userId string) (kyc.KYCLevel, error) {
	getBCRes, getBCErr := bankCustClient.GetBankCustomer(ctx, &bcPb.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if grpcErr := epifigrpc.RPCError(getBCRes, getBCErr); grpcErr != nil {
		if !getBCRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "failed to get bank customer", zap.Error(grpcErr))
		}
		return 0, grpcErr
	}

	kycLevel := getBCRes.GetBankCustomer().GetKycInfo().GetKycLevel()

	// found kyc level in User, returning.
	if kycLevel != 0 {
		return kycLevel, nil
	}

	// for screens after liveness, the kyc level is expected to be present in user.
	// so kyc client is optional parameter. If we don't find the kyc level, the
	// client will fall back to default behaviour.
	if kycClient == nil || actorId == "" {
		logger.Error(ctx, "kyc client or actor not available")
		return 0, epifierrors.ErrRecordNotFound
	}

	// kyc level not found in user, falling back to kyc
	kycRes, err := kycStatus(ctx, kycClient, actorId)
	if err != nil {
		return 0, err
	}
	kycLevel = kycRes.GetKycLevel()

	// return err if kyc level not found
	if kycLevel == 0 {
		logger.Error(ctx, "kyc level not found")
		return 0, epifierrors.ErrRecordNotFound
	}

	// found kyc level in kyc service
	return kycLevel, nil
}

func MarkAsConsumerFlow(ctx context.Context) context.Context {
	return context.WithValue(ctx, CtxConsumerKey, CtxConsumerKey)
}

func IsConsumerFlow(ctx context.Context) bool {
	if ctx.Value(CtxConsumerKey) != nil && ctx.Value(CtxConsumerKey) == CtxConsumerKey {
		logger.Debug(ctx, "consumer flow in sync onboarding")
		return true
	}
	logger.Debug(ctx, "user flow in onboarding")
	return false
}

// IsOrchestratorAction checks if the error is an action item for the onboarding orchestrator
func IsOrchestratorAction(err error) bool {
	return errors.Is(err, NoActionError) ||
		errors.Is(err, SkipStageError) ||
		errors.Is(err, NoActionSkipStatusUpdateError)
}

func stageInfo(onb *onbPb.OnboardingDetails, stage onbPb.OnboardingStage) *onbPb.StageInfo {
	return onb.GetStageDetails().GetStageMapping()[stage.String()]
}

func IsUserStuck(ctx context.Context, onb *onbPb.OnboardingDetails, stuckTime time.Duration) bool {
	stage := onb.GetCurrentOnboardingStage()
	if !isStageInAccountSetupStages(stage) {
		return false
	}

	stageDetails := stageInfo(onb, stage)
	if stageDetails == nil {
		logger.Error(ctx, "error in getting mapping for stage", zap.String(logger.ONBOARDING_STAGE, stage.String()))
		return false
	}

	if getStageStatus(onb, stage).IsSuccessOrSkipped() {
		return false
	}
	startedAt := stageDetails.GetStartedAt()
	if startedAt == nil {
		logger.Info(ctx, fmt.Sprintf("startedAt is nil for stage: %s", stage))
		return false
	}

	if time.Since(startedAt.AsTime()) >= stuckTime {
		return true
	}

	return false
}

// logOnb returns zap field for logging onboarding ID
func logOnb(onboardingID string) zap.Field {
	return zap.String(logger.ONBOARDING_ID, onboardingID)
}

func isStageInAccountSetupStages(currStage onbPb.OnboardingStage) bool {
	stages := []onbPb.OnboardingStage{
		onbPb.OnboardingStage_CUSTOMER_CREATION,
		onbPb.OnboardingStage_CARD_CREATION,
		onbPb.OnboardingStage_SHIPPING_ADDRESS_UPDATE,
		onbPb.OnboardingStage_ACCOUNT_CREATION,
		onbPb.OnboardingStage_UPDATE_PROFILE_DETAILS,
	}
	for _, stage := range stages {
		if stage == currStage {
			return true
		}
	}

	return false
}

func getUserAndKycRecordFromOnbDetails(ctx context.Context, onb *onbPb.OnboardingDetails, userClient usersPb.UsersClient, kycClient kyc.KycClient) (*usersPb.GetUserResponse, *kyc.GetKYCRecordResponse, error) {
	var (
		wg              sync.WaitGroup
		err, err1, err2 error
		kycResp         *kyc.GetKYCRecordResponse
		userResp        *usersPb.GetUserResponse
	)

	wg.Add(1)
	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
		defer wg.Done()
		userResp, err = userClient.GetUser(ctx, &usersPb.GetUserRequest{
			Identifier: &usersPb.GetUserRequest_Id{Id: onb.GetUserId()},
		})
		if err1 = epifigrpc.RPCError(userResp, err); err1 != nil {
			logger.Error(ctx, "failed to get user from user service", zap.Error(err1), zap.String(logger.ENTITY_ID, onb.GetUserId()))
		}
	})
	wg.Add(1)
	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
		defer wg.Done()
		// fetch kyc record
		kycResp, err = kycClient.GetKYCRecord(ctx, &kyc.GetKYCRecordRequest{
			ActorId: onb.GetActorId(),
		})
		if err2 = epifigrpc.RPCError(kycResp, err); err2 != nil {
			if kycResp.GetStatus().IsRecordNotFound() {
				logger.Info(ctx, "kyc record not found")
				err2 = ErrKYCRecordNotFound
			} else {
				logger.Error(ctx, "failed to get kyc record from kyc service", zap.Error(err2))
			}
		}
	})
	waitgroup.SafeWaitWithDefaultTimeout(&wg)

	if err1 != nil {
		return nil, nil, err1
	}
	if err2 != nil {
		return nil, nil, err2
	}
	return userResp, kycResp, nil
}

func isUserStudentType(ctx context.Context, empClient employment.EmploymentClient, actorId string) (bool, error) {
	resp, errResp := empClient.GetEmploymentDetailsForActor(ctx, &employment.GetEmploymentDetailsForActorRequest{
		ActorId: actorId,
	})
	if err := epifigrpc.RPCError(resp, errResp); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return false, nil
		}
		logger.Error(ctx, "could not fetch employment type for actor", zap.Error(err))
		return false, err
	}
	return resp.GetEmploymentType() == types.EmploymentType_EMPLOYMENT_TYPE_STUDENT, nil
}

func runScreenerV2Check(ctx context.Context, screenerClient scrnrPb.ScreenerClient, actorId string, currentCheck scrnrPb.CheckType) (*StageProcessorResponse, error) {
	runCheckResp, errRun := screenerClient.RunCheck(ctx, &scrnrPb.RunCheckRequest{
		ActorId:   actorId,
		CheckType: currentCheck,
	})
	if grpcErr := epifigrpc.RPCError(runCheckResp, errRun); grpcErr != nil {
		logger.Error(ctx, "failed to run check", zap.Error(grpcErr))
		return nil, grpcErr
	}

	if currentCheck == scrnrPb.CheckType_CHECK_TYPE_GMAIL_INSIGHTS {
		if runCheckResp.GetCheckDetails().GetCheckResult() != scrnrPb.CheckResult_CHECK_RESULT_IN_PROGRESS {
			logger.Info(ctx, "RunCheck response", zap.String("Resp", fmt.Sprintf("%+v", runCheckResp)),
				zap.String("status", runCheckResp.GetCheckDetails().GetCheckResult().String()),
				zap.String("stage", runCheckResp.GetCheckDetails().GetCheckType().String()),
			)
		}
	} else {
		logger.Info(ctx, "RunCheck response", zap.String("Resp", fmt.Sprintf("%+v", runCheckResp)),
			zap.String("status", runCheckResp.GetCheckDetails().GetCheckResult().String()),
			zap.String("stage", runCheckResp.GetCheckDetails().GetCheckType().String()),
		)
	}

	// handling terminal run states here rather than waiting for the next action
	// to get the run state again from screener
	switch runCheckResp.GetCheckDetails().GetCheckResult() {
	case scrnrPb.CheckResult_CHECK_RESULT_PASSED:
		return nil, NoActionError
	case scrnrPb.CheckResult_CHECK_RESULT_SKIPPED,
		scrnrPb.CheckResult_CHECK_RESULT_FAILED,
		scrnrPb.CheckResult_CHECK_RESULT_DISABLED:
		if runCheckResp.GetNextAction() != nil {
			return &StageProcessorResponse{
				NextAction: runCheckResp.GetNextAction(),
			}, nil
		}
		return nil, SkipStageError
	case scrnrPb.CheckResult_CHECK_RESULT_IN_PROGRESS, scrnrPb.CheckResult_CHECK_RESULT_INITIATED:
		if runCheckResp.GetNextAction() != nil {
			return &StageProcessorResponse{
				NextAction: runCheckResp.GetNextAction(),
			}, nil
		}
		return nil, NoActionSkipStatusUpdateError
	}
	if runCheckResp.GetNextAction() != nil {
		return &StageProcessorResponse{
			NextAction: runCheckResp.GetNextAction(),
		}, nil
	}
	return &StageProcessorResponse{
		NextAction: deeplink.NewActionToGetNextAction(),
	}, nil
}

func getKycLevelUpdateFlow(ctx context.Context, onb *onbPb.OnboardingDetails, kycRecord *kyc.GetKYCRecordResponse, reqParams *kyc.KYCAttemptRequestParams, empClient employment.EmploymentClient, userProc helper.UserProcessor, env string) (bankcust.KycLevelUpdateFlow, error) {
	g, _ := errgroup.WithContext(ctx)
	var (
		isStudent              bool
		isFiLiteUser           = onb.GetFiLiteDetails().GetIsEnabled() == commontypes.BooleanEnum_TRUE
		isUserLowQuality       commontypes.BooleanEnum
		isPanLinkedToClosedAcc = false
		isNrOnboardingUser     = false
		isFiLiteCCUser         = onb.GetFeature() == onbPb.Feature_FEATURE_CC
	)
	g.Go(func() error {
		var err error
		isStudent, err = isUserStudentType(ctx, empClient, onb.GetActorId())
		if err != nil {
			logger.Error(ctx, "error while fetching employment type for actor", zap.Error(err))
			return err
		}
		return nil
	})
	g.Go(func() error {
		var err error
		isUserLowQuality, err = userProc.IsLowQualityUser(ctx, onb.GetActorId())
		if err != nil {
			logger.Error(ctx, "error in fetching user quality", zap.Error(err))
			return err
		}
		return nil
	})
	g.Go(func() error {
		isNrOnboardingUserRes, err := userProc.IsNonResidentUser(ctx, onb.GetActorId())
		if err != nil {
			logger.Error(ctx, "error in checking if user is NR onboarding", zap.Error(err))
			return err
		}
		isNrOnboardingUser = isNrOnboardingUserRes.GetIsNonResidentUser().ToBool()
		return nil
	})
	g.Go(func() error {
		var panLinkErr error
		isPanLinkedToClosedAcc, panLinkErr = isPanLinkedToClosedAccount(ctx, userProc, onb.GetUserId())
		if panLinkErr != nil {
			return panLinkErr
		}
		return nil
	})
	if err := g.Wait(); err != nil {
		logger.Error(ctx, "error in err group for calculating kyc level update flow", zap.Error(err))
		return bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_UNSPECIFIED, err
	}
	switch {
	case isBKYCSuccess(onb):
		logger.Info(ctx, "BKYC user pre onboarding kyc level update flow")
		return bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_BKYC_PRE_ONBOARDING, nil
	case vkyc.IsUserCKYCWithOFlag(kycRecord.GetCkycAttempt()):
		logger.Info(ctx, "user is of o type")
		return bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_O, nil
	// takes more priority than dedupe with partial KYC
	case isPanLinkedToClosedAcc:
		logger.Info(ctx, "closed account reopening kyc level update flow")
		return bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_CLOSED_ACCOUNT_REOPENING, nil
	case isNrOnboardingUser:
		logger.Info(ctx, "Non Resident onboarding kyc level update flow")
		return bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_NON_RESIDENT_ONBOARDING, nil
	case isUserDedupeWithPartialKYC(onb):
		logger.Info(ctx, "user is partial kyc dedupe")
		return bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC, nil
	case isStudent:
		logger.Info(ctx, "user is student")
		return bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_STUDENT, nil
	case kycRecord.GetKycType() == kyc.KycType_EKYC && (reqParams.GetEkycNumberMismatch().GetIsNumberMismatch() == commontypes.BooleanEnum_TRUE ||
		(reqParams.GetEkycNumberMismatch().GetIsNumberMismatch() == commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED && cfg.IsProdEnv(env))):
		logger.Info(ctx, "ekyc number mismatch kyc level update flow")
		return bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_EKYC_NUMBER_MISMATCH, nil
	case isUserLowQuality == commontypes.BooleanEnum_TRUE:
		logger.Info(ctx, "low quality user kyc level update flow")
		return bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_LOW_QUALITY_USERS, nil
	case onb.GetFeature() == onbPb.Feature_FEATURE_PL:
		logger.Info(ctx, "personal loan user kyc level update flow")
		return bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_FEDERAL_LOANS, nil
	case isFiLiteUser:
		logger.Info(ctx, "fi lite user kyc level update flow")
		return bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_FI_LITE_USERS, nil
	case isFiLiteCCUser:
		logger.Info(ctx, "fi lite CC onboarding user kyc level update flow")
		return bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_FI_LITE_USERS, nil
	default:
		logger.Info(ctx, "user in default kyc level update flow")
		return bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_UNSPECIFIED, nil
	}
}

func isBKYCSuccess(onb *onbPb.OnboardingDetails) bool {
	return onb.GetStageDetails().GetStageMapping()[onbPb.OnboardingStage_BKYC.String()].GetState() == onbPb.OnboardingState_SUCCESS
}

// isPanLinkedToClosedAccount checks if the user has a pan linked to a closed account
func isPanLinkedToClosedAccount(ctx context.Context, userProc helper.UserProcessor, userId string) (bool, error) {
	user, err := userProc.GetUserByUserId(ctx, userId)
	if err != nil {
		logger.Error(ctx, "error in getting user", zap.Error(err))
		return false, err
	}

	delUsers, err := userProc.GetDeletedUsersLinkedToPan(ctx, user.GetProfile().GetPAN(), usersPb.DeletionDetails_DELETION_REASON_CLOSED_SAVINGS_ACCOUNT)
	if len(delUsers) > 0 {
		logger.Info(ctx, "pan linked to closed account")
		return true, nil
	}

	return false, nil
}

// getOfferWidgetForOnbScreensViaFiniteCode returns the offer-widget we'd want to show on onboarding screens. Its based on the following:
// 1. A/B for the experiment
// 2. Checks if you have used a finite-code or not.
// 3. Fetches different config based on the finite-code, i.e. whether its FI200 based or regular ones
func getOfferWidgetForOnbScreensViaFiniteCode(ctx context.Context, inAppReferralClient inAppReferralPb.InAppReferralClient, onbDynConf *genconf.OnboardingConfig, actorId string) *deeplinkPb.IconTextWidget {
	abEvaluator := release.NewABEvaluator[string](
		onbDynConf.OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig(),
		release.NewConstraintFactoryImpl(release.NewAppVersionConstraint(), release.NewStickinessConstraint(), release.NewUserGroupConstraint(nil, nil, nil)),
		func(str string) string {
			return str
		},
	)
	offerWidgetEnabled, variant, err := abEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_UNSPECIFIED).WithActorId(actorId))
	if err != nil {
		logger.WarnWithCtx(ctx, "error evaluating AB for offer-widget on onb screens. ignoring silently", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil
	}

	// return if the widget is not enabled for the actor, or the actor doesn't fall in the variant
	if !offerWidgetEnabled || variant != "ONE" {
		return nil
	}

	referralDetailsRes, err := inAppReferralClient.GetReferralDetailsForActor(ctx, &inAppReferralPb.GetReferralDetailsForActorRequest{
		ActorId:                   actorId,
		ForceIncludeExceptionCase: true,
	})
	if err != nil || (!referralDetailsRes.GetStatus().IsSuccess() && !referralDetailsRes.GetStatus().IsRecordNotFound()) {
		logger.WarnWithCtx(ctx, "error fetching referral details for the actor", zap.Error(err), zap.Any(logger.RPC_STATUS, referralDetailsRes.GetStatus()))
		return nil
	}
	if referralDetailsRes.GetStatus().IsRecordNotFound() {
		logger.Debug(ctx, "actor is not a referee. Thus, no offer widget details")
		return nil
	}

	finiteCode := referralDetailsRes.GetReferralDetails().GetCode()
	// check if we have an offer-widget config for the finite-code or not, i.e. whether its FI200 based or not
	offerWidgetConf := onbDynConf.ReferralOfferWidgetsDuringOnboarding().Get(finiteCode)
	// if available but is not enabled, no need to show any offer-widget
	if offerWidgetConf != nil && !offerWidgetConf.IsEnabled() {
		return nil
	}

	// if FI200 specific config is not present, fetch the default/regular conf
	if offerWidgetConf == nil {
		offerWidgetConf = onbDynConf.ReferralOfferWidgetsDuringOnboarding().Get("REGULAR")
	}

	// return if none of the offer-widget configs are available or enabled
	if offerWidgetConf == nil || !offerWidgetConf.IsEnabled() {
		return nil
	}

	return &deeplinkPb.IconTextWidget{
		LeftImg: &commontypes.Image{ImageType: commontypes.ImageType_PNG, ImageUrl: offerWidgetConf.LeftIcon()},
		Text: &commontypes.Text{
			DisplayValue: &commontypes.Text_Html{Html: offerWidgetConf.OfferText()},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
		},
		LeftImgTextPadding: 4,
		BgColor:            offerWidgetConf.BgColor(),
	}
}

// getABEvaluatorOfFeature returns an instance of the AB evaluator to perform experiments of type ABExperiment
func getABEvaluatorOfFeature[ABExperiment any](
	actorClient actorPb.ActorClient, userClient usersPb.UsersClient, userGroupClient userGroupPb.GroupClient,
	abFeatureReleaseConf *releaseGenConf.ABFeatureReleaseConfig,
	strToExprFn func(str string) ABExperiment,
) *release.ABEvaluator[ABExperiment] {
	abEvaluator := release.NewABEvaluator[ABExperiment](
		abFeatureReleaseConf,
		release.NewConstraintFactoryImpl(
			release.NewAppVersionConstraint(),
			release.NewStickinessConstraint(),
			release.NewUserGroupConstraint(actorClient, userClient, userGroupClient),
		),
		strToExprFn,
	)

	return abEvaluator
}

// skipCKYCForDedupeUsers checks the latest dedupe status for users and allows us to check if we can skip ckyc for them.
// https://monorail.pointz.in/p/fi-app/issues/detail?id=37308
func skipCKYCForDedupeUsers(onb *onbPb.OnboardingDetails) bool {
	return onb.GetStageMetadata().GetLatestDedupeStatus() == vgCustomerPb.DedupeStatus_CUSTOMER_EXISTS ||
		onb.GetStageMetadata().GetLatestDedupeStatus() == vgCustomerPb.DedupeStatus_CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT ||
		onb.GetStageMetadata().GetLatestDedupeStatus() == vgCustomerPb.DedupeStatus_CUSTOMER_EXISTS_PARTIAL_KYC
}

func CheckReleaseStickinessConstraint(ctx context.Context, actorId, feature string, rolloutPercentage int) bool {
	featureAndActor := fmt.Sprintf("%v%v", feature, actorId)
	num, err := release.GetHashNum(featureAndActor)
	if err != nil {
		logger.Error(ctx, "error while generating hash for actor", zap.Error(err))
		return false
	}
	logger.Info(ctx, fmt.Sprintf("check user-bankcust split release stickiness: %v %v, %v, %v", actorId, num, num%100,
		rolloutPercentage))
	if num%100 < uint64(rolloutPercentage) {
		logger.Info(ctx, fmt.Sprintf("%v ReleaseStickinessConstraint evaluation returned true", feature))
		return true
	}
	return false
}

// FiLiteReleaseConstraint is a custom function to support fractional release of fi lite to external with a base of 1000 instead of 100
func FiLiteReleaseConstraint(ctx context.Context, actorId string, rolloutPercentage int) bool {
	feature := "fi_lite_external_release"
	featureAndActor := fmt.Sprintf("%v%v", feature, actorId)
	num, err := release.GetHashNum(featureAndActor)
	if err != nil {
		logger.Error(ctx, "error while generating hash for actor", zap.Error(err))
		return false
	}
	logger.Info(ctx, fmt.Sprintf("check %v split release stickiness: %v %v, %v, %v", feature, actorId, num, num%1000,
		rolloutPercentage))
	if num%1000 < uint64(rolloutPercentage) {
		logger.Info(ctx, fmt.Sprintf("%v ReleaseStickinessConstraint evaluation returned true", feature))
		return true
	}
	return false
}

// hasUserOnboardedViaRegularInAppReferral: checks whether user has onboarded via a regular (type), in app referral (channel)
func hasUserOnboardedViaRegularInAppReferral(ctx context.Context, inAppReferralClient inAppReferralPb.InAppReferralClient, actorId string) (bool, error) {
	referralDetailsResp, referralDetailsErr := inAppReferralClient.GetReferralDetailsForActor(ctx, &inAppReferralPb.GetReferralDetailsForActorRequest{
		ActorId: actorId,
	})

	if rpcErr := epifigrpc.RPCError(referralDetailsResp, referralDetailsErr); rpcErr != nil && !referralDetailsResp.GetStatus().IsRecordNotFound() {
		return false, fmt.Errorf("error while fetching referral details: %w, returning false", rpcErr)
	}

	if referralDetailsResp.GetStatus().IsRecordNotFound() {
		return false, nil
	}

	return referralDetailsResp.GetReferralDetails().GetFiniteCodeChannel() == inAppReferralEnumPb.FiniteCodeChannel_IN_APP_REFERRAL &&
		referralDetailsResp.GetReferralDetails().GetFiniteCodeType() == inAppReferralEnumPb.FiniteCodeType_REGULAR, nil
}

func shouldDirectUserToFiLite(ctx context.Context, onbConf *genconf.OnboardingConfig, _ usersPb.AcquisitionIntent, _ usersPb.AcquisitionChannel) bool {
	// direct to fi lite is enabled
	return onbConf.DirectToFiLite().Enable(ctx)
}

// transitionToFiLite updates the fi lite state to enabled, and moves the user to home.
// This will be called wherever forced transition to Fi Lite home is required
func transitionToFiLite(ctx context.Context, dao dao.OnboardingDao, eventLogger userEvents.EventLogger, onb *onbPb.OnboardingDetails, source onbPb.FiLiteSource) (*StageProcessorResponse, error) {
	if onb.GetFiLiteDetails() == nil {
		onb.FiLiteDetails = &onbPb.FiLiteDetails{}
	}
	onb.Feature = onbPb.Feature_FEATURE_FI_LITE
	if onb.GetFiLiteDetails().GetFiLiteSource() == onbPb.FiLiteSource_FI_LITE_SOURCE_UNSPECIFIED {
		onb.FiLiteDetails.FiLiteSource = source
	}
	onb.FiLiteDetails.IsEnabled = commontypes.BooleanEnum_TRUE
	if onb.GetFiLiteDetails().GetAccessibilityEnabledAt() == nil {
		onb.FiLiteDetails.AccessibilityEnabledAt = timestampPb.Now()
	}

	if errUpdate := dao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_FEATURE,
		onbPb.OnboardingDetailsFieldMask_FI_LITE_DETAILS,
	}, onb); errUpdate != nil {
		logger.Error(ctx, "failed to update onb details", zap.Error(errUpdate))
		return nil, errUpdate
	}

	eventLogger.LogFiLiteUserConverted(ctx, onb.GetActorId(), onb.GetFiLiteDetails().GetFiLiteSource())
	return &StageProcessorResponse{
		NextAction: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
			ScreenOptions: &deeplinkPb.Deeplink_GetNextOnboardingActionScreenOptions{
				GetNextOnboardingActionScreenOptions: &deeplinkPb.GetNextOnboardingActionScreenOptions{
					Title:    "Taking you to home!",
					ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite_dino.png",
					Feature:  onbPb.Feature_FEATURE_FI_LITE.String(),
				},
			},
		},
	}, nil
}

// removeFiLiteFlag is used to upgrade the user from fi lite to full account after debit card pin setup is done
func removeFiLiteFlag(ctx context.Context, onbDao dao.OnboardingDao, onbDetail *onbPb.OnboardingDetails) error {
	if onbDetail.GetFiLiteDetails().GetIsEnabled() != commontypes.BooleanEnum_TRUE {
		return nil
	}

	onbDetail.FiLiteDetails.IsEnabled = commontypes.BooleanEnum_FALSE
	return onbDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_FI_LITE_DETAILS}, onbDetail)

}

// time client needs to be injected in case feature needs to be updated to active, as we need to set CompletedAt field
func updateFeatureOnbStatus(ctx context.Context, onbDao dao.OnboardingDao, onbDetail *onbPb.OnboardingDetails, feature onbPb.Feature, featureStatus onbPb.FeatureStatus, timeClient datetime.Time) error {
	if onbDetail.GetFeatureDetails().GetFeatureInfo()[feature.String()].GetFeatureStatus() == featureStatus {
		return nil
	}

	switch {
	case onbDetail.GetFeatureDetails() == nil:
		onbDetail.FeatureDetails = &onbPb.FeatureDetails{
			FeatureInfo: map[string]*onbPb.FeatureInfo{
				feature.String(): {},
			},
		}
	case onbDetail.GetFeatureDetails().GetFeatureInfo() == nil:
		onbDetail.FeatureDetails.FeatureInfo = map[string]*onbPb.FeatureInfo{
			feature.String(): {},
		}
	case onbDetail.GetFeatureDetails().GetFeatureInfo()[feature.String()] == nil:
		onbDetail.GetFeatureDetails().GetFeatureInfo()[feature.String()] = &onbPb.FeatureInfo{}

	}
	onbDetail.GetFeatureDetails().GetFeatureInfo()[feature.String()].FeatureStatus = featureStatus
	if featureStatus == onbPb.FeatureStatus_FEATURE_STATUS_ACTIVE {
		onbDetail.GetFeatureDetails().GetFeatureInfo()[feature.String()].CompletedAt = timestampPb.New(timeClient.Now())
	}
	return onbDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_FEATURE_DETAILS}, onbDetail)

}

func updateFiLiteAccessibility(ctx context.Context, onbDao dao.OnboardingDao, onb *onbPb.OnboardingDetails, timeClient datetime.Time) error {
	if onb.GetFiLiteDetails() == nil {
		onb.FiLiteDetails = &onbPb.FiLiteDetails{}
	}

	onb.GetFiLiteDetails().IsEnabled = commontypes.BooleanEnum_TRUE
	if onb.GetFiLiteDetails().GetAccessibilityEnabledAt() == nil {
		onb.FiLiteDetails.AccessibilityEnabledAt = timestampPb.New(timeClient.Now())
	}

	return onbDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_FI_LITE_DETAILS}, onb)
}

// util function to check if flag is enabled and group mapping is present
func isFiLiteEnabled(ctx context.Context, client userGroupPb.GroupClient, userProc helper.UserProcessor, onb *onbPb.OnboardingDetails,
	conf *genconf.OnboardingConfig, needUG bool) bool {
	if onb.GetFeature().IsNonResidentUserOnboarding() {
		return false
	}

	// If user group needs to be checked, otherwise check external release rollout config
	if needUG {
		user, err := userProc.GetUserByActorId(ctx, onb.GetActorId())
		if err != nil {
			logger.Error(ctx, "failed to get user by actor id", zap.Error(err))
			return false
		}

		getGroupsResp, errGetGroups := client.GetGroupsMappedToEmail(ctx, &userGroupPb.GetGroupsMappedToEmailRequest{
			Email: user.GetProfile().GetEmail(),
		})
		if grpcErr := epifigrpc.RPCError(getGroupsResp, errGetGroups); grpcErr != nil {
			logger.Error(ctx, "failed to get user group mapping by email id", zap.Error(err))
			return false
		}

		_, inUG := lo.Find(getGroupsResp.GetGroups(), func(item commontypes.UserGroup) bool {
			return item == commontypes.UserGroup_INTERNAL || item == commontypes.UserGroup_FNF
		})
		if !inUG {
			return false
		}
	} else {
		if !FiLiteReleaseConstraint(ctx, onb.GetActorId(), conf.FiLiteRolloutPercentage()) {
			return false
		}
		isNonResidentUserRes, err := userProc.IsNonResidentUser(ctx, onb.GetActorId())
		if err != nil {
			logger.Error(ctx, "error in checking if user is non resident", zap.Error(err))
			return false
		}
		if isNonResidentUserRes.GetIsNonResidentUser().ToBool() {
			logger.Info(ctx, "blocking fi lite for non resident users")
			return false
		}
	}

	// fi lite risk screening check
	// do not let the user enter if fi lite risk screening is failed
	if stageInfo(onb, onbPb.OnboardingStage_FI_LITE_RISK_SCREENING).GetState() == onbPb.OnboardingState_FAILURE {
		return false
	}

	logger.Info(ctx, "fi lite is enabled for user")
	return true
}

// isUserPastCurrentStage function tells if any stage after the current onboarding stage has been skipped or successful
func isUserPastCurrentStage(stagesOrder []onbPb.OnboardingStage, details *onbPb.OnboardingDetails) bool {
	stageAfterCurrentStage := false
	for _, stage := range stagesOrder {
		if stage == details.GetCurrentOnboardingStage() {
			stageAfterCurrentStage = true
			continue
		}
		if stageAfterCurrentStage && getStageStatus(details, stage).IsSuccessOrSkipped() {
			return true
		}
	}
	return false
}

func ActionForUserStuckOnAcctCreation(ctx context.Context, onb *onbPb.OnboardingDetails) *deeplinkPb.Deeplink {
	errOpts := error2.UserStuckOnAccountCreation()
	if cfg.IsNonProdEnvBestEffort() {
		errOpts.Subtitle += "<br><br>" + getNonProdAdviceForAccountCreation(ctx, onb)
	}
	return deeplink.NewErrorFullScreen(errOpts)
}

// TODO(aditya): Move impl to troubleshooter. Starting with basic as Troubleshooter advice framework might take some time.
func getNonProdAdviceForAccountCreation(_ context.Context, onb *onbPb.OnboardingDetails) string {
	return map[onbPb.OnboardingStage]string{
		onbPb.OnboardingStage_UPDATE_PROFILE_DETAILS: "You're stuck on profile update for dedupe user stage. Reach out to @onb-oncall on #5-onboarding-meeting-room",
		onbPb.OnboardingStage_CUSTOMER_CREATION:      "You're stuck on customer creation stage. Reach out to @onb-oncall on #5-onboarding-meeting-room",
		onbPb.OnboardingStage_ACCOUNT_CREATION:       "You're stuck on account creation stage. Reach out to @cg-oncall on #central-growth",
		onbPb.OnboardingStage_CARD_CREATION:          "You're stuck on Card creation stage. Reach out to @cards-oncall  on #cards-tech",
		onbPb.OnboardingStage_NRO_ACCOUNT_CREATION:   "You're stuck on NRO account creation stage. Reach out to@onb-oncall on #5-onboarding-meeting-room",
	}[onb.GetCurrentOnboardingStage()]
}

func IsScreenerStageSkippable(stage onbPb.OnboardingStage) bool {
	return lo.Contains(skippableScreenerStages, stage)
}

func IsOnbStageInScreener(stage onbPb.OnboardingStage) bool {
	return lo.Contains(screenerStages, stage)
}

func GetOnbStagesTillAppScreening(currentStage onbPb.OnboardingStage) ([]onbPb.OnboardingStage, error) {
	startIndex := lo.IndexOf(SAOnboardingStagesOrder, currentStage)
	if startIndex == -1 {
		return nil, notSaOnbStageErr
	}
	endIndex := lo.IndexOf(SAOnboardingStagesOrder, onbPb.OnboardingStage_APP_SCREENING)
	if startIndex >= endIndex {
		return nil, stageAfterAppScreeningErr
	}
	return SAOnboardingStagesOrder[startIndex:endIndex], nil
}

// getUserCountryFromIp extracts ip from context, and fetches its corresponding ip
func getUserCountryFromIp(ctx context.Context, locClient userLocationPb.LocationClient, obfClient obfuscator.ObfuscatorClient) (string, error) {
	ipToken, ipErr := obfPkg.GenIPAddressTokenFromCtx(ctx, obfClient)
	if ipErr != nil {
		return "", fmt.Errorf("invalid ip address in verify location stage: %w", ipErr)
	}
	resp, err := locClient.FetchAndStoreAddressForIdentifier(ctx, &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
		IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
		IdentifierValue: ipToken,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil && !resp.GetStatus().IsRecordNotFound() && !(resp.GetStatus().GetCode() == uint32(userLocationPb.FetchAndStoreAddressForIdentifierResponse_PRIVATE_IP)) {
		return "", fmt.Errorf("error in fetching address for IP : %w", err)
	}
	switch resp.GetStatus().GetCode() {
	case uint32(userLocationPb.FetchAndStoreAddressForIdentifierResponse_OK):
		return resp.GetAddress().GetRegionCode(), nil
	case uint32(userLocationPb.FetchAndStoreAddressForIdentifierResponse_RECORD_NOT_FOUND):
		return "", epifierrors.ErrRecordNotFound
	case uint32(userLocationPb.FetchAndStoreAddressForIdentifierResponse_PRIVATE_IP):
		return "", privateIpErr
	default:
		return "", fmt.Errorf("unhandled error code for FetchAndStoreAddressForIdentifier in getUserCountryFromIp: %v ", resp.GetStatus().GetCode())
	}
}

func handleGetUserCountryFromIpError(ctx context.Context, err error) (*StageProcessorResponse, error) {
	switch {
	case errors.Is(err, privateIpErr):
		// GNOA will send fresh ip from the client which ideally should not be private ip again
		return &StageProcessorResponse{
			NextAction: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
			},
		}, nil
	default:
		// encountered random error while fetching address for ip, not blocking the user here as
		// risk-screening check should handle that
		logger.Error(ctx, "error while getting address for ip", zap.Error(err))
		return nil, NoActionSkipStatusUpdateError
	}
}

// getUserGroupsByIdentifier returns user groups to which a given user is linked using GetGroupsMappedToIdentifier RPC
// in case user doesn't belong to any group then empty slice is returned
func getUserGroupsByIdentifier(ctx context.Context, userId string, userGrpClient userGroupPb.GroupClient, userClient usersPb.UsersClient) ([]commontypes.UserGroup, error) {
	// First get user email
	userRes, userErr := userClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_Id{Id: userId},
	})
	if err := epifigrpc.RPCError(userRes, userErr); err != nil {
		return nil, fmt.Errorf("failed to fetch user: %s: %w", userId, err)
	}

	userGrpRes, userGrpErr := userGrpClient.GetGroupsMappedToIdentifier(ctx,
		&userGroupPb.GetGroupsMappedToIdentifierRequest{
			IdentifierValue: &userGroupPb.IdentifierValue{
				Identifier: &userGroupPb.IdentifierValue_Email{
					Email: userRes.GetUser().GetProfile().GetEmail(),
				},
			},
		})
	if err := epifigrpc.RPCError(userGrpRes, userGrpErr); err != nil {
		return nil, fmt.Errorf("failed to fetch user groups for user: %s: %w", userId, err)
	}

	return userGrpRes.GetGroups(), nil
}
