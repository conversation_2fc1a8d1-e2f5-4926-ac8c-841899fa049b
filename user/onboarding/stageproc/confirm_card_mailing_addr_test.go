package stageproc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/gamma/api/bankcust"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/kyc"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	kycPkg "github.com/epifi/gamma/pkg/kyc"
)

var (
	kycLevelFull = kyc.KYCLevel_FULL_KYC

	shippingPref1 = &user.ShippingPreference{
		Id:      "1234",
		ActorId: "123",
	}
	shippingPreferenceReq = &user.GetShippingPreferenceRequest{
		ActorId:      "123",
		ShippingItem: types.ShippingItem_DEBIT_CARD,
	}
	shippingRespSuccess = &user.GetShippingPreferenceResponse{
		Preference: shippingPref1,
		Status:     rpc.StatusOk(),
	}
	shippingRespRecordNotFound = &user.GetShippingPreferenceResponse{
		Preference: shippingPref1,
		Status:     rpc.StatusRecordNotFound(),
	}
	longNameUserFix = &user.User{
		Profile: &user.Profile{
			KycName: &commontypes.Name{
				FirstName: "Long Name",
				LastName:  "Very Long Name",
			},
		},
	}
	emptyNameUserFix = &user.User{
		Profile: &user.Profile{
			KycName: &commontypes.Name{
				FirstName: "1 &^%",
				LastName:  "& * *",
			},
		},
	}
	shortNameUserFix = &user.User{
		Profile: &user.Profile{
			KycName: &commontypes.Name{
				FirstName: "Short",
				LastName:  "Name",
			},
		},
	}
	cleanedNameUserFix = &user.User{
		Profile: &user.Profile{
			KycName: &commontypes.Name{
				FirstName: "Sh@89ort",
				LastName:  "Na#me",
			},
		},
	}
	cleanedNameUserFix2 = &user.User{
		Profile: &user.Profile{
			KycName: &commontypes.Name{
				FirstName: "Sh@89ort abc",
				LastName:  "Na#me",
			},
		},
	}
)

func TestService_ConfirmCardMailingAddress(t *testing.T) {
	t.Parallel()
	screenParams := ts.Conf.Onboarding.PhysicalCardChargesMailingAddressScreenParams
	type getShippingPreferenceParams struct {
		req  *user.GetShippingPreferenceRequest
		resp *user.GetShippingPreferenceResponse
		err  error
	}

	type args struct {
		onb                         *onbPb.OnboardingDetails
		getShippingPreferenceParams *getShippingPreferenceParams
	}
	type test struct {
		name    string
		args    args
		mocks   func(mock *Clients)
		wantErr bool
		wantDL  *dlPb.Deeplink
	}
	ctx := context.Background()
	tests := []test{
		{
			name: "StageMetadata missing",
			args: args{
				onb: &onbPb.OnboardingDetails{
					UserId:  "1",
					ActorId: "123",
				},
			},
			mocks: func(mock *Clients) {
				mock.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&user.GetUserResponse{
					User:   longNameUserFix,
					Status: rpc.StatusOk(),
				}, nil)
				mock.bankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						Id: "bc-1",
						KycInfo: &bankcust.KYCInfo{
							KycLevel: kycLevelFull,
						},
					},
				}, nil)
				mock.KycClient.EXPECT().CheckKYCStatus(gomock.Any(), &kyc.CheckKYCStatusRequest{
					ActorId: "123",
				}).Return(&kyc.CheckKYCStatusResponse{
					Status:  rpc.StatusOk(),
					KycType: kyc.KycType_EKYC,
				}, nil)
			},
			wantErr: false,
			wantDL: &dlPb.Deeplink{
				Screen: dlPb.Screen_CONFIRM_CARD_MAILING_ADDRESS,
				ScreenOptions: &dlPb.Deeplink_ConfirmCardMailingAddressOptions{
					ConfirmCardMailingAddressOptions: &dlPb.ConfirmCardMailingAddressOptions{
						HideAddressField:      true,
						KycLevel:              kycPkg.KycLevelMap[kycLevelFull],
						PlaceHolderForName:    screenParams.PlaceholderForName,
						PlaceHolderForAddress: screenParams.PlaceholderForAddress,
						CheckBoxTexts: []*dlPb.ConfirmCardMailingAddressOptions_CheckBoxText{
							{
								Type: types.AddressType_MAILING,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
							{
								Type: types.AddressType_PERMANENT,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
							{
								Type: types.AddressType_SHIPPING,
								Text: screenParams.NonAadharAddrCheckBoxText,
							},
							{
								Type: types.AddressType_ADDRESS_TYPE_UNSPECIFIED,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
						},
						Flow: dlPb.ConfirmCardMailingAddressOptions_FLOW_ONBOARDING,
						Image: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  screenParams.ImageUrl,
						},
						CheckboxTextColor: screenParams.CheckboxTextColor,
						PlaceHolderColor:  screenParams.PlaceholderColor,
						ContentColor:      screenParams.ContentColor,
						DividerColor:      screenParams.DividerColor,
						EditIconColor:     screenParams.EditIconColor,
						CardColor:         screenParams.CardColor,
						BackgroundColor:   screenParams.BackgroundColor,
						ScreenTitle: &commontypes.Text{
							FontColor:    screenParams.TitleFontColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: screenParams.Title},
						},
						ScreenSubtitle: &commontypes.Text{
							FontColor:    screenParams.SubtitleFontColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: screenParams.Subtitle},
						},
						Cta: &dlPb.Cta{
							Type:         dlPb.Cta_CONTINUE,
							Text:         screenParams.CtaText,
							DisplayTheme: dlPb.Cta_PRIMARY,
							Status:       dlPb.Cta_CTA_STATUS_ENABLED,
						},
					},
				},
			},
		},
		{
			name: "Debit card retry not exhausted",
			args: args{
				onb: &onbPb.OnboardingDetails{
					UserId:  "1",
					ActorId: "123",
					StageMetadata: &onbPb.StageMetadata{
						DebitCardNameCheck: &onbPb.DebitCardNameCheck{
							OldNameMatchPassed:  false,
							NameCheckRetryCount: 1,
						},
					},
				},
			},
			mocks: func(mock *Clients) {
				mock.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&user.GetUserResponse{
					User:   longNameUserFix,
					Status: rpc.StatusOk(),
				}, nil)
				mock.bankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						Id: "bc-1",
						KycInfo: &bankcust.KYCInfo{
							KycLevel: kycLevelFull,
						},
					},
				}, nil)
				mock.KycClient.EXPECT().CheckKYCStatus(gomock.Any(), &kyc.CheckKYCStatusRequest{
					ActorId: "123",
				}).Return(&kyc.CheckKYCStatusResponse{
					Status:  rpc.StatusOk(),
					KycType: kyc.KycType_EKYC,
				}, nil)
			},
			wantErr: false,
			wantDL: &dlPb.Deeplink{
				Screen: dlPb.Screen_CONFIRM_CARD_MAILING_ADDRESS,
				ScreenOptions: &dlPb.Deeplink_ConfirmCardMailingAddressOptions{
					ConfirmCardMailingAddressOptions: &dlPb.ConfirmCardMailingAddressOptions{
						HideAddressField:      true,
						KycLevel:              kycPkg.KycLevelMap[kycLevelFull],
						PlaceHolderForName:    screenParams.PlaceholderForName,
						PlaceHolderForAddress: screenParams.PlaceholderForAddress,
						CheckBoxTexts: []*dlPb.ConfirmCardMailingAddressOptions_CheckBoxText{
							{
								Type: types.AddressType_MAILING,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
							{
								Type: types.AddressType_PERMANENT,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
							{
								Type: types.AddressType_SHIPPING,
								Text: screenParams.NonAadharAddrCheckBoxText,
							},
							{
								Type: types.AddressType_ADDRESS_TYPE_UNSPECIFIED,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
						},
						Flow: dlPb.ConfirmCardMailingAddressOptions_FLOW_ONBOARDING,
						Image: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  screenParams.ImageUrl,
						},
						CheckboxTextColor: screenParams.CheckboxTextColor,
						PlaceHolderColor:  screenParams.PlaceholderColor,
						ContentColor:      screenParams.ContentColor,
						DividerColor:      screenParams.DividerColor,
						EditIconColor:     screenParams.EditIconColor,
						CardColor:         screenParams.CardColor,
						BackgroundColor:   screenParams.BackgroundColor,
						ScreenTitle: &commontypes.Text{
							FontColor:    screenParams.TitleFontColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: screenParams.Title},
						},
						ScreenSubtitle: &commontypes.Text{
							FontColor:    screenParams.SubtitleFontColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: screenParams.Subtitle},
						},
						Cta: &dlPb.Cta{
							Type:         dlPb.Cta_CONTINUE,
							Text:         screenParams.CtaText,
							DisplayTheme: dlPb.Cta_PRIMARY,
							Status:       dlPb.Cta_CTA_STATUS_ENABLED,
						},
					},
				},
			},
		},
		{
			name: "Debit card retries exhausted",
			args: args{
				onb: &onbPb.OnboardingDetails{
					UserId:  "1",
					ActorId: "123",
					StageMetadata: &onbPb.StageMetadata{
						DebitCardNameCheck: &onbPb.DebitCardNameCheck{
							OldNameMatchPassed:  false,
							NameCheckRetryCount: constant.DebitCardNameMaxRetries,
						},
					},
				},
			},
			mocks: func(mock *Clients) {
				mock.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&user.GetUserResponse{
					User:   longNameUserFix,
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: false,
			wantDL:  deeplink.NewErrorFullScreen(error2.OnbErrCardNameMaxRetries),
		},
		{
			name: "confirm card mailing automatic",
			args: args{
				onb: &onbPb.OnboardingDetails{
					UserId:  "1",
					ActorId: "123",
					StageMetadata: &onbPb.StageMetadata{
						DebitCardNameCheck: &onbPb.DebitCardNameCheck{
							OldNameMatchPassed:  true,
							NameCheckRetryCount: 1,
						},
					},
				},
			},
			mocks: func(mock *Clients) {
				mock.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&user.GetUserResponse{
					User:   shortNameUserFix,
					Status: rpc.StatusOk(),
				}, nil)
				mock.userClient.EXPECT().UpdateUser(gomock.Any(), &user.UpdateUserRequest{
					User: &user.User{
						Id: "1",
						Profile: &user.Profile{
							DebitCardName: shortNameUserFix.GetProfile().GetKycName(),
						},
					},
					UpdateMask: []user.UserFieldMask{
						user.UserFieldMask_DEBIT_CARD_NAME,
					},
				}).Return(&user.UpdateUserResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: true,
			wantDL:  getDeviceRegistrationTransitionScreen(onbPb.Feature_FEATURE_UNSPECIFIED),
		},
		{
			name: "confirm card mailing, with Sanitize KYC Name",
			args: args{
				onb: &onbPb.OnboardingDetails{
					UserId:  "1",
					ActorId: "123",
					StageMetadata: &onbPb.StageMetadata{
						DebitCardNameCheck: &onbPb.DebitCardNameCheck{
							OldNameMatchPassed:  true,
							NameCheckRetryCount: 1,
						},
					},
				},
			},
			mocks: func(mock *Clients) {
				mock.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&user.GetUserResponse{
					User:   cleanedNameUserFix,
					Status: rpc.StatusOk(),
				}, nil)
				mock.userClient.EXPECT().UpdateUser(gomock.Any(), &user.UpdateUserRequest{
					User: &user.User{
						Id: "1",
						Profile: &user.Profile{
							DebitCardName: shortNameUserFix.GetProfile().GetKycName(),
						},
					},
					UpdateMask: []user.UserFieldMask{
						user.UserFieldMask_DEBIT_CARD_NAME,
					},
				}).Return(&user.UpdateUserResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: true,
			wantDL:  getDeviceRegistrationTransitionScreen(onbPb.Feature_FEATURE_UNSPECIFIED),
		},
		{
			name: "confirm card mailing, with Sanitize KYC Name white space",
			args: args{
				onb: &onbPb.OnboardingDetails{
					UserId:  "1",
					ActorId: "123",
					StageMetadata: &onbPb.StageMetadata{
						DebitCardNameCheck: &onbPb.DebitCardNameCheck{
							OldNameMatchPassed:  true,
							NameCheckRetryCount: 1,
						},
					},
				},
			},
			mocks: func(mock *Clients) {
				mock.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&user.GetUserResponse{
					User:   cleanedNameUserFix2,
					Status: rpc.StatusOk(),
				}, nil)
				mock.userClient.EXPECT().UpdateUser(gomock.Any(), &user.UpdateUserRequest{
					User: &user.User{
						Id: "1",
						Profile: &user.Profile{
							DebitCardName: &commontypes.Name{
								FirstName:  "Short",
								MiddleName: "abc",
								LastName:   "Name",
							},
						},
					},
					UpdateMask: []user.UserFieldMask{
						user.UserFieldMask_DEBIT_CARD_NAME,
					},
				}).Return(&user.UpdateUserResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: true,
			wantDL:  getDeviceRegistrationTransitionScreen(onbPb.Feature_FEATURE_UNSPECIFIED),
		},
		{
			name: "empty name fix",
			args: args{
				onb: &onbPb.OnboardingDetails{
					UserId:  "1",
					ActorId: "123",
					StageMetadata: &onbPb.StageMetadata{
						DebitCardNameCheck: &onbPb.DebitCardNameCheck{
							OldNameMatchPassed:  false,
							NameCheckRetryCount: 1,
						},
					},
				},
				getShippingPreferenceParams: &getShippingPreferenceParams{
					req:  shippingPreferenceReq,
					resp: shippingRespRecordNotFound,
					err:  nil,
				},
			},
			mocks: func(mock *Clients) {
				mock.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&user.GetUserResponse{
					User:   emptyNameUserFix,
					Status: rpc.StatusOk(),
				}, nil)
				mock.bankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						Id: "bc-1",
						KycInfo: &bankcust.KYCInfo{
							KycLevel: kycLevelFull,
						},
					},
				}, nil)
				mock.KycClient.EXPECT().CheckKYCStatus(gomock.Any(), &kyc.CheckKYCStatusRequest{
					ActorId: "123",
				}).Return(&kyc.CheckKYCStatusResponse{
					Status:  rpc.StatusOk(),
					KycType: kyc.KycType_EKYC,
				}, nil)
			},
			wantErr: false,
			wantDL: &dlPb.Deeplink{
				Screen: dlPb.Screen_CONFIRM_CARD_MAILING_ADDRESS,
				ScreenOptions: &dlPb.Deeplink_ConfirmCardMailingAddressOptions{
					ConfirmCardMailingAddressOptions: &dlPb.ConfirmCardMailingAddressOptions{
						HideAddressField:      true,
						KycLevel:              kycPkg.KycLevelMap[kycLevelFull],
						PlaceHolderForName:    screenParams.PlaceholderForName,
						PlaceHolderForAddress: screenParams.PlaceholderForAddress,
						CheckBoxTexts: []*dlPb.ConfirmCardMailingAddressOptions_CheckBoxText{
							{
								Type: types.AddressType_MAILING,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
							{
								Type: types.AddressType_PERMANENT,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
							{
								Type: types.AddressType_SHIPPING,
								Text: screenParams.NonAadharAddrCheckBoxText,
							},
							{
								Type: types.AddressType_ADDRESS_TYPE_UNSPECIFIED,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
						},
						Flow: dlPb.ConfirmCardMailingAddressOptions_FLOW_ONBOARDING,
						Image: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  screenParams.ImageUrl,
						},
						CheckboxTextColor: screenParams.CheckboxTextColor,
						PlaceHolderColor:  screenParams.PlaceholderColor,
						ContentColor:      screenParams.ContentColor,
						DividerColor:      screenParams.DividerColor,
						EditIconColor:     screenParams.EditIconColor,
						CardColor:         screenParams.CardColor,
						BackgroundColor:   screenParams.BackgroundColor,
						ScreenTitle: &commontypes.Text{
							FontColor:    screenParams.TitleFontColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: screenParams.Title},
						},
						ScreenSubtitle: &commontypes.Text{
							FontColor:    screenParams.SubtitleFontColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: screenParams.Subtitle},
						},
						Cta: &dlPb.Cta{
							Type:         dlPb.Cta_CONTINUE,
							Text:         screenParams.CtaText,
							DisplayTheme: dlPb.Cta_PRIMARY,
							Status:       dlPb.Cta_CTA_STATUS_ENABLED,
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			clients := setupServiceWithMocks(t)
			s := NewConfirmCardMailingAddressStage(clients.userClient, clients.KycClient, clients.bankCustClient, ts.GenConf.Onboarding())
			tt.mocks(clients)
			res, err := s.StageProcessor(ctx, &StageProcessorRequest{Onb: tt.args.onb})
			dl := res.GetNextAction()
			assert.Equal(t, tt.wantErr, err != nil)
			assert.Equal(t, tt.wantDL, dl)
		})
	}
}
