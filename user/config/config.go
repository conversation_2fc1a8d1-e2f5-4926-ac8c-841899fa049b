package config

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"time"

	"github.com/pkg/errors"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/frontend/app"
	sdkconfig "github.com/epifi/be-common/quest/sdk/config"

	"github.com/epifi/gamma/api/comms"
	userPb "github.com/epifi/gamma/api/user"
	userintelPb "github.com/epifi/gamma/api/userintel"
	releaseConfig "github.com/epifi/gamma/pkg/feature/release/config"
)

const (
	PUSH_NOTIFICATION NotificationType = "PUSH_NOTIFICATION"
	SMS               NotificationType = "SMS"
	WHATSAPP          NotificationType = "WHATSAPP"
)

var (
	once       sync.Once
	config     *Config
	err        error
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(false)
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load config")
	}

	return config, nil
}

// LoadOnlyStaticConf loads configs only from static config files if not done already and returns it
func LoadOnlyStaticConf() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(true)
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig(onlyStaticFiles bool) (*Config, error) {
	conf := &Config{}
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()

	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, cfg.USER_SERVICE)

	if err != nil {
		return nil, fmt.Errorf("failed to load dynamic config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to refresh dymanic config: %w", err)
	}
	if onlyStaticFiles {
		return conf, nil
	}

	_, err = cfg.LoadSecretsAndPrepareDBConfig(conf.Secrets, conf.Application.Environment, conf.AWS.Region, conf.EpifiDb, conf.FeatureEngineeringDb)
	if err != nil {
		return nil, fmt.Errorf("failed to load secrets %w", err)
	}

	if val, ok := os.LookupEnv("REDIS_HOST"); ok {
		conf.RedisOptions.Addr = val
	}
	return conf, nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

// TODO(kunal): Add validate tags later and verify config struct post unmarshalling
// Constraints with respect to dynamic config generate tool:
// 1. Struct names have to start with uppercase letters
// 2. Struct variables need to be pointers

//go:generate conf_gen github.com/epifi/gamma/user/config Config
type Config struct {
	Application                                 *Application
	EpifiDb                                     *cfg.DB
	FeatureEngineeringDb                        *cfg.DB
	AWS                                         *Aws
	RedisOptions                                *cfg.RedisOptions
	QuestSdk                                    *sdkconfig.Config `dynamic:"true"`
	OnboardingStageEventPublisher               *cfg.SnsPublisher
	OnboardingUserUpdatePublisher               *cfg.SnsPublisher
	OnboardingUserUpdateVKYCSubscriber          *cfg.SqsSubscriber `dynamic:"true"`
	Consent                                     *Consent
	Referral                                    *ReferralConfig
	Onboarding                                  *OnboardingConfig                   `dynamic:"true" ,quest:"component,area:Onboarding"`
	FeatureReleaseConfig                        *releaseConfig.FeatureReleaseConfig `dynamic:"true"`
	Flags                                       *Flags                              `dynamic:"true"`
	NewOnFi                                     *NewOnFiConfig
	RudderStack                                 *cfg.RudderStackBroker
	Secrets                                     *cfg.Secrets
	ShippingAddressUpdatePublisher              *cfg.SqsPublisher
	ShippingAddressUpdateSubscriber             *cfg.SqsSubscriber `dynamic:"true"`
	ShippingAddressUpdateCallbackSubscriber     *cfg.SqsSubscriber `dynamic:"true"`
	VKYC                                        *VKYC              `dynamic:"true"`
	VKYCUpdateSubscriber                        *cfg.SqsSubscriber `dynamic:"true"`
	EKYCSuccessSubscriber                       *cfg.SqsSubscriber `dynamic:"true"`
	ConsentEventPublisher                       *cfg.SnsPublisher
	UserAccessRevokeUpdatePublisher             *cfg.SnsPublisher
	ShippingAddressUpdateEventPublisher         *cfg.SnsPublisher
	CSIS                                        *CSIS
	CreditReportPresencePublisher               *cfg.SqsPublisher
	CreditReportPresenceSubscriber              *cfg.SqsSubscriber `dynamic:"true"`
	CreditReportVerificationPublisher           *cfg.SqsPublisher
	CreditReportVerificationSubscriber          *cfg.SqsSubscriber `dynamic:"true"`
	AfPurchasePublisher                         *cfg.SqsPublisher
	EventsAfPurchaseSubscriber                  *cfg.SqsSubscriber `dynamic:"true"`
	EventsCompletedTnCPublisher                 *cfg.SqsPublisher
	EventsCompletedTnCSubscriber                *cfg.SqsSubscriber `dynamic:"true"`
	Screening                                   *Screening
	ExperianDataStorageLimitInHrs               int
	UserUpdateEventSubscriber                   *cfg.SqsSubscriber      `dynamic:"true"`
	BankCustomerUpdateEventSubscriber           *cfg.SqsSubscriber      `dynamic:"true"`
	CreditReportVerificationEventSubscriber     *cfg.SqsSubscriber      `dynamic:"true"`
	LivManualReviewEventSubscriber              *cfg.SqsSubscriber      `dynamic:"true"`
	UserCacheConfig                             *UserCacheConfig        `dynamic:"true"`
	UserGroupCacheConfig                        *UserGroupCacheConfig   `dynamic:"true"`
	MinimalUserCacheConfig                      *MinimalUserCacheConfig `dynamic:"true"`
	Events                                      *EventsConfig
	VpaMigrationConsentPublisher                *cfg.SqsPublisher
	CreditReportConfig                          *CreditReportConfig `dynamic:"true"`
	CreditReportDerivedAttributesPublisher      *cfg.SqsPublisher
	CreditReportDerivedAttributesSubscriber     *cfg.SqsSubscriber `dynamic:"true"`
	ProcessOnboardingEventUserContactSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	ProcessAfuEventUserContactSubscriber        *cfg.SqsSubscriber `dynamic:"true"`
	ProcessDeleteUserSubscriber                 *cfg.SqsSubscriber `dynamic:"true"`
	UserDevicePropertiesUpdatePublisher         *cfg.SnsPublisher
	H3RankingFile                               string                           // Filepath of CSV shared by DS team which contains the mapping of H3 cells to corresponding influence and risk scores
	RecordHashedContactLockTimeout              time.Duration                    `dynamic:"true"`
	DedupeCacheExpiry                           time.Duration                    `dynamic:"true"`
	AccessRevokeCooldownDuration                map[string]time.Duration         `dynamic:"true"`
	SyncOnboardingSubscriber                    *cfg.SqsSubscriber               `dynamic:"true"`
	ProcessCardCreationEvent                    *cfg.SqsSubscriber               `dynamic:"true"`
	VKYCCallCompletedEventSubscriber            *cfg.SqsSubscriber               `dynamic:"true"`
	InHouseVkycCallCompletedEventSubscriber     *cfg.SqsSubscriber               `dynamic:"true"`
	ProcessSavingsAccountUpdateEvent            *cfg.SqsSubscriber               `dynamic:"true"`
	UserDevicePropertiesCacheConfig             *UserDevicePropertiesCacheConfig `dynamic:"true"`
	ProcessAccessRevokeCooldownPublisher        *cfg.SqsPublisher
	ProcessAccessRevokeCooldownSubscriber       *cfg.SqsSubscriber `dynamic:"true"`
	DeleteUserPublisher                         *cfg.SnsPublisher
	// WhitelistNumberHashesForUAENR is used to whitelist numbers with indian country code for testing of UAE NR Onboarding
	// Generate hash required to be added in config via auth -> pii_token DB state
	// Use entire number when computing hash using DB state : country code + national number , eg: ************
	// If a number is present in both WhitelistNumberHashesForUAENR and WhitelistNumberHashesForQatarNR, priority will be given to UAE NR onboarding
	WhitelistNumberHashesForUAENR []string `dynamic:"true"`
	// WhitelistNumberHashesForQatarNR is used to whitelist numbers with indian country code for testing of Qatar NR Onboarding
	// todo(saiteja): check for an easy way to handle this without adding dynamic element
	WhitelistNumberHashesForQatarNR []string `dynamic:"true"`
}

type CrossValidationConfig struct {
	// Disclaimer: do not populate in params, individual map elements which are present in params but not in other envs are added to the other envs map
	// which might lead to unexpected behaviour
	// OnbFeature:Flow -> CrossValidationCheck -> List of data sources
	// For example, FEATURE_NON_RESIDENT_SA_QATAR:FLOW_FORM_60 is the key to be used to get cross validation data sources for NR Qatar onboarding using form 60
	// Flow is defined in user/onboarding/stageproc/datavalidator/flow.go
	CrossValidationDataSources map[string]map[string][]string
	FmPollingRetryConfig       *cfg.RegularInterval
	NameMatchScoreThreshold    float32
	DummyDynamicFlag           bool `dynamic:"true"`
}

type UserDevicePropertiesCacheConfig struct {
	IsCacheEnabled bool          `dynamic:"true"`
	CacheTTL       time.Duration `dynamic:"true"`
}

type Application struct {
	Environment   string
	Name          string
	IsSecureRedis bool
}

type Consent struct {
	Versions *Versions
	Urls     *Urls
}

type Versions struct {
	FiTnc              int
	FedTnC             int
	FiPrivacyPolicy    int
	FiWealthTnc        int
	FiP2pInvestmentTnc int
	VpaMigration       int
	SecureUsageTnC     int
}

type Urls struct {
	FiTnc              string
	FiTncNonResident   string
	FedTnC             string
	FiPrivacyPolicy    string
	FiWealthTnc        string
	FiP2pInvestmentTnc string
	VpaMigration       string
}

type Aws struct {
	Region string
	S3     *S3
}

type S3 struct {
	UsersBucketName         string `iam:"s3-readwrite"`
	CreditReportsBucketName string `iam:"s3-readwrite"`
}

type ReferralConfig struct {
	ReferralLimits map[string]int64
	ReferralExpiry map[string]time.Duration
}

type OnboardingFlags struct {
	// This is required to see all available details in NR ops
	IgnoreErrorsInGetDataForCrossValidationManualReview bool `dynamic:"true"`
	// Flag to control whether the new liveness flow should be used, which includes the CameraX library migration and the removal of hardcoded polling logic.
	// NextAction is mandatory in CheckLivenessScreenOptions in the new flow
	// Applicable only for android
	UseNewLivenessFlow *app.FeatureConfig `dynamic:"true"`

	// list of option for minimum balance requirement for add money flow
	// minimum balance is user specific
	// option to be used -> (account number % total enabled options available)
	// TODO(Raunak) this list is replicated in the frontend service. Refactor to have the values in a common place
	AddMoneyBalanceOptions []*AddMoneyBalanceOption

	// Enable async processing of wip onboarding users
	EnableSyncOnboarding bool

	// Enable secure usage guidlines
	EnableSecureUsageGuidelinesConsent bool `dynamic:"true"`

	// If true, sensitve user's credit report data received from vendor is not stored in db
	MangleRawCreditReportData bool

	// Switch for onboarding stages. Stage processors of the stages present in this list
	// will not be called by the orchestrator.
	DisabledStages map[string]bool `dynamic:"true"`

	// Threshold to be used for PAN-KYC name matching
	InhouseNamematchThreshold float32

	// Threshold below which PAN-KYC name match is considered as failure
	InhouseNamematchFailureThreshold float32

	// This flag is used to control if we want to allow users in manual review till customer creation
	// and then show manual review screen
	AllowManualReviewUsers bool `dynamic:"true"`

	EnableAffluenceV2 bool `dynamic:"true"`

	// flag to toggle savings account introduction screen.
	// it also toggles between showing users minimal consents screen (TNC stage)
	EnableSavingsIntroScreen *app.FeatureConfig `dynamic:"true"`

	BlockCCUserForPANLinkage bool `dynamic:"true"`

	MarkCKYCSuccessWithoutCKYCDownload bool `dynamic:"true"`

	EnableUpdateProfileDetailsStage bool `dynamic:"true"`

	EnableGNOAOnError bool `dynamic:"true"`

	EnableNonResidentOnboardingCrossValidation bool `dynamic:"true"`

	// SkipLocationCheckForNROnboarding is used to skip location checks for NR onboarding in non-prod environments
	SkipLocationCheckForNROnboarding bool `dynamic:"true"`

	// SkipCountryIdVerification is used to skip country id verification for NR Onboarding only for qa and staging environments
	SkipCountryIdVerification bool `dynamic:"true"`

	// SkipPassportVerification is used to skip passport verification for NR Onboarding
	SkipPassportVerification bool `dynamic:"true"`

	// EnableRiskCheckForNRUser is used to perform risk check on NR users
	EnableRiskCheckForNRUser bool `dynamic:"true"`

	// EnableRiskScreeningForD2H indicates if risk screening is enabled for Direct to home users
	EnableRiskScreeningForD2H bool `dynamic:"true"`

	// EnableSMSParserConsentScreen is used to enable the SMS parser consent stage
	EnableSMSParserConsentScreen *app.FeatureConfig `dynamic:"true"`

	// WealthAnalyserFeature is used to enable Wealth Analyser feature for user
	WealthAnalyserFeature *app.FeatureConfig `dynamic:"true"`

	// EnableContactPermissionInOnb controls the contacts permission asked from user during onboarding
	EnableContactPermissionInOnb *app.FeatureConfig `dynamic:"true"`

	// EnableParentNamePrefillFromCKYC is used to enable prefilling of parent name from CKYC data on mother father screen
	EnableParentNamePrefillFromCKYC bool `dynamic:"true"`

	// EnableGlobalIssuedPassportVerification is used to enable the verification of global issued passport with the help
	// of old passport file number
	EnableGlobalIssuedPassportVerification *app.FeatureConfig `dynamic:"true"`

	EnableCkyc bool `dynamic:"true"`

	AllowNRIDedupeUsers bool `dynamic:"true"`

	// EnablePassportVerification is used to enable the verification of global issued passport with the help ARN
	EnableGlobalIssuedPassportARNFlow bool `dynamic:"true"`

	// BlockNrOnboarding is used to block NR onboarding
	BlockNrOnboarding bool `dynamic:"true"`

	// Feature config for enabling new separate stage to get client permission(location, sms, etc..) in onboarding flow
	EnablePermissionStage *app.FeatureConfig `dynamic:"true"`

	// Move PANAadhaar validation to pre_customer_check stage
	EnablePanAadharCheckInPreCustomerCreationCheckStage bool               `dynamic:"true"`
	EnableSaDeclarationstage                            *app.FeatureConfig `dynamic:"true"`
}

type AddMoneyBalanceOption struct {
	// minimum balance required for completion of add money flow
	Amount *moneyPb.Money
	// denotes  if this option is enabled or not
	IsEnabled bool
}

type OnboardingConfig struct {
	Flags *OnboardingFlags `dynamic:"true"`

	ABFeatureReleaseConfig *releaseConfig.ABFeatureReleaseConfig `dynamic:"true"`

	// publishers
	SyncOnboardingSqsPublisher *cfg.SqsPublisher

	OnbDetailsCacheConfig         *OnbDetailsCacheConfig         `dynamic:"true"`
	NextActionDecisionCacheConfig *NextActionDecisionCacheConfig `dynamic:"true"`

	// subscribers
	StuckUserNudges           StuckUserNudges
	CCFiliteStuckUserNudges   StuckUserNudges
	StuckUserAlerts           StuckUserAlerts
	UNNameCheckMailingAddress *UNNameCheckMailingAddress
	AWS                       *Aws
	StageManualPassNotif      StageManualPassNotif
	// Block onboarding time range
	// time format "2006-01-02 15:04:05.000" (time zone IST)
	BlockOnboardingFromTime string `dynamic:"true"`
	BlockOnboardingTillTime string `dynamic:"true"`
	BlockOnboardingMsg      string `dynamic:"true"`
	KYCDedupeRetryCount     int32
	// for app version >= KYCNameUpdateNewSubTitleMinAndroid, new subtitle text will be shown on name retry screen
	KYCNameUpdateNewSubTitleMinAndroid int

	SyncOnboardingInterval time.Duration

	// Onboarding attempts older than this duration will stop syncing
	SyncOnboardingCutOff time.Duration

	// denotes action to be taken when cb verification fails due to time out
	ActionOnReportVerificationTimeout string

	// denotes action to be taken when cb report not found to download
	ActionOnReportNotFoundToDownload string

	// map between the app platform (ios/ andriod) to add funds restriction
	AddFundsRestrictionMap map[string]*AddFundsRestriction

	AppScreeningConfig *AppScreeningConfig `dynamic:"true"`

	DedupeAPICacheTimeout time.Duration
	// Duration (in hours) after which add money stage can be skipped for user
	DurationToSkipAddMoney time.Duration `dynamic:"true" ,quest:"variable"`
	// Onboarding stage to health config mapping
	HealthConfig map[string]*ServiceHealth `dynamic:"true"`

	// Maximum time in account creation stages after which we want to show the
	// user error screen instead of the loading bar
	AccountSetupMaxStuckDuration time.Duration

	OnboardingVelocityConfig *OnboardingVelocityConfig `dynamic:"true"`

	// offer-codes to be shown to the user as alternatives to finite-codes
	ReferralOfferCodesDuringOnboarding map[string]*ReferralOfferCodeDuringOnboarding `dynamic:"true"`
	ReferralOfferCodesABReleaseConfig  *releaseConfig.ABFeatureReleaseConfig         `dynamic:"true"`
	// offer widget to be shown on onboarding screens via finite-code, map[finite-code]*offer-widget-conf
	ReferralOfferWidgetsDuringOnboarding              map[string]*ReferralOfferWidgetDuringOnboarding `dynamic:"true"`
	OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig *releaseConfig.ABFeatureReleaseConfig           `dynamic:"true"`

	// key to be used for accessing finite-code from attribution link params for autofill of finite-code
	FiniteCodeFromAttributionParamsKey string `dynamic:"true"`

	// map to store durations post which add-funds should be skipped for affluence classes
	// For e.g. map['ENTRY_1']*{class, duration}
	DurationToSkipAddFundsForAffluenceClasses map[string]*DurationToSkipAddFundsForAffluenceClass `dynamic:"true"`

	// bool to simulate the failure of balance check API for onboarding add-funds stage
	SimulateBalanceCheckFailureForAddFunds bool `dynamic:"true"`

	// check for amount via order during onb-add-funds
	TotalAmountViaOrderAddFunds bool `dynamic:"true"`

	// min android version support user-action basis balance refresh for onb-add-funds
	MinAndroidVersionForManualBalanceRefreshOnbAddFunds int `dynamic:"true"`
	// min ios version support user-action basis balance refresh for onb-add-funds
	MinIosVersionForManualBalanceRefreshOnbAddFunds int `dynamic:"true"`

	// web-urls for salary-b2b flow users
	// For e.g. map['FLOW_1']*{Url: 'https://fi.money/signup'}
	WebUrlsForSalaryB2BFlows map[string]*WebUrlForSalaryB2BFlow `dynamic:"true"`

	// Minimum version required for cert upgrade to take place
	EKYCCertUpgradeFeatureConfig *app.FeatureConfig `dynamic:"true"`

	// Config to control forced add money stage for MIN KYC users
	MinKycMandatoryAddFundConfig *MandatoryMinKycAddFundConfig `dynamic:"true"`

	// Feature config for enabling edit employment in screener choice screen
	EditEmploymentInScreener *app.FeatureConfig `dynamic:"true"`

	RiskScreeningExpiry time.Duration `dynamic:"true"`

	OrchestratorLockConfig *OrchestratorLockConfig

	// PhysicalCardChargesMailingAddressScreenParams for CARD_MAILING_ADDRESS screen options
	PhysicalCardChargesMailingAddressScreenParams *CardMailingAddressParams

	// SkipAddFundsUserGroups - Add funds will be skipped for this list of user groups
	SkipAddFundsUserGroups []commontypes.UserGroup

	LivenessSummaryExpiryDuration time.Duration `dynamic:"true"`

	// Affluence classes which are eligible for bonus transition screen after app screening
	AffluenceClassesEligibleForBonusTransitionScreen map[string]bool `dynamic:"true"`

	// add-funds stage associated config
	AddFundsConfig *OnbAddFundsConfig `dynamic:"true" ,quest:"component"`

	ResetOnboardingJourney *ResetOnboardingJourneyConfig `dynamic:"true"`

	// BlockOnboardingDueToUnlinkedPANAndAadhaar states that the onboarding process is blocked because the PAN and Aadhaar are not linked
	BlockOnboardingDueToUnlinkedPANAndAadhaar bool `dynamic:"true"`

	FiLiteRolloutPercentage                int  `dynamic:"true"` // The base of this percentage is 1000, so for full rollout please make this value to 1000
	IsPanDOBDropOffToWealthAnalyserEnabled bool `dynamic:"true"`

	SecureUsageGuidelineVersion         int32         `dynamic:"true"`
	SecureUsageGuidelineConsentInterval time.Duration `dynamic:"true"`

	ConfirmCardMailingAddress *ConfirmCardMailingAddressConfig `dynamic:"true"`

	IntentSelectionConfigV2 *IntentSelectionConfigV2 `dynamic:"true"`

	SoftIntentSelectionConfig *SoftIntentSelectionConfig `dynamic:"true" ,quest:"component"`

	DirectToFiLite *DirectToFiLiteConfig `dynamic:"true" ,quest:"component"`

	// Feature config for the new PAN_VERIFICATION deeplink
	PanValidateV3FeatureConfig *app.FeatureConfig `dynamic:"true"`

	ShowNewConsentInPanValidateV2 *app.FeatureConfig `dynamic:"true"`

	NonResidentCrossValidationConfig *CrossValidationConfig `dynamic:"true"`

	PassportVerificationConfig *PassportVerificationConfig `dynamic:"true"`

	NrBucketName string `iam:"s3-readwrite"`

	// EnableTriggerNROAccountCreation if enabled we trigger the nro account creation if user has given the consent in savings intro screen
	EnableTriggerNROAccountCreation *app.FeatureConfig `dynamic:"true"`

	// PrefillParentNameFromPassportOCR is used to prefill the parent name from passport OCR data on mother father screen
	PrefillParentNameFromPassportOCR *app.FeatureConfig `dynamic:"true"`

	// order physical card stage config
	OrderPhysicalDebitCardConfig *OrderPhysicalDebitCardConfig `dynamic:"true" ,quest:"component"`

	UqudoCountryIdVerificationConfig *UqudoCountryIdVerificationConfig `dynamic:"true"`

	ActorsWhitelistedForPreFunding []string `dynamic:"true"`

	WalkthroughScreenConfig *WalkthroughScreenConfig `dynamic:"true"`

	ParentNamePrefillFromCKYCRolloutPercentage int `dynamic:"true"`
}

type UqudoCountryIdVerificationConfig struct {
	EnableTamperCheck        bool `dynamic:"true"`
	ScreenDetectionThreshold int  `dynamic:"true"` // Range : 0-100, higher the value higher the tampering
	PrintDetectionThreshold  int  `dynamic:"true"` // Range : 0-100, higher the value higher the tampering
	PhotoTamperingThreshold  int  `dynamic:"true"` // Range : 0-100, higher the value higher the tampering
	// Block users whose id expire within the threshold
	IdExpiryThreshold  time.Duration `dynamic:"true"`
	FaceMatchThreshold int32         `dynamic:"true"`
}

type OrderPhysicalDebitCardConfig struct {
	EnableViaQuest bool `dynamic:"true" ,quest:"variable"`
	// config for providing free physical card to referee as reward
	FreePhysicalDCRefereeRewardConfig *FreePhysicalDCRefereeRewardConfig `dynamic:"true"`
}

type FreePhysicalDCRefereeRewardConfig struct {
	IsEnabled bool `dynamic:"true"`
	// construct active from (inclusive) and active till (exclusive) time
	ActiveFrom time.Time `dynamic:"true"`
	ActiveTill time.Time `dynamic:"true"`
	// quest experiment variable name on which the free dc experiment is running
	// free dc is applicable only for user's referrer who are part of a quest experiment
	QuestExpVariablePath string `dynamic:"true"`
	// free dc applicable only if the evaluated value of QuestExpVariablePath for user's referrer is QuestExpVariableValue
	QuestExpVariableValue string `dynamic:"true"`
}

type PassportVerificationConfig struct {
	// Behaviour if the vendor api fails. If true, passport verification stage is marked successful for
	// the user on API failure or invalid response.
	IgnoreVendorError bool `dynamic:"true"`

	// Block users whose passports expire within the threshold
	PassportExpiryThreshold time.Duration `dynamic:"true"`

	// flag to control turning off/on passport confirmation screen
	DetailsConfirmationFeatureConfig *app.FeatureConfig `dynamic:"true"`
}

type DirectToFiLiteConfig struct {
	// Enable direct to fi lite
	Enable bool `dynamic:"true" ,quest:"variable"`
	// Direct to fi lite variant
	Variant string `dynamic:"true" ,quest:"variable"`
}

type WalkthroughScreenConfig struct {
	// Feature config for enabling walkthrough screen
	FeatureConfig *app.FeatureConfig `dynamic:"true"`
}

type SoftIntentSelectionConfig struct {
	SoftIntentCollectionScreenFeatureConfig     *app.FeatureConfig `dynamic:"true"`
	SoftIntentCollectionScreenPercentageRollout int                `dynamic:"true"`
	// contains a list of allowed soft intents in form of a map for quick lookups
	AllowedSoftIntents map[string]bool `dynamic:"true"`
	// config to control the behaviour of the post onboarding soft intent screen
	PostOnboardingSoftIntentScreenConfig          *PostOnboardingSoftIntentScreenConfig          `dynamic:"true" ,quest:"component"`
	PreOnboardingCompletionSoftIntentScreenConfig *PreOnboardingCompletionSoftIntentScreenConfig `dynamic:"true" ,quest:"component"`
}

type PostOnboardingSoftIntentScreenConfig struct {
	// bool to enable or disable post onboarding soft intent screen
	Enabled bool `dynamic:"true" ,quest:"variable"`
	// time duration post onboarding completion after which we show the soft intent screen
	WaitDuration time.Duration `dynamic:"true" ,quest:"variable"`
	// if wait duration for a user crosses this value, we don't show them the soft intent screen
	MaxWaitDuration time.Duration `dynamic:"true"`
}

type PreOnboardingCompletionSoftIntentScreenConfig struct {
	// bool to enable or disable soft intent screen just before onboarding completion
	Enabled bool `dynamic:"true" ,quest:"variable"`
}

type IntentSelectionConfigV2 struct {
	IntentCollectionScreenFeatureConfig     *app.FeatureConfig       `dynamic:"true"`
	IntentCollectionScreenPercentageRollout int                      `dynamic:"true"`
	IntentConfigMap                         map[string]*IntentConfig `dynamic:"true"`
	// Referral finite code to acquisition intent map for auto selection of intent for the user
	// ex: on using WEALTHGROW referral code user will be onboarded with ACQUISITION_INTENT_WEALTH_ANALYSER intent automatically
	FiniteCodeToAcquisitionIntentMap map[string]userPb.AcquisitionIntent
	// List of agencies for which intent should be set to Personal Loan
	PLIntentAgencies             []string
	EnableDefaultIntentSelection bool `dynamic:"true"`
}

type IntentConfig struct {
	FeatureConfig     *app.FeatureConfig `dynamic:"true"`
	RolloutPercentage int                `dynamic:"true"`
	// If the array is empty, then it will be considered that it is rolled out to external
	AllowedUserGroups []string `dynamic:"true"`
}

type LocationAreaLatLongInfo struct {
	// bool to toggle the behaviour
	IsEnabled bool `dynamic:"true"`
	// description / usage purpose
	Purpose string `dynamic:"true"`

	// latitude in consideration (using string for the time being as dynamic config doesn't support float64)
	Latitude string `dynamic:"true"`
	// longitude in consideration (using string for the time being as dynamic config doesn't support float64)
	Longitude string `dynamic:"true"`
	// radius to consider around the lat-long.
	// unit can be use-case basis, i.e. metre, km etc
	Radius int `dynamic:"true"`
}

// nolint: govet
type AddFundsConfig struct {
	ShowSkipCtaViaQuest bool `dynamic:"true",quest:"variable"`
}

// OnbAddFundsConfig houses all the config related to add-funds stage during onboarding
// Note: existing add-funds related configs are not moved to this. Please add any new config associated
// with add-funds here.
type OnbAddFundsConfig struct {
	ShowSkipCtaViaQuest  bool          `dynamic:"true" ,quest:"variable"`
	SkipDurationViaQuest time.Duration `dynamic:"true" ,quest:"variable"`
	// bool to decide which onboarding add funds page has to be shown.
	// Note: by default this will be set to true. And quest exp will decide whom to show the old.
	ShowV2Page bool `dynamic:"true" ,quest:"variable"`
	// integer to decide which onboarding add funds v2 page has to be shown
	// this variable only decides the minor version, major version still being 2
	// Note: by default this will be set to 0. And quest exp will decide the newer versions.
	V2PageMinorVersion int `dynamic:"true" ,quest:"variable"`
}

type CardMailingAddressParams struct {
	CurrentAddrCheckBoxText   string
	NonAadharAddrCheckBoxText string
	Title                     string
	TitleFontColor            string
	Subtitle                  string
	SubtitleFontColor         string
	PlaceholderForName        string
	PlaceholderForAddress     string
	CtaText                   string
	ImageUrl                  string
	CheckboxTextColor         string
	PlaceholderColor          string
	ContentColor              string
	DividerColor              string
	EditIconColor             string
	CardColor                 string
	BackgroundColor           string
}
type ReferralOfferCodeDuringOnboarding struct {
	// flag to tell whether the offer-code is enabled/applicable or not
	IsEnabled bool `dynamic:"true"`

	// title to be shown before the code is applied, i.e. selected by the user
	BeforeAppliedTitle string `dynamic:"true"`
	// title to be shown after the code is applied
	AfterAppliedTitle string `dynamic:"true"`

	// description of the offer code before the code is applied
	BeforeAppliedDesc string `dynamic:"true"`
	// description of the offer code after the code is applied
	AfterAppliedDesc string `dynamic:"true"`

	// icon to be shown before the code is applied
	BeforeAppliedIconUrl string `dynamic:"true"`
	// icon to be shown after the code is applied
	AfterAppliedIconUrl string `dynamic:"true"`

	// actual code to be used by client for filling the input field
	Code string `dynamic:"true"`
	// finite-code with which we want the user to onboard with.
	// make the ClaimFiniteCode API call with this code if its present.
	UnderlyingFiniteCode string `dynamic:"true"`
}

type ReferralOfferWidgetDuringOnboarding struct {
	// flag to tell whether the offer-widget is enabled/applicable or not
	IsEnabled bool `dynamic:"true"`
	// icon to be shown on the left of the text
	LeftIcon string `dynamic:"true"`
	// offer text
	OfferText string `dynamic:"true"`
	// background color of the widget
	BgColor string `dynamic:"true"`
}

type DurationToSkipAddFundsForAffluenceClass struct {
	// flag to tell whether the config is still applicable or not
	IsEnabled bool `dynamic:"true"`

	// affluence class in consideration
	AffluenceClass userintelPb.AffluenceClass
	// duration post which add-funds should be skipped for the concerned affluence class
	Duration time.Duration `dynamic:"true"`
}

type WebUrlForSalaryB2BFlow struct {
	// flag to tell whether the config is still applicable or not
	IsEnabled bool `dynamic:"true"`

	Url string `dynamic:"true"`
}

type NextActionDecisionCacheConfig struct {
	IsCacheEnabled                bool          `dynamic:"true"`
	CacheTTL                      time.Duration `dynamic:"true"`
	NextActionDecisionCachePrefix string
}
type OnboardingVelocityConfig struct {
	BucketExpiry       time.Duration `dynamic:"true"`
	QueryRangeDuration time.Duration `dynamic:"true"`
	Threshold          int64         `dynamic:"true"`
	BucketPrecision    int           `dynamic:"true"`
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool

	// Flag for enabling comms calls in update user rpc
	EnableUpdateUserComms bool

	// Flag for enabling active products by pan check while updating user profile
	EnableActiveProductsByPANCheck bool `dynamic:"true"`
}

type NewOnFiConfig struct {
	// Maximum number of user's contacts that will be displayed in "New on Fi" section
	Count int
	// A user contact will be part of User's "New on Fi" list if the contact has onboarded within this time window
	TimeWindow *cfg.TimeDuration
}

type VKYC struct {
	Option map[string]*VKYCOption `dynamic:"true"`
	// instruction page skip option timer in seconds
	InstructionPageSkipOptionTime int32 `dynamic:"true"`
	// landing page skip option timer in seconds
	LandingPageSkipOptionTime   int32         `dynamic:"true"`
	EnableDemandManagement      bool          `dynamic:"true"`
	ShowAuditorAcceptedTileTime time.Duration `dynamic:"true"`
	// KYC expiry for VKYC for NR users. If VKYC call is not completed within NRKYCExpiryForVKYC, KYC is considered as expired and user has to redo KYC again irrespective of data purging policy.
	NRKYCExpiryForVKYC                      time.Duration      `dynamic:"true"`
	EnablePassportFaceImageCheckForNRExpiry bool               `dynamic:"true"`
	EnableVKYCFlowV2                        *app.FeatureConfig `dynamic:"true"`
}

type StuckUserNudges map[string][]*UserNudge

// StuckUserAlerts key maps to featureName:StageName, for example, FEATURE_SA:CUSTOMER_CREATION
type StuckUserAlerts map[string][]*UserAlerts

type UserNudge struct {
	// Time after which we will nudge the user
	StuckDuration time.Duration
	// Type of notification to be sent to the user
	// It can be either PUSH notification or SMS.
	NotificationType NotificationType
	// NotificationData contains data required for sending communication to the user.
	NotificationData NotificationData
	// Stores information regarding the time range between which we want to send the notification
	// and also if we want to send notification on given weekdays.
	NotificationTimeRange NotificationTimeRange
}

// NotificationData contains data required for sending communication to the user.
type NotificationData struct {
	PushNotificationData PushNotificationData
	SMSData              SMSData
	WhatsappData         WhatsappData
}

// PushNotificationData contains data required for sending push notification to the user
type PushNotificationData struct {
	// Title to be shown for notification
	Title string
	// Body contains extra information regarding the notification
	Body string
	// Icon url to be shown in notification
	NotificationIconURL string
}

// SMSData contains data for sending particular sms to the user
type SMSData struct {
	// Unique identifier for the template of the message to be sent
	SmsType comms.SmsType
}

// WhatsappData contains data for sending particular whatsapp message to the user
type WhatsappData struct {
	WhatsappType comms.WhatsappType
}

// Stores information regarding the time range between which we want to send the notification
// and also if we want to send notification on given weekdays.
type NotificationTimeRange struct {
	// Time after which notification can be sent
	NotificationStartTime string
	// Time before which notification can be sent
	NotificationEndTime string
	// Days on which notification can be sent, this will be empty if we can send it on all days
	Weekdays []int
}

type UserAlerts struct {
	// Time after which we'll raise alert
	StuckDuration time.Duration
}

type NotificationType string

type UNNameCheckMailingAddress struct {
	FromAddress string
	ToAddress   string
	FromName    string
	ToName      string
}

type CSIS struct {
	IsCsisEnable bool
	StartTime    string
	EndTime      string
}

type Screening struct {
	CreditReportPresenceCheck *CreditReportPresenceCheck
	CreditReportVerification  *CreditReportVerification
}

type CreditReportPresenceCheck struct {
	// denotes max duration allowed for user to be processed in credit report presence check step
	CreditReportPresenceCheckMaxDuration time.Duration
}

type CreditReportVerification struct {
	CreditScoreThreshold int
	// denotes max duration allowed for user to be processed in credit report verification step
	CreditReportVerificationMaxDuration time.Duration
}

type StageManualPassNotif map[string][]*StageNotificationInfo

type StageNotificationInfo struct {
	IsEnabled        bool
	NotificationType NotificationType
	Title            string
	Body             string
	IconUrl          string
	Type             string
}

type SecureLogging struct {
	EnableSecureLog bool
	SecureLogPath   string
	MaxSizeInMBs    int // megabytes
	MaxBackups      int // There will be MaxBackups + 1 total files
}

type AddFundsRestriction struct {
	// if restricted only allowed user groups will go to the add funds screen
	IsRestricted bool
	// List of user groups allowed for add funds, if the flow is restricted
	AllowedUserGrps []commontypes.UserGroup
}

type AppScreeningConfig struct {
	// ForceScreenerCheckTTL is the time after which forced checks are skipped
	ForceScreenerCheckTTL time.Duration `dynamic:"true"`
	// Config for parameters specific to UAN presence check
	UANCheckConfig *UANCheckConfig `dynamic:"true"`
	// EnableChatbotInChoiceScreen is used to enable/disable chatbot entry in screener choice screen
	EnableChatbotInChoiceScreen bool `dynamic:"true"`
	// EnableFiLiteEntryPointInChoiceScreen is used to enable/disable fi lite entry point in screener choice screen
	EnableFiLiteEntryPointInChoiceScreen bool `dynamic:"true"`
}

type UANCheckConfig struct {
	CreditScoreThreshold int32 `dynamic:"true"`
}

type LinkedInNameMatchThreshold struct {
	ProfileName float32
	CompanyName float32
}

type VKYCOption struct {
	// true indicates vkyc orchestrator stage to be blocked until approved
	IsVKYCApprovalBlocking bool `dynamic:"true"`
	// probabilistic percentage enable value
	VkycEnablePercentage int `dynamic:"true"`
	// true denotes vkyc schedule flow is enabled for the option
	EnableVKYCScheduleFlow bool `dynamic:"true"`
	// feature enable flags
	MinAndroidVersion int `dynamic:"true"`
	MinIOSVersion     int `dynamic:"true"`
	// when true user will be shown option to skip vkyc on landing page
	SkipOptionFlag bool `dynamic:"true"`
	// false indicates full kyc user needs to do vkyc
	IgnoreFullKYCUser bool `dynamic:"true"`
	// when true user will get skip call option on instruction page
	InstructionPageSkipOptionFlag bool `dynamic:"true"`
}

type UserCacheConfig struct {
	IsCachingEnabled bool `dynamic:"true"`
	// prefix to be appended to id while creating the cache keys
	UserIdPrefix string
	// duration for which user data should be cached
	CacheTTl time.Duration `dynamic:"true"`
}

type UserGroupCacheConfig struct {
	IsCachingEnabled bool `dynamic:"true"`
	// prefix to be appended to email while creating the cache keys
	UserGroupEmailPrefix string
	// duration for which user data should be cached
	CacheTTl time.Duration
}

type MinimalUserCacheConfig struct {
	IsCachingEnabled bool `dynamic:"true"`
	// duration for which user data should be cached
	CacheTTl time.Duration `dynamic:"true"`
}

type ServiceHealth struct {
	// start/end time for blocking of service in case of scheduled downtimes
	// time format "2006-01-02 15:04:05.000" (time zone IST)
	From string `dynamic:"true"`
	To   string `dynamic:"true"`
	// health of the service, 0 -> totally degraded, 1 -> partially degraded, 2 -> healthy
	HealthStatus int `dynamic:"true"`
	// message to be shown to the user in case of scheduled downtime
	Message string `dynamic:"true"`
}

type WorkEmailConfig struct {
	SendOtpAttempts   int
	VerifyOtpAttempts int
}

type EventsConfig struct {
	AfPurchasePublishDelay   time.Duration
	CompletedTnCPublishDelay time.Duration
}

type CreditReportConfig struct {
	ExperianConsentConfig *ExperianConsentConfig `dynamic:"true"`
	// CreditReportPresenceEnabled enforces credit report presence before collecting consent to verify credit report
	// in screener. If flag is off, we show consent to all the users.
	// This config complements the flag in OnboardingConfig
	CreditReportPresenceEnabled bool `dynamic:"true"`
	// Config for controlling wait time for Credit report download in Wealth Builder
	DownloadWaitConfigForWealthBuilder *DownloadWaitConfigForWealthBuilder `dynamic:"true"`
}

type ExperianConsentConfig struct {
	ConsentExtension time.Duration `dynamic:"true"`
	ConsentExpiry    time.Duration `dynamic:"true"`
}

type MandatoryMinKycAddFundConfig struct {
	IsEnabled     bool `dynamic:"true"`
	MinimumAmount *moneyPb.Money
}

type OrchestratorLockConfig struct {
	// Timeout sets the max duration for which the lock can be acquired by a request.
	// This should be little less than the request timeout for most cases.
	// Default: 25s
	Timeout time.Duration

	// SleepWindow sets the time for which an orchestrator waits before retrying lock acquisition.
	// Default: 1s
	SleepWindow time.Duration
}

type OnbDetailsCacheConfig struct {
	OnbDetailsMinCacheConfig *OnbDetailsMinCacheConfig `dynamic:"true"`
	OnbDetailsTTLConfig      *OnbDetailsTTLConfig      `dynamic:"true"`
	IsCachingEnabled         bool                      `dynamic:"true"`
}

type OnbDetailsTTLConfig struct {
	// TTL to store onboarding details for onboarding completed users
	OnbDoneTTL time.Duration `dynamic:"true"`
	// TTL to store onboarding details for onboarding in progress users
	OnbInProgressTTL time.Duration `dynamic:"true"`
}

type OnbDetailsMinCacheConfig struct {
	// TTL to store onboarding details min for onboarding completed users
	OnbDoneMinTTL time.Duration `dynamic:"true"`
	// TTL to store onboarding details min for onboarding in progress users
	OnbInProgressMinTTL time.Duration `dynamic:"true"`
	ActorToOnbTTL       time.Duration `dynamic:"true"`
}

type ResetOnboardingJourneyConfig struct {
	OnboardingJourneyResetThresholdTime time.Duration `dynamic:"true"`
	DisableForIOS                       bool          `dynamic:"true"`
}

type ConfirmCardMailingAddressConfig struct {
	EnableConfirmCardMailingAddressV2 bool `dynamic:"true"`

	MaxKYCNameLenToSkipConfirmCardMailingAddr int `dynamic:"true"`
}

/*
Config used for controlling for how much time we wish to wait and check for credit report
download which is used for pre-filling PAN in wealth builder onboarding
*/
type DownloadWaitConfigForWealthBuilder struct {
	// No of times we would try to check whether the report is downloaded
	MaxAttemptsForCheckingDownloadStatus int32 `dynamic:"true"`
	// Duration to wait between each attempt
	// Total duration of wait would be 'maxAttempts * sleepDuration'
	SleepDurationBetweenEachAttempt time.Duration `dynamic:"true"`
}
