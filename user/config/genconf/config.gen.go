// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	time "time"

	money "google.golang.org/genproto/googleapis/type/money"

	pkgweb "github.com/epifi/be-common/api/pkg/web"
	questtypes "github.com/epifi/be-common/api/quest/types"
	common "github.com/epifi/be-common/api/typesv2/common"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	roarray "github.com/epifi/be-common/pkg/data_structs/roarray"
	genapp "github.com/epifi/be-common/pkg/frontend/app/genconf"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	genconfig "github.com/epifi/be-common/quest/sdk/config/genconf"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	user "github.com/epifi/gamma/api/user"
	userintel "github.com/epifi/gamma/api/userintel"
	genconfig2 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	config "github.com/epifi/gamma/user/config"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_RecordHashedContactLockTimeout int64
	_DedupeCacheExpiry              int64
	_AccessRevokeCooldownDuration   *syncmap.Map[string, time.Duration]
	// WhitelistNumberHashesForUAENR is used to whitelist numbers with indian country code for testing of UAE NR Onboarding
	// Generate hash required to be added in config via auth -> pii_token DB state
	// Use entire number when computing hash using DB state : country code + national number , eg: ************
	// If a number is present in both WhitelistNumberHashesForUAENR and WhitelistNumberHashesForQatarNR, priority will be given to UAE NR onboarding
	_WhitelistNumberHashesForUAENR      roarray.ROArray[string]
	_WhitelistNumberHashesForUAENRMutex *sync.RWMutex
	// WhitelistNumberHashesForQatarNR is used to whitelist numbers with indian country code for testing of Qatar NR Onboarding
	// todo(saiteja): check for an easy way to handle this without adding dynamic element
	_WhitelistNumberHashesForQatarNR             roarray.ROArray[string]
	_WhitelistNumberHashesForQatarNRMutex        *sync.RWMutex
	_QuestSdk                                    *genconfig.Config
	_OnboardingUserUpdateVKYCSubscriber          *gencfg.SqsSubscriber
	_Onboarding                                  *OnboardingConfig
	_FeatureReleaseConfig                        *genconfig2.FeatureReleaseConfig
	_Flags                                       *Flags
	_ShippingAddressUpdateSubscriber             *gencfg.SqsSubscriber
	_ShippingAddressUpdateCallbackSubscriber     *gencfg.SqsSubscriber
	_VKYC                                        *VKYC
	_VKYCUpdateSubscriber                        *gencfg.SqsSubscriber
	_EKYCSuccessSubscriber                       *gencfg.SqsSubscriber
	_CreditReportPresenceSubscriber              *gencfg.SqsSubscriber
	_CreditReportVerificationSubscriber          *gencfg.SqsSubscriber
	_EventsAfPurchaseSubscriber                  *gencfg.SqsSubscriber
	_EventsCompletedTnCSubscriber                *gencfg.SqsSubscriber
	_UserUpdateEventSubscriber                   *gencfg.SqsSubscriber
	_BankCustomerUpdateEventSubscriber           *gencfg.SqsSubscriber
	_CreditReportVerificationEventSubscriber     *gencfg.SqsSubscriber
	_LivManualReviewEventSubscriber              *gencfg.SqsSubscriber
	_UserCacheConfig                             *UserCacheConfig
	_UserGroupCacheConfig                        *UserGroupCacheConfig
	_MinimalUserCacheConfig                      *MinimalUserCacheConfig
	_CreditReportConfig                          *CreditReportConfig
	_CreditReportDerivedAttributesSubscriber     *gencfg.SqsSubscriber
	_ProcessOnboardingEventUserContactSubscriber *gencfg.SqsSubscriber
	_ProcessAfuEventUserContactSubscriber        *gencfg.SqsSubscriber
	_ProcessDeleteUserSubscriber                 *gencfg.SqsSubscriber
	_SyncOnboardingSubscriber                    *gencfg.SqsSubscriber
	_ProcessCardCreationEvent                    *gencfg.SqsSubscriber
	_VKYCCallCompletedEventSubscriber            *gencfg.SqsSubscriber
	_InHouseVkycCallCompletedEventSubscriber     *gencfg.SqsSubscriber
	_ProcessSavingsAccountUpdateEvent            *gencfg.SqsSubscriber
	_UserDevicePropertiesCacheConfig             *UserDevicePropertiesCacheConfig
	_ProcessAccessRevokeCooldownSubscriber       *gencfg.SqsSubscriber
	_Application                                 *config.Application
	_EpifiDb                                     *cfg.DB
	_FeatureEngineeringDb                        *cfg.DB
	_AWS                                         *config.Aws
	_RedisOptions                                *cfg.RedisOptions
	_OnboardingStageEventPublisher               *cfg.SnsPublisher
	_OnboardingUserUpdatePublisher               *cfg.SnsPublisher
	_Consent                                     *config.Consent
	_Referral                                    *config.ReferralConfig
	_NewOnFi                                     *config.NewOnFiConfig
	_RudderStack                                 *cfg.RudderStackBroker
	_Secrets                                     *cfg.Secrets
	_ShippingAddressUpdatePublisher              *cfg.SqsPublisher
	_ConsentEventPublisher                       *cfg.SnsPublisher
	_UserAccessRevokeUpdatePublisher             *cfg.SnsPublisher
	_ShippingAddressUpdateEventPublisher         *cfg.SnsPublisher
	_CSIS                                        *config.CSIS
	_CreditReportPresencePublisher               *cfg.SqsPublisher
	_CreditReportVerificationPublisher           *cfg.SqsPublisher
	_AfPurchasePublisher                         *cfg.SqsPublisher
	_EventsCompletedTnCPublisher                 *cfg.SqsPublisher
	_Screening                                   *config.Screening
	_ExperianDataStorageLimitInHrs               int
	_Events                                      *config.EventsConfig
	_VpaMigrationConsentPublisher                *cfg.SqsPublisher
	_CreditReportDerivedAttributesPublisher      *cfg.SqsPublisher
	_UserDevicePropertiesUpdatePublisher         *cfg.SnsPublisher
	_H3RankingFile                               string
	_ProcessAccessRevokeCooldownPublisher        *cfg.SqsPublisher
	_DeleteUserPublisher                         *cfg.SnsPublisher
}

func (obj *Config) RecordHashedContactLockTimeout() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._RecordHashedContactLockTimeout))
}
func (obj *Config) DedupeCacheExpiry() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._DedupeCacheExpiry))
}
func (obj *Config) AccessRevokeCooldownDuration() *syncmap.Map[string, time.Duration] {
	return obj._AccessRevokeCooldownDuration
}

// WhitelistNumberHashesForUAENR is used to whitelist numbers with indian country code for testing of UAE NR Onboarding
// Generate hash required to be added in config via auth -> pii_token DB state
// Use entire number when computing hash using DB state : country code + national number , eg: ************
// If a number is present in both WhitelistNumberHashesForUAENR and WhitelistNumberHashesForQatarNR, priority will be given to UAE NR onboarding
func (obj *Config) WhitelistNumberHashesForUAENR() roarray.ROArray[string] {
	obj._WhitelistNumberHashesForUAENRMutex.RLock()
	defer obj._WhitelistNumberHashesForUAENRMutex.RUnlock()
	return obj._WhitelistNumberHashesForUAENR
}

// WhitelistNumberHashesForQatarNR is used to whitelist numbers with indian country code for testing of Qatar NR Onboarding
// todo(saiteja): check for an easy way to handle this without adding dynamic element
func (obj *Config) WhitelistNumberHashesForQatarNR() roarray.ROArray[string] {
	obj._WhitelistNumberHashesForQatarNRMutex.RLock()
	defer obj._WhitelistNumberHashesForQatarNRMutex.RUnlock()
	return obj._WhitelistNumberHashesForQatarNR
}
func (obj *Config) QuestSdk() *genconfig.Config {
	return obj._QuestSdk
}
func (obj *Config) OnboardingUserUpdateVKYCSubscriber() *gencfg.SqsSubscriber {
	return obj._OnboardingUserUpdateVKYCSubscriber
}
func (obj *Config) Onboarding() *OnboardingConfig {
	return obj._Onboarding
}
func (obj *Config) FeatureReleaseConfig() *genconfig2.FeatureReleaseConfig {
	return obj._FeatureReleaseConfig
}
func (obj *Config) Flags() *Flags {
	return obj._Flags
}
func (obj *Config) ShippingAddressUpdateSubscriber() *gencfg.SqsSubscriber {
	return obj._ShippingAddressUpdateSubscriber
}
func (obj *Config) ShippingAddressUpdateCallbackSubscriber() *gencfg.SqsSubscriber {
	return obj._ShippingAddressUpdateCallbackSubscriber
}
func (obj *Config) VKYC() *VKYC {
	return obj._VKYC
}
func (obj *Config) VKYCUpdateSubscriber() *gencfg.SqsSubscriber {
	return obj._VKYCUpdateSubscriber
}
func (obj *Config) EKYCSuccessSubscriber() *gencfg.SqsSubscriber {
	return obj._EKYCSuccessSubscriber
}
func (obj *Config) CreditReportPresenceSubscriber() *gencfg.SqsSubscriber {
	return obj._CreditReportPresenceSubscriber
}
func (obj *Config) CreditReportVerificationSubscriber() *gencfg.SqsSubscriber {
	return obj._CreditReportVerificationSubscriber
}
func (obj *Config) EventsAfPurchaseSubscriber() *gencfg.SqsSubscriber {
	return obj._EventsAfPurchaseSubscriber
}
func (obj *Config) EventsCompletedTnCSubscriber() *gencfg.SqsSubscriber {
	return obj._EventsCompletedTnCSubscriber
}
func (obj *Config) UserUpdateEventSubscriber() *gencfg.SqsSubscriber {
	return obj._UserUpdateEventSubscriber
}
func (obj *Config) BankCustomerUpdateEventSubscriber() *gencfg.SqsSubscriber {
	return obj._BankCustomerUpdateEventSubscriber
}
func (obj *Config) CreditReportVerificationEventSubscriber() *gencfg.SqsSubscriber {
	return obj._CreditReportVerificationEventSubscriber
}
func (obj *Config) LivManualReviewEventSubscriber() *gencfg.SqsSubscriber {
	return obj._LivManualReviewEventSubscriber
}
func (obj *Config) UserCacheConfig() *UserCacheConfig {
	return obj._UserCacheConfig
}
func (obj *Config) UserGroupCacheConfig() *UserGroupCacheConfig {
	return obj._UserGroupCacheConfig
}
func (obj *Config) MinimalUserCacheConfig() *MinimalUserCacheConfig {
	return obj._MinimalUserCacheConfig
}
func (obj *Config) CreditReportConfig() *CreditReportConfig {
	return obj._CreditReportConfig
}
func (obj *Config) CreditReportDerivedAttributesSubscriber() *gencfg.SqsSubscriber {
	return obj._CreditReportDerivedAttributesSubscriber
}
func (obj *Config) ProcessOnboardingEventUserContactSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessOnboardingEventUserContactSubscriber
}
func (obj *Config) ProcessAfuEventUserContactSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessAfuEventUserContactSubscriber
}
func (obj *Config) ProcessDeleteUserSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessDeleteUserSubscriber
}
func (obj *Config) SyncOnboardingSubscriber() *gencfg.SqsSubscriber {
	return obj._SyncOnboardingSubscriber
}
func (obj *Config) ProcessCardCreationEvent() *gencfg.SqsSubscriber {
	return obj._ProcessCardCreationEvent
}
func (obj *Config) VKYCCallCompletedEventSubscriber() *gencfg.SqsSubscriber {
	return obj._VKYCCallCompletedEventSubscriber
}
func (obj *Config) InHouseVkycCallCompletedEventSubscriber() *gencfg.SqsSubscriber {
	return obj._InHouseVkycCallCompletedEventSubscriber
}
func (obj *Config) ProcessSavingsAccountUpdateEvent() *gencfg.SqsSubscriber {
	return obj._ProcessSavingsAccountUpdateEvent
}
func (obj *Config) UserDevicePropertiesCacheConfig() *UserDevicePropertiesCacheConfig {
	return obj._UserDevicePropertiesCacheConfig
}
func (obj *Config) ProcessAccessRevokeCooldownSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessAccessRevokeCooldownSubscriber
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) EpifiDb() *cfg.DB {
	return obj._EpifiDb
}
func (obj *Config) FeatureEngineeringDb() *cfg.DB {
	return obj._FeatureEngineeringDb
}
func (obj *Config) AWS() *config.Aws {
	return obj._AWS
}
func (obj *Config) RedisOptions() *cfg.RedisOptions {
	return obj._RedisOptions
}
func (obj *Config) OnboardingStageEventPublisher() *cfg.SnsPublisher {
	return obj._OnboardingStageEventPublisher
}
func (obj *Config) OnboardingUserUpdatePublisher() *cfg.SnsPublisher {
	return obj._OnboardingUserUpdatePublisher
}
func (obj *Config) Consent() *config.Consent {
	return obj._Consent
}
func (obj *Config) Referral() *config.ReferralConfig {
	return obj._Referral
}
func (obj *Config) NewOnFi() *config.NewOnFiConfig {
	return obj._NewOnFi
}
func (obj *Config) RudderStack() *cfg.RudderStackBroker {
	return obj._RudderStack
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) ShippingAddressUpdatePublisher() *cfg.SqsPublisher {
	return obj._ShippingAddressUpdatePublisher
}
func (obj *Config) ConsentEventPublisher() *cfg.SnsPublisher {
	return obj._ConsentEventPublisher
}
func (obj *Config) UserAccessRevokeUpdatePublisher() *cfg.SnsPublisher {
	return obj._UserAccessRevokeUpdatePublisher
}
func (obj *Config) ShippingAddressUpdateEventPublisher() *cfg.SnsPublisher {
	return obj._ShippingAddressUpdateEventPublisher
}
func (obj *Config) CSIS() *config.CSIS {
	return obj._CSIS
}
func (obj *Config) CreditReportPresencePublisher() *cfg.SqsPublisher {
	return obj._CreditReportPresencePublisher
}
func (obj *Config) CreditReportVerificationPublisher() *cfg.SqsPublisher {
	return obj._CreditReportVerificationPublisher
}
func (obj *Config) AfPurchasePublisher() *cfg.SqsPublisher {
	return obj._AfPurchasePublisher
}
func (obj *Config) EventsCompletedTnCPublisher() *cfg.SqsPublisher {
	return obj._EventsCompletedTnCPublisher
}
func (obj *Config) Screening() *config.Screening {
	return obj._Screening
}
func (obj *Config) ExperianDataStorageLimitInHrs() int {
	return obj._ExperianDataStorageLimitInHrs
}
func (obj *Config) Events() *config.EventsConfig {
	return obj._Events
}
func (obj *Config) VpaMigrationConsentPublisher() *cfg.SqsPublisher {
	return obj._VpaMigrationConsentPublisher
}
func (obj *Config) CreditReportDerivedAttributesPublisher() *cfg.SqsPublisher {
	return obj._CreditReportDerivedAttributesPublisher
}
func (obj *Config) UserDevicePropertiesUpdatePublisher() *cfg.SnsPublisher {
	return obj._UserDevicePropertiesUpdatePublisher
}
func (obj *Config) H3RankingFile() string {
	return obj._H3RankingFile
}
func (obj *Config) ProcessAccessRevokeCooldownPublisher() *cfg.SqsPublisher {
	return obj._ProcessAccessRevokeCooldownPublisher
}
func (obj *Config) DeleteUserPublisher() *cfg.SnsPublisher {
	return obj._DeleteUserPublisher
}

type OnboardingConfig struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	// min android version support user-action basis balance refresh for onb-add-funds
	_MinAndroidVersionForManualBalanceRefreshOnbAddFunds int64
	// min ios version support user-action basis balance refresh for onb-add-funds
	_MinIosVersionForManualBalanceRefreshOnbAddFunds int64
	_FiLiteRolloutPercentage                         int64
	_SecureUsageGuidelineVersion                     int32
	_ParentNamePrefillFromCKYCRolloutPercentage      int64
	// bool to simulate the failure of balance check API for onboarding add-funds stage
	_SimulateBalanceCheckFailureForAddFunds uint32
	// check for amount via order during onb-add-funds
	_TotalAmountViaOrderAddFunds uint32
	// BlockOnboardingDueToUnlinkedPANAndAadhaar states that the onboarding process is blocked because the PAN and Aadhaar are not linked
	_BlockOnboardingDueToUnlinkedPANAndAadhaar uint32
	_IsPanDOBDropOffToWealthAnalyserEnabled    uint32
	// Duration (in hours) after which add money stage can be skipped for user
	_DurationToSkipAddMoney              int64
	_RiskScreeningExpiry                 int64
	_LivenessSummaryExpiryDuration       int64
	_SecureUsageGuidelineConsentInterval int64
	// Onboarding stage to health config mapping
	_HealthConfig *syncmap.Map[string, *ServiceHealth]
	// offer-codes to be shown to the user as alternatives to finite-codes
	_ReferralOfferCodesDuringOnboarding *syncmap.Map[string, *ReferralOfferCodeDuringOnboarding]
	// offer widget to be shown on onboarding screens via finite-code, map[finite-code]*offer-widget-conf
	_ReferralOfferWidgetsDuringOnboarding *syncmap.Map[string, *ReferralOfferWidgetDuringOnboarding]
	// map to store durations post which add-funds should be skipped for affluence classes
	// For e.g. map['ENTRY_1']*{class, duration}
	_DurationToSkipAddFundsForAffluenceClasses *syncmap.Map[string, *DurationToSkipAddFundsForAffluenceClass]
	// web-urls for salary-b2b flow users
	// For e.g. map['FLOW_1']*{Url: 'https://fi.money/signup'}
	_WebUrlsForSalaryB2BFlows *syncmap.Map[string, *WebUrlForSalaryB2BFlow]
	// Affluence classes which are eligible for bonus transition screen after app screening
	_AffluenceClassesEligibleForBonusTransitionScreen *syncmap.Map[string, bool]
	_ActorsWhitelistedForPreFunding                   roarray.ROArray[string]
	_ActorsWhitelistedForPreFundingMutex              *sync.RWMutex
	// Block onboarding time range
	// time format "2006-01-02 15:04:05.000" (time zone IST)
	_BlockOnboardingFromTime      string
	_BlockOnboardingFromTimeMutex *sync.RWMutex
	_BlockOnboardingTillTime      string
	_BlockOnboardingTillTimeMutex *sync.RWMutex
	_BlockOnboardingMsg           string
	_BlockOnboardingMsgMutex      *sync.RWMutex
	// key to be used for accessing finite-code from attribution link params for autofill of finite-code
	_FiniteCodeFromAttributionParamsKey                string
	_FiniteCodeFromAttributionParamsKeyMutex           *sync.RWMutex
	_Flags                                             *OnboardingFlags
	_ABFeatureReleaseConfig                            *genconfig2.ABFeatureReleaseConfig
	_OnbDetailsCacheConfig                             *OnbDetailsCacheConfig
	_NextActionDecisionCacheConfig                     *NextActionDecisionCacheConfig
	_AppScreeningConfig                                *AppScreeningConfig
	_OnboardingVelocityConfig                          *OnboardingVelocityConfig
	_ReferralOfferCodesABReleaseConfig                 *genconfig2.ABFeatureReleaseConfig
	_OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig *genconfig2.ABFeatureReleaseConfig
	_EKYCCertUpgradeFeatureConfig                      *genapp.FeatureConfig
	_MinKycMandatoryAddFundConfig                      *MandatoryMinKycAddFundConfig
	_EditEmploymentInScreener                          *genapp.FeatureConfig
	_AddFundsConfig                                    *OnbAddFundsConfig
	_ResetOnboardingJourney                            *ResetOnboardingJourneyConfig
	_ConfirmCardMailingAddress                         *ConfirmCardMailingAddressConfig
	_IntentSelectionConfigV2                           *IntentSelectionConfigV2
	_SoftIntentSelectionConfig                         *SoftIntentSelectionConfig
	_DirectToFiLite                                    *DirectToFiLiteConfig
	_PanValidateV3FeatureConfig                        *genapp.FeatureConfig
	_ShowNewConsentInPanValidateV2                     *genapp.FeatureConfig
	_NonResidentCrossValidationConfig                  *CrossValidationConfig
	_PassportVerificationConfig                        *PassportVerificationConfig
	_EnableTriggerNROAccountCreation                   *genapp.FeatureConfig
	_PrefillParentNameFromPassportOCR                  *genapp.FeatureConfig
	_OrderPhysicalDebitCardConfig                      *OrderPhysicalDebitCardConfig
	_UqudoCountryIdVerificationConfig                  *UqudoCountryIdVerificationConfig
	_WalkthroughScreenConfig                           *WalkthroughScreenConfig
	_SyncOnboardingSqsPublisher                        *cfg.SqsPublisher
	_StuckUserNudges                                   config.StuckUserNudges
	_CCFiliteStuckUserNudges                           config.StuckUserNudges
	_StuckUserAlerts                                   config.StuckUserAlerts
	_UNNameCheckMailingAddress                         *config.UNNameCheckMailingAddress
	_AWS                                               *config.Aws
	_StageManualPassNotif                              config.StageManualPassNotif
	_KYCDedupeRetryCount                               int32
	_KYCNameUpdateNewSubTitleMinAndroid                int
	_SyncOnboardingInterval                            time.Duration
	_SyncOnboardingCutOff                              time.Duration
	_ActionOnReportVerificationTimeout                 string
	_ActionOnReportNotFoundToDownload                  string
	_AddFundsRestrictionMap                            map[string]*config.AddFundsRestriction
	_DedupeAPICacheTimeout                             time.Duration
	_AccountSetupMaxStuckDuration                      time.Duration
	_OrchestratorLockConfig                            *config.OrchestratorLockConfig
	_PhysicalCardChargesMailingAddressScreenParams     *config.CardMailingAddressParams
	_SkipAddFundsUserGroups                            []common.UserGroup
	_NrBucketName                                      string
}

// min android version support user-action basis balance refresh for onb-add-funds
func (obj *OnboardingConfig) MinAndroidVersionForManualBalanceRefreshOnbAddFunds() int {
	return int(atomic.LoadInt64(&obj._MinAndroidVersionForManualBalanceRefreshOnbAddFunds))
}

// min ios version support user-action basis balance refresh for onb-add-funds
func (obj *OnboardingConfig) MinIosVersionForManualBalanceRefreshOnbAddFunds() int {
	return int(atomic.LoadInt64(&obj._MinIosVersionForManualBalanceRefreshOnbAddFunds))
}
func (obj *OnboardingConfig) FiLiteRolloutPercentage() int {
	return int(atomic.LoadInt64(&obj._FiLiteRolloutPercentage))
}
func (obj *OnboardingConfig) SecureUsageGuidelineVersion() int32 {
	return int32(atomic.LoadInt32(&obj._SecureUsageGuidelineVersion))
}
func (obj *OnboardingConfig) ParentNamePrefillFromCKYCRolloutPercentage() int {
	return int(atomic.LoadInt64(&obj._ParentNamePrefillFromCKYCRolloutPercentage))
}

// bool to simulate the failure of balance check API for onboarding add-funds stage
func (obj *OnboardingConfig) SimulateBalanceCheckFailureForAddFunds() bool {
	if atomic.LoadUint32(&obj._SimulateBalanceCheckFailureForAddFunds) == 0 {
		return false
	} else {
		return true
	}
}

// check for amount via order during onb-add-funds
func (obj *OnboardingConfig) TotalAmountViaOrderAddFunds() bool {
	if atomic.LoadUint32(&obj._TotalAmountViaOrderAddFunds) == 0 {
		return false
	} else {
		return true
	}
}

// BlockOnboardingDueToUnlinkedPANAndAadhaar states that the onboarding process is blocked because the PAN and Aadhaar are not linked
func (obj *OnboardingConfig) BlockOnboardingDueToUnlinkedPANAndAadhaar() bool {
	if atomic.LoadUint32(&obj._BlockOnboardingDueToUnlinkedPANAndAadhaar) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OnboardingConfig) IsPanDOBDropOffToWealthAnalyserEnabled() bool {
	if atomic.LoadUint32(&obj._IsPanDOBDropOffToWealthAnalyserEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// Duration (in hours) after which add money stage can be skipped for user
func (obj *OnboardingConfig) durationToSkipAddMoney() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._DurationToSkipAddMoney))
}

// Duration (in hours) after which add money stage can be skipped for user
func (obj *OnboardingConfig) DurationToSkipAddMoney(ctx context.Context) time.Duration {
	defVal := obj.durationToSkipAddMoney()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "DurationToSkipAddMoney"}, defVal)
	val, ok := res.(time.Duration)
	if ok {
		return val
	}
	return defVal
}

func (obj *OnboardingConfig) RiskScreeningExpiry() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._RiskScreeningExpiry))
}
func (obj *OnboardingConfig) LivenessSummaryExpiryDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._LivenessSummaryExpiryDuration))
}
func (obj *OnboardingConfig) SecureUsageGuidelineConsentInterval() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._SecureUsageGuidelineConsentInterval))
}

// Onboarding stage to health config mapping
func (obj *OnboardingConfig) HealthConfig() *syncmap.Map[string, *ServiceHealth] {
	return obj._HealthConfig
}

// offer-codes to be shown to the user as alternatives to finite-codes
func (obj *OnboardingConfig) ReferralOfferCodesDuringOnboarding() *syncmap.Map[string, *ReferralOfferCodeDuringOnboarding] {
	return obj._ReferralOfferCodesDuringOnboarding
}

// offer widget to be shown on onboarding screens via finite-code, map[finite-code]*offer-widget-conf
func (obj *OnboardingConfig) ReferralOfferWidgetsDuringOnboarding() *syncmap.Map[string, *ReferralOfferWidgetDuringOnboarding] {
	return obj._ReferralOfferWidgetsDuringOnboarding
}

// map to store durations post which add-funds should be skipped for affluence classes
// For e.g. map['ENTRY_1']*{class, duration}
func (obj *OnboardingConfig) DurationToSkipAddFundsForAffluenceClasses() *syncmap.Map[string, *DurationToSkipAddFundsForAffluenceClass] {
	return obj._DurationToSkipAddFundsForAffluenceClasses
}

// web-urls for salary-b2b flow users
// For e.g. map['FLOW_1']*{Url: 'https://fi.money/signup'}
func (obj *OnboardingConfig) WebUrlsForSalaryB2BFlows() *syncmap.Map[string, *WebUrlForSalaryB2BFlow] {
	return obj._WebUrlsForSalaryB2BFlows
}

// Affluence classes which are eligible for bonus transition screen after app screening
func (obj *OnboardingConfig) AffluenceClassesEligibleForBonusTransitionScreen() *syncmap.Map[string, bool] {
	return obj._AffluenceClassesEligibleForBonusTransitionScreen
}
func (obj *OnboardingConfig) ActorsWhitelistedForPreFunding() roarray.ROArray[string] {
	obj._ActorsWhitelistedForPreFundingMutex.RLock()
	defer obj._ActorsWhitelistedForPreFundingMutex.RUnlock()
	return obj._ActorsWhitelistedForPreFunding
}

// Block onboarding time range
// time format "2006-01-02 15:04:05.000" (time zone IST)
func (obj *OnboardingConfig) BlockOnboardingFromTime() string {
	obj._BlockOnboardingFromTimeMutex.RLock()
	defer obj._BlockOnboardingFromTimeMutex.RUnlock()
	return obj._BlockOnboardingFromTime
}
func (obj *OnboardingConfig) BlockOnboardingTillTime() string {
	obj._BlockOnboardingTillTimeMutex.RLock()
	defer obj._BlockOnboardingTillTimeMutex.RUnlock()
	return obj._BlockOnboardingTillTime
}
func (obj *OnboardingConfig) BlockOnboardingMsg() string {
	obj._BlockOnboardingMsgMutex.RLock()
	defer obj._BlockOnboardingMsgMutex.RUnlock()
	return obj._BlockOnboardingMsg
}

// key to be used for accessing finite-code from attribution link params for autofill of finite-code
func (obj *OnboardingConfig) FiniteCodeFromAttributionParamsKey() string {
	obj._FiniteCodeFromAttributionParamsKeyMutex.RLock()
	defer obj._FiniteCodeFromAttributionParamsKeyMutex.RUnlock()
	return obj._FiniteCodeFromAttributionParamsKey
}
func (obj *OnboardingConfig) Flags() *OnboardingFlags {
	return obj._Flags
}
func (obj *OnboardingConfig) ABFeatureReleaseConfig() *genconfig2.ABFeatureReleaseConfig {
	return obj._ABFeatureReleaseConfig
}
func (obj *OnboardingConfig) OnbDetailsCacheConfig() *OnbDetailsCacheConfig {
	return obj._OnbDetailsCacheConfig
}
func (obj *OnboardingConfig) NextActionDecisionCacheConfig() *NextActionDecisionCacheConfig {
	return obj._NextActionDecisionCacheConfig
}
func (obj *OnboardingConfig) AppScreeningConfig() *AppScreeningConfig {
	return obj._AppScreeningConfig
}
func (obj *OnboardingConfig) OnboardingVelocityConfig() *OnboardingVelocityConfig {
	return obj._OnboardingVelocityConfig
}
func (obj *OnboardingConfig) ReferralOfferCodesABReleaseConfig() *genconfig2.ABFeatureReleaseConfig {
	return obj._ReferralOfferCodesABReleaseConfig
}
func (obj *OnboardingConfig) OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig() *genconfig2.ABFeatureReleaseConfig {
	return obj._OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig
}
func (obj *OnboardingConfig) EKYCCertUpgradeFeatureConfig() *genapp.FeatureConfig {
	return obj._EKYCCertUpgradeFeatureConfig
}
func (obj *OnboardingConfig) MinKycMandatoryAddFundConfig() *MandatoryMinKycAddFundConfig {
	return obj._MinKycMandatoryAddFundConfig
}
func (obj *OnboardingConfig) EditEmploymentInScreener() *genapp.FeatureConfig {
	return obj._EditEmploymentInScreener
}
func (obj *OnboardingConfig) AddFundsConfig() *OnbAddFundsConfig {
	return obj._AddFundsConfig
}
func (obj *OnboardingConfig) ResetOnboardingJourney() *ResetOnboardingJourneyConfig {
	return obj._ResetOnboardingJourney
}
func (obj *OnboardingConfig) ConfirmCardMailingAddress() *ConfirmCardMailingAddressConfig {
	return obj._ConfirmCardMailingAddress
}
func (obj *OnboardingConfig) IntentSelectionConfigV2() *IntentSelectionConfigV2 {
	return obj._IntentSelectionConfigV2
}
func (obj *OnboardingConfig) SoftIntentSelectionConfig() *SoftIntentSelectionConfig {
	return obj._SoftIntentSelectionConfig
}
func (obj *OnboardingConfig) DirectToFiLite() *DirectToFiLiteConfig {
	return obj._DirectToFiLite
}
func (obj *OnboardingConfig) PanValidateV3FeatureConfig() *genapp.FeatureConfig {
	return obj._PanValidateV3FeatureConfig
}
func (obj *OnboardingConfig) ShowNewConsentInPanValidateV2() *genapp.FeatureConfig {
	return obj._ShowNewConsentInPanValidateV2
}
func (obj *OnboardingConfig) NonResidentCrossValidationConfig() *CrossValidationConfig {
	return obj._NonResidentCrossValidationConfig
}
func (obj *OnboardingConfig) PassportVerificationConfig() *PassportVerificationConfig {
	return obj._PassportVerificationConfig
}
func (obj *OnboardingConfig) EnableTriggerNROAccountCreation() *genapp.FeatureConfig {
	return obj._EnableTriggerNROAccountCreation
}
func (obj *OnboardingConfig) PrefillParentNameFromPassportOCR() *genapp.FeatureConfig {
	return obj._PrefillParentNameFromPassportOCR
}
func (obj *OnboardingConfig) OrderPhysicalDebitCardConfig() *OrderPhysicalDebitCardConfig {
	return obj._OrderPhysicalDebitCardConfig
}
func (obj *OnboardingConfig) UqudoCountryIdVerificationConfig() *UqudoCountryIdVerificationConfig {
	return obj._UqudoCountryIdVerificationConfig
}
func (obj *OnboardingConfig) WalkthroughScreenConfig() *WalkthroughScreenConfig {
	return obj._WalkthroughScreenConfig
}
func (obj *OnboardingConfig) SyncOnboardingSqsPublisher() *cfg.SqsPublisher {
	return obj._SyncOnboardingSqsPublisher
}
func (obj *OnboardingConfig) StuckUserNudges() config.StuckUserNudges {
	return obj._StuckUserNudges
}
func (obj *OnboardingConfig) CCFiliteStuckUserNudges() config.StuckUserNudges {
	return obj._CCFiliteStuckUserNudges
}
func (obj *OnboardingConfig) StuckUserAlerts() config.StuckUserAlerts {
	return obj._StuckUserAlerts
}
func (obj *OnboardingConfig) UNNameCheckMailingAddress() *config.UNNameCheckMailingAddress {
	return obj._UNNameCheckMailingAddress
}
func (obj *OnboardingConfig) AWS() *config.Aws {
	return obj._AWS
}
func (obj *OnboardingConfig) StageManualPassNotif() config.StageManualPassNotif {
	return obj._StageManualPassNotif
}
func (obj *OnboardingConfig) KYCDedupeRetryCount() int32 {
	return obj._KYCDedupeRetryCount
}
func (obj *OnboardingConfig) KYCNameUpdateNewSubTitleMinAndroid() int {
	return obj._KYCNameUpdateNewSubTitleMinAndroid
}
func (obj *OnboardingConfig) SyncOnboardingInterval() time.Duration {
	return obj._SyncOnboardingInterval
}
func (obj *OnboardingConfig) SyncOnboardingCutOff() time.Duration {
	return obj._SyncOnboardingCutOff
}
func (obj *OnboardingConfig) ActionOnReportVerificationTimeout() string {
	return obj._ActionOnReportVerificationTimeout
}
func (obj *OnboardingConfig) ActionOnReportNotFoundToDownload() string {
	return obj._ActionOnReportNotFoundToDownload
}
func (obj *OnboardingConfig) AddFundsRestrictionMap() map[string]*config.AddFundsRestriction {
	return obj._AddFundsRestrictionMap
}
func (obj *OnboardingConfig) DedupeAPICacheTimeout() time.Duration {
	return obj._DedupeAPICacheTimeout
}
func (obj *OnboardingConfig) AccountSetupMaxStuckDuration() time.Duration {
	return obj._AccountSetupMaxStuckDuration
}
func (obj *OnboardingConfig) OrchestratorLockConfig() *config.OrchestratorLockConfig {
	return obj._OrchestratorLockConfig
}
func (obj *OnboardingConfig) PhysicalCardChargesMailingAddressScreenParams() *config.CardMailingAddressParams {
	return obj._PhysicalCardChargesMailingAddressScreenParams
}
func (obj *OnboardingConfig) SkipAddFundsUserGroups() []common.UserGroup {
	return obj._SkipAddFundsUserGroups
}
func (obj *OnboardingConfig) NrBucketName() string {
	return obj._NrBucketName
}

type OnboardingFlags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// This is required to see all available details in NR ops
	_IgnoreErrorsInGetDataForCrossValidationManualReview uint32
	// Enable secure usage guidlines
	_EnableSecureUsageGuidelinesConsent uint32
	// This flag is used to control if we want to allow users in manual review till customer creation
	// and then show manual review screen
	_AllowManualReviewUsers                     uint32
	_EnableAffluenceV2                          uint32
	_BlockCCUserForPANLinkage                   uint32
	_MarkCKYCSuccessWithoutCKYCDownload         uint32
	_EnableUpdateProfileDetailsStage            uint32
	_EnableGNOAOnError                          uint32
	_EnableNonResidentOnboardingCrossValidation uint32
	// SkipLocationCheckForNROnboarding is used to skip location checks for NR onboarding in non-prod environments
	_SkipLocationCheckForNROnboarding uint32
	// SkipCountryIdVerification is used to skip country id verification for NR Onboarding only for qa and staging environments
	_SkipCountryIdVerification uint32
	// SkipPassportVerification is used to skip passport verification for NR Onboarding
	_SkipPassportVerification uint32
	// EnableRiskCheckForNRUser is used to perform risk check on NR users
	_EnableRiskCheckForNRUser uint32
	// EnableRiskScreeningForD2H indicates if risk screening is enabled for Direct to home users
	_EnableRiskScreeningForD2H uint32
	// EnableParentNamePrefillFromCKYC is used to enable prefilling of parent name from CKYC data on mother father screen
	_EnableParentNamePrefillFromCKYC uint32
	_EnableCkyc                      uint32
	_AllowNRIDedupeUsers             uint32
	// EnablePassportVerification is used to enable the verification of global issued passport with the help ARN
	_EnableGlobalIssuedPassportARNFlow uint32
	// BlockNrOnboarding is used to block NR onboarding
	_BlockNrOnboarding uint32
	// Move PANAadhaar validation to pre_customer_check stage
	_EnablePanAadharCheckInPreCustomerCreationCheckStage uint32
	_EnableSaDeclarationstage                            uint32
	// Switch for onboarding stages. Stage processors of the stages present in this list
	// will not be called by the orchestrator.
	_DisabledStages                         *syncmap.Map[string, bool]
	_UseNewLivenessFlow                     *genapp.FeatureConfig
	_EnableSavingsIntroScreen               *genapp.FeatureConfig
	_EnableSMSParserConsentScreen           *genapp.FeatureConfig
	_WealthAnalyserFeature                  *genapp.FeatureConfig
	_EnableContactPermissionInOnb           *genapp.FeatureConfig
	_EnableGlobalIssuedPassportVerification *genapp.FeatureConfig
	_EnablePermissionStage                  *genapp.FeatureConfig
	_AddMoneyBalanceOptions                 []*config.AddMoneyBalanceOption
	_EnableSyncOnboarding                   bool
	_MangleRawCreditReportData              bool
	_InhouseNamematchThreshold              float32
	_InhouseNamematchFailureThreshold       float32
}

// This is required to see all available details in NR ops
func (obj *OnboardingFlags) IgnoreErrorsInGetDataForCrossValidationManualReview() bool {
	if atomic.LoadUint32(&obj._IgnoreErrorsInGetDataForCrossValidationManualReview) == 0 {
		return false
	} else {
		return true
	}
}

// Enable secure usage guidlines
func (obj *OnboardingFlags) EnableSecureUsageGuidelinesConsent() bool {
	if atomic.LoadUint32(&obj._EnableSecureUsageGuidelinesConsent) == 0 {
		return false
	} else {
		return true
	}
}

// This flag is used to control if we want to allow users in manual review till customer creation
// and then show manual review screen
func (obj *OnboardingFlags) AllowManualReviewUsers() bool {
	if atomic.LoadUint32(&obj._AllowManualReviewUsers) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OnboardingFlags) EnableAffluenceV2() bool {
	if atomic.LoadUint32(&obj._EnableAffluenceV2) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OnboardingFlags) BlockCCUserForPANLinkage() bool {
	if atomic.LoadUint32(&obj._BlockCCUserForPANLinkage) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OnboardingFlags) MarkCKYCSuccessWithoutCKYCDownload() bool {
	if atomic.LoadUint32(&obj._MarkCKYCSuccessWithoutCKYCDownload) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OnboardingFlags) EnableUpdateProfileDetailsStage() bool {
	if atomic.LoadUint32(&obj._EnableUpdateProfileDetailsStage) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OnboardingFlags) EnableGNOAOnError() bool {
	if atomic.LoadUint32(&obj._EnableGNOAOnError) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OnboardingFlags) EnableNonResidentOnboardingCrossValidation() bool {
	if atomic.LoadUint32(&obj._EnableNonResidentOnboardingCrossValidation) == 0 {
		return false
	} else {
		return true
	}
}

// SkipLocationCheckForNROnboarding is used to skip location checks for NR onboarding in non-prod environments
func (obj *OnboardingFlags) SkipLocationCheckForNROnboarding() bool {
	if atomic.LoadUint32(&obj._SkipLocationCheckForNROnboarding) == 0 {
		return false
	} else {
		return true
	}
}

// SkipCountryIdVerification is used to skip country id verification for NR Onboarding only for qa and staging environments
func (obj *OnboardingFlags) SkipCountryIdVerification() bool {
	if atomic.LoadUint32(&obj._SkipCountryIdVerification) == 0 {
		return false
	} else {
		return true
	}
}

// SkipPassportVerification is used to skip passport verification for NR Onboarding
func (obj *OnboardingFlags) SkipPassportVerification() bool {
	if atomic.LoadUint32(&obj._SkipPassportVerification) == 0 {
		return false
	} else {
		return true
	}
}

// EnableRiskCheckForNRUser is used to perform risk check on NR users
func (obj *OnboardingFlags) EnableRiskCheckForNRUser() bool {
	if atomic.LoadUint32(&obj._EnableRiskCheckForNRUser) == 0 {
		return false
	} else {
		return true
	}
}

// EnableRiskScreeningForD2H indicates if risk screening is enabled for Direct to home users
func (obj *OnboardingFlags) EnableRiskScreeningForD2H() bool {
	if atomic.LoadUint32(&obj._EnableRiskScreeningForD2H) == 0 {
		return false
	} else {
		return true
	}
}

// EnableParentNamePrefillFromCKYC is used to enable prefilling of parent name from CKYC data on mother father screen
func (obj *OnboardingFlags) EnableParentNamePrefillFromCKYC() bool {
	if atomic.LoadUint32(&obj._EnableParentNamePrefillFromCKYC) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OnboardingFlags) EnableCkyc() bool {
	if atomic.LoadUint32(&obj._EnableCkyc) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OnboardingFlags) AllowNRIDedupeUsers() bool {
	if atomic.LoadUint32(&obj._AllowNRIDedupeUsers) == 0 {
		return false
	} else {
		return true
	}
}

// EnablePassportVerification is used to enable the verification of global issued passport with the help ARN
func (obj *OnboardingFlags) EnableGlobalIssuedPassportARNFlow() bool {
	if atomic.LoadUint32(&obj._EnableGlobalIssuedPassportARNFlow) == 0 {
		return false
	} else {
		return true
	}
}

// BlockNrOnboarding is used to block NR onboarding
func (obj *OnboardingFlags) BlockNrOnboarding() bool {
	if atomic.LoadUint32(&obj._BlockNrOnboarding) == 0 {
		return false
	} else {
		return true
	}
}

// Move PANAadhaar validation to pre_customer_check stage
func (obj *OnboardingFlags) EnablePanAadharCheckInPreCustomerCreationCheckStage() bool {
	if atomic.LoadUint32(&obj._EnablePanAadharCheckInPreCustomerCreationCheckStage) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OnboardingFlags) EnableSaDeclarationstage() bool {
	if atomic.LoadUint32(&obj._EnableSaDeclarationstage) == 0 {
		return false
	} else {
		return true
	}
}

// Switch for onboarding stages. Stage processors of the stages present in this list
// will not be called by the orchestrator.
func (obj *OnboardingFlags) DisabledStages() *syncmap.Map[string, bool] {
	return obj._DisabledStages
}
func (obj *OnboardingFlags) UseNewLivenessFlow() *genapp.FeatureConfig {
	return obj._UseNewLivenessFlow
}
func (obj *OnboardingFlags) EnableSavingsIntroScreen() *genapp.FeatureConfig {
	return obj._EnableSavingsIntroScreen
}
func (obj *OnboardingFlags) EnableSMSParserConsentScreen() *genapp.FeatureConfig {
	return obj._EnableSMSParserConsentScreen
}
func (obj *OnboardingFlags) WealthAnalyserFeature() *genapp.FeatureConfig {
	return obj._WealthAnalyserFeature
}
func (obj *OnboardingFlags) EnableContactPermissionInOnb() *genapp.FeatureConfig {
	return obj._EnableContactPermissionInOnb
}
func (obj *OnboardingFlags) EnableGlobalIssuedPassportVerification() *genapp.FeatureConfig {
	return obj._EnableGlobalIssuedPassportVerification
}
func (obj *OnboardingFlags) EnablePermissionStage() *genapp.FeatureConfig {
	return obj._EnablePermissionStage
}
func (obj *OnboardingFlags) AddMoneyBalanceOptions() []*config.AddMoneyBalanceOption {
	return obj._AddMoneyBalanceOptions
}
func (obj *OnboardingFlags) EnableSyncOnboarding() bool {
	return obj._EnableSyncOnboarding
}
func (obj *OnboardingFlags) MangleRawCreditReportData() bool {
	return obj._MangleRawCreditReportData
}
func (obj *OnboardingFlags) InhouseNamematchThreshold() float32 {
	return obj._InhouseNamematchThreshold
}
func (obj *OnboardingFlags) InhouseNamematchFailureThreshold() float32 {
	return obj._InhouseNamematchFailureThreshold
}

type OnbDetailsCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCachingEnabled         uint32
	_OnbDetailsMinCacheConfig *OnbDetailsMinCacheConfig
	_OnbDetailsTTLConfig      *OnbDetailsTTLConfig
}

func (obj *OnbDetailsCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OnbDetailsCacheConfig) OnbDetailsMinCacheConfig() *OnbDetailsMinCacheConfig {
	return obj._OnbDetailsMinCacheConfig
}
func (obj *OnbDetailsCacheConfig) OnbDetailsTTLConfig() *OnbDetailsTTLConfig {
	return obj._OnbDetailsTTLConfig
}

type OnbDetailsMinCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// TTL to store onboarding details min for onboarding completed users
	_OnbDoneMinTTL int64
	// TTL to store onboarding details min for onboarding in progress users
	_OnbInProgressMinTTL int64
	_ActorToOnbTTL       int64
}

// TTL to store onboarding details min for onboarding completed users
func (obj *OnbDetailsMinCacheConfig) OnbDoneMinTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._OnbDoneMinTTL))
}

// TTL to store onboarding details min for onboarding in progress users
func (obj *OnbDetailsMinCacheConfig) OnbInProgressMinTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._OnbInProgressMinTTL))
}
func (obj *OnbDetailsMinCacheConfig) ActorToOnbTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ActorToOnbTTL))
}

type OnbDetailsTTLConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// TTL to store onboarding details for onboarding completed users
	_OnbDoneTTL int64
	// TTL to store onboarding details for onboarding in progress users
	_OnbInProgressTTL int64
}

// TTL to store onboarding details for onboarding completed users
func (obj *OnbDetailsTTLConfig) OnbDoneTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._OnbDoneTTL))
}

// TTL to store onboarding details for onboarding in progress users
func (obj *OnbDetailsTTLConfig) OnbInProgressTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._OnbInProgressTTL))
}

type NextActionDecisionCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCacheEnabled                uint32
	_CacheTTL                      int64
	_NextActionDecisionCachePrefix string
}

func (obj *NextActionDecisionCacheConfig) IsCacheEnabled() bool {
	if atomic.LoadUint32(&obj._IsCacheEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *NextActionDecisionCacheConfig) CacheTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CacheTTL))
}
func (obj *NextActionDecisionCacheConfig) NextActionDecisionCachePrefix() string {
	return obj._NextActionDecisionCachePrefix
}

type AppScreeningConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// EnableChatbotInChoiceScreen is used to enable/disable chatbot entry in screener choice screen
	_EnableChatbotInChoiceScreen uint32
	// EnableFiLiteEntryPointInChoiceScreen is used to enable/disable fi lite entry point in screener choice screen
	_EnableFiLiteEntryPointInChoiceScreen uint32
	// ForceScreenerCheckTTL is the time after which forced checks are skipped
	_ForceScreenerCheckTTL int64
	_UANCheckConfig        *UANCheckConfig
}

// EnableChatbotInChoiceScreen is used to enable/disable chatbot entry in screener choice screen
func (obj *AppScreeningConfig) EnableChatbotInChoiceScreen() bool {
	if atomic.LoadUint32(&obj._EnableChatbotInChoiceScreen) == 0 {
		return false
	} else {
		return true
	}
}

// EnableFiLiteEntryPointInChoiceScreen is used to enable/disable fi lite entry point in screener choice screen
func (obj *AppScreeningConfig) EnableFiLiteEntryPointInChoiceScreen() bool {
	if atomic.LoadUint32(&obj._EnableFiLiteEntryPointInChoiceScreen) == 0 {
		return false
	} else {
		return true
	}
}

// ForceScreenerCheckTTL is the time after which forced checks are skipped
func (obj *AppScreeningConfig) ForceScreenerCheckTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ForceScreenerCheckTTL))
}
func (obj *AppScreeningConfig) UANCheckConfig() *UANCheckConfig {
	return obj._UANCheckConfig
}

type UANCheckConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_CreditScoreThreshold int32
}

func (obj *UANCheckConfig) CreditScoreThreshold() int32 {
	return int32(atomic.LoadInt32(&obj._CreditScoreThreshold))
}

type ServiceHealth struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// health of the service, 0 -> totally degraded, 1 -> partially degraded, 2 -> healthy
	_HealthStatus int64
	// start/end time for blocking of service in case of scheduled downtimes
	// time format "2006-01-02 15:04:05.000" (time zone IST)
	_From      string
	_FromMutex *sync.RWMutex
	_To        string
	_ToMutex   *sync.RWMutex
	// message to be shown to the user in case of scheduled downtime
	_Message      string
	_MessageMutex *sync.RWMutex
}

// health of the service, 0 -> totally degraded, 1 -> partially degraded, 2 -> healthy
func (obj *ServiceHealth) HealthStatus() int {
	return int(atomic.LoadInt64(&obj._HealthStatus))
}

// start/end time for blocking of service in case of scheduled downtimes
// time format "2006-01-02 15:04:05.000" (time zone IST)
func (obj *ServiceHealth) From() string {
	obj._FromMutex.RLock()
	defer obj._FromMutex.RUnlock()
	return obj._From
}
func (obj *ServiceHealth) To() string {
	obj._ToMutex.RLock()
	defer obj._ToMutex.RUnlock()
	return obj._To
}

// message to be shown to the user in case of scheduled downtime
func (obj *ServiceHealth) Message() string {
	obj._MessageMutex.RLock()
	defer obj._MessageMutex.RUnlock()
	return obj._Message
}

type OnboardingVelocityConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Threshold          int32
	_BucketPrecision    int64
	_BucketExpiry       int64
	_QueryRangeDuration int64
}

func (obj *OnboardingVelocityConfig) Threshold() int64 {
	return int64(atomic.LoadInt32(&obj._Threshold))
}
func (obj *OnboardingVelocityConfig) BucketPrecision() int {
	return int(atomic.LoadInt64(&obj._BucketPrecision))
}
func (obj *OnboardingVelocityConfig) BucketExpiry() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._BucketExpiry))
}
func (obj *OnboardingVelocityConfig) QueryRangeDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._QueryRangeDuration))
}

type ReferralOfferCodeDuringOnboarding struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// flag to tell whether the offer-code is enabled/applicable or not
	_IsEnabled uint32
	// title to be shown before the code is applied, i.e. selected by the user
	_BeforeAppliedTitle      string
	_BeforeAppliedTitleMutex *sync.RWMutex
	// title to be shown after the code is applied
	_AfterAppliedTitle      string
	_AfterAppliedTitleMutex *sync.RWMutex
	// description of the offer code before the code is applied
	_BeforeAppliedDesc      string
	_BeforeAppliedDescMutex *sync.RWMutex
	// description of the offer code after the code is applied
	_AfterAppliedDesc      string
	_AfterAppliedDescMutex *sync.RWMutex
	// icon to be shown before the code is applied
	_BeforeAppliedIconUrl      string
	_BeforeAppliedIconUrlMutex *sync.RWMutex
	// icon to be shown after the code is applied
	_AfterAppliedIconUrl      string
	_AfterAppliedIconUrlMutex *sync.RWMutex
	// actual code to be used by client for filling the input field
	_Code      string
	_CodeMutex *sync.RWMutex
	// finite-code with which we want the user to onboard with.
	// make the ClaimFiniteCode API call with this code if its present.
	_UnderlyingFiniteCode      string
	_UnderlyingFiniteCodeMutex *sync.RWMutex
}

// flag to tell whether the offer-code is enabled/applicable or not
func (obj *ReferralOfferCodeDuringOnboarding) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// title to be shown before the code is applied, i.e. selected by the user
func (obj *ReferralOfferCodeDuringOnboarding) BeforeAppliedTitle() string {
	obj._BeforeAppliedTitleMutex.RLock()
	defer obj._BeforeAppliedTitleMutex.RUnlock()
	return obj._BeforeAppliedTitle
}

// title to be shown after the code is applied
func (obj *ReferralOfferCodeDuringOnboarding) AfterAppliedTitle() string {
	obj._AfterAppliedTitleMutex.RLock()
	defer obj._AfterAppliedTitleMutex.RUnlock()
	return obj._AfterAppliedTitle
}

// description of the offer code before the code is applied
func (obj *ReferralOfferCodeDuringOnboarding) BeforeAppliedDesc() string {
	obj._BeforeAppliedDescMutex.RLock()
	defer obj._BeforeAppliedDescMutex.RUnlock()
	return obj._BeforeAppliedDesc
}

// description of the offer code after the code is applied
func (obj *ReferralOfferCodeDuringOnboarding) AfterAppliedDesc() string {
	obj._AfterAppliedDescMutex.RLock()
	defer obj._AfterAppliedDescMutex.RUnlock()
	return obj._AfterAppliedDesc
}

// icon to be shown before the code is applied
func (obj *ReferralOfferCodeDuringOnboarding) BeforeAppliedIconUrl() string {
	obj._BeforeAppliedIconUrlMutex.RLock()
	defer obj._BeforeAppliedIconUrlMutex.RUnlock()
	return obj._BeforeAppliedIconUrl
}

// icon to be shown after the code is applied
func (obj *ReferralOfferCodeDuringOnboarding) AfterAppliedIconUrl() string {
	obj._AfterAppliedIconUrlMutex.RLock()
	defer obj._AfterAppliedIconUrlMutex.RUnlock()
	return obj._AfterAppliedIconUrl
}

// actual code to be used by client for filling the input field
func (obj *ReferralOfferCodeDuringOnboarding) Code() string {
	obj._CodeMutex.RLock()
	defer obj._CodeMutex.RUnlock()
	return obj._Code
}

// finite-code with which we want the user to onboard with.
// make the ClaimFiniteCode API call with this code if its present.
func (obj *ReferralOfferCodeDuringOnboarding) UnderlyingFiniteCode() string {
	obj._UnderlyingFiniteCodeMutex.RLock()
	defer obj._UnderlyingFiniteCodeMutex.RUnlock()
	return obj._UnderlyingFiniteCode
}

type ReferralOfferWidgetDuringOnboarding struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// flag to tell whether the offer-widget is enabled/applicable or not
	_IsEnabled uint32
	// icon to be shown on the left of the text
	_LeftIcon      string
	_LeftIconMutex *sync.RWMutex
	// offer text
	_OfferText      string
	_OfferTextMutex *sync.RWMutex
	// background color of the widget
	_BgColor      string
	_BgColorMutex *sync.RWMutex
}

// flag to tell whether the offer-widget is enabled/applicable or not
func (obj *ReferralOfferWidgetDuringOnboarding) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// icon to be shown on the left of the text
func (obj *ReferralOfferWidgetDuringOnboarding) LeftIcon() string {
	obj._LeftIconMutex.RLock()
	defer obj._LeftIconMutex.RUnlock()
	return obj._LeftIcon
}

// offer text
func (obj *ReferralOfferWidgetDuringOnboarding) OfferText() string {
	obj._OfferTextMutex.RLock()
	defer obj._OfferTextMutex.RUnlock()
	return obj._OfferText
}

// background color of the widget
func (obj *ReferralOfferWidgetDuringOnboarding) BgColor() string {
	obj._BgColorMutex.RLock()
	defer obj._BgColorMutex.RUnlock()
	return obj._BgColor
}

type DurationToSkipAddFundsForAffluenceClass struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// flag to tell whether the config is still applicable or not
	_IsEnabled uint32
	// duration post which add-funds should be skipped for the concerned affluence class
	_Duration       int64
	_AffluenceClass userintel.AffluenceClass
}

// flag to tell whether the config is still applicable or not
func (obj *DurationToSkipAddFundsForAffluenceClass) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// duration post which add-funds should be skipped for the concerned affluence class
func (obj *DurationToSkipAddFundsForAffluenceClass) Duration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._Duration))
}
func (obj *DurationToSkipAddFundsForAffluenceClass) AffluenceClass() userintel.AffluenceClass {
	return obj._AffluenceClass
}

type WebUrlForSalaryB2BFlow struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// flag to tell whether the config is still applicable or not
	_IsEnabled uint32
	_Url       string
	_UrlMutex  *sync.RWMutex
}

// flag to tell whether the config is still applicable or not
func (obj *WebUrlForSalaryB2BFlow) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *WebUrlForSalaryB2BFlow) Url() string {
	obj._UrlMutex.RLock()
	defer obj._UrlMutex.RUnlock()
	return obj._Url
}

type MandatoryMinKycAddFundConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled     uint32
	_MinimumAmount *money.Money
}

func (obj *MandatoryMinKycAddFundConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *MandatoryMinKycAddFundConfig) MinimumAmount() *money.Money {
	return obj._MinimumAmount
}

type OnbAddFundsConfig struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	// integer to decide which onboarding add funds v2 page has to be shown
	// this variable only decides the minor version, major version still being 2
	// Note: by default this will be set to 0. And quest exp will decide the newer versions.
	_V2PageMinorVersion  int64
	_ShowSkipCtaViaQuest uint32
	// bool to decide which onboarding add funds page has to be shown.
	// Note: by default this will be set to true. And quest exp will decide whom to show the old.
	_ShowV2Page           uint32
	_SkipDurationViaQuest int64
}

// integer to decide which onboarding add funds v2 page has to be shown
// this variable only decides the minor version, major version still being 2
// Note: by default this will be set to 0. And quest exp will decide the newer versions.
func (obj *OnbAddFundsConfig) v2PageMinorVersion() int {
	return int(atomic.LoadInt64(&obj._V2PageMinorVersion))
}

// integer to decide which onboarding add funds v2 page has to be shown
// this variable only decides the minor version, major version still being 2
// Note: by default this will be set to 0. And quest exp will decide the newer versions.
func (obj *OnbAddFundsConfig) V2PageMinorVersion(ctx context.Context) int {
	defVal := obj.v2PageMinorVersion()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "V2PageMinorVersion"}, defVal)
	val, ok := res.(int64)
	if ok {
		return int(val)
	}
	return defVal
}

func (obj *OnbAddFundsConfig) showSkipCtaViaQuest() bool {
	if atomic.LoadUint32(&obj._ShowSkipCtaViaQuest) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OnbAddFundsConfig) ShowSkipCtaViaQuest(ctx context.Context) bool {
	defVal := obj.showSkipCtaViaQuest()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "ShowSkipCtaViaQuest"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

// bool to decide which onboarding add funds page has to be shown.
// Note: by default this will be set to true. And quest exp will decide whom to show the old.
func (obj *OnbAddFundsConfig) showV2Page() bool {
	if atomic.LoadUint32(&obj._ShowV2Page) == 0 {
		return false
	} else {
		return true
	}
}

// bool to decide which onboarding add funds page has to be shown.
// Note: by default this will be set to true. And quest exp will decide whom to show the old.
func (obj *OnbAddFundsConfig) ShowV2Page(ctx context.Context) bool {
	defVal := obj.showV2Page()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "ShowV2Page"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *OnbAddFundsConfig) skipDurationViaQuest() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._SkipDurationViaQuest))
}
func (obj *OnbAddFundsConfig) SkipDurationViaQuest(ctx context.Context) time.Duration {
	defVal := obj.skipDurationViaQuest()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "SkipDurationViaQuest"}, defVal)
	val, ok := res.(time.Duration)
	if ok {
		return val
	}
	return defVal
}

type ResetOnboardingJourneyConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_DisableForIOS                       uint32
	_OnboardingJourneyResetThresholdTime int64
}

func (obj *ResetOnboardingJourneyConfig) DisableForIOS() bool {
	if atomic.LoadUint32(&obj._DisableForIOS) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *ResetOnboardingJourneyConfig) OnboardingJourneyResetThresholdTime() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._OnboardingJourneyResetThresholdTime))
}

type ConfirmCardMailingAddressConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MaxKYCNameLenToSkipConfirmCardMailingAddr int64
	_EnableConfirmCardMailingAddressV2         uint32
}

func (obj *ConfirmCardMailingAddressConfig) MaxKYCNameLenToSkipConfirmCardMailingAddr() int {
	return int(atomic.LoadInt64(&obj._MaxKYCNameLenToSkipConfirmCardMailingAddr))
}
func (obj *ConfirmCardMailingAddressConfig) EnableConfirmCardMailingAddressV2() bool {
	if atomic.LoadUint32(&obj._EnableConfirmCardMailingAddressV2) == 0 {
		return false
	} else {
		return true
	}
}

type IntentSelectionConfigV2 struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IntentCollectionScreenPercentageRollout int64
	_EnableDefaultIntentSelection            uint32
	_IntentConfigMap                         *syncmap.Map[string, *IntentConfig]
	_IntentCollectionScreenFeatureConfig     *genapp.FeatureConfig
	_FiniteCodeToAcquisitionIntentMap        map[string]user.AcquisitionIntent
	_PLIntentAgencies                        []string
}

func (obj *IntentSelectionConfigV2) IntentCollectionScreenPercentageRollout() int {
	return int(atomic.LoadInt64(&obj._IntentCollectionScreenPercentageRollout))
}
func (obj *IntentSelectionConfigV2) EnableDefaultIntentSelection() bool {
	if atomic.LoadUint32(&obj._EnableDefaultIntentSelection) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *IntentSelectionConfigV2) IntentConfigMap() *syncmap.Map[string, *IntentConfig] {
	return obj._IntentConfigMap
}
func (obj *IntentSelectionConfigV2) IntentCollectionScreenFeatureConfig() *genapp.FeatureConfig {
	return obj._IntentCollectionScreenFeatureConfig
}
func (obj *IntentSelectionConfigV2) FiniteCodeToAcquisitionIntentMap() map[string]user.AcquisitionIntent {
	return obj._FiniteCodeToAcquisitionIntentMap
}
func (obj *IntentSelectionConfigV2) PLIntentAgencies() []string {
	return obj._PLIntentAgencies
}

type IntentConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_RolloutPercentage int64
	// If the array is empty, then it will be considered that it is rolled out to external
	_AllowedUserGroups      roarray.ROArray[string]
	_AllowedUserGroupsMutex *sync.RWMutex
	_FeatureConfig          *genapp.FeatureConfig
}

func (obj *IntentConfig) RolloutPercentage() int {
	return int(atomic.LoadInt64(&obj._RolloutPercentage))
}

// If the array is empty, then it will be considered that it is rolled out to external
func (obj *IntentConfig) AllowedUserGroups() roarray.ROArray[string] {
	obj._AllowedUserGroupsMutex.RLock()
	defer obj._AllowedUserGroupsMutex.RUnlock()
	return obj._AllowedUserGroups
}
func (obj *IntentConfig) FeatureConfig() *genapp.FeatureConfig {
	return obj._FeatureConfig
}

type SoftIntentSelectionConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_SoftIntentCollectionScreenPercentageRollout int64
	// contains a list of allowed soft intents in form of a map for quick lookups
	_AllowedSoftIntents                            *syncmap.Map[string, bool]
	_SoftIntentCollectionScreenFeatureConfig       *genapp.FeatureConfig
	_PostOnboardingSoftIntentScreenConfig          *PostOnboardingSoftIntentScreenConfig
	_PreOnboardingCompletionSoftIntentScreenConfig *PreOnboardingCompletionSoftIntentScreenConfig
}

func (obj *SoftIntentSelectionConfig) SoftIntentCollectionScreenPercentageRollout() int {
	return int(atomic.LoadInt64(&obj._SoftIntentCollectionScreenPercentageRollout))
}

// contains a list of allowed soft intents in form of a map for quick lookups
func (obj *SoftIntentSelectionConfig) AllowedSoftIntents() *syncmap.Map[string, bool] {
	return obj._AllowedSoftIntents
}
func (obj *SoftIntentSelectionConfig) SoftIntentCollectionScreenFeatureConfig() *genapp.FeatureConfig {
	return obj._SoftIntentCollectionScreenFeatureConfig
}
func (obj *SoftIntentSelectionConfig) PostOnboardingSoftIntentScreenConfig() *PostOnboardingSoftIntentScreenConfig {
	return obj._PostOnboardingSoftIntentScreenConfig
}
func (obj *SoftIntentSelectionConfig) PreOnboardingCompletionSoftIntentScreenConfig() *PreOnboardingCompletionSoftIntentScreenConfig {
	return obj._PreOnboardingCompletionSoftIntentScreenConfig
}

type PostOnboardingSoftIntentScreenConfig struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	// bool to enable or disable post onboarding soft intent screen
	_Enabled uint32
	// time duration post onboarding completion after which we show the soft intent screen
	_WaitDuration int64
	// if wait duration for a user crosses this value, we don't show them the soft intent screen
	_MaxWaitDuration int64
}

// bool to enable or disable post onboarding soft intent screen
func (obj *PostOnboardingSoftIntentScreenConfig) enabled() bool {
	if atomic.LoadUint32(&obj._Enabled) == 0 {
		return false
	} else {
		return true
	}
}

// bool to enable or disable post onboarding soft intent screen
func (obj *PostOnboardingSoftIntentScreenConfig) Enabled(ctx context.Context) bool {
	defVal := obj.enabled()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Enabled"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

// time duration post onboarding completion after which we show the soft intent screen
func (obj *PostOnboardingSoftIntentScreenConfig) waitDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._WaitDuration))
}

// time duration post onboarding completion after which we show the soft intent screen
func (obj *PostOnboardingSoftIntentScreenConfig) WaitDuration(ctx context.Context) time.Duration {
	defVal := obj.waitDuration()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "WaitDuration"}, defVal)
	val, ok := res.(time.Duration)
	if ok {
		return val
	}
	return defVal
}

// if wait duration for a user crosses this value, we don't show them the soft intent screen
func (obj *PostOnboardingSoftIntentScreenConfig) MaxWaitDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._MaxWaitDuration))
}

type PreOnboardingCompletionSoftIntentScreenConfig struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	// bool to enable or disable soft intent screen just before onboarding completion
	_Enabled uint32
}

// bool to enable or disable soft intent screen just before onboarding completion
func (obj *PreOnboardingCompletionSoftIntentScreenConfig) enabled() bool {
	if atomic.LoadUint32(&obj._Enabled) == 0 {
		return false
	} else {
		return true
	}
}

// bool to enable or disable soft intent screen just before onboarding completion
func (obj *PreOnboardingCompletionSoftIntentScreenConfig) Enabled(ctx context.Context) bool {
	defVal := obj.enabled()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Enabled"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

type DirectToFiLiteConfig struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	// Enable direct to fi lite
	_Enable uint32
	// Direct to fi lite variant
	_Variant      string
	_VariantMutex *sync.RWMutex
}

// Enable direct to fi lite
func (obj *DirectToFiLiteConfig) enable() bool {
	if atomic.LoadUint32(&obj._Enable) == 0 {
		return false
	} else {
		return true
	}
}

// Enable direct to fi lite
func (obj *DirectToFiLiteConfig) Enable(ctx context.Context) bool {
	defVal := obj.enable()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Enable"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

// Direct to fi lite variant
func (obj *DirectToFiLiteConfig) variant() string {
	obj._VariantMutex.RLock()
	defer obj._VariantMutex.RUnlock()
	return obj._Variant
}

// Direct to fi lite variant
func (obj *DirectToFiLiteConfig) Variant(ctx context.Context) string {
	defVal := obj.variant()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Variant"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

type CrossValidationConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_DummyDynamicFlag           uint32
	_CrossValidationDataSources map[string]map[string][]string
	_FmPollingRetryConfig       *cfg.RegularInterval
	_NameMatchScoreThreshold    float32
}

func (obj *CrossValidationConfig) DummyDynamicFlag() bool {
	if atomic.LoadUint32(&obj._DummyDynamicFlag) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CrossValidationConfig) CrossValidationDataSources() map[string]map[string][]string {
	return obj._CrossValidationDataSources
}
func (obj *CrossValidationConfig) FmPollingRetryConfig() *cfg.RegularInterval {
	return obj._FmPollingRetryConfig
}
func (obj *CrossValidationConfig) NameMatchScoreThreshold() float32 {
	return obj._NameMatchScoreThreshold
}

type PassportVerificationConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Behaviour if the vendor api fails. If true, passport verification stage is marked successful for
	// the user on API failure or invalid response.
	_IgnoreVendorError uint32
	// Block users whose passports expire within the threshold
	_PassportExpiryThreshold          int64
	_DetailsConfirmationFeatureConfig *genapp.FeatureConfig
}

// Behaviour if the vendor api fails. If true, passport verification stage is marked successful for
// the user on API failure or invalid response.
func (obj *PassportVerificationConfig) IgnoreVendorError() bool {
	if atomic.LoadUint32(&obj._IgnoreVendorError) == 0 {
		return false
	} else {
		return true
	}
}

// Block users whose passports expire within the threshold
func (obj *PassportVerificationConfig) PassportExpiryThreshold() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._PassportExpiryThreshold))
}
func (obj *PassportVerificationConfig) DetailsConfirmationFeatureConfig() *genapp.FeatureConfig {
	return obj._DetailsConfirmationFeatureConfig
}

type OrderPhysicalDebitCardConfig struct {
	callbacks                          *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk                           questsdk.Client
	questFieldPath                     string
	_EnableViaQuest                    uint32
	_FreePhysicalDCRefereeRewardConfig *FreePhysicalDCRefereeRewardConfig
}

func (obj *OrderPhysicalDebitCardConfig) enableViaQuest() bool {
	if atomic.LoadUint32(&obj._EnableViaQuest) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *OrderPhysicalDebitCardConfig) EnableViaQuest(ctx context.Context) bool {
	defVal := obj.enableViaQuest()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "EnableViaQuest"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *OrderPhysicalDebitCardConfig) FreePhysicalDCRefereeRewardConfig() *FreePhysicalDCRefereeRewardConfig {
	return obj._FreePhysicalDCRefereeRewardConfig
}

type FreePhysicalDCRefereeRewardConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled uint32
	// construct active from (inclusive) and active till (exclusive) time
	_ActiveFrom      time.Time
	_ActiveFromMutex *sync.RWMutex
	_ActiveTill      time.Time
	_ActiveTillMutex *sync.RWMutex
	// quest experiment variable name on which the free dc experiment is running
	// free dc is applicable only for user's referrer who are part of a quest experiment
	_QuestExpVariablePath      string
	_QuestExpVariablePathMutex *sync.RWMutex
	// free dc applicable only if the evaluated value of QuestExpVariablePath for user's referrer is QuestExpVariableValue
	_QuestExpVariableValue      string
	_QuestExpVariableValueMutex *sync.RWMutex
}

func (obj *FreePhysicalDCRefereeRewardConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// construct active from (inclusive) and active till (exclusive) time
func (obj *FreePhysicalDCRefereeRewardConfig) ActiveFrom() time.Time {
	obj._ActiveFromMutex.RLock()
	defer obj._ActiveFromMutex.RUnlock()
	return obj._ActiveFrom
}
func (obj *FreePhysicalDCRefereeRewardConfig) ActiveTill() time.Time {
	obj._ActiveTillMutex.RLock()
	defer obj._ActiveTillMutex.RUnlock()
	return obj._ActiveTill
}

// quest experiment variable name on which the free dc experiment is running
// free dc is applicable only for user's referrer who are part of a quest experiment
func (obj *FreePhysicalDCRefereeRewardConfig) QuestExpVariablePath() string {
	obj._QuestExpVariablePathMutex.RLock()
	defer obj._QuestExpVariablePathMutex.RUnlock()
	return obj._QuestExpVariablePath
}

// free dc applicable only if the evaluated value of QuestExpVariablePath for user's referrer is QuestExpVariableValue
func (obj *FreePhysicalDCRefereeRewardConfig) QuestExpVariableValue() string {
	obj._QuestExpVariableValueMutex.RLock()
	defer obj._QuestExpVariableValueMutex.RUnlock()
	return obj._QuestExpVariableValue
}

type UqudoCountryIdVerificationConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ScreenDetectionThreshold int64
	_PrintDetectionThreshold  int64
	_PhotoTamperingThreshold  int64
	_FaceMatchThreshold       int32
	_EnableTamperCheck        uint32
	// Block users whose id expire within the threshold
	_IdExpiryThreshold int64
}

func (obj *UqudoCountryIdVerificationConfig) ScreenDetectionThreshold() int {
	return int(atomic.LoadInt64(&obj._ScreenDetectionThreshold))
}
func (obj *UqudoCountryIdVerificationConfig) PrintDetectionThreshold() int {
	return int(atomic.LoadInt64(&obj._PrintDetectionThreshold))
}
func (obj *UqudoCountryIdVerificationConfig) PhotoTamperingThreshold() int {
	return int(atomic.LoadInt64(&obj._PhotoTamperingThreshold))
}
func (obj *UqudoCountryIdVerificationConfig) FaceMatchThreshold() int32 {
	return int32(atomic.LoadInt32(&obj._FaceMatchThreshold))
}
func (obj *UqudoCountryIdVerificationConfig) EnableTamperCheck() bool {
	if atomic.LoadUint32(&obj._EnableTamperCheck) == 0 {
		return false
	} else {
		return true
	}
}

// Block users whose id expire within the threshold
func (obj *UqudoCountryIdVerificationConfig) IdExpiryThreshold() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._IdExpiryThreshold))
}

type WalkthroughScreenConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_FeatureConfig *genapp.FeatureConfig
}

func (obj *WalkthroughScreenConfig) FeatureConfig() *genapp.FeatureConfig {
	return obj._FeatureConfig
}

type Flags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Flag for enabling active products by pan check while updating user profile
	_EnableActiveProductsByPANCheck uint32
	_TrimDebugMessageFromStatus     bool
	_EnableUpdateUserComms          bool
}

// Flag for enabling active products by pan check while updating user profile
func (obj *Flags) EnableActiveProductsByPANCheck() bool {
	if atomic.LoadUint32(&obj._EnableActiveProductsByPANCheck) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) TrimDebugMessageFromStatus() bool {
	return obj._TrimDebugMessageFromStatus
}
func (obj *Flags) EnableUpdateUserComms() bool {
	return obj._EnableUpdateUserComms
}

type VKYC struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// instruction page skip option timer in seconds
	_InstructionPageSkipOptionTime int32
	// landing page skip option timer in seconds
	_LandingPageSkipOptionTime               int32
	_EnableDemandManagement                  uint32
	_EnablePassportFaceImageCheckForNRExpiry uint32
	_ShowAuditorAcceptedTileTime             int64
	// KYC expiry for VKYC for NR users. If VKYC call is not completed within NRKYCExpiryForVKYC, KYC is considered as expired and user has to redo KYC again irrespective of data purging policy.
	_NRKYCExpiryForVKYC int64
	_Option             *syncmap.Map[string, *VKYCOption]
	_EnableVKYCFlowV2   *genapp.FeatureConfig
}

// instruction page skip option timer in seconds
func (obj *VKYC) InstructionPageSkipOptionTime() int32 {
	return int32(atomic.LoadInt32(&obj._InstructionPageSkipOptionTime))
}

// landing page skip option timer in seconds
func (obj *VKYC) LandingPageSkipOptionTime() int32 {
	return int32(atomic.LoadInt32(&obj._LandingPageSkipOptionTime))
}
func (obj *VKYC) EnableDemandManagement() bool {
	if atomic.LoadUint32(&obj._EnableDemandManagement) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *VKYC) EnablePassportFaceImageCheckForNRExpiry() bool {
	if atomic.LoadUint32(&obj._EnablePassportFaceImageCheckForNRExpiry) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *VKYC) ShowAuditorAcceptedTileTime() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ShowAuditorAcceptedTileTime))
}

// KYC expiry for VKYC for NR users. If VKYC call is not completed within NRKYCExpiryForVKYC, KYC is considered as expired and user has to redo KYC again irrespective of data purging policy.
func (obj *VKYC) NRKYCExpiryForVKYC() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._NRKYCExpiryForVKYC))
}
func (obj *VKYC) Option() *syncmap.Map[string, *VKYCOption] {
	return obj._Option
}
func (obj *VKYC) EnableVKYCFlowV2() *genapp.FeatureConfig {
	return obj._EnableVKYCFlowV2
}

type VKYCOption struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// probabilistic percentage enable value
	_VkycEnablePercentage int64
	// feature enable flags
	_MinAndroidVersion int64
	_MinIOSVersion     int64
	// true indicates vkyc orchestrator stage to be blocked until approved
	_IsVKYCApprovalBlocking uint32
	// true denotes vkyc schedule flow is enabled for the option
	_EnableVKYCScheduleFlow uint32
	// when true user will be shown option to skip vkyc on landing page
	_SkipOptionFlag uint32
	// false indicates full kyc user needs to do vkyc
	_IgnoreFullKYCUser uint32
	// when true user will get skip call option on instruction page
	_InstructionPageSkipOptionFlag uint32
}

// probabilistic percentage enable value
func (obj *VKYCOption) VkycEnablePercentage() int {
	return int(atomic.LoadInt64(&obj._VkycEnablePercentage))
}

// feature enable flags
func (obj *VKYCOption) MinAndroidVersion() int {
	return int(atomic.LoadInt64(&obj._MinAndroidVersion))
}
func (obj *VKYCOption) MinIOSVersion() int {
	return int(atomic.LoadInt64(&obj._MinIOSVersion))
}

// true indicates vkyc orchestrator stage to be blocked until approved
func (obj *VKYCOption) IsVKYCApprovalBlocking() bool {
	if atomic.LoadUint32(&obj._IsVKYCApprovalBlocking) == 0 {
		return false
	} else {
		return true
	}
}

// true denotes vkyc schedule flow is enabled for the option
func (obj *VKYCOption) EnableVKYCScheduleFlow() bool {
	if atomic.LoadUint32(&obj._EnableVKYCScheduleFlow) == 0 {
		return false
	} else {
		return true
	}
}

// when true user will be shown option to skip vkyc on landing page
func (obj *VKYCOption) SkipOptionFlag() bool {
	if atomic.LoadUint32(&obj._SkipOptionFlag) == 0 {
		return false
	} else {
		return true
	}
}

// false indicates full kyc user needs to do vkyc
func (obj *VKYCOption) IgnoreFullKYCUser() bool {
	if atomic.LoadUint32(&obj._IgnoreFullKYCUser) == 0 {
		return false
	} else {
		return true
	}
}

// when true user will get skip call option on instruction page
func (obj *VKYCOption) InstructionPageSkipOptionFlag() bool {
	if atomic.LoadUint32(&obj._InstructionPageSkipOptionFlag) == 0 {
		return false
	} else {
		return true
	}
}

type UserCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCachingEnabled uint32
	// duration for which user data should be cached
	_CacheTTl     int64
	_UserIdPrefix string
}

func (obj *UserCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// duration for which user data should be cached
func (obj *UserCacheConfig) CacheTTl() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CacheTTl))
}
func (obj *UserCacheConfig) UserIdPrefix() string {
	return obj._UserIdPrefix
}

type UserGroupCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCachingEnabled     uint32
	_UserGroupEmailPrefix string
	_CacheTTl             time.Duration
}

func (obj *UserGroupCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *UserGroupCacheConfig) UserGroupEmailPrefix() string {
	return obj._UserGroupEmailPrefix
}
func (obj *UserGroupCacheConfig) CacheTTl() time.Duration {
	return obj._CacheTTl
}

type MinimalUserCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCachingEnabled uint32
	// duration for which user data should be cached
	_CacheTTl int64
}

func (obj *MinimalUserCacheConfig) IsCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// duration for which user data should be cached
func (obj *MinimalUserCacheConfig) CacheTTl() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CacheTTl))
}

type CreditReportConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// CreditReportPresenceEnabled enforces credit report presence before collecting consent to verify credit report
	// in screener. If flag is off, we show consent to all the users.
	// This config complements the flag in OnboardingConfig
	_CreditReportPresenceEnabled        uint32
	_ExperianConsentConfig              *ExperianConsentConfig
	_DownloadWaitConfigForWealthBuilder *DownloadWaitConfigForWealthBuilder
}

// CreditReportPresenceEnabled enforces credit report presence before collecting consent to verify credit report
// in screener. If flag is off, we show consent to all the users.
// This config complements the flag in OnboardingConfig
func (obj *CreditReportConfig) CreditReportPresenceEnabled() bool {
	if atomic.LoadUint32(&obj._CreditReportPresenceEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CreditReportConfig) ExperianConsentConfig() *ExperianConsentConfig {
	return obj._ExperianConsentConfig
}
func (obj *CreditReportConfig) DownloadWaitConfigForWealthBuilder() *DownloadWaitConfigForWealthBuilder {
	return obj._DownloadWaitConfigForWealthBuilder
}

type ExperianConsentConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ConsentExtension int64
	_ConsentExpiry    int64
}

func (obj *ExperianConsentConfig) ConsentExtension() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ConsentExtension))
}
func (obj *ExperianConsentConfig) ConsentExpiry() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ConsentExpiry))
}

type DownloadWaitConfigForWealthBuilder struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// No of times we would try to check whether the report is downloaded
	_MaxAttemptsForCheckingDownloadStatus int32
	// Duration to wait between each attempt
	// Total duration of wait would be 'maxAttempts * sleepDuration'
	_SleepDurationBetweenEachAttempt int64
}

// No of times we would try to check whether the report is downloaded
func (obj *DownloadWaitConfigForWealthBuilder) MaxAttemptsForCheckingDownloadStatus() int32 {
	return int32(atomic.LoadInt32(&obj._MaxAttemptsForCheckingDownloadStatus))
}

// Duration to wait between each attempt
// Total duration of wait would be 'maxAttempts * sleepDuration'
func (obj *DownloadWaitConfigForWealthBuilder) SleepDurationBetweenEachAttempt() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._SleepDurationBetweenEachAttempt))
}

type UserDevicePropertiesCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCacheEnabled uint32
	_CacheTTL       int64
}

func (obj *UserDevicePropertiesCacheConfig) IsCacheEnabled() bool {
	if atomic.LoadUint32(&obj._IsCacheEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *UserDevicePropertiesCacheConfig) CacheTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CacheTTL))
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["recordhashedcontactlocktimeout"] = _obj.SetRecordHashedContactLockTimeout
	_setters["dedupecacheexpiry"] = _obj.SetDedupeCacheExpiry

	_obj._AccessRevokeCooldownDuration = &syncmap.Map[string, time.Duration]{}
	_setters["accessrevokecooldownduration"] = _obj.SetAccessRevokeCooldownDuration
	_setters["whitelistnumberhashesforuaenr"] = _obj.SetWhitelistNumberHashesForUAENR
	_obj._WhitelistNumberHashesForUAENRMutex = &sync.RWMutex{}
	_setters["whitelistnumberhashesforqatarnr"] = _obj.SetWhitelistNumberHashesForQatarNR
	_obj._WhitelistNumberHashesForQatarNRMutex = &sync.RWMutex{}
	_QuestSdk, _fieldSetters := genconfig.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_OnboardingUserUpdateVKYCSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OnboardingUserUpdateVKYCSubscriber = _OnboardingUserUpdateVKYCSubscriber
	helper.AddFieldSetters("onboardinguserupdatevkycsubscriber", _fieldSetters, _setters)
	_Onboarding, _fieldSetters := NewOnboardingConfig()
	_obj._Onboarding = _Onboarding
	helper.AddFieldSetters("onboarding", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig2.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_ShippingAddressUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ShippingAddressUpdateSubscriber = _ShippingAddressUpdateSubscriber
	helper.AddFieldSetters("shippingaddressupdatesubscriber", _fieldSetters, _setters)
	_ShippingAddressUpdateCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ShippingAddressUpdateCallbackSubscriber = _ShippingAddressUpdateCallbackSubscriber
	helper.AddFieldSetters("shippingaddressupdatecallbacksubscriber", _fieldSetters, _setters)
	_VKYC, _fieldSetters := NewVKYC()
	_obj._VKYC = _VKYC
	helper.AddFieldSetters("vkyc", _fieldSetters, _setters)
	_VKYCUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._VKYCUpdateSubscriber = _VKYCUpdateSubscriber
	helper.AddFieldSetters("vkycupdatesubscriber", _fieldSetters, _setters)
	_EKYCSuccessSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._EKYCSuccessSubscriber = _EKYCSuccessSubscriber
	helper.AddFieldSetters("ekycsuccesssubscriber", _fieldSetters, _setters)
	_CreditReportPresenceSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditReportPresenceSubscriber = _CreditReportPresenceSubscriber
	helper.AddFieldSetters("creditreportpresencesubscriber", _fieldSetters, _setters)
	_CreditReportVerificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditReportVerificationSubscriber = _CreditReportVerificationSubscriber
	helper.AddFieldSetters("creditreportverificationsubscriber", _fieldSetters, _setters)
	_EventsAfPurchaseSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._EventsAfPurchaseSubscriber = _EventsAfPurchaseSubscriber
	helper.AddFieldSetters("eventsafpurchasesubscriber", _fieldSetters, _setters)
	_EventsCompletedTnCSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._EventsCompletedTnCSubscriber = _EventsCompletedTnCSubscriber
	helper.AddFieldSetters("eventscompletedtncsubscriber", _fieldSetters, _setters)
	_UserUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UserUpdateEventSubscriber = _UserUpdateEventSubscriber
	helper.AddFieldSetters("userupdateeventsubscriber", _fieldSetters, _setters)
	_BankCustomerUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._BankCustomerUpdateEventSubscriber = _BankCustomerUpdateEventSubscriber
	helper.AddFieldSetters("bankcustomerupdateeventsubscriber", _fieldSetters, _setters)
	_CreditReportVerificationEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditReportVerificationEventSubscriber = _CreditReportVerificationEventSubscriber
	helper.AddFieldSetters("creditreportverificationeventsubscriber", _fieldSetters, _setters)
	_LivManualReviewEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._LivManualReviewEventSubscriber = _LivManualReviewEventSubscriber
	helper.AddFieldSetters("livmanualrevieweventsubscriber", _fieldSetters, _setters)
	_UserCacheConfig, _fieldSetters := NewUserCacheConfig()
	_obj._UserCacheConfig = _UserCacheConfig
	helper.AddFieldSetters("usercacheconfig", _fieldSetters, _setters)
	_UserGroupCacheConfig, _fieldSetters := NewUserGroupCacheConfig()
	_obj._UserGroupCacheConfig = _UserGroupCacheConfig
	helper.AddFieldSetters("usergroupcacheconfig", _fieldSetters, _setters)
	_MinimalUserCacheConfig, _fieldSetters := NewMinimalUserCacheConfig()
	_obj._MinimalUserCacheConfig = _MinimalUserCacheConfig
	helper.AddFieldSetters("minimalusercacheconfig", _fieldSetters, _setters)
	_CreditReportConfig, _fieldSetters := NewCreditReportConfig()
	_obj._CreditReportConfig = _CreditReportConfig
	helper.AddFieldSetters("creditreportconfig", _fieldSetters, _setters)
	_CreditReportDerivedAttributesSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditReportDerivedAttributesSubscriber = _CreditReportDerivedAttributesSubscriber
	helper.AddFieldSetters("creditreportderivedattributessubscriber", _fieldSetters, _setters)
	_ProcessOnboardingEventUserContactSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessOnboardingEventUserContactSubscriber = _ProcessOnboardingEventUserContactSubscriber
	helper.AddFieldSetters("processonboardingeventusercontactsubscriber", _fieldSetters, _setters)
	_ProcessAfuEventUserContactSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessAfuEventUserContactSubscriber = _ProcessAfuEventUserContactSubscriber
	helper.AddFieldSetters("processafueventusercontactsubscriber", _fieldSetters, _setters)
	_ProcessDeleteUserSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessDeleteUserSubscriber = _ProcessDeleteUserSubscriber
	helper.AddFieldSetters("processdeleteusersubscriber", _fieldSetters, _setters)
	_SyncOnboardingSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SyncOnboardingSubscriber = _SyncOnboardingSubscriber
	helper.AddFieldSetters("synconboardingsubscriber", _fieldSetters, _setters)
	_ProcessCardCreationEvent, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessCardCreationEvent = _ProcessCardCreationEvent
	helper.AddFieldSetters("processcardcreationevent", _fieldSetters, _setters)
	_VKYCCallCompletedEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._VKYCCallCompletedEventSubscriber = _VKYCCallCompletedEventSubscriber
	helper.AddFieldSetters("vkyccallcompletedeventsubscriber", _fieldSetters, _setters)
	_InHouseVkycCallCompletedEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._InHouseVkycCallCompletedEventSubscriber = _InHouseVkycCallCompletedEventSubscriber
	helper.AddFieldSetters("inhousevkyccallcompletedeventsubscriber", _fieldSetters, _setters)
	_ProcessSavingsAccountUpdateEvent, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessSavingsAccountUpdateEvent = _ProcessSavingsAccountUpdateEvent
	helper.AddFieldSetters("processsavingsaccountupdateevent", _fieldSetters, _setters)
	_UserDevicePropertiesCacheConfig, _fieldSetters := NewUserDevicePropertiesCacheConfig()
	_obj._UserDevicePropertiesCacheConfig = _UserDevicePropertiesCacheConfig
	helper.AddFieldSetters("userdevicepropertiescacheconfig", _fieldSetters, _setters)
	_ProcessAccessRevokeCooldownSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessAccessRevokeCooldownSubscriber = _ProcessAccessRevokeCooldownSubscriber
	helper.AddFieldSetters("processaccessrevokecooldownsubscriber", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["recordhashedcontactlocktimeout"] = _obj.SetRecordHashedContactLockTimeout
	_setters["dedupecacheexpiry"] = _obj.SetDedupeCacheExpiry

	_obj._AccessRevokeCooldownDuration = &syncmap.Map[string, time.Duration]{}
	_setters["accessrevokecooldownduration"] = _obj.SetAccessRevokeCooldownDuration
	_setters["whitelistnumberhashesforuaenr"] = _obj.SetWhitelistNumberHashesForUAENR
	_obj._WhitelistNumberHashesForUAENRMutex = &sync.RWMutex{}
	_setters["whitelistnumberhashesforqatarnr"] = _obj.SetWhitelistNumberHashesForQatarNR
	_obj._WhitelistNumberHashesForQatarNRMutex = &sync.RWMutex{}
	_QuestSdk, _fieldSetters := genconfig.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_OnboardingUserUpdateVKYCSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OnboardingUserUpdateVKYCSubscriber = _OnboardingUserUpdateVKYCSubscriber
	helper.AddFieldSetters("onboardinguserupdatevkycsubscriber", _fieldSetters, _setters)
	_Onboarding, _fieldSetters := NewOnboardingConfigWithQuest(questFieldPath + "/" + "Onboarding")
	_obj._Onboarding = _Onboarding
	helper.AddFieldSetters("onboarding", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig2.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_ShippingAddressUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ShippingAddressUpdateSubscriber = _ShippingAddressUpdateSubscriber
	helper.AddFieldSetters("shippingaddressupdatesubscriber", _fieldSetters, _setters)
	_ShippingAddressUpdateCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ShippingAddressUpdateCallbackSubscriber = _ShippingAddressUpdateCallbackSubscriber
	helper.AddFieldSetters("shippingaddressupdatecallbacksubscriber", _fieldSetters, _setters)
	_VKYC, _fieldSetters := NewVKYC()
	_obj._VKYC = _VKYC
	helper.AddFieldSetters("vkyc", _fieldSetters, _setters)
	_VKYCUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._VKYCUpdateSubscriber = _VKYCUpdateSubscriber
	helper.AddFieldSetters("vkycupdatesubscriber", _fieldSetters, _setters)
	_EKYCSuccessSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._EKYCSuccessSubscriber = _EKYCSuccessSubscriber
	helper.AddFieldSetters("ekycsuccesssubscriber", _fieldSetters, _setters)
	_CreditReportPresenceSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditReportPresenceSubscriber = _CreditReportPresenceSubscriber
	helper.AddFieldSetters("creditreportpresencesubscriber", _fieldSetters, _setters)
	_CreditReportVerificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditReportVerificationSubscriber = _CreditReportVerificationSubscriber
	helper.AddFieldSetters("creditreportverificationsubscriber", _fieldSetters, _setters)
	_EventsAfPurchaseSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._EventsAfPurchaseSubscriber = _EventsAfPurchaseSubscriber
	helper.AddFieldSetters("eventsafpurchasesubscriber", _fieldSetters, _setters)
	_EventsCompletedTnCSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._EventsCompletedTnCSubscriber = _EventsCompletedTnCSubscriber
	helper.AddFieldSetters("eventscompletedtncsubscriber", _fieldSetters, _setters)
	_UserUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UserUpdateEventSubscriber = _UserUpdateEventSubscriber
	helper.AddFieldSetters("userupdateeventsubscriber", _fieldSetters, _setters)
	_BankCustomerUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._BankCustomerUpdateEventSubscriber = _BankCustomerUpdateEventSubscriber
	helper.AddFieldSetters("bankcustomerupdateeventsubscriber", _fieldSetters, _setters)
	_CreditReportVerificationEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditReportVerificationEventSubscriber = _CreditReportVerificationEventSubscriber
	helper.AddFieldSetters("creditreportverificationeventsubscriber", _fieldSetters, _setters)
	_LivManualReviewEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._LivManualReviewEventSubscriber = _LivManualReviewEventSubscriber
	helper.AddFieldSetters("livmanualrevieweventsubscriber", _fieldSetters, _setters)
	_UserCacheConfig, _fieldSetters := NewUserCacheConfig()
	_obj._UserCacheConfig = _UserCacheConfig
	helper.AddFieldSetters("usercacheconfig", _fieldSetters, _setters)
	_UserGroupCacheConfig, _fieldSetters := NewUserGroupCacheConfig()
	_obj._UserGroupCacheConfig = _UserGroupCacheConfig
	helper.AddFieldSetters("usergroupcacheconfig", _fieldSetters, _setters)
	_MinimalUserCacheConfig, _fieldSetters := NewMinimalUserCacheConfig()
	_obj._MinimalUserCacheConfig = _MinimalUserCacheConfig
	helper.AddFieldSetters("minimalusercacheconfig", _fieldSetters, _setters)
	_CreditReportConfig, _fieldSetters := NewCreditReportConfig()
	_obj._CreditReportConfig = _CreditReportConfig
	helper.AddFieldSetters("creditreportconfig", _fieldSetters, _setters)
	_CreditReportDerivedAttributesSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditReportDerivedAttributesSubscriber = _CreditReportDerivedAttributesSubscriber
	helper.AddFieldSetters("creditreportderivedattributessubscriber", _fieldSetters, _setters)
	_ProcessOnboardingEventUserContactSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessOnboardingEventUserContactSubscriber = _ProcessOnboardingEventUserContactSubscriber
	helper.AddFieldSetters("processonboardingeventusercontactsubscriber", _fieldSetters, _setters)
	_ProcessAfuEventUserContactSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessAfuEventUserContactSubscriber = _ProcessAfuEventUserContactSubscriber
	helper.AddFieldSetters("processafueventusercontactsubscriber", _fieldSetters, _setters)
	_ProcessDeleteUserSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessDeleteUserSubscriber = _ProcessDeleteUserSubscriber
	helper.AddFieldSetters("processdeleteusersubscriber", _fieldSetters, _setters)
	_SyncOnboardingSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SyncOnboardingSubscriber = _SyncOnboardingSubscriber
	helper.AddFieldSetters("synconboardingsubscriber", _fieldSetters, _setters)
	_ProcessCardCreationEvent, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessCardCreationEvent = _ProcessCardCreationEvent
	helper.AddFieldSetters("processcardcreationevent", _fieldSetters, _setters)
	_VKYCCallCompletedEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._VKYCCallCompletedEventSubscriber = _VKYCCallCompletedEventSubscriber
	helper.AddFieldSetters("vkyccallcompletedeventsubscriber", _fieldSetters, _setters)
	_InHouseVkycCallCompletedEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._InHouseVkycCallCompletedEventSubscriber = _InHouseVkycCallCompletedEventSubscriber
	helper.AddFieldSetters("inhousevkyccallcompletedeventsubscriber", _fieldSetters, _setters)
	_ProcessSavingsAccountUpdateEvent, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessSavingsAccountUpdateEvent = _ProcessSavingsAccountUpdateEvent
	helper.AddFieldSetters("processsavingsaccountupdateevent", _fieldSetters, _setters)
	_UserDevicePropertiesCacheConfig, _fieldSetters := NewUserDevicePropertiesCacheConfig()
	_obj._UserDevicePropertiesCacheConfig = _UserDevicePropertiesCacheConfig
	helper.AddFieldSetters("userdevicepropertiescacheconfig", _fieldSetters, _setters)
	_ProcessAccessRevokeCooldownSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessAccessRevokeCooldownSubscriber = _ProcessAccessRevokeCooldownSubscriber
	helper.AddFieldSetters("processaccessrevokecooldownsubscriber", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init(questFieldPath string) {
	newObj, _ := NewConfig()
	*obj = *newObj
}
func (obj *Config) InitWithQuest(questFieldPath string) {
	newObj, _ := NewConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
	obj._Onboarding.SetQuestSDK(questSdk)
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	childVars, childVarsErr = obj._Onboarding.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}

	for _, v := range childVars {
		v.Area = "Onboarding" // from quest tag annotation for the component field
		vars = append(vars, v)
	}
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "recordhashedcontactlocktimeout":
		return obj.SetRecordHashedContactLockTimeout(v.RecordHashedContactLockTimeout, true, nil)
	case "dedupecacheexpiry":
		return obj.SetDedupeCacheExpiry(v.DedupeCacheExpiry, true, nil)
	case "accessrevokecooldownduration":
		return obj.SetAccessRevokeCooldownDuration(v.AccessRevokeCooldownDuration, true, path)
	case "whitelistnumberhashesforuaenr":
		return obj.SetWhitelistNumberHashesForUAENR(v.WhitelistNumberHashesForUAENR, true, path)
	case "whitelistnumberhashesforqatarnr":
		return obj.SetWhitelistNumberHashesForQatarNR(v.WhitelistNumberHashesForQatarNR, true, path)
	case "questsdk":
		return obj._QuestSdk.Set(v.QuestSdk, true, path)
	case "onboardinguserupdatevkycsubscriber":
		return obj._OnboardingUserUpdateVKYCSubscriber.Set(v.OnboardingUserUpdateVKYCSubscriber, true, path)
	case "onboarding":
		return obj._Onboarding.Set(v.Onboarding, true, path)
	case "featurereleaseconfig":
		return obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, true, path)
	case "flags":
		return obj._Flags.Set(v.Flags, true, path)
	case "shippingaddressupdatesubscriber":
		return obj._ShippingAddressUpdateSubscriber.Set(v.ShippingAddressUpdateSubscriber, true, path)
	case "shippingaddressupdatecallbacksubscriber":
		return obj._ShippingAddressUpdateCallbackSubscriber.Set(v.ShippingAddressUpdateCallbackSubscriber, true, path)
	case "vkyc":
		return obj._VKYC.Set(v.VKYC, true, path)
	case "vkycupdatesubscriber":
		return obj._VKYCUpdateSubscriber.Set(v.VKYCUpdateSubscriber, true, path)
	case "ekycsuccesssubscriber":
		return obj._EKYCSuccessSubscriber.Set(v.EKYCSuccessSubscriber, true, path)
	case "creditreportpresencesubscriber":
		return obj._CreditReportPresenceSubscriber.Set(v.CreditReportPresenceSubscriber, true, path)
	case "creditreportverificationsubscriber":
		return obj._CreditReportVerificationSubscriber.Set(v.CreditReportVerificationSubscriber, true, path)
	case "eventsafpurchasesubscriber":
		return obj._EventsAfPurchaseSubscriber.Set(v.EventsAfPurchaseSubscriber, true, path)
	case "eventscompletedtncsubscriber":
		return obj._EventsCompletedTnCSubscriber.Set(v.EventsCompletedTnCSubscriber, true, path)
	case "userupdateeventsubscriber":
		return obj._UserUpdateEventSubscriber.Set(v.UserUpdateEventSubscriber, true, path)
	case "bankcustomerupdateeventsubscriber":
		return obj._BankCustomerUpdateEventSubscriber.Set(v.BankCustomerUpdateEventSubscriber, true, path)
	case "creditreportverificationeventsubscriber":
		return obj._CreditReportVerificationEventSubscriber.Set(v.CreditReportVerificationEventSubscriber, true, path)
	case "livmanualrevieweventsubscriber":
		return obj._LivManualReviewEventSubscriber.Set(v.LivManualReviewEventSubscriber, true, path)
	case "usercacheconfig":
		return obj._UserCacheConfig.Set(v.UserCacheConfig, true, path)
	case "usergroupcacheconfig":
		return obj._UserGroupCacheConfig.Set(v.UserGroupCacheConfig, true, path)
	case "minimalusercacheconfig":
		return obj._MinimalUserCacheConfig.Set(v.MinimalUserCacheConfig, true, path)
	case "creditreportconfig":
		return obj._CreditReportConfig.Set(v.CreditReportConfig, true, path)
	case "creditreportderivedattributessubscriber":
		return obj._CreditReportDerivedAttributesSubscriber.Set(v.CreditReportDerivedAttributesSubscriber, true, path)
	case "processonboardingeventusercontactsubscriber":
		return obj._ProcessOnboardingEventUserContactSubscriber.Set(v.ProcessOnboardingEventUserContactSubscriber, true, path)
	case "processafueventusercontactsubscriber":
		return obj._ProcessAfuEventUserContactSubscriber.Set(v.ProcessAfuEventUserContactSubscriber, true, path)
	case "processdeleteusersubscriber":
		return obj._ProcessDeleteUserSubscriber.Set(v.ProcessDeleteUserSubscriber, true, path)
	case "synconboardingsubscriber":
		return obj._SyncOnboardingSubscriber.Set(v.SyncOnboardingSubscriber, true, path)
	case "processcardcreationevent":
		return obj._ProcessCardCreationEvent.Set(v.ProcessCardCreationEvent, true, path)
	case "vkyccallcompletedeventsubscriber":
		return obj._VKYCCallCompletedEventSubscriber.Set(v.VKYCCallCompletedEventSubscriber, true, path)
	case "inhousevkyccallcompletedeventsubscriber":
		return obj._InHouseVkycCallCompletedEventSubscriber.Set(v.InHouseVkycCallCompletedEventSubscriber, true, path)
	case "processsavingsaccountupdateevent":
		return obj._ProcessSavingsAccountUpdateEvent.Set(v.ProcessSavingsAccountUpdateEvent, true, path)
	case "userdevicepropertiescacheconfig":
		return obj._UserDevicePropertiesCacheConfig.Set(v.UserDevicePropertiesCacheConfig, true, path)
	case "processaccessrevokecooldownsubscriber":
		return obj._ProcessAccessRevokeCooldownSubscriber.Set(v.ProcessAccessRevokeCooldownSubscriber, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj.SetRecordHashedContactLockTimeout(v.RecordHashedContactLockTimeout, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDedupeCacheExpiry(v.DedupeCacheExpiry, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAccessRevokeCooldownDuration(v.AccessRevokeCooldownDuration, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetWhitelistNumberHashesForUAENR(v.WhitelistNumberHashesForUAENR, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetWhitelistNumberHashesForQatarNR(v.WhitelistNumberHashesForQatarNR, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._QuestSdk.Set(v.QuestSdk, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OnboardingUserUpdateVKYCSubscriber.Set(v.OnboardingUserUpdateVKYCSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Onboarding.Set(v.Onboarding, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Flags.Set(v.Flags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ShippingAddressUpdateSubscriber.Set(v.ShippingAddressUpdateSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ShippingAddressUpdateCallbackSubscriber.Set(v.ShippingAddressUpdateCallbackSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._VKYC.Set(v.VKYC, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._VKYCUpdateSubscriber.Set(v.VKYCUpdateSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EKYCSuccessSubscriber.Set(v.EKYCSuccessSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreditReportPresenceSubscriber.Set(v.CreditReportPresenceSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreditReportVerificationSubscriber.Set(v.CreditReportVerificationSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EventsAfPurchaseSubscriber.Set(v.EventsAfPurchaseSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EventsCompletedTnCSubscriber.Set(v.EventsCompletedTnCSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UserUpdateEventSubscriber.Set(v.UserUpdateEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._BankCustomerUpdateEventSubscriber.Set(v.BankCustomerUpdateEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreditReportVerificationEventSubscriber.Set(v.CreditReportVerificationEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._LivManualReviewEventSubscriber.Set(v.LivManualReviewEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UserCacheConfig.Set(v.UserCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UserGroupCacheConfig.Set(v.UserGroupCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._MinimalUserCacheConfig.Set(v.MinimalUserCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreditReportConfig.Set(v.CreditReportConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreditReportDerivedAttributesSubscriber.Set(v.CreditReportDerivedAttributesSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessOnboardingEventUserContactSubscriber.Set(v.ProcessOnboardingEventUserContactSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessAfuEventUserContactSubscriber.Set(v.ProcessAfuEventUserContactSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessDeleteUserSubscriber.Set(v.ProcessDeleteUserSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SyncOnboardingSubscriber.Set(v.SyncOnboardingSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessCardCreationEvent.Set(v.ProcessCardCreationEvent, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._VKYCCallCompletedEventSubscriber.Set(v.VKYCCallCompletedEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._InHouseVkycCallCompletedEventSubscriber.Set(v.InHouseVkycCallCompletedEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessSavingsAccountUpdateEvent.Set(v.ProcessSavingsAccountUpdateEvent, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UserDevicePropertiesCacheConfig.Set(v.UserDevicePropertiesCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessAccessRevokeCooldownSubscriber.Set(v.ProcessAccessRevokeCooldownSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._EpifiDb = v.EpifiDb
	obj._FeatureEngineeringDb = v.FeatureEngineeringDb
	obj._AWS = v.AWS
	obj._RedisOptions = v.RedisOptions
	obj._OnboardingStageEventPublisher = v.OnboardingStageEventPublisher
	obj._OnboardingUserUpdatePublisher = v.OnboardingUserUpdatePublisher
	obj._Consent = v.Consent
	obj._Referral = v.Referral
	obj._NewOnFi = v.NewOnFi
	obj._RudderStack = v.RudderStack
	obj._Secrets = v.Secrets
	obj._ShippingAddressUpdatePublisher = v.ShippingAddressUpdatePublisher
	obj._ConsentEventPublisher = v.ConsentEventPublisher
	obj._UserAccessRevokeUpdatePublisher = v.UserAccessRevokeUpdatePublisher
	obj._ShippingAddressUpdateEventPublisher = v.ShippingAddressUpdateEventPublisher
	obj._CSIS = v.CSIS
	obj._CreditReportPresencePublisher = v.CreditReportPresencePublisher
	obj._CreditReportVerificationPublisher = v.CreditReportVerificationPublisher
	obj._AfPurchasePublisher = v.AfPurchasePublisher
	obj._EventsCompletedTnCPublisher = v.EventsCompletedTnCPublisher
	obj._Screening = v.Screening
	obj._ExperianDataStorageLimitInHrs = v.ExperianDataStorageLimitInHrs
	obj._Events = v.Events
	obj._VpaMigrationConsentPublisher = v.VpaMigrationConsentPublisher
	obj._CreditReportDerivedAttributesPublisher = v.CreditReportDerivedAttributesPublisher
	obj._UserDevicePropertiesUpdatePublisher = v.UserDevicePropertiesUpdatePublisher
	obj._H3RankingFile = v.H3RankingFile
	obj._ProcessAccessRevokeCooldownPublisher = v.ProcessAccessRevokeCooldownPublisher
	obj._DeleteUserPublisher = v.DeleteUserPublisher
	return nil
}

func (obj *Config) SetRecordHashedContactLockTimeout(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.RecordHashedContactLockTimeout", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._RecordHashedContactLockTimeout, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RecordHashedContactLockTimeout")
	}
	return nil
}
func (obj *Config) SetDedupeCacheExpiry(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.DedupeCacheExpiry", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._DedupeCacheExpiry, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "DedupeCacheExpiry")
	}
	return nil
}
func (obj *Config) SetAccessRevokeCooldownDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.AccessRevokeCooldownDuration", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._AccessRevokeCooldownDuration, v, path)
}
func (obj *Config) SetWhitelistNumberHashesForUAENR(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.WhitelistNumberHashesForUAENR", reflect.TypeOf(val))
	}
	obj._WhitelistNumberHashesForUAENRMutex.Lock()
	defer obj._WhitelistNumberHashesForUAENRMutex.Unlock()
	obj._WhitelistNumberHashesForUAENR = roarray.New[string](v)
	return nil
}
func (obj *Config) SetWhitelistNumberHashesForQatarNR(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.WhitelistNumberHashesForQatarNR", reflect.TypeOf(val))
	}
	obj._WhitelistNumberHashesForQatarNRMutex.Lock()
	defer obj._WhitelistNumberHashesForQatarNRMutex.Unlock()
	obj._WhitelistNumberHashesForQatarNR = roarray.New[string](v)
	return nil
}

func NewOnboardingConfig() (_obj *OnboardingConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &OnboardingConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minandroidversionformanualbalancerefreshonbaddfunds"] = _obj.SetMinAndroidVersionForManualBalanceRefreshOnbAddFunds
	_setters["miniosversionformanualbalancerefreshonbaddfunds"] = _obj.SetMinIosVersionForManualBalanceRefreshOnbAddFunds
	_setters["filiterolloutpercentage"] = _obj.SetFiLiteRolloutPercentage
	_setters["secureusageguidelineversion"] = _obj.SetSecureUsageGuidelineVersion
	_setters["parentnameprefillfromckycrolloutpercentage"] = _obj.SetParentNamePrefillFromCKYCRolloutPercentage
	_setters["simulatebalancecheckfailureforaddfunds"] = _obj.SetSimulateBalanceCheckFailureForAddFunds
	_setters["totalamountviaorderaddfunds"] = _obj.SetTotalAmountViaOrderAddFunds
	_setters["blockonboardingduetounlinkedpanandaadhaar"] = _obj.SetBlockOnboardingDueToUnlinkedPANAndAadhaar
	_setters["ispandobdropofftowealthanalyserenabled"] = _obj.SetIsPanDOBDropOffToWealthAnalyserEnabled
	_setters["durationtoskipaddmoney"] = _obj.SetDurationToSkipAddMoney
	_setters["riskscreeningexpiry"] = _obj.SetRiskScreeningExpiry
	_setters["livenesssummaryexpiryduration"] = _obj.SetLivenessSummaryExpiryDuration
	_setters["secureusageguidelineconsentinterval"] = _obj.SetSecureUsageGuidelineConsentInterval

	_obj._HealthConfig = &syncmap.Map[string, *ServiceHealth]{}
	_setters["healthconfig"] = _obj.SetHealthConfig

	_obj._ReferralOfferCodesDuringOnboarding = &syncmap.Map[string, *ReferralOfferCodeDuringOnboarding]{}
	_setters["referraloffercodesduringonboarding"] = _obj.SetReferralOfferCodesDuringOnboarding

	_obj._ReferralOfferWidgetsDuringOnboarding = &syncmap.Map[string, *ReferralOfferWidgetDuringOnboarding]{}
	_setters["referralofferwidgetsduringonboarding"] = _obj.SetReferralOfferWidgetsDuringOnboarding

	_obj._DurationToSkipAddFundsForAffluenceClasses = &syncmap.Map[string, *DurationToSkipAddFundsForAffluenceClass]{}
	_setters["durationtoskipaddfundsforaffluenceclasses"] = _obj.SetDurationToSkipAddFundsForAffluenceClasses

	_obj._WebUrlsForSalaryB2BFlows = &syncmap.Map[string, *WebUrlForSalaryB2BFlow]{}
	_setters["weburlsforsalaryb2bflows"] = _obj.SetWebUrlsForSalaryB2BFlows

	_obj._AffluenceClassesEligibleForBonusTransitionScreen = &syncmap.Map[string, bool]{}
	_setters["affluenceclasseseligibleforbonustransitionscreen"] = _obj.SetAffluenceClassesEligibleForBonusTransitionScreen
	_setters["actorswhitelistedforprefunding"] = _obj.SetActorsWhitelistedForPreFunding
	_obj._ActorsWhitelistedForPreFundingMutex = &sync.RWMutex{}
	_setters["blockonboardingfromtime"] = _obj.SetBlockOnboardingFromTime
	_obj._BlockOnboardingFromTimeMutex = &sync.RWMutex{}
	_setters["blockonboardingtilltime"] = _obj.SetBlockOnboardingTillTime
	_obj._BlockOnboardingTillTimeMutex = &sync.RWMutex{}
	_setters["blockonboardingmsg"] = _obj.SetBlockOnboardingMsg
	_obj._BlockOnboardingMsgMutex = &sync.RWMutex{}
	_setters["finitecodefromattributionparamskey"] = _obj.SetFiniteCodeFromAttributionParamsKey
	_obj._FiniteCodeFromAttributionParamsKeyMutex = &sync.RWMutex{}
	_Flags, _fieldSetters := NewOnboardingFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_ABFeatureReleaseConfig, _fieldSetters := genconfig2.NewABFeatureReleaseConfig()
	_obj._ABFeatureReleaseConfig = _ABFeatureReleaseConfig
	helper.AddFieldSetters("abfeaturereleaseconfig", _fieldSetters, _setters)
	_OnbDetailsCacheConfig, _fieldSetters := NewOnbDetailsCacheConfig()
	_obj._OnbDetailsCacheConfig = _OnbDetailsCacheConfig
	helper.AddFieldSetters("onbdetailscacheconfig", _fieldSetters, _setters)
	_NextActionDecisionCacheConfig, _fieldSetters := NewNextActionDecisionCacheConfig()
	_obj._NextActionDecisionCacheConfig = _NextActionDecisionCacheConfig
	helper.AddFieldSetters("nextactiondecisioncacheconfig", _fieldSetters, _setters)
	_AppScreeningConfig, _fieldSetters := NewAppScreeningConfig()
	_obj._AppScreeningConfig = _AppScreeningConfig
	helper.AddFieldSetters("appscreeningconfig", _fieldSetters, _setters)
	_OnboardingVelocityConfig, _fieldSetters := NewOnboardingVelocityConfig()
	_obj._OnboardingVelocityConfig = _OnboardingVelocityConfig
	helper.AddFieldSetters("onboardingvelocityconfig", _fieldSetters, _setters)
	_ReferralOfferCodesABReleaseConfig, _fieldSetters := genconfig2.NewABFeatureReleaseConfig()
	_obj._ReferralOfferCodesABReleaseConfig = _ReferralOfferCodesABReleaseConfig
	helper.AddFieldSetters("referraloffercodesabreleaseconfig", _fieldSetters, _setters)
	_OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig, _fieldSetters := genconfig2.NewABFeatureReleaseConfig()
	_obj._OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig = _OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig
	helper.AddFieldSetters("offerwidgetonbscreensviafinitecodeabreleaseconfig", _fieldSetters, _setters)
	_EKYCCertUpgradeFeatureConfig, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EKYCCertUpgradeFeatureConfig = _EKYCCertUpgradeFeatureConfig
	helper.AddFieldSetters("ekyccertupgradefeatureconfig", _fieldSetters, _setters)
	_MinKycMandatoryAddFundConfig, _fieldSetters := NewMandatoryMinKycAddFundConfig()
	_obj._MinKycMandatoryAddFundConfig = _MinKycMandatoryAddFundConfig
	helper.AddFieldSetters("minkycmandatoryaddfundconfig", _fieldSetters, _setters)
	_EditEmploymentInScreener, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EditEmploymentInScreener = _EditEmploymentInScreener
	helper.AddFieldSetters("editemploymentinscreener", _fieldSetters, _setters)
	_AddFundsConfig, _fieldSetters := NewOnbAddFundsConfig()
	_obj._AddFundsConfig = _AddFundsConfig
	helper.AddFieldSetters("addfundsconfig", _fieldSetters, _setters)
	_ResetOnboardingJourney, _fieldSetters := NewResetOnboardingJourneyConfig()
	_obj._ResetOnboardingJourney = _ResetOnboardingJourney
	helper.AddFieldSetters("resetonboardingjourney", _fieldSetters, _setters)
	_ConfirmCardMailingAddress, _fieldSetters := NewConfirmCardMailingAddressConfig()
	_obj._ConfirmCardMailingAddress = _ConfirmCardMailingAddress
	helper.AddFieldSetters("confirmcardmailingaddress", _fieldSetters, _setters)
	_IntentSelectionConfigV2, _fieldSetters := NewIntentSelectionConfigV2()
	_obj._IntentSelectionConfigV2 = _IntentSelectionConfigV2
	helper.AddFieldSetters("intentselectionconfigv2", _fieldSetters, _setters)
	_SoftIntentSelectionConfig, _fieldSetters := NewSoftIntentSelectionConfig()
	_obj._SoftIntentSelectionConfig = _SoftIntentSelectionConfig
	helper.AddFieldSetters("softintentselectionconfig", _fieldSetters, _setters)
	_DirectToFiLite, _fieldSetters := NewDirectToFiLiteConfig()
	_obj._DirectToFiLite = _DirectToFiLite
	helper.AddFieldSetters("directtofilite", _fieldSetters, _setters)
	_PanValidateV3FeatureConfig, _fieldSetters := genapp.NewFeatureConfig()
	_obj._PanValidateV3FeatureConfig = _PanValidateV3FeatureConfig
	helper.AddFieldSetters("panvalidatev3featureconfig", _fieldSetters, _setters)
	_ShowNewConsentInPanValidateV2, _fieldSetters := genapp.NewFeatureConfig()
	_obj._ShowNewConsentInPanValidateV2 = _ShowNewConsentInPanValidateV2
	helper.AddFieldSetters("shownewconsentinpanvalidatev2", _fieldSetters, _setters)
	_NonResidentCrossValidationConfig, _fieldSetters := NewCrossValidationConfig()
	_obj._NonResidentCrossValidationConfig = _NonResidentCrossValidationConfig
	helper.AddFieldSetters("nonresidentcrossvalidationconfig", _fieldSetters, _setters)
	_PassportVerificationConfig, _fieldSetters := NewPassportVerificationConfig()
	_obj._PassportVerificationConfig = _PassportVerificationConfig
	helper.AddFieldSetters("passportverificationconfig", _fieldSetters, _setters)
	_EnableTriggerNROAccountCreation, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableTriggerNROAccountCreation = _EnableTriggerNROAccountCreation
	helper.AddFieldSetters("enabletriggernroaccountcreation", _fieldSetters, _setters)
	_PrefillParentNameFromPassportOCR, _fieldSetters := genapp.NewFeatureConfig()
	_obj._PrefillParentNameFromPassportOCR = _PrefillParentNameFromPassportOCR
	helper.AddFieldSetters("prefillparentnamefrompassportocr", _fieldSetters, _setters)
	_OrderPhysicalDebitCardConfig, _fieldSetters := NewOrderPhysicalDebitCardConfig()
	_obj._OrderPhysicalDebitCardConfig = _OrderPhysicalDebitCardConfig
	helper.AddFieldSetters("orderphysicaldebitcardconfig", _fieldSetters, _setters)
	_UqudoCountryIdVerificationConfig, _fieldSetters := NewUqudoCountryIdVerificationConfig()
	_obj._UqudoCountryIdVerificationConfig = _UqudoCountryIdVerificationConfig
	helper.AddFieldSetters("uqudocountryidverificationconfig", _fieldSetters, _setters)
	_WalkthroughScreenConfig, _fieldSetters := NewWalkthroughScreenConfig()
	_obj._WalkthroughScreenConfig = _WalkthroughScreenConfig
	helper.AddFieldSetters("walkthroughscreenconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewOnboardingConfigWithQuest(questFieldPath string) (_obj *OnboardingConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &OnboardingConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minandroidversionformanualbalancerefreshonbaddfunds"] = _obj.SetMinAndroidVersionForManualBalanceRefreshOnbAddFunds
	_setters["miniosversionformanualbalancerefreshonbaddfunds"] = _obj.SetMinIosVersionForManualBalanceRefreshOnbAddFunds
	_setters["filiterolloutpercentage"] = _obj.SetFiLiteRolloutPercentage
	_setters["secureusageguidelineversion"] = _obj.SetSecureUsageGuidelineVersion
	_setters["parentnameprefillfromckycrolloutpercentage"] = _obj.SetParentNamePrefillFromCKYCRolloutPercentage
	_setters["simulatebalancecheckfailureforaddfunds"] = _obj.SetSimulateBalanceCheckFailureForAddFunds
	_setters["totalamountviaorderaddfunds"] = _obj.SetTotalAmountViaOrderAddFunds
	_setters["blockonboardingduetounlinkedpanandaadhaar"] = _obj.SetBlockOnboardingDueToUnlinkedPANAndAadhaar
	_setters["ispandobdropofftowealthanalyserenabled"] = _obj.SetIsPanDOBDropOffToWealthAnalyserEnabled
	_setters["durationtoskipaddmoney"] = _obj.SetDurationToSkipAddMoney
	_setters["riskscreeningexpiry"] = _obj.SetRiskScreeningExpiry
	_setters["livenesssummaryexpiryduration"] = _obj.SetLivenessSummaryExpiryDuration
	_setters["secureusageguidelineconsentinterval"] = _obj.SetSecureUsageGuidelineConsentInterval

	_obj._HealthConfig = &syncmap.Map[string, *ServiceHealth]{}
	_setters["healthconfig"] = _obj.SetHealthConfig

	_obj._ReferralOfferCodesDuringOnboarding = &syncmap.Map[string, *ReferralOfferCodeDuringOnboarding]{}
	_setters["referraloffercodesduringonboarding"] = _obj.SetReferralOfferCodesDuringOnboarding

	_obj._ReferralOfferWidgetsDuringOnboarding = &syncmap.Map[string, *ReferralOfferWidgetDuringOnboarding]{}
	_setters["referralofferwidgetsduringonboarding"] = _obj.SetReferralOfferWidgetsDuringOnboarding

	_obj._DurationToSkipAddFundsForAffluenceClasses = &syncmap.Map[string, *DurationToSkipAddFundsForAffluenceClass]{}
	_setters["durationtoskipaddfundsforaffluenceclasses"] = _obj.SetDurationToSkipAddFundsForAffluenceClasses

	_obj._WebUrlsForSalaryB2BFlows = &syncmap.Map[string, *WebUrlForSalaryB2BFlow]{}
	_setters["weburlsforsalaryb2bflows"] = _obj.SetWebUrlsForSalaryB2BFlows

	_obj._AffluenceClassesEligibleForBonusTransitionScreen = &syncmap.Map[string, bool]{}
	_setters["affluenceclasseseligibleforbonustransitionscreen"] = _obj.SetAffluenceClassesEligibleForBonusTransitionScreen
	_setters["actorswhitelistedforprefunding"] = _obj.SetActorsWhitelistedForPreFunding
	_obj._ActorsWhitelistedForPreFundingMutex = &sync.RWMutex{}
	_setters["blockonboardingfromtime"] = _obj.SetBlockOnboardingFromTime
	_obj._BlockOnboardingFromTimeMutex = &sync.RWMutex{}
	_setters["blockonboardingtilltime"] = _obj.SetBlockOnboardingTillTime
	_obj._BlockOnboardingTillTimeMutex = &sync.RWMutex{}
	_setters["blockonboardingmsg"] = _obj.SetBlockOnboardingMsg
	_obj._BlockOnboardingMsgMutex = &sync.RWMutex{}
	_setters["finitecodefromattributionparamskey"] = _obj.SetFiniteCodeFromAttributionParamsKey
	_obj._FiniteCodeFromAttributionParamsKeyMutex = &sync.RWMutex{}
	_Flags, _fieldSetters := NewOnboardingFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_ABFeatureReleaseConfig, _fieldSetters := genconfig2.NewABFeatureReleaseConfig()
	_obj._ABFeatureReleaseConfig = _ABFeatureReleaseConfig
	helper.AddFieldSetters("abfeaturereleaseconfig", _fieldSetters, _setters)
	_OnbDetailsCacheConfig, _fieldSetters := NewOnbDetailsCacheConfig()
	_obj._OnbDetailsCacheConfig = _OnbDetailsCacheConfig
	helper.AddFieldSetters("onbdetailscacheconfig", _fieldSetters, _setters)
	_NextActionDecisionCacheConfig, _fieldSetters := NewNextActionDecisionCacheConfig()
	_obj._NextActionDecisionCacheConfig = _NextActionDecisionCacheConfig
	helper.AddFieldSetters("nextactiondecisioncacheconfig", _fieldSetters, _setters)
	_AppScreeningConfig, _fieldSetters := NewAppScreeningConfig()
	_obj._AppScreeningConfig = _AppScreeningConfig
	helper.AddFieldSetters("appscreeningconfig", _fieldSetters, _setters)
	_OnboardingVelocityConfig, _fieldSetters := NewOnboardingVelocityConfig()
	_obj._OnboardingVelocityConfig = _OnboardingVelocityConfig
	helper.AddFieldSetters("onboardingvelocityconfig", _fieldSetters, _setters)
	_ReferralOfferCodesABReleaseConfig, _fieldSetters := genconfig2.NewABFeatureReleaseConfig()
	_obj._ReferralOfferCodesABReleaseConfig = _ReferralOfferCodesABReleaseConfig
	helper.AddFieldSetters("referraloffercodesabreleaseconfig", _fieldSetters, _setters)
	_OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig, _fieldSetters := genconfig2.NewABFeatureReleaseConfig()
	_obj._OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig = _OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig
	helper.AddFieldSetters("offerwidgetonbscreensviafinitecodeabreleaseconfig", _fieldSetters, _setters)
	_EKYCCertUpgradeFeatureConfig, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EKYCCertUpgradeFeatureConfig = _EKYCCertUpgradeFeatureConfig
	helper.AddFieldSetters("ekyccertupgradefeatureconfig", _fieldSetters, _setters)
	_MinKycMandatoryAddFundConfig, _fieldSetters := NewMandatoryMinKycAddFundConfig()
	_obj._MinKycMandatoryAddFundConfig = _MinKycMandatoryAddFundConfig
	helper.AddFieldSetters("minkycmandatoryaddfundconfig", _fieldSetters, _setters)
	_EditEmploymentInScreener, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EditEmploymentInScreener = _EditEmploymentInScreener
	helper.AddFieldSetters("editemploymentinscreener", _fieldSetters, _setters)
	_AddFundsConfig, _fieldSetters := NewOnbAddFundsConfigWithQuest(questFieldPath + "/" + "AddFundsConfig")
	_obj._AddFundsConfig = _AddFundsConfig
	helper.AddFieldSetters("addfundsconfig", _fieldSetters, _setters)
	_ResetOnboardingJourney, _fieldSetters := NewResetOnboardingJourneyConfig()
	_obj._ResetOnboardingJourney = _ResetOnboardingJourney
	helper.AddFieldSetters("resetonboardingjourney", _fieldSetters, _setters)
	_ConfirmCardMailingAddress, _fieldSetters := NewConfirmCardMailingAddressConfig()
	_obj._ConfirmCardMailingAddress = _ConfirmCardMailingAddress
	helper.AddFieldSetters("confirmcardmailingaddress", _fieldSetters, _setters)
	_IntentSelectionConfigV2, _fieldSetters := NewIntentSelectionConfigV2()
	_obj._IntentSelectionConfigV2 = _IntentSelectionConfigV2
	helper.AddFieldSetters("intentselectionconfigv2", _fieldSetters, _setters)
	_SoftIntentSelectionConfig, _fieldSetters := NewSoftIntentSelectionConfigWithQuest(questFieldPath + "/" + "SoftIntentSelectionConfig")
	_obj._SoftIntentSelectionConfig = _SoftIntentSelectionConfig
	helper.AddFieldSetters("softintentselectionconfig", _fieldSetters, _setters)
	_DirectToFiLite, _fieldSetters := NewDirectToFiLiteConfigWithQuest(questFieldPath + "/" + "DirectToFiLite")
	_obj._DirectToFiLite = _DirectToFiLite
	helper.AddFieldSetters("directtofilite", _fieldSetters, _setters)
	_PanValidateV3FeatureConfig, _fieldSetters := genapp.NewFeatureConfig()
	_obj._PanValidateV3FeatureConfig = _PanValidateV3FeatureConfig
	helper.AddFieldSetters("panvalidatev3featureconfig", _fieldSetters, _setters)
	_ShowNewConsentInPanValidateV2, _fieldSetters := genapp.NewFeatureConfig()
	_obj._ShowNewConsentInPanValidateV2 = _ShowNewConsentInPanValidateV2
	helper.AddFieldSetters("shownewconsentinpanvalidatev2", _fieldSetters, _setters)
	_NonResidentCrossValidationConfig, _fieldSetters := NewCrossValidationConfig()
	_obj._NonResidentCrossValidationConfig = _NonResidentCrossValidationConfig
	helper.AddFieldSetters("nonresidentcrossvalidationconfig", _fieldSetters, _setters)
	_PassportVerificationConfig, _fieldSetters := NewPassportVerificationConfig()
	_obj._PassportVerificationConfig = _PassportVerificationConfig
	helper.AddFieldSetters("passportverificationconfig", _fieldSetters, _setters)
	_EnableTriggerNROAccountCreation, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableTriggerNROAccountCreation = _EnableTriggerNROAccountCreation
	helper.AddFieldSetters("enabletriggernroaccountcreation", _fieldSetters, _setters)
	_PrefillParentNameFromPassportOCR, _fieldSetters := genapp.NewFeatureConfig()
	_obj._PrefillParentNameFromPassportOCR = _PrefillParentNameFromPassportOCR
	helper.AddFieldSetters("prefillparentnamefrompassportocr", _fieldSetters, _setters)
	_OrderPhysicalDebitCardConfig, _fieldSetters := NewOrderPhysicalDebitCardConfigWithQuest(questFieldPath + "/" + "OrderPhysicalDebitCardConfig")
	_obj._OrderPhysicalDebitCardConfig = _OrderPhysicalDebitCardConfig
	helper.AddFieldSetters("orderphysicaldebitcardconfig", _fieldSetters, _setters)
	_UqudoCountryIdVerificationConfig, _fieldSetters := NewUqudoCountryIdVerificationConfig()
	_obj._UqudoCountryIdVerificationConfig = _UqudoCountryIdVerificationConfig
	helper.AddFieldSetters("uqudocountryidverificationconfig", _fieldSetters, _setters)
	_WalkthroughScreenConfig, _fieldSetters := NewWalkthroughScreenConfig()
	_obj._WalkthroughScreenConfig = _WalkthroughScreenConfig
	helper.AddFieldSetters("walkthroughscreenconfig", _fieldSetters, _setters)
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *OnboardingConfig) Init(questFieldPath string) {
	newObj, _ := NewOnboardingConfig()
	*obj = *newObj
}
func (obj *OnboardingConfig) InitWithQuest(questFieldPath string) {
	newObj, _ := NewOnboardingConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *OnboardingConfig) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
	obj._AddFundsConfig.SetQuestSDK(questSdk)
	obj._SoftIntentSelectionConfig.SetQuestSDK(questSdk)
	obj._DirectToFiLite.SetQuestSDK(questSdk)
	obj._OrderPhysicalDebitCardConfig.SetQuestSDK(questSdk)
}

func (obj *OnboardingConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OnboardingConfig) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "DurationToSkipAddMoney",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_DURATION},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	childVars, childVarsErr = obj._AddFundsConfig.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._SoftIntentSelectionConfig.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._DirectToFiLite.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._OrderPhysicalDebitCardConfig.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	return vars, nil
}

func (obj *OnboardingConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OnboardingConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OnboardingConfig) setDynamicField(v *config.OnboardingConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "minandroidversionformanualbalancerefreshonbaddfunds":
		return obj.SetMinAndroidVersionForManualBalanceRefreshOnbAddFunds(v.MinAndroidVersionForManualBalanceRefreshOnbAddFunds, true, nil)
	case "miniosversionformanualbalancerefreshonbaddfunds":
		return obj.SetMinIosVersionForManualBalanceRefreshOnbAddFunds(v.MinIosVersionForManualBalanceRefreshOnbAddFunds, true, nil)
	case "filiterolloutpercentage":
		return obj.SetFiLiteRolloutPercentage(v.FiLiteRolloutPercentage, true, nil)
	case "secureusageguidelineversion":
		return obj.SetSecureUsageGuidelineVersion(v.SecureUsageGuidelineVersion, true, nil)
	case "parentnameprefillfromckycrolloutpercentage":
		return obj.SetParentNamePrefillFromCKYCRolloutPercentage(v.ParentNamePrefillFromCKYCRolloutPercentage, true, nil)
	case "simulatebalancecheckfailureforaddfunds":
		return obj.SetSimulateBalanceCheckFailureForAddFunds(v.SimulateBalanceCheckFailureForAddFunds, true, nil)
	case "totalamountviaorderaddfunds":
		return obj.SetTotalAmountViaOrderAddFunds(v.TotalAmountViaOrderAddFunds, true, nil)
	case "blockonboardingduetounlinkedpanandaadhaar":
		return obj.SetBlockOnboardingDueToUnlinkedPANAndAadhaar(v.BlockOnboardingDueToUnlinkedPANAndAadhaar, true, nil)
	case "ispandobdropofftowealthanalyserenabled":
		return obj.SetIsPanDOBDropOffToWealthAnalyserEnabled(v.IsPanDOBDropOffToWealthAnalyserEnabled, true, nil)
	case "durationtoskipaddmoney":
		return obj.SetDurationToSkipAddMoney(v.DurationToSkipAddMoney, true, nil)
	case "riskscreeningexpiry":
		return obj.SetRiskScreeningExpiry(v.RiskScreeningExpiry, true, nil)
	case "livenesssummaryexpiryduration":
		return obj.SetLivenessSummaryExpiryDuration(v.LivenessSummaryExpiryDuration, true, nil)
	case "secureusageguidelineconsentinterval":
		return obj.SetSecureUsageGuidelineConsentInterval(v.SecureUsageGuidelineConsentInterval, true, nil)
	case "healthconfig":
		return obj.SetHealthConfig(v.HealthConfig, true, path)
	case "referraloffercodesduringonboarding":
		return obj.SetReferralOfferCodesDuringOnboarding(v.ReferralOfferCodesDuringOnboarding, true, path)
	case "referralofferwidgetsduringonboarding":
		return obj.SetReferralOfferWidgetsDuringOnboarding(v.ReferralOfferWidgetsDuringOnboarding, true, path)
	case "durationtoskipaddfundsforaffluenceclasses":
		return obj.SetDurationToSkipAddFundsForAffluenceClasses(v.DurationToSkipAddFundsForAffluenceClasses, true, path)
	case "weburlsforsalaryb2bflows":
		return obj.SetWebUrlsForSalaryB2BFlows(v.WebUrlsForSalaryB2BFlows, true, path)
	case "affluenceclasseseligibleforbonustransitionscreen":
		return obj.SetAffluenceClassesEligibleForBonusTransitionScreen(v.AffluenceClassesEligibleForBonusTransitionScreen, true, path)
	case "actorswhitelistedforprefunding":
		return obj.SetActorsWhitelistedForPreFunding(v.ActorsWhitelistedForPreFunding, true, path)
	case "blockonboardingfromtime":
		return obj.SetBlockOnboardingFromTime(v.BlockOnboardingFromTime, true, nil)
	case "blockonboardingtilltime":
		return obj.SetBlockOnboardingTillTime(v.BlockOnboardingTillTime, true, nil)
	case "blockonboardingmsg":
		return obj.SetBlockOnboardingMsg(v.BlockOnboardingMsg, true, nil)
	case "finitecodefromattributionparamskey":
		return obj.SetFiniteCodeFromAttributionParamsKey(v.FiniteCodeFromAttributionParamsKey, true, nil)
	case "flags":
		return obj._Flags.Set(v.Flags, true, path)
	case "abfeaturereleaseconfig":
		return obj._ABFeatureReleaseConfig.Set(v.ABFeatureReleaseConfig, true, path)
	case "onbdetailscacheconfig":
		return obj._OnbDetailsCacheConfig.Set(v.OnbDetailsCacheConfig, true, path)
	case "nextactiondecisioncacheconfig":
		return obj._NextActionDecisionCacheConfig.Set(v.NextActionDecisionCacheConfig, true, path)
	case "appscreeningconfig":
		return obj._AppScreeningConfig.Set(v.AppScreeningConfig, true, path)
	case "onboardingvelocityconfig":
		return obj._OnboardingVelocityConfig.Set(v.OnboardingVelocityConfig, true, path)
	case "referraloffercodesabreleaseconfig":
		return obj._ReferralOfferCodesABReleaseConfig.Set(v.ReferralOfferCodesABReleaseConfig, true, path)
	case "offerwidgetonbscreensviafinitecodeabreleaseconfig":
		return obj._OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig.Set(v.OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig, true, path)
	case "ekyccertupgradefeatureconfig":
		return obj._EKYCCertUpgradeFeatureConfig.Set(v.EKYCCertUpgradeFeatureConfig, true, path)
	case "minkycmandatoryaddfundconfig":
		return obj._MinKycMandatoryAddFundConfig.Set(v.MinKycMandatoryAddFundConfig, true, path)
	case "editemploymentinscreener":
		return obj._EditEmploymentInScreener.Set(v.EditEmploymentInScreener, true, path)
	case "addfundsconfig":
		return obj._AddFundsConfig.Set(v.AddFundsConfig, true, path)
	case "resetonboardingjourney":
		return obj._ResetOnboardingJourney.Set(v.ResetOnboardingJourney, true, path)
	case "confirmcardmailingaddress":
		return obj._ConfirmCardMailingAddress.Set(v.ConfirmCardMailingAddress, true, path)
	case "intentselectionconfigv2":
		return obj._IntentSelectionConfigV2.Set(v.IntentSelectionConfigV2, true, path)
	case "softintentselectionconfig":
		return obj._SoftIntentSelectionConfig.Set(v.SoftIntentSelectionConfig, true, path)
	case "directtofilite":
		return obj._DirectToFiLite.Set(v.DirectToFiLite, true, path)
	case "panvalidatev3featureconfig":
		return obj._PanValidateV3FeatureConfig.Set(v.PanValidateV3FeatureConfig, true, path)
	case "shownewconsentinpanvalidatev2":
		return obj._ShowNewConsentInPanValidateV2.Set(v.ShowNewConsentInPanValidateV2, true, path)
	case "nonresidentcrossvalidationconfig":
		return obj._NonResidentCrossValidationConfig.Set(v.NonResidentCrossValidationConfig, true, path)
	case "passportverificationconfig":
		return obj._PassportVerificationConfig.Set(v.PassportVerificationConfig, true, path)
	case "enabletriggernroaccountcreation":
		return obj._EnableTriggerNROAccountCreation.Set(v.EnableTriggerNROAccountCreation, true, path)
	case "prefillparentnamefrompassportocr":
		return obj._PrefillParentNameFromPassportOCR.Set(v.PrefillParentNameFromPassportOCR, true, path)
	case "orderphysicaldebitcardconfig":
		return obj._OrderPhysicalDebitCardConfig.Set(v.OrderPhysicalDebitCardConfig, true, path)
	case "uqudocountryidverificationconfig":
		return obj._UqudoCountryIdVerificationConfig.Set(v.UqudoCountryIdVerificationConfig, true, path)
	case "walkthroughscreenconfig":
		return obj._WalkthroughScreenConfig.Set(v.WalkthroughScreenConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OnboardingConfig) setDynamicFields(v *config.OnboardingConfig, dynamic bool, path []string) (err error) {

	err = obj.SetMinAndroidVersionForManualBalanceRefreshOnbAddFunds(v.MinAndroidVersionForManualBalanceRefreshOnbAddFunds, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinIosVersionForManualBalanceRefreshOnbAddFunds(v.MinIosVersionForManualBalanceRefreshOnbAddFunds, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFiLiteRolloutPercentage(v.FiLiteRolloutPercentage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSecureUsageGuidelineVersion(v.SecureUsageGuidelineVersion, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetParentNamePrefillFromCKYCRolloutPercentage(v.ParentNamePrefillFromCKYCRolloutPercentage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSimulateBalanceCheckFailureForAddFunds(v.SimulateBalanceCheckFailureForAddFunds, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTotalAmountViaOrderAddFunds(v.TotalAmountViaOrderAddFunds, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBlockOnboardingDueToUnlinkedPANAndAadhaar(v.BlockOnboardingDueToUnlinkedPANAndAadhaar, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsPanDOBDropOffToWealthAnalyserEnabled(v.IsPanDOBDropOffToWealthAnalyserEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDurationToSkipAddMoney(v.DurationToSkipAddMoney, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRiskScreeningExpiry(v.RiskScreeningExpiry, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLivenessSummaryExpiryDuration(v.LivenessSummaryExpiryDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSecureUsageGuidelineConsentInterval(v.SecureUsageGuidelineConsentInterval, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetHealthConfig(v.HealthConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetReferralOfferCodesDuringOnboarding(v.ReferralOfferCodesDuringOnboarding, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetReferralOfferWidgetsDuringOnboarding(v.ReferralOfferWidgetsDuringOnboarding, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetDurationToSkipAddFundsForAffluenceClasses(v.DurationToSkipAddFundsForAffluenceClasses, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetWebUrlsForSalaryB2BFlows(v.WebUrlsForSalaryB2BFlows, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetAffluenceClassesEligibleForBonusTransitionScreen(v.AffluenceClassesEligibleForBonusTransitionScreen, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetActorsWhitelistedForPreFunding(v.ActorsWhitelistedForPreFunding, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetBlockOnboardingFromTime(v.BlockOnboardingFromTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBlockOnboardingTillTime(v.BlockOnboardingTillTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBlockOnboardingMsg(v.BlockOnboardingMsg, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFiniteCodeFromAttributionParamsKey(v.FiniteCodeFromAttributionParamsKey, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._Flags.Set(v.Flags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ABFeatureReleaseConfig.Set(v.ABFeatureReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OnbDetailsCacheConfig.Set(v.OnbDetailsCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._NextActionDecisionCacheConfig.Set(v.NextActionDecisionCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AppScreeningConfig.Set(v.AppScreeningConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OnboardingVelocityConfig.Set(v.OnboardingVelocityConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ReferralOfferCodesABReleaseConfig.Set(v.ReferralOfferCodesABReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig.Set(v.OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EKYCCertUpgradeFeatureConfig.Set(v.EKYCCertUpgradeFeatureConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._MinKycMandatoryAddFundConfig.Set(v.MinKycMandatoryAddFundConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EditEmploymentInScreener.Set(v.EditEmploymentInScreener, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AddFundsConfig.Set(v.AddFundsConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ResetOnboardingJourney.Set(v.ResetOnboardingJourney, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ConfirmCardMailingAddress.Set(v.ConfirmCardMailingAddress, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._IntentSelectionConfigV2.Set(v.IntentSelectionConfigV2, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SoftIntentSelectionConfig.Set(v.SoftIntentSelectionConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DirectToFiLite.Set(v.DirectToFiLite, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PanValidateV3FeatureConfig.Set(v.PanValidateV3FeatureConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ShowNewConsentInPanValidateV2.Set(v.ShowNewConsentInPanValidateV2, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._NonResidentCrossValidationConfig.Set(v.NonResidentCrossValidationConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PassportVerificationConfig.Set(v.PassportVerificationConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableTriggerNROAccountCreation.Set(v.EnableTriggerNROAccountCreation, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PrefillParentNameFromPassportOCR.Set(v.PrefillParentNameFromPassportOCR, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OrderPhysicalDebitCardConfig.Set(v.OrderPhysicalDebitCardConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UqudoCountryIdVerificationConfig.Set(v.UqudoCountryIdVerificationConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._WalkthroughScreenConfig.Set(v.WalkthroughScreenConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *OnboardingConfig) setStaticFields(v *config.OnboardingConfig) error {

	obj._SyncOnboardingSqsPublisher = v.SyncOnboardingSqsPublisher
	obj._StuckUserNudges = v.StuckUserNudges
	obj._CCFiliteStuckUserNudges = v.CCFiliteStuckUserNudges
	obj._StuckUserAlerts = v.StuckUserAlerts
	obj._UNNameCheckMailingAddress = v.UNNameCheckMailingAddress
	obj._AWS = v.AWS
	obj._StageManualPassNotif = v.StageManualPassNotif
	obj._KYCDedupeRetryCount = v.KYCDedupeRetryCount
	obj._KYCNameUpdateNewSubTitleMinAndroid = v.KYCNameUpdateNewSubTitleMinAndroid
	obj._SyncOnboardingInterval = v.SyncOnboardingInterval
	obj._SyncOnboardingCutOff = v.SyncOnboardingCutOff
	obj._ActionOnReportVerificationTimeout = v.ActionOnReportVerificationTimeout
	obj._ActionOnReportNotFoundToDownload = v.ActionOnReportNotFoundToDownload
	obj._AddFundsRestrictionMap = v.AddFundsRestrictionMap
	obj._DedupeAPICacheTimeout = v.DedupeAPICacheTimeout
	obj._AccountSetupMaxStuckDuration = v.AccountSetupMaxStuckDuration
	obj._OrchestratorLockConfig = v.OrchestratorLockConfig
	obj._PhysicalCardChargesMailingAddressScreenParams = v.PhysicalCardChargesMailingAddressScreenParams
	obj._SkipAddFundsUserGroups = v.SkipAddFundsUserGroups
	obj._NrBucketName = v.NrBucketName
	return nil
}

func (obj *OnboardingConfig) SetMinAndroidVersionForManualBalanceRefreshOnbAddFunds(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.MinAndroidVersionForManualBalanceRefreshOnbAddFunds", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MinAndroidVersionForManualBalanceRefreshOnbAddFunds, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinAndroidVersionForManualBalanceRefreshOnbAddFunds")
	}
	return nil
}
func (obj *OnboardingConfig) SetMinIosVersionForManualBalanceRefreshOnbAddFunds(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.MinIosVersionForManualBalanceRefreshOnbAddFunds", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MinIosVersionForManualBalanceRefreshOnbAddFunds, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinIosVersionForManualBalanceRefreshOnbAddFunds")
	}
	return nil
}
func (obj *OnboardingConfig) SetFiLiteRolloutPercentage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.FiLiteRolloutPercentage", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._FiLiteRolloutPercentage, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "FiLiteRolloutPercentage")
	}
	return nil
}
func (obj *OnboardingConfig) SetSecureUsageGuidelineVersion(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.SecureUsageGuidelineVersion", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._SecureUsageGuidelineVersion, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SecureUsageGuidelineVersion")
	}
	return nil
}
func (obj *OnboardingConfig) SetParentNamePrefillFromCKYCRolloutPercentage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.ParentNamePrefillFromCKYCRolloutPercentage", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ParentNamePrefillFromCKYCRolloutPercentage, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ParentNamePrefillFromCKYCRolloutPercentage")
	}
	return nil
}
func (obj *OnboardingConfig) SetSimulateBalanceCheckFailureForAddFunds(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.SimulateBalanceCheckFailureForAddFunds", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SimulateBalanceCheckFailureForAddFunds, 1)
	} else {
		atomic.StoreUint32(&obj._SimulateBalanceCheckFailureForAddFunds, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SimulateBalanceCheckFailureForAddFunds")
	}
	return nil
}
func (obj *OnboardingConfig) SetTotalAmountViaOrderAddFunds(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.TotalAmountViaOrderAddFunds", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._TotalAmountViaOrderAddFunds, 1)
	} else {
		atomic.StoreUint32(&obj._TotalAmountViaOrderAddFunds, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "TotalAmountViaOrderAddFunds")
	}
	return nil
}
func (obj *OnboardingConfig) SetBlockOnboardingDueToUnlinkedPANAndAadhaar(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.BlockOnboardingDueToUnlinkedPANAndAadhaar", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._BlockOnboardingDueToUnlinkedPANAndAadhaar, 1)
	} else {
		atomic.StoreUint32(&obj._BlockOnboardingDueToUnlinkedPANAndAadhaar, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "BlockOnboardingDueToUnlinkedPANAndAadhaar")
	}
	return nil
}
func (obj *OnboardingConfig) SetIsPanDOBDropOffToWealthAnalyserEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.IsPanDOBDropOffToWealthAnalyserEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsPanDOBDropOffToWealthAnalyserEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsPanDOBDropOffToWealthAnalyserEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsPanDOBDropOffToWealthAnalyserEnabled")
	}
	return nil
}
func (obj *OnboardingConfig) SetDurationToSkipAddMoney(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.DurationToSkipAddMoney", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._DurationToSkipAddMoney, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "DurationToSkipAddMoney")
	}
	return nil
}
func (obj *OnboardingConfig) SetRiskScreeningExpiry(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.RiskScreeningExpiry", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._RiskScreeningExpiry, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RiskScreeningExpiry")
	}
	return nil
}
func (obj *OnboardingConfig) SetLivenessSummaryExpiryDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.LivenessSummaryExpiryDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._LivenessSummaryExpiryDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "LivenessSummaryExpiryDuration")
	}
	return nil
}
func (obj *OnboardingConfig) SetSecureUsageGuidelineConsentInterval(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.SecureUsageGuidelineConsentInterval", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._SecureUsageGuidelineConsentInterval, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SecureUsageGuidelineConsentInterval")
	}
	return nil
}
func (obj *OnboardingConfig) SetHealthConfig(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.ServiceHealth)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.HealthConfig", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._HealthConfig, v, dynamic, path)

}
func (obj *OnboardingConfig) SetReferralOfferCodesDuringOnboarding(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.ReferralOfferCodeDuringOnboarding)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.ReferralOfferCodesDuringOnboarding", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._ReferralOfferCodesDuringOnboarding, v, dynamic, path)

}
func (obj *OnboardingConfig) SetReferralOfferWidgetsDuringOnboarding(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.ReferralOfferWidgetDuringOnboarding)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.ReferralOfferWidgetsDuringOnboarding", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._ReferralOfferWidgetsDuringOnboarding, v, dynamic, path)

}
func (obj *OnboardingConfig) SetDurationToSkipAddFundsForAffluenceClasses(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.DurationToSkipAddFundsForAffluenceClass)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.DurationToSkipAddFundsForAffluenceClasses", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._DurationToSkipAddFundsForAffluenceClasses, v, dynamic, path)

}
func (obj *OnboardingConfig) SetWebUrlsForSalaryB2BFlows(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.WebUrlForSalaryB2BFlow)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.WebUrlsForSalaryB2BFlows", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._WebUrlsForSalaryB2BFlows, v, dynamic, path)

}
func (obj *OnboardingConfig) SetAffluenceClassesEligibleForBonusTransitionScreen(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.AffluenceClassesEligibleForBonusTransitionScreen", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._AffluenceClassesEligibleForBonusTransitionScreen, v, path)
}
func (obj *OnboardingConfig) SetActorsWhitelistedForPreFunding(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.ActorsWhitelistedForPreFunding", reflect.TypeOf(val))
	}
	obj._ActorsWhitelistedForPreFundingMutex.Lock()
	defer obj._ActorsWhitelistedForPreFundingMutex.Unlock()
	obj._ActorsWhitelistedForPreFunding = roarray.New[string](v)
	return nil
}
func (obj *OnboardingConfig) SetBlockOnboardingFromTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.BlockOnboardingFromTime", reflect.TypeOf(val))
	}
	obj._BlockOnboardingFromTimeMutex.Lock()
	defer obj._BlockOnboardingFromTimeMutex.Unlock()
	obj._BlockOnboardingFromTime = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BlockOnboardingFromTime")
	}
	return nil
}
func (obj *OnboardingConfig) SetBlockOnboardingTillTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.BlockOnboardingTillTime", reflect.TypeOf(val))
	}
	obj._BlockOnboardingTillTimeMutex.Lock()
	defer obj._BlockOnboardingTillTimeMutex.Unlock()
	obj._BlockOnboardingTillTime = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BlockOnboardingTillTime")
	}
	return nil
}
func (obj *OnboardingConfig) SetBlockOnboardingMsg(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.BlockOnboardingMsg", reflect.TypeOf(val))
	}
	obj._BlockOnboardingMsgMutex.Lock()
	defer obj._BlockOnboardingMsgMutex.Unlock()
	obj._BlockOnboardingMsg = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BlockOnboardingMsg")
	}
	return nil
}
func (obj *OnboardingConfig) SetFiniteCodeFromAttributionParamsKey(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingConfig.FiniteCodeFromAttributionParamsKey", reflect.TypeOf(val))
	}
	obj._FiniteCodeFromAttributionParamsKeyMutex.Lock()
	defer obj._FiniteCodeFromAttributionParamsKeyMutex.Unlock()
	obj._FiniteCodeFromAttributionParamsKey = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FiniteCodeFromAttributionParamsKey")
	}
	return nil
}

func NewOnboardingFlags() (_obj *OnboardingFlags, _setters map[string]dynconf.SetFunc) {
	_obj = &OnboardingFlags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ignoreerrorsingetdataforcrossvalidationmanualreview"] = _obj.SetIgnoreErrorsInGetDataForCrossValidationManualReview
	_setters["enablesecureusageguidelinesconsent"] = _obj.SetEnableSecureUsageGuidelinesConsent
	_setters["allowmanualreviewusers"] = _obj.SetAllowManualReviewUsers
	_setters["enableaffluencev2"] = _obj.SetEnableAffluenceV2
	_setters["blockccuserforpanlinkage"] = _obj.SetBlockCCUserForPANLinkage
	_setters["markckycsuccesswithoutckycdownload"] = _obj.SetMarkCKYCSuccessWithoutCKYCDownload
	_setters["enableupdateprofiledetailsstage"] = _obj.SetEnableUpdateProfileDetailsStage
	_setters["enablegnoaonerror"] = _obj.SetEnableGNOAOnError
	_setters["enablenonresidentonboardingcrossvalidation"] = _obj.SetEnableNonResidentOnboardingCrossValidation
	_setters["skiplocationcheckfornronboarding"] = _obj.SetSkipLocationCheckForNROnboarding
	_setters["skipcountryidverification"] = _obj.SetSkipCountryIdVerification
	_setters["skippassportverification"] = _obj.SetSkipPassportVerification
	_setters["enableriskcheckfornruser"] = _obj.SetEnableRiskCheckForNRUser
	_setters["enableriskscreeningford2h"] = _obj.SetEnableRiskScreeningForD2H
	_setters["enableparentnameprefillfromckyc"] = _obj.SetEnableParentNamePrefillFromCKYC
	_setters["enableckyc"] = _obj.SetEnableCkyc
	_setters["allownridedupeusers"] = _obj.SetAllowNRIDedupeUsers
	_setters["enableglobalissuedpassportarnflow"] = _obj.SetEnableGlobalIssuedPassportARNFlow
	_setters["blocknronboarding"] = _obj.SetBlockNrOnboarding
	_setters["enablepanaadharcheckinprecustomercreationcheckstage"] = _obj.SetEnablePanAadharCheckInPreCustomerCreationCheckStage
	_setters["enablesadeclarationstage"] = _obj.SetEnableSaDeclarationstage

	_obj._DisabledStages = &syncmap.Map[string, bool]{}
	_setters["disabledstages"] = _obj.SetDisabledStages
	_UseNewLivenessFlow, _fieldSetters := genapp.NewFeatureConfig()
	_obj._UseNewLivenessFlow = _UseNewLivenessFlow
	helper.AddFieldSetters("usenewlivenessflow", _fieldSetters, _setters)
	_EnableSavingsIntroScreen, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableSavingsIntroScreen = _EnableSavingsIntroScreen
	helper.AddFieldSetters("enablesavingsintroscreen", _fieldSetters, _setters)
	_EnableSMSParserConsentScreen, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableSMSParserConsentScreen = _EnableSMSParserConsentScreen
	helper.AddFieldSetters("enablesmsparserconsentscreen", _fieldSetters, _setters)
	_WealthAnalyserFeature, _fieldSetters := genapp.NewFeatureConfig()
	_obj._WealthAnalyserFeature = _WealthAnalyserFeature
	helper.AddFieldSetters("wealthanalyserfeature", _fieldSetters, _setters)
	_EnableContactPermissionInOnb, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableContactPermissionInOnb = _EnableContactPermissionInOnb
	helper.AddFieldSetters("enablecontactpermissioninonb", _fieldSetters, _setters)
	_EnableGlobalIssuedPassportVerification, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableGlobalIssuedPassportVerification = _EnableGlobalIssuedPassportVerification
	helper.AddFieldSetters("enableglobalissuedpassportverification", _fieldSetters, _setters)
	_EnablePermissionStage, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnablePermissionStage = _EnablePermissionStage
	helper.AddFieldSetters("enablepermissionstage", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *OnboardingFlags) Init() {
	newObj, _ := NewOnboardingFlags()
	*obj = *newObj
}

func (obj *OnboardingFlags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OnboardingFlags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OnboardingFlags)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OnboardingFlags) setDynamicField(v *config.OnboardingFlags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "ignoreerrorsingetdataforcrossvalidationmanualreview":
		return obj.SetIgnoreErrorsInGetDataForCrossValidationManualReview(v.IgnoreErrorsInGetDataForCrossValidationManualReview, true, nil)
	case "enablesecureusageguidelinesconsent":
		return obj.SetEnableSecureUsageGuidelinesConsent(v.EnableSecureUsageGuidelinesConsent, true, nil)
	case "allowmanualreviewusers":
		return obj.SetAllowManualReviewUsers(v.AllowManualReviewUsers, true, nil)
	case "enableaffluencev2":
		return obj.SetEnableAffluenceV2(v.EnableAffluenceV2, true, nil)
	case "blockccuserforpanlinkage":
		return obj.SetBlockCCUserForPANLinkage(v.BlockCCUserForPANLinkage, true, nil)
	case "markckycsuccesswithoutckycdownload":
		return obj.SetMarkCKYCSuccessWithoutCKYCDownload(v.MarkCKYCSuccessWithoutCKYCDownload, true, nil)
	case "enableupdateprofiledetailsstage":
		return obj.SetEnableUpdateProfileDetailsStage(v.EnableUpdateProfileDetailsStage, true, nil)
	case "enablegnoaonerror":
		return obj.SetEnableGNOAOnError(v.EnableGNOAOnError, true, nil)
	case "enablenonresidentonboardingcrossvalidation":
		return obj.SetEnableNonResidentOnboardingCrossValidation(v.EnableNonResidentOnboardingCrossValidation, true, nil)
	case "skiplocationcheckfornronboarding":
		return obj.SetSkipLocationCheckForNROnboarding(v.SkipLocationCheckForNROnboarding, true, nil)
	case "skipcountryidverification":
		return obj.SetSkipCountryIdVerification(v.SkipCountryIdVerification, true, nil)
	case "skippassportverification":
		return obj.SetSkipPassportVerification(v.SkipPassportVerification, true, nil)
	case "enableriskcheckfornruser":
		return obj.SetEnableRiskCheckForNRUser(v.EnableRiskCheckForNRUser, true, nil)
	case "enableriskscreeningford2h":
		return obj.SetEnableRiskScreeningForD2H(v.EnableRiskScreeningForD2H, true, nil)
	case "enableparentnameprefillfromckyc":
		return obj.SetEnableParentNamePrefillFromCKYC(v.EnableParentNamePrefillFromCKYC, true, nil)
	case "enableckyc":
		return obj.SetEnableCkyc(v.EnableCkyc, true, nil)
	case "allownridedupeusers":
		return obj.SetAllowNRIDedupeUsers(v.AllowNRIDedupeUsers, true, nil)
	case "enableglobalissuedpassportarnflow":
		return obj.SetEnableGlobalIssuedPassportARNFlow(v.EnableGlobalIssuedPassportARNFlow, true, nil)
	case "blocknronboarding":
		return obj.SetBlockNrOnboarding(v.BlockNrOnboarding, true, nil)
	case "enablepanaadharcheckinprecustomercreationcheckstage":
		return obj.SetEnablePanAadharCheckInPreCustomerCreationCheckStage(v.EnablePanAadharCheckInPreCustomerCreationCheckStage, true, nil)
	case "enablesadeclarationstage":
		return obj.SetEnableSaDeclarationstage(v.EnableSaDeclarationstage, true, nil)
	case "disabledstages":
		return obj.SetDisabledStages(v.DisabledStages, true, path)
	case "usenewlivenessflow":
		return obj._UseNewLivenessFlow.Set(v.UseNewLivenessFlow, true, path)
	case "enablesavingsintroscreen":
		return obj._EnableSavingsIntroScreen.Set(v.EnableSavingsIntroScreen, true, path)
	case "enablesmsparserconsentscreen":
		return obj._EnableSMSParserConsentScreen.Set(v.EnableSMSParserConsentScreen, true, path)
	case "wealthanalyserfeature":
		return obj._WealthAnalyserFeature.Set(v.WealthAnalyserFeature, true, path)
	case "enablecontactpermissioninonb":
		return obj._EnableContactPermissionInOnb.Set(v.EnableContactPermissionInOnb, true, path)
	case "enableglobalissuedpassportverification":
		return obj._EnableGlobalIssuedPassportVerification.Set(v.EnableGlobalIssuedPassportVerification, true, path)
	case "enablepermissionstage":
		return obj._EnablePermissionStage.Set(v.EnablePermissionStage, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OnboardingFlags) setDynamicFields(v *config.OnboardingFlags, dynamic bool, path []string) (err error) {

	err = obj.SetIgnoreErrorsInGetDataForCrossValidationManualReview(v.IgnoreErrorsInGetDataForCrossValidationManualReview, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableSecureUsageGuidelinesConsent(v.EnableSecureUsageGuidelinesConsent, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAllowManualReviewUsers(v.AllowManualReviewUsers, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableAffluenceV2(v.EnableAffluenceV2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBlockCCUserForPANLinkage(v.BlockCCUserForPANLinkage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMarkCKYCSuccessWithoutCKYCDownload(v.MarkCKYCSuccessWithoutCKYCDownload, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableUpdateProfileDetailsStage(v.EnableUpdateProfileDetailsStage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableGNOAOnError(v.EnableGNOAOnError, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableNonResidentOnboardingCrossValidation(v.EnableNonResidentOnboardingCrossValidation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSkipLocationCheckForNROnboarding(v.SkipLocationCheckForNROnboarding, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSkipCountryIdVerification(v.SkipCountryIdVerification, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSkipPassportVerification(v.SkipPassportVerification, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableRiskCheckForNRUser(v.EnableRiskCheckForNRUser, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableRiskScreeningForD2H(v.EnableRiskScreeningForD2H, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableParentNamePrefillFromCKYC(v.EnableParentNamePrefillFromCKYC, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableCkyc(v.EnableCkyc, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAllowNRIDedupeUsers(v.AllowNRIDedupeUsers, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableGlobalIssuedPassportARNFlow(v.EnableGlobalIssuedPassportARNFlow, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBlockNrOnboarding(v.BlockNrOnboarding, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnablePanAadharCheckInPreCustomerCreationCheckStage(v.EnablePanAadharCheckInPreCustomerCreationCheckStage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableSaDeclarationstage(v.EnableSaDeclarationstage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisabledStages(v.DisabledStages, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UseNewLivenessFlow.Set(v.UseNewLivenessFlow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableSavingsIntroScreen.Set(v.EnableSavingsIntroScreen, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableSMSParserConsentScreen.Set(v.EnableSMSParserConsentScreen, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._WealthAnalyserFeature.Set(v.WealthAnalyserFeature, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableContactPermissionInOnb.Set(v.EnableContactPermissionInOnb, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableGlobalIssuedPassportVerification.Set(v.EnableGlobalIssuedPassportVerification, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnablePermissionStage.Set(v.EnablePermissionStage, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *OnboardingFlags) setStaticFields(v *config.OnboardingFlags) error {

	obj._AddMoneyBalanceOptions = v.AddMoneyBalanceOptions
	obj._EnableSyncOnboarding = v.EnableSyncOnboarding
	obj._MangleRawCreditReportData = v.MangleRawCreditReportData
	obj._InhouseNamematchThreshold = v.InhouseNamematchThreshold
	obj._InhouseNamematchFailureThreshold = v.InhouseNamematchFailureThreshold
	return nil
}

func (obj *OnboardingFlags) SetIgnoreErrorsInGetDataForCrossValidationManualReview(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.IgnoreErrorsInGetDataForCrossValidationManualReview", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IgnoreErrorsInGetDataForCrossValidationManualReview, 1)
	} else {
		atomic.StoreUint32(&obj._IgnoreErrorsInGetDataForCrossValidationManualReview, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IgnoreErrorsInGetDataForCrossValidationManualReview")
	}
	return nil
}
func (obj *OnboardingFlags) SetEnableSecureUsageGuidelinesConsent(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.EnableSecureUsageGuidelinesConsent", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableSecureUsageGuidelinesConsent, 1)
	} else {
		atomic.StoreUint32(&obj._EnableSecureUsageGuidelinesConsent, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableSecureUsageGuidelinesConsent")
	}
	return nil
}
func (obj *OnboardingFlags) SetAllowManualReviewUsers(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.AllowManualReviewUsers", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._AllowManualReviewUsers, 1)
	} else {
		atomic.StoreUint32(&obj._AllowManualReviewUsers, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "AllowManualReviewUsers")
	}
	return nil
}
func (obj *OnboardingFlags) SetEnableAffluenceV2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.EnableAffluenceV2", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableAffluenceV2, 1)
	} else {
		atomic.StoreUint32(&obj._EnableAffluenceV2, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableAffluenceV2")
	}
	return nil
}
func (obj *OnboardingFlags) SetBlockCCUserForPANLinkage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.BlockCCUserForPANLinkage", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._BlockCCUserForPANLinkage, 1)
	} else {
		atomic.StoreUint32(&obj._BlockCCUserForPANLinkage, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "BlockCCUserForPANLinkage")
	}
	return nil
}
func (obj *OnboardingFlags) SetMarkCKYCSuccessWithoutCKYCDownload(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.MarkCKYCSuccessWithoutCKYCDownload", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._MarkCKYCSuccessWithoutCKYCDownload, 1)
	} else {
		atomic.StoreUint32(&obj._MarkCKYCSuccessWithoutCKYCDownload, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "MarkCKYCSuccessWithoutCKYCDownload")
	}
	return nil
}
func (obj *OnboardingFlags) SetEnableUpdateProfileDetailsStage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.EnableUpdateProfileDetailsStage", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableUpdateProfileDetailsStage, 1)
	} else {
		atomic.StoreUint32(&obj._EnableUpdateProfileDetailsStage, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableUpdateProfileDetailsStage")
	}
	return nil
}
func (obj *OnboardingFlags) SetEnableGNOAOnError(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.EnableGNOAOnError", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableGNOAOnError, 1)
	} else {
		atomic.StoreUint32(&obj._EnableGNOAOnError, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableGNOAOnError")
	}
	return nil
}
func (obj *OnboardingFlags) SetEnableNonResidentOnboardingCrossValidation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.EnableNonResidentOnboardingCrossValidation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableNonResidentOnboardingCrossValidation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableNonResidentOnboardingCrossValidation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableNonResidentOnboardingCrossValidation")
	}
	return nil
}
func (obj *OnboardingFlags) SetSkipLocationCheckForNROnboarding(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.SkipLocationCheckForNROnboarding", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SkipLocationCheckForNROnboarding, 1)
	} else {
		atomic.StoreUint32(&obj._SkipLocationCheckForNROnboarding, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SkipLocationCheckForNROnboarding")
	}
	return nil
}
func (obj *OnboardingFlags) SetSkipCountryIdVerification(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.SkipCountryIdVerification", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SkipCountryIdVerification, 1)
	} else {
		atomic.StoreUint32(&obj._SkipCountryIdVerification, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SkipCountryIdVerification")
	}
	return nil
}
func (obj *OnboardingFlags) SetSkipPassportVerification(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.SkipPassportVerification", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SkipPassportVerification, 1)
	} else {
		atomic.StoreUint32(&obj._SkipPassportVerification, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SkipPassportVerification")
	}
	return nil
}
func (obj *OnboardingFlags) SetEnableRiskCheckForNRUser(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.EnableRiskCheckForNRUser", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableRiskCheckForNRUser, 1)
	} else {
		atomic.StoreUint32(&obj._EnableRiskCheckForNRUser, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableRiskCheckForNRUser")
	}
	return nil
}
func (obj *OnboardingFlags) SetEnableRiskScreeningForD2H(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.EnableRiskScreeningForD2H", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableRiskScreeningForD2H, 1)
	} else {
		atomic.StoreUint32(&obj._EnableRiskScreeningForD2H, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableRiskScreeningForD2H")
	}
	return nil
}
func (obj *OnboardingFlags) SetEnableParentNamePrefillFromCKYC(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.EnableParentNamePrefillFromCKYC", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableParentNamePrefillFromCKYC, 1)
	} else {
		atomic.StoreUint32(&obj._EnableParentNamePrefillFromCKYC, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableParentNamePrefillFromCKYC")
	}
	return nil
}
func (obj *OnboardingFlags) SetEnableCkyc(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.EnableCkyc", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableCkyc, 1)
	} else {
		atomic.StoreUint32(&obj._EnableCkyc, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableCkyc")
	}
	return nil
}
func (obj *OnboardingFlags) SetAllowNRIDedupeUsers(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.AllowNRIDedupeUsers", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._AllowNRIDedupeUsers, 1)
	} else {
		atomic.StoreUint32(&obj._AllowNRIDedupeUsers, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "AllowNRIDedupeUsers")
	}
	return nil
}
func (obj *OnboardingFlags) SetEnableGlobalIssuedPassportARNFlow(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.EnableGlobalIssuedPassportARNFlow", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableGlobalIssuedPassportARNFlow, 1)
	} else {
		atomic.StoreUint32(&obj._EnableGlobalIssuedPassportARNFlow, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableGlobalIssuedPassportARNFlow")
	}
	return nil
}
func (obj *OnboardingFlags) SetBlockNrOnboarding(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.BlockNrOnboarding", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._BlockNrOnboarding, 1)
	} else {
		atomic.StoreUint32(&obj._BlockNrOnboarding, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "BlockNrOnboarding")
	}
	return nil
}
func (obj *OnboardingFlags) SetEnablePanAadharCheckInPreCustomerCreationCheckStage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.EnablePanAadharCheckInPreCustomerCreationCheckStage", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnablePanAadharCheckInPreCustomerCreationCheckStage, 1)
	} else {
		atomic.StoreUint32(&obj._EnablePanAadharCheckInPreCustomerCreationCheckStage, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnablePanAadharCheckInPreCustomerCreationCheckStage")
	}
	return nil
}
func (obj *OnboardingFlags) SetEnableSaDeclarationstage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.EnableSaDeclarationstage", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableSaDeclarationstage, 1)
	} else {
		atomic.StoreUint32(&obj._EnableSaDeclarationstage, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableSaDeclarationstage")
	}
	return nil
}
func (obj *OnboardingFlags) SetDisabledStages(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingFlags.DisabledStages", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._DisabledStages, v, path)
}

func NewOnbDetailsCacheConfig() (_obj *OnbDetailsCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &OnbDetailsCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled
	_OnbDetailsMinCacheConfig, _fieldSetters := NewOnbDetailsMinCacheConfig()
	_obj._OnbDetailsMinCacheConfig = _OnbDetailsMinCacheConfig
	helper.AddFieldSetters("onbdetailsmincacheconfig", _fieldSetters, _setters)
	_OnbDetailsTTLConfig, _fieldSetters := NewOnbDetailsTTLConfig()
	_obj._OnbDetailsTTLConfig = _OnbDetailsTTLConfig
	helper.AddFieldSetters("onbdetailsttlconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *OnbDetailsCacheConfig) Init() {
	newObj, _ := NewOnbDetailsCacheConfig()
	*obj = *newObj
}

func (obj *OnbDetailsCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OnbDetailsCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OnbDetailsCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnbDetailsCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OnbDetailsCacheConfig) setDynamicField(v *config.OnbDetailsCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	case "onbdetailsmincacheconfig":
		return obj._OnbDetailsMinCacheConfig.Set(v.OnbDetailsMinCacheConfig, true, path)
	case "onbdetailsttlconfig":
		return obj._OnbDetailsTTLConfig.Set(v.OnbDetailsTTLConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OnbDetailsCacheConfig) setDynamicFields(v *config.OnbDetailsCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._OnbDetailsMinCacheConfig.Set(v.OnbDetailsMinCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OnbDetailsTTLConfig.Set(v.OnbDetailsTTLConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *OnbDetailsCacheConfig) setStaticFields(v *config.OnbDetailsCacheConfig) error {

	return nil
}

func (obj *OnbDetailsCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnbDetailsCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}

func NewOnbDetailsMinCacheConfig() (_obj *OnbDetailsMinCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &OnbDetailsMinCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["onbdoneminttl"] = _obj.SetOnbDoneMinTTL
	_setters["onbinprogressminttl"] = _obj.SetOnbInProgressMinTTL
	_setters["actortoonbttl"] = _obj.SetActorToOnbTTL
	return _obj, _setters
}

func (obj *OnbDetailsMinCacheConfig) Init() {
	newObj, _ := NewOnbDetailsMinCacheConfig()
	*obj = *newObj
}

func (obj *OnbDetailsMinCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OnbDetailsMinCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OnbDetailsMinCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnbDetailsMinCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OnbDetailsMinCacheConfig) setDynamicField(v *config.OnbDetailsMinCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "onbdoneminttl":
		return obj.SetOnbDoneMinTTL(v.OnbDoneMinTTL, true, nil)
	case "onbinprogressminttl":
		return obj.SetOnbInProgressMinTTL(v.OnbInProgressMinTTL, true, nil)
	case "actortoonbttl":
		return obj.SetActorToOnbTTL(v.ActorToOnbTTL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OnbDetailsMinCacheConfig) setDynamicFields(v *config.OnbDetailsMinCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetOnbDoneMinTTL(v.OnbDoneMinTTL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOnbInProgressMinTTL(v.OnbInProgressMinTTL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetActorToOnbTTL(v.ActorToOnbTTL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *OnbDetailsMinCacheConfig) setStaticFields(v *config.OnbDetailsMinCacheConfig) error {

	return nil
}

func (obj *OnbDetailsMinCacheConfig) SetOnbDoneMinTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnbDetailsMinCacheConfig.OnbDoneMinTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._OnbDoneMinTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "OnbDoneMinTTL")
	}
	return nil
}
func (obj *OnbDetailsMinCacheConfig) SetOnbInProgressMinTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnbDetailsMinCacheConfig.OnbInProgressMinTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._OnbInProgressMinTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "OnbInProgressMinTTL")
	}
	return nil
}
func (obj *OnbDetailsMinCacheConfig) SetActorToOnbTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnbDetailsMinCacheConfig.ActorToOnbTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ActorToOnbTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ActorToOnbTTL")
	}
	return nil
}

func NewOnbDetailsTTLConfig() (_obj *OnbDetailsTTLConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &OnbDetailsTTLConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["onbdonettl"] = _obj.SetOnbDoneTTL
	_setters["onbinprogressttl"] = _obj.SetOnbInProgressTTL
	return _obj, _setters
}

func (obj *OnbDetailsTTLConfig) Init() {
	newObj, _ := NewOnbDetailsTTLConfig()
	*obj = *newObj
}

func (obj *OnbDetailsTTLConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OnbDetailsTTLConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OnbDetailsTTLConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnbDetailsTTLConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OnbDetailsTTLConfig) setDynamicField(v *config.OnbDetailsTTLConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "onbdonettl":
		return obj.SetOnbDoneTTL(v.OnbDoneTTL, true, nil)
	case "onbinprogressttl":
		return obj.SetOnbInProgressTTL(v.OnbInProgressTTL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OnbDetailsTTLConfig) setDynamicFields(v *config.OnbDetailsTTLConfig, dynamic bool, path []string) (err error) {

	err = obj.SetOnbDoneTTL(v.OnbDoneTTL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOnbInProgressTTL(v.OnbInProgressTTL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *OnbDetailsTTLConfig) setStaticFields(v *config.OnbDetailsTTLConfig) error {

	return nil
}

func (obj *OnbDetailsTTLConfig) SetOnbDoneTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnbDetailsTTLConfig.OnbDoneTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._OnbDoneTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "OnbDoneTTL")
	}
	return nil
}
func (obj *OnbDetailsTTLConfig) SetOnbInProgressTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnbDetailsTTLConfig.OnbInProgressTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._OnbInProgressTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "OnbInProgressTTL")
	}
	return nil
}

func NewNextActionDecisionCacheConfig() (_obj *NextActionDecisionCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &NextActionDecisionCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscacheenabled"] = _obj.SetIsCacheEnabled
	_setters["cachettl"] = _obj.SetCacheTTL
	return _obj, _setters
}

func (obj *NextActionDecisionCacheConfig) Init() {
	newObj, _ := NewNextActionDecisionCacheConfig()
	*obj = *newObj
}

func (obj *NextActionDecisionCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *NextActionDecisionCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.NextActionDecisionCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *NextActionDecisionCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *NextActionDecisionCacheConfig) setDynamicField(v *config.NextActionDecisionCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscacheenabled":
		return obj.SetIsCacheEnabled(v.IsCacheEnabled, true, nil)
	case "cachettl":
		return obj.SetCacheTTL(v.CacheTTL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *NextActionDecisionCacheConfig) setDynamicFields(v *config.NextActionDecisionCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCacheEnabled(v.IsCacheEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCacheTTL(v.CacheTTL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *NextActionDecisionCacheConfig) setStaticFields(v *config.NextActionDecisionCacheConfig) error {

	obj._NextActionDecisionCachePrefix = v.NextActionDecisionCachePrefix
	return nil
}

func (obj *NextActionDecisionCacheConfig) SetIsCacheEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *NextActionDecisionCacheConfig.IsCacheEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCacheEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCacheEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCacheEnabled")
	}
	return nil
}
func (obj *NextActionDecisionCacheConfig) SetCacheTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *NextActionDecisionCacheConfig.CacheTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CacheTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CacheTTL")
	}
	return nil
}

func NewAppScreeningConfig() (_obj *AppScreeningConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &AppScreeningConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablechatbotinchoicescreen"] = _obj.SetEnableChatbotInChoiceScreen
	_setters["enablefiliteentrypointinchoicescreen"] = _obj.SetEnableFiLiteEntryPointInChoiceScreen
	_setters["forcescreenercheckttl"] = _obj.SetForceScreenerCheckTTL
	_UANCheckConfig, _fieldSetters := NewUANCheckConfig()
	_obj._UANCheckConfig = _UANCheckConfig
	helper.AddFieldSetters("uancheckconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *AppScreeningConfig) Init() {
	newObj, _ := NewAppScreeningConfig()
	*obj = *newObj
}

func (obj *AppScreeningConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AppScreeningConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AppScreeningConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *AppScreeningConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AppScreeningConfig) setDynamicField(v *config.AppScreeningConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablechatbotinchoicescreen":
		return obj.SetEnableChatbotInChoiceScreen(v.EnableChatbotInChoiceScreen, true, nil)
	case "enablefiliteentrypointinchoicescreen":
		return obj.SetEnableFiLiteEntryPointInChoiceScreen(v.EnableFiLiteEntryPointInChoiceScreen, true, nil)
	case "forcescreenercheckttl":
		return obj.SetForceScreenerCheckTTL(v.ForceScreenerCheckTTL, true, nil)
	case "uancheckconfig":
		return obj._UANCheckConfig.Set(v.UANCheckConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AppScreeningConfig) setDynamicFields(v *config.AppScreeningConfig, dynamic bool, path []string) (err error) {

	err = obj.SetEnableChatbotInChoiceScreen(v.EnableChatbotInChoiceScreen, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableFiLiteEntryPointInChoiceScreen(v.EnableFiLiteEntryPointInChoiceScreen, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetForceScreenerCheckTTL(v.ForceScreenerCheckTTL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._UANCheckConfig.Set(v.UANCheckConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AppScreeningConfig) setStaticFields(v *config.AppScreeningConfig) error {

	return nil
}

func (obj *AppScreeningConfig) SetEnableChatbotInChoiceScreen(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AppScreeningConfig.EnableChatbotInChoiceScreen", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableChatbotInChoiceScreen, 1)
	} else {
		atomic.StoreUint32(&obj._EnableChatbotInChoiceScreen, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableChatbotInChoiceScreen")
	}
	return nil
}
func (obj *AppScreeningConfig) SetEnableFiLiteEntryPointInChoiceScreen(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AppScreeningConfig.EnableFiLiteEntryPointInChoiceScreen", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableFiLiteEntryPointInChoiceScreen, 1)
	} else {
		atomic.StoreUint32(&obj._EnableFiLiteEntryPointInChoiceScreen, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableFiLiteEntryPointInChoiceScreen")
	}
	return nil
}
func (obj *AppScreeningConfig) SetForceScreenerCheckTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *AppScreeningConfig.ForceScreenerCheckTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ForceScreenerCheckTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ForceScreenerCheckTTL")
	}
	return nil
}

func NewUANCheckConfig() (_obj *UANCheckConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &UANCheckConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["creditscorethreshold"] = _obj.SetCreditScoreThreshold
	return _obj, _setters
}

func (obj *UANCheckConfig) Init() {
	newObj, _ := NewUANCheckConfig()
	*obj = *newObj
}

func (obj *UANCheckConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *UANCheckConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.UANCheckConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *UANCheckConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *UANCheckConfig) setDynamicField(v *config.UANCheckConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "creditscorethreshold":
		return obj.SetCreditScoreThreshold(v.CreditScoreThreshold, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *UANCheckConfig) setDynamicFields(v *config.UANCheckConfig, dynamic bool, path []string) (err error) {

	err = obj.SetCreditScoreThreshold(v.CreditScoreThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *UANCheckConfig) setStaticFields(v *config.UANCheckConfig) error {

	return nil
}

func (obj *UANCheckConfig) SetCreditScoreThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *UANCheckConfig.CreditScoreThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._CreditScoreThreshold, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CreditScoreThreshold")
	}
	return nil
}

func NewServiceHealth() (_obj *ServiceHealth, _setters map[string]dynconf.SetFunc) {
	_obj = &ServiceHealth{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["healthstatus"] = _obj.SetHealthStatus
	_setters["from"] = _obj.SetFrom
	_obj._FromMutex = &sync.RWMutex{}
	_setters["to"] = _obj.SetTo
	_obj._ToMutex = &sync.RWMutex{}
	_setters["message"] = _obj.SetMessage
	_obj._MessageMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *ServiceHealth) Init() {
	newObj, _ := NewServiceHealth()
	*obj = *newObj
}

func (obj *ServiceHealth) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ServiceHealth) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ServiceHealth)
	if !ok {
		return fmt.Errorf("invalid data type %v *ServiceHealth", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ServiceHealth) setDynamicField(v *config.ServiceHealth, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "healthstatus":
		return obj.SetHealthStatus(v.HealthStatus, true, nil)
	case "from":
		return obj.SetFrom(v.From, true, nil)
	case "to":
		return obj.SetTo(v.To, true, nil)
	case "message":
		return obj.SetMessage(v.Message, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ServiceHealth) setDynamicFields(v *config.ServiceHealth, dynamic bool, path []string) (err error) {

	err = obj.SetHealthStatus(v.HealthStatus, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFrom(v.From, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTo(v.To, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMessage(v.Message, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ServiceHealth) setStaticFields(v *config.ServiceHealth) error {

	return nil
}

func (obj *ServiceHealth) SetHealthStatus(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *ServiceHealth.HealthStatus", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._HealthStatus, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "HealthStatus")
	}
	return nil
}
func (obj *ServiceHealth) SetFrom(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ServiceHealth.From", reflect.TypeOf(val))
	}
	obj._FromMutex.Lock()
	defer obj._FromMutex.Unlock()
	obj._From = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "From")
	}
	return nil
}
func (obj *ServiceHealth) SetTo(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ServiceHealth.To", reflect.TypeOf(val))
	}
	obj._ToMutex.Lock()
	defer obj._ToMutex.Unlock()
	obj._To = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "To")
	}
	return nil
}
func (obj *ServiceHealth) SetMessage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ServiceHealth.Message", reflect.TypeOf(val))
	}
	obj._MessageMutex.Lock()
	defer obj._MessageMutex.Unlock()
	obj._Message = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Message")
	}
	return nil
}

func NewOnboardingVelocityConfig() (_obj *OnboardingVelocityConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &OnboardingVelocityConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["threshold"] = _obj.SetThreshold
	_setters["bucketprecision"] = _obj.SetBucketPrecision
	_setters["bucketexpiry"] = _obj.SetBucketExpiry
	_setters["queryrangeduration"] = _obj.SetQueryRangeDuration
	return _obj, _setters
}

func (obj *OnboardingVelocityConfig) Init() {
	newObj, _ := NewOnboardingVelocityConfig()
	*obj = *newObj
}

func (obj *OnboardingVelocityConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OnboardingVelocityConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OnboardingVelocityConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingVelocityConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OnboardingVelocityConfig) setDynamicField(v *config.OnboardingVelocityConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "threshold":
		return obj.SetThreshold(v.Threshold, true, nil)
	case "bucketprecision":
		return obj.SetBucketPrecision(v.BucketPrecision, true, nil)
	case "bucketexpiry":
		return obj.SetBucketExpiry(v.BucketExpiry, true, nil)
	case "queryrangeduration":
		return obj.SetQueryRangeDuration(v.QueryRangeDuration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OnboardingVelocityConfig) setDynamicFields(v *config.OnboardingVelocityConfig, dynamic bool, path []string) (err error) {

	err = obj.SetThreshold(v.Threshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBucketPrecision(v.BucketPrecision, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBucketExpiry(v.BucketExpiry, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetQueryRangeDuration(v.QueryRangeDuration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *OnboardingVelocityConfig) setStaticFields(v *config.OnboardingVelocityConfig) error {

	return nil
}

func (obj *OnboardingVelocityConfig) SetThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingVelocityConfig.Threshold", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._Threshold, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Threshold")
	}
	return nil
}
func (obj *OnboardingVelocityConfig) SetBucketPrecision(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingVelocityConfig.BucketPrecision", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._BucketPrecision, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BucketPrecision")
	}
	return nil
}
func (obj *OnboardingVelocityConfig) SetBucketExpiry(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingVelocityConfig.BucketExpiry", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._BucketExpiry, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BucketExpiry")
	}
	return nil
}
func (obj *OnboardingVelocityConfig) SetQueryRangeDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingVelocityConfig.QueryRangeDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._QueryRangeDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "QueryRangeDuration")
	}
	return nil
}

func NewReferralOfferCodeDuringOnboarding() (_obj *ReferralOfferCodeDuringOnboarding, _setters map[string]dynconf.SetFunc) {
	_obj = &ReferralOfferCodeDuringOnboarding{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["beforeappliedtitle"] = _obj.SetBeforeAppliedTitle
	_obj._BeforeAppliedTitleMutex = &sync.RWMutex{}
	_setters["afterappliedtitle"] = _obj.SetAfterAppliedTitle
	_obj._AfterAppliedTitleMutex = &sync.RWMutex{}
	_setters["beforeapplieddesc"] = _obj.SetBeforeAppliedDesc
	_obj._BeforeAppliedDescMutex = &sync.RWMutex{}
	_setters["afterapplieddesc"] = _obj.SetAfterAppliedDesc
	_obj._AfterAppliedDescMutex = &sync.RWMutex{}
	_setters["beforeappliediconurl"] = _obj.SetBeforeAppliedIconUrl
	_obj._BeforeAppliedIconUrlMutex = &sync.RWMutex{}
	_setters["afterappliediconurl"] = _obj.SetAfterAppliedIconUrl
	_obj._AfterAppliedIconUrlMutex = &sync.RWMutex{}
	_setters["code"] = _obj.SetCode
	_obj._CodeMutex = &sync.RWMutex{}
	_setters["underlyingfinitecode"] = _obj.SetUnderlyingFiniteCode
	_obj._UnderlyingFiniteCodeMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *ReferralOfferCodeDuringOnboarding) Init() {
	newObj, _ := NewReferralOfferCodeDuringOnboarding()
	*obj = *newObj
}

func (obj *ReferralOfferCodeDuringOnboarding) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ReferralOfferCodeDuringOnboarding) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ReferralOfferCodeDuringOnboarding)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralOfferCodeDuringOnboarding", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ReferralOfferCodeDuringOnboarding) setDynamicField(v *config.ReferralOfferCodeDuringOnboarding, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "beforeappliedtitle":
		return obj.SetBeforeAppliedTitle(v.BeforeAppliedTitle, true, nil)
	case "afterappliedtitle":
		return obj.SetAfterAppliedTitle(v.AfterAppliedTitle, true, nil)
	case "beforeapplieddesc":
		return obj.SetBeforeAppliedDesc(v.BeforeAppliedDesc, true, nil)
	case "afterapplieddesc":
		return obj.SetAfterAppliedDesc(v.AfterAppliedDesc, true, nil)
	case "beforeappliediconurl":
		return obj.SetBeforeAppliedIconUrl(v.BeforeAppliedIconUrl, true, nil)
	case "afterappliediconurl":
		return obj.SetAfterAppliedIconUrl(v.AfterAppliedIconUrl, true, nil)
	case "code":
		return obj.SetCode(v.Code, true, nil)
	case "underlyingfinitecode":
		return obj.SetUnderlyingFiniteCode(v.UnderlyingFiniteCode, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ReferralOfferCodeDuringOnboarding) setDynamicFields(v *config.ReferralOfferCodeDuringOnboarding, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBeforeAppliedTitle(v.BeforeAppliedTitle, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAfterAppliedTitle(v.AfterAppliedTitle, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBeforeAppliedDesc(v.BeforeAppliedDesc, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAfterAppliedDesc(v.AfterAppliedDesc, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBeforeAppliedIconUrl(v.BeforeAppliedIconUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAfterAppliedIconUrl(v.AfterAppliedIconUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCode(v.Code, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUnderlyingFiniteCode(v.UnderlyingFiniteCode, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ReferralOfferCodeDuringOnboarding) setStaticFields(v *config.ReferralOfferCodeDuringOnboarding) error {

	return nil
}

func (obj *ReferralOfferCodeDuringOnboarding) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralOfferCodeDuringOnboarding.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *ReferralOfferCodeDuringOnboarding) SetBeforeAppliedTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralOfferCodeDuringOnboarding.BeforeAppliedTitle", reflect.TypeOf(val))
	}
	obj._BeforeAppliedTitleMutex.Lock()
	defer obj._BeforeAppliedTitleMutex.Unlock()
	obj._BeforeAppliedTitle = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BeforeAppliedTitle")
	}
	return nil
}
func (obj *ReferralOfferCodeDuringOnboarding) SetAfterAppliedTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralOfferCodeDuringOnboarding.AfterAppliedTitle", reflect.TypeOf(val))
	}
	obj._AfterAppliedTitleMutex.Lock()
	defer obj._AfterAppliedTitleMutex.Unlock()
	obj._AfterAppliedTitle = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "AfterAppliedTitle")
	}
	return nil
}
func (obj *ReferralOfferCodeDuringOnboarding) SetBeforeAppliedDesc(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralOfferCodeDuringOnboarding.BeforeAppliedDesc", reflect.TypeOf(val))
	}
	obj._BeforeAppliedDescMutex.Lock()
	defer obj._BeforeAppliedDescMutex.Unlock()
	obj._BeforeAppliedDesc = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BeforeAppliedDesc")
	}
	return nil
}
func (obj *ReferralOfferCodeDuringOnboarding) SetAfterAppliedDesc(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralOfferCodeDuringOnboarding.AfterAppliedDesc", reflect.TypeOf(val))
	}
	obj._AfterAppliedDescMutex.Lock()
	defer obj._AfterAppliedDescMutex.Unlock()
	obj._AfterAppliedDesc = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "AfterAppliedDesc")
	}
	return nil
}
func (obj *ReferralOfferCodeDuringOnboarding) SetBeforeAppliedIconUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralOfferCodeDuringOnboarding.BeforeAppliedIconUrl", reflect.TypeOf(val))
	}
	obj._BeforeAppliedIconUrlMutex.Lock()
	defer obj._BeforeAppliedIconUrlMutex.Unlock()
	obj._BeforeAppliedIconUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BeforeAppliedIconUrl")
	}
	return nil
}
func (obj *ReferralOfferCodeDuringOnboarding) SetAfterAppliedIconUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralOfferCodeDuringOnboarding.AfterAppliedIconUrl", reflect.TypeOf(val))
	}
	obj._AfterAppliedIconUrlMutex.Lock()
	defer obj._AfterAppliedIconUrlMutex.Unlock()
	obj._AfterAppliedIconUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "AfterAppliedIconUrl")
	}
	return nil
}
func (obj *ReferralOfferCodeDuringOnboarding) SetCode(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralOfferCodeDuringOnboarding.Code", reflect.TypeOf(val))
	}
	obj._CodeMutex.Lock()
	defer obj._CodeMutex.Unlock()
	obj._Code = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Code")
	}
	return nil
}
func (obj *ReferralOfferCodeDuringOnboarding) SetUnderlyingFiniteCode(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralOfferCodeDuringOnboarding.UnderlyingFiniteCode", reflect.TypeOf(val))
	}
	obj._UnderlyingFiniteCodeMutex.Lock()
	defer obj._UnderlyingFiniteCodeMutex.Unlock()
	obj._UnderlyingFiniteCode = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "UnderlyingFiniteCode")
	}
	return nil
}

func NewReferralOfferWidgetDuringOnboarding() (_obj *ReferralOfferWidgetDuringOnboarding, _setters map[string]dynconf.SetFunc) {
	_obj = &ReferralOfferWidgetDuringOnboarding{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["lefticon"] = _obj.SetLeftIcon
	_obj._LeftIconMutex = &sync.RWMutex{}
	_setters["offertext"] = _obj.SetOfferText
	_obj._OfferTextMutex = &sync.RWMutex{}
	_setters["bgcolor"] = _obj.SetBgColor
	_obj._BgColorMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *ReferralOfferWidgetDuringOnboarding) Init() {
	newObj, _ := NewReferralOfferWidgetDuringOnboarding()
	*obj = *newObj
}

func (obj *ReferralOfferWidgetDuringOnboarding) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ReferralOfferWidgetDuringOnboarding) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ReferralOfferWidgetDuringOnboarding)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralOfferWidgetDuringOnboarding", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ReferralOfferWidgetDuringOnboarding) setDynamicField(v *config.ReferralOfferWidgetDuringOnboarding, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "lefticon":
		return obj.SetLeftIcon(v.LeftIcon, true, nil)
	case "offertext":
		return obj.SetOfferText(v.OfferText, true, nil)
	case "bgcolor":
		return obj.SetBgColor(v.BgColor, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ReferralOfferWidgetDuringOnboarding) setDynamicFields(v *config.ReferralOfferWidgetDuringOnboarding, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLeftIcon(v.LeftIcon, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOfferText(v.OfferText, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBgColor(v.BgColor, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ReferralOfferWidgetDuringOnboarding) setStaticFields(v *config.ReferralOfferWidgetDuringOnboarding) error {

	return nil
}

func (obj *ReferralOfferWidgetDuringOnboarding) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralOfferWidgetDuringOnboarding.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *ReferralOfferWidgetDuringOnboarding) SetLeftIcon(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralOfferWidgetDuringOnboarding.LeftIcon", reflect.TypeOf(val))
	}
	obj._LeftIconMutex.Lock()
	defer obj._LeftIconMutex.Unlock()
	obj._LeftIcon = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "LeftIcon")
	}
	return nil
}
func (obj *ReferralOfferWidgetDuringOnboarding) SetOfferText(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralOfferWidgetDuringOnboarding.OfferText", reflect.TypeOf(val))
	}
	obj._OfferTextMutex.Lock()
	defer obj._OfferTextMutex.Unlock()
	obj._OfferText = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "OfferText")
	}
	return nil
}
func (obj *ReferralOfferWidgetDuringOnboarding) SetBgColor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralOfferWidgetDuringOnboarding.BgColor", reflect.TypeOf(val))
	}
	obj._BgColorMutex.Lock()
	defer obj._BgColorMutex.Unlock()
	obj._BgColor = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BgColor")
	}
	return nil
}

func NewDurationToSkipAddFundsForAffluenceClass() (_obj *DurationToSkipAddFundsForAffluenceClass, _setters map[string]dynconf.SetFunc) {
	_obj = &DurationToSkipAddFundsForAffluenceClass{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["duration"] = _obj.SetDuration
	return _obj, _setters
}

func (obj *DurationToSkipAddFundsForAffluenceClass) Init() {
	newObj, _ := NewDurationToSkipAddFundsForAffluenceClass()
	*obj = *newObj
}

func (obj *DurationToSkipAddFundsForAffluenceClass) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DurationToSkipAddFundsForAffluenceClass) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DurationToSkipAddFundsForAffluenceClass)
	if !ok {
		return fmt.Errorf("invalid data type %v *DurationToSkipAddFundsForAffluenceClass", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DurationToSkipAddFundsForAffluenceClass) setDynamicField(v *config.DurationToSkipAddFundsForAffluenceClass, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "duration":
		return obj.SetDuration(v.Duration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DurationToSkipAddFundsForAffluenceClass) setDynamicFields(v *config.DurationToSkipAddFundsForAffluenceClass, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDuration(v.Duration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DurationToSkipAddFundsForAffluenceClass) setStaticFields(v *config.DurationToSkipAddFundsForAffluenceClass) error {

	obj._AffluenceClass = v.AffluenceClass
	return nil
}

func (obj *DurationToSkipAddFundsForAffluenceClass) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DurationToSkipAddFundsForAffluenceClass.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *DurationToSkipAddFundsForAffluenceClass) SetDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *DurationToSkipAddFundsForAffluenceClass.Duration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._Duration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Duration")
	}
	return nil
}

func NewWebUrlForSalaryB2BFlow() (_obj *WebUrlForSalaryB2BFlow, _setters map[string]dynconf.SetFunc) {
	_obj = &WebUrlForSalaryB2BFlow{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["url"] = _obj.SetUrl
	_obj._UrlMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *WebUrlForSalaryB2BFlow) Init() {
	newObj, _ := NewWebUrlForSalaryB2BFlow()
	*obj = *newObj
}

func (obj *WebUrlForSalaryB2BFlow) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *WebUrlForSalaryB2BFlow) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.WebUrlForSalaryB2BFlow)
	if !ok {
		return fmt.Errorf("invalid data type %v *WebUrlForSalaryB2BFlow", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *WebUrlForSalaryB2BFlow) setDynamicField(v *config.WebUrlForSalaryB2BFlow, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "url":
		return obj.SetUrl(v.Url, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *WebUrlForSalaryB2BFlow) setDynamicFields(v *config.WebUrlForSalaryB2BFlow, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUrl(v.Url, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *WebUrlForSalaryB2BFlow) setStaticFields(v *config.WebUrlForSalaryB2BFlow) error {

	return nil
}

func (obj *WebUrlForSalaryB2BFlow) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *WebUrlForSalaryB2BFlow.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *WebUrlForSalaryB2BFlow) SetUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *WebUrlForSalaryB2BFlow.Url", reflect.TypeOf(val))
	}
	obj._UrlMutex.Lock()
	defer obj._UrlMutex.Unlock()
	obj._Url = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Url")
	}
	return nil
}

func NewMandatoryMinKycAddFundConfig() (_obj *MandatoryMinKycAddFundConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &MandatoryMinKycAddFundConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	return _obj, _setters
}

func (obj *MandatoryMinKycAddFundConfig) Init() {
	newObj, _ := NewMandatoryMinKycAddFundConfig()
	*obj = *newObj
}

func (obj *MandatoryMinKycAddFundConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *MandatoryMinKycAddFundConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.MandatoryMinKycAddFundConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *MandatoryMinKycAddFundConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *MandatoryMinKycAddFundConfig) setDynamicField(v *config.MandatoryMinKycAddFundConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *MandatoryMinKycAddFundConfig) setDynamicFields(v *config.MandatoryMinKycAddFundConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *MandatoryMinKycAddFundConfig) setStaticFields(v *config.MandatoryMinKycAddFundConfig) error {

	obj._MinimumAmount = v.MinimumAmount
	return nil
}

func (obj *MandatoryMinKycAddFundConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *MandatoryMinKycAddFundConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}

func NewOnbAddFundsConfig() (_obj *OnbAddFundsConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &OnbAddFundsConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["v2pageminorversion"] = _obj.SetV2PageMinorVersion
	_setters["showskipctaviaquest"] = _obj.SetShowSkipCtaViaQuest
	_setters["showv2page"] = _obj.SetShowV2Page
	_setters["skipdurationviaquest"] = _obj.SetSkipDurationViaQuest
	return _obj, _setters
}

func NewOnbAddFundsConfigWithQuest(questFieldPath string) (_obj *OnbAddFundsConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &OnbAddFundsConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["v2pageminorversion"] = _obj.SetV2PageMinorVersion
	_setters["showskipctaviaquest"] = _obj.SetShowSkipCtaViaQuest
	_setters["showv2page"] = _obj.SetShowV2Page
	_setters["skipdurationviaquest"] = _obj.SetSkipDurationViaQuest
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *OnbAddFundsConfig) Init(questFieldPath string) {
	newObj, _ := NewOnbAddFundsConfig()
	*obj = *newObj
}
func (obj *OnbAddFundsConfig) InitWithQuest(questFieldPath string) {
	newObj, _ := NewOnbAddFundsConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *OnbAddFundsConfig) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *OnbAddFundsConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OnbAddFundsConfig) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:        obj.questFieldPath + "/" + "V2PageMinorVersion",
		Datatype:    &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_INT},
		Area:        "", // from quest tag annotation
		Description: `integer to decide which onboarding add funds v2 page has to be shown this variable only decides the minor version, major version still being 2 Note: by default this will be set to 0. And quest exp will decide the newer versions.`,
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "ShowSkipCtaViaQuest",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "ShowV2Page",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "SkipDurationViaQuest",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_DURATION},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *OnbAddFundsConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OnbAddFundsConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnbAddFundsConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OnbAddFundsConfig) setDynamicField(v *config.OnbAddFundsConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "v2pageminorversion":
		return obj.SetV2PageMinorVersion(v.V2PageMinorVersion, true, nil)
	case "showskipctaviaquest":
		return obj.SetShowSkipCtaViaQuest(v.ShowSkipCtaViaQuest, true, nil)
	case "showv2page":
		return obj.SetShowV2Page(v.ShowV2Page, true, nil)
	case "skipdurationviaquest":
		return obj.SetSkipDurationViaQuest(v.SkipDurationViaQuest, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OnbAddFundsConfig) setDynamicFields(v *config.OnbAddFundsConfig, dynamic bool, path []string) (err error) {

	err = obj.SetV2PageMinorVersion(v.V2PageMinorVersion, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetShowSkipCtaViaQuest(v.ShowSkipCtaViaQuest, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetShowV2Page(v.ShowV2Page, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSkipDurationViaQuest(v.SkipDurationViaQuest, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *OnbAddFundsConfig) setStaticFields(v *config.OnbAddFundsConfig) error {

	return nil
}

func (obj *OnbAddFundsConfig) SetV2PageMinorVersion(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnbAddFundsConfig.V2PageMinorVersion", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._V2PageMinorVersion, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "V2PageMinorVersion")
	}
	return nil
}
func (obj *OnbAddFundsConfig) SetShowSkipCtaViaQuest(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnbAddFundsConfig.ShowSkipCtaViaQuest", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ShowSkipCtaViaQuest, 1)
	} else {
		atomic.StoreUint32(&obj._ShowSkipCtaViaQuest, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ShowSkipCtaViaQuest")
	}
	return nil
}
func (obj *OnbAddFundsConfig) SetShowV2Page(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnbAddFundsConfig.ShowV2Page", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ShowV2Page, 1)
	} else {
		atomic.StoreUint32(&obj._ShowV2Page, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ShowV2Page")
	}
	return nil
}
func (obj *OnbAddFundsConfig) SetSkipDurationViaQuest(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnbAddFundsConfig.SkipDurationViaQuest", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._SkipDurationViaQuest, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SkipDurationViaQuest")
	}
	return nil
}

func NewResetOnboardingJourneyConfig() (_obj *ResetOnboardingJourneyConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ResetOnboardingJourneyConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["disableforios"] = _obj.SetDisableForIOS
	_setters["onboardingjourneyresetthresholdtime"] = _obj.SetOnboardingJourneyResetThresholdTime
	return _obj, _setters
}

func (obj *ResetOnboardingJourneyConfig) Init() {
	newObj, _ := NewResetOnboardingJourneyConfig()
	*obj = *newObj
}

func (obj *ResetOnboardingJourneyConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ResetOnboardingJourneyConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ResetOnboardingJourneyConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ResetOnboardingJourneyConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ResetOnboardingJourneyConfig) setDynamicField(v *config.ResetOnboardingJourneyConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "disableforios":
		return obj.SetDisableForIOS(v.DisableForIOS, true, nil)
	case "onboardingjourneyresetthresholdtime":
		return obj.SetOnboardingJourneyResetThresholdTime(v.OnboardingJourneyResetThresholdTime, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ResetOnboardingJourneyConfig) setDynamicFields(v *config.ResetOnboardingJourneyConfig, dynamic bool, path []string) (err error) {

	err = obj.SetDisableForIOS(v.DisableForIOS, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOnboardingJourneyResetThresholdTime(v.OnboardingJourneyResetThresholdTime, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ResetOnboardingJourneyConfig) setStaticFields(v *config.ResetOnboardingJourneyConfig) error {

	return nil
}

func (obj *ResetOnboardingJourneyConfig) SetDisableForIOS(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ResetOnboardingJourneyConfig.DisableForIOS", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableForIOS, 1)
	} else {
		atomic.StoreUint32(&obj._DisableForIOS, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableForIOS")
	}
	return nil
}
func (obj *ResetOnboardingJourneyConfig) SetOnboardingJourneyResetThresholdTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *ResetOnboardingJourneyConfig.OnboardingJourneyResetThresholdTime", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._OnboardingJourneyResetThresholdTime, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "OnboardingJourneyResetThresholdTime")
	}
	return nil
}

func NewConfirmCardMailingAddressConfig() (_obj *ConfirmCardMailingAddressConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ConfirmCardMailingAddressConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxkycnamelentoskipconfirmcardmailingaddr"] = _obj.SetMaxKYCNameLenToSkipConfirmCardMailingAddr
	_setters["enableconfirmcardmailingaddressv2"] = _obj.SetEnableConfirmCardMailingAddressV2
	return _obj, _setters
}

func (obj *ConfirmCardMailingAddressConfig) Init() {
	newObj, _ := NewConfirmCardMailingAddressConfig()
	*obj = *newObj
}

func (obj *ConfirmCardMailingAddressConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ConfirmCardMailingAddressConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ConfirmCardMailingAddressConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ConfirmCardMailingAddressConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ConfirmCardMailingAddressConfig) setDynamicField(v *config.ConfirmCardMailingAddressConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maxkycnamelentoskipconfirmcardmailingaddr":
		return obj.SetMaxKYCNameLenToSkipConfirmCardMailingAddr(v.MaxKYCNameLenToSkipConfirmCardMailingAddr, true, nil)
	case "enableconfirmcardmailingaddressv2":
		return obj.SetEnableConfirmCardMailingAddressV2(v.EnableConfirmCardMailingAddressV2, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ConfirmCardMailingAddressConfig) setDynamicFields(v *config.ConfirmCardMailingAddressConfig, dynamic bool, path []string) (err error) {

	err = obj.SetMaxKYCNameLenToSkipConfirmCardMailingAddr(v.MaxKYCNameLenToSkipConfirmCardMailingAddr, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableConfirmCardMailingAddressV2(v.EnableConfirmCardMailingAddressV2, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ConfirmCardMailingAddressConfig) setStaticFields(v *config.ConfirmCardMailingAddressConfig) error {

	return nil
}

func (obj *ConfirmCardMailingAddressConfig) SetMaxKYCNameLenToSkipConfirmCardMailingAddr(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *ConfirmCardMailingAddressConfig.MaxKYCNameLenToSkipConfirmCardMailingAddr", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxKYCNameLenToSkipConfirmCardMailingAddr, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxKYCNameLenToSkipConfirmCardMailingAddr")
	}
	return nil
}
func (obj *ConfirmCardMailingAddressConfig) SetEnableConfirmCardMailingAddressV2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ConfirmCardMailingAddressConfig.EnableConfirmCardMailingAddressV2", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableConfirmCardMailingAddressV2, 1)
	} else {
		atomic.StoreUint32(&obj._EnableConfirmCardMailingAddressV2, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableConfirmCardMailingAddressV2")
	}
	return nil
}

func NewIntentSelectionConfigV2() (_obj *IntentSelectionConfigV2, _setters map[string]dynconf.SetFunc) {
	_obj = &IntentSelectionConfigV2{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["intentcollectionscreenpercentagerollout"] = _obj.SetIntentCollectionScreenPercentageRollout
	_setters["enabledefaultintentselection"] = _obj.SetEnableDefaultIntentSelection

	_obj._IntentConfigMap = &syncmap.Map[string, *IntentConfig]{}
	_setters["intentconfigmap"] = _obj.SetIntentConfigMap
	_IntentCollectionScreenFeatureConfig, _fieldSetters := genapp.NewFeatureConfig()
	_obj._IntentCollectionScreenFeatureConfig = _IntentCollectionScreenFeatureConfig
	helper.AddFieldSetters("intentcollectionscreenfeatureconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *IntentSelectionConfigV2) Init() {
	newObj, _ := NewIntentSelectionConfigV2()
	*obj = *newObj
}

func (obj *IntentSelectionConfigV2) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *IntentSelectionConfigV2) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.IntentSelectionConfigV2)
	if !ok {
		return fmt.Errorf("invalid data type %v *IntentSelectionConfigV2", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *IntentSelectionConfigV2) setDynamicField(v *config.IntentSelectionConfigV2, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "intentcollectionscreenpercentagerollout":
		return obj.SetIntentCollectionScreenPercentageRollout(v.IntentCollectionScreenPercentageRollout, true, nil)
	case "enabledefaultintentselection":
		return obj.SetEnableDefaultIntentSelection(v.EnableDefaultIntentSelection, true, nil)
	case "intentconfigmap":
		return obj.SetIntentConfigMap(v.IntentConfigMap, true, path)
	case "intentcollectionscreenfeatureconfig":
		return obj._IntentCollectionScreenFeatureConfig.Set(v.IntentCollectionScreenFeatureConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *IntentSelectionConfigV2) setDynamicFields(v *config.IntentSelectionConfigV2, dynamic bool, path []string) (err error) {

	err = obj.SetIntentCollectionScreenPercentageRollout(v.IntentCollectionScreenPercentageRollout, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableDefaultIntentSelection(v.EnableDefaultIntentSelection, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIntentConfigMap(v.IntentConfigMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._IntentCollectionScreenFeatureConfig.Set(v.IntentCollectionScreenFeatureConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *IntentSelectionConfigV2) setStaticFields(v *config.IntentSelectionConfigV2) error {

	obj._FiniteCodeToAcquisitionIntentMap = v.FiniteCodeToAcquisitionIntentMap
	obj._PLIntentAgencies = v.PLIntentAgencies
	return nil
}

func (obj *IntentSelectionConfigV2) SetIntentCollectionScreenPercentageRollout(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *IntentSelectionConfigV2.IntentCollectionScreenPercentageRollout", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._IntentCollectionScreenPercentageRollout, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "IntentCollectionScreenPercentageRollout")
	}
	return nil
}
func (obj *IntentSelectionConfigV2) SetEnableDefaultIntentSelection(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *IntentSelectionConfigV2.EnableDefaultIntentSelection", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableDefaultIntentSelection, 1)
	} else {
		atomic.StoreUint32(&obj._EnableDefaultIntentSelection, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableDefaultIntentSelection")
	}
	return nil
}
func (obj *IntentSelectionConfigV2) SetIntentConfigMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.IntentConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *IntentSelectionConfigV2.IntentConfigMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._IntentConfigMap, v, dynamic, path)

}

func NewIntentConfig() (_obj *IntentConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &IntentConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["rolloutpercentage"] = _obj.SetRolloutPercentage
	_setters["allowedusergroups"] = _obj.SetAllowedUserGroups
	_obj._AllowedUserGroupsMutex = &sync.RWMutex{}
	_FeatureConfig, _fieldSetters := genapp.NewFeatureConfig()
	_obj._FeatureConfig = _FeatureConfig
	helper.AddFieldSetters("featureconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *IntentConfig) Init() {
	newObj, _ := NewIntentConfig()
	*obj = *newObj
}

func (obj *IntentConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *IntentConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.IntentConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *IntentConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *IntentConfig) setDynamicField(v *config.IntentConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "rolloutpercentage":
		return obj.SetRolloutPercentage(v.RolloutPercentage, true, nil)
	case "allowedusergroups":
		return obj.SetAllowedUserGroups(v.AllowedUserGroups, true, path)
	case "featureconfig":
		return obj._FeatureConfig.Set(v.FeatureConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *IntentConfig) setDynamicFields(v *config.IntentConfig, dynamic bool, path []string) (err error) {

	err = obj.SetRolloutPercentage(v.RolloutPercentage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAllowedUserGroups(v.AllowedUserGroups, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureConfig.Set(v.FeatureConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *IntentConfig) setStaticFields(v *config.IntentConfig) error {

	return nil
}

func (obj *IntentConfig) SetRolloutPercentage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *IntentConfig.RolloutPercentage", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._RolloutPercentage, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RolloutPercentage")
	}
	return nil
}
func (obj *IntentConfig) SetAllowedUserGroups(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *IntentConfig.AllowedUserGroups", reflect.TypeOf(val))
	}
	obj._AllowedUserGroupsMutex.Lock()
	defer obj._AllowedUserGroupsMutex.Unlock()
	obj._AllowedUserGroups = roarray.New[string](v)
	return nil
}

func NewSoftIntentSelectionConfig() (_obj *SoftIntentSelectionConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &SoftIntentSelectionConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["softintentcollectionscreenpercentagerollout"] = _obj.SetSoftIntentCollectionScreenPercentageRollout

	_obj._AllowedSoftIntents = &syncmap.Map[string, bool]{}
	_setters["allowedsoftintents"] = _obj.SetAllowedSoftIntents
	_SoftIntentCollectionScreenFeatureConfig, _fieldSetters := genapp.NewFeatureConfig()
	_obj._SoftIntentCollectionScreenFeatureConfig = _SoftIntentCollectionScreenFeatureConfig
	helper.AddFieldSetters("softintentcollectionscreenfeatureconfig", _fieldSetters, _setters)
	_PostOnboardingSoftIntentScreenConfig, _fieldSetters := NewPostOnboardingSoftIntentScreenConfig()
	_obj._PostOnboardingSoftIntentScreenConfig = _PostOnboardingSoftIntentScreenConfig
	helper.AddFieldSetters("postonboardingsoftintentscreenconfig", _fieldSetters, _setters)
	_PreOnboardingCompletionSoftIntentScreenConfig, _fieldSetters := NewPreOnboardingCompletionSoftIntentScreenConfig()
	_obj._PreOnboardingCompletionSoftIntentScreenConfig = _PreOnboardingCompletionSoftIntentScreenConfig
	helper.AddFieldSetters("preonboardingcompletionsoftintentscreenconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewSoftIntentSelectionConfigWithQuest(questFieldPath string) (_obj *SoftIntentSelectionConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &SoftIntentSelectionConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["softintentcollectionscreenpercentagerollout"] = _obj.SetSoftIntentCollectionScreenPercentageRollout

	_obj._AllowedSoftIntents = &syncmap.Map[string, bool]{}
	_setters["allowedsoftintents"] = _obj.SetAllowedSoftIntents
	_SoftIntentCollectionScreenFeatureConfig, _fieldSetters := genapp.NewFeatureConfig()
	_obj._SoftIntentCollectionScreenFeatureConfig = _SoftIntentCollectionScreenFeatureConfig
	helper.AddFieldSetters("softintentcollectionscreenfeatureconfig", _fieldSetters, _setters)
	_PostOnboardingSoftIntentScreenConfig, _fieldSetters := NewPostOnboardingSoftIntentScreenConfigWithQuest(questFieldPath + "/" + "PostOnboardingSoftIntentScreenConfig")
	_obj._PostOnboardingSoftIntentScreenConfig = _PostOnboardingSoftIntentScreenConfig
	helper.AddFieldSetters("postonboardingsoftintentscreenconfig", _fieldSetters, _setters)
	_PreOnboardingCompletionSoftIntentScreenConfig, _fieldSetters := NewPreOnboardingCompletionSoftIntentScreenConfigWithQuest(questFieldPath + "/" + "PreOnboardingCompletionSoftIntentScreenConfig")
	_obj._PreOnboardingCompletionSoftIntentScreenConfig = _PreOnboardingCompletionSoftIntentScreenConfig
	helper.AddFieldSetters("preonboardingcompletionsoftintentscreenconfig", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *SoftIntentSelectionConfig) Init(questFieldPath string) {
	newObj, _ := NewSoftIntentSelectionConfig()
	*obj = *newObj
}
func (obj *SoftIntentSelectionConfig) InitWithQuest(questFieldPath string) {
	newObj, _ := NewSoftIntentSelectionConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *SoftIntentSelectionConfig) SetQuestSDK(questSdk questsdk.Client) {
	obj._PostOnboardingSoftIntentScreenConfig.SetQuestSDK(questSdk)
	obj._PreOnboardingCompletionSoftIntentScreenConfig.SetQuestSDK(questSdk)
}

func (obj *SoftIntentSelectionConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SoftIntentSelectionConfig) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	childVars, childVarsErr = obj._PostOnboardingSoftIntentScreenConfig.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._PreOnboardingCompletionSoftIntentScreenConfig.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	return vars, nil
}

func (obj *SoftIntentSelectionConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SoftIntentSelectionConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *SoftIntentSelectionConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SoftIntentSelectionConfig) setDynamicField(v *config.SoftIntentSelectionConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "softintentcollectionscreenpercentagerollout":
		return obj.SetSoftIntentCollectionScreenPercentageRollout(v.SoftIntentCollectionScreenPercentageRollout, true, nil)
	case "allowedsoftintents":
		return obj.SetAllowedSoftIntents(v.AllowedSoftIntents, true, path)
	case "softintentcollectionscreenfeatureconfig":
		return obj._SoftIntentCollectionScreenFeatureConfig.Set(v.SoftIntentCollectionScreenFeatureConfig, true, path)
	case "postonboardingsoftintentscreenconfig":
		return obj._PostOnboardingSoftIntentScreenConfig.Set(v.PostOnboardingSoftIntentScreenConfig, true, path)
	case "preonboardingcompletionsoftintentscreenconfig":
		return obj._PreOnboardingCompletionSoftIntentScreenConfig.Set(v.PreOnboardingCompletionSoftIntentScreenConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SoftIntentSelectionConfig) setDynamicFields(v *config.SoftIntentSelectionConfig, dynamic bool, path []string) (err error) {

	err = obj.SetSoftIntentCollectionScreenPercentageRollout(v.SoftIntentCollectionScreenPercentageRollout, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAllowedSoftIntents(v.AllowedSoftIntents, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SoftIntentCollectionScreenFeatureConfig.Set(v.SoftIntentCollectionScreenFeatureConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PostOnboardingSoftIntentScreenConfig.Set(v.PostOnboardingSoftIntentScreenConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PreOnboardingCompletionSoftIntentScreenConfig.Set(v.PreOnboardingCompletionSoftIntentScreenConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SoftIntentSelectionConfig) setStaticFields(v *config.SoftIntentSelectionConfig) error {

	return nil
}

func (obj *SoftIntentSelectionConfig) SetSoftIntentCollectionScreenPercentageRollout(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *SoftIntentSelectionConfig.SoftIntentCollectionScreenPercentageRollout", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._SoftIntentCollectionScreenPercentageRollout, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SoftIntentCollectionScreenPercentageRollout")
	}
	return nil
}
func (obj *SoftIntentSelectionConfig) SetAllowedSoftIntents(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *SoftIntentSelectionConfig.AllowedSoftIntents", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._AllowedSoftIntents, v, path)
}

func NewPostOnboardingSoftIntentScreenConfig() (_obj *PostOnboardingSoftIntentScreenConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &PostOnboardingSoftIntentScreenConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enabled"] = _obj.SetEnabled
	_setters["waitduration"] = _obj.SetWaitDuration
	_setters["maxwaitduration"] = _obj.SetMaxWaitDuration
	return _obj, _setters
}

func NewPostOnboardingSoftIntentScreenConfigWithQuest(questFieldPath string) (_obj *PostOnboardingSoftIntentScreenConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &PostOnboardingSoftIntentScreenConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enabled"] = _obj.SetEnabled
	_setters["waitduration"] = _obj.SetWaitDuration
	_setters["maxwaitduration"] = _obj.SetMaxWaitDuration
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *PostOnboardingSoftIntentScreenConfig) Init(questFieldPath string) {
	newObj, _ := NewPostOnboardingSoftIntentScreenConfig()
	*obj = *newObj
}
func (obj *PostOnboardingSoftIntentScreenConfig) InitWithQuest(questFieldPath string) {
	newObj, _ := NewPostOnboardingSoftIntentScreenConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *PostOnboardingSoftIntentScreenConfig) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *PostOnboardingSoftIntentScreenConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PostOnboardingSoftIntentScreenConfig) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Enabled",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "WaitDuration",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_DURATION},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *PostOnboardingSoftIntentScreenConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PostOnboardingSoftIntentScreenConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *PostOnboardingSoftIntentScreenConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PostOnboardingSoftIntentScreenConfig) setDynamicField(v *config.PostOnboardingSoftIntentScreenConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enabled":
		return obj.SetEnabled(v.Enabled, true, nil)
	case "waitduration":
		return obj.SetWaitDuration(v.WaitDuration, true, nil)
	case "maxwaitduration":
		return obj.SetMaxWaitDuration(v.MaxWaitDuration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PostOnboardingSoftIntentScreenConfig) setDynamicFields(v *config.PostOnboardingSoftIntentScreenConfig, dynamic bool, path []string) (err error) {

	err = obj.SetEnabled(v.Enabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetWaitDuration(v.WaitDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxWaitDuration(v.MaxWaitDuration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PostOnboardingSoftIntentScreenConfig) setStaticFields(v *config.PostOnboardingSoftIntentScreenConfig) error {

	return nil
}

func (obj *PostOnboardingSoftIntentScreenConfig) SetEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *PostOnboardingSoftIntentScreenConfig.Enabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Enabled, 1)
	} else {
		atomic.StoreUint32(&obj._Enabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Enabled")
	}
	return nil
}
func (obj *PostOnboardingSoftIntentScreenConfig) SetWaitDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *PostOnboardingSoftIntentScreenConfig.WaitDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._WaitDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "WaitDuration")
	}
	return nil
}
func (obj *PostOnboardingSoftIntentScreenConfig) SetMaxWaitDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *PostOnboardingSoftIntentScreenConfig.MaxWaitDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxWaitDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxWaitDuration")
	}
	return nil
}

func NewPreOnboardingCompletionSoftIntentScreenConfig() (_obj *PreOnboardingCompletionSoftIntentScreenConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &PreOnboardingCompletionSoftIntentScreenConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enabled"] = _obj.SetEnabled
	return _obj, _setters
}

func NewPreOnboardingCompletionSoftIntentScreenConfigWithQuest(questFieldPath string) (_obj *PreOnboardingCompletionSoftIntentScreenConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &PreOnboardingCompletionSoftIntentScreenConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enabled"] = _obj.SetEnabled
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *PreOnboardingCompletionSoftIntentScreenConfig) Init(questFieldPath string) {
	newObj, _ := NewPreOnboardingCompletionSoftIntentScreenConfig()
	*obj = *newObj
}
func (obj *PreOnboardingCompletionSoftIntentScreenConfig) InitWithQuest(questFieldPath string) {
	newObj, _ := NewPreOnboardingCompletionSoftIntentScreenConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *PreOnboardingCompletionSoftIntentScreenConfig) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *PreOnboardingCompletionSoftIntentScreenConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PreOnboardingCompletionSoftIntentScreenConfig) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Enabled",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *PreOnboardingCompletionSoftIntentScreenConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PreOnboardingCompletionSoftIntentScreenConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *PreOnboardingCompletionSoftIntentScreenConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PreOnboardingCompletionSoftIntentScreenConfig) setDynamicField(v *config.PreOnboardingCompletionSoftIntentScreenConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enabled":
		return obj.SetEnabled(v.Enabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PreOnboardingCompletionSoftIntentScreenConfig) setDynamicFields(v *config.PreOnboardingCompletionSoftIntentScreenConfig, dynamic bool, path []string) (err error) {

	err = obj.SetEnabled(v.Enabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PreOnboardingCompletionSoftIntentScreenConfig) setStaticFields(v *config.PreOnboardingCompletionSoftIntentScreenConfig) error {

	return nil
}

func (obj *PreOnboardingCompletionSoftIntentScreenConfig) SetEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *PreOnboardingCompletionSoftIntentScreenConfig.Enabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Enabled, 1)
	} else {
		atomic.StoreUint32(&obj._Enabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Enabled")
	}
	return nil
}

func NewDirectToFiLiteConfig() (_obj *DirectToFiLiteConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DirectToFiLiteConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enable"] = _obj.SetEnable
	_setters["variant"] = _obj.SetVariant
	_obj._VariantMutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewDirectToFiLiteConfigWithQuest(questFieldPath string) (_obj *DirectToFiLiteConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DirectToFiLiteConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enable"] = _obj.SetEnable
	_setters["variant"] = _obj.SetVariant
	_obj._VariantMutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *DirectToFiLiteConfig) Init(questFieldPath string) {
	newObj, _ := NewDirectToFiLiteConfig()
	*obj = *newObj
}
func (obj *DirectToFiLiteConfig) InitWithQuest(questFieldPath string) {
	newObj, _ := NewDirectToFiLiteConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *DirectToFiLiteConfig) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *DirectToFiLiteConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DirectToFiLiteConfig) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Enable",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Variant",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *DirectToFiLiteConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DirectToFiLiteConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *DirectToFiLiteConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DirectToFiLiteConfig) setDynamicField(v *config.DirectToFiLiteConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enable":
		return obj.SetEnable(v.Enable, true, nil)
	case "variant":
		return obj.SetVariant(v.Variant, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DirectToFiLiteConfig) setDynamicFields(v *config.DirectToFiLiteConfig, dynamic bool, path []string) (err error) {

	err = obj.SetEnable(v.Enable, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetVariant(v.Variant, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DirectToFiLiteConfig) setStaticFields(v *config.DirectToFiLiteConfig) error {

	return nil
}

func (obj *DirectToFiLiteConfig) SetEnable(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DirectToFiLiteConfig.Enable", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Enable, 1)
	} else {
		atomic.StoreUint32(&obj._Enable, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Enable")
	}
	return nil
}
func (obj *DirectToFiLiteConfig) SetVariant(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *DirectToFiLiteConfig.Variant", reflect.TypeOf(val))
	}
	obj._VariantMutex.Lock()
	defer obj._VariantMutex.Unlock()
	obj._Variant = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Variant")
	}
	return nil
}

func NewCrossValidationConfig() (_obj *CrossValidationConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CrossValidationConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["dummydynamicflag"] = _obj.SetDummyDynamicFlag
	return _obj, _setters
}

func (obj *CrossValidationConfig) Init() {
	newObj, _ := NewCrossValidationConfig()
	*obj = *newObj
}

func (obj *CrossValidationConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CrossValidationConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CrossValidationConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CrossValidationConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CrossValidationConfig) setDynamicField(v *config.CrossValidationConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "dummydynamicflag":
		return obj.SetDummyDynamicFlag(v.DummyDynamicFlag, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CrossValidationConfig) setDynamicFields(v *config.CrossValidationConfig, dynamic bool, path []string) (err error) {

	err = obj.SetDummyDynamicFlag(v.DummyDynamicFlag, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CrossValidationConfig) setStaticFields(v *config.CrossValidationConfig) error {

	obj._CrossValidationDataSources = v.CrossValidationDataSources
	obj._FmPollingRetryConfig = v.FmPollingRetryConfig
	obj._NameMatchScoreThreshold = v.NameMatchScoreThreshold
	return nil
}

func (obj *CrossValidationConfig) SetDummyDynamicFlag(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CrossValidationConfig.DummyDynamicFlag", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DummyDynamicFlag, 1)
	} else {
		atomic.StoreUint32(&obj._DummyDynamicFlag, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DummyDynamicFlag")
	}
	return nil
}

func NewPassportVerificationConfig() (_obj *PassportVerificationConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &PassportVerificationConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ignorevendorerror"] = _obj.SetIgnoreVendorError
	_setters["passportexpirythreshold"] = _obj.SetPassportExpiryThreshold
	_DetailsConfirmationFeatureConfig, _fieldSetters := genapp.NewFeatureConfig()
	_obj._DetailsConfirmationFeatureConfig = _DetailsConfirmationFeatureConfig
	helper.AddFieldSetters("detailsconfirmationfeatureconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *PassportVerificationConfig) Init() {
	newObj, _ := NewPassportVerificationConfig()
	*obj = *newObj
}

func (obj *PassportVerificationConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PassportVerificationConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PassportVerificationConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *PassportVerificationConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PassportVerificationConfig) setDynamicField(v *config.PassportVerificationConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "ignorevendorerror":
		return obj.SetIgnoreVendorError(v.IgnoreVendorError, true, nil)
	case "passportexpirythreshold":
		return obj.SetPassportExpiryThreshold(v.PassportExpiryThreshold, true, nil)
	case "detailsconfirmationfeatureconfig":
		return obj._DetailsConfirmationFeatureConfig.Set(v.DetailsConfirmationFeatureConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PassportVerificationConfig) setDynamicFields(v *config.PassportVerificationConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIgnoreVendorError(v.IgnoreVendorError, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPassportExpiryThreshold(v.PassportExpiryThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._DetailsConfirmationFeatureConfig.Set(v.DetailsConfirmationFeatureConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PassportVerificationConfig) setStaticFields(v *config.PassportVerificationConfig) error {

	return nil
}

func (obj *PassportVerificationConfig) SetIgnoreVendorError(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *PassportVerificationConfig.IgnoreVendorError", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IgnoreVendorError, 1)
	} else {
		atomic.StoreUint32(&obj._IgnoreVendorError, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IgnoreVendorError")
	}
	return nil
}
func (obj *PassportVerificationConfig) SetPassportExpiryThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *PassportVerificationConfig.PassportExpiryThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._PassportExpiryThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "PassportExpiryThreshold")
	}
	return nil
}

func NewOrderPhysicalDebitCardConfig() (_obj *OrderPhysicalDebitCardConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &OrderPhysicalDebitCardConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableviaquest"] = _obj.SetEnableViaQuest
	_FreePhysicalDCRefereeRewardConfig, _fieldSetters := NewFreePhysicalDCRefereeRewardConfig()
	_obj._FreePhysicalDCRefereeRewardConfig = _FreePhysicalDCRefereeRewardConfig
	helper.AddFieldSetters("freephysicaldcrefereerewardconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewOrderPhysicalDebitCardConfigWithQuest(questFieldPath string) (_obj *OrderPhysicalDebitCardConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &OrderPhysicalDebitCardConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableviaquest"] = _obj.SetEnableViaQuest
	_FreePhysicalDCRefereeRewardConfig, _fieldSetters := NewFreePhysicalDCRefereeRewardConfig()
	_obj._FreePhysicalDCRefereeRewardConfig = _FreePhysicalDCRefereeRewardConfig
	helper.AddFieldSetters("freephysicaldcrefereerewardconfig", _fieldSetters, _setters)
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *OrderPhysicalDebitCardConfig) Init(questFieldPath string) {
	newObj, _ := NewOrderPhysicalDebitCardConfig()
	*obj = *newObj
}
func (obj *OrderPhysicalDebitCardConfig) InitWithQuest(questFieldPath string) {
	newObj, _ := NewOrderPhysicalDebitCardConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *OrderPhysicalDebitCardConfig) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *OrderPhysicalDebitCardConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OrderPhysicalDebitCardConfig) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "EnableViaQuest",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *OrderPhysicalDebitCardConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OrderPhysicalDebitCardConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *OrderPhysicalDebitCardConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OrderPhysicalDebitCardConfig) setDynamicField(v *config.OrderPhysicalDebitCardConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enableviaquest":
		return obj.SetEnableViaQuest(v.EnableViaQuest, true, nil)
	case "freephysicaldcrefereerewardconfig":
		return obj._FreePhysicalDCRefereeRewardConfig.Set(v.FreePhysicalDCRefereeRewardConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OrderPhysicalDebitCardConfig) setDynamicFields(v *config.OrderPhysicalDebitCardConfig, dynamic bool, path []string) (err error) {

	err = obj.SetEnableViaQuest(v.EnableViaQuest, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._FreePhysicalDCRefereeRewardConfig.Set(v.FreePhysicalDCRefereeRewardConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *OrderPhysicalDebitCardConfig) setStaticFields(v *config.OrderPhysicalDebitCardConfig) error {

	return nil
}

func (obj *OrderPhysicalDebitCardConfig) SetEnableViaQuest(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *OrderPhysicalDebitCardConfig.EnableViaQuest", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableViaQuest, 1)
	} else {
		atomic.StoreUint32(&obj._EnableViaQuest, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableViaQuest")
	}
	return nil
}

func NewFreePhysicalDCRefereeRewardConfig() (_obj *FreePhysicalDCRefereeRewardConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &FreePhysicalDCRefereeRewardConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["activefrom"] = _obj.SetActiveFrom
	_obj._ActiveFromMutex = &sync.RWMutex{}
	_setters["activetill"] = _obj.SetActiveTill
	_obj._ActiveTillMutex = &sync.RWMutex{}
	_setters["questexpvariablepath"] = _obj.SetQuestExpVariablePath
	_obj._QuestExpVariablePathMutex = &sync.RWMutex{}
	_setters["questexpvariablevalue"] = _obj.SetQuestExpVariableValue
	_obj._QuestExpVariableValueMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *FreePhysicalDCRefereeRewardConfig) Init() {
	newObj, _ := NewFreePhysicalDCRefereeRewardConfig()
	*obj = *newObj
}

func (obj *FreePhysicalDCRefereeRewardConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FreePhysicalDCRefereeRewardConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.FreePhysicalDCRefereeRewardConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *FreePhysicalDCRefereeRewardConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FreePhysicalDCRefereeRewardConfig) setDynamicField(v *config.FreePhysicalDCRefereeRewardConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "activefrom":
		return obj.SetActiveFrom(v.ActiveFrom, true, nil)
	case "activetill":
		return obj.SetActiveTill(v.ActiveTill, true, nil)
	case "questexpvariablepath":
		return obj.SetQuestExpVariablePath(v.QuestExpVariablePath, true, nil)
	case "questexpvariablevalue":
		return obj.SetQuestExpVariableValue(v.QuestExpVariableValue, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FreePhysicalDCRefereeRewardConfig) setDynamicFields(v *config.FreePhysicalDCRefereeRewardConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetActiveFrom(v.ActiveFrom, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetActiveTill(v.ActiveTill, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetQuestExpVariablePath(v.QuestExpVariablePath, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetQuestExpVariableValue(v.QuestExpVariableValue, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FreePhysicalDCRefereeRewardConfig) setStaticFields(v *config.FreePhysicalDCRefereeRewardConfig) error {

	return nil
}

func (obj *FreePhysicalDCRefereeRewardConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FreePhysicalDCRefereeRewardConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *FreePhysicalDCRefereeRewardConfig) SetActiveFrom(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Time)
	if !ok {
		return fmt.Errorf("invalid data type %v *FreePhysicalDCRefereeRewardConfig.ActiveFrom", reflect.TypeOf(val))
	}
	obj._ActiveFromMutex.Lock()
	defer obj._ActiveFromMutex.Unlock()
	obj._ActiveFrom = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ActiveFrom")
	}
	return nil
}
func (obj *FreePhysicalDCRefereeRewardConfig) SetActiveTill(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Time)
	if !ok {
		return fmt.Errorf("invalid data type %v *FreePhysicalDCRefereeRewardConfig.ActiveTill", reflect.TypeOf(val))
	}
	obj._ActiveTillMutex.Lock()
	defer obj._ActiveTillMutex.Unlock()
	obj._ActiveTill = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ActiveTill")
	}
	return nil
}
func (obj *FreePhysicalDCRefereeRewardConfig) SetQuestExpVariablePath(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FreePhysicalDCRefereeRewardConfig.QuestExpVariablePath", reflect.TypeOf(val))
	}
	obj._QuestExpVariablePathMutex.Lock()
	defer obj._QuestExpVariablePathMutex.Unlock()
	obj._QuestExpVariablePath = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "QuestExpVariablePath")
	}
	return nil
}
func (obj *FreePhysicalDCRefereeRewardConfig) SetQuestExpVariableValue(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FreePhysicalDCRefereeRewardConfig.QuestExpVariableValue", reflect.TypeOf(val))
	}
	obj._QuestExpVariableValueMutex.Lock()
	defer obj._QuestExpVariableValueMutex.Unlock()
	obj._QuestExpVariableValue = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "QuestExpVariableValue")
	}
	return nil
}

func NewUqudoCountryIdVerificationConfig() (_obj *UqudoCountryIdVerificationConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &UqudoCountryIdVerificationConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["screendetectionthreshold"] = _obj.SetScreenDetectionThreshold
	_setters["printdetectionthreshold"] = _obj.SetPrintDetectionThreshold
	_setters["phototamperingthreshold"] = _obj.SetPhotoTamperingThreshold
	_setters["facematchthreshold"] = _obj.SetFaceMatchThreshold
	_setters["enabletampercheck"] = _obj.SetEnableTamperCheck
	_setters["idexpirythreshold"] = _obj.SetIdExpiryThreshold
	return _obj, _setters
}

func (obj *UqudoCountryIdVerificationConfig) Init() {
	newObj, _ := NewUqudoCountryIdVerificationConfig()
	*obj = *newObj
}

func (obj *UqudoCountryIdVerificationConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *UqudoCountryIdVerificationConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.UqudoCountryIdVerificationConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *UqudoCountryIdVerificationConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *UqudoCountryIdVerificationConfig) setDynamicField(v *config.UqudoCountryIdVerificationConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "screendetectionthreshold":
		return obj.SetScreenDetectionThreshold(v.ScreenDetectionThreshold, true, nil)
	case "printdetectionthreshold":
		return obj.SetPrintDetectionThreshold(v.PrintDetectionThreshold, true, nil)
	case "phototamperingthreshold":
		return obj.SetPhotoTamperingThreshold(v.PhotoTamperingThreshold, true, nil)
	case "facematchthreshold":
		return obj.SetFaceMatchThreshold(v.FaceMatchThreshold, true, nil)
	case "enabletampercheck":
		return obj.SetEnableTamperCheck(v.EnableTamperCheck, true, nil)
	case "idexpirythreshold":
		return obj.SetIdExpiryThreshold(v.IdExpiryThreshold, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *UqudoCountryIdVerificationConfig) setDynamicFields(v *config.UqudoCountryIdVerificationConfig, dynamic bool, path []string) (err error) {

	err = obj.SetScreenDetectionThreshold(v.ScreenDetectionThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPrintDetectionThreshold(v.PrintDetectionThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPhotoTamperingThreshold(v.PhotoTamperingThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFaceMatchThreshold(v.FaceMatchThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableTamperCheck(v.EnableTamperCheck, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIdExpiryThreshold(v.IdExpiryThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *UqudoCountryIdVerificationConfig) setStaticFields(v *config.UqudoCountryIdVerificationConfig) error {

	return nil
}

func (obj *UqudoCountryIdVerificationConfig) SetScreenDetectionThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *UqudoCountryIdVerificationConfig.ScreenDetectionThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ScreenDetectionThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ScreenDetectionThreshold")
	}
	return nil
}
func (obj *UqudoCountryIdVerificationConfig) SetPrintDetectionThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *UqudoCountryIdVerificationConfig.PrintDetectionThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._PrintDetectionThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "PrintDetectionThreshold")
	}
	return nil
}
func (obj *UqudoCountryIdVerificationConfig) SetPhotoTamperingThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *UqudoCountryIdVerificationConfig.PhotoTamperingThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._PhotoTamperingThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "PhotoTamperingThreshold")
	}
	return nil
}
func (obj *UqudoCountryIdVerificationConfig) SetFaceMatchThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *UqudoCountryIdVerificationConfig.FaceMatchThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._FaceMatchThreshold, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "FaceMatchThreshold")
	}
	return nil
}
func (obj *UqudoCountryIdVerificationConfig) SetEnableTamperCheck(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *UqudoCountryIdVerificationConfig.EnableTamperCheck", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableTamperCheck, 1)
	} else {
		atomic.StoreUint32(&obj._EnableTamperCheck, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableTamperCheck")
	}
	return nil
}
func (obj *UqudoCountryIdVerificationConfig) SetIdExpiryThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *UqudoCountryIdVerificationConfig.IdExpiryThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._IdExpiryThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "IdExpiryThreshold")
	}
	return nil
}

func NewWalkthroughScreenConfig() (_obj *WalkthroughScreenConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &WalkthroughScreenConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_FeatureConfig, _fieldSetters := genapp.NewFeatureConfig()
	_obj._FeatureConfig = _FeatureConfig
	helper.AddFieldSetters("featureconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *WalkthroughScreenConfig) Init() {
	newObj, _ := NewWalkthroughScreenConfig()
	*obj = *newObj
}

func (obj *WalkthroughScreenConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *WalkthroughScreenConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.WalkthroughScreenConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *WalkthroughScreenConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *WalkthroughScreenConfig) setDynamicField(v *config.WalkthroughScreenConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "featureconfig":
		return obj._FeatureConfig.Set(v.FeatureConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *WalkthroughScreenConfig) setDynamicFields(v *config.WalkthroughScreenConfig, dynamic bool, path []string) (err error) {

	err = obj._FeatureConfig.Set(v.FeatureConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *WalkthroughScreenConfig) setStaticFields(v *config.WalkthroughScreenConfig) error {

	return nil
}

func NewFlags() (_obj *Flags, _setters map[string]dynconf.SetFunc) {
	_obj = &Flags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableactiveproductsbypancheck"] = _obj.SetEnableActiveProductsByPANCheck
	return _obj, _setters
}

func (obj *Flags) Init() {
	newObj, _ := NewFlags()
	*obj = *newObj
}

func (obj *Flags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Flags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Flags)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Flags) setDynamicField(v *config.Flags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enableactiveproductsbypancheck":
		return obj.SetEnableActiveProductsByPANCheck(v.EnableActiveProductsByPANCheck, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Flags) setDynamicFields(v *config.Flags, dynamic bool, path []string) (err error) {

	err = obj.SetEnableActiveProductsByPANCheck(v.EnableActiveProductsByPANCheck, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Flags) setStaticFields(v *config.Flags) error {

	obj._TrimDebugMessageFromStatus = v.TrimDebugMessageFromStatus
	obj._EnableUpdateUserComms = v.EnableUpdateUserComms
	return nil
}

func (obj *Flags) SetEnableActiveProductsByPANCheck(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableActiveProductsByPANCheck", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableActiveProductsByPANCheck, 1)
	} else {
		atomic.StoreUint32(&obj._EnableActiveProductsByPANCheck, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableActiveProductsByPANCheck")
	}
	return nil
}

func NewVKYC() (_obj *VKYC, _setters map[string]dynconf.SetFunc) {
	_obj = &VKYC{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["instructionpageskipoptiontime"] = _obj.SetInstructionPageSkipOptionTime
	_setters["landingpageskipoptiontime"] = _obj.SetLandingPageSkipOptionTime
	_setters["enabledemandmanagement"] = _obj.SetEnableDemandManagement
	_setters["enablepassportfaceimagecheckfornrexpiry"] = _obj.SetEnablePassportFaceImageCheckForNRExpiry
	_setters["showauditoracceptedtiletime"] = _obj.SetShowAuditorAcceptedTileTime
	_setters["nrkycexpiryforvkyc"] = _obj.SetNRKYCExpiryForVKYC

	_obj._Option = &syncmap.Map[string, *VKYCOption]{}
	_setters["option"] = _obj.SetOption
	_EnableVKYCFlowV2, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableVKYCFlowV2 = _EnableVKYCFlowV2
	helper.AddFieldSetters("enablevkycflowv2", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *VKYC) Init() {
	newObj, _ := NewVKYC()
	*obj = *newObj
}

func (obj *VKYC) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *VKYC) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.VKYC)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *VKYC) setDynamicField(v *config.VKYC, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "instructionpageskipoptiontime":
		return obj.SetInstructionPageSkipOptionTime(v.InstructionPageSkipOptionTime, true, nil)
	case "landingpageskipoptiontime":
		return obj.SetLandingPageSkipOptionTime(v.LandingPageSkipOptionTime, true, nil)
	case "enabledemandmanagement":
		return obj.SetEnableDemandManagement(v.EnableDemandManagement, true, nil)
	case "enablepassportfaceimagecheckfornrexpiry":
		return obj.SetEnablePassportFaceImageCheckForNRExpiry(v.EnablePassportFaceImageCheckForNRExpiry, true, nil)
	case "showauditoracceptedtiletime":
		return obj.SetShowAuditorAcceptedTileTime(v.ShowAuditorAcceptedTileTime, true, nil)
	case "nrkycexpiryforvkyc":
		return obj.SetNRKYCExpiryForVKYC(v.NRKYCExpiryForVKYC, true, nil)
	case "option":
		return obj.SetOption(v.Option, true, path)
	case "enablevkycflowv2":
		return obj._EnableVKYCFlowV2.Set(v.EnableVKYCFlowV2, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *VKYC) setDynamicFields(v *config.VKYC, dynamic bool, path []string) (err error) {

	err = obj.SetInstructionPageSkipOptionTime(v.InstructionPageSkipOptionTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLandingPageSkipOptionTime(v.LandingPageSkipOptionTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableDemandManagement(v.EnableDemandManagement, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnablePassportFaceImageCheckForNRExpiry(v.EnablePassportFaceImageCheckForNRExpiry, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetShowAuditorAcceptedTileTime(v.ShowAuditorAcceptedTileTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNRKYCExpiryForVKYC(v.NRKYCExpiryForVKYC, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOption(v.Option, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableVKYCFlowV2.Set(v.EnableVKYCFlowV2, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *VKYC) setStaticFields(v *config.VKYC) error {

	return nil
}

func (obj *VKYC) SetInstructionPageSkipOptionTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.InstructionPageSkipOptionTime", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._InstructionPageSkipOptionTime, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "InstructionPageSkipOptionTime")
	}
	return nil
}
func (obj *VKYC) SetLandingPageSkipOptionTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.LandingPageSkipOptionTime", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._LandingPageSkipOptionTime, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "LandingPageSkipOptionTime")
	}
	return nil
}
func (obj *VKYC) SetEnableDemandManagement(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.EnableDemandManagement", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableDemandManagement, 1)
	} else {
		atomic.StoreUint32(&obj._EnableDemandManagement, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableDemandManagement")
	}
	return nil
}
func (obj *VKYC) SetEnablePassportFaceImageCheckForNRExpiry(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.EnablePassportFaceImageCheckForNRExpiry", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnablePassportFaceImageCheckForNRExpiry, 1)
	} else {
		atomic.StoreUint32(&obj._EnablePassportFaceImageCheckForNRExpiry, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnablePassportFaceImageCheckForNRExpiry")
	}
	return nil
}
func (obj *VKYC) SetShowAuditorAcceptedTileTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.ShowAuditorAcceptedTileTime", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ShowAuditorAcceptedTileTime, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ShowAuditorAcceptedTileTime")
	}
	return nil
}
func (obj *VKYC) SetNRKYCExpiryForVKYC(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.NRKYCExpiryForVKYC", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._NRKYCExpiryForVKYC, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "NRKYCExpiryForVKYC")
	}
	return nil
}
func (obj *VKYC) SetOption(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.VKYCOption)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.Option", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._Option, v, dynamic, path)

}

func NewVKYCOption() (_obj *VKYCOption, _setters map[string]dynconf.SetFunc) {
	_obj = &VKYCOption{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["vkycenablepercentage"] = _obj.SetVkycEnablePercentage
	_setters["minandroidversion"] = _obj.SetMinAndroidVersion
	_setters["miniosversion"] = _obj.SetMinIOSVersion
	_setters["isvkycapprovalblocking"] = _obj.SetIsVKYCApprovalBlocking
	_setters["enablevkycscheduleflow"] = _obj.SetEnableVKYCScheduleFlow
	_setters["skipoptionflag"] = _obj.SetSkipOptionFlag
	_setters["ignorefullkycuser"] = _obj.SetIgnoreFullKYCUser
	_setters["instructionpageskipoptionflag"] = _obj.SetInstructionPageSkipOptionFlag
	return _obj, _setters
}

func (obj *VKYCOption) Init() {
	newObj, _ := NewVKYCOption()
	*obj = *newObj
}

func (obj *VKYCOption) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *VKYCOption) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.VKYCOption)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCOption", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *VKYCOption) setDynamicField(v *config.VKYCOption, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "vkycenablepercentage":
		return obj.SetVkycEnablePercentage(v.VkycEnablePercentage, true, nil)
	case "minandroidversion":
		return obj.SetMinAndroidVersion(v.MinAndroidVersion, true, nil)
	case "miniosversion":
		return obj.SetMinIOSVersion(v.MinIOSVersion, true, nil)
	case "isvkycapprovalblocking":
		return obj.SetIsVKYCApprovalBlocking(v.IsVKYCApprovalBlocking, true, nil)
	case "enablevkycscheduleflow":
		return obj.SetEnableVKYCScheduleFlow(v.EnableVKYCScheduleFlow, true, nil)
	case "skipoptionflag":
		return obj.SetSkipOptionFlag(v.SkipOptionFlag, true, nil)
	case "ignorefullkycuser":
		return obj.SetIgnoreFullKYCUser(v.IgnoreFullKYCUser, true, nil)
	case "instructionpageskipoptionflag":
		return obj.SetInstructionPageSkipOptionFlag(v.InstructionPageSkipOptionFlag, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *VKYCOption) setDynamicFields(v *config.VKYCOption, dynamic bool, path []string) (err error) {

	err = obj.SetVkycEnablePercentage(v.VkycEnablePercentage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinAndroidVersion(v.MinAndroidVersion, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinIOSVersion(v.MinIOSVersion, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsVKYCApprovalBlocking(v.IsVKYCApprovalBlocking, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableVKYCScheduleFlow(v.EnableVKYCScheduleFlow, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSkipOptionFlag(v.SkipOptionFlag, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIgnoreFullKYCUser(v.IgnoreFullKYCUser, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetInstructionPageSkipOptionFlag(v.InstructionPageSkipOptionFlag, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *VKYCOption) setStaticFields(v *config.VKYCOption) error {

	return nil
}

func (obj *VKYCOption) SetVkycEnablePercentage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCOption.VkycEnablePercentage", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._VkycEnablePercentage, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "VkycEnablePercentage")
	}
	return nil
}
func (obj *VKYCOption) SetMinAndroidVersion(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCOption.MinAndroidVersion", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MinAndroidVersion, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinAndroidVersion")
	}
	return nil
}
func (obj *VKYCOption) SetMinIOSVersion(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCOption.MinIOSVersion", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MinIOSVersion, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinIOSVersion")
	}
	return nil
}
func (obj *VKYCOption) SetIsVKYCApprovalBlocking(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCOption.IsVKYCApprovalBlocking", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsVKYCApprovalBlocking, 1)
	} else {
		atomic.StoreUint32(&obj._IsVKYCApprovalBlocking, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsVKYCApprovalBlocking")
	}
	return nil
}
func (obj *VKYCOption) SetEnableVKYCScheduleFlow(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCOption.EnableVKYCScheduleFlow", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableVKYCScheduleFlow, 1)
	} else {
		atomic.StoreUint32(&obj._EnableVKYCScheduleFlow, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableVKYCScheduleFlow")
	}
	return nil
}
func (obj *VKYCOption) SetSkipOptionFlag(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCOption.SkipOptionFlag", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SkipOptionFlag, 1)
	} else {
		atomic.StoreUint32(&obj._SkipOptionFlag, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SkipOptionFlag")
	}
	return nil
}
func (obj *VKYCOption) SetIgnoreFullKYCUser(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCOption.IgnoreFullKYCUser", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IgnoreFullKYCUser, 1)
	} else {
		atomic.StoreUint32(&obj._IgnoreFullKYCUser, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IgnoreFullKYCUser")
	}
	return nil
}
func (obj *VKYCOption) SetInstructionPageSkipOptionFlag(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCOption.InstructionPageSkipOptionFlag", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._InstructionPageSkipOptionFlag, 1)
	} else {
		atomic.StoreUint32(&obj._InstructionPageSkipOptionFlag, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "InstructionPageSkipOptionFlag")
	}
	return nil
}

func NewUserCacheConfig() (_obj *UserCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &UserCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled
	_setters["cachettl"] = _obj.SetCacheTTl
	return _obj, _setters
}

func (obj *UserCacheConfig) Init() {
	newObj, _ := NewUserCacheConfig()
	*obj = *newObj
}

func (obj *UserCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *UserCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.UserCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *UserCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *UserCacheConfig) setDynamicField(v *config.UserCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	case "cachettl":
		return obj.SetCacheTTl(v.CacheTTl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *UserCacheConfig) setDynamicFields(v *config.UserCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCacheTTl(v.CacheTTl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *UserCacheConfig) setStaticFields(v *config.UserCacheConfig) error {

	obj._UserIdPrefix = v.UserIdPrefix
	return nil
}

func (obj *UserCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *UserCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}
func (obj *UserCacheConfig) SetCacheTTl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *UserCacheConfig.CacheTTl", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CacheTTl, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CacheTTl")
	}
	return nil
}

func NewUserGroupCacheConfig() (_obj *UserGroupCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &UserGroupCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled
	return _obj, _setters
}

func (obj *UserGroupCacheConfig) Init() {
	newObj, _ := NewUserGroupCacheConfig()
	*obj = *newObj
}

func (obj *UserGroupCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *UserGroupCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.UserGroupCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *UserGroupCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *UserGroupCacheConfig) setDynamicField(v *config.UserGroupCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *UserGroupCacheConfig) setDynamicFields(v *config.UserGroupCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *UserGroupCacheConfig) setStaticFields(v *config.UserGroupCacheConfig) error {

	obj._UserGroupEmailPrefix = v.UserGroupEmailPrefix
	obj._CacheTTl = v.CacheTTl
	return nil
}

func (obj *UserGroupCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *UserGroupCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}

func NewMinimalUserCacheConfig() (_obj *MinimalUserCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &MinimalUserCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscachingenabled"] = _obj.SetIsCachingEnabled
	_setters["cachettl"] = _obj.SetCacheTTl
	return _obj, _setters
}

func (obj *MinimalUserCacheConfig) Init() {
	newObj, _ := NewMinimalUserCacheConfig()
	*obj = *newObj
}

func (obj *MinimalUserCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *MinimalUserCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.MinimalUserCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *MinimalUserCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *MinimalUserCacheConfig) setDynamicField(v *config.MinimalUserCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscachingenabled":
		return obj.SetIsCachingEnabled(v.IsCachingEnabled, true, nil)
	case "cachettl":
		return obj.SetCacheTTl(v.CacheTTl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *MinimalUserCacheConfig) setDynamicFields(v *config.MinimalUserCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCachingEnabled(v.IsCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCacheTTl(v.CacheTTl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *MinimalUserCacheConfig) setStaticFields(v *config.MinimalUserCacheConfig) error {

	return nil
}

func (obj *MinimalUserCacheConfig) SetIsCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *MinimalUserCacheConfig.IsCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCachingEnabled")
	}
	return nil
}
func (obj *MinimalUserCacheConfig) SetCacheTTl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *MinimalUserCacheConfig.CacheTTl", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CacheTTl, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CacheTTl")
	}
	return nil
}

func NewCreditReportConfig() (_obj *CreditReportConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CreditReportConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["creditreportpresenceenabled"] = _obj.SetCreditReportPresenceEnabled
	_ExperianConsentConfig, _fieldSetters := NewExperianConsentConfig()
	_obj._ExperianConsentConfig = _ExperianConsentConfig
	helper.AddFieldSetters("experianconsentconfig", _fieldSetters, _setters)
	_DownloadWaitConfigForWealthBuilder, _fieldSetters := NewDownloadWaitConfigForWealthBuilder()
	_obj._DownloadWaitConfigForWealthBuilder = _DownloadWaitConfigForWealthBuilder
	helper.AddFieldSetters("downloadwaitconfigforwealthbuilder", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *CreditReportConfig) Init() {
	newObj, _ := NewCreditReportConfig()
	*obj = *newObj
}

func (obj *CreditReportConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CreditReportConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CreditReportConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditReportConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CreditReportConfig) setDynamicField(v *config.CreditReportConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "creditreportpresenceenabled":
		return obj.SetCreditReportPresenceEnabled(v.CreditReportPresenceEnabled, true, nil)
	case "experianconsentconfig":
		return obj._ExperianConsentConfig.Set(v.ExperianConsentConfig, true, path)
	case "downloadwaitconfigforwealthbuilder":
		return obj._DownloadWaitConfigForWealthBuilder.Set(v.DownloadWaitConfigForWealthBuilder, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CreditReportConfig) setDynamicFields(v *config.CreditReportConfig, dynamic bool, path []string) (err error) {

	err = obj.SetCreditReportPresenceEnabled(v.CreditReportPresenceEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._ExperianConsentConfig.Set(v.ExperianConsentConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DownloadWaitConfigForWealthBuilder.Set(v.DownloadWaitConfigForWealthBuilder, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CreditReportConfig) setStaticFields(v *config.CreditReportConfig) error {

	return nil
}

func (obj *CreditReportConfig) SetCreditReportPresenceEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditReportConfig.CreditReportPresenceEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._CreditReportPresenceEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._CreditReportPresenceEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "CreditReportPresenceEnabled")
	}
	return nil
}

func NewExperianConsentConfig() (_obj *ExperianConsentConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ExperianConsentConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["consentextension"] = _obj.SetConsentExtension
	_setters["consentexpiry"] = _obj.SetConsentExpiry
	return _obj, _setters
}

func (obj *ExperianConsentConfig) Init() {
	newObj, _ := NewExperianConsentConfig()
	*obj = *newObj
}

func (obj *ExperianConsentConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ExperianConsentConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ExperianConsentConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ExperianConsentConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ExperianConsentConfig) setDynamicField(v *config.ExperianConsentConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "consentextension":
		return obj.SetConsentExtension(v.ConsentExtension, true, nil)
	case "consentexpiry":
		return obj.SetConsentExpiry(v.ConsentExpiry, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ExperianConsentConfig) setDynamicFields(v *config.ExperianConsentConfig, dynamic bool, path []string) (err error) {

	err = obj.SetConsentExtension(v.ConsentExtension, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetConsentExpiry(v.ConsentExpiry, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ExperianConsentConfig) setStaticFields(v *config.ExperianConsentConfig) error {

	return nil
}

func (obj *ExperianConsentConfig) SetConsentExtension(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *ExperianConsentConfig.ConsentExtension", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ConsentExtension, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ConsentExtension")
	}
	return nil
}
func (obj *ExperianConsentConfig) SetConsentExpiry(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *ExperianConsentConfig.ConsentExpiry", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ConsentExpiry, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ConsentExpiry")
	}
	return nil
}

func NewDownloadWaitConfigForWealthBuilder() (_obj *DownloadWaitConfigForWealthBuilder, _setters map[string]dynconf.SetFunc) {
	_obj = &DownloadWaitConfigForWealthBuilder{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxattemptsforcheckingdownloadstatus"] = _obj.SetMaxAttemptsForCheckingDownloadStatus
	_setters["sleepdurationbetweeneachattempt"] = _obj.SetSleepDurationBetweenEachAttempt
	return _obj, _setters
}

func (obj *DownloadWaitConfigForWealthBuilder) Init() {
	newObj, _ := NewDownloadWaitConfigForWealthBuilder()
	*obj = *newObj
}

func (obj *DownloadWaitConfigForWealthBuilder) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DownloadWaitConfigForWealthBuilder) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DownloadWaitConfigForWealthBuilder)
	if !ok {
		return fmt.Errorf("invalid data type %v *DownloadWaitConfigForWealthBuilder", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DownloadWaitConfigForWealthBuilder) setDynamicField(v *config.DownloadWaitConfigForWealthBuilder, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maxattemptsforcheckingdownloadstatus":
		return obj.SetMaxAttemptsForCheckingDownloadStatus(v.MaxAttemptsForCheckingDownloadStatus, true, nil)
	case "sleepdurationbetweeneachattempt":
		return obj.SetSleepDurationBetweenEachAttempt(v.SleepDurationBetweenEachAttempt, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DownloadWaitConfigForWealthBuilder) setDynamicFields(v *config.DownloadWaitConfigForWealthBuilder, dynamic bool, path []string) (err error) {

	err = obj.SetMaxAttemptsForCheckingDownloadStatus(v.MaxAttemptsForCheckingDownloadStatus, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSleepDurationBetweenEachAttempt(v.SleepDurationBetweenEachAttempt, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DownloadWaitConfigForWealthBuilder) setStaticFields(v *config.DownloadWaitConfigForWealthBuilder) error {

	return nil
}

func (obj *DownloadWaitConfigForWealthBuilder) SetMaxAttemptsForCheckingDownloadStatus(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *DownloadWaitConfigForWealthBuilder.MaxAttemptsForCheckingDownloadStatus", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MaxAttemptsForCheckingDownloadStatus, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxAttemptsForCheckingDownloadStatus")
	}
	return nil
}
func (obj *DownloadWaitConfigForWealthBuilder) SetSleepDurationBetweenEachAttempt(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *DownloadWaitConfigForWealthBuilder.SleepDurationBetweenEachAttempt", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._SleepDurationBetweenEachAttempt, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SleepDurationBetweenEachAttempt")
	}
	return nil
}

func NewUserDevicePropertiesCacheConfig() (_obj *UserDevicePropertiesCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &UserDevicePropertiesCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscacheenabled"] = _obj.SetIsCacheEnabled
	_setters["cachettl"] = _obj.SetCacheTTL
	return _obj, _setters
}

func (obj *UserDevicePropertiesCacheConfig) Init() {
	newObj, _ := NewUserDevicePropertiesCacheConfig()
	*obj = *newObj
}

func (obj *UserDevicePropertiesCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *UserDevicePropertiesCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.UserDevicePropertiesCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *UserDevicePropertiesCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *UserDevicePropertiesCacheConfig) setDynamicField(v *config.UserDevicePropertiesCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscacheenabled":
		return obj.SetIsCacheEnabled(v.IsCacheEnabled, true, nil)
	case "cachettl":
		return obj.SetCacheTTL(v.CacheTTL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *UserDevicePropertiesCacheConfig) setDynamicFields(v *config.UserDevicePropertiesCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCacheEnabled(v.IsCacheEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCacheTTL(v.CacheTTL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *UserDevicePropertiesCacheConfig) setStaticFields(v *config.UserDevicePropertiesCacheConfig) error {

	return nil
}

func (obj *UserDevicePropertiesCacheConfig) SetIsCacheEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *UserDevicePropertiesCacheConfig.IsCacheEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCacheEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCacheEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCacheEnabled")
	}
	return nil
}
func (obj *UserDevicePropertiesCacheConfig) SetCacheTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *UserDevicePropertiesCacheConfig.CacheTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CacheTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CacheTTL")
	}
	return nil
}
