Application:
  Environment: "test"
  Name: "user"

EpifiDb:
  DbType: "CRDB"
  AppName: "user"
  StatementTimeout: 5m
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi_test"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

FeatureEngineeringDb:
  DbType: "PGDB"
  AppName: "user"
  StatementTimeout: 5m
  Name: "feature_engineering_test"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

AWS:
  Region: "ap-south-1"
  S3:
    UsersBucketName: "epifi-dev-users"
    CreditReportsBucketName: "epifi-credit-reports"

RedisOptions:
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 3
  HystrixCommand:
    CommandName: "user_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 100ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

OnboardingStageEventPublisher:
  TopicName: "onboarding-stage-update-topic"

Consent:
  Versions:
    FiTnc: 0
    FedTnc: 0
    FiPrivacyPolicy: 0
    FiWealthTnc: 0
    FiP2pInvestmentTnc: 0
    SecureUsageTnC: 1
  Urls:
    FiTnc: "https://web.staging.pointz.in/T&C"
    FiTncNonResident: "https://fi.money/tnc/nr"
    FedTnc: "https://www.federalbank.co.in/epifi-tandc#CASA"
    FiPrivacyPolicy: "https://web.staging.pointz.in/privacy"
    FiWealthTnc: "https://web.staging.pointz.in/wealth/TnC"
    FiP2pInvestmentTnc: "https://web.staging.pointz.in/wealth/TnC"

Onboarding:
  NonResidentCrossValidationConfig:
    # todo (NRI) match with prod and QA config
    CrossValidationDataSources:
      FEATURE_NON_RESIDENT_SA:FLOW_PAN:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_GENDER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
      FEATURE_NON_RESIDENT_SA_QATAR:FLOW_PAN:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_NUMBER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_DATE_OF_EXPIRY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
      FEATURE_NON_RESIDENT_SA:FLOW_FORM_60:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_GENDER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
      FEATURE_NON_RESIDENT_SA_QATAR:FLOW_FORM_60:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_NUMBER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_DATE_OF_EXPIRY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
  ConfirmCardMailingAddress:
    EnableConfirmCardMailingAddressV2: true
  RiskScreening:
    EnabledRiskChecks:
      - 1 # KYC PINCODE
      - 2 # GEO LOCATION PINCODE
      - 3 # ONBOARDING RISK MODEL
      - 4 # GEO LOCATION LAT LONG
      - 5 # FINITE CODE
      - 6 # RISKY DEVICE
      - 9 # ONBOARDING VELOCITY
      - 10 # EMAIL ID
      - 11 # PHONE NUMBER
      - 14 # INCOME EMPLOYMENT DISCREPANCY
      - 13 # HUNTER
    EssentialRiskChecks:
      - 1
      - 2
      - 4
  NextActionDecisionCacheConfig:
    IsCacheEnabled: true
    CacheTTL: "2160h" # 3 months
  HealthConfig:
    - CHECK_CREDIT_REPORT_PRESENCE:
        From: "2022-03-28 11:20:00.000"
        To: "2022-03-29 23:20:00.000"
        HealthStatus: 2
        Message: "Test message 2"
  OrchestratorLockConfig:
    Timeout: 5s
    SleepWindow: 500ms
  Flags:
    EnableSaDeclarationstage:
      MinAndroidVersion: 1
      MinIOSVersion: 1
      FallbackToEnableFeature: true
      DisableFeature: false
    MarkCKYCSuccessWithoutCKYCDownload: true
    EnableParentNamePrefillFromCKYC: true
    SkipLocationCheckForNROnboarding: false
    EnableRiskScreeningForD2H: false
    EnableUpdateProfileDetailsStage: true
    AddMoneyBalanceOptions:
      - Amount:
          CurrencyCode: "INR"
          Units: 1000
          Nanos: 0
        IsEnabled: true
    EnableSyncOnboarding: true
    EnableSecureUsageGuidelinesConsent: false
    AllowManualReviewUsers: false
    PanAutofill:
      PrefetchCreditReportWithoutPan: true
      RefetchCreditReportOnMismatch: true
    DisabledStages:
      FI_LITE_RISK_SCREENING: true
      ADD_MONEY: true
      PRE_CUSTOMER_CREATION_DEDUPE_CHECK: true
      SHIPPING_ADDRESS_UPDATE: true
    EnableAffluenceV2: false
    EnableSavingsIntroScreen:
      MinAndroidVersion: 10
      MinIOSVersion: 10
      FallbackToEnableFeature: false
      DisableFeature: false
    EnableSMSParserConsentScreen:
      MinAndroidVersion: 10
      MinIOSVersion: 10
      FallbackToEnableFeature: true
      DisableFeature: true
  WealthAnalyserFeature:
    MinIOSVersion: 556
    MinAndroidVersion: 401
    FallbackToEnableFeature: true
    DisableFeature: false
  IsPanDOBDropOffToWealthAnalyserEnabled: true

  ABFeatureReleaseConfig:
    FeatureConstraints:
      - FEATURE_ENABLE_CONSENT_SCREEN_V2:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 9999999
              MinIOSVersion: 9999999
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - ATT_IOS_PERMISSION_PROMPT:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 1 # enabling for all in iOS as it just sends a flag in screen-options
            StickyPercentageConstraintConfig:
              RolloutPercentage: 100
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - ONBOARDING_ADD_FUNDS_V2:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 0
              MinIOSVersion: 0
            StickyPercentageConstraintConfig:
              RolloutPercentage: 100
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - SCREENER_CHOICE_PAGE:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 0
              MinIOSVersion: 0
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - REFERRAL_SCREEN_DURING_ONBOARDING_V1:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
          Buckets:
            - ONE:
                Start: 0
                End: 99
  AccountSetupMaxStuckDuration: 20m
  EnableTriggerNROAccountCreation:
    MinAndroidVersion: 99999
    MinIOSVersion: 99999
    FallbackToEnableFeature: true
    DisableFeature: true
  OnboardingVelocityConfig:
    QueryRangeDuration: 24h
    Threshold: 3
    BucketExpiry: 24h
    BucketPrecision: 3

  SyncOnboardingSqsPublisher:
    QueueName: "sync-onboarding-queue"

  UNNameCheckMailingAddress:
    FromAddress: "<EMAIL>"
    ToAddress: "<EMAIL>"
    FromName: "unnamecheck-test"
    ToName: "unnamecheck-test"

  AWS:
    Region: "ap-south-1"
    S3:
      BucketNames:
        BucketUsers: "epifi-dev-users"

  KYCDedupeRetryCount: 3

  StuckUserNudges:
    DEBIT_CARD_PIN_SETUP:
      - StuckDuration: "10s"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Your debit card is waiting for you"
            Body: "Set a secret 4-digit PIN and start using your Visa Platinum Debit Card for online payments!🔑"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
          Weekdays:
            - 0
            - 1
            - 2
            - 3
            - 4
            - 5
            - 6
    CARD_CREATION:
      - StuckDuration: "10s"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Your debit card is waiting for you"
            Body: "Set a secret 4-digit PIN and start using your Visa Platinum Debit Card for online payments!🔑"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    KYC_AND_LIVENESS_COMPLETION:
      - StuckDuration: "10s"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Your debit card is waiting for you"
            Body: "Set a secret 4-digit PIN and start using your Visa Platinum Debit Card for online payments!🔑"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    INITIATE_CKYC:
      - StuckDuration: "10s"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 57
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    EKYC:
      - StuckDuration: "10s"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 58
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    NAME_MISMATCH:
      - StuckDuration: "10s"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 62
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    LIVENESS:
      - StuckDuration: "10s"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 60
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    EMPLOYMENT_VERIFICATION:
      - StuckDuration: "5m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Are you ready for your ₹250 reward?😍"
            Body: "Get up to ₹250 once you complete your sign-up on Fi. This will take just 5 minutes. Start now! ➡️"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "48h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "💳Ready for your cool debit card?"
            Body: "Just complete your sign-up and get offers from Swiggy, Amazon, Myntra, and more. It takes just 5 minutes. Start now! ➡️"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "72h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "💪Your personal information is safe."
            Body: "Finish your KYC and your savings account will be ready. Your money is insured up to ₹5L."
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    DOB_AND_PAN:
      - StuckDuration: "24h"
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 42
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"

  StuckUserAlerts:
    FEATURE_SA:TNC_CONSENT:
      - StuckDuration: "30s"
    FEATURE_SA:MOTHER_FATHER_NAME:
      - StuckDuration: "30s"
    FEATURE_SA:DEBIT_CARD_PIN_SETUP:
      - StuckDuration: "30s"
    FEATURE_SA:ACCOUNT_CREATION:
      - StuckDuration: "4m"

  BlockOnboardingFromTime: "2021-06-10 21:00:00.000"
  BlockOnboardingTillTime: "2021-06-10 21:10:00.000"
  BlockOnboardingMsg: "Our partner bank has scheduled maintenance activity from 11:45PM-3AM. Please try after 3AM."
  SyncOnboardingInterval: "5m"
  SyncOnboardingCutOff: "1h"
  ReferralOfferCodesABReleaseConfig:
    FeatureConstraints:
      - FEATURE_UNSPECIFIED:
          ConstraintConfig:
            StickyPercentageConstraintConfig:
              RolloutPercentage: 100
          Buckets:
            - ONE:
                Start: 0
                End: 49
  LivenessSummaryExpiryDuration: "90s"
  NrBucketName: "epifi-nrusers"

Flags:
  TrimDebugMessageFromStatus: false
  EnableUpdateUserComms: false

NewOnFi:
  Count: 4
  TimeWindow:
    Value: 24
    TimeUnit: "Hour"

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    DbUsernamePassword: "{\"username\": \"root\", \"password\": \"\"}"
    PgdbCredentials: "{\"username\": \"root\", \"password\": \"\"}"
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"

OnboardingUserUpdatePublisher:
  TopicName: "user-update-topic"

OnboardingUserUpdateVKYCSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "onboarding-user-update-vkyc-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

ShippingAddressUpdatePublisher:
  QueueName: "shipping-address-update-queue"

ShippingAddressUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "shipping-address-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

ShippingAddressUpdateCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "shipping-address-update-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

UserAccessRevokeUpdatePublisher:
  TopicName: "user-access-revoke-update-topic"
ShippingAddressUpdateEventPublisher:
  TopicName: "user-shipping-address-update-topic"

VKYC:
  Option:
    VKYC_OPTION_LSO:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: false
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_CKYC_O:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_ONBOARDING:
      IsVKYCApprovalBlocking: false
      VkycEnablePercentage: 0
      MinAndroidVersion: 131
      MinIOSVersion: 251
      SkipOptionFlag: true
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: true
    VKYC_OPTION_STUDENT:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_PARTIAL_KYC_DEDUPE:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_EKYC_NUMBER_MISMATCH:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_LOW_QUALITY_USERS:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_FI_LITE_CC_USERS:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_FI_LITE_USERS:
      IsVKYCApprovalBlocking: false
      VkycEnablePercentage: 0
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_CLOSED_ACCOUNT_REOPENING:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_NON_RESIDENT_ONBOARDING:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: false
      InstructionPageSkipOptionFlag: false
  EnableDemandManagement: false

VKYCUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "vkyc-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

EKYCSuccessSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "kyc-ekyc-success-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

ConsentEventPublisher:
  TopicName: "consent-topic"

VpaMigrationConsentPublisher:
  QueueName: "upi-vpa-migration-consent-queue"

# Time should be in HH:MM 24-hour format
# It is quick fix for CSIS time and will be removed when CSIS service is up.
CSIS:
  IsCsisEnable: false
  StartTime: "01:30"
  EndTime: "03:30"

CreditReportPresencePublisher:
  QueueName: "user-credit-report-presence-queue"

CreditReportPresenceSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "user-credit-report-presence-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

CreditReportVerificationPublisher:
  QueueName: "user-credit-report-verification-queue"

CreditReportVerificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "user-credit-report-verification-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

Screening:
  CreditReportPresenceCheck:
    CreditReportPresenceCheckMaxDuration: 30s
  CreditReportVerification:
    CreditScoreThreshold: 600
    CreditReportVerificationMaxDuration: 30s

UserUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "user-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

BankCustomerUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "bank-customer-update-event-onboarding-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

CreditReportVerificationEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "onboarding-credit-report-verification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

LivManualReviewEventSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "onboarding-liveness-manual-review-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 2
      TimeUnit: "Second"

AfPurchasePublisher:
  QueueName: "event-af-purchase-queue"

EventsAfPurchaseSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "event-af-purchase-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

EventsCompletedTnCPublisher:
  QueueName: "event-completed-tnc-queue"

EventsCompletedTnCSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "event-completed-tnc-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

UserCacheConfig:
  IsCachingEnabled: true
  UserIdPrefix: "user_id_"
  CacheTTl: "2m"

UserGroupCacheConfig:
  IsCachingEnabled: true
  UserGroupEmailPrefix: "USER_GROUP_MAPPING"
  CacheTTl: "2m"

Events:
  AfPurchasePublishDelay: "1m"
  CompletedTnCPublishDelay: "1s"

CreditReportConfig:
  ExperianConsentConfig:
    ConsentExtension: "2160h"
    ConsentExpiry: "4320h"
  CreditReportPresenceEnabled: false

CreditReportDerivedAttributesPublisher:
  QueueName: "credit-report-derived-attributes-queue"

CreditReportDerivedAttributesSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "credit-report-derived-attributes-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

ProcessOnboardingEventUserContactSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "user-contact-onboarding-stage-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

ProcessAfuEventUserContactSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "user-contact-auth-factor-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

ProcessDeleteUserSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "user-contact-delete-user-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

UserDevicePropertiesUpdatePublisher:
  TopicName: "user-device-properties-update-topic"

DeleteUserPublisher:
  TopicName: "delete-user-event-topic"

SyncOnboardingSqsPublisher:
  QueueName: "sync-onboarding-queue"

ProcessAccessRevokeCooldownPublisher:
  QueueName: "user-access-revoke-cooldown-queue"

ProcessAccessRevokeCooldownSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "user-access-revoke-cooldown-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 50
      TimeUnit: "Second"

SyncOnboardingSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "sync-onboarding-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 1
      TimeUnit: "Second"

VKYCCallCompletedEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "user-vkyc-call-completed-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

InHouseVkycCallCompletedEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "user-inhouse-vkyc-call-completed-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

ProcessSavingsAccountUpdateEvent:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "onboarding-savings-account-state-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

ProcessCardCreationEvent:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "onboarding-card-creation-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 5
      TimeUnit: "Minute"

QuestSdk:
  Disable: true

UserDevicePropertiesCacheConfig:
  IsCacheEnabled: true
  CacheTTL: "15m"

WhitelistNumberHashesForUAENR:
  - "741d5bacc019d561f21fbded65324155e5072d3f"   #************

WhitelistNumberHashesForQatarNR:
  - "b5a160d47e2df8100be6f3fbb8cdfa4f0c1280bb"   #************
