Application:
  Environment: "staging"
  Name: "user"
  IsSecureRedis: true

EpifiDb:
  DbType: "CRDB"
  AppName: "user"
  StatementTimeout: 1s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

FeatureEngineeringDb:
  DbType: "PGDB"
  AppName: "user"
  StatementTimeout: 5s
  Name: "feature_engineering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "staging/rds/postgres/feature-engineering"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

AWS:
  Region: "ap-south-1"
  S3:
    UsersBucketName: "epifi-staging-dev-users"
    CreditReportsBucketName: "epifi-staging-credit-reports"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 12
    PoolSize: 20 # this is set to twice the size of default connection pool size
    MinIdleConns: 10 # this allows min. number of conn to be readily available
  HystrixCommand:
    CommandName: "user_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 500ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

OnboardingStageEventPublisher:
  TopicName: "staging-onboarding-stage-update-topic"

Consent:
  Versions:
    FiTnc: 0
    FedTnc: 0
    FiPrivacyPolicy: 0
    FiWealthTnc: 0
    FiP2pInvestmentTnc: 0
    VpaMigration: 0
    SecureUsageTnC: 1
  Urls:
    FiTnc: "https://web.staging.pointz.in/T&C"
    FiTncNonResident: "https://fi.money/tnc/nr"
    FedTnc: "https://www.federalbank.co.in/epifi-tandc#CASA"
    FiPrivacyPolicy: "https://web.staging.pointz.in/privacy"
    FiWealthTnc: "https://web.staging.pointz.in/wealth/TnC"
    FiP2pInvestmentTnc: "https://web.staging.pointz.in/wealth/TnC"

FeatureReleaseConfig:
  FeatureConstraints:
    - ONBOARDING_ADD_FUNDS_V2_2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # internal
    - FEATURE_ONBOARDING_PRE_ACCOUNT_CREATION_ADD_FUNDS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
    - FEATURE_MF_IMPORT_WEALTH_BUILDER_ONBOARDING:
        AppVersionConstraintConfig:
          MinAndroidVersion: 422
          MinIOSVersion: 546
    - FEATURE_MIN_BALANCE_ACCOUNT_SCREEN_SDUI:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 37 # Pay_Experimental
    - FEATURE_SEND_SMS_DATA_WEALTH_BUILDER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 429
          MinIOSVersion: 100000
    - FEATURE_CA_FLOW_WEALTH_BUILDER_ONBOARDING:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL

Onboarding:
  ActorsWhitelistedForPreFunding:
    - "AC2xhKaWombV241217"
  PanValidateV3FeatureConfig:
    MinIOSVersion: 100
    MinAndroidVersion: 100
    FallbackToEnableFeature: true
    DisableFeature: false
  NonResidentCrossValidationConfig:
    CrossValidationDataSources:
      FEATURE_NON_RESIDENT_SA:FLOW_PAN:
        CROSS_VALIDATION_CHECK_USER_NAME:
        - CROSS_VALIDATION_DATA_SOURCE_PAN
        - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_DOB:
        - CROSS_VALIDATION_DATA_SOURCE_PAN
        - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_GENDER:
        - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
        - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
        - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
        - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
        - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
        - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
        - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
        - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
      FEATURE_NON_RESIDENT_SA_QATAR:FLOW_PAN:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
      FEATURE_NON_RESIDENT_SA:FLOW_FORM_60:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_GENDER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
      FEATURE_NON_RESIDENT_SA_QATAR:FLOW_FORM_60:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
  ConfirmCardMailingAddress:
    EnableConfirmCardMailingAddressV2: false
  NextActionDecisionCacheConfig:
    IsCacheEnabled: true
    CacheTTL: "2160h" # 3 months
  ABFeatureReleaseConfig:
    FeatureConstraints:
      - FEATURE_ENABLE_CONSENT_SCREEN_V2:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 9999
              MinIOSVersion: 9999
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - VKYC_NEW_REVIEW_SCREEN:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 0
              MinIOSVersion: 0
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - REFERRAL_SCREEN_DURING_ONBOARDING_V1:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
            UserGroupConstraintConfig:
              AllowedGroups:
                - 1 # INTERNAL
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - ATT_IOS_PERMISSION_PROMPT:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 1 # enabling for all in iOS as it just sends a flag in screen-options
            StickyPercentageConstraintConfig:
              RolloutPercentage: 100
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - AFFLUENT_USER_BONUS_TRANSITION_SCREEN:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
            UserGroupConstraintConfig:
              AllowedGroups:
                - 1 # INTERNAL
          Buckets:
            - ONE:
                Start: 0
                End: 0
      - ONBOARDING_ADD_FUNDS_V2:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 247
              MinIOSVersion: 239
            StickyPercentageConstraintConfig:
              RolloutPercentage: 100
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - AFFLUENT_USER_BONUS_TRANSITION_SCREEN_NON_REFEREE:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 99999
              MinIOSVersion: 99999
            UserGroupConstraintConfig:
              AllowedGroups:
                - 1 # INTERNAL
          Buckets:
            - ONE:
                Start: 0
                End: 0
      - PHONE_NUMBER_AS_REFERRAL_CODE:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 1
              MinIOSVersion: 1
            UserGroupConstraintConfig:
              AllowedGroups:
                - 1 # INTERNAL
                - 2 # FNF
          Buckets:
            - ONE:
                Start: 0
                End: 99
      - SCREENER_CHOICE_PAGE:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 1
              MinIOSVersion: 1
          Buckets:
            - ONE:
                Start: 0
                End: 99
  HealthConfig:
    - CHECK_CREDIT_REPORT_PRESENCE:
        From: "2022-03-28 11:20:00.000"
        To: "2022-03-29 23:20:00.000"
        HealthStatus: 2
        Message: "Test message 2"
  Flags:
    EnableSaDeclarationstage:
      MinAndroidVersion: 1
      MinIOSVersion: 1
      FallbackToEnableFeature: false
      DisableFeature: true
    EnablePanAadharCheckInPreCustomerCreationCheckStage: true
    EnableRiskCheckForNRUser: true
    SkipCountryIdVerification: false
    EnableUpdateProfileDetailsStage: true
    EnableSyncOnboarding: true
    EnableSecureUsageGuidelinesConsent: true
    PanAutofill:
      PrefetchCreditReportWithoutPan: true
      RefetchCreditReportOnMismatch: true
    DisabledStages:
      FORM16_CHECK: true
      LINKEDIN_VERIFICATION: true
      PRE_CUSTOMER_CREATION_DEDUPE_CHECK: true
      SHIPPING_ADDRESS_UPDATE: true
    EnableSavingsIntroScreen:
      MinAndroidVersion: 1
      MinIOSVersion: 1
      FallbackToEnableFeature: false
      DisableFeature: false
  TotalAmountViaOrderAddFunds: true
  MinAndroidVersionForManualBalanceRefreshOnbAddFunds: 259
  MinIosVersionForManualBalanceRefreshOnbAddFunds: 240
  ReferralOfferCodesDuringOnboarding:
    - CODE_1:
        IsEnabled: true
        BeforeAppliedTitle: "<font color='#313234'>FI200: </font><font color='#5d7d4c'>Get up to ₹200</font>"
        AfterAppliedTitle: "<font color='#313234'>\"FI200\" applied</font>"
        BeforeAppliedDesc: "When you add money to your account"
        AfterAppliedDesc: "Get up to ₹200 when you add money"
        BeforeAppliedIconUrl: "https://epifi-icons.pointz.in/referrals/onboarding-tag.png"
        AfterAppliedIconUrl: "https://epifi-icons.pointz.in/referrals/onboarding-success-check.png"
        Code: "FI200"
        UnderlyingFiniteCode: "VNJT9V27WJ"
  ReferralOfferCodesABReleaseConfig:
    FeatureConstraints:
      - FEATURE_UNSPECIFIED:
          ConstraintConfig:
            StickyPercentageConstraintConfig:
              RolloutPercentage: 100
          Buckets:
            - ONE:
                Start: 0
                End: 99 # enabled for all user-layers in staging for testing purposes
  ReferralOfferWidgetsDuringOnboarding:
    - VNJT9V27WJ: #FI200 based
        IsEnabled: true
        LeftIcon: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/offer_widget/onb_offer_widget_ref.png"
        OfferText: "<font color ='#313234'>Offer applied! Get flat </font><font color='#5D7D4C'>₹200 </font><font color='#313234'>when you sign up</font>"
        BgColor: "#E7F7DE"
    - REGULAR:
        IsEnabled: true
        LeftIcon: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/offer_widget/onb_offer_widget_ref.png"
        OfferText: "<font color ='#313234'>Offer applied! Buy your first US stock to claim your rewards</font>"
        BgColor: "#E7F7DE"
  OfferWidgetOnbScreensViaFiniteCodeABReleaseConfig:
    FeatureConstraints:
      - FEATURE_UNSPECIFIED:
          ConstraintConfig:
            AppVersionConstraintConfig:
              MinAndroidVersion: 0
              MinIOSVersion: 0
          Buckets:
            - ONE:
                Start: 0
                End: 99
  DurationToSkipAddFundsForAffluenceClasses:
    - ENTRY_1:
        IsEnabled: false
        AffluenceClass: 1
        Duration: 10m
    - ENTRY_2:
        IsEnabled: false
        AffluenceClass: 2
        Duration: 15m
    - ENTRY_3:
        IsEnabled: false
        AffluenceClass: 3
        Duration: 20m
  WebUrlsForSalaryB2BFlows:
    - FLOW_1:
        IsEnabled: true
        Url: "https://web.staging.pointz.in/signup"
  EnableTriggerNROAccountCreation:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: true
    DisableFeature: false
  AddFundsConfig:
    ShowSkipCtaViaQuest: false
    SkipDurationViaQuest: "15m"
    V2PageMinorVersion: 1
    ShowV2Page: true
  OrderPhysicalDebitCardConfig:
    EnableViaQuest: true
  DurationToSkipAddMoney: 24h
  AccountSetupMaxStuckDuration: 2m
  OnboardingVelocityConfig:
    QueryRangeDuration: 24h
    Threshold: 3
    BucketExpiry: 24h
    BucketPrecision: 3
  SyncOnboardingSqsPublisher:
    QueueName: "staging-sync-onboarding-queue"

  UNNameCheckMailingAddress:
    FromAddress: "<EMAIL>"
    ToAddress: "<EMAIL>"
    FromName: "epifi test mail"
    ToName: "keerthana"

  StuckUserAlerts:
    FEATURE_SA:CUSTOMER_CREATION:
      - StuckDuration: "1m"
    FEATURE_SA:ACCOUNT_CREATION:
      - StuckDuration: "1m"
    FEATURE_SA:SHIPPING_ADDRESS_UPDATE:
      - StuckDuration: "1m"
    FEATURE_SA:CARD_CREATION:
      - StuckDuration: "1m"
    FEATURE_SA:DEBIT_CARD_PIN_SETUP:
      - StuckDuration: "1m"

  CCFiliteStuckUserNudges:
    EMPLOYMENT_VERIFICATION:
      - StuckDuration: "15s"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Status updated ✅"
            Body: "Your eligibility for credit card has been updated on the Fi app. Tap to view now️"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "48s"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "⚠️ Action pending"
            Body: "Complete your Credit Card application on the Fi app now!"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    VKYC:
      - StuckDuration: "15s"
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 77
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "15s"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 226
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "15s"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "📞 Complete your KYC verification call"
            Body: "Tap to complete the video KYC call to get your credit card. \n💡Keep your original PAN ready."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "30s"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "🚨 90% done! 1 more step left..."
            Body: " Just finish a 3-minute video KYC call to verify yourself. Tap to start the call now! "
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "72h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Ready for your video call? 🤓 🤙"
            Body: "Complete your account creation by getting on a quick 3 min video KYC call with us."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    DOB_AND_PAN:
      - StuckDuration: "15s"
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 74
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "15s"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 223
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "30s"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 224
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "15s"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Your credit card is ready for you 💳"
            Body: "Complete your application now on the Fi app & get access to the most rewarding credit card in just 2 min!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Why do we need PAN card details? 💭"
            Body: "Because its required to process your credit card application! Don’t worry, your details are safe. Fill them up now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "36h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Your application is 50% complete!"
            Body: "It wil take just 2 more minutes to access your new Fi-Federal Credit Card. Complete your application now >"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "48h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "3% back on every spend 😱"
            Body: "That's what you get with AmpliFi Fi-Federal Credit Card. Complete your application now"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "96h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "0 Forex & Lounge Access 😱"
            Body: "That's what you get with AmpliFi Fi-Federal Credit Card. Complete your application now"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    EKYC:
      - StuckDuration: "30s"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 225
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "15s"
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 75
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "15s"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "🪪 Aadhaar, get set, go!"
            Body: "Enter your Aadhaar details to continue with your credit card application on the Fi app"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "You are just 2️⃣ steps away"
            Body: "from getting the most rewarding credit card. Complete your application now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "72h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "⚠️ Reminder"
            Body: "Your credit card application is still pending. Complete it now in just 2 minutes on the Fi app"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    LIVENESS:
      - StuckDuration: "15s"
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 76
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "30s"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "90% Done!"
            Body: "It will take hardly a minute to get your hands on your new credit card. Complete your application now"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "⚠️ Action Pending ⚠️"
            Body: "Complete your application for the AmpliFi Fi-Federal Credit Card. You will get the digital card instantly"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "72h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "⚠️ Reminder"
            Body: "Your credit card application is still pending. Complete it now in just 2 minutes on the Fi app"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"

  StuckUserNudges:
    TNC_CONSENT:
      - StuckDuration: "2m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "We just met you and this is crazy..."
            Body: "Finish your KYC to open a Federal Bank Savings Account in minutes!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    INITIATE_CKYC:
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Why do we need PAN card details? 💭"
            Body: "Because you are opening a savings account through Fi! Don’t worry, your details are safe with us. Fill them up now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "We're hoping things PAN out between us!"
            Body: "Keep your PAN and Aadhaar details on hand, and you can open a Federal savings account in minutes! Need help? Talk to our Fi Support Team."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "5m"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 57
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    DOB_AND_PAN:
      - StuckDuration: "10m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "No one's going to judge your PAN card photo. Promise!"
            Body: "Enter your PAN details to move ahead and open a Federal savings account. Stuck somewhere? Reach out to our super-friendly Fi Support team."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "6h"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 118 # ONBOARDING_DOB_AND_PAN_DROP_OFF
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Missed out adding your PAN?🪪"
            Body: "PAN details fast-track your verification. With Fi, securely create your account in minutes."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 57
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "3m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "30 Lakh Fi users have added PAN"
            Body: "Did you? Tap to complete your sign-up."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "4m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Add your PAN to continue with Fi"
            Body: "Don't miss out on the Fi journey! Securely enter your PAN details🏦"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "5m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Your PAN verification is pending"
            Body: "Don't worry, your data will not be shared without your knowledge"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "3m"
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 42
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    CONFIRM_CARD_MAILING_ADDRESS:
      - StuckDuration: "2m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Special delivery! Where do we send your Fi Card?"
            Body: "Add your shipping address and open a savings account in minutes."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    DEVICE_REGISTRATION:
      - StuckDuration: "2m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "You're (almost) ready to begin your journey with Fi!"
            Body: "Final step: All you need to do is – log in to the app and open your savings account."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    DEBIT_CARD_PIN_SETUP:
      - StuckDuration: "5m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Your debit card is waiting for you"
            Body: "Set a secret 4-digit PIN and start using your Visa Platinum Debit Card for online payments!🔑"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "7m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Your debit card is waiting for you"
            Body: "Set a 4-digit ATM PIN and use your Visa Platinum Debit card online right away"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    ADD_MONEY:
      - StuckDuration: "2m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Congrats! Your Federal savings account is ready."
            Body: "Add funds, receive money via UPI, or make online payments with your Fi card!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    EKYC:
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Don't lose momentum⚡"
            Body: "You're minutes away from creating your account via Fi. Securely enter your Aadhaar to continue."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 58
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    NAME_MISMATCH:
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Houston, we no longer have a problem"
            Body: "Update your Fi app so you can resume opening your savings account now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 62
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    DOB_MISMATCH:
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Houston, we no longer have a problem"
            Body: "Update your Fi app so you can resume opening your savings account now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 62
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    LIVENESS:
      - StuckDuration: "1m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Not home? In a crowd? That's okay📷"
            Body: "Get on a 15-sec video check to verify your identity. Anywhere, anyhow works!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m"
        NotificationType: "SMS"
        NotificationData:
          SMSData:
            SmsType: 60
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    VKYC:
      - StuckDuration: "1m" #1m
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 44
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "2m" #2m
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "📞 Complete your KYC verification call"
            Body: "Tap to complete the video KYC call to access your account💡Keep your original PAN card ready."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "3m" #3m
        NotificationType: "WHATSAPP"
        NotificationData:
          WhatsappData:
            WhatsappType: 45
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "4m" # 4m
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "🚨 90% done! 1 more step left..."
            Body: "Just finish a 3-minute video KYC call to verify yourself and start using Fi. Tap to start the call now!"
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "5m" # 5m
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Ready for your video call? 🤓 🤙"
            Body: "Complete your account creation by getting on a quick 3 min video KYC call with us."
            NotificationIconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
    EMPLOYMENT_VERIFICATION:
      - StuckDuration: "5m"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "Are you ready for your ₹250 reward?😍"
            Body: "Get up to ₹250 once you complete your sign-up on Fi. This will take just 5 minutes. Start now! ➡️"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "48h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "💳Ready for your cool debit card?"
            Body: "Just complete your sign-up and get offers from Swiggy, Amazon, Myntra, and more. It takes just 5 minutes. Start now! ➡️"
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"
      - StuckDuration: "72h"
        NotificationType: "PUSH_NOTIFICATION"
        NotificationData:
          PushNotificationData:
            Title: "💪Your personal information is safe."
            Body: "Finish your KYC and your savings account will be ready. Your money is insured up to ₹5L."
        NotificationTimeRange:
          NotificationStartTime: "00:00"
          NotificationEndTime: "23:59"

  StageManualPassNotif:
    PAN_NAME_CHECK:
      - IsEnabled: true
        NotificationType: "PUSH_NOTIFICATION"
        Title: "30 Lakh Fi users have added Aadhaar"
        Body: "Your PAN verification is complete. Log in to the app and open your Federal savings account now!"
        IconUrl: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
        Type: "SYSTEM_TRAY"

  AWS:
    Region: "ap-south-1"
    S3:
      BucketNames:
        BucketUsers: "epifi-dev-users"

  BlockOnboardingFromTime: "2021-06-10 23:20:00.000"
  BlockOnboardingTillTime: "2021-06-10 23:25:00.000"
  BlockOnboardingMsg: "Our partner bank has scheduled maintenance activity from 11:45PM-3AM. Please try after 3AM."

  KYCDedupeRetryCount: 3
  SyncOnboardingInterval: "30s"
  SyncOnboardingCutOff: "1h"

  AffluenceClassesEligibleForBonusTransitionScreen:
    - "AFFLUENCE_CLASS_CLASS_1": true
    - "AFFLUENCE_CLASS_CLASS_2": true
    - "AFFLUENCE_CLASS_CLASS_3": true
    - "AFFLUENCE_CLASS_CLASS_4": false
    - "AFFLUENCE_CLASS_CLASS_5": false

  BlockOnboardingDueToUnlinkedPANAndAadhaar: true
  NrBucketName: "epifi-staging-nrusers"

Flags:
  WealthAnalyserFeature:
    MinIOSVersion: 556
    MinAndroidVersion: 401
    FallbackToEnableFeature: true
    DisableFeature: false
  TrimDebugMessageFromStatus: false

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    DbUsernamePassword: "staging/rds/postgres14"
    PgdbCredentials: "staging/rds/postgres/feature-engineering"
    RudderWriteKey: "staging/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "staging/gcloud/profiling-service-account-key"

OnboardingUserUpdatePublisher:
  TopicName: "staging-user-update-topic"

OnboardingUserUpdateVKYCSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-onboarding-user-update-vkyc-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

ShippingAddressUpdatePublisher:
  QueueName: "staging-shipping-address-update-queue"

ShippingAddressUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-shipping-address-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

ShippingAddressUpdateCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-shipping-address-update-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

UserAccessRevokeUpdatePublisher:
  TopicName: "staging-user-access-revoke-update-topic"
ShippingAddressUpdateEventPublisher:
  TopicName: "staging-user-shipping-address-update-topic"
UserDevicePropertiesUpdatePublisher:
  TopicName: "staging-user-device-properties-update-topic"

DeleteUserPublisher:
  TopicName: "staging-delete-user-event-topic"

VKYC:
  Option:
    VKYC_OPTION_LSO:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 131
      MinIOSVersion: 251
      SkipOptionFlag: true
      IgnoreFullKYCUser: false
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_CKYC_O:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 131
      MinIOSVersion: 138
      SkipOptionFlag: true
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_ONBOARDING:
      IsVKYCApprovalBlocking: false
      VkycEnablePercentage: 100
      MinAndroidVersion: 163
      MinIOSVersion: 122
      SkipOptionFlag: true
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: true
    VKYC_OPTION_STUDENT:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 163
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_PARTIAL_KYC_DEDUPE:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 163
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_EKYC_NUMBER_MISMATCH:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 240
      MinIOSVersion: 335
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_LOW_QUALITY_USERS:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 240
      MinIOSVersion: 335
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_FI_LITE_CC_USERS:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 240
      MinIOSVersion: 335
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_FI_LITE_USERS:
      IsVKYCApprovalBlocking: false
      VkycEnablePercentage: 0
      MinAndroidVersion: 240
      MinIOSVersion: 335
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_CLOSED_ACCOUNT_REOPENING:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 240
      MinIOSVersion: 335
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_NON_RESIDENT_ONBOARDING:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: false
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_FEDERAL_LOANS:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: false
      InstructionPageSkipOptionFlag: false
  EnableDemandManagement: false

VKYCUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-vkyc-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

EKYCSuccessSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-kyc-ekyc-success-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

ConsentEventPublisher:
  TopicName: "staging-consent-topic"

VpaMigrationConsentPublisher:
  QueueName: "staging-upi-vpa-migration-consent-queue"

CreditReportPresencePublisher:
  QueueName: "staging-user-credit-report-presence-queue"

CreditReportPresenceSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-user-credit-report-presence-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

CreditReportVerificationPublisher:
  QueueName: "staging-user-credit-report-verification-queue"

CreditReportVerificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-user-credit-report-verification-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

Screening:
  CreditReportPresenceCheck:
    CreditReportPresenceCheckMaxDuration: 30s
  CreditReportVerification:
    CreditScoreThreshold: 600
    CreditReportVerificationMaxDuration: 30s

UserUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-user-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

BankCustomerUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-bank-customer-update-event-onboarding-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

CreditReportVerificationEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-onboarding-credit-report-verification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

LivManualReviewEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-onboarding-liveness-manual-review-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 2
      TimeUnit: "Second"

AfPurchasePublisher:
  QueueName: "staging-event-af-purchase-queue"

EventsAfPurchaseSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-event-af-purchase-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

EventsCompletedTnCPublisher:
  QueueName: "staging-event-completed-tnc-queue"

EventsCompletedTnCSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-event-completed-tnc-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

UserCacheConfig:
  IsCachingEnabled: true
  UserIdPrefix: "user_id_"
  CacheTTl: "2m"

UserGroupCacheConfig:
  IsCachingEnabled: true
  UserGroupEmailPrefix: "USER_GROUP_MAPPING"
  CacheTTl: "2m"

Events:
  AfPurchasePublishDelay: "2m"
  CompletedTnCPublishDelay: "1m"

CreditReportConfig:
  ExperianConsentConfig:
    ConsentExtension: "2160h"
    ConsentExpiry: "4320h"

CreditReportDerivedAttributesPublisher:
  QueueName: "staging-credit-report-derived-attributes-queue"

CreditReportDerivedAttributesSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-credit-report-derived-attributes-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

ProcessOnboardingEventUserContactSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-user-contact-onboarding-stage-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

ProcessAfuEventUserContactSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-user-contact-auth-factor-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

ProcessDeleteUserSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-user-contact-delete-user-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

ProcessAccessRevokeCooldownSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-user-access-revoke-cooldown-queue"
  RetryStrategy:
    RegularInterval:
      BaseInterval: 5
      MaxAttempts: 100
      TimeUnit: "Minute"

SyncOnboardingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-sync-onboarding-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 50
      TimeUnit: "Second"

VKYCCallCompletedEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-user-vkyc-call-completed-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 50
      TimeUnit: "Second"

InHouseVkycCallCompletedEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-user-inhouse-vkyc-call-completed-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

ProcessSavingsAccountUpdateEvent:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-onboarding-savings-account-state-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

ProcessCardCreationEvent:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-onboarding-card-creation-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 5
      TimeUnit: "Minute"

WhitelistNumberHashesForUAENR:
  - "741d5bacc019d561f21fbded65324155e5072d3f"   #************
  - "587a2637e71f3abd8c78196a196d2c6550ecc7aa"
  - "93b9c5a4657097280525b16e0e45ee0915db9d6e"

ProcessAccessRevokeCooldownPublisher:
  QueueName: "staging-user-access-revoke-cooldown-queue"
