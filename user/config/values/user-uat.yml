Application:
  Environment: "uat"
  Name: "user"
  IsSecureRedis: true

EpifiDb:
  DbType: "CRDB"
  AppName: "user"
  StatementTimeout: 5s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "uat/cockroach/ca.crt"
  SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

FeatureEngineeringDb:
  DbType: "PGDB"
  AppName: "user"
  StatementTimeout: 5s
  Name: "feature_engineering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "uat/rds/postgres/feature-engineering"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

AWS:
  Region: "ap-south-1"
  S3:
    UsersBucketName: "epifi-uat-dev-users"
    CreditReportsBucketName: "epifi-uat-credit-reports"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 12
  HystrixCommand:
    CommandName: "user_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 100ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

OnboardingStageEventPublisher:
  TopicName: "uat-onboarding-stage-update-topic"

Consent:
  Versions:
    FiTnc: 0
    FedTnc: 0
    FiPrivacyPolicy: 0
    FiWealthTnc: 0
    FiP2pInvestmentTnc: 0
    VpaMigration: 0
    SecureUsageTnC: 1
  Urls:
    FiTnc: "https://web.uat.pointz.in/T&C"
    FiTncNonResident: "https://fi.money/tnc/nr"
    FedTnc: "https://www.federalbank.co.in/epifi-tandc#CASA"
    FiPrivacyPolicy: "https://web.uat.pointz.in/privacy"
    FiWealthTnc: "https://web.uat.pointz.in/wealth/TnC"
    FiP2pInvestmentTnc: "https://web.uat.pointz.in/wealth/TnC"

Onboarding:
  NonResidentCrossValidationConfig:
    FmPollingRetryConfig:
      Interval: 300 # default to using milliseconds
      MaxAttempts: 5
    CrossValidationDataSources:
      FEATURE_NON_RESIDENT_SA:FLOW_PAN:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_GENDER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
      FEATURE_NON_RESIDENT_SA_QATAR:FLOW_PAN:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PAN
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_NUMBER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_DATE_OF_EXPIRY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
      FEATURE_NON_RESIDENT_SA:FLOW_FORM_60:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_GENDER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID
      FEATURE_NON_RESIDENT_SA_QATAR:FLOW_FORM_60:
        CROSS_VALIDATION_CHECK_USER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_DOB:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_NATIONALITY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_FATHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_MOTHER_NAME:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
        CROSS_VALIDATION_CHECK_FACE:
          - CROSS_VALIDATION_DATA_SOURCE_USER_INPUT
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_NUMBER:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
        CROSS_VALIDATION_CHECK_PASSPORT_DATE_OF_EXPIRY:
          - CROSS_VALIDATION_DATA_SOURCE_PASSPORT
          - CROSS_VALIDATION_DATA_SOURCE_QATAR_ID
  ConfirmCardMailingAddress:
    EnableConfirmCardMailingAddressV2: false
  NextActionDecisionCacheConfig:
    IsCacheEnabled: true
    CacheTTL: "2160h" # 3 months
  Flags:
    EnableSaDeclarationstage:
      MinAndroidVersion: 1
      MinIOSVersion: 1
      FallbackToEnableFeature: false
      DisableFeature: true
    EnablePanAadharCheckInPreCustomerCreationCheckStage: true
    SkipCountryIdVerification: false
    SkipPassportVerification: false
    EnableSyncOnboarding: true
    EnableSecureUsageGuidelinesConsent: true
    DisabledStages:
      FI_LITE_RISK_SCREENING: true
      PAN_NAME_CHECK: true
      PRE_CUSTOMER_CREATION_DEDUPE_CHECK: true
      AADHAR_MOBILE_VALIDATION: true
      ADD_MONEY: true
      SHIPPING_ADDRESS_UPDATE: true
      SMS_PARSER_DATA_VERIFICATION: true

  OnboardingVelocityConfig:
    QueryRangeDuration: 24h
    Threshold: 100
    BucketExpiry: 24h
    BucketPrecision: 3

  SyncOnboardingSqsPublisher:
    QueueName: "uat-sync-onboarding-queue"

  UNNameCheckMailingAddress:
    FromAddress: "<EMAIL>"
    ToAddress: "<EMAIL>"
    FromName: "unnamecheck-test"
    ToName: "unnamecheck-test"

  AWS:
    Region: "ap-south-1"
    S3:
      BucketNames:
        BucketUsers: "epifi-dev-users"

  TotalAmountViaOrderAddFunds: true
  MinAndroidVersionForManualBalanceRefreshOnbAddFunds: 99999
  MinIosVersionForManualBalanceRefreshOnbAddFunds: 99999
  ReferralOfferCodesDuringOnboarding:
    - CODE_1:
        IsEnabled: true
        BeforeAppliedTitle: "<font color='#313234'>FI200: </font><font color='#5d7d4c'>Get up to ₹200</font>"
        AfterAppliedTitle: "<font color='#313234'>\"FI200\" applied</font>"
        BeforeAppliedDesc: "When you add money to your account"
        AfterAppliedDesc: "Get up to ₹200 when you add money"
        BeforeAppliedIconUrl: "https://epifi-icons.pointz.in/referrals/onboarding-tag.png"
        AfterAppliedIconUrl: "https://epifi-icons.pointz.in/referrals/onboarding-success-check.png"
        Code: "FI200"
        UnderlyingFiniteCode: ""
  ReferralOfferCodesABReleaseConfig:
    FeatureConstraints:
      - FEATURE_UNSPECIFIED:
          ConstraintConfig:
            StickyPercentageConstraintConfig:
              RolloutPercentage: 100
          Buckets:
            - ONE:
                Start: 0
                End: 99

  KYCDedupeRetryCount: 3
  SyncOnboardingInterval: "5m"
  SyncOnboardingCutOff: "1h"
  NrBucketName: "epifi-uat-nrusers"

Flags:
  TrimDebugMessageFromStatus: false

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    DbUsernamePassword: "uat/rds/postgres"
    PgdbCredentials: "uat/rds/postgres/feature-engineering"
    RudderWriteKey: "uat/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "uat/gcloud/profiling-service-account-key"

OnboardingUserUpdatePublisher:
  TopicName: "uat-user-update-topic"

OnboardingUserUpdateVKYCSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-onboarding-user-update-vkyc-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

ShippingAddressUpdatePublisher:
  QueueName: "uat-shipping-address-update-queue"

ShippingAddressUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-shipping-address-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 4 #The base interval is scaled to make sure, max retry time is around 20 mins
      MaxAttempts: 17
      TimeUnit: "Second"

ShippingAddressUpdateCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-shipping-address-update-callback-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 7 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 83
          TimeUnit: "Hour"
      MaxAttempts: 93
      CutOff: 10

VKYCUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-vkyc-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

EKYCSuccessSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-kyc-ekyc-success-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

UserAccessRevokeUpdatePublisher:
  TopicName: "uat-user-access-revoke-update-topic"
ShippingAddressUpdateEventPublisher:
  TopicName: "uat-user-shipping-address-update-topic"

VKYC:
  Option:
    VKYC_OPTION_LSO:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 131
      MinIOSVersion: 140
      SkipOptionFlag: false
      IgnoreFullKYCUser: false
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_CKYC_O:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 132
      MinIOSVersion: 265
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_ONBOARDING:
      IsVKYCApprovalBlocking: false
      VkycEnablePercentage: 0
      MinAndroidVersion: 131
      MinIOSVersion: 251
      SkipOptionFlag: true
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: true
    VKYC_OPTION_STUDENT:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 150
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_PARTIAL_KYC_DEDUPE:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 150
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_EKYC_NUMBER_MISMATCH:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 238
      MinIOSVersion: 208
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_LOW_QUALITY_USERS:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 238
      MinIOSVersion: 208
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_FI_LITE_CC_USERS:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 238
      MinIOSVersion: 208
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_FI_LITE_USERS:
      IsVKYCApprovalBlocking: false
      VkycEnablePercentage: 0
      MinAndroidVersion: 238
      MinIOSVersion: 208
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_CLOSED_ACCOUNT_REOPENING:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 238
      MinIOSVersion: 208
      SkipOptionFlag: false
      IgnoreFullKYCUser: true
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_NON_RESIDENT_ONBOARDING:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: false
      InstructionPageSkipOptionFlag: false
    VKYC_OPTION_FEDERAL_LOANS:
      IsVKYCApprovalBlocking: true
      VkycEnablePercentage: 100
      MinAndroidVersion: 0
      MinIOSVersion: 0
      SkipOptionFlag: false
      IgnoreFullKYCUser: false
      InstructionPageSkipOptionFlag: false

ConsentEventPublisher:
  TopicName: "uat-consent-topic"

VpaMigrationConsentPublisher:
  QueueName: "uat-upi-vpa-migration-consent-queue"

CreditReportPresencePublisher:
  QueueName: "uat-user-credit-report-presence-queue"

CreditReportPresenceSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-user-credit-report-presence-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

CreditReportVerificationPublisher:
  QueueName: "uat-user-credit-report-verification-queue"

CreditReportVerificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-user-credit-report-verification-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

Screening:
  CreditReportPresenceCheck:
    CreditReportPresenceCheckMaxDuration: 30s
  CreditReportVerification:
    CreditScoreThreshold: 600
    CreditReportVerificationMaxDuration: 30s

UserUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-user-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

BankCustomerUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-bank-customer-update-event-onboarding-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

CreditReportVerificationEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-onboarding-credit-report-verification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

LivManualReviewEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-onboarding-liveness-manual-review-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 2
      TimeUnit: "Second"

AfPurchasePublisher:
  QueueName: "uat-event-af-purchase-queue"

EventsAfPurchaseSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-event-af-purchase-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 12
      TimeUnit: "Second"

EventsCompletedTnCPublisher:
  QueueName: "uat-event-completed-tnc-queue"

EventsCompletedTnCSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-event-completed-tnc-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

UserCacheConfig:
  IsCachingEnabled: false
  UserIdPrefix: "user_id_"
  CacheTTl: "2m"

UserGroupCacheConfig:
  IsCachingEnabled: true
  UserGroupEmailPrefix: "USER_GROUP_MAPPING"
  CacheTTl: "2m"

Events:
  AfPurchasePublishDelay: "2m"
  CompletedTnCPublishDelay: "1m"

CreditReportConfig:
  ExperianConsentConfig:
    ConsentExtension: "2160h"
    ConsentExpiry: "4320h"

CreditReportDerivedAttributesPublisher:
  QueueName: "uat-credit-report-derived-attributes-queue"

CreditReportDerivedAttributesSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-credit-report-derived-attributes-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

ProcessOnboardingEventUserContactSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-user-contact-onboarding-stage-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

ProcessAfuEventUserContactSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-user-contact-auth-factor-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

ProcessDeleteUserSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-user-contact-delete-user-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

UserDevicePropertiesUpdatePublisher:
  TopicName: "uat-user-device-properties-update-topic"

DeleteUserPublisher:
  TopicName: "uat-delete-user-event-topic"

ProcessAccessRevokeCooldownPublisher:
  QueueName: "uat-user-access-revoke-cooldown-queue"

ProcessAccessRevokeCooldownSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-user-access-revoke-cooldown-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 50
      TimeUnit: "Second"

SyncOnboardingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-sync-onboarding-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 50
      TimeUnit: "Second"

VKYCCallCompletedEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-user-vkyc-call-completed-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

InHouseVkycCallCompletedEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 4
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-user-inhouse-vkyc-call-completed-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

ProcessSavingsAccountUpdateEvent:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-onboarding-savings-account-state-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

ProcessCardCreationEvent:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-onboarding-card-creation-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 5
      TimeUnit: "Minute"
