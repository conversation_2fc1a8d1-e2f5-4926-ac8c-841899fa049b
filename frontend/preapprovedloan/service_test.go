// nolint: gocritic
package preapprovedloan

import (
	"context"
	"testing"

	errorsPb "github.com/epifi/gamma/api/frontend/errors"
)

func Test_validateUserName(t *testing.T) {
	type args struct {
		ctx  context.Context
		name string
	}
	tests := []struct {
		name          string
		args          args
		wantErrorView *errorsPb.ErrorView
		wantErrorType errorsPb.ErrorViewType
		wantErrorMsg  string
		wantNoError   bool
	}{
		{
			name: "#1 Valid name with first and last name",
			args: args{
				ctx:  context.Background(),
				name: "<PERSON>",
			},
			wantNoError: true,
		},
		{
			name: "#2 Valid name with multiple names",
			args: args{
				ctx:  context.Background(),
				name: "<PERSON>",
			},
			wantNoError: true,
		},
		{
			name: "#3 Valid name with single character names",
			args: args{
				ctx:  context.Background(),
				name: "A <PERSON>",
			},
			wantNoError: true,
		},
		{
			name: "#4 Valid name with leading and trailing spaces",
			args: args{
				ctx:  context.Background(),
				name: "  <PERSON>  ",
			},
			wantNoError: true,
		},
		{
			name: "#5 Empty name",
			args: args{
				ctx:  context.Background(),
				name: "",
			},
			wantErrorType: errorsPb.ErrorViewType_BOTTOM_SHEET,
			wantErrorMsg:  "Provided name seems incomplete",
		},
		{
			name: "#6 Only spaces",
			args: args{
				ctx:  context.Background(),
				name: "   ",
			},
			wantErrorType: errorsPb.ErrorViewType_BOTTOM_SHEET,
			wantErrorMsg:  "Provided name seems incomplete",
		},
		{
			name: "#7 Single name without space",
			args: args{
				ctx:  context.Background(),
				name: "John",
			},
			wantErrorType: errorsPb.ErrorViewType_BOTTOM_SHEET,
			wantErrorMsg:  "Provided name seems incomplete",
		},
		{
			name: "#8 Single name with leading/trailing spaces but no internal space",
			args: args{
				ctx:  context.Background(),
				name: "  John  ",
			},
			wantErrorType: errorsPb.ErrorViewType_BOTTOM_SHEET,
			wantErrorMsg:  "Provided name seems incomplete",
		},
		{
			name: "#9 Name with numbers at the end",
			args: args{
				ctx:  context.Background(),
				name: "John Doe 123",
			},
			wantErrorType: errorsPb.ErrorViewType_TOAST,
			wantErrorMsg:  "Please enter your Full Name",
		},
		{
			name: "#10 Name with apostrophe",
			args: args{
				ctx:  context.Background(),
				name: "John O'Connor",
			},
			wantErrorType: errorsPb.ErrorViewType_TOAST,
			wantErrorMsg:  "Please enter your Full Name",
		},
		{
			name: "#11 Name with comma",
			args: args{
				ctx:  context.Background(),
				name: "John, Doe",
			},
			wantErrorType: errorsPb.ErrorViewType_TOAST,
			wantErrorMsg:  "Please enter your Full Name",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := validateUserName(tt.args.ctx, tt.args.name)

			if tt.wantNoError {
				if got != nil {
					t.Errorf("validateUserName() = %v, want nil", got)
				}
				return
			}

			if got == nil {
				t.Errorf("validateUserName() = nil, want error")
				return
			}

			if got.Type != tt.wantErrorType {
				t.Errorf("validateUserName() error type = %v, want %v", got.Type, tt.wantErrorType)
			}

			var actualMessage string
			switch tt.wantErrorType {
			case errorsPb.ErrorViewType_BOTTOM_SHEET:
				if got.GetBottomSheetErrorView() == nil {
					t.Errorf("validateUserName() bottom sheet error view is nil")
					return
				}
				actualMessage = got.GetBottomSheetErrorView().GetTitleText().GetPlainString()
			case errorsPb.ErrorViewType_TOAST:
				if got.GetToastErrorView() == nil {
					t.Errorf("validateUserName() toast error view is nil")
					return
				}
				actualMessage = got.GetToastErrorView().GetTitle()
			}

			if actualMessage != tt.wantErrorMsg {
				t.Errorf("validateUserName() error message = %v, want %v", actualMessage, tt.wantErrorMsg)
			}
		})
	}
}
