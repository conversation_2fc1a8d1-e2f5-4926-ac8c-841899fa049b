package amb

import (
	"context"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	segmentPb "github.com/epifi/gamma/api/segment"
	tieringPb "github.com/epifi/gamma/api/tiering"
	enums "github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	types "github.com/epifi/gamma/api/typesv2"
	genConf "github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/feature/release"
)

func IsAmbEnabledForActor(ctx context.Context, actorId string, tieringConf *genConf.Tiering, tieringClient tieringPb.TieringClient, segmentClient segmentPb.SegmentationServiceClient, evaluator release.IEvaluator) bool {
	// FIRST: Check AMB Entrypoint Banner feature flag
	isAMBEntrypointBannerEnabled, err := evaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_AMB_ENTRYPOINT_BANNER).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error evaluating AMB Entrypoint Banner feature flag", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return false
	}

	if !isAMBEntrypointBannerEnabled {
		return false
	}

	// SECOND: Check segment-based logic (original logic preserved)
	excludedSegmentsForAmb := make([]string, 0)

	// Time-based segment exclusion logic
	segmentExclusionStartTime := &timestamppb.Timestamp{Seconds: tieringConf.ExcludeSegmentsFromAMBScreenEntrypoint().StartTime()}
	segmentExclusionEndTime := &timestamppb.Timestamp{Seconds: tieringConf.ExcludeSegmentsFromAMBScreenEntrypoint().EndTime()}
	tNow := timestamppb.Now()
	if segmentExclusionStartTime.IsValid() && segmentExclusionEndTime.IsValid() && !segmentExclusionStartTime.AsTime().IsZero() &&
		!segmentExclusionEndTime.AsTime().IsZero() && tNow.AsTime().After(segmentExclusionStartTime.AsTime()) &&
		tNow.AsTime().Before(segmentExclusionEndTime.AsTime()) {
		excludedSegmentsForAmb = append(excludedSegmentsForAmb, tieringConf.ExcludeSegmentsFromAMBScreenEntrypoint().ExcludedSegmentIds().ToStringArray()...)
	}

	// Check segments if any are configured
	if len(excludedSegmentsForAmb) > 0 {
		segRes, segErr := segmentClient.IsMember(ctx, &segmentPb.IsMemberRequest{
			ActorId:    actorId,
			SegmentIds: excludedSegmentsForAmb,
			LatestBy:   timestamppb.Now(),
		})
		if te := epifigrpc.RPCError(segRes, segErr); te != nil {
			logger.Error(ctx, "error checking if actor is in segment", zap.String(logger.ACTOR_ID_V2, actorId))
			return false
		}

		// Check for segment exclusion - if user is in any excluded segment, deny AMB access
		for _, segId := range excludedSegmentsForAmb {
			if segRes.GetSegmentMembershipMap()[segId].GetIsActorMember() {
				return false
			}
		}
	}

	// THIRD: Call GetTieringPitchV2 to get tier details
	tieringResponse, tieringErr := tieringClient.GetTieringPitchV2(ctx, &tieringPb.GetTieringPitchV2Request{
		ActorId: actorId,
	})

	// MANDATORY: Use epifigrpc.RPCError pattern for gRPC calls
	if te := epifigrpc.RPCError(tieringResponse, tieringErr); te != nil {
		logger.Error(ctx, "error calling GetTieringPitchV2", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId))
		return false
	}

	// FOURTH: Check if current tier is excluded using configuration
	currentTier := tieringResponse.GetCurrentTier()
	excludedTiers := tieringConf.AMBConfig().ExcludedTiers().ToStringArray()
	if lo.Contains(excludedTiers, currentTier.String()) {
		return false
	}

	// CriteriaOptionType_BALANCE_V2_AND_KYC is applicable for only reward tiers (non-regular tiers)
	if currentTier == tieringExtPb.Tier_TIER_FI_REGULAR {
		return true
	}

	// FIFTH: Filter based on current criteria option type (BALANCE_V2_AND_KYC)
	// Use CurrentCriteriaOptionType to handle users who entered with one criteria but stay with another
	return tieringResponse.GetCurrentCriteriaOptionType() == enums.CriteriaOptionType_BALANCE_V2_AND_KYC
}
