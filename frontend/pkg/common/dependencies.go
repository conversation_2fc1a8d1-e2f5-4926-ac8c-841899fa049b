package common

import (
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	savingsPb "github.com/epifi/gamma/api/savings"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/feature/release"
	pkgUser "github.com/epifi/gamma/pkg/user"
	questSdk "github.com/epifi/gamma/quest/sdk"
)

// ExternalDependencies holds common external client dependencies.
// Add new dependencies here as needed.
type ExternalDependencies struct {
	Evaluator            release.IEvaluator
	OnboardingClient     onboarding.OnboardingClient
	QuestSdkClient       *questSdk.Client
	UserAttributeFetcher pkgUser.UserAttributesFetcher
	NetWorthClient       networthPb.NetWorthClient
	BeTieringClient      beTieringPb.TieringClient
	SavingsClient        savingsPb.SavingsClient
	AccountBalanceClient accountBalancePb.BalanceClient
	Config               *genconf.Config
}

// GetEvaluator safely returns the Evaluator.
func (d *ExternalDependencies) GetEvaluator() release.IEvaluator {
	if d == nil {
		return nil
	}
	return d.Evaluator
}

// GetOnboardingClient safely returns the OnboardingClient.
func (d *ExternalDependencies) GetOnboardingClient() onboarding.OnboardingClient {
	if d == nil {
		return nil
	}
	return d.OnboardingClient
}

// GetQuestSdkClient safely returns the QuestSdkClient.
func (d *ExternalDependencies) GetQuestSdkClient() *questSdk.Client {
	if d == nil {
		return nil
	}
	return d.QuestSdkClient
}

// GetUserAttributeFetcher safely returns the UserAttributeFetcher.
func (d *ExternalDependencies) GetUserAttributeFetcher() pkgUser.UserAttributesFetcher {
	if d == nil {
		return nil
	}
	return d.UserAttributeFetcher
}

func (d *ExternalDependencies) GetNetWorthClient() networthPb.NetWorthClient {
	if d == nil {
		return nil
	}
	return d.NetWorthClient
}

// GetBeTieringClient safely returns the BeTieringClient
func (d *ExternalDependencies) GetBeTieringClient() beTieringPb.TieringClient {
	if d == nil {
		return nil
	}
	return d.BeTieringClient
}

// GetSavingsClient safely returns the SavingsClient
func (d *ExternalDependencies) GetSavingsClient() savingsPb.SavingsClient {
	if d == nil {
		return nil
	}
	return d.SavingsClient
}

// GetAccountBalanceClient safely returns the AccountBalanceClient
func (d *ExternalDependencies) GetAccountBalanceClient() accountBalancePb.BalanceClient {
	if d == nil {
		return nil
	}
	return d.AccountBalanceClient
}

// GetConfig safely return config
func (d *ExternalDependencies) GetConfig() *genconf.Config {
	if d == nil {
		return nil
	}
	return d.Config
}
