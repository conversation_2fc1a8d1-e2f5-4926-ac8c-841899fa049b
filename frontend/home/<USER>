package home

import (
	"context"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/header"
	pb "github.com/epifi/gamma/api/frontend/home"
	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/external"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/feature/release"
)

type Occasion string

const (
	Unspecified Occasion = "Unspecified"
	Holi        Occasion = "Holi"
	Diwali      Occasion = "Diwali"
	Christmas   Occasion = "Christmas"
)

func (s *Service) GetAlternateAppIcon(ctx context.Context, req *pb.GetAlternateAppIconRequest) (*pb.GetAlternateAppIconResponse, error) {
	var (
		actorId = req.GetReq().GetAuth().GetActorId()
	)

	isEligible, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_ALTERNATE_APP_ICON).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed to evaluate release config for alternate app icon", zap.Error(err))
		return &pb.GetAlternateAppIconResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusUnavailable()}}, nil
	}
	if !isEligible {
		return &pb.GetAlternateAppIconResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusUnavailable()}}, nil
	}

	accountTierRes, respErr := s.beTieringClient.GetTierAtTime(ctx, &tieringPb.GetTierAtTimeRequest{
		ActorId:       actorId,
		TierTimestamp: timestampPb.New(time.Now()),
	})
	if rpcErr := epifigrpc.RPCError(accountTierRes, respErr); rpcErr != nil {
		logger.Error(ctx, "error fetching account tier of the actor", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.Any(logger.RPC_STATUS, accountTierRes.GetStatus()))
		return &pb.GetAlternateAppIconResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}

	currentTier := accountTierRes.GetTierInfo().GetTier()
	currentOccasion := s.getCurrentOccasion()

	return &pb.GetAlternateAppIconResponse{
		RespHeader:                 &header.ResponseHeader{Status: rpc.StatusOk()},
		AlternateAppIconIdentifier: getAlternateAppIconIdentifier(currentTier, currentOccasion),
	}, nil
}

func getAlternateAppIconIdentifier(currentTier external.Tier, occasion Occasion) pb.AlternateAppIconIdentifier {
	// If no special occasion, return tier-based icons
	if occasion == Unspecified {
		switch currentTier {
		case external.Tier_TIER_FI_PLUS:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PLUS
		case external.Tier_TIER_FI_INFINITE:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_INFINITE
		case external.Tier_TIER_FI_SALARY, external.Tier_TIER_FI_SALARY_BASIC:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_SALARY
		case external.Tier_TIER_FI_AA_SALARY_BAND_1, external.Tier_TIER_FI_AA_SALARY_BAND_2, external.Tier_TIER_FI_AA_SALARY_BAND_3:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PRIME
		default:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_UNSPECIFIED
		}
	}

	// Return occasion-specific icons based on tier and occasion
	switch currentTier {
	case external.Tier_TIER_FI_PLUS:
		switch occasion {
		case Holi:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PLUS_OCCASION_HOLI
		case Diwali:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PLUS_OCCASION_DIWALI
		case Christmas:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PLUS_OCCASION_CHRISTMAS
		}
	case external.Tier_TIER_FI_INFINITE:
		switch occasion {
		case Holi:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_INFINITE_OCCASION_HOLI
		case Diwali:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_INFINITE_OCCASION_DIWALI
		case Christmas:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_INFINITE_OCCASION_CHRISTMAS
		}
	case external.Tier_TIER_FI_SALARY:
		switch occasion {
		case Holi:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_SALARY_OCCASION_HOLI
		case Diwali:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_SALARY_OCCASION_DIWALI
		case Christmas:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_SALARY_OCCASION_CHRISTMAS
		}
	case external.Tier_TIER_FI_AA_SALARY_BAND_1, external.Tier_TIER_FI_AA_SALARY_BAND_2, external.Tier_TIER_FI_AA_SALARY_BAND_3:
		switch occasion {
		case Holi:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PRIME_OCCASION_HOLI
		case Diwali:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PRIME_OCCASION_DIWALI
		case Christmas:
			return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_SAVINGS_ACCOUNT_TIER_PRIME_OCCASION_CHRISTMAS
		}
	}

	// If no specific combination matches or for unsupported tiers, return the occasion-only icon if available
	switch occasion {
	case Holi:
		return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_OCCASION_HOLI
	case Diwali:
		return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_OCCASION_DIWALI
	case Christmas:
		return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_OCCASION_CHRISTMAS
	}

	return pb.AlternateAppIconIdentifier_ALTERNATE_APP_ICON_IDENTIFIER_UNSPECIFIED
}

func (s *Service) getCurrentOccasion() Occasion {
	t := time.Now()

	// Check if the current date falls within any of the configured date ranges for occasions
	for occasion, ranges := range s.conf.AlternateAppIconConfig.OccasionRanges {
		for _, dateRange := range ranges {
			if t.After(dateRange.Start) && t.Before(dateRange.End) {
				return Occasion(occasion)
			}
		}
	}
	return Unspecified
}
