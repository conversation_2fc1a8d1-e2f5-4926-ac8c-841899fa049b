package tiering

import tieringExtPb "github.com/epifi/gamma/api/tiering/external"

// Properties and constants related to tiering add funds
// Beware of cyclic dependencies

const (
	// Tiering ddd funds card constants
	TieringTopCardTitle                                 = "Upgrade to %s"
	TieringTopCardRetentionTitle                        = "Stay in %s"
	TieringTopCardTitleFontColour                       = "#FFFFFF" // Gray/snow
	FiBasicBadgeImageUrlForAddFundsTopCard              = "https://epifi-icons.pointz.in/tiering/basic_bordered_3d.png"
	FiPlusBadgeImageUrlForAddFundsTopCard               = "https://epifi-icons.pointz.in/tiering/plus_badge_bordered_3d.png"
	FiInfiniteBadgeImageUrlForAddFundsTopCard           = "https://epifi-icons.pointz.in/tiering/infinite_bordered_3d_v2.png"
	FiPrimeBadgeImageUrlForAddFundsTopCard              = "https://epifi-icons.pointz.in/tiering/prime_bordered_3d.png"
	TieringTopCardBgColour                              = "#383838" // Charcoal black
	TopCardBottomItcPlusTitle                           = "Upgrade to Plus: Earn 1% back on spends"
	TopCardBottomItcInfiniteTitle                       = "Upgrade to Infinite: Earn 2% back on spends, 0 forex fees & priority customer support"
	TopCardBottomItcPrimeTitle                          = "Upgrade to Prime: Earn 3% back on spends, 0 forex fees & priority customer support"
	TopCardBottomItcTitleFontColor                      = "#CED2D6"
	TopCardBottomItcLeftImgpading                 int32 = 16
	TopCardBottomItcPlusLeftImgUrl                      = "https://epifi-icons.pointz.in/tiering/add_funds/2x_rewards_on_spends.png"
	TopCardBottomItcInfiniteLeftImgUrl                  = "https://epifi-icons.pointz.in/tiering/add_funds/2_percent_cashback.png"
	TopCardBottomItcPrimeLeftImgUrl                     = "https://epifi-icons.pointz.in/tiering/add_funds/3_percent_cashback.png"
	TopCardBottomItcLeftImgHeight                 int32 = 48
	TopCardBottomItcLeftImgWidth                  int32 = 48
	TopCardBottomItcContainerBgColor                    = "#555555"
	TopCardBottomItcContainerRadius               int32 = 19
	TopCardBottomItcContainerLeftPadding          int32 = 12
	TopCardBottomItcContainerRightPadding         int32 = 12
	TopCardBottomItcContainerTopPadding           int32 = 20
	TopCardBottomItcContainerBottomPadding        int32 = 20
	TopCardImageUrl                                     = "https://epifi-icons.pointz.in/tiering/add_funds/add_funds.png"
	TopCardImageHeight                            int32 = 198
	TopCardImageWidth                             int32 = 198
	TopCardImageTxtImgUrl                               = "https://epifi-icons.pointz.in/tiering/add_funds/image_with_text.png"
	TopCardImageTxtPadding                        int32 = 20
	TopCardImageText                                    = "Make payments, save for your goals & more on Fi"
	TopCardImageTextHeight                        int32 = 120
	TopCardImageTextWidth                         int32 = 120
	TopCardImageTextFontColor                           = "#F6F9FD"
	TieringTopCardCurrentNodeTitle                      = "YOU"
	TieringTopCardProgressBarFilledColourBasic          = "#7FBECE" // Dark Ocean
	TieringTopCardProgressBarFilledColourPlus           = "#00B899" // Forest
	TieringTopCardProgressBarFilledColourInfinite       = "#7FBECE" // Purple
	TieringTopCardProgressBarFilledColourPrime          = "#7FBECE"
	TieringTopCardProgressBarUnfilledColour             = "#555555" // Gray/iron
	TieringProgressBarCurrentNodeImageUrl               = ""
	TieringSuggestionText                               = "Add %s to upgrade to %s"
	TieringSuggestionRetentionText                      = "Add %s to stay in %s"
	FiBasicDisplayText                                  = "Standard"
	FiRegularDisplayText                                = "Regular"
	FiPlusDisplayText                                   = "Plus"
	FiInfiniteDisplayText                               = "Infinite"
	FiPrimeDisplayText                                  = "Prime"
	PrimaryCtaText                                      = "Add money & Upgrade"
	FallbackCtaText                                     = "Add money"
	AddFundsTieringSuggestionComponentBgColour          = "#F7F9FA" // Gray/ivory
	TieringAddFundsWarningLogoUrl                       = "https://epifi-icons.pointz.in/tiering/add_funds/warning_triangle.png"
	TieringTopCardStartNodeFontColour                   = "#FFFFFF" // Gray/snow
	TieringTopCardCurrentNodeFontColour                 = "#FFFFFF" // Gray/snow
	TieringTopCardEndNodeFontColour                     = "#FFFFFF" // Gray/snow
	FiTextDisplayColour                                 = "#00B899" // Green
)

func GetAddFundsBadgeUrlForTopCard(tier tieringExtPb.Tier) string {
	switch tier {
	case tieringExtPb.Tier_TIER_FI_BASIC, tieringExtPb.Tier_TIER_FI_REGULAR:
		return FiBasicBadgeImageUrlForAddFundsTopCard
	case tieringExtPb.Tier_TIER_FI_PLUS:
		return FiPlusBadgeImageUrlForAddFundsTopCard
	case tieringExtPb.Tier_TIER_FI_INFINITE:
		return FiInfiniteBadgeImageUrlForAddFundsTopCard
	case tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return FiPrimeBadgeImageUrlForAddFundsTopCard
	default:
		return ""
	}
}

func GetDisplayStringForTier(tier tieringExtPb.Tier) string {
	switch tier {
	case tieringExtPb.Tier_TIER_FI_REGULAR:
		return FiRegularDisplayText
	case tieringExtPb.Tier_TIER_FI_BASIC:
		return FiBasicDisplayText
	case tieringExtPb.Tier_TIER_FI_PLUS:
		return FiPlusDisplayText
	case tieringExtPb.Tier_TIER_FI_INFINITE:
		return FiInfiniteDisplayText
	case tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return FiPrimeDisplayText
	default:
		return ""
	}
}

func GetTextColourForTier(tier tieringExtPb.Tier) string {
	switch tier {
	case tieringExtPb.Tier_TIER_FI_BASIC, tieringExtPb.Tier_TIER_FI_REGULAR:
		return FiTextDisplayColour
	case tieringExtPb.Tier_TIER_FI_PLUS:
		return FiTextDisplayColour
	case tieringExtPb.Tier_TIER_FI_INFINITE:
		return FiTextDisplayColour
	default:
		return ""
	}
}

func GetTopCardProgressBarFilledColourForTier(tier tieringExtPb.Tier) string {
	switch tier {
	case tieringExtPb.Tier_TIER_FI_BASIC, tieringExtPb.Tier_TIER_FI_REGULAR:
		return TieringTopCardProgressBarFilledColourBasic
	case tieringExtPb.Tier_TIER_FI_PLUS:
		return TieringTopCardProgressBarFilledColourPlus
	case tieringExtPb.Tier_TIER_FI_INFINITE:
		return TieringTopCardProgressBarFilledColourInfinite
	case tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return TieringTopCardProgressBarFilledColourPrime
	default:
		return ""
	}
}
