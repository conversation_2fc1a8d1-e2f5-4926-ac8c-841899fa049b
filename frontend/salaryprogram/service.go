package salaryprogram

import (
	"context"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/thoas/go-funk"
	"go.uber.org/multierr"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	pkgMoney "github.com/epifi/be-common/pkg/money"
	pkgPag "github.com/epifi/be-common/pkg/pagination"

	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/actor"
	actorPb "github.com/epifi/gamma/api/actor"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	beEmploymentPb "github.com/epifi/gamma/api/employment"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	feDeeplink "github.com/epifi/gamma/api/frontend/deeplink"
	errorsPb "github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	palEnumFePb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	fePb "github.com/epifi/gamma/api/frontend/salaryprogram"
	"github.com/epifi/gamma/api/frontend/search/widget"
	beInAppReferralPb "github.com/epifi/gamma/api/inappreferral"
	vkycBe "github.com/epifi/gamma/api/kyc/vkyc"
	orderServicePb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/actoractivity"
	"github.com/epifi/gamma/api/order/payment"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	recurringpaymentPb "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	beRewardsPb "github.com/epifi/gamma/api/rewards"
	beRewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	beSalaryPb "github.com/epifi/gamma/api/salaryprogram"
	healthinsurancePb "github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	beSalaryReferralsPb "github.com/epifi/gamma/api/salaryprogram/referrals"
	"github.com/epifi/gamma/api/savings"
	savingsPb "github.com/epifi/gamma/api/savings"
	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/tiering"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/account"
	salaryScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryprogram"
	"github.com/epifi/gamma/api/typesv2/ui"
	usersPb "github.com/epifi/gamma/api/user"
	usergroupPb "github.com/epifi/gamma/api/user/group"
	vgEmploymentPb "github.com/epifi/gamma/api/vendorgateway/employment"
	employerNameMatchVgPb "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamematch"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	aaSalary "github.com/epifi/gamma/frontend/salaryprogram/aa_salary"
	salaryBenefits "github.com/epifi/gamma/frontend/salaryprogram/benefits"
	feTieringDataCollector "github.com/epifi/gamma/frontend/tiering/data_collector"
	wireTypes "github.com/epifi/gamma/frontend/wire/types"
	pkgDeeplink "github.com/epifi/gamma/pkg/deeplink"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	gammanames "github.com/epifi/gamma/pkg/names"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
)

type Service struct {
	fePb.UnimplementedSalaryProgramServer
	salaryProgramClient         beSalaryPb.SalaryProgramClient
	salaryReferralsClient       beSalaryReferralsPb.ReferralsClient
	employmentClient            beEmploymentPb.EmploymentClient
	healthInsuranceClient       healthinsurancePb.HealthInsuranceClient
	rewardsGeneratorClient      beRewardsPb.RewardsGeneratorClient
	rewardOffersClient          beRewardOffersPb.RewardOffersClient
	usersClient                 usersPb.UsersClient
	conf                        *config.Config
	epifiIconsBucketS3Client    s3.S3Client
	salaryProgramBucketS3Client wireTypes.SalaryProgramS3Client
	dyconf                      *genconf.Config
	actorActivityClient         actoractivity.ActorActivityClient
	inAppReferralClient         beInAppReferralPb.InAppReferralClient
	savingsClient               savings.SavingsClient
	actorClient                 actor.ActorClient
	releaseEvaluator            release.IEvaluator
	bankCustClient              bankCustPb.BankCustomerServiceClient
	salaryBenefitsSvc           salaryBenefits.IService
	vgEmploymentClient          vgEmploymentPb.EmploymentClient
	vgEmployerNameMatchClient   employerNameMatchVgPb.EmployerNameMatchClient
	preApprovedLoanClient       palPb.PreApprovedLoanClient
	recurringPaymentClient      recurringpaymentPb.RecurringPaymentServiceClient
	enachClient                 enachPb.EnachServiceClient
	orderServiceClient          orderServicePb.OrderServiceClient
	txnCatClient                categorizerPb.TxnCategorizerClient
	segmentClient               segmentPb.SegmentationServiceClient
	userGrpClient               usergroupPb.GroupClient
	aaSalaryNextActionManager   aaSalary.INextActionManager
	aaSalaryScreenBuilder       aaSalary.IScreenBuilder
	tieringClient               tiering.TieringClient
	tieringDataCollector        feTieringDataCollector.DataCollector
}

var _ fePb.SalaryProgramServer = &Service{}

// BenefitsCardsEntryPoint : type created to identify the entry points for rendering BenefitsCards
type BenefitsCardsEntryPoint int

const (
	SalaryIntroPage BenefitsCardsEntryPoint = iota
	SalaryLandingPage
	SalaryLiteIntroPage
)

var (
	beAmountBadgeToFeAmountBadgeMap = map[actoractivity.GetActivitiesResponse_Activity_AmountBadge]widget.AmountBadge{
		actoractivity.GetActivitiesResponse_Activity_CREDIT:  widget.AmountBadge_CREDIT,
		actoractivity.GetActivitiesResponse_Activity_DEBIT:   widget.AmountBadge_DEBIT,
		actoractivity.GetActivitiesResponse_Activity_SAVINGS: widget.AmountBadge_SAVINGS,
	}
	ActorActivityTimeStampLayout = "Mon Jan 02"
	gstinRegex                   = regexp.MustCompile("(^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[A-Z0-9]{1}[A-Z0-9]{2}$)")
)

func NewService(
	salaryProgramClient beSalaryPb.SalaryProgramClient,
	healthInsuranceClient healthinsurancePb.HealthInsuranceClient,
	salaryReferralsClient beSalaryReferralsPb.ReferralsClient,
	employmentClient beEmploymentPb.EmploymentClient,
	rewardsGeneratorClient beRewardsPb.RewardsGeneratorClient,
	rewardOffersClient beRewardOffersPb.RewardOffersClient,
	actorClient actorPb.ActorClient,
	savingsClient savingsPb.SavingsClient,
	usersClient usersPb.UsersClient,
	conf *config.Config,
	epifiIconsBucketS3Client s3.S3Client,
	salaryProgramBucketS3Client wireTypes.SalaryProgramS3Client,
	dyconf *genconf.Config,
	actorActivityClient actoractivity.ActorActivityClient,
	inAppReferralClient beInAppReferralPb.InAppReferralClient,
	releaseEvaluator release.IEvaluator,
	bankCustClient bankCustPb.BankCustomerServiceClient,
	salaryBenefitsSvc salaryBenefits.IService,
	vgEmploymentClient vgEmploymentPb.EmploymentClient,
	vgEmployerNameMatchClient employerNameMatchVgPb.EmployerNameMatchClient,
	preApprovedLoanClient palPb.PreApprovedLoanClient,
	recurringPaymentClient recurringpaymentPb.RecurringPaymentServiceClient,
	enachClient enachPb.EnachServiceClient,
	orderServiceClient orderServicePb.OrderServiceClient,
	txnCatClient categorizerPb.TxnCategorizerClient,
	segmentClient segmentPb.SegmentationServiceClient,
	userGrpClient usergroupPb.GroupClient,
	aaSalaryNextActionManager aaSalary.INextActionManager,
	aaSalaryScreenBuilder aaSalary.IScreenBuilder,
	tieringClient tiering.TieringClient,
	tieringDataCollector feTieringDataCollector.DataCollector,
) *Service {
	return &Service{
		salaryProgramClient:         salaryProgramClient,
		salaryReferralsClient:       salaryReferralsClient,
		employmentClient:            employmentClient,
		healthInsuranceClient:       healthInsuranceClient,
		rewardsGeneratorClient:      rewardsGeneratorClient,
		rewardOffersClient:          rewardOffersClient,
		usersClient:                 usersClient,
		conf:                        conf,
		epifiIconsBucketS3Client:    epifiIconsBucketS3Client,
		salaryProgramBucketS3Client: salaryProgramBucketS3Client,
		dyconf:                      dyconf,
		actorActivityClient:         actorActivityClient,
		savingsClient:               savingsClient,
		actorClient:                 actorClient,
		inAppReferralClient:         inAppReferralClient,
		releaseEvaluator:            releaseEvaluator,
		bankCustClient:              bankCustClient,
		salaryBenefitsSvc:           salaryBenefitsSvc,
		vgEmploymentClient:          vgEmploymentClient,
		vgEmployerNameMatchClient:   vgEmployerNameMatchClient,
		preApprovedLoanClient:       preApprovedLoanClient,
		recurringPaymentClient:      recurringPaymentClient,
		enachClient:                 enachClient,
		orderServiceClient:          orderServiceClient,
		txnCatClient:                txnCatClient,
		segmentClient:               segmentClient,
		userGrpClient:               userGrpClient,
		aaSalaryNextActionManager:   aaSalaryNextActionManager,
		aaSalaryScreenBuilder:       aaSalaryScreenBuilder,
		tieringClient:               tieringClient,
		tieringDataCollector:        tieringDataCollector,
	}
}

func (s *Service) AcknowledgeSalaryVerificationStatus(ctx context.Context, req *fePb.AcknowledgeSalaryVerificationStatusRequest) (*fePb.AcknowledgeSalaryVerificationStatusResponse, error) {
	ackVerificationStatusRes, err := s.salaryProgramClient.AckSalaryVerificationRequestStatus(ctx, &beSalaryPb.AckSalaryVerificationRequestStatusRequest{
		SalaryTxnVerRequestId: req.GetSalaryTxnVerRequestId(),
	})
	if err != nil || !ackVerificationStatusRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error acknowledging salary verification request status", zap.Error(err),
			zap.Any(logger.RPC_STATUS, ackVerificationStatusRes.GetStatus()), zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()),
		)
		return &fePb.AcknowledgeSalaryVerificationStatusResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error ack'ing salary verification request status")},
		}, nil
	}

	return &fePb.AcknowledgeSalaryVerificationStatusResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
	}, nil
}

func (s *Service) GetSalaryProgramActivationStatus(ctx context.Context, req *fePb.GetSalaryProgramActivationStatusRequest) (*fePb.GetSalaryProgramActivationStatusResponse, error) {
	var (
		actorId = req.GetReq().GetAuth().GetActorId()
	)

	registrationStatusRes, err := s.salaryProgramClient.GetCurrentRegStatusAndNextRegStage(ctx, &beSalaryPb.CurrentRegStatusAndNextRegStageRequest{
		ActorId:  actorId,
		FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if err != nil || !registrationStatusRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching current registration status for the actor", zap.Error(err),
			zap.Any(logger.RPC_STATUS, registrationStatusRes.GetStatus()),
		)
		return &fePb.GetSalaryProgramActivationStatusResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error fetching current registration status for the actor")},
		}, nil
	}

	// in case the user has not completed the registration, it will be considered as salary program inactive
	if registrationStatusRes.GetRegistrationStatus() != beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		return &fePb.GetSalaryProgramActivationStatusResponse{
			ActivationStatus: fePb.SalaryProgramActivationStatus_STATUS_INACTIVE,
			RespHeader:       &header.ResponseHeader{Status: rpc.StatusOk()},
		}, nil
	}

	// fetch latest activation details to check whether salary program is active or not
	latestActivationDetailsRes, err := s.salaryProgramClient.GetLatestActivationDetailsActiveAtTime(ctx, &beSalaryPb.LatestActivationDetailsActiveAtTimeRequest{
		RegistrationId: registrationStatusRes.GetRegistrationId(),
		ActiveAtTime:   timestampPb.Now(),
		// activation_kind is unspecified as latest activation can be used to determine whether an active salary program exists for the user.
		ActivationKind: beSalaryPb.SalaryProgramActivationKind_SALARY_PROGRAM_ACTIVATION_KIND_UNSPECIFIED,
	})
	if err != nil || (!latestActivationDetailsRes.GetStatus().IsSuccess() && !latestActivationDetailsRes.GetStatus().IsRecordNotFound()) {
		logger.Error(ctx, "error fetching latest activation details for the registration id", zap.Error(err),
			zap.Any(logger.RPC_STATUS, latestActivationDetailsRes.GetStatus()),
			zap.String(logger.REGISTRATION_ID, registrationStatusRes.GetRegistrationId()),
		)
		return &fePb.GetSalaryProgramActivationStatusResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error fetching latest salary program activation details")},
		}, nil
	}

	// if no ongoing activation details are found, it will be considered as salary program inactive
	if latestActivationDetailsRes.GetStatus().IsRecordNotFound() {
		return &fePb.GetSalaryProgramActivationStatusResponse{
			ActivationStatus: fePb.SalaryProgramActivationStatus_STATUS_INACTIVE,
			RespHeader:       &header.ResponseHeader{Status: rpc.StatusOk()},
		}, nil
	}

	return &fePb.GetSalaryProgramActivationStatusResponse{
		ActivationStatus: fePb.SalaryProgramActivationStatus_STATUS_ACTIVE,
		RespHeader:       &header.ResponseHeader{Status: rpc.StatusOk()},
	}, nil
}

// ConfirmEmployerUpdateForSalaryVerification updates the employer detected by us post getting a confirmation input from the user. This is done in cases where we identify salary
// from an employer different from the one selected by the user. It also does the following:
// 1. Marks the salary txn verification request as verified by OPS.
// 2. Auto-Acknowledges the success terminal state for the txn verification request.
// nolint:funlen,dupl
func (s *Service) ConfirmEmployerUpdateForSalaryVerification(ctx context.Context, req *fePb.ConfirmEmployerUpdateForSalaryVerificationRequest) (*fePb.ConfirmEmployerUpdateForSalaryVerificationResponse, error) {
	var (
		actorId              = req.GetReq().GetAuth().GetActorId()
		txnVerificationReqId = req.GetTxnVerificationRequestId()
	)

	// fetch details of the salary txn verification request
	txnVerificationRequestRes, err := s.salaryProgramClient.GetSalaryTxnVerificationRequests(ctx, &beSalaryPb.GetSalaryTxnVerificationRequestsRequest{
		PageContext: &rpc.PageContextRequest{PageSize: 1},
		Filters: &beSalaryPb.GetSalaryTxnVerificationRequestsRequest_Filters{
			Id: txnVerificationReqId,
		},
	})
	if err != nil || !txnVerificationRequestRes.GetStatus().IsSuccess() || len(txnVerificationRequestRes.GetSalaryTxnVerificationRequests()) < 1 {
		logger.Error(ctx, "error fetching salary txn verification request by id", zap.Error(err), zap.String(logger.REQUEST_ID, txnVerificationReqId),
			zap.Any(logger.RPC_STATUS, txnVerificationRequestRes.GetStatus()), zap.Int("totalRequests", len(txnVerificationRequestRes.GetSalaryTxnVerificationRequests())),
		)
		return &fePb.ConfirmEmployerUpdateForSalaryVerificationResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error fetching salary txn verification request by id")},
		}, nil
	}

	txnVerificationRequest := txnVerificationRequestRes.GetSalaryTxnVerificationRequests()[0]

	// todo: should we check whether the txnVerificationReqId actually belongs to the concerned actor?

	if (txnVerificationRequest.GetVerificationStatus() != beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS && txnVerificationRequest.GetVerificationStatus() != beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED) ||
		txnVerificationRequest.GetVerificationSubStatus() != beSalaryPb.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_EMPLOYMENT_UPDATE {
		logger.Error(ctx, "txn verification request status or sub-status are not as expected. Gracefully returning non-error response", zap.String(logger.REQUEST_ID, txnVerificationReqId),
			zap.String("txnVerificationSubStatus", txnVerificationRequest.GetVerificationSubStatus().String()),
			zap.String("txnVerificationStatus", txnVerificationRequest.GetVerificationStatus().String()),
		)
		// not returning error response deliberately to handle it gracefully from UX pov.
		return &fePb.ConfirmEmployerUpdateForSalaryVerificationResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusOkWithDebugMsg("status or sub-status not as expected")},
		}, nil
	}

	// update employer of the user
	empUpdateRes, err := s.employmentClient.UpdateNewEmploymentData(ctx, &beEmploymentPb.UpdateNewEmploymentDataRequest{
		ActorId:        actorId,
		EmploymentType: beEmploymentPb.EmploymentType_SALARIED,
		EmploymentInfoOptions: &beEmploymentPb.UpdateNewEmploymentDataRequest_EmployerInfo{
			EmployerInfo: &beEmploymentPb.EmployerInfoOption{
				EmployerId: txnVerificationRequest.GetTxnEmployerId(),
			},
		},
		UpdateSource: beEmploymentPb.UpdateSource_UPDATE_SOURCE_SALARY_PROGRAM_INAPP,
	})
	if err != nil || !empUpdateRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error updating employer of the user", zap.Error(err),
			zap.Any(logger.RPC_STATUS, empUpdateRes.GetStatus()), zap.String("employerId", txnVerificationRequest.GetTxnEmployerId()),
		)
		return &fePb.ConfirmEmployerUpdateForSalaryVerificationResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error updating employer of the actor")},
		}, nil
	}

	// mark the txn verification request as verified for in progress request
	if txnVerificationRequest.GetVerificationStatus() == beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS &&
		txnVerificationRequest.GetVerificationSubStatus() == beSalaryPb.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_EMPLOYMENT_UPDATE {
		updateTxnVerificationRes, updateManualSalaryTxnVerificationStatusErr := s.salaryProgramClient.UpdateManualSalaryTxnVerificationStatus(ctx, &beSalaryPb.UpdateManualSalaryTxnVerificationStatusRequest{
			SalaryTxnVerRequestId: txnVerificationReqId,
			UpdateStatusTo:        beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED,
			VerifiedBy:            beSalaryPb.SalaryTxnVerificationRequestVerifiedBy_VERIFIED_BY_OPS,
			SalaryTxnEmployerId:   txnVerificationRequest.GetTxnEmployerId(),
		})
		if updateManualSalaryTxnVerificationStatusErr != nil || !updateTxnVerificationRes.GetStatus().IsSuccess() {
			logger.Error(ctx, "error updating manual salary txn verification request", zap.String(logger.REQUEST_ID, txnVerificationReqId),
				zap.String("employerId", txnVerificationRequest.GetTxnEmployerId()), zap.Error(updateManualSalaryTxnVerificationStatusErr),
				zap.Any(logger.RPC_STATUS, updateTxnVerificationRes.GetStatus()),
			)
			return &fePb.ConfirmEmployerUpdateForSalaryVerificationResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error updating manual salary txn verification status")},
			}, nil
		}
	}

	// auto acknowledge the success terminal state so that user doesn't have to do so manually. Why?
	// confirming the employer update nudge is in itself an acknowledgement from the user.
	ackVerificationStatusRes, err := s.salaryProgramClient.AckSalaryVerificationRequestStatus(ctx, &beSalaryPb.AckSalaryVerificationRequestStatusRequest{
		SalaryTxnVerRequestId: txnVerificationReqId,
	})
	if err != nil || !ackVerificationStatusRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error acknowledging salary verification request status, but not returning error in response", zap.Error(err),
			zap.Any(logger.RPC_STATUS, ackVerificationStatusRes.GetStatus()), zap.String(logger.REQUEST_ID, txnVerificationReqId),
		)
		/*
			Not returning error in this case as it's not a critical step and the acknowledgement can be performed by the user
			manually when we show the corresponding ack msg.
		*/
	}

	return &fePb.ConfirmEmployerUpdateForSalaryVerificationResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
	}, nil
}

// DismissEmployerUpdateNudgeForSalaryVerification dismisses the nudge we provide to the user when we detect salary from an employer different from the one
// selected by the user. It marks the salary-txn-verification request as failed and also acknowledges the failure.
// nolint:funlen,dupl
func (s *Service) DismissEmployerUpdateNudgeForSalaryVerification(ctx context.Context, req *fePb.DismissEmployerUpdateNudgeForSalaryVerificationRequest) (*fePb.DismissEmployerUpdateNudgeForSalaryVerificationResponse, error) {
	var (
		txnVerificationReqId = req.GetTxnVerificationRequestId()
	)

	// fetch details of the salary txn verification request
	txnVerificationRequestRes, err := s.salaryProgramClient.GetSalaryTxnVerificationRequests(ctx, &beSalaryPb.GetSalaryTxnVerificationRequestsRequest{
		PageContext: &rpc.PageContextRequest{PageSize: 1},
		Filters: &beSalaryPb.GetSalaryTxnVerificationRequestsRequest_Filters{
			Id: txnVerificationReqId,
		},
	})
	if err != nil || !txnVerificationRequestRes.GetStatus().IsSuccess() || len(txnVerificationRequestRes.GetSalaryTxnVerificationRequests()) < 1 {
		logger.Error(ctx, "error fetching salary txn verification request by id", zap.Error(err), zap.String(logger.REQUEST_ID, txnVerificationReqId),
			zap.Any(logger.RPC_STATUS, txnVerificationRequestRes.GetStatus()), zap.Int("totalRequests", len(txnVerificationRequestRes.GetSalaryTxnVerificationRequests())),
		)
		return &fePb.DismissEmployerUpdateNudgeForSalaryVerificationResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error fetching salary txn verification request by id")},
		}, nil
	}

	txnVerificationRequest := txnVerificationRequestRes.GetSalaryTxnVerificationRequests()[0]

	// todo: should we check whether the txnVerificationReqId actually belongs to the concerned actor?

	if (txnVerificationRequest.GetVerificationStatus() != beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS && txnVerificationRequest.GetVerificationStatus() != beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED) ||
		txnVerificationRequest.GetVerificationSubStatus() != beSalaryPb.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_EMPLOYMENT_UPDATE {
		logger.Error(ctx, "txn verification request status or sub-status not as expected. Gracefully returning non-error response", zap.String(logger.REQUEST_ID, txnVerificationReqId),
			zap.String("txnVerificationSubStatus", txnVerificationRequest.GetVerificationSubStatus().String()),
			zap.String("txnVerificationStatus", txnVerificationRequest.GetVerificationStatus().String()),
		)
		// not returning error response deliberately to handle it gracefully from UX pov.
		return &fePb.DismissEmployerUpdateNudgeForSalaryVerificationResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusOkWithDebugMsg("status or sub-status not as expected")},
		}, nil
	}

	// mark the txn verification request as verification-failed only if the request is in In-progress state
	if txnVerificationRequest.GetVerificationStatus() == beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS &&
		txnVerificationRequest.GetVerificationSubStatus() == beSalaryPb.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_EMPLOYMENT_UPDATE {
		updateTxnVerificationRes, updateManualSalaryTxnVerificationStatusErr := s.salaryProgramClient.UpdateManualSalaryTxnVerificationStatus(ctx, &beSalaryPb.UpdateManualSalaryTxnVerificationStatusRequest{
			SalaryTxnVerRequestId: txnVerificationReqId,
			UpdateStatusTo:        beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFICATION_FAILED,
			VerifiedBy:            beSalaryPb.SalaryTxnVerificationRequestVerifiedBy_VERIFIED_BY_OPS,
		})
		if updateManualSalaryTxnVerificationStatusErr != nil || !updateTxnVerificationRes.GetStatus().IsSuccess() {
			logger.Error(ctx, "error updating manual salary txn verification request", zap.String(logger.REQUEST_ID, txnVerificationReqId),
				zap.Error(updateManualSalaryTxnVerificationStatusErr), zap.Any(logger.RPC_STATUS, updateTxnVerificationRes.GetStatus()),
			)
			return &fePb.DismissEmployerUpdateNudgeForSalaryVerificationResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error updating manual salary txn verification status")},
			}, nil
		}
	}

	// auto acknowledge the failure terminal state so that user doesn't have to do so manually. Why?
	// dismissing the employer update nudge is in itself an acknowledgement from the user.
	ackVerificationStatusRes, err := s.salaryProgramClient.AckSalaryVerificationRequestStatus(ctx, &beSalaryPb.AckSalaryVerificationRequestStatusRequest{
		SalaryTxnVerRequestId: txnVerificationReqId,
	})
	if err != nil || !ackVerificationStatusRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error acknowledging salary verification request status", zap.Error(err),
			zap.Any(logger.RPC_STATUS, ackVerificationStatusRes.GetStatus()), zap.String(logger.REQUEST_ID, txnVerificationReqId),
		)
		return &fePb.DismissEmployerUpdateNudgeForSalaryVerificationResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error ack'ing salary verification request status")},
		}, nil
	}

	return &fePb.DismissEmployerUpdateNudgeForSalaryVerificationResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
	}, nil
}

// GetDetailsToShareOnEmail returns the email content required for sharing any details via email, ex- sharing account details with the employer
// Currently there is only once use case, can add param in rpc req to extend it for more use cases.
func (s *Service) GetDetailsToShareOnEmail(ctx context.Context, req *fePb.GetDetailsToShareOnEmailRequest) (*fePb.GetDetailsToShareOnEmailResponse, error) {
	var (
		actorId = req.GetReq().GetAuth().GetActorId()
	)
	if req.GetEmailTemplateType() == fePb.SalaryProgramEmailTemplateType_SHARE_PAYSLIP_ON_EMPLOYER_VERIFICATION_FAILURE {
		// fetch user
		userResp, userErr := s.usersClient.GetUser(ctx, &usersPb.GetUserRequest{
			Identifier: &usersPb.GetUserRequest_ActorId{
				ActorId: actorId,
			},
		})
		if rpcErr := epifigrpc.RPCError(userResp, userErr); rpcErr != nil {
			logger.Error(ctx, "error fetching user by actor id", zap.Error(rpcErr))
			return &fePb.GetDetailsToShareOnEmailResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error fetching user by actor id")},
			}, nil
		}
		user := userResp.GetUser()

		// fetch savings account details
		accountRes, accountErr := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
				PrimaryUserId: user.GetId(),
			},
		})
		// note: no Status field in the RPC's response.
		if accountErr != nil || accountRes.GetAccount() == nil {
			logger.Error(ctx, "savingsClient.GetAccount rpc call failed", zap.Error(accountErr))
			return &fePb.GetDetailsToShareOnEmailResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("savingsClient.GetAccount rpc call failed")},
			}, nil
		}

		var (
			savingsAccount        = accountRes.GetAccount()
			accountNumber         = savingsAccount.GetAccountNo()
			phoneNumber           = user.GetProfile().GetPhoneNumber()
			bestNameFromProfile   = gammanames.BestNameFromProfile(ctx, user.GetProfile()).ToString()
			sharePayslipEmailBody = s.dyconf.SalaryProgram().SharePayslipForEmployerVerificationMailInfo().EmailBody()
			sharePayslipToEmailId = s.dyconf.SalaryProgram().SharePayslipForEmployerVerificationMailInfo().ToEmailId()
		)

		sharePayslipEmailBody = strings.ReplaceAll(sharePayslipEmailBody, "<ACCOUNT_HOLDER_NAME_PLACEHOLDER>", bestNameFromProfile)
		sharePayslipEmailBody = strings.ReplaceAll(sharePayslipEmailBody, "<ACCOUNT_NUMBER_PLACEHOLDER>", accountNumber)
		sharePayslipEmailBody = strings.ReplaceAll(sharePayslipEmailBody, "<NAME_PLACEHOLDER>", bestNameFromProfile)
		sharePayslipEmailBody = strings.ReplaceAll(sharePayslipEmailBody, "<PHONE_NUMBER_PLACEHOLDER>", phoneNumber.ToString())

		return &fePb.GetDetailsToShareOnEmailResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusOk(),
			},
			EmailBody: sharePayslipEmailBody,
			ToEmailId: sharePayslipToEmailId,
		}, nil
	}

	// if template type is not passed then return share account details with HR email template as default.

	emailContent, err := s.getShareAccountDetailsWithHrEmailContent(ctx, actorId, true)
	if err != nil {
		logger.Error(ctx, "error fetching share account details with HR email content", zap.Error(err))
		return &fePb.GetDetailsToShareOnEmailResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error fetching share account details with HR email content, err: %v", err)),
			},
		}, nil
	}

	return &fePb.GetDetailsToShareOnEmailResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		EmailBody:        emailContent.GetEmailBody(),
		EmailSubject:     emailContent.GetEmailSubject(),
		EmailAttachments: emailContent.GetEmailAttachments(),
	}, nil
}

func (s *Service) getShareAccountDetailsWithHrEmailContent(ctx context.Context, actorId string, sendCancelledChequeAttachment bool) (*fePb.ShareDetailsOnEmailActionData, error) {
	var (
		savingsAccount *savingsPb.Account
	)

	userInfo, usrErr := s.getUserByActorId(ctx, actorId)
	if usrErr != nil {
		return nil, fmt.Errorf("error fetching user by actorId: %w", usrErr)
	}

	// fetch savings account details
	accountRes, accountErr := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
			PrimaryUserId: userInfo.GetId(),
		},
	})
	// note: no Status field in the RPC's response.
	if accountErr != nil || accountRes.GetAccount() == nil {
		return nil, fmt.Errorf("error fetching savings account details for the user, err: %w", accountErr)
	}
	savingsAccount = accountRes.GetAccount()

	var (
		accountNumber              = savingsAccount.GetAccountNo()
		ifscCode                   = savingsAccount.GetIfscCode()
		bestNameFromProfile        = gammanames.BestNameFromProfile(ctx, userInfo.GetProfile()).ToString()
		accountDetailsEmailSubject = s.dyconf.SalaryProgram().ShareAccountDetailsMailInfo().EmailSubject()
		accountDetailsEmailBody    = s.dyconf.SalaryProgram().ShareAccountDetailsMailInfo().EmailBody()
		emailAttachments           []*commontypes.Image
	)
	if bestNameFromProfile == "" {
		return nil, fmt.Errorf("no best name found for the actor from user profile")
	}

	if sendCancelledChequeAttachment {
		chequeRes, err := s.bankCustClient.GetDigitalCancelledCheque(ctx, &bankCustPb.GetDigitalCancelledChequeRequest{
			ActorId: actorId,
		})
		if grpcErr := epifigrpc.RPCError(chequeRes, err); grpcErr != nil {
			// intentionally muting the error to not fail the entire flow due to failure in fetching cancelled cheque
			logger.Error(ctx, "error fetching digital cancelled cheque", zap.Error(grpcErr))
		} else {
			accountDetailsEmailBody = s.dyconf.SalaryProgram().ShareAccountDetailsMailInfo().EmailBodyWithCancelledCheque()
			emailAttachments = append(emailAttachments, chequeRes.GetCheque())
		}
	}

	accountDetailsEmailBody = strings.ReplaceAll(accountDetailsEmailBody, "<ACCOUNT_HOLDER_NAME_PLACEHOLDER>", bestNameFromProfile)
	accountDetailsEmailBody = strings.ReplaceAll(accountDetailsEmailBody, "<ACCOUNT_NUMBER_PLACEHOLDER>", accountNumber)
	accountDetailsEmailBody = strings.ReplaceAll(accountDetailsEmailBody, "<ACCOUNT_IFSC_PLACEHOLDER>", ifscCode)
	accountDetailsEmailBody = strings.ReplaceAll(accountDetailsEmailBody, "<NAME>", bestNameFromProfile)

	return &fePb.ShareDetailsOnEmailActionData{
		EmailBody:        accountDetailsEmailBody,
		EmailSubject:     accountDetailsEmailSubject,
		EmailAttachments: emailAttachments,
	}, nil
}

// nolint:funlen
func (s *Service) GetSalaryAccountBenefitsLandingPage(ctx context.Context, req *fePb.SalaryAccountBenefitsLandingPageRequest) (*fePb.SalaryAccountBenefitsLandingPageResponse, error) {
	var (
		actorId                    = req.GetReq().GetAuth().GetActorId()
		appPlatform                = req.GetReq().GetAuth().GetDevice().GetPlatform()
		appVersion                 = req.GetReq().GetAppVersionCode()
		latestActivationDetailsRes *beSalaryPb.LatestActivationDetailsActiveAtTimeResponse
		activationHistories        []*beSalaryPb.SalaryProgramActivationHistory
		salaryLiteMandateReq       *beSalaryPb.SalaryLiteMandateRequest
	)

	registrationDetailsRes, err := s.salaryProgramClient.GetRegistrationDetails(ctx, &beSalaryPb.GetRegistrationDetailsRequest{
		ActorId:  actorId,
		FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if err != nil || !registrationDetailsRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching current registration status for the user", zap.Error(err),
			zap.Any(logger.RPC_STATUS, registrationDetailsRes.GetStatus()),
		)
		return &fePb.SalaryAccountBenefitsLandingPageResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error fetching current registration status of the user")},
		}, nil
	}

	if registrationDetailsRes.GetRegistrationStatus() != beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		logger.Error(ctx, "salary program registration has not completed for the user",
			zap.String(logger.REGISTRATION_ID, registrationDetailsRes.GetRegistrationId()),
		)
		return &fePb.SalaryAccountBenefitsLandingPageResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusFailedPreconditionWithDebugMsg("salary program registration has not completed for the user")},
		}, nil
	}

	// todo(yuvraj): remove this post salary lite release to whole userbase
	isUserEligibleForSalaryLite, err := s.isUserEligibleForSalaryLite(ctx, actorId)
	if err != nil {
		// muting the error, as landing page should not break due to this error
		logger.Error(ctx, "error checking the eligibility of user for salary lite", zap.Error(err))
		isUserEligibleForSalaryLite = false
	}

	errGrp, gctx := errgroup.WithContext(ctx)
	errGrp.Go(func() error {
		res, getErr := s.salaryProgramClient.GetLatestActivationDetailsActiveAtTime(gctx, &beSalaryPb.LatestActivationDetailsActiveAtTimeRequest{
			RegistrationId: registrationDetailsRes.GetRegistrationId(),
			ActiveAtTime:   timestampPb.Now(),
		})
		if getErr != nil || (!res.GetStatus().IsSuccess() && !res.GetStatus().IsRecordNotFound()) {
			logger.Error(gctx, "error fetching latest activation details for the registration id", zap.Error(getErr),
				zap.Any(logger.RPC_STATUS, res.GetStatus()),
				zap.String(logger.REGISTRATION_ID, registrationDetailsRes.GetRegistrationId()),
			)
			return fmt.Errorf("error fetching latest activation details for the registration id")
		}
		latestActivationDetailsRes = res
		return nil
	})
	errGrp.Go(func() error {
		// return if user is not eligible for salary lite, since it's used only in lite flow
		if !isUserEligibleForSalaryLite {
			return nil
		}
		salaryLiteMandateReqsRes, getErr := s.salaryProgramClient.GetSalaryLiteMandateRequests(ctx, &beSalaryPb.GetSalaryLiteMandateRequestsRequest{
			Filters: &beSalaryPb.GetSalaryLiteMandateRequestsRequest_Filters{
				ActorId: actorId,
			},
			PageContextRequest: &rpc.PageContextRequest{
				PageSize: 1,
			},
		})
		if salaryLiteMandateReqsRes.GetStatus().IsRecordNotFound() {
			return nil
		}
		if rpcErr := epifigrpc.RPCError(salaryLiteMandateReqsRes, getErr); rpcErr != nil {
			logger.Error(ctx, "salaryProgramClient.GetSalaryLiteMandateRequests call failed", zap.Error(rpcErr))
			return fmt.Errorf("salaryProgramClient.GetSalaryLiteMandateRequests call failed, err: %w", rpcErr)
		}
		salaryLiteMandateReq = salaryLiteMandateReqsRes.GetSalaryLiteMandateRequests()[0]
		return nil
	})
	errGrp.Go(func() error {
		// return if user is not eligible for salary lite, since it's used only in lite flow
		if !isUserEligibleForSalaryLite {
			return nil
		}
		res, getErr := s.salaryProgramClient.GetSalaryProgramActivationHistories(gctx, &beSalaryPb.GetSalaryProgramActivationHistoriesRequest{
			RegistrationId: registrationDetailsRes.GetRegistrationId(),
			SortOrder:      beSalaryPb.SortOrder_DESC,
			PageContext: &rpc.PageContextRequest{
				PageSize: 1,
			},
		})

		if getErr != nil || (!res.GetStatus().IsSuccess() && !res.GetStatus().IsRecordNotFound()) {
			logger.Error(gctx, "error fetching salary program activation histories", zap.Error(getErr),
				zap.Any(logger.RPC_STATUS, res.GetStatus()),
				zap.String(logger.REGISTRATION_ID, registrationDetailsRes.GetRegistrationId()),
			)
			return fmt.Errorf("error fetching salary program activation histories")
		}
		activationHistories = res.GetActivationHistories()
		return nil
	})

	errGrp.Go(func() error {
		getSavingsResp, getSavingsErr := s.savingsClient.GetSavingsAccountEssentials(gctx, &savingsPb.GetSavingsAccountEssentialsRequest{
			Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorUniqueAccountIdentifier{
				ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
					ActorId:                actorId,
					AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
					PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
		})
		if rpcErr := epifigrpc.RPCError(getSavingsResp, getSavingsErr); rpcErr != nil {
			if getSavingsResp.GetStatus().IsRecordNotFound() {
				return nil
			}
			logger.Error(gctx, "error fetching savings account essentials", zap.Error(rpcErr))
			return fmt.Errorf("error fetching savings account essentials, err: %w", rpcErr)
		}

		return nil
	})

	if errGrpErr := errGrp.Wait(); errGrpErr != nil {
		return &fePb.SalaryAccountBenefitsLandingPageResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(errGrpErr.Error())},
		}, nil
	}

	var (
		isFullSalaryProgramActive bool
		isSalaryLiteProgramActive bool
		// user was salary program active previously but not active currently
		isChurnedUser bool
		// user have salary lite mandate creation request in either success or in_progress state.
		// this doesn't implies that user is salary lite active.
		isSalaryLiteUser         bool
		salaryProgramActiveSince *timestampPb.Timestamp
		// salaryProgramActiveTill    *timestampPb.Timestamp
		salaryTxnCreditDate        *timestampPb.Timestamp
		latestActivationEmployerId string
		regCompletionTime          = registrationDetailsRes.RegistrationCompletionTime
	)

	switch {
	case !latestActivationDetailsRes.GetStatus().IsRecordNotFound():
		salaryProgramActiveSince = latestActivationDetailsRes.GetActiveFrom()
		// salaryProgramActiveTill = latestActivationDetailsRes.GetActiveTill()

		if latestActivationDetailsRes.GetActivationType() == beSalaryPb.SalaryActivationType_FULL_SALARY_ACTIVATION {
			isFullSalaryProgramActive = true
			salaryTxnCreditDate = latestActivationDetailsRes.GetSalaryTxnTimestamp()
			latestActivationEmployerId = latestActivationDetailsRes.GetSalaryTxnEmployerId()
		} else {
			isSalaryLiteProgramActive = true
		}
	case len(activationHistories) > 0:
		isChurnedUser = true
	}

	if isSalaryLiteProgramActive || salaryLiteMandateReq.GetRequestStatus() == beSalaryPb.SalaryLiteMandateRequestStatus_IN_PROGRESS ||
		salaryLiteMandateReq.GetRequestStatus() == beSalaryPb.SalaryLiteMandateRequestStatus_SUCCESS {
		isSalaryLiteUser = true
	}

	var (
		// Salary account registration completion will show stepper info instead of section info
		salaryAccountVerificationStepperInfo *fePb.SalaryAccountBenefitsLandingPageResponse_StepperInfo
		// info of page sections of the salary landing page
		salaryAccountVerificationSectionInfo *fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo
		quickLinksSectionInfo                *fePb.SalaryAccountBenefitsLandingPageResponse_QuickLinksInfo
		benefitsSectionInfo                  *fePb.SalaryAccountBenefitsLandingPageResponse_PageSection
		winningsSectionInfo                  *fePb.WinningsInfo
		employmentSectionInfo                *fePb.SalaryAccountBenefitsLandingPageResponse_EmploymentInfo
		helpSectionInfo                      *fePb.SalaryAccountBenefitsLandingPageResponse_HelpInfo
		bannerSectionInfo                    *fePb.SalaryAccountBenefitsLandingPageResponse_BannerInfo
		// banner showing eligibility details for salary program
		eligibilityBannerSectionInfo *fePb.SalaryAccountBenefitsLandingPageResponse_BannerInfo
		salaryLiteBannerSectionInfo  *fePb.SalaryAccountBenefitsLandingPageResponse_SalaryLiteBannerInfo
		commsSectionInfo             *fePb.SalaryAccountBenefitsLandingPageResponse_CommsInfo
	)

	pageSectionsErrGrp, gctx := errgroup.WithContext(ctx)

	pageSectionsErrGrp.Go(func() error {
		commsInfo, sectionErr := s.getCommsSectionInfo(gctx, actorId, isUserEligibleForSalaryLite, isSalaryLiteUser, isSalaryLiteProgramActive, isFullSalaryProgramActive, salaryProgramActiveSince.AsTime(), salaryLiteMandateReq)
		if sectionErr != nil {
			return fmt.Errorf("error fetching comms section info: %w", sectionErr)
		}
		if commsInfo != nil {
			commsSectionInfo = commsInfo
			return nil
		}
		salaryVerificationInfo, stepperInfo, sectionErr := s.getSalaryAccountVerificationSectionInfo(gctx, actorId, isFullSalaryProgramActive, regCompletionTime, appPlatform, appVersion)
		if sectionErr != nil {
			return fmt.Errorf("error fetching salary account verification section info: %w", sectionErr)
		}

		// if stepper info is found, we set it. For older clients - we support the section info
		if stepperInfo != nil {
			salaryAccountVerificationStepperInfo = stepperInfo
			return nil
		}

		/*
			if no info associated with salary account verification is supposed to be shown, then we can utilise the space to promote other comms
		*/
		if salaryVerificationInfo == nil {
			var commsInfoErr error

			salaryVerificationInfo, commsInfoErr = s.getLandingPageTopSectionCommsInfo(gctx, actorId, isFullSalaryProgramActive, latestActivationEmployerId, salaryTxnCreditDate, appPlatform, appVersion)
			if commsInfoErr != nil {
				logger.Error(gctx, "error while fetching info for comms to be done via top section. Ignoring silently to handle it gracefully", zap.Error(commsInfoErr))
			}
		}
		if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dyconf.SalaryProgram().LandingPageCommsInfoSectionFeatureConfig()) && salaryVerificationInfo != nil {
			// commsSectionInfo is v1 version of salaryAccountVerificationSectionInfo component
			commsInfoTile := s.getCommsInfoTileFromSalaryAccountVerificationInfo(salaryVerificationInfo)
			commsSectionInfo = &fePb.SalaryAccountBenefitsLandingPageResponse_CommsInfo{
				CommsInfoTiles: []*fePb.SalaryAccountBenefitsLandingPageResponse_CommsInfoTile{
					commsInfoTile,
				},
			}
			return nil
		}

		salaryAccountVerificationSectionInfo = salaryVerificationInfo
		return nil
	})
	pageSectionsErrGrp.Go(func() error {
		sectionInfo := s.getQuickLinksSectionInfo(gctx, actorId, isFullSalaryProgramActive, isSalaryLiteUser, isUserEligibleForSalaryLite, salaryLiteMandateReq.GetRequestStatus(), appPlatform, appVersion)

		quickLinksSectionInfo = sectionInfo
		return nil
	})
	pageSectionsErrGrp.Go(func() error {
		sectionInfo, sectionErr := s.getBenefitsSectionInfo(gctx, actorId, isFullSalaryProgramActive, isSalaryLiteProgramActive, isSalaryLiteUser, salaryProgramActiveSince, salaryTxnCreditDate, appPlatform, appVersion)
		if sectionErr != nil {
			return fmt.Errorf("error fetching benefits section info: %w", sectionErr)
		}

		benefitsSectionInfo = sectionInfo
		return nil
	})
	pageSectionsErrGrp.Go(func() error {
		sectionInfo, sectionErr := s.getWinningsSectionInfo(gctx, actorId, appPlatform, appVersion)
		if sectionErr != nil {
			return fmt.Errorf("error fetching winnings section info: %w", sectionErr)
		}

		winningsSectionInfo = sectionInfo
		return nil
	})
	pageSectionsErrGrp.Go(func() error {
		sectionInfo, sectionErr := s.getEmploymentSectionInfo(gctx, actorId)
		if sectionErr != nil {
			return fmt.Errorf("error fetching employment section info: %w", sectionErr)
		}

		employmentSectionInfo = sectionInfo
		return nil
	})
	pageSectionsErrGrp.Go(func() error {
		sectionInfo, sectionErr := s.getHelpSectionInfo(gctx, appPlatform, appVersion)
		if sectionErr != nil {
			return fmt.Errorf("error fetching employment section info: %w", sectionErr)
		}

		helpSectionInfo = sectionInfo
		return nil
	})
	pageSectionsErrGrp.Go(func() error {
		sectionInfo, sectionErr := s.getBannerSectionInfo(gctx, isSalaryLiteUser, isFullSalaryProgramActive)
		if sectionErr != nil {
			return fmt.Errorf("error fetching banner section info: %w", sectionErr)
		}

		bannerSectionInfo = sectionInfo
		return nil
	})
	pageSectionsErrGrp.Go(func() error {
		sectionInfo, sectionErr := s.getEligibilityBannerSectionInfo(gctx, actorId, isFullSalaryProgramActive, isSalaryLiteUser)
		if sectionErr != nil {
			return fmt.Errorf("error fetching eligibility banner section info: %w", sectionErr)
		}

		eligibilityBannerSectionInfo = sectionInfo
		return nil
	})
	pageSectionsErrGrp.Go(func() error {
		sectionInfo, sectionErr := s.getSalaryLiteBannerSectionInfo(ctx, actorId, isSalaryLiteUser, isFullSalaryProgramActive, isChurnedUser, isUserEligibleForSalaryLite, regCompletionTime)
		if sectionErr != nil {
			return fmt.Errorf("error fetching salary lite banner section info: %w", sectionErr)
		}

		salaryLiteBannerSectionInfo = sectionInfo
		return nil
	})

	if pageSectionErr := pageSectionsErrGrp.Wait(); pageSectionErr != nil {
		logger.Error(ctx, "error while fetching page sections info for salary landing page", zap.Error(pageSectionErr))
		return &fePb.SalaryAccountBenefitsLandingPageResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error while fetching page sections info")},
		}, nil
	}

	// Note: the ordering here matters as the client can use the order to render the page sections accordingly
	var pageSections []*fePb.SalaryAccountBenefitsLandingPageResponse_PageSection
	if salaryAccountVerificationStepperInfo != nil {
		pageSections = append(pageSections, &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection{
			Section: &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection_StepperInfo{
				StepperInfo: salaryAccountVerificationStepperInfo,
			},
		})
	}
	if commsSectionInfo != nil {
		pageSections = append(pageSections, &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection{
			Section: &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection_CommsInfo{
				CommsInfo: commsSectionInfo,
			},
		})
	}
	if salaryAccountVerificationSectionInfo != nil {
		pageSections = append(pageSections, &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection{
			Section: &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection_VerificationInfo{
				VerificationInfo: salaryAccountVerificationSectionInfo,
			},
		})
	}
	if eligibilityBannerSectionInfo != nil {
		pageSections = append(pageSections, &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection{
			Section: &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection_BannerInfo{
				BannerInfo: eligibilityBannerSectionInfo,
			},
		})
	}
	if salaryLiteBannerSectionInfo != nil {
		pageSections = append(pageSections, &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection{
			Section: &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection_SalaryLiteBannerInfo{
				SalaryLiteBannerInfo: salaryLiteBannerSectionInfo,
			},
		})
	}
	if bannerSectionInfo != nil && s.dyconf.SalaryProgram().EnableSalaryReferralsSeason() {
		pageSections = append(pageSections, &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection{
			Section: &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection_BannerInfo{
				BannerInfo: bannerSectionInfo,
			},
		})
	}
	if benefitsSectionInfo != nil {
		pageSections = append(pageSections, benefitsSectionInfo)
	}
	// todo (utkarsh) : add support for changing the order of quick links section based on some condition (active/inactive status , segment etc)
	if quickLinksSectionInfo != nil {
		pageSections = append(pageSections, &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection{
			Title: &fePb.Text{Text: "Quick Links", FontColor: "#FFFFFF"}, // Snow
			Section: &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection_QuickLinksInfo{
				QuickLinksInfo: quickLinksSectionInfo,
			},
		})
	}
	if winningsSectionInfo != nil {
		pageSections = s.getWinningSection(pageSections, winningsSectionInfo)
	}
	if employmentSectionInfo != nil {
		pageSections = append(pageSections, &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection{
			Title: &fePb.Text{Text: "Employment Information", FontColor: "#333333"}, // Night
			Section: &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection_EmploymentInfo{
				EmploymentInfo: employmentSectionInfo,
			},
		})
	}
	if helpSectionInfo != nil {
		pageSections = append(pageSections, &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection{
			Section: &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection_HelpInfo{
				HelpInfo: helpSectionInfo,
			},
		})
	}

	title := "Salary Account Benefits"
	if isSalaryLiteUser && !isFullSalaryProgramActive {
		title = "Salary Lite benefits"
	}

	res := &fePb.SalaryAccountBenefitsLandingPageResponse{
		RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
		Title:        commontypes.GetTextFromStringFontColourFontStyle(title, "#FFFFFF", commontypes.FontStyle_HEADLINE_3),
		PageSections: pageSections,
	}

	// populate survey information in response object, if applicable
	s.populateInAppCsatSurveyInfo(ctx, actorId, res)

	return res, nil
}

func (s *Service) getWinningSection(pageSections []*fePb.SalaryAccountBenefitsLandingPageResponse_PageSection, winningsSectionInfo *fePb.WinningsInfo) []*fePb.SalaryAccountBenefitsLandingPageResponse_PageSection {
	// show winnings section at top only if the user is active in the full salary program and the current date is within the winning section prioritized monthly interval
	if s.dyconf.SalaryProgram().SalaryWinningSectionPrioritizedMonthlyIntervals().StartDate() <= time.Now().Day() && time.Now().Day() <= s.dyconf.SalaryProgram().SalaryWinningSectionPrioritizedMonthlyIntervals().EndDate() {
		pageSections = append([]*fePb.SalaryAccountBenefitsLandingPageResponse_PageSection{
			{
				Title: &fePb.Text{Text: "My Winnings", FontColor: "#333333"}, // Night
				Section: &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection_WinningsInfo{
					WinningsInfo: winningsSectionInfo,
				},
			},
		}, pageSections...)
	} else {
		pageSections = append(pageSections, &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection{
			Title: &fePb.Text{Text: "My Winnings", FontColor: "#333333"}, // Night
			Section: &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection_WinningsInfo{
				WinningsInfo: winningsSectionInfo,
			},
		})
	}
	return pageSections
}

func (s *Service) getWinningsSectionInfo(ctx context.Context, actorId string, appPlatform commontypes.Platform, appVersion uint32) (*fePb.WinningsInfo, error) {

	rewardsErrGrp, grpCtx := errgroup.WithContext(ctx)

	var totalFiCoinsEarned float32
	rewardsErrGrp.Go(func() error {
		tieringPitchResp, tieringPitchErr := s.tieringClient.GetTieringPitchV2(ctx, &tiering.GetTieringPitchV2Request{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(tieringPitchResp, tieringPitchErr); rpcErr != nil {
			return fmt.Errorf("error fetching tiering pitch: %w", rpcErr)
		}

		var totalFiCoinsErr error
		totalFiCoinsEarned, totalFiCoinsErr = s.tieringDataCollector.GetFiCoinsRewardAggregate(grpCtx, actorId, tieringPitchResp.GetCurrentTier(), time.Now().AddDate(-3, 0, 0), time.Now())
		if totalFiCoinsErr != nil {
			return fmt.Errorf("error fetching total fi coins earned: %w", totalFiCoinsErr)
		}
		return nil
	})

	rewardsAggregateErr := rewardsErrGrp.Wait()
	if rewardsAggregateErr != nil {
		return nil, fmt.Errorf("failed to fetch data in err group, %w", rewardsAggregateErr)
	}

	storiesCta := &fePb.CTA{
		Text: &fePb.Text{Text: "Learn about Fi-Coins", FontColor: "#333333"},
		Action: &fePb.CTA_DeeplinkAction{
			DeeplinkAction: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_STORY_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_StoryScreenOptions{
					StoryScreenOptions: &deeplinkPb.StoryScreenOptions{
						StoryTitle: "Fi-Coins Intro",
						StoryUrl:   "https://stories.fi.money/stories/fi-coins-intro",
						StoryId:    "e24825d2-740b-4fce-9aba-6a1ecc47d0ef",
					},
				},
			},
		},
		ImageUrl:  "https://epifi-icons.pointz.in/salaryprogram/my-winnings-about-fi-coins-cta.png",
		IsVisible: true,
	}
	offerCatalogCta := &fePb.CTA{
		Text:      &fePb.Text{Text: "Spend your Fi-Coins", FontColor: "#333333"},
		Action:    &fePb.CTA_DeeplinkAction{DeeplinkAction: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN}},
		ImageUrl:  "https://epifi-icons.pointz.in/salaryprogram/my-winnings-spend-fi-coins.png",
		IsVisible: true,
	}

	rewardsSummaries := []*fePb.RewardSummary{
		{
			Name:     &fePb.Text{Text: "Fi-Coins", FontColor: "#333333"},
			ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/my-winnings-fi-coins.png",
			Value:    &fePb.Text{Text: fmt.Sprintf("%f", totalFiCoinsEarned), FontColor: "#333333"},
		},
		/*
			// can be uncommented later when we'd want to show cash related winnings.
			{
				Name:     &fePb.Text{Text: "Savings", FontColor: "#333333"},
				ImageUrl: "",
				Value:    &fePb.Text{Text: fmt.Sprintf("₹%d", rewardsSummaryRes.GetTotalCashRewardEarned().GetUnits()+rewardsSummaryRes.GetTotalSidRewardEarned().GetUnits()), FontColor: "#333333"},
			},
		*/
	}

	if appPlatform == commontypes.Platform_IOS && appVersion >= s.dyconf.SalaryProgram().MinIosAppVersionHandlingWinningsSectionV2() ||
		appPlatform == commontypes.Platform_ANDROID && appVersion >= s.dyconf.SalaryProgram().MinAndroidAppVersionHandlingWinningsSectionV2() {
		rewardsSummaries = []*fePb.RewardSummary{
			{
				Name:     &fePb.Text{Text: "FI-COINS EARNED", FontColor: "#8D8D8D"},
				ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/my-winnings-fi-coins.png",
				Value:    &fePb.Text{Text: fmt.Sprintf("%s", money.ToDisplayStringFromIntValue(int(totalFiCoinsEarned))), FontColor: "#333333"},
				BgColor:  "#F7F9FA",
			},
		}
	}

	return &fePb.WinningsInfo{
		RewardSummaries:       rewardsSummaries,
		Ctas:                  []*fePb.CTA{storiesCta, offerCatalogCta},
		IsOffersWidgetVisible: true,
	}, nil
}

func (s *Service) isUserEligibleForSalaryLite(ctx context.Context, actorId string) (bool, error) {
	isFeatureEnabledForUser, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_SALARY_LITE_PROGRAM).WithActorId(actorId))
	if err != nil {
		return false, fmt.Errorf("error checking if salary lite program feature is enabled for the user, err : %w", err)
	}

	if !isFeatureEnabledForUser {
		return false, nil
	}
	return true, nil
}

func (s *Service) getEmploymentSectionInfo(ctx context.Context, actorId string) (*fePb.SalaryAccountBenefitsLandingPageResponse_EmploymentInfo, error) {
	employerInfo, err := s.getCurrentEmployerOfActor(ctx, actorId)
	if err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Debug(ctx, "error fetching employer info for employment section by actorId", zap.Error(err))
			return nil, fmt.Errorf("error fetching employer info for employment section of the user: %w", err)
		}
		/*
			In case no employerInfo is found, we'd return empty details gracefully instead of returning an error.
			Why? Because there is a chance that the user could modify the employer from some other flow and we wouldn't
			want the landing page section to fail loading because of that. This will also allow the user to modify the
			employer from the landing page.
		*/
		logger.Info(ctx, "received empty employer for employment section")
	}

	return &fePb.SalaryAccountBenefitsLandingPageResponse_EmploymentInfo{
		Employer: employerInfo,
		Cta: &fePb.CTA{
			IsVisible: true,
			Action:    &fePb.CTA_DeeplinkAction{DeeplinkAction: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_SALARY_PROGRAM_EMPLOYER_CONFIRMATION_SCREEN}},
			ImageUrl:  "https://epifi-icons.pointz.in/salaryprogram/employment-info-edit-pencil.png",
		},
		ComponentStateId: employerInfo.GetId(),
	}, nil
}

// getBenefitsSectionInfo populates the benefits section info with the following data:
// 1. Benefits Status
// 2. Benefit Cards
// 3. Banner within the benefits section info (currently used for referrals entry point)
// nolint:funlen
func (s *Service) getBenefitsSectionInfo(ctx context.Context, actorId string, isFullSalaryProgramActive, isSalaryLiteProgramActive, isSalaryLiteUser bool, salaryProgramActiveSince, salaryTxnCreditDate *timestampPb.Timestamp, appPlatform commontypes.Platform,
	appVersion uint32) (*fePb.SalaryAccountBenefitsLandingPageResponse_PageSection, error) {
	var (
		benefitsInfo  *fePb.BenefitsInfo
		topBannerInfo *fePb.BenefitsInfo_Banner
	)

	parallelErrGrp, gctx := errgroup.WithContext(ctx)

	parallelErrGrp.Go(func() error {
		benefitsCards, _, err := s.getBenefitsCardsAndCashEquivalentForActor(gctx, actorId, isFullSalaryProgramActive, isSalaryLiteProgramActive, isSalaryLiteUser, SalaryLandingPage, appPlatform, appVersion)
		if err != nil {
			logger.Error(gctx, "error fetching benefits cards for actor", zap.Bool("isSalaryProgramActive", isFullSalaryProgramActive),
				zap.Error(err),
			)
			return fmt.Errorf("error fetching benefits cards for actor: %w", err)
		}

		switch {
		case isFullSalaryProgramActive:
			_, salaryCreditMonth, salaryCreditDay := salaryTxnCreditDate.AsTime().Date()

			benefitsInfo = &fePb.BenefitsInfo{
				Title:          &fePb.Text{Text: "Benefits active", FontColor: "#5D7D4C"},
				SubTitle:       &fePb.Text{Text: fmt.Sprintf("Salary credited on %s %d", salaryCreditMonth.String(), salaryCreditDay), FontColor: "#646464"}, // Lead
				BenefitsStatus: fePb.BenefitsStatus_BENEFITS_STATUS_ACTIVE,
				HeaderBgColor:  "#D9F2CC", // pastel mint
				VisualElement:  commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryprogram/green-circle-check.png"),
				BenefitsCards:  benefitsCards,
			}
		case isSalaryLiteProgramActive:
			_, salaryCreditMonth, salaryCreditDay := salaryProgramActiveSince.AsTime().Date()
			// grace period state for salary lite active user
			if time.Since(salaryProgramActiveSince.AsTime()) > s.conf.SalaryProgram.SalaryLiteConfig.MinReqDurationSinceLastActivationForShowingGracePeriod {
				benefitsInfo = &fePb.BenefitsInfo{
					Title:          &fePb.Text{Text: "Benefits active", FontColor: "#AC7C44"},
					SubTitle:       &fePb.Text{Text: fmt.Sprintf("Last transfer received on %s %d", salaryCreditMonth.String(), salaryCreditDay), FontColor: "#646464"}, // Lead
					BenefitsStatus: fePb.BenefitsStatus_BENEFITS_STATUS_ACTIVE,
					HeaderBgColor:  "#F4E7BF",
					VisualElement:  commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryprogram/warning-yellow.png"),
					BenefitsCards:  benefitsCards,
				}
			} else {
				benefitsInfo = &fePb.BenefitsInfo{
					Title:          &fePb.Text{Text: "Benefits active", FontColor: "#5D7D4C"},
					SubTitle:       &fePb.Text{Text: fmt.Sprintf("Last transfer received on %s %d", salaryCreditMonth.String(), salaryCreditDay), FontColor: "#646464"}, // Lead
					BenefitsStatus: fePb.BenefitsStatus_BENEFITS_STATUS_ACTIVE,
					HeaderBgColor:  "#D9F2CC", // pastel mint
					VisualElement:  commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryprogram/green-circle-check.png"),
					BenefitsCards:  benefitsCards,
				}
			}
		default:
			benefitsInfo = &fePb.BenefitsInfo{
				Title:          &fePb.Text{Text: "Benefits inactive", FontColor: "#A73F4B"}, // Tertiary Peach
				BenefitsStatus: fePb.BenefitsStatus_BENEFITS_STATUS_INACTIVE,
				HeaderBgColor:  "#ECEEF0",
				VisualElement:  commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryprogram/warning-red.png"),
				BenefitsCards:  benefitsCards,
			}
		}

		return nil
	})

	parallelErrGrp.Go(func() error {
		// note: keeping this in a goroutine for now, can move it out later since no rpc calls/ heavy computation required here
		// assuming we will have content related to benefits calculator only, since the deeplink is hardcoded for now
		// calculator is not shown to the salary lite users
		if s.dyconf.SalaryProgram().SalaryAccountBenefitsSectionTopBannerInfo().IsVisible() && !isSalaryLiteUser {
			topBannerInfo = &fePb.BenefitsInfo_Banner{
				Title: &fePb.Text{
					Text:      s.dyconf.SalaryProgram().SalaryAccountBenefitsSectionTopBannerInfo().Title(),
					FontColor: s.dyconf.SalaryProgram().SalaryAccountBenefitsSectionTopBannerInfo().TitleFontColor(),
				},
				BgColor:      s.dyconf.SalaryProgram().SalaryAccountBenefitsSectionTopBannerInfo().BgColor(),
				LeftIconUrl:  s.dyconf.SalaryProgram().SalaryAccountBenefitsSectionTopBannerInfo().LeftIconUrl(),
				RightIconUrl: s.dyconf.SalaryProgram().SalaryAccountBenefitsSectionTopBannerInfo().RightIconUrl(),
				Cta:          &fePb.CTA{Action: &fePb.CTA_DeeplinkAction{DeeplinkAction: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_SALARY_PROGRAM_BENEFITS_CALCULATOR_SCREEN}}},
			}

			// show information popup for app update if user's app version does not support benefits calculator
			if appPlatform == commontypes.Platform_IOS && appVersion < s.dyconf.SalaryProgram().MinIosAppVersionToSupportBenefitsCalculatorPage() ||
				appPlatform == commontypes.Platform_ANDROID && appVersion < s.dyconf.SalaryProgram().MinAndroidAppVersionToSupportBenefitsCalculatorPage() {
				topBannerInfo.Cta = &fePb.CTA{
					Action: &fePb.CTA_DeeplinkAction{
						DeeplinkAction: &deeplinkPb.Deeplink{
							Screen: deeplinkPb.Screen_INFORMATION_POPUP,
							ScreenOptions: &deeplinkPb.Deeplink_InformationPopupOptions{
								InformationPopupOptions: &deeplinkPb.InformationPopupOptions{
									TextTitle:    &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Get benefits upto ₹10000 per year"}, FontColor: "#333333"},
									TextSubTitle: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Upgrade your app version to know how much you can earn"}, FontColor: "#646464"},
									BgColor:      "#FFFFFF",
									Ctas: []*deeplinkPb.Cta{
										{
											Type: deeplinkPb.Cta_CUSTOM,
											Text: "Upgrade now",
											Deeplink: &deeplinkPb.Deeplink{
												Screen: deeplinkPb.Screen_UPDATE_APP_SCREEN,
											},
											DisplayTheme: deeplinkPb.Cta_PRIMARY,
											Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
										},
									},
								},
							},
						},
					},
				}
			}
		}

		return nil
	})

	if groupErr := parallelErrGrp.Wait(); groupErr != nil {
		logger.Error(ctx, "error while fetching data for benefits section info", zap.Error(groupErr))
		return nil, fmt.Errorf("error while fetching data for benefits section info")
	}

	if topBannerInfo != nil {
		benefitsInfo.TopBanner = topBannerInfo
	}

	title := &fePb.Text{Text: "Salary Benefits", FontColor: "#333333"}
	if isSalaryLiteUser && !isFullSalaryProgramActive {
		title = &fePb.Text{Text: "Salary Lite benefits", FontColor: "#333333"}
	}

	return &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection{
		Title: title, // Night
		Section: &fePb.SalaryAccountBenefitsLandingPageResponse_PageSection_BenefitsInfo{
			BenefitsInfo: benefitsInfo,
		},
	}, nil
}

// nolint: funlen
// getBenefitsCardsAndCashEquivalentForActor returns benefits cards and total cash equivalent corresponding to all benefits for given actor
func (s *Service) getBenefitsCardsAndCashEquivalentForActor(ctx context.Context, actorId string, isFullSalaryProgramActive, isSalaryLiteProgramActive, isSalaryLiteUser bool, benefitsCardsEntryPoint BenefitsCardsEntryPoint, appPlatform commontypes.Platform,
	appVersion uint32) ([]*fePb.BenefitsCardInfo, uint32, error) {
	var (
		rewardOfferTags []beRewardOffersPb.RewardOfferTag
	)
	if isSalaryLiteUser && !isFullSalaryProgramActive {
		rewardOfferTags = append(rewardOfferTags, beRewardOffersPb.RewardOfferTag_SALARY_LITE_ACCOUNT_OFFER)
	} else {
		rewardOfferTags = append(rewardOfferTags, beRewardOffersPb.RewardOfferTag_FULL_SALARY_ACCOUNT_OFFER)
	}

	rewardOffersRes, err := s.rewardOffersClient.GetRewardOffersForScreen(ctx, &beRewardOffersPb.GetRewardOffersForScreenRequest{
		ActorId: actorId,
		Filter: &beRewardOffersPb.GetRewardOffersForScreenRequest_Filter{
			OfferTypes: []beRewardsPb.RewardOfferType{beRewardsPb.RewardOfferType_SALARY_PROGRAM_OFFER},
			Tags:       rewardOfferTags,
		},
	})
	if err != nil || !rewardOffersRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching reward offers for salary program", zap.Error(err),
			zap.Any(logger.RPC_STATUS, rewardOffersRes.GetStatus()),
		)
		return nil, 0, fmt.Errorf("error fetching reward offers for salary prorgram")
	}
	rewardOffers := rewardOffersRes.GetRewardOffers()

	// sort the reward offers according to the display rank
	sort.Slice(rewardOffers, func(i, j int) bool {
		rewardOfferIDisplayRank, rewardOfferJDisplayRank := rewardOffers[i].GetDisplayMeta().GetDisplayRank(), rewardOffers[j].GetDisplayMeta().GetDisplayRank()
		if benefitsCardsEntryPoint == SalaryIntroPage {
			// 0 implies rank was not specified, using default display_rank in such a case
			if rewardOffers[i].GetDisplayMeta().GetDisplayRankOnSalaryIntroPage() != 0 {
				rewardOfferIDisplayRank = rewardOffers[i].GetDisplayMeta().GetDisplayRankOnSalaryIntroPage()
			}
			if rewardOffers[j].GetDisplayMeta().GetDisplayRankOnSalaryIntroPage() != 0 {
				rewardOfferJDisplayRank = rewardOffers[j].GetDisplayMeta().GetDisplayRankOnSalaryIntroPage()
			}
		}
		return rewardOfferIDisplayRank < rewardOfferJDisplayRank
	})

	// todo: move to a separate filtering method
	filteredRewardOffers := make([]*beRewardOffersPb.RewardOffer, 0, len(rewardOffers))
	totalCashEquivalent := uint32(0)

	for _, rewardOffer := range rewardOffers {
		if !shouldDisplayRewardOfferOnGivenAppVersion(rewardOffer, appPlatform, appVersion) {
			continue
		}
		totalCashEquivalent += rewardOffer.GetDisplayMeta().GetCashEquivalent()

		if benefitsCardsEntryPoint == SalaryIntroPage {
			// limiting the number of reward-offers to be shown on the intro page to 3.
			// Why? because the intro page was designed to support only 3 gracefully.
			// Sending > 3 in android creates tetris effect, and iOS doesn't render at all.
			if len(filteredRewardOffers) == 3 {
				// using continue instead of break here as these offers need not be displayed on intro page but still need to be accounted for in totalCashEquivalent calculation.
				continue
			}
			// skipping the listing of reward offer on salary intro page if the visibility is off
			if !rewardOffer.GetDisplayMeta().GetIsVisibleOnSalaryIntroPage() {
				continue
			}
		}

		filteredRewardOffers = append(filteredRewardOffers, rewardOffer)
	}

	var benefitsCards []*fePb.BenefitsCardInfo
	for i, rewardOffer := range filteredRewardOffers {
		benefitCard, err := s.getFeBenefitCardFromBeRewardOffer(ctx, rewardOffer, isFullSalaryProgramActive || isSalaryLiteProgramActive, benefitsCardsEntryPoint, i == 0)
		if err != nil {
			logger.Error(ctx, "error converting reward offer to benefit card", zap.Error(err), zap.String(logger.REWARD_OFFER_ID, rewardOffer.GetId()))
			continue
		}

		// updating the benefit info based on user state
		shouldDisplayTheBenefit, updateErr := s.updateFeBenefitCardBasedOnUserState(ctx, benefitCard, rewardOffer, actorId, isFullSalaryProgramActive, benefitsCardsEntryPoint)
		switch {
		case updateErr != nil:
			// intentionally muting the error to prevent the complete benefits section from failing due to issue in a single benefit.
			logger.Error(ctx, "error updating benefit card based on user state", zap.String(logger.REWARD_OFFER_ID, rewardOffer.GetId()), zap.Error(updateErr))
			continue
		case !shouldDisplayTheBenefit:
			// remove the cash equivalent value for this offer from the totalCashEquivalent
			totalCashEquivalent -= rewardOffer.GetDisplayMeta().GetCashEquivalent()
			logger.Debug(ctx, "given benefit should not be displayed to the user", zap.String(logger.REWARD_OFFER_ID, rewardOffer.GetId()))
			continue
		}

		benefitsCards = append(benefitsCards, benefitCard)
	}

	return benefitsCards, totalCashEquivalent, nil
}

func (s *Service) updateFeBenefitCardBasedOnUserState(ctx context.Context, benefitCard *fePb.BenefitsCardInfo, rewardOffer *beRewardOffersPb.RewardOffer, actorId string, isFullSalaryProgramActive bool, benefitCardEntryPoint BenefitsCardsEntryPoint) (shouldDisplayBenefit bool, updateErr error) {
	// update salary benefit card based on user state
	salaryProgramHealthInsurance := s.dyconf.SalaryProgram().HealthInsuranceRewardOfferIdToPolicyConfigMap().Get(rewardOffer.GetId())
	if salaryProgramHealthInsurance != nil {
		shouldDisplayBenefit, updateErr := s.updateHealthInsuranceFeBenefitCardBasedOnUserState(ctx, benefitCard, rewardOffer, actorId, isFullSalaryProgramActive, benefitCardEntryPoint, salaryProgramHealthInsurance.HealthInsurancePolicyType())
		if updateErr != nil {
			return false, fmt.Errorf("error updating health-insurance benefit card, err : %w", updateErr)
		}
		return shouldDisplayBenefit, nil
	} else if s.dyconf.SalaryProgram().EarlySalaryBenefitConfig().EarlySalaryBenefitRewardOfferId() == rewardOffer.GetId() { // update early salary benefit card based on user state
		shouldDisplayBenefit, updateErr := s.updateEarlySalaryFeBenefitCardBasedOnUserState(ctx, benefitCard, actorId, isFullSalaryProgramActive)
		if updateErr != nil {
			return false, fmt.Errorf("error updating early salary benefit card, err : %w", updateErr)
		}
		return shouldDisplayBenefit, nil
	}
	return true, nil
}

// updateHealthInsuranceFeBenefitCardBasedOnUserState updates some properties like cta, display info etc of healthinsurance benefitCard based on the user state.
// nolint: funlen
func (s *Service) updateHealthInsuranceFeBenefitCardBasedOnUserState(ctx context.Context, benefitCard *fePb.BenefitsCardInfo, rewardOffer *beRewardOffersPb.RewardOffer, actorId string, isFullSalaryProgramActive bool, benefitCardEntryPoint BenefitsCardsEntryPoint,
	healthInsuranceType string) (shouldDisplayBenefit bool,
	updateErr error) {
	if benefitCard == nil {
		return false, nil
	}
	empInfo, empErr := s.getCurrentEmployerOfActor(ctx, actorId)
	if empErr != nil {
		logger.Debug(ctx, "error fetching employer info by actorId", zap.Error(empErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return false, fmt.Errorf("error fetching employer info by actorId: %v, err : %w", actorId, empErr)
	}

	// resetting these values at these need be updated based here based on user level checks.
	benefitCard.Info = nil
	benefitCard.GetDisplayInfo().Tags = nil

	// check if insurance benefit is rolled out for the user
	isBenefitEnabledForUser, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_SALARY_PROGRAM_HEALTH_INSURANCE_BENEFIT).WithActorId(actorId))
	if err != nil {
		return false, fmt.Errorf("error checking if healthinsurance feature is enabled for the user, err : %w", err)
	}

	// if feature is not enabled for the user then show a coming soon info on the benefit card.
	if !isBenefitEnabledForUser {
		// todo (utkarsh) : add coming soon tag
		benefitCard.Info = &fePb.BenefitsCardInfo_InactiveInfo_{
			InactiveInfo: &fePb.BenefitsCardInfo_InactiveInfo{
				Title: &fePb.Text{Text: "Coming Soon", FontColor: "#333333"}, // Night
				Desc:  &fePb.Text{Text: "This benefit will be available soon. Stay tuned!", FontColor: "#000000"},
			},
		}
		return true, nil
	}

	var (
		issuedPolicies                     []*healthinsurancePb.HealthInsurancePolicyDetails
		isSomePolicyIssuanceInProgress     bool
		hasLastPolicyIssuanceRequestFailed bool
	)

	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)

	errGrp, grpCtx := errgroup.WithContext(ctx)
	errGrp.Go(func() error {
		// get issued policies for actor
		issuedPoliciesRes, getIssuedPoliciesErr := s.healthInsuranceClient.GetIssuedPoliciesForActor(grpCtx, &healthinsurancePb.GetIssuedPoliciesForActorRequest{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(issuedPoliciesRes, getIssuedPoliciesErr); rpcErr != nil {
			return fmt.Errorf("healthInsuranceClient.GetIssuedPoliciesForActor rpc call failed, err : %w", rpcErr)
		}
		issuedPolicies = issuedPoliciesRes.GetPolicies()

		return nil
	})

	errGrp.Go(func() error {
		// get inprogress policy issuance requests for actor
		inProgressPolicyIssuanceRequestsRes, getIssuanceReqsErr := s.healthInsuranceClient.GetPolicyIssuanceRequestsForActor(grpCtx, &healthinsurancePb.GetPolicyIssuanceRequestsForActorRequest{
			ActorId: actorId,
			Filters: &healthinsurancePb.GetPolicyIssuanceRequestsForActorRequest_Filters{
				IssuanceReqStatuses: []healthinsurancePb.PolicyIssuanceRequestStatus{healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_VENDOR_PURCHASE_IN_PROGRESS},
			},
			PageContext: &rpc.PageContextRequest{PageSize: 1},
		})
		if rpcErr := epifigrpc.RPCError(inProgressPolicyIssuanceRequestsRes, getIssuanceReqsErr); rpcErr != nil {
			return fmt.Errorf("healthInsuranceClient.GetPolicyIssuanceRequestsForActor rpc call failed, err : %w", rpcErr)
		}
		isSomePolicyIssuanceInProgress = len(inProgressPolicyIssuanceRequestsRes.GetPolicyIssuanceRequests()) > 0

		return nil
	})

	errGrp.Go(func() error {
		// get latest policy issuance request for actor
		latestPolicyIssuanceRequestRes, getLatestIssuanceReqErr := s.healthInsuranceClient.GetPolicyIssuanceRequestsForActor(grpCtx, &healthinsurancePb.GetPolicyIssuanceRequestsForActorRequest{
			ActorId:     actorId,
			PageContext: &rpc.PageContextRequest{PageSize: 1},
			SortOrder:   healthinsurancePb.SortOrder_DESC,
		})
		if rpcErr := epifigrpc.RPCError(latestPolicyIssuanceRequestRes, getLatestIssuanceReqErr); rpcErr != nil {
			return fmt.Errorf("healthInsuranceClient.GetPolicyIssuanceRequestsForActor rpc call failed, err : %w", rpcErr)
		}
		policyIssuanceReqs := latestPolicyIssuanceRequestRes.GetPolicyIssuanceRequests()

		// check if the last request had failed
		if len(policyIssuanceReqs) > 0 && policyIssuanceReqs[0].GetRequestStatus() == healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_VENDOR_PURCHASE_FAILED {
			hasLastPolicyIssuanceRequestFailed = true
		}

		return nil
	})

	if err = errGrp.Wait(); err != nil {
		return false, fmt.Errorf("error fetching policy details for actor, err : %w", err)
	}

	// check if an active policy exists
	isSomePolicyActive := false
	for _, policy := range issuedPolicies {
		if policy.GetPolicyActiveFrom().AsTime().Before(time.Now()) && (policy.GetPolicyActiveTill() == nil || policy.GetPolicyActiveTill().AsTime().After(time.Now())) {
			isSomePolicyActive = true
		}
	}
	var healthInsuranceCtaText string
	if s.dyconf.SalaryProgram().HealthInsuranceRewardOfferIdToPolicyConfigMap().Get(rewardOffer.GetId()).HealthInsurancePolicyType() == healthinsurancePb.HealthInsurancePolicyType_SUPER_TOP_UP_INSURANCE.String() {
		healthInsuranceCtaText = "Get free top-up"
	} else {
		healthInsuranceCtaText = "Get policy"
	}
	var insuranceCardCta *fePb.CTA

	if isFullSalaryProgramActive {
		// 'ACTIVE' tag needs to be shown only on salary landing page
		if benefitCardEntryPoint == SalaryLandingPage {
			// active/inactive tags should NOT be displayed on benefits section v1
			if appPlatform == commontypes.Platform_ANDROID && appVersion < int(s.dyconf.SalaryProgram().MinAndroidAppVersionSupportingBenefitsSectionV1()) ||
				appPlatform == commontypes.Platform_IOS && appVersion < int(s.dyconf.SalaryProgram().MinIosAppVersionSupportingBenefitsSectionV1()) {
				s.prefixTagInCardDisplayInfoTags(benefitCard.GetDisplayInfo(), "ACTIVE", "#5D7D4C", "#D9F2CC") // T19, Pastel Mint
			}
		}

		switch {
		case isSomePolicyActive:
			insuranceCardCta = &fePb.CTA{
				IsVisible: true,
				Text:      &fePb.Text{Text: "View policy", FontColor: "#FFFFFF"},
				BgColor:   "#00B899",
				Action:    &fePb.CTA_CustomAction{CustomAction: &fePb.CustomAction{ActionApi: fePb.CustomActionApi_GET_HEALTH_INSURANCE_ISSUED_POLICIES_REDIRECTION_INFO}},
			}

		case isSomePolicyIssuanceInProgress:
			insuranceCardCta = &fePb.CTA{
				IsVisible: true,
				Text:      &fePb.Text{Text: healthInsuranceCtaText, FontColor: "#FFFFFF"},
				BgColor:   "#00B899",
				Action: &fePb.CTA_CustomAction{CustomAction: &fePb.CustomAction{
					Action: fePb.CustomAction_OPEN_BOTTOM_SHEET_DIALOG,
					ActionData: &fePb.CustomAction_OpenBottomSheetDialog{
						OpenBottomSheetDialog: &fePb.OpenBottomSheetDialogActionData{
							IconUrl:  "https://epifi-icons.pointz.in/salaryprogram/cloud-thunder.png",
							Title:    &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Your policy is currently under processing"}, FontColor: "#333333"},
							Subtitle: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Please check back later to view your policy details"}, FontColor: "#646464"},
						},
					},
				}},
			}

		// nolint: dupl
		case hasLastPolicyIssuanceRequestFailed:
			createNewPolicyCta := &fePb.CTA{
				IsVisible: true,
				Text:      &fePb.Text{Text: "Create new policy", FontColor: "#FFFFFF"},
				BgColor:   "#00B899",
				Action:    &fePb.CTA_CustomAction{CustomAction: &fePb.CustomAction{ActionApi: fePb.CustomActionApi_GET_HEALTH_INSURANCE_POLICY_PURCHASE_REDIRECTION_INFO}},
			}

			insuranceCardCta = &fePb.CTA{
				IsVisible: true,
				Text:      &fePb.Text{Text: healthInsuranceCtaText, FontColor: "#FFFFFF"},
				BgColor:   "#00B899",
				Action: &fePb.CTA_CustomAction{CustomAction: &fePb.CustomAction{
					Action: fePb.CustomAction_OPEN_BOTTOM_SHEET_DIALOG,
					ActionData: &fePb.CustomAction_OpenBottomSheetDialog{
						OpenBottomSheetDialog: &fePb.OpenBottomSheetDialogActionData{
							IconUrl:  "https://epifi-icons.pointz.in/salaryprogram/cloud-thunder.png",
							Title:    &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Something went wrong"}, FontColor: "#333333"},
							Subtitle: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "We were unable to create a policy for you. Please try again"}, FontColor: "#646464"},
							Ctas:     []*fePb.CTA{createNewPolicyCta},
						},
					},
				}},
			}

		default:
			if len(issuedPolicies) == 0 {
				insuranceCardCta = &fePb.CTA{
					IsVisible: true,
					Text:      &fePb.Text{Text: healthInsuranceCtaText, FontColor: "#FFFFFF"},
					BgColor:   "#00B899",
					Action:    &fePb.CTA_CustomAction{CustomAction: &fePb.CustomAction{ActionApi: fePb.CustomActionApi_GET_HEALTH_INSURANCE_POLICY_PURCHASE_REDIRECTION_INFO}},
				}
			} else {
				viewOldPolicyCta := &fePb.CTA{
					IsVisible: true,
					Text:      &fePb.Text{Text: "View old policy", FontColor: "#00B899"},
					BgColor:   "#F7F9FA",
					Action:    &fePb.CTA_CustomAction{CustomAction: &fePb.CustomAction{ActionApi: fePb.CustomActionApi_GET_HEALTH_INSURANCE_ISSUED_POLICIES_REDIRECTION_INFO}},
				}
				createNewPolicyCta := &fePb.CTA{
					IsVisible: true,
					Text:      &fePb.Text{Text: "Create new policy", FontColor: "#FFFFFF"},
					BgColor:   "#00B899",
					Action:    &fePb.CTA_CustomAction{CustomAction: &fePb.CustomAction{ActionApi: fePb.CustomActionApi_GET_HEALTH_INSURANCE_POLICY_PURCHASE_REDIRECTION_INFO}},
				}

				insuranceCardCta = &fePb.CTA{
					IsVisible: true,
					Text:      &fePb.Text{Text: "View policy", FontColor: "#FFFFFF"},
					BgColor:   "#00B899",
					Action: &fePb.CTA_CustomAction{
						CustomAction: &fePb.CustomAction{
							Action: fePb.CustomAction_OPEN_BOTTOM_SHEET_DIALOG,
							ActionData: &fePb.CustomAction_OpenBottomSheetDialog{
								OpenBottomSheetDialog: &fePb.OpenBottomSheetDialogActionData{
									IconUrl:  "https://epifi-icons.pointz.in/salaryprogram/cloud-thunder.png",
									Title:    &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Your policy is currently not active"}, FontColor: "#333333"},
									Subtitle: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "If you had an old policy, it has lapsed. You can get a new one for free"}, FontColor: "#646464"},
									Ctas:     []*fePb.CTA{viewOldPolicyCta, createNewPolicyCta},
								},
							},
						}},
				}
			}
		}
	} else {
		// 'INACTIVE' tag needs to be shown only on salary landing page
		if benefitCardEntryPoint == SalaryLandingPage {
			// active/inactive tags need not displayed on benefits section v1
			if appPlatform == commontypes.Platform_ANDROID && appVersion < int(s.dyconf.SalaryProgram().MinAndroidAppVersionSupportingBenefitsSectionV1()) ||
				appPlatform == commontypes.Platform_IOS && appVersion < int(s.dyconf.SalaryProgram().MinIosAppVersionSupportingBenefitsSectionV1()) {
				s.prefixTagInCardDisplayInfoTags(benefitCard.GetDisplayInfo(), "INACTIVE", "#A73F4B", "#FAD0D0") // T6, Pastel Peach
			}
		}
		switch {
		// nolint: dupl
		case isSomePolicyActive:
			viewPolicyCta := &fePb.CTA{
				IsVisible: true,
				Text:      &fePb.Text{Text: "View policy", FontColor: "#FFFFFF"},
				BgColor:   "#00B899",
				Action:    &fePb.CTA_CustomAction{CustomAction: &fePb.CustomAction{ActionApi: fePb.CustomActionApi_GET_HEALTH_INSURANCE_ISSUED_POLICIES_REDIRECTION_INFO}},
			}
			// nolint: dupl
			insuranceCardCta = &fePb.CTA{
				IsVisible: true,
				Text:      &fePb.Text{Text: "View policy", FontColor: "#FFFFFF"},
				BgColor:   "#00B899",
				Action: &fePb.CTA_CustomAction{
					CustomAction: &fePb.CustomAction{
						Action: fePb.CustomAction_OPEN_BOTTOM_SHEET_DIALOG,
						ActionData: &fePb.CustomAction_OpenBottomSheetDialog{
							OpenBottomSheetDialog: &fePb.OpenBottomSheetDialogActionData{
								IconUrl:  "https://epifi-icons.pointz.in/salaryprogram/cloud-thunder.png",
								Title:    &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Your policy might lapse soon"}, FontColor: "#333333"},
								Subtitle: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Since your salary benefits are no longer active, your health insurance will lapse soon"}, FontColor: "#646464"},
								Ctas:     []*fePb.CTA{viewPolicyCta},
							},
						},
					}},
			}

		case len(issuedPolicies) > 0:
			viewOldPolicyCta := &fePb.CTA{
				IsVisible: true,
				Text:      &fePb.Text{Text: "View old policy", FontColor: "#FFFFFF"},
				BgColor:   "#00B899",
				Action:    &fePb.CTA_CustomAction{CustomAction: &fePb.CustomAction{ActionApi: fePb.CustomActionApi_GET_HEALTH_INSURANCE_ISSUED_POLICIES_REDIRECTION_INFO}},
			}
			// nolint: dupl
			insuranceCardCta = &fePb.CTA{
				IsVisible: true,
				Text:      &fePb.Text{Text: "View policy", FontColor: "#FFFFFF"},
				BgColor:   "#00B899",
				Action: &fePb.CTA_CustomAction{
					CustomAction: &fePb.CustomAction{
						Action: fePb.CustomAction_OPEN_BOTTOM_SHEET_DIALOG,
						ActionData: &fePb.CustomAction_OpenBottomSheetDialog{
							OpenBottomSheetDialog: &fePb.OpenBottomSheetDialogActionData{
								IconUrl:  "https://epifi-icons.pointz.in/salaryprogram/cloud-thunder.png",
								Title:    &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Policy deactivated due to inactive salary benefits"}, FontColor: "#333333"},
								Subtitle: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "To activate your benefits, make sure your salary arrives in your Federal account"}, FontColor: "#646464"},
								Ctas:     []*fePb.CTA{viewOldPolicyCta},
							},
						},
					}},
			}
		default:
			insuranceCardCta = &fePb.CTA{
				IsVisible: true,
				Text:      &fePb.Text{Text: healthInsuranceCtaText, FontColor: "#FFFFFF"},
				BgColor:   "#00B899",
				Action: &fePb.CTA_CustomAction{
					CustomAction: &fePb.CustomAction{
						Action: fePb.CustomAction_OPEN_BOTTOM_SHEET_DIALOG,
						ActionData: &fePb.CustomAction_OpenBottomSheetDialog{
							OpenBottomSheetDialog: &fePb.OpenBottomSheetDialogActionData{
								Title:    &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Benefit inactive"}, FontColor: "#333333"},
								Subtitle: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "All benefits get activated once your salary arrives in your Federal account"}, FontColor: "#000000"},
							},
						},
					}},
			}
		}
	}

	// todo (utkarsh) : evaluate if we can have central mappers to avoid code duplication and corresponding maintenance
	var feStepsInfoV1, feTncsInfoV1 []*fePb.BenefitsCardInfo_ExpandedInfo_DynamicInfo_InfoPoint
	for _, beStepV1 := range rewardOffer.GetDisplayMeta().GetStepsV1() {
		feStepsInfoV1 = append(feStepsInfoV1, &fePb.BenefitsCardInfo_ExpandedInfo_DynamicInfo_InfoPoint{
			Text:     beStepV1.GetText(),
			Deeplink: beStepV1.GetDeeplink(),
		})
	}

	for _, beTncV1 := range rewardOffer.GetDisplayMeta().GetTncsV1() {
		feTncsInfoV1 = append(feTncsInfoV1, &fePb.BenefitsCardInfo_ExpandedInfo_DynamicInfo_InfoPoint{
			Text:     beTncV1.GetText(),
			Deeplink: beTncV1.GetDeeplink(),
		})
	}

	// fallback to the populate V1 info with the older steps and tncs fields
	// todo (utkarsh) : remove this logic once we start configuring v1 fields in offer
	if len(feStepsInfoV1) == 0 {
		for _, stepInfo := range rewardOffer.GetDisplayMeta().GetSteps() {
			feStepsInfoV1 = append(feStepsInfoV1, &fePb.BenefitsCardInfo_ExpandedInfo_DynamicInfo_InfoPoint{
				Text: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: stepInfo}},
			})
		}
	}

	// add policy faq doc link in steps
	policyFAQsDocUrl, err := s.getHealthInsurancePolicyFAQDocUrl(ctx, rewardOffer.GetId())
	if err != nil {
		// intentionally muting the error as this error shouldn't fail the entire flow
		logger.WarnWithCtx(ctx, "error fetching healthinsurance policy faqs doc url", zap.Error(err))
	} else {
		feStepsInfoV1 = append(feStepsInfoV1, &fePb.BenefitsCardInfo_ExpandedInfo_DynamicInfo_InfoPoint{
			Text: &commontypes.Text{DisplayValue: &commontypes.Text_Html{Html: "For additional questions on the policy refer to <font color='#00B899'>Policy related FAQs</font>"}},
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_EXTERNAL_REDIRECTION,
				ScreenOptions: &deeplinkPb.Deeplink_ExternalRedirectionScreenOptions{
					ExternalRedirectionScreenOptions: &deeplinkPb.ExternalRedirectionScreenOptions{ExternalUrl: policyFAQsDocUrl},
				},
			},
		})
	}

	// add policy claim process doc link in steps
	policyClaimProcessDocUrl, err := s.getHealthInsurancePolicyClaimProcessDocUrl(ctx, rewardOffer.GetId())
	if err != nil {
		// intentionally muting the error as this error shouldn't fail the entire flow
		logger.WarnWithCtx(ctx, "error fetching healthinsurance policy claim process doc url", zap.Error(err))
	} else {
		feStepsInfoV1 = append(feStepsInfoV1, &fePb.BenefitsCardInfo_ExpandedInfo_DynamicInfo_InfoPoint{
			Text: &commontypes.Text{DisplayValue: &commontypes.Text_Html{Html: "For details on the claims process, please refer to <font color='#00B899'>Claims process details</font>"}},
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_EXTERNAL_REDIRECTION,
				ScreenOptions: &deeplinkPb.Deeplink_ExternalRedirectionScreenOptions{
					ExternalRedirectionScreenOptions: &deeplinkPb.ExternalRedirectionScreenOptions{ExternalUrl: policyClaimProcessDocUrl},
				},
			},
		})
	}

	cashlessHospitalListDocUrl, err := s.getHealthInsuranceCashlessHospitalListDocUrl(ctx, rewardOffer.GetId())
	if err != nil {
		// intentionally muting the error as this error shouldn't fail the entire flow
		logger.WarnWithCtx(ctx, "error fetching healthinsurance cashless hospital list doc url", zap.Error(err))
	} else {
		feStepsInfoV1 = append(feStepsInfoV1, &fePb.BenefitsCardInfo_ExpandedInfo_DynamicInfo_InfoPoint{
			Text: &commontypes.Text{DisplayValue: &commontypes.Text_Html{Html: "For cashless network hospital list <font color='#00B899'>see here</font>"}},
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_EXTERNAL_REDIRECTION,
				ScreenOptions: &deeplinkPb.Deeplink_ExternalRedirectionScreenOptions{
					ExternalRedirectionScreenOptions: &deeplinkPb.ExternalRedirectionScreenOptions{ExternalUrl: cashlessHospitalListDocUrl},
				},
			},
		})
	}

	if len(feTncsInfoV1) == 0 {
		for _, tncInfo := range rewardOffer.GetDisplayMeta().GetTncs() {
			feTncsInfoV1 = append(feTncsInfoV1, &fePb.BenefitsCardInfo_ExpandedInfo_DynamicInfo_InfoPoint{
				Text: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: tncInfo}},
			})
		}
	}

	tncsDocUrl, err := s.getHealthInsuranceTncsDocUrl(ctx, rewardOffer.GetId())
	if err != nil {
		// intentionally muting the error as this error shouldn't fail the entire flow
		logger.WarnWithCtx(ctx, "error fetching healthinsurance tncs doc url", zap.Error(err))
	} else {
		feTncsInfoV1 = append(feTncsInfoV1, &fePb.BenefitsCardInfo_ExpandedInfo_DynamicInfo_InfoPoint{
			Text: &commontypes.Text{DisplayValue: &commontypes.Text_Html{Html: "For insurance T&Cs as provided by Manipal Cigna Insurance Company, please <font color='#00B899'>see here</font>"}},
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_EXTERNAL_REDIRECTION,
				ScreenOptions: &deeplinkPb.Deeplink_ExternalRedirectionScreenOptions{
					ExternalRedirectionScreenOptions: &deeplinkPb.ExternalRedirectionScreenOptions{ExternalUrl: tncsDocUrl},
				},
			},
		})
	}
	if empInfo.GetSalaryProgramChannel() == fePb.EmployerSalaryProgramChannel_B2C && !(isSomePolicyActive || isSomePolicyIssuanceInProgress || hasLastPolicyIssuanceRequestFailed) {
		return false, nil
	}

	if empInfo.GetSalaryProgramChannel() == fePb.EmployerSalaryProgramChannel_B2B && appPlatform == commontypes.Platform_ANDROID &&
		lo.Contains([]string{healthinsurancePb.HealthInsurancePolicyType_BASE_HEALTH_INSURANCE.String(),
			healthinsurancePb.HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_2A.String(),
			healthinsurancePb.HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_2A2C.String(),
			healthinsurancePb.HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_1A.String()}, healthInsuranceType) &&
		!apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dyconf.SalaryProgram().HealthInsuranceOnsurityPolicyFlowsConfig()) {
		benefitCard.Info = &fePb.BenefitsCardInfo_DeeplinkInfo{
			DeeplinkInfo: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_UPDATE_APP_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_UpdateAppScreenOptions_{
					UpdateAppScreenOptions: &deeplinkPb.Deeplink_UpdateAppScreenOptions{
						UpdateType: deeplinkPb.Deeplink_UpdateAppScreenOptions_UPDATE_TYPE_IMMEDIATE,
					},
				},
			},
		}
	} else {
		benefitCard.Info = &fePb.BenefitsCardInfo_ExpandedInfo_{
			ExpandedInfo: &fePb.BenefitsCardInfo_ExpandedInfo{
				Title:    &fePb.Text{Text: rewardOffer.GetDisplayMeta().GetTitle(), FontColor: "#FFFFFF"}, // Snow
				ImageUrl: rewardOffer.GetDisplayMeta().GetIcon(),
				Infos: []*fePb.BenefitsCardInfo_ExpandedInfo_DynamicInfo{
					{
						Title:    &fePb.Text{Text: "How it works", FontColor: "#333333"}, // Night
						Points:   rewardOffer.GetDisplayMeta().GetSteps(),
						PointsV1: feStepsInfoV1,
					},
					{
						Title:    &fePb.Text{Text: "Terms & Conditions", FontColor: "#333333"}, // Night
						Points:   rewardOffer.GetDisplayMeta().GetTncs(),
						PointsV1: feTncsInfoV1,
					},
				},
				Cta: insuranceCardCta,
			},
		}
	}

	return true, nil
}

// updateEarlySalaryFeBenefitCardBasedOnUserState updates some properties like display text, deeplink etc of early salary benefitCard based on the user state.
func (s *Service) updateEarlySalaryFeBenefitCardBasedOnUserState(ctx context.Context, benefitCard *fePb.BenefitsCardInfo, actorId string, isFullSalaryProgramActive bool) (shouldDisplayBenefit bool,
	updateErr error) {
	if benefitCard == nil {
		return false, nil
	}

	// check if early salary is rolled out for the user
	isBenefitEnabledForUser, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_LOANS_EARLY_SALARY).WithActorId(actorId))
	if err != nil {
		return false, fmt.Errorf("error checking if early salary feature is enabled for the user, err : %w", err)
	}
	if !isBenefitEnabledForUser {
		return false, nil
	}

	isPostLoanV2, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_POST_LOAN_DISBURSAL_SCREENS).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed in post loan disbursal release evaluator", zap.Error(err))
		return false, nil
	}
	appVersionConstraintData := release.NewAppVersionConstraintData(s.dyconf.Lending().PreApprovedLoan().PostDisbursalV2FlowAppVersionConstraintConfig().AppVersionConstraintConfig())
	isAppVersionGreater, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
	if appVerErr != nil {
		logger.Error(ctx, "failed in post loan disbursal app version evaluator", zap.Error(err))
		return false, nil
	}
	platform, _ := epificontext.AppPlatformAndVersion(ctx)
	if platform == commontypes.Platform_ANDROID && isPostLoanV2 && isAppVersionGreater && (s.dyconf.Lending().PreApprovedLoan().PostDisbursalV2FlowAppVersionConstraintConfig().SkipVendorLoanProgramCheck() ||
		s.dyconf.Lending().PreApprovedLoan().PostDisbursalV2FlowAppVersionConstraintConfig().VendorLoanProgramMap().Get(strings.Join([]string{
			"LIQUILOANS",
			"LOAN_PROGRAM_EARLY_SALARY",
		}, ":"))) {
		// tapping on early salary benefit card should redirect the user to early salary landing screen
		benefitCard.Info = &fePb.BenefitsCardInfo_DeeplinkInfo{
			DeeplinkInfo: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
					PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
						LoanHeader: &palEnumFePb.LoanHeader{LoanProgram: palEnumFePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY},
					},
				},
			},
		}
	} else {
		// tapping on early salary benefit card should redirect the user to early salary landing screen
		benefitCard.Info = &fePb.BenefitsCardInfo_DeeplinkInfo{
			DeeplinkInfo: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_EARLY_SALARY_LANDING_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
					PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
						LoanHeader: &palEnumFePb.LoanHeader{LoanProgram: palEnumFePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY},
					},
				},
			},
		}
	}

	var bottomBanner *fePb.CardDisplayInfo_Banner
	if isFullSalaryProgramActive {
		earlySalaryDetailsRes, err := s.preApprovedLoanClient.GetEarlySalaryDetails(ctx, &palPb.GetEarlySalaryDetailsRequest{
			ActorId:    actorId,
			LoanHeader: &palPb.LoanHeader{LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY},
		})
		if rpcErr := epifigrpc.RPCError(earlySalaryDetailsRes, err); rpcErr != nil {
			// muting this error as this api is only used to show a banner on the early salary benefit tile and we do not want
			// to fail the flow if there is failure while fetching this info.
			logger.Error(ctx, "preApprovedLoanClient.GetEarlySalaryDetails rpc call failed", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		}
		if earlySalaryDetailsRes.GetStatus().IsSuccess() && earlySalaryDetailsRes.GetBannerDetails().GetBottomMsg() != "" {
			bottomBanner = &fePb.CardDisplayInfo_Banner{
				Title:   &commontypes.Text{FontColor: "#FFFFFF", DisplayValue: &commontypes.Text_PlainString{PlainString: earlySalaryDetailsRes.GetBannerDetails().GetBottomMsg()}},
				BgColor: "#99282828",
			}
		}
	}
	benefitCard.DisplayInfo.BottomBanner = bottomBanner

	// todo (utkarsh) : add handling for enach salary active user to display a separate text on bottom banner/not display the benefit
	return true, nil
}

// todo (yuvraj): add default fallback handling on rpc failure to defer block
func (s *Service) GetSalaryProgramLandingScreenRedirectionInfo(ctx context.Context, req *fePb.GetSalaryProgramLandingScreenRedirectionInfoRequest) (*fePb.GetSalaryProgramLandingScreenRedirectionInfoResponse, error) {
	var (
		actorId                 = req.GetReq().GetAuth().GetActorId()
		salaryIntroPageDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_SALARY_PROGRAM_INTRO_SCREEN,
		}
	)

	registrationStatusRes, err := s.salaryProgramClient.GetCurrentRegStatusAndNextRegStage(ctx, &beSalaryPb.CurrentRegStatusAndNextRegStageRequest{
		ActorId:  actorId,
		FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if grpcErr := epifigrpc.RPCError(registrationStatusRes, err); grpcErr != nil {
		logger.Error(ctx, "error while fetching salary registration status for actor", zap.Error(grpcErr))
		// returning SALARY_PROGRAM_INTRO_SCREEN as fallback screen in case GetCurrentRegStatusAndNextRegStage call fails.
		return &fePb.GetSalaryProgramLandingScreenRedirectionInfoResponse{
			Deeplink: salaryIntroPageDeeplink,
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusOk(),
			},
		}, nil
	}

	// create registration if entry point is Tier Salary Plan Screen
	if req.GetEntryPoint() == fePb.SalaryProgramLandingRedirectionScreenEntryPoint_TIER_SALARY_PLAN_SCREEN &&
		registrationStatusRes.GetRegistrationStatus() == beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_NOT_INITIATED {
		// create registration
		newRegistrationRes, regErr := s.salaryProgramClient.CreateRegistration(ctx, &beSalaryPb.CreateRegistrationRequest{
			ActorId:              actorId,
			RegistrationFlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
			AccountType:          beSalaryPb.SalaryProgramRegistrationAccountType_SALARY_PROGRAM_REGISTRATION_ACCOUNT_TYPE_FI_FED_SAVINGS_ACC,
		})
		if grpcErr := epifigrpc.RPCError(newRegistrationRes, regErr); grpcErr != nil {
			logger.Error(ctx, "error creating salary program registration", zap.Error(grpcErr))
			// not returning error on rpc call failure and fallback to SALARY_PROGRAM_INTRO_SCREEN
		}

		// fetching the newly generated registration to get the next-stage to be completed
		registrationStatusRes, err = s.salaryProgramClient.GetCurrentRegStatusAndNextRegStage(ctx, &beSalaryPb.CurrentRegStatusAndNextRegStageRequest{
			ActorId:  actorId,
			FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
		})
		if grpcErr := epifigrpc.RPCError(registrationStatusRes, err); grpcErr != nil {
			logger.Error(ctx, "error fetching current salary reg status and next reg stage", zap.Error(grpcErr))
			// not returning error on rpc call failure and fallback to SALARY_PROGRAM_INTRO_SCREEN
		}
	}

	// deeplink where the user should be redirected to
	var redirectionDeeplink *deeplinkPb.Deeplink
	switch {
	case registrationStatusRes.GetRegistrationStatus() == beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED:
		redirectionDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN,
		}
	case registrationStatusRes.GetRegistrationStatus() == beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_INITIATED &&
		registrationStatusRes.GetNextStage() == beSalaryPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_EMPLOYMENT_CONFIRMATION:
		redirectionDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_SALARY_PROGRAM_EMPLOYER_CONFIRMATION_SCREEN,
		}
	case registrationStatusRes.GetRegistrationStatus() == beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_INITIATED &&
		registrationStatusRes.GetNextStage() == beSalaryPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_FULL_KYC_COMPLETION:
		vkycDeeplink, deeplinkErr := vkycPkg.BuildVKYCStatusDeeplink(&vkycPkg.StatusScreenOptions{
			EntryPoint:           vkycBe.EntryPoint_ENTRY_POINT_SALARY_PROGRAM,
			EntryPointDeprecated: deeplinkPb.EntryPoint_ENTRY_POINT_SALARY_PROGRAM,
		})
		if deeplinkErr != nil {
			logger.Error(ctx, "error while building vkyc status deeplink", zap.Error(deeplinkErr))
			// sending salary intro page deeplink as deeplink in case of failur
			redirectionDeeplink = salaryIntroPageDeeplink
		} else {
			redirectionDeeplink = vkycDeeplink
		}
	default:
		redirectionDeeplink = salaryIntroPageDeeplink
	}

	return &fePb.GetSalaryProgramLandingScreenRedirectionInfoResponse{
		Deeplink: redirectionDeeplink,
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}, nil
}

// GetSalaryProgramBenefitCardInfo returns benefit card info for a given benefit.
func (s *Service) GetSalaryProgramBenefitCardInfo(ctx context.Context, req *fePb.SalaryProgramBenefitCardInfoRequest) (*fePb.SalaryProgramBenefitCardInfoResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()

	// get rewardOffer using benefitId
	rewardOffersRes, err := s.rewardOffersClient.GetRewardOffersForScreen(ctx, &beRewardOffersPb.GetRewardOffersForScreenRequest{
		ActorId: actorId,
		Filter: &beRewardOffersPb.GetRewardOffersForScreenRequest_Filter{
			OfferTypes: []beRewardsPb.RewardOfferType{beRewardsPb.RewardOfferType_SALARY_PROGRAM_OFFER},
			// as of now benefit-id is the id of corresponding rewardOffer.
			RewardOfferId: req.GetBenefitId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(rewardOffersRes, err); rpcErr != nil {
		logger.Error(ctx, "rewardOffersClient.GetRewardOffersForScreen rpc call failed", zap.String("benefitId", req.GetBenefitId()), zap.Error(rpcErr))
		return &fePb.SalaryProgramBenefitCardInfoResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error())}}, nil
	}
	if len(rewardOffersRes.GetRewardOffers()) == 0 {
		logger.Error(ctx, "no benefit found with given id", zap.String("benefitId", req.GetBenefitId()))
		return &fePb.SalaryProgramBenefitCardInfoResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusRecordNotFound()}}, nil
	}
	rewardOffer := rewardOffersRes.GetRewardOffers()[0]

	isUserSalaryProgramActive, activationType, err := s.isUserSalaryProgramActive(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error checking if user is salaryprogram active", zap.Error(err))
		return &fePb.SalaryProgramBenefitCardInfoResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())}}, nil
	}
	isUserFullSalaryProgramActive := isUserSalaryProgramActive && activationType == beSalaryPb.SalaryActivationType_FULL_SALARY_ACTIVATION

	// get benefitCard from rewardOffer
	benefitCard, err := s.getFeBenefitCardFromBeRewardOffer(ctx, rewardOffer, isUserSalaryProgramActive, SalaryLandingPage, false)
	if err != nil {
		logger.Error(ctx, "error creating feBenefitCard from rewardOffer", zap.Error(err))
		return &fePb.SalaryProgramBenefitCardInfoResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())}}, nil
	}

	// updating benefits info based on user state
	shouldDisplayBenefit, updateErr := s.updateFeBenefitCardBasedOnUserState(ctx, benefitCard, rewardOffer, actorId, isUserFullSalaryProgramActive, SalaryLandingPage)
	switch {
	case updateErr != nil:
		logger.Error(ctx, "error updating benefit card based on user state", zap.String(logger.REWARD_OFFER_ID, rewardOffer.GetId()), zap.Error(updateErr))
		return &fePb.SalaryProgramBenefitCardInfoResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(updateErr.Error())}}, nil
	case !shouldDisplayBenefit:
		logger.Info(ctx, "given benefit should not be displayed to the user", zap.String(logger.REWARD_OFFER_ID, rewardOffer.GetId()))
		return &fePb.SalaryProgramBenefitCardInfoResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusRecordNotFound()}}, nil
	}

	return &fePb.SalaryProgramBenefitCardInfoResponse{
		RespHeader:      &header.ResponseHeader{Status: rpc.StatusOk()},
		BenefitCardInfo: benefitCard,
	}, nil
}

// shouldDisplayRewardOfferOnGivenAppVersion returns true if the rewardOffer should be displayed on the given app version.
// It returns false only if the given app version is not within the supported app version range of the rewardOffer.
func shouldDisplayRewardOfferOnGivenAppVersion(rewardOffer *beRewardOffersPb.RewardOffer, appPlatform commontypes.Platform, appVersion uint32) bool {
	minAllowedAppVersion, isMinVerConfigured := rewardOffer.GetDisplayMeta().GetPlatformToMinSupportedAppVersionMap()[appPlatform.String()]
	maxAllowedAppVersion, isMaxVerConfigured := rewardOffer.GetDisplayMeta().GetPlatformToMaxSupportedAppVersionMap()[appPlatform.String()]

	if isMinVerConfigured && appVersion < minAllowedAppVersion || isMaxVerConfigured && appVersion > maxAllowedAppVersion {
		return false
	}
	return true
}

func (s *Service) getShareAccountDetailsWithHrCustomAction(ctx context.Context, actorId string) (*fePb.CustomAction, error) {
	var (
		customAction *fePb.CustomAction
	)
	switch {
	// In v1 share details on mail flow we separately call GetDetailsToShareOnEmail rpc to get email content.
	case apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dyconf.SalaryProgram().ShareDetailsOnMailV1FeatureConfig()):
		customAction = &fePb.CustomAction{
			ActionApi: fePb.CustomActionApi_GET_DETAILS_TO_SHARE_ON_EMAIL,
			ActionData: &fePb.CustomAction_GetDetailsToShareOnEmail{
				GetDetailsToShareOnEmail: &fePb.GetDetailsToShareOnEmailActionData{},
			},
		}
	// v0 flow: with attachment in email (in v0 flow we send share-account-details-with-HR email content in this rpc itself)
	// this flow is supported only in android.
	case apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dyconf.SalaryProgram().EmailAttachmentsSupportFeatureConfig()):
		emailContent, err := s.getShareAccountDetailsWithHrEmailContent(ctx, actorId, true)
		if err != nil {
			logger.Error(ctx, "error fetching share account details with HR email content", zap.Error(err))
			return nil, fmt.Errorf("error fetching share account details with HR email content, err: %w", err)
		}
		customAction = &fePb.CustomAction{
			Action: fePb.CustomAction_SHARE_ACCOUNT_DETAILS,
			ActionData: &fePb.CustomAction_ShareAccountDetailsMailActionData{
				ShareAccountDetailsMailActionData: &fePb.ShareAccountDetailsMailInfoActionData{
					EmailBody:        emailContent.GetEmailBody(),
					EmailSubject:     emailContent.GetEmailSubject(),
					EmailAttachments: emailContent.GetEmailAttachments(),
				},
			},
		}
	// v0 flow: without attachment in email (in v0 flow we send share-account-details-with-HR email content in this rpc itself)
	default:
		emailContent, err := s.getShareAccountDetailsWithHrEmailContent(ctx, actorId, false)
		if err != nil {
			logger.Error(ctx, "error fetching share account details with HR email content", zap.Error(err))
			return nil, fmt.Errorf("error fetching share account details with HR email content, err: %w", err)
		}
		customAction = &fePb.CustomAction{
			Action: fePb.CustomAction_SHARE_ACCOUNT_DETAILS,
			ActionData: &fePb.CustomAction_ShareAccountDetailsMailActionData{
				ShareAccountDetailsMailActionData: &fePb.ShareAccountDetailsMailInfoActionData{
					EmailBody:    emailContent.GetEmailBody(),
					EmailSubject: emailContent.GetEmailSubject(),
				},
			},
		}
	}
	return customAction, nil
}

func (s *Service) getSalaryLiteBannerSectionInfo(ctx context.Context, actorId string, isSalaryLiteUser, isFullSalaryProgramActive, isChurnedUser, isUserEligibleForSalaryLite bool, regCompletionTime *timestampPb.Timestamp) (*fePb.SalaryAccountBenefitsLandingPageResponse_SalaryLiteBannerInfo, error) {
	if !isUserEligibleForSalaryLite {
		return nil, nil
	}

	switch {
	// If user is salary lite user then show banner to upgrade to full salary program
	case isSalaryLiteUser && !isFullSalaryProgramActive:
		return &fePb.SalaryAccountBenefitsLandingPageResponse_SalaryLiteBannerInfo{
			Title:    commontypes.GetTextFromStringFontColourFontStyle("Unlock health insurance, early salary and more benefits", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M),
			ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/3d-writing-pad.png",
			Cta: &fePb.CTA{
				ImageUrl:  "https://epifi-icons.pointz.in/salaryprogram/right-chevron-pastel-berry.png",
				IsVisible: true,
				Action: &fePb.CTA_DeeplinkAction{
					DeeplinkAction: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_UPGRADE_TO_FULL_SALARY_PROGRAM_SCREEN,
					},
				},
				TextV1: commontypes.GetTextFromStringFontColourFontStyle("Share account details", "#CDC6E8", commontypes.FontStyle_SUBTITLE_S),
			},
			BgColor: "#9287BD",
		}, nil
	// The banner to nudge user for salary lite program	is shown if:
	// 1. Entrypoint flag is enabled
	// 2. If user is a churned user
	// 3. If user is not full salary program active- CURRENTLY NOT APPLICABLE TO INTERNAL USERS
	// 4. If segment expression is present then actor should be member of the segment expression
	case s.dyconf.SalaryProgram().SalaryLiteConfig().EnableLandingPageBanner() && apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dyconf.SalaryProgram().SalaryLiteConfig().FeatureReleaseConfig()) && (isChurnedUser || datetime.IsDatePastXDays(regCompletionTime, 0)):
		isUserEligible, err := s.isUserEligibleToShowSalaryLiteRegBanner(ctx, actorId, isFullSalaryProgramActive)
		if err != nil {
			return nil, fmt.Errorf("error checking if user is eligible to show salary lite banner, err: %w", err)
		}
		if !isUserEligible {
			return nil, nil
		}

		return &fePb.SalaryAccountBenefitsLandingPageResponse_SalaryLiteBannerInfo{
			Title:    commontypes.GetTextFromStringFontColourFontStyle("Looking for a faster way to unlock some salary benefits?", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M),
			ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/3d-keys.png",
			Cta: &fePb.CTA{
				ImageUrl:  "https://epifi-icons.pointz.in/salaryprogram/right-chevron-pastel-indigo.png",
				IsVisible: true,
				Action: &fePb.CTA_DeeplinkAction{
					DeeplinkAction: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_SALARY_LITE_INTRO_SCREEN,
					},
				},
				TextV1: commontypes.GetTextFromStringFontColourFontStyle("See Salary Lite Plan", "#D1DAF1", commontypes.FontStyle_SUBTITLE_S),
			},
			BgColor: "#879EDB",
		}, nil
	default:
		return nil, nil
	}
}

func (s *Service) isUserEligibleToShowSalaryLiteRegBanner(ctx context.Context, actorId string, isFullSalaryProgramActive bool) (bool, error) {
	eligibleActorsList := s.dyconf.SalaryProgram().SalaryLiteFlowsEligibleActors().ToStringArray()
	if len(eligibleActorsList) != 0 && lo.Contains(eligibleActorsList, actorId) {
		logger.Info(ctx, "Enabled salary lite flows", zap.Any(logger.ACTOR_ID_V2, actorId))
		return true, nil
	}

	if isFullSalaryProgramActive {
		if s.dyconf.SalaryProgram().SalaryLiteConfig().IsEnabledForInternalActiveUsers() {
			isUserPartOfGrp, err := s.isUserPartOfUserGroups(ctx, []commontypes.UserGroup{commontypes.UserGroup_INTERNAL}, actorId)
			if err != nil {
				logger.Error(ctx, "error checking if user is part of INTERNAL group", zap.Error(err))
				// intentionally muting the error to avoid failure of landing page due this error
				return false, nil
			}
			return isUserPartOfGrp, nil
		} else {
			return false, nil
		}
	}

	displaySegmentExpression := s.dyconf.SalaryProgram().SalaryLiteConfig().LandingPageBannerDisplaySegmentExpression()
	if displaySegmentExpression != "" {
		isMember, err := s.IsActorMemberOfAnySegmentExpression(ctx, actorId, []string{displaySegmentExpression})
		if err != nil {
			return false, fmt.Errorf("error checking whether actor is member of segment exression, err: %w", err)
		}

		return isMember, nil
	}

	return true, nil
}

// getLandingPageTopSectionCommsInfo generates the comms info to be shown by leveraging the salary verification section (top section of salary landing page)
// nolint:funlen
func (s *Service) getLandingPageTopSectionCommsInfo(ctx context.Context, actorId string, isFullSalaryProgramActive bool, latestActivationEmployerId string, salaryTxnCreditDate *timestampPb.Timestamp, appPlatform commontypes.Platform,
	appVersion uint32) (*fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo, error) {
	// if salary program benefits are about to expire, we give priority to raise verification request comms for top section
	if isFullSalaryProgramActive && time.Since(salaryTxnCreditDate.AsTime()) > s.conf.SalaryProgram.MinReqDurationSinceLastActivationForBenefitsExpiryComms {
		customAction, err := s.getShareAccountDetailsWithHrCustomAction(ctx, actorId)
		if err != nil {
			return nil, err
		}
		return &fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo{
			ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/timer-yellow.png",
			Title:    &fePb.Text{Text: "Salary benefits will expire soon", FontColor: "#FFFFFF"},
			Desc:     &fePb.Text{Text: "We were unable to detect your latest salary. If you have received your salary in the last 30 days, please raise a verification request to keep salary benefits active", FontColor: "#CED2D6"},
			PrimaryCta: &fePb.CTA{
				Text:      &fePb.Text{Text: "Raise verification request", FontColor: "#00B899"},
				IsVisible: true,
				Action:    &fePb.CTA_DeeplinkAction{DeeplinkAction: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_SALARY_PROGRAM_TRANSACTIONS_SCREEN}},
				BgColor:   "#555555",
			},
			SecondaryCta: &fePb.CTA{
				Text:      &fePb.Text{Text: "Share account details with HR", FontColor: "#00B899"},
				IsVisible: true,
				Action: &fePb.CTA_CustomAction{
					CustomAction: customAction,
				},
			},
		}, nil
	}

	var (
		topSectionCommsInfoConfig *genconf.SalaryProgramLandingPageTopSectionCommsInfo
		actorEmployerId           = latestActivationEmployerId
		isB2BEmployer             bool
	)

	// fetch current employer-id if salary latest-activation employerId is not available
	if actorEmployerId == "" {
		employerInfo, err := s.getCurrentEmployerOfActor(ctx, actorId)
		if err != nil {
			if !errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Debug(ctx, "error fetching current employer of actor for generating top section comms info", zap.Error(err))
				return nil, fmt.Errorf("error fetching current employer of actor for generation top section comms info: %w", err)
			}
		}

		// can be empty as well in case of RecordNotFound
		actorEmployerId = employerInfo.GetId()
	}

	// check if employer is onboarded by B2B route in salary program
	s.dyconf.SalaryProgram().B2BEmployersMap().Range(func(empId string, _ string) bool {
		if actorEmployerId == empId {
			isB2BEmployer = true
			return false
		}
		return true
	})

	// iterate over the configs to pick the one which matches all the conditions
	s.dyconf.SalaryProgram().LandingPageTopSectionCommsInfoMap().Range(func(_ string, cnf *genconf.SalaryProgramLandingPageTopSectionCommsInfo) bool {
		if !cnf.IsEnabled() {
			return true
		}
		if cnf.OnlyForSalaryProgramActiveUsers() && !isFullSalaryProgramActive {
			return true
		}
		if commontypes.Platform_IOS == appPlatform &&
			(appVersion < cnf.MinIosAppVersionSupported() || cnf.MaxIosAppVersionAllowed() != 0 && appVersion > cnf.MaxIosAppVersionAllowed()) {
			return true
		}
		if commontypes.Platform_ANDROID == appPlatform &&
			(appVersion < cnf.MinAndroidAppVersionSupported() || cnf.MaxAndroidAppVersionAllowed() != 0 && appVersion > cnf.MaxAndroidAppVersionAllowed()) {
			return true
		}
		if cnf.OnlyForB2BEmployers() && !isB2BEmployer {
			return true
		}
		if cnf.OnlyForB2CEmployers() && isB2BEmployer {
			return true
		}

		topSectionCommsInfoConfig = cnf
		return false
	})

	if topSectionCommsInfoConfig != nil {
		// converting config deeplink to deeplinkPb
		var parsedDeeplink *deeplinkPb.Deeplink
		if topSectionCommsInfoConfig.PrimaryCta().Deeplink() != nil {
			var deeplinkErr error
			parsedDeeplink, deeplinkErr = pkgDeeplink.NewDeeplinkFromV2Config(topSectionCommsInfoConfig.PrimaryCta().Deeplink())
			if deeplinkErr != nil {
				logger.Error(ctx, "error converting config parsedDeeplink to parsedDeeplink while generating top section comms info", zap.Error(deeplinkErr))
				return nil, fmt.Errorf("error converting config parsedDeeplink to parsedDeeplink")
			}
		}

		return &fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo{
			ImageUrl: topSectionCommsInfoConfig.ImageUrl(),
			Title:    &fePb.Text{Text: topSectionCommsInfoConfig.Title(), FontColor: topSectionCommsInfoConfig.TitleColor()},
			Desc:     &fePb.Text{Text: topSectionCommsInfoConfig.Desc(), FontColor: topSectionCommsInfoConfig.DescColor()},
			PrimaryCta: &fePb.CTA{
				Text:      &fePb.Text{Text: topSectionCommsInfoConfig.PrimaryCta().Text(), FontColor: topSectionCommsInfoConfig.PrimaryCta().TextColor()},
				IsVisible: topSectionCommsInfoConfig.PrimaryCta().IsVisible(),
				Action:    &fePb.CTA_DeeplinkAction{DeeplinkAction: parsedDeeplink},
				BgColor:   topSectionCommsInfoConfig.PrimaryCta().BgColor(),
			},
		}, nil
	}

	return nil, nil
}

// getSalaryVerificationStepperInfo generates the stepper info to be shown by leveraging the salary stepper section (top section of salary landing page)
// nolint: funlen
func (s *Service) getSalaryVerificationStepperInfo(ctx context.Context, actorId string, registrationCompletionDate *timestampPb.Timestamp, salaryUnderReview bool, appPlatform commontypes.Platform, appVersion uint32) (*fePb.SalaryAccountBenefitsLandingPageResponse_StepperInfo, error) {
	if !s.dyconf.SalaryProgram().SalaryAccountVerificationStepperInfo().IsEnabled() || (appPlatform == commontypes.Platform_IOS && appVersion < s.dyconf.SalaryProgram().SalaryAccountVerificationStepperInfo().MinIosAppVersionSupported() ||
		appPlatform == commontypes.Platform_ANDROID && appVersion < s.dyconf.SalaryProgram().SalaryAccountVerificationStepperInfo().MinAndroidAppVersionSupported()) {
		return nil, nil
	}

	primaryCta := &fePb.CTA{
		IsVisible: false,
	}
	secondaryCta := &fePb.CTA{
		IsVisible: false,
	}
	shareDetailsWithHRText := "Salary under review"
	shareDetailsWithHRDesc := "It'll take 2-3 business days to verify"
	shareDetailsWithHRIcon := "https://epifi-icons.pointz.in/salaryprogram/inprogress-green-border.png"
	if !salaryUnderReview {
		customAction, err := s.getShareAccountDetailsWithHrCustomAction(ctx, actorId)
		if err != nil {
			return nil, err
		}
		primaryCta = &fePb.CTA{
			TextV1: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "View a/c details"},
				FontColor:    "#FFFFFF",
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_BUTTON_S,
				},
			},
			IsVisible: true,
			Action: &fePb.CTA_DeeplinkAction{
				DeeplinkAction: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PRIMARY_SAVINGS_SUMMARY_SCREEN},
			},
			BgColor: "#00B899",
		}
		secondaryCta = &fePb.CTA{
			TextV1: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Share now"},
				FontColor:    "#00B899",
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_BUTTON_S,
				},
			},
			IsVisible: true,
			Action: &fePb.CTA_CustomAction{
				CustomAction: customAction,
			},
			BgColor: "#38393B",
		}
		shareDetailsWithHRText = "Share details with HR"
		shareDetailsWithHRDesc = "We've written the email for you"
		shareDetailsWithHRIcon = "https://epifi-icons.pointz.in/salaryprogram/mail-green-border.png"
	}

	salaryVerificationStepperInfo := s.getSalaryVerificationCompletedStepInfo()

	// registration
	maxAllowedDaysFromRegForAvailingAmazonVoucher := 45
	daysLeftToAvailAmazonVoucher := s.getDaysLeftToAvailAmazonVoucher(ctx, actorId, maxAllowedDaysFromRegForAvailingAmazonVoucher, registrationCompletionDate)
	salaryVerificationStepperInfo.Ticker = s.buildSalaryVerificationStepperTicker(daysLeftToAvailAmazonVoucher)

	salaryVerificationStepperInfo.StepInfos = append(salaryVerificationStepperInfo.StepInfos,
		s.getSalaryVerificationInProgressStepInfo(shareDetailsWithHRText, shareDetailsWithHRDesc, shareDetailsWithHRIcon, primaryCta, secondaryCta),
		s.getSalaryVerificationNotStartedStepInfo())
	return salaryVerificationStepperInfo, nil
}

func (s *Service) getSalaryVerificationCompletedStepInfo() *fePb.SalaryAccountBenefitsLandingPageResponse_StepperInfo {
	salaryVerificationStepperInfo := &fePb.SalaryAccountBenefitsLandingPageResponse_StepperInfo{
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "How to unlock benefits"},
			FontColor:    "#FFFFFF",
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
			},
		},
		BgColor: "#313234",
		Cta: &fePb.CTA{
			TextV1: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Already received salary?"},
				FontColor:    "#00B899",
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
				},
			},
			Action: &fePb.CTA_DeeplinkAction{
				DeeplinkAction: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_SALARY_PROGRAM_TRANSACTIONS_SCREEN},
			},
			IsVisible: true,
		},
		StepsProgressBarColor: "#648E4D",
		StepInfos: []*fePb.SalaryAccountBenefitsLandingPageResponse_StepInfo{
			&fePb.SalaryAccountBenefitsLandingPageResponse_StepInfo{
				StepStatus: fePb.SalaryAccountBenefitsLandingPageResponse_StepInfo_STEP_STATUS_COMPLETE,
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Confirm employer"},
					FontColor:    "#929599",
				},
				VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryprogram/success-green-bg.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  40,
					Height: 40,
				}),
			},
		},
	}
	return salaryVerificationStepperInfo
}

func (s *Service) getSalaryVerificationInProgressStepInfo(shareDetailsWithHRText string, shareDetailsWithHRDesc string, shareDetailsWithHRIcon string, primaryCta *fePb.CTA, secondaryCta *fePb.CTA) *fePb.SalaryAccountBenefitsLandingPageResponse_StepInfo {
	return &fePb.SalaryAccountBenefitsLandingPageResponse_StepInfo{
		StepStatus: fePb.SalaryAccountBenefitsLandingPageResponse_StepInfo_STEP_STATUS_IN_PROGRESS,
		Tag: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "YOU ARE HERE"},
			FontColor:    "#86BA6F",
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
			},
		},
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: shareDetailsWithHRText},
			FontColor:    "#929599",
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
			},
		},
		Desc: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: shareDetailsWithHRDesc},
			FontColor:    "#929599",
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_S,
			},
		},
		VisualElement: commontypes.GetVisualElementImageFromUrl(shareDetailsWithHRIcon).
			WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
			Width:  40,
			Height: 40,
		}),
		PrimaryCta:   primaryCta,
		SecondaryCta: secondaryCta,
	}
}

func (s *Service) getSalaryVerificationNotStartedStepInfo() *fePb.SalaryAccountBenefitsLandingPageResponse_StepInfo {
	return &fePb.SalaryAccountBenefitsLandingPageResponse_StepInfo{
		StepStatus: fePb.SalaryAccountBenefitsLandingPageResponse_StepInfo_STEP_STATUS_NOT_STARTED,
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Fi Salary Program benefits unlock"},
			FontColor:    "#929599",
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
			},
		},
		Desc: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "When salary received in account"},
			FontColor:    "#929599",
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_S,
			},
		},
		VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryprogram/lightning-bolt-grey-bg.png").
			WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
			Width:  40,
			Height: 40,
		}),
	}
}

// nolint:funlen
func (s *Service) getSalaryAccountVerificationSectionInfo(ctx context.Context, actorId string, isSalaryProgramActive bool, registrationCompletionDate *timestampPb.Timestamp, appPlatform commontypes.Platform,
	appVersion uint32) (*fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo, *fePb.SalaryAccountBenefitsLandingPageResponse_StepperInfo, error) {

	txnVerificationsRes, err := s.salaryProgramClient.GetSalaryTxnVerificationRequests(ctx, &beSalaryPb.GetSalaryTxnVerificationRequestsRequest{
		PageContext: &rpc.PageContextRequest{PageSize: 1},
		Filters: &beSalaryPb.GetSalaryTxnVerificationRequestsRequest_Filters{
			ActorId: actorId,
		},
		SortOrder: beSalaryPb.SortOrder_DESC,
	})
	if err != nil || !txnVerificationsRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching salary txn verification requests for the actor", zap.Error(err),
			zap.Any(logger.RPC_STATUS, txnVerificationsRes.GetStatus()),
		)
		return nil, nil, fmt.Errorf("error fetching salary txn verification requests for the actor")
	}

	// if no salary txn verifications requests have been raised yet, then the user should be nudged for
	// sharing account details and raising manual salary verification request by selecting a txn.
	if len(txnVerificationsRes.GetSalaryTxnVerificationRequests()) == 0 {
		// ask the user for sharing the account details
		res, err1 := s.getShareAccountDetailsWithPayrollInfoForResponse(ctx, actorId)
		// Early return stepper info if received from SalaryAccountVerificationSectionInfo according to new design
		// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=16582-45019&mode=design&t=q3srzH4EtS3ZPsTk-0
		stepperInfo, err2 := s.getSalaryVerificationStepperInfo(ctx, actorId, registrationCompletionDate, false, appPlatform, appVersion)
		return res, stepperInfo, multierr.Combine(err1, err2)
	}

	salaryTxnVerificationRequest := txnVerificationsRes.GetSalaryTxnVerificationRequests()[0]

	// if the system has marked salary txn as verified, but employer confirmation is pending as the txn remitter
	// was different than the users declared employer, then we will show the new screen for the user to confirm the employer
	if salaryTxnVerificationRequest.GetVerificationStatus() == beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED && salaryTxnVerificationRequest.GetVerificationSubStatus() == beSalaryPb.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_EMPLOYMENT_UPDATE {
		res, getBenefitsActivatedAndConfirmEmployerCommsTileErr := s.getBenefitsActivatedAndConfirmEmployerCommsTile(ctx, salaryTxnVerificationRequest.GetTxnEmployerId(), salaryTxnVerificationRequest.GetId())
		return res, nil, getBenefitsActivatedAndConfirmEmployerCommsTileErr
	}

	/*
		Next, we will fetch any manually raised verification request in the last 30 days and decide the nudges
		to be shown on the landing page based on its verification and acknowledgement statuses.
	*/

	manualTxnVerificationRes, err := s.salaryProgramClient.GetSalaryTxnVerificationRequests(ctx, &beSalaryPb.GetSalaryTxnVerificationRequestsRequest{
		PageContext: &rpc.PageContextRequest{PageSize: 1},
		Filters: &beSalaryPb.GetSalaryTxnVerificationRequestsRequest_Filters{
			ActorId: actorId,
			// todo (utkarsh) : remove reqSource filter after reqSources filter goes live
			ReqSource:  beSalaryPb.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_IN_APP_USER_REQUEST,
			ReqSources: []beSalaryPb.SalaryTxnVerificationRequestSource{beSalaryPb.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_IN_APP_USER_REQUEST},
			FromDate:   timestampPb.New(time.Now().Add(-1 * 30 * 24 * time.Hour)), // raised within last one month
		},
		SortOrder: beSalaryPb.SortOrder_DESC,
	})
	if err != nil || !manualTxnVerificationRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching manual salary txn verification request for the actor", zap.Error(err),
			zap.Any(logger.RPC_STATUS, manualTxnVerificationRes.GetStatus()),
		)
		return nil, nil, fmt.Errorf("error fetching manual salary txn verification request for the actor")
	}

	if len(manualTxnVerificationRes.GetSalaryTxnVerificationRequests()) == 0 {
		// if salary program is inactive, then we need to nudge the user to raise salary txn verification request.
		if !isSalaryProgramActive {
			// return "couldn't detect your salary credit" with "Verify now" and "Share account details with HR" CTAs
			res, err1 := s.getVerificationFailureAndVerifyNowInfoForResponse(ctx, actorId)
			// Early return stepper info if received from SalaryAccountVerificationSectionInfo according to new design
			// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=16582-45019&mode=design&t=q3srzH4EtS3ZPsTk-0
			stepperInfo, err2 := s.getSalaryVerificationStepperInfo(ctx, actorId, registrationCompletionDate, false, appPlatform, appVersion)
			return res, stepperInfo, multierr.Combine(err1, err2)
		}

		// returning nil as we don't need to show the verification section if program is active and no manual verification request exists.
		return nil, nil, nil
	}

	manualTxnVerificationRequest := manualTxnVerificationRes.GetSalaryTxnVerificationRequests()[0]

	switch manualTxnVerificationRequest.GetVerificationStatus() {
	case beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS:
		switch manualTxnVerificationRequest.GetVerificationSubStatus() {
		// if the sub-status is awaiting-employment-update, it means we need to nudge the user to update the employer
		// by confirming to the one detected by us via their salary txn.
		case beSalaryPb.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_EMPLOYMENT_UPDATE:
			res, err := s.getConfirmEmployerNudgeInfoForResponse(ctx, manualTxnVerificationRequest.GetTxnEmployerId(), manualTxnVerificationRequest.GetId())
			return res, nil, err

		// return salary txn verification request pending as a default response for IN_PROGRESS status
		default:
			// Early return stepper info if received from SalaryAccountVerificationSectionInfo according to new design
			// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=16582-45019&mode=design&t=q3srzH4EtS3ZPsTk-0
			stepperInfo, err := s.getSalaryVerificationStepperInfo(ctx, actorId, registrationCompletionDate, true, appPlatform, appVersion)
			return s.getSalaryVerificationPendingInfoForResponse(), stepperInfo, err
		}

	case beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED:
		if manualTxnVerificationRequest.GetUserAckStatus() != beSalaryPb.AcknowledgmentStatus_ACKNOWLEDGED {
			// return "Ok, got it" success screen
			return s.getSalaryVerificationSuccessfulInfoForResponse(manualTxnVerificationRequest.GetId()), nil, nil
		} else {
			// no need to do anything if no acknowledgement is required
			return nil, nil, nil
		}
	case beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFICATION_FAILED:
		if manualTxnVerificationRequest.GetUserAckStatus() != beSalaryPb.AcknowledgmentStatus_ACKNOWLEDGED {
			// return "Ok, got it" failure screen
			// return s.getSalaryVerificationFailureInfoForResponse(manualTxnVerificationRequest.GetId()), nil

			// return the option to share payslips again
			res, err := s.getVerificationFailureAndSharePayslipsInfoForResponse(ctx, actorId, manualTxnVerificationRequest.GetId())
			return res, nil, err
		} else if !isSalaryProgramActive {
			// return "couldn't detect salary" failure screen with "Verify now" and "Share account details with HR" CTAs
			res, err := s.getVerificationFailureAndVerifyNowInfoForResponse(ctx, actorId)
			return res, nil, err
		}
	default:
		logger.Error(ctx, "invalid verification status of the manual-txn-verification-request",
			zap.String(logger.REQUEST_ID, manualTxnVerificationRequest.GetId()),
			zap.String(logger.VERIFICATION_STATUS, manualTxnVerificationRequest.GetVerificationStatus().String()),
		)
		return nil, nil, fmt.Errorf("invalid verification status of the manual-txn-verification-request")
	}

	return nil, nil, nil
}

func (s *Service) getCancelSalaryLiteMandateQuickLinkTile() *fePb.SalaryAccountBenefitsLandingPageResponse_QuickLinksInfo_QuickLinkTile {
	cancelSalaryLiteMandateScreenDl := s.getCancelSalaryLiteMandateScreenDeeplink()
	return &fePb.SalaryAccountBenefitsLandingPageResponse_QuickLinksInfo_QuickLinkTile{
		Title:    commontypes.GetTextFromStringFontColourFontStyle("Cancel monthly transfer", "#FFFFFF", commontypes.FontStyle_SUBTITLE_XS),
		ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/failure-cross-circle.png",
		BgColor:  "#383838",
		Cta: &fePb.CTA{
			IsVisible: true,
			Action: &fePb.CTA_DeeplinkAction{
				DeeplinkAction: cancelSalaryLiteMandateScreenDl,
			},
		},
		Shadow: &widgetPb.Shadow{
			Height: 4,
			Colour: &widgetPb.BackgroundColour{
				Colour: &widgetPb.BackgroundColour_BlockColour{
					BlockColour: "#181818",
				},
			},
		},
	}
}

// nolint:funlen
func (s *Service) getCancelSalaryLiteMandateScreenDeeplink() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_SALARY_LITE_CANCEL_MANDATE_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&salaryScreenOptionsPb.SalaryLiteCancelMandateScreenOptions{
			Title:         commontypes.GetTextFromStringFontColourFontStyle("To cancel your monthly transfer, here’s what you need to do", "#313234", commontypes.FontStyle_HEADLINE_L),
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/salaryprogram/3d-switch-on.png"),
			StepsToCancel: []*commontypes.Text{
				commontypes.GetTextFromHtmlStringFontColourFontStyle("Start by sending a cancellation request", "#606265", commontypes.FontStyle_SUBTITLE_S),
				commontypes.GetTextFromHtmlStringFontColourFontStyle("Mandate will be paused and your source account would not be requested for a debit going further", "#606265", commontypes.FontStyle_SUBTITLE_S),
				commontypes.GetTextFromHtmlStringFontColourFontStyle("Its recommended that you cancel the mandate at your source bank account as well", "#606265", commontypes.FontStyle_SUBTITLE_S),
			},
			BottomCta: &fePb.CTA{
				IsVisible: true,
				TextV1:    commontypes.GetTextFromStringFontColourFontStyle("Send request", "#FFFFFF", commontypes.FontStyle_BUTTON_M),
				BgColor:   "#00B899",
				Shadow: &widgetPb.Shadow{
					Height: 4,
					Colour: &widgetPb.BackgroundColour{
						Colour: &widgetPb.BackgroundColour_BlockColour{
							BlockColour: "#00866F",
						},
					},
				},
				Action: &fePb.CTA_CustomAction{
					CustomAction: &fePb.CustomAction{
						Action: fePb.CustomAction_OPEN_V1_BOTTOM_SHEET,
						ActionData: &fePb.CustomAction_OpenV1BottomSheetDialog{
							OpenV1BottomSheetDialog: &fePb.OpenV1BottomSheetActionData{
								Title: commontypes.GetTextFromStringFontColourFontStyle("Are you sure you want to cancel?\nYou’ll be missing out on ", "#313234", commontypes.FontStyle_HEADLINE_L),
								Banners: []*fePb.OpenV1BottomSheetActionData_Banner{
									{
										ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/3d-bank.png",
										Title:    commontypes.GetTextFromStringFontColourFontStyle("A zero-balance account", "#313234", commontypes.FontStyle_SUBTITLE_S),
									},
									{
										ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/3d-coupon.png",
										Title:    commontypes.GetTextFromStringFontColourFontStyle("Access to salary benefits like cashback, vouchers and more", "#313234", commontypes.FontStyle_SUBTITLE_S),
									},
								},
								Ctas: []*fePb.CTA{
									{
										IsVisible: true,
										TextV1:    commontypes.GetTextFromStringFontColourFontStyle("Go back", "#00B899", commontypes.FontStyle_BUTTON_M),
										BgColor:   "#FFFFFF",
										Action: &fePb.CTA_DeeplinkAction{
											DeeplinkAction: &deeplinkPb.Deeplink{
												Screen: deeplinkPb.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN,
											},
										},
										Shadow: &widgetPb.Shadow{
											Height: 4,
											Colour: &widgetPb.BackgroundColour{
												Colour: &widgetPb.BackgroundColour_BlockColour{
													BlockColour: "#CED2D6",
												},
											},
										}},
									{
										IsVisible: true,
										TextV1:    commontypes.GetTextFromStringFontColourFontStyle("Yes, continue", "#FFFFFF", commontypes.FontStyle_BUTTON_M),
										BgColor:   "#00B899",
										Action: &fePb.CTA_CustomAction{
											CustomAction: &fePb.CustomAction{
												ActionApi: fePb.CustomActionApi_CANCEL_SALARY_LITE_MANDATE,
											},
										},
										Shadow: &widgetPb.Shadow{
											Height: 4,
											Colour: &widgetPb.BackgroundColour{
												Colour: &widgetPb.BackgroundColour_BlockColour{
													BlockColour: "#00866F",
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		}),
	}
}

// nolint:funlen
func (s *Service) getQuickLinksSectionInfo(ctx context.Context, actorId string, isFullSalaryProgramActive, isSalaryLiteUser, isUserEligibleForSalaryLite bool, salaryLiteMandateReqStatus beSalaryPb.SalaryLiteMandateRequestStatus, appPlatform commontypes.Platform,
	appVersion uint32) *fePb.SalaryAccountBenefitsLandingPageResponse_QuickLinksInfo {
	if appPlatform == commontypes.Platform_ANDROID && appVersion < s.dyconf.SalaryProgram().SalaryBenefitsLandingPageQuickLinksSection().MinAndroidVersionSupportingQuickLinksSection() ||
		appPlatform == commontypes.Platform_IOS && appVersion < s.dyconf.SalaryProgram().SalaryBenefitsLandingPageQuickLinksSection().MinIOSVersionSupportingQuickLinksSection() {
		return nil
	}

	if !s.dyconf.SalaryProgram().SalaryBenefitsLandingPageQuickLinksSection().IsVisible() {
		return nil
	}

	var (
		feQuickLinkTiles            []*fePb.SalaryAccountBenefitsLandingPageResponse_QuickLinksInfo_QuickLinkTile
		quickLinkTiles              []*genconf.QuickLinksTile
		exploreAllQuickLinksTitle   = &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Explore all quick links"}, FontColor: "#FFFFFF"}
		isNewQuickLinkTileUiEnabled = false
	)

	// v1 quick link tiles ui
	// figma:https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=13616%3A101958&mode=design&t=Ur2lvoW89b1CVrQ4-1
	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dyconf.SalaryProgram().LandingPageQuickLinkTilesV1FeatureConfig()) {
		isNewQuickLinkTileUiEnabled = true
	}

	quickLinksTilesCfg := s.dyconf.SalaryProgram().SalaryBenefitsLandingPageQuickLinksSection().QuickLinksTiles()
	quickLinksTilesCfg.Range(func(_ string, tile *genconf.QuickLinksTile) bool {
		quickLinkTiles = append(quickLinkTiles, tile)
		return true
	})
	// order the tiles by rank
	sort.Slice(quickLinkTiles, func(i, j int) bool {
		return quickLinkTiles[i].TileRank() < quickLinkTiles[j].TileRank()
	})

	// add salary lite specific tiles
	if isUserEligibleForSalaryLite && isSalaryLiteUser {
		// show cancel mandate tile only if mandate is success
		if salaryLiteMandateReqStatus == beSalaryPb.SalaryLiteMandateRequestStatus_SUCCESS {
			feQuickLinkTiles = append(feQuickLinkTiles, s.getCancelSalaryLiteMandateQuickLinkTile())
		}
		feQuickLinkTiles = append(feQuickLinkTiles, &fePb.SalaryAccountBenefitsLandingPageResponse_QuickLinksInfo_QuickLinkTile{
			Title:    commontypes.GetTextFromStringFontColourFontStyle("Already received salary on Fi?", "#FFFFFF", commontypes.FontStyle_SUBTITLE_XS),
			ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/3d-note.png",
			BgColor:  "383838",
			Cta: &fePb.CTA{
				IsVisible: true,
				Action:    &fePb.CTA_DeeplinkAction{DeeplinkAction: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_SALARY_PROGRAM_TRANSACTIONS_SCREEN}},
			},
			Shadow: &widgetPb.Shadow{
				Height: 4,
				Colour: &widgetPb.BackgroundColour{
					Colour: &widgetPb.BackgroundColour_BlockColour{
						BlockColour: "#181818",
					},
				},
			},
		})
	}

	if isNewQuickLinkTileUiEnabled {
		// won't be showing explore tile in new ui
		exploreAllQuickLinksTitle = nil
	}

	for _, tile := range quickLinkTiles {
		if isFullSalaryProgramActive {
			continue
		}
		if appPlatform == commontypes.Platform_ANDROID && appVersion < tile.VisibleFromAndroidVersion() ||
			appPlatform == commontypes.Platform_IOS && appVersion < tile.VisibleFromIosVersion() {
			continue
		}

		tileBgColor := tile.BgColor()
		titleColor := tile.TitleColor()
		var fontStyle *commontypes.Text_StandardFontStyle
		if isNewQuickLinkTileUiEnabled {
			// new ui properties, keeping the older properties for backward compatibility
			tileBgColor = "#383838"
			titleColor = "#FFFFFF"
			fontStyle = &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS,
			}
		}

		feQuickLinkTile := &fePb.SalaryAccountBenefitsLandingPageResponse_QuickLinksInfo_QuickLinkTile{
			Title: &commontypes.Text{
				FontColor:    titleColor,
				DisplayValue: &commontypes.Text_PlainString{PlainString: tile.Title()},
				FontStyle:    fontStyle,
			},
			ImageUrl: tile.ImageUrl(),
			BgColor:  tileBgColor,
			Shadow: &widgetPb.Shadow{
				Height: 4,
				Colour: &widgetPb.BackgroundColour{
					Colour: &widgetPb.BackgroundColour_BlockColour{
						BlockColour: "#181818",
					},
				},
			},
		}
		if tile.CTA() != nil {
			// todo (utkarsh) : add support for custom action in the config
			deeplink, deeplinkErr := pkgDeeplink.NewDeeplinkFromV2Config(tile.CTA().Deeplink())
			if deeplinkErr != nil {
				// intentionally muting the error to prevent config error in one tile to affect rendering of other tiles
				logger.Error(ctx, "error parsing deeplink from config", zap.Error(deeplinkErr))
				continue
			}
			if err := s.populateUserSpecificOptionsInDeeplink(ctx, actorId, deeplink); err != nil {
				// intentionally muting the error to prevent config error in one tile to affect rendering of other tiles
				logger.Error(ctx, "error populating user specific options in deeplink", zap.Error(err))
				continue
			}
			feQuickLinkTile.Cta = &fePb.CTA{
				IsVisible: tile.CTA().IsVisible(),
				ImageUrl:  tile.CTA().ImageUrl(),
				BgColor:   tile.CTA().BgColor(),
				Action:    &fePb.CTA_DeeplinkAction{DeeplinkAction: deeplink},
			}
		}
		feQuickLinkTiles = append(feQuickLinkTiles, feQuickLinkTile)
	}
	// at-least 2 tiles are required to display else do not need to show the quick links section.
	if len(feQuickLinkTiles) < 2 {
		return nil
	}
	maxAllowedTilesOnPage := 2
	if len(feQuickLinkTiles) >= 4 {
		maxAllowedTilesOnPage = 4
	}

	return &fePb.SalaryAccountBenefitsLandingPageResponse_QuickLinksInfo{
		QuickLinkTiles:        feQuickLinkTiles,
		MaxAllowedTilesOnPage: int32(maxAllowedTilesOnPage),
		// not present in latest ui
		ExploreAllQuickLinksTitle: exploreAllQuickLinksTitle,
		// cta to view all tile, Only present in new ui
		ViewAllCta: &fePb.CTA{
			IsVisible: true,
			TextV1:    commontypes.GetTextFromStringFontColourFontStyle("VIEW ALL", "#00B899", commontypes.FontStyle_OVERLINE_XS_CAPS),
			ImageUrl:  "https://epifi-icons.pointz.in/salaryprogram/right-chevron-green.png",
		},
	}
}

func (s *Service) getHelpSectionInfo(_ context.Context, appPlatform commontypes.Platform, appVersion uint32) (*fePb.SalaryAccountBenefitsLandingPageResponse_HelpInfo, error) {
	if !s.dyconf.SalaryProgram().HelpSectionInfo().IsVisible() {
		return nil, nil
	}

	if appPlatform == commontypes.Platform_ANDROID && appVersion < s.dyconf.SalaryProgram().HelpSectionInfo().MinAndroidVersionSupportingHelpSection() ||
		appPlatform == commontypes.Platform_IOS && appVersion < s.dyconf.SalaryProgram().HelpSectionInfo().MinIOSVersionSupportingHelpSection() {
		return nil, nil
	}

	var ctas []*fePb.CTA

	ctas = append(ctas,
		&fePb.CTA{
			Text:      &fePb.Text{Text: "FAQs", FontColor: "#00B899"},
			ImageUrl:  "https://epifi-icons.pointz.in/salaryprogram/clipboard.png",
			IsVisible: true,
			Action: &fePb.CTA_DeeplinkAction{DeeplinkAction: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_FAQ_CATEGORY,
				ScreenOptions: &deeplinkPb.Deeplink_FaqCategoryOptions{
					FaqCategoryOptions: &deeplinkPb.FaqCategoryOptions{CategoryId: s.dyconf.SalaryProgram().SalaryProgramFAQsCategoryId()},
				},
			}},
			BgColor: "#FFFFFF",
		},
		&fePb.CTA{
			Text:      &fePb.Text{Text: "Email us", FontColor: "#00B899"},
			ImageUrl:  "https://epifi-icons.pointz.in/salaryprogram/mail.png",
			IsVisible: true,
			Action: &fePb.CTA_CustomAction{
				CustomAction: &fePb.CustomAction{
					Action: fePb.CustomAction_SHARE_DETAILS_ON_EMAIL,
					ActionData: &fePb.CustomAction_ShareDetailsOnEmail{
						ShareDetailsOnEmail: &fePb.ShareDetailsOnEmailActionData{
							// todo (utkarsh) : should we send subject as well ?
							ToEmailId: s.dyconf.SalaryProgram().HelpSectionInfo().HelpSupportMailId(),
						},
					}}},
			BgColor: "#FFFFFF",
		})

	return &fePb.SalaryAccountBenefitsLandingPageResponse_HelpInfo{
		Title: &commontypes.Text{
			FontColor:    s.dyconf.SalaryProgram().HelpSectionInfo().TitleColor(),
			DisplayValue: &commontypes.Text_Html{Html: s.dyconf.SalaryProgram().HelpSectionInfo().Title()},
		},
		Subtitle: &commontypes.Text{
			FontColor:    s.dyconf.SalaryProgram().HelpSectionInfo().SubtitleColor(),
			DisplayValue: &commontypes.Text_Html{Html: s.dyconf.SalaryProgram().HelpSectionInfo().Subtitle()},
		},
		IconUrl: s.dyconf.SalaryProgram().HelpSectionInfo().IconUrl(),
		Ctas:    ctas,
	}, nil
}

// getEligibilityBannerSectionInfo get salary benefits landing page banner section info for showing eligibility for salary program
func (s *Service) getEligibilityBannerSectionInfo(ctx context.Context, actorId string, isSalaryProgramActive, isSalaryLiteUser bool) (*fePb.SalaryAccountBenefitsLandingPageResponse_BannerInfo, error) {
	// don't show the eligibility banner if user is salary active or user is salary lite user
	if isSalaryLiteUser || isSalaryProgramActive || !apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dyconf.SalaryProgram().LandingPageEligibilityBannerFeatureConfig()) {
		return nil, nil
	}

	minReqSalaryAmountRes, err := s.salaryProgramClient.GetMinRequiredAmountForSalaryTxnDetection(ctx, &beSalaryPb.MinRequiredAmountForSalaryTxnDetectionRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(minReqSalaryAmountRes, err); rpcErr != nil {
		logger.Error(ctx, "error fetching min required salary amount for actor", zap.Error(err))
		return nil, fmt.Errorf("error fetching min required salary amount for actor, err: %w", err)
	}

	displayStr := money.ToDisplayStringInIndianFormat(minReqSalaryAmountRes.GetAmount(), 0, true)
	return &fePb.SalaryAccountBenefitsLandingPageResponse_BannerInfo{
		Title:   commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("Your monthly salary must be %s or more <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/blog/salary-program-tnc\">Learn more</a>", displayStr), "#F6F9FD", commontypes.FontStyle_SUBTITLE_S),
		Heading: commontypes.GetTextFromStringFontColourFontStyle("Eligibility", "#929599", commontypes.FontStyle_SUBTITLE_S),
		BgColor: "#38393B",
		LeftVisualElement: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.pointz.in/salaryprogram/info-dark.png",
					},
					Properties: &commontypes.VisualElementProperties{
						Width:  40,
						Height: 40,
					},
				},
			},
		},
	}, nil
}

// getBannerSectionInfo get salary benefits landing page banner section info
// nolint:funlen
func (s *Service) getBannerSectionInfo(ctx context.Context, isSalaryLiteUser, isFullSalaryProgramActive bool) (*fePb.SalaryAccountBenefitsLandingPageResponse_BannerInfo, error) {
	var (
		bannerSectionInfoDynCfg = s.dyconf.SalaryProgram().SalaryBenefitsLandingPageBannerSectionInfo()
	)
	// referral banner is not visible to salary lite users
	if !bannerSectionInfoDynCfg.IsVisible() || (isSalaryLiteUser && !isFullSalaryProgramActive) {
		return nil, nil
	}

	// fetch active referrals seasons and the corresponding banner info
	currentTime := timestampPb.Now()
	activeSeasonsRes, err := s.salaryReferralsClient.GetSeasons(ctx, &beSalaryReferralsPb.GetSeasonsRequest{
		DisplaySince: currentTime,
		DisplayTill:  currentTime,
		ActiveSince:  currentTime,
		ActiveTill:   currentTime,
	})
	if err != nil || !activeSeasonsRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching active referrals seasons", zap.Any(logger.RPC_STATUS, activeSeasonsRes.GetStatus()), zap.Error(err))
		return nil, fmt.Errorf("error fetching active referrals seasons")
	}
	if len(activeSeasonsRes.GetSeasons()) == 0 {
		return nil, nil
	}
	season := activeSeasonsRes.GetSeasons()[0]
	if !season.GetDisplayMeta().GetEntryPointBannerInfo().GetIsVisible() {
		return nil, nil
	}
	bannerInfo := season.GetDisplayMeta().GetEntryPointBannerInfo()

	return &fePb.SalaryAccountBenefitsLandingPageResponse_BannerInfo{
		Title: &commontypes.Text{
			FontColor: bannerInfo.GetTitleColor(),
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: bannerInfo.GetTitle(),
			},
		},
		BgColor:  bannerInfo.GetBgColor(),
		ImageUrl: bannerInfo.GetImageUrl(),
		RightVisualElement: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: bannerInfo.GetImageUrl(),
					},
					Properties: &commontypes.VisualElementProperties{
						Width:  100,
						Height: 100,
					},
				},
			},
		},
		Cta: &fePb.CTA{
			Text: &fePb.Text{
				Text:      bannerInfo.GetCta().GetName(),
				FontColor: bannerInfo.GetCta().GetNameColor(),
			},
			BgColor:   bannerInfo.GetCta().GetBgColor(),
			ImageUrl:  bannerInfo.GetCta().GetImageUrl(),
			IsVisible: bannerInfo.GetCta().GetIsVisible(),
			Action: &fePb.CTA_DeeplinkAction{
				DeeplinkAction: bannerInfo.GetCta().GetDeeplink(),
			},
		},
	}, nil
}

// Landing page top section: getShareAccountDetailsWithPayrollInfoForResponse generates the response with steps to share account details with
// user's employer after the salary program registration is complete. It also includes the CTA to initiate salary txn verification process.
// nolint:funlen
func (s *Service) getShareAccountDetailsWithPayrollInfoForResponse(ctx context.Context, actorId string) (*fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo, error) {
	var (
		employerInfo *fePb.Employer
		// title used if sending cancelled cheque with email
		titleText    = "One last step: Share account info &\n cancelled cheque"
		customAction *fePb.CustomAction
	)

	errGrp, gctx := errgroup.WithContext(ctx)

	errGrp.Go(func() error {
		switch {
		// In v1 share details on mail flow we separately call GetDetailsToShareOnEmail rpc to get email content.
		case apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dyconf.SalaryProgram().ShareDetailsOnMailV1FeatureConfig()):
			customAction = &fePb.CustomAction{
				ActionApi: fePb.CustomActionApi_GET_DETAILS_TO_SHARE_ON_EMAIL,
				ActionData: &fePb.CustomAction_GetDetailsToShareOnEmail{
					GetDetailsToShareOnEmail: &fePb.GetDetailsToShareOnEmailActionData{},
				},
			}
		// v0 flow: with attachment in email (in v0 flow we send share-account-details-with-HR email content in this rpc itself)
		// this flow is supported only in android.
		case apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dyconf.SalaryProgram().EmailAttachmentsSupportFeatureConfig()):
			emailContent, err := s.getShareAccountDetailsWithHrEmailContent(ctx, actorId, true)
			if err != nil {
				logger.Error(ctx, "error fetching share account details with HR email content", zap.Error(err))
				return fmt.Errorf("error fetching share account details with HR email content, err: %w", err)
			}
			customAction = &fePb.CustomAction{
				Action: fePb.CustomAction_SHARE_ACCOUNT_DETAILS,
				ActionData: &fePb.CustomAction_ShareAccountDetailsMailActionData{
					ShareAccountDetailsMailActionData: &fePb.ShareAccountDetailsMailInfoActionData{
						EmailBody:        emailContent.GetEmailBody(),
						EmailSubject:     emailContent.GetEmailSubject(),
						EmailAttachments: emailContent.GetEmailAttachments(),
					},
				},
			}
		// v0 flow: without attachment in email (in v0 flow we send share-account-details-with-HR email content in this rpc itself)
		default:
			emailContent, err := s.getShareAccountDetailsWithHrEmailContent(ctx, actorId, false)
			if err != nil {
				logger.Error(ctx, "error fetching share account details with HR email content", zap.Error(err))
				return fmt.Errorf("error fetching share account details with HR email content, err: %w", err)
			}
			titleText = "One last step:\nShare account details with your HR"
			customAction = &fePb.CustomAction{
				Action: fePb.CustomAction_SHARE_ACCOUNT_DETAILS,
				ActionData: &fePb.CustomAction_ShareAccountDetailsMailActionData{
					ShareAccountDetailsMailActionData: &fePb.ShareAccountDetailsMailInfoActionData{
						EmailBody:    emailContent.GetEmailBody(),
						EmailSubject: emailContent.GetEmailSubject(),
					},
				},
			}
		}
		return nil
	})
	errGrp.Go(func() error {
		empInfo, empErr := s.getCurrentEmployerOfActor(gctx, actorId)
		if empErr != nil {
			if !errors.Is(empErr, epifierrors.ErrRecordNotFound) {
				logger.Debug(gctx, "error fetching employer info for populating employer name", zap.Error(empErr))
				return fmt.Errorf("error fetching employer info for populating employer name: %w", empErr)
			}

			/*
				In case no employerInfo is found, we'd return empty details gracefully instead of returning an error.
				Why? Because there is a chance that the user could modify the employer from some other flow and we wouldn't
				want the landing page section to fail loading because of that. This will also allow the user to modify the
				employer from the landing page.
			*/
			logger.Info(ctx, "received empty employer info for populating account sharing details")
		}

		employerInfo = empInfo
		return nil
	})

	if callsErr := errGrp.Wait(); callsErr != nil {
		return nil, fmt.Errorf("error fetching email content/employer info")
	}

	// todo(rohan): make these condition checks cleaner
	// if employer info is not available, the legal name will be empty. Thus, gracefully handling the empty string.
	employerName := employerInfo.GetLegalName()
	if employerName == "" {
		employerName = "your employer"
	} else {
		// making the employer name as bold to be shown on the UI
		employerName = fmt.Sprintf("<b>%s</b>", employerName)
	}

	return &fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo{
		ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/share-acnt-details-status.png",
		Title:    &fePb.Text{Text: titleText, FontColor: "#FFFFFF"},
		Desc: &fePb.Text{
			Text:      fmt.Sprintf("Benefits will get activated once your salary arrives in your Federal account from %s", employerName),
			FontColor: "#CED2D6", // Onyx
		},
		PrimaryCta: &fePb.CTA{
			Text:      &fePb.Text{Text: "Share account details with HR", FontColor: "#00B899"}, // Forest
			IsVisible: true,
			Action: &fePb.CTA_CustomAction{
				CustomAction: customAction,
			},
		},
		SecondaryCta: &fePb.CTA{
			Text: &fePb.Text{Text: "Already received salary?", FontColor: "#00B899"},
			Action: &fePb.CTA_DeeplinkAction{
				DeeplinkAction: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_SALARY_PROGRAM_TRANSACTIONS_SCREEN},
			},
			IsVisible: true,
		},
	}, nil
}

// Landing page top section: getSalaryVerificationPendingInfoForResponse generates the response for the state when salary
// verification is in progress
func (s *Service) getSalaryVerificationPendingInfoForResponse() *fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo {
	return &fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo{
		ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/salary-verifi-in-prog-status.png",
		Title:    &fePb.Text{Text: "Salary verification request has been raised", FontColor: "#FFFFFF"},
		Desc:     &fePb.Text{Text: "Our team will verify your salary transaction in 3-5 business days", FontColor: "#CED2D6"}, // Onyx
	}
}

// Landing page top section: getSalaryVerificationSuccessfulInfoForResponse generates the response for the state when
// salary verification is successful
// salaryTxnVerificationReqId denotes the id of salary verification request that was successful, due to which the current info is being shown.
func (s *Service) getSalaryVerificationSuccessfulInfoForResponse(salaryTxnVerificationReqId string) *fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo {
	return &fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo{
		ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/salary-verifi-success-status.png",
		Title:    &fePb.Text{Text: "Salary verification successful", FontColor: "#FFFFFF"},
		Desc:     &fePb.Text{Text: "Your salary benefits are now active!", FontColor: "#CED2D6"}, // Onyx
		PrimaryCta: &fePb.CTA{
			Text:      &fePb.Text{Text: "Ok, got it", FontColor: "#00B899"}, // Forest
			IsVisible: true,
			Action: &fePb.CTA_CustomAction{
				CustomAction: &fePb.CustomAction{
					Action:    fePb.CustomAction_REFRESH_AFTER_API_CALL,
					ActionApi: fePb.CustomActionApi_SALARY_VERIFICATION_STATUS_ACK_API,
					ActionData: &fePb.CustomAction_SalaryVerificationStatusAckActionData{
						SalaryVerificationStatusAckActionData: &fePb.SalaryVerificationStatusAckActionData{
							TxnVerificationRequestId: salaryTxnVerificationReqId,
						},
					},
				},
			},
		},
		TxnVerificationRequestId: salaryTxnVerificationReqId,
	}
}

// Landing page top section: getSalaryVerificationFailureInfoForResponse generates the response for the state when salary
// verification has failed
// salaryTxnVerificationReqId denotes the id of salary verification request that failed, due to which the current info is being shown.
func (s *Service) getSalaryVerificationFailureInfoForResponse(salaryTxnVerificationReqId string) *fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo {
	return &fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo{
		ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/salary-verifi-failure-status.png",
		Tag:      &fePb.Text{Text: "BENEFITS INACTIVE", FontColor: "#CF8888", BgColor: "#333333"}, // Dark Peach, Night
		Title:    &fePb.Text{Text: "Not eligible for salary benefits", FontColor: "#FFFFFF"},
		Desc:     &fePb.Text{Text: "We were not able to verify your chosen transaction as salary credit from your current employer", FontColor: "#CED2D6"}, // Onyx
		PrimaryCta: &fePb.CTA{
			Text:      &fePb.Text{Text: "Ok, got it", FontColor: "#00B899"}, // Forest
			IsVisible: true,
			Action: &fePb.CTA_CustomAction{
				CustomAction: &fePb.CustomAction{
					Action:    fePb.CustomAction_REFRESH_AFTER_API_CALL,
					ActionApi: fePb.CustomActionApi_SALARY_VERIFICATION_STATUS_ACK_API,
					ActionData: &fePb.CustomAction_SalaryVerificationStatusAckActionData{
						SalaryVerificationStatusAckActionData: &fePb.SalaryVerificationStatusAckActionData{
							TxnVerificationRequestId: salaryTxnVerificationReqId,
						},
					},
				},
			},
		},
		TxnVerificationRequestId: salaryTxnVerificationReqId,
	}
}

// getVerificationFailureAndSharePayslipsInfoForResponse generates response for the state when salary verification has failed.
// It asks the user to share payslips or skip sharing payslips. Skipping action acknowledges the verification failure state.
func (s *Service) getVerificationFailureAndSharePayslipsInfoForResponse(ctx context.Context, actorId, salaryTxnVerificationReqId string) (*fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo, error) {
	userRes, usrErr := s.getUserByActorId(ctx, actorId)
	if usrErr != nil {
		return nil, fmt.Errorf("error fetching user by actorId: %w", usrErr)
	}

	emailBody := s.dyconf.SalaryProgram().SalaryPayslipMailDuringFailureInfo().EmailBody()
	emailBody = strings.ReplaceAll(emailBody, "<NAME>", gammanames.BestNameFromProfile(ctx, userRes.GetProfile()).ToString())

	return &fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo{
		ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/salary-verifi-non-terminal-failure.png",
		Title:    &fePb.Text{Text: s.dyconf.SalaryProgram().SalaryPayslipMailDuringFailureInfo().Title(), FontColor: "#FFFFFF"},
		Desc:     &fePb.Text{Text: s.dyconf.SalaryProgram().SalaryPayslipMailDuringFailureInfo().Desc(), FontColor: "#CED2D6"}, // Onyx
		PrimaryCta: &fePb.CTA{
			Text: &fePb.Text{
				Text:      s.dyconf.SalaryProgram().SalaryPayslipMailDuringFailureInfo().SharePayslipCta().Text(),
				FontColor: s.dyconf.SalaryProgram().SalaryPayslipMailDuringFailureInfo().SharePayslipCta().TextColor(),
			},
			IsVisible: s.dyconf.SalaryProgram().SalaryPayslipMailDuringFailureInfo().SharePayslipCta().IsVisible(),
			Action: &fePb.CTA_CustomAction{
				CustomAction: &fePb.CustomAction{
					/*
						Using SHARE_ACCOUNT_DETAILS action for payslip action for now since client doesn't have any payslip sharing
						flow or custom action. We can enrich the same for any "share via mail" related actions
					*/
					Action: fePb.CustomAction_SHARE_ACCOUNT_DETAILS,
					ActionData: &fePb.CustomAction_ShareAccountDetailsMailActionData{
						ShareAccountDetailsMailActionData: &fePb.ShareAccountDetailsMailInfoActionData{
							EmailBody:    emailBody,
							EmailSubject: s.dyconf.SalaryProgram().SalaryPayslipMailDuringFailureInfo().EmailSubject(),
							ToEmailId:    s.dyconf.SalaryProgram().SalaryPayslipMailDuringFailureInfo().EmailId(),
						},
					},
				},
			},
		},
		// nolint:dupl
		SecondaryCta: &fePb.CTA{
			Text: &fePb.Text{
				Text:      s.dyconf.SalaryProgram().SalaryPayslipMailDuringFailureInfo().SkipSharePayslipCta().Text(),
				FontColor: s.dyconf.SalaryProgram().SalaryPayslipMailDuringFailureInfo().SkipSharePayslipCta().TextColor(),
			},
			IsVisible: s.dyconf.SalaryProgram().SalaryPayslipMailDuringFailureInfo().SkipSharePayslipCta().IsVisible(),
			Action: &fePb.CTA_CustomAction{
				CustomAction: &fePb.CustomAction{
					Action:    fePb.CustomAction_REFRESH_AFTER_API_CALL,
					ActionApi: fePb.CustomActionApi_SALARY_VERIFICATION_STATUS_ACK_API,
					ActionData: &fePb.CustomAction_SalaryVerificationStatusAckActionData{
						SalaryVerificationStatusAckActionData: &fePb.SalaryVerificationStatusAckActionData{
							TxnVerificationRequestId: salaryTxnVerificationReqId,
						},
					},
				},
			},
		},
		TxnVerificationRequestId: salaryTxnVerificationReqId,
	}, nil
}

// Landing page top section: getVerificationFailureAndVerifyNowInfoForResponse generates the response for the case when salary
// verification failure state is already ack'd. So we need to nudge the user to verify a salary txn again and also provide the option to share
// account details with their employer.
// nolint:funlen,dupl
func (s *Service) getVerificationFailureAndVerifyNowInfoForResponse(ctx context.Context, actorId string) (*fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo, error) {
	var (
		customAction *fePb.CustomAction
	)

	switch {
	// In v1 share details on mail flow we separately call GetDetailsToShareOnEmail rpc to get email content.
	case apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dyconf.SalaryProgram().ShareDetailsOnMailV1FeatureConfig()):
		customAction = &fePb.CustomAction{
			ActionApi: fePb.CustomActionApi_GET_DETAILS_TO_SHARE_ON_EMAIL,
			ActionData: &fePb.CustomAction_GetDetailsToShareOnEmail{
				GetDetailsToShareOnEmail: &fePb.GetDetailsToShareOnEmailActionData{},
			},
		}

	// v0 flow: with attachment in email (in v0 flow we send share-account-details-with-HR email content in this rpc itself)
	// this flow is supported only in android.
	case apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dyconf.SalaryProgram().EmailAttachmentsSupportFeatureConfig()):
		emailContent, err := s.getShareAccountDetailsWithHrEmailContent(ctx, actorId, true)
		if err != nil {
			return nil, fmt.Errorf("error fetching share account details with HR email content, err: %w", err)
		}
		customAction = &fePb.CustomAction{
			Action: fePb.CustomAction_SHARE_ACCOUNT_DETAILS,
			ActionData: &fePb.CustomAction_ShareAccountDetailsMailActionData{
				ShareAccountDetailsMailActionData: &fePb.ShareAccountDetailsMailInfoActionData{
					EmailBody:        emailContent.GetEmailBody(),
					EmailSubject:     emailContent.GetEmailSubject(),
					EmailAttachments: emailContent.GetEmailAttachments(),
				},
			},
		}
	// v0 flow: without attachment in email (in v0 flow we send share-account-details-with-HR email content in this rpc itself)
	default:
		emailContent, err := s.getShareAccountDetailsWithHrEmailContent(ctx, actorId, false)
		if err != nil {
			return nil, fmt.Errorf("error fetching share account details with HR email content, err: %w", err)
		}
		customAction = &fePb.CustomAction{
			Action: fePb.CustomAction_SHARE_ACCOUNT_DETAILS,
			ActionData: &fePb.CustomAction_ShareAccountDetailsMailActionData{
				ShareAccountDetailsMailActionData: &fePb.ShareAccountDetailsMailInfoActionData{
					EmailBody:    emailContent.GetEmailBody(),
					EmailSubject: emailContent.GetEmailSubject(),
				},
			},
		}
	}

	return &fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo{
		ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/salary-verifi-failure-status.png",
		Tag:      &fePb.Text{Text: "BENEFITS INACTIVE", FontColor: "#CF8888", BgColor: "#333333"}, // Dark Peach, Night
		Title:    &fePb.Text{Text: "Salary not detected", FontColor: "#FFFFFF"},
		Desc:     &fePb.Text{Text: "Make sure you’ve shared your Federal account details with your company HR. Benefits will get activated once your salary arrives here", FontColor: "#CED2D6"}, // Onyx
		PrimaryCta: &fePb.CTA{
			Text:      &fePb.Text{Text: "Share account details with HR", FontColor: "#00B899"}, // Forest
			IsVisible: true,
			Action: &fePb.CTA_CustomAction{
				CustomAction: customAction,
			},
		},
		SecondaryCta: &fePb.CTA{
			Text:      &fePb.Text{Text: "Already received salary?", FontColor: "#00B899"}, // Forest
			IsVisible: true,
			Action: &fePb.CTA_DeeplinkAction{
				DeeplinkAction: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_SALARY_PROGRAM_TRANSACTIONS_SCREEN},
			},
		},
	}, nil
}

// getConfirmEmployerNudgeInfoForResponse generates the response to nudge user to update their employer by confirming to the one detected by us from their
// salary transaction instead of the one selected by them. It also provides the option to dismiss the nudge.
func (s *Service) getConfirmEmployerNudgeInfoForResponse(ctx context.Context, employerId, salaryTxnVerificationReqId string) (*fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo, error) {
	employerInfoRes, err := s.employmentClient.GetEmployer(ctx, &beEmploymentPb.GetEmployerRequest{
		Identifier: &beEmploymentPb.GetEmployerRequest_EmployerId{
			EmployerId: employerId,
		},
	})
	if err != nil || !employerInfoRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching employer info by id", zap.Error(err),
			zap.Any(logger.RPC_STATUS, employerInfoRes.GetStatus()), zap.String("employerId", employerId),
		)
		return nil, fmt.Errorf("error fetching employer info by id")
	}
	return &fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo{
		Title: &fePb.Text{Text: s.dyconf.SalaryProgram().ConfirmEmployerUpdateNudgeInfo().Title(), FontColor: "#FFFFFF"},
		Desc: &fePb.Text{
			Text:      fmt.Sprintf(s.dyconf.SalaryProgram().ConfirmEmployerUpdateNudgeInfo().Desc(), employerInfoRes.GetEmployerInfo().GetNameBySource()),
			FontColor: "#CED2D6",
		},
		ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/salary-verifi-non-terminal-failure.png",
		// nolint:dupl
		PrimaryCta: &fePb.CTA{
			Text: &fePb.Text{
				Text:      s.dyconf.SalaryProgram().ConfirmEmployerUpdateNudgeInfo().ConfirmEmployerCta().Text(),
				FontColor: s.dyconf.SalaryProgram().ConfirmEmployerUpdateNudgeInfo().ConfirmEmployerCta().TextColor(),
			},
			IsVisible: s.dyconf.SalaryProgram().ConfirmEmployerUpdateNudgeInfo().ConfirmEmployerCta().IsVisible(),
			Action: &fePb.CTA_CustomAction{
				CustomAction: &fePb.CustomAction{
					Action:    fePb.CustomAction_REFRESH_AFTER_API_CALL,
					ActionApi: fePb.CustomActionApi_CONFIRM_EMPLOYER_FOR_SALARY_VERIFICATION,
					ActionData: &fePb.CustomAction_ConfirmEmployerForSalaryVerificationActionData{
						ConfirmEmployerForSalaryVerificationActionData: &fePb.ConfirmEmployerForSalaryVerificationActionData{
							TxnVerificationRequestId: salaryTxnVerificationReqId,
						},
					},
				},
			},
		},
		// nolint:dupl
		SecondaryCta: &fePb.CTA{
			Text: &fePb.Text{
				Text:      s.dyconf.SalaryProgram().ConfirmEmployerUpdateNudgeInfo().DismissEmployerCta().Text(),
				FontColor: s.dyconf.SalaryProgram().ConfirmEmployerUpdateNudgeInfo().DismissEmployerCta().TextColor(),
			},
			IsVisible: s.dyconf.SalaryProgram().ConfirmEmployerUpdateNudgeInfo().DismissEmployerCta().IsVisible(),
			Action: &fePb.CTA_CustomAction{
				CustomAction: &fePb.CustomAction{
					Action:    fePb.CustomAction_REFRESH_AFTER_API_CALL,
					ActionApi: fePb.CustomActionApi_DISMISS_EMPLOYER_UPDATE_NUDGE_FOR_SALARY_VERIFICATION,
					ActionData: &fePb.CustomAction_DismissEmployerUpdateNudgeActionData{
						DismissEmployerUpdateNudgeActionData: &fePb.DismissEmployerUpdateNudgeForSalaryVerificationActionData{
							TxnVerificationRequestId: salaryTxnVerificationReqId,
						},
					},
				},
			},
		},
	}, nil
}

// getBenefitsActivatedAndConfirmEmployerCommsTile generates the response to nudge user to update their employer by confirming to the one detected by us from their
// salary transaction instead of the one selected by them, for this case benefits have been activated by the system, we will update the employer in db if user confirms it
func (s *Service) getBenefitsActivatedAndConfirmEmployerCommsTile(ctx context.Context, employerId, salaryTxnVerificationReqId string) (*fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo, error) {
	employerInfoRes, err := s.employmentClient.GetEmployer(ctx, &beEmploymentPb.GetEmployerRequest{
		Identifier: &beEmploymentPb.GetEmployerRequest_EmployerId{
			EmployerId: employerId,
		},
	})
	if err != nil || !employerInfoRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching employer info by id", zap.Error(err),
			zap.Any(logger.RPC_STATUS, employerInfoRes.GetStatus()), zap.String("employerId", employerId),
		)
		return nil, fmt.Errorf("error fetching employer info by id")
	}
	return &fePb.SalaryAccountBenefitsLandingPageResponse_SalaryAccountVerificationInfo{
		Title: &fePb.Text{Text: s.dyconf.SalaryProgram().BenefitsActivatedAndConfirmEmployerCommsTile().Title(), FontColor: "#FFFFFF"},
		Desc: &fePb.Text{
			Text:      fmt.Sprintf(s.dyconf.SalaryProgram().BenefitsActivatedAndConfirmEmployerCommsTile().Desc(), employerInfoRes.GetEmployerInfo().GetNameBySource()),
			FontColor: "#CED2D6",
		},
		ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/green-tick.png",
		// nolint:dupl
		PrimaryCta: &fePb.CTA{
			Text: &fePb.Text{
				Text:      s.dyconf.SalaryProgram().BenefitsActivatedAndConfirmEmployerCommsTile().ConfirmEmployerCta().Text(),
				FontColor: s.dyconf.SalaryProgram().BenefitsActivatedAndConfirmEmployerCommsTile().ConfirmEmployerCta().TextColor(),
				BgColor:   s.dyconf.SalaryProgram().BenefitsActivatedAndConfirmEmployerCommsTile().ConfirmEmployerCta().BgColor(),
			},
			IsVisible: s.dyconf.SalaryProgram().BenefitsActivatedAndConfirmEmployerCommsTile().ConfirmEmployerCta().IsVisible(),
			Action: &fePb.CTA_CustomAction{
				CustomAction: &fePb.CustomAction{
					Action:    fePb.CustomAction_REFRESH_AFTER_API_CALL,
					ActionApi: fePb.CustomActionApi_CONFIRM_EMPLOYER_FOR_SALARY_VERIFICATION,
					ActionData: &fePb.CustomAction_ConfirmEmployerForSalaryVerificationActionData{
						ConfirmEmployerForSalaryVerificationActionData: &fePb.ConfirmEmployerForSalaryVerificationActionData{
							TxnVerificationRequestId: salaryTxnVerificationReqId,
						},
					},
				},
			},
		},
		// nolint:dupl
		SecondaryCta: &fePb.CTA{
			Text: &fePb.Text{
				Text:      s.dyconf.SalaryProgram().BenefitsActivatedAndConfirmEmployerCommsTile().DismissEmployerCta().Text(),
				FontColor: s.dyconf.SalaryProgram().BenefitsActivatedAndConfirmEmployerCommsTile().DismissEmployerCta().TextColor(),
			},
			IsVisible: s.dyconf.SalaryProgram().BenefitsActivatedAndConfirmEmployerCommsTile().DismissEmployerCta().IsVisible(),
			Action: &fePb.CTA_CustomAction{
				CustomAction: &fePb.CustomAction{
					Action:    fePb.CustomAction_REFRESH_AFTER_API_CALL,
					ActionApi: fePb.CustomActionApi_DISMISS_EMPLOYER_UPDATE_NUDGE_FOR_SALARY_VERIFICATION,
					ActionData: &fePb.CustomAction_DismissEmployerUpdateNudgeActionData{
						DismissEmployerUpdateNudgeActionData: &fePb.DismissEmployerUpdateNudgeForSalaryVerificationActionData{
							TxnVerificationRequestId: salaryTxnVerificationReqId,
						},
					},
				},
			},
		},
	}, nil
}

// nolint:funlen
func (s *Service) RaiseSalaryVerification(ctx context.Context, req *fePb.RaiseSalaryVerificationRequest) (*fePb.RaiseSalaryVerificationResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	orderId := req.GetOrderId()

	raiseManualSalaryVerificationResponse, err := s.salaryProgramClient.RaiseManualSalaryVerification(ctx, &beSalaryPb.RaiseManualSalaryVerificationRequest{
		ActorId:   actorId,
		OrderId:   orderId,
		ReqSource: beSalaryPb.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_IN_APP_USER_REQUEST,
	})

	if err != nil || !raiseManualSalaryVerificationResponse.GetStatus().IsSuccess() {
		logger.Error(ctx, "error while raising manual salary verification request", zap.Error(err),
			zap.String(logger.RPC_STATUS, raiseManualSalaryVerificationResponse.GetStatus().String()),
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String(logger.ORDER_ID, orderId))
		return &fePb.RaiseSalaryVerificationResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error while raising manual salary verification request")},
		}, nil
	}

	switch raiseManualSalaryVerificationResponse.GetSalaryVerificationReq().GetVerificationStatus() {
	case beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFICATION_FAILED:
		return &fePb.RaiseSalaryVerificationResponse{
			ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/verification_failure.png",
			Title: &fePb.Text{
				Text:      "Incorrect transaction selected",
				FontColor: "#FFFFFF",
			},
			Desc: &fePb.Text{
				Text:      "The chosen transaction doesn't seem to be a salary transaction. Please try again",
				FontColor: "#A4A4A4",
			},
			Cta: &fePb.CTA{
				Text: &fePb.Text{
					Text:      "Go back",
					FontColor: "#FFFFFF",
					BgColor:   "#00B899", // Forest
				},
				Action: &fePb.CTA_DeeplinkAction{
					DeeplinkAction: &deeplinkPb.Deeplink{
						Screen:        deeplinkPb.Screen_SALARY_PROGRAM_TRANSACTIONS_SCREEN,
						ScreenOptions: nil,
					},
				},
				IsVisible: true,
			},
			RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		}, nil

	case beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS:
		return &fePb.RaiseSalaryVerificationResponse{
			ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/verification_success.png",
			Title: &fePb.Text{
				Text:      "Verification request raised!",
				FontColor: "#FFFFFF",
			},
			Desc: &fePb.Text{
				Text:      "We will verify if your salary previously arrived on Fi. Expect a response in 3-5 business days. Our team may reach out for additional details.",
				FontColor: "#A4A4A4",
			},
			Cta: &fePb.CTA{
				Text: &fePb.Text{
					Text:      "Ok, got it",
					FontColor: "#FFFFFF",
					BgColor:   "#00B899", // Forest
				},
				Action: &fePb.CTA_DeeplinkAction{
					DeeplinkAction: &deeplinkPb.Deeplink{
						Screen:        deeplinkPb.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN,
						ScreenOptions: nil,
					},
				},
				IsVisible: true,
			},
			RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		}, nil

	case beSalaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED:
		return &fePb.RaiseSalaryVerificationResponse{
			ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/verification_success.png",
			Title: &fePb.Text{
				Text:      "Transaction successfully verified",
				FontColor: "#FFFFFF",
			},
			Desc: &fePb.Text{
				Text:      "Your salary transaction looks good to go! You can now access your salary benefits",
				FontColor: "#A4A4A4",
			},
			Cta: &fePb.CTA{
				Text: &fePb.Text{
					Text:      "Ok, got it",
					FontColor: "#FFFFFF",
					BgColor:   "#00B899", // Forest
				},
				Action: &fePb.CTA_DeeplinkAction{
					DeeplinkAction: &deeplinkPb.Deeplink{
						Screen:        deeplinkPb.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN,
						ScreenOptions: nil,
					},
				},
				IsVisible: true,
			},
			RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		}, nil

	default:
		logger.Error(ctx, "unknown salary request verification status received",
			zap.String("salaryVerificationStatus", raiseManualSalaryVerificationResponse.GetSalaryVerificationReq().GetVerificationStatus().String()),
			zap.String("salaryVerificationRequestId", raiseManualSalaryVerificationResponse.GetSalaryVerificationReq().GetId()))
		return &fePb.RaiseSalaryVerificationResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("unknown salary request verification status received")},
		}, nil
	}
}

// nolint: funlen
func (s *Service) GetPossibleSalaryTransactions(ctx context.Context, req *fePb.GetPossibleSalaryTransactionsRequest) (*fePb.GetPossibleSalaryTransactionsResponse, error) {

	var (
		actorId                  = req.GetReq().GetAuth().GetActorId()
		appPlatform              = req.GetReq().GetAuth().GetDevice().GetPlatform()
		appVersion               = req.GetReq().GetAppVersionCode()
		err                      error
		descending               bool
		activitiesStartTimestamp *timestampPb.Timestamp
		activitiesEndTimestamp   = timestampPb.Now()
		orderOffset              = uint32(0)
		accountId                string
		minReqSalaryTxnAmount    *moneyPb.Money
		hasBefore                = false
		hasAfter                 = false
		nextPageExist            = false
		title                    = &fePb.Text{Text: s.conf.SalaryProgram.TransactionsPage.Title, FontColor: "#FFFFFF"} // Snow

		updateEmpDetailsErrorView = &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
			Options: &errorsPb.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
					Title:    "Update Employment Details",
					Subtitle: "Please update your employment details to continue.",
					Ctas: []*errorsPb.CTA{{
						Type: errorsPb.CTA_CUSTOM,
						Text: "Update Now",
						Action: &deeplinkPb.Deeplink{
							Screen: deeplinkPb.Screen_SALARY_PROGRAM_EMPLOYER_CONFIRMATION_SCREEN,
						},
						DisplayTheme: errorsPb.CTA_PRIMARY,
					}},
				}},
		}
	)

	// todo (utkarsh) : evaluate on how to handle unverified employer cases centrally in different salary flows.
	// check if the user is linked to a verified employer or not as verified employer is mandatory for showing relevant txns to
	// the user based on his employer level salary threshold.
	currentEmployerRes, err := s.getCurrentEmployerOfActor(ctx, actorId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound) || err == nil && !currentEmployerRes.GetIsVerifiedEmployer():
		logger.WarnWithCtx(ctx, "user is not linked to a verified employer", zap.String(logger.ACTOR_ID_V2, actorId))
		resp := &fePb.GetPossibleSalaryTransactionsResponse{RespHeader: &header.ResponseHeader{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("user is not linked to a verified employer"),
		}}
		if appPlatform == commontypes.Platform_IOS && appVersion >= s.dyconf.SalaryProgram().MinIosAppVersionHandlingRpcResponseErrorView() ||
			appPlatform == commontypes.Platform_ANDROID && appVersion >= s.dyconf.SalaryProgram().MinAndroidAppVersionHandlingRpcResponseErrorView() {
			resp.RespHeader.ErrorView = updateEmpDetailsErrorView
		}
		return resp, nil

	case err != nil:
		logger.Error(ctx, "error fetching employer details", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return &fePb.GetPossibleSalaryTransactionsResponse{RespHeader: &header.ResponseHeader{
			Status: rpc.StatusInternalWithDebugMsg("error fetching employer details"),
		}}, nil
	}

	returnInternalError := func(err1 string) (*fePb.GetPossibleSalaryTransactionsResponse, error) {
		return &fePb.GetPossibleSalaryTransactionsResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err1)}}, nil
	}

	switch req.GetToken().(type) {
	case *fePb.GetPossibleSalaryTransactionsRequest_AfterToken:
		afterToken := pkgPag.PageToken{}
		err = afterToken.Unmarshal(req.GetAfterToken())
		if err != nil {
			logger.Error(ctx, "invalid token", zap.Error(err))
			return returnInternalError(fmt.Sprint("invalid token", zap.Error(err)))
		}
		activitiesStartTimestamp = afterToken.GetTimestamp()
		orderOffset = afterToken.GetOffset()
		descending = false
		hasBefore = true
	case *fePb.GetPossibleSalaryTransactionsRequest_BeforeToken:
		beforeToken := pkgPag.PageToken{}
		err = beforeToken.Unmarshal(req.GetBeforeToken())
		if err != nil {
			logger.Error(ctx, "invalid token", zap.Error(err))
			return returnInternalError(fmt.Sprint("invalid token", zap.Error(err)))
		}
		activitiesStartTimestamp = beforeToken.GetTimestamp()
		activitiesEndTimestamp = timestampPb.New(time.Now().Add(time.Duration(s.conf.SalaryProgram.SalaryTransactionFilters.MaxDaysOfTransactionsForSalary*-24) * time.Hour))
		orderOffset = beforeToken.GetOffset()
		descending = true
		hasAfter = true
	case *fePb.GetPossibleSalaryTransactionsRequest_LatestPage:
		activitiesStartTimestamp = timestampPb.Now()
		activitiesEndTimestamp = timestampPb.New(time.Now().Add(time.Duration(s.conf.SalaryProgram.SalaryTransactionFilters.MaxDaysOfTransactionsForSalary*-24) * time.Hour))
		descending = true
	default:
		logger.Info(ctx, "unhandled token type")
		return returnInternalError("unhandled token type")
	}

	errGrp, gctx := errgroup.WithContext(ctx)
	errGrp.Go(func() error {
		// Get primary account holder id
		actorRes, getActorErr := s.actorClient.GetActorById(gctx, &actor.GetActorByIdRequest{Id: actorId})
		if getActorErr != nil || !actorRes.GetStatus().IsSuccess() {
			logger.Error(ctx, "actorClient.GetActorById rpc call failed", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any(logger.RPC_STATUS, actorRes.GetStatus()), zap.Error(getActorErr))
			return fmt.Errorf("actorClient.GetActorById rpc call failed")
		}
		// Getting account details of the user
		accountRes, getAccountErr := s.savingsClient.GetAccount(gctx, &savings.GetAccountRequest{
			Identifier: &savings.GetAccountRequest_PrimaryUserId{PrimaryUserId: actorRes.GetActor().GetEntityId()},
		})
		if getAccountErr != nil || accountRes.GetAccount() == nil {
			logger.Error(ctx, "failed to get account by primary user id", zap.String(logger.USER_ID, actorRes.GetActor().GetEntityId()), zap.Error(err))
			return fmt.Errorf("failed to get account by primary user id")
		}
		accountId = accountRes.GetAccount().GetId()
		return nil
	})

	errGrp.Go(func() error {
		minReqAmountRes, getMinAmountErr := s.salaryProgramClient.GetMinRequiredAmountForSalaryTxnDetection(gctx, &beSalaryPb.MinRequiredAmountForSalaryTxnDetectionRequest{ActorId: actorId})
		if getMinAmountErr != nil || !minReqAmountRes.GetStatus().IsSuccess() {
			logger.Error(ctx, "salaryProgramClient.GetMinRequiredAmountForSalaryTxnDetection rpc call failed", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any(logger.RPC_STATUS, minReqAmountRes.GetStatus()), zap.Error(getMinAmountErr))
			return fmt.Errorf("salaryProgramClient.GetMinRequiredAmountForSalaryTxnDetection rpc call failed")
		}
		minReqSalaryTxnAmount = minReqAmountRes.GetAmount()
		return nil
	})

	eGrpErr := errGrp.Wait()
	if eGrpErr != nil {
		logger.Error(ctx, "error fetching account, minReqSalaryTxnAmount details for actor", zap.Error(eGrpErr))
		return &fePb.GetPossibleSalaryTransactionsResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(eGrpErr.Error())}}, nil
	}

	minReqSalaryTxnAmountDisplayString := pkgMoney.ToDisplayStringWithPrecision(minReqSalaryTxnAmount, 0)

	subTitle := &fePb.Text{
		Text:      fmt.Sprintf(s.conf.SalaryProgram.TransactionsPage.Subtitle, minReqSalaryTxnAmountDisplayString),
		FontColor: "#FFFFFF", // Snow
	}
	salaryEligibilityCriteria := &fePb.GetPossibleSalaryTransactionsResponse_SalaryAccountEligibilityCriteria{
		Title: &fePb.Text{
			Text:      "Salary Account Eligibility Criteria",
			FontColor: "#333333", // Night
		},
		Desc: &fePb.Text{
			Text:      fmt.Sprintf("To be eligible for a salary account, you need to have an in-hand salary of greater than %s", minReqSalaryTxnAmountDisplayString),
			FontColor: "#646464", // Lead
		},
	}
	emptyTransactionsResponse := &fePb.GetPossibleSalaryTransactionsResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		Title:      title,    // Snow
		SubTitle:   subTitle, // Snow
		SalaryTnxEmptyListInfo: &fePb.GetPossibleSalaryTransactionsResponse_SalaryTnxEmptyListInfo{
			Message: &fePb.Text{
				Text:      fmt.Sprintf("No credit transactions above %s in the last %d days", minReqSalaryTxnAmountDisplayString, s.conf.SalaryProgram.SalaryTransactionFilters.MaxDaysOfTransactionsForSalary),
				FontColor: "#CED2D6", // Onyx
			},
			Image: &commontypes.Image{
				ImageUrl: s.conf.SalaryProgram.TransactionsPage.EmptyTnxErrorImage,
			},
		},
		SalaryAccountEligibilityCriteria: salaryEligibilityCriteria,
	}

	transactionProtocols := []payment.PaymentProtocol{}
	for _, protocol := range s.dyconf.SalaryProgram().SalaryTransactionFilters().AllowedTransactionProtocols().ToStringArray() {
		transactionProtocols = append(transactionProtocols, payment.PaymentProtocol(payment.PaymentProtocol_value[protocol]))
	}

	beReq := &actoractivity.GetActivitiesRequest{
		CurrentActorId: actorId,
		AccountFilter: []*actoractivity.GetActivitiesRequest_AccountFilter{
			{
				AccountId:   accountId,
				AccountType: accounts.Type_SAVINGS,
			},
		},
		ActivitiesStartTimestamp: activitiesStartTimestamp,
		ActivitiesEndTimestamp:   activitiesEndTimestamp,
		PageSize:                 req.GetPageSize() + 1,
		Descending:               descending,
		OrderOffset:              int32(orderOffset),
		PaymentFilter: &actoractivity.GetActivitiesRequest_PaymentFilter{
			PaymentProtocol: transactionProtocols,
			FromAmount:      minReqSalaryTxnAmount,
			TransactionType: payment.AccountingEntryType_CREDIT,
		},
	}
	logger.Info(ctx, fmt.Sprintf("GetActivities request: %v", beReq.String()))
	beRes, err := s.actorActivityClient.GetActivities(ctx, beReq)
	if err = epifigrpc.RPCError(beRes, err); err != nil {
		if beRes.GetStatus().IsRecordNotFound() {
			return emptyTransactionsResponse, nil
		}
		logger.Error(ctx, "failed to get activities from BE", zap.Error(err))
		return returnInternalError(fmt.Sprint("failed to get activities from BE", zap.Error(err)))
	}
	logger.Info(ctx, fmt.Sprintf("GetActivities response len: %v", len(beRes.GetActivities())))
	transactions := beRes.GetActivities()
	if len(beRes.GetActivities()) > int(req.GetPageSize()) {
		nextPageExist = true
		transactions = beRes.GetActivities()[:req.GetPageSize()]
	}

	beforeToken := getBeforeToken(transactions)
	beforeTokenMarshal, err := beforeToken.Marshal()
	if err != nil {
		logger.Error(ctx, "failed to marshal token", zap.Error(err))
		return returnInternalError(fmt.Sprint("failed to marshal token", zap.Error(err)))
	}

	afterToken := getAfterToken(transactions)
	afterTokenMarshal, err := afterToken.Marshal()
	if err != nil {
		logger.Error(ctx, "failed to marshal token", zap.Error(err))
		return returnInternalError(fmt.Sprint("failed to marshal token", zap.Error(err)))
	}

	// If we could not fetch pagesize+1 elements, there are no more pages to fetch
	if nextPageExist {
		switch req.GetToken().(type) {
		case *fePb.GetPossibleSalaryTransactionsRequest_AfterToken:
			hasAfter = true
		case *fePb.GetPossibleSalaryTransactionsRequest_BeforeToken, *fePb.GetPossibleSalaryTransactionsRequest_LatestPage:
			hasBefore = true
		}
	}

	// If any of before or after tokens is empty, then don't send the corresponding token
	if !hasBefore {
		beforeTokenMarshal = ""
	}
	if !hasAfter {
		afterTokenMarshal = ""
	}

	// filtering transactions
	transactions, err = s.filterPossibleSalaryTransactions(ctx, transactions)
	if err != nil {
		logger.Error(ctx, "error while filtering possible salary txns", zap.Error(err))
		return returnInternalError("error while filtering possible salary txns")
	}
	if len(transactions) == 0 {
		return emptyTransactionsResponse, nil
	}

	res := &fePb.GetPossibleSalaryTransactionsResponse{
		RespHeader:                       &header.ResponseHeader{Status: rpc.StatusOk()},
		Title:                            title, // Snow
		SubTitle:                         subTitle,
		SalaryAccountEligibilityCriteria: salaryEligibilityCriteria,
		Transactions:                     s.getTransactionsWidgetList(transactions),
		PageTokens: &fePb.PageTokens{
			BeforeToken: beforeTokenMarshal,
			AfterToken:  afterTokenMarshal,
		},
	}
	return res, nil
}

// filterPossibleSalaryTransactions filters out txns and returns only possible salary txns
// nolint: funlen
func (s *Service) filterPossibleSalaryTransactions(ctx context.Context, transactions []*actoractivity.GetActivitiesResponse_Activity) ([]*actoractivity.GetActivitiesResponse_Activity, error) {

	// if true then we will skip these filters
	if cfg.IsSimulatedEnv(s.dyconf.Application().Environment) {
		return transactions, nil
	}

	orderIdentifierList := []*orderServicePb.OrderIdentifier{}
	for _, activity := range transactions {
		orderIdentifierList = append(orderIdentifierList, &orderServicePb.OrderIdentifier{
			Identifier: &orderServicePb.OrderIdentifier_OrderId{
				OrderId: activity.GetActivityId(),
			},
		})
	}
	getOrdersWithTransactionsRes, err := s.orderServiceClient.GetOrdersWithTransactions(ctx, &orderServicePb.GetOrdersWithTransactionsRequest{
		OrderIdentifiers: orderIdentifierList,
	})
	if rpcErr := epifigrpc.RPCError(getOrdersWithTransactionsRes, err); rpcErr != nil {
		logger.Error(ctx, "error in GetOrdersWithTransactions rpc", zap.Error(rpcErr), zap.Any("order_identifiers", orderIdentifierList))
		return nil, fmt.Errorf("error in GetOrdersWithTransactions rpc")
	}

	filteredTxns := []*actoractivity.GetActivitiesResponse_Activity{}
	for indx, orderWithTransaction := range getOrdersWithTransactionsRes.GetOrderWithTransactions() {
		// only a single txn will exist in salary orders.
		if len(orderWithTransaction.GetTransactions()) != 1 {
			continue
		}
		txn := orderWithTransaction.GetTransactions()[0]

		// check if given order workflow is possible for a salary txn
		if !isGivenWorkflowPossibleForSalaryTxn(orderWithTransaction.GetOrder().GetWorkflow()) {
			continue
		}

		// check if given order provenance is possible for a salary txn
		if !isGivenProvenancePossibleForSalaryTxn(orderWithTransaction.GetOrder().GetProvenance()) {
			continue
		}

		// deposit txn cannot be salary txn
		if isDepositTxn(orderWithTransaction.GetOrder().GetTags()) {
			continue
		}

		// only orders in terminal success state can be verified for salary txn
		// for salary orders terminal success state is PAID
		if orderWithTransaction.GetOrder().GetStatus() != orderServicePb.OrderStatus_PAID {
			continue
		}

		// if the txn is refund related, then it can't be salary.
		isNonIncomeTxn, err := s.isGivenTxnNotCategorizedAsIncome(ctx, orderWithTransaction.GetOrder().GetToActorId(), txn)
		if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.WarnWithCtx(ctx, "failed to categorize txn, hence filtering out", zap.String(logger.ORDER_ID, orderWithTransaction.GetOrder().GetId()))
			continue
		}
		if err != nil {
			logger.Error(ctx, "error in checking if txn was not income", zap.String(logger.ORDER_ID, orderWithTransaction.GetOrder().GetId()))
			return nil, fmt.Errorf("error checking if the txn was categorized as refund, orderId: %s, err: %w", orderWithTransaction.GetOrder().GetId(), err)
		}
		if isNonIncomeTxn {
			logger.Info(ctx, "txn is categorized as refund, hence not a salary txn", zap.String(logger.ORDER_ID, orderWithTransaction.GetOrder().GetId()), zap.String("transactionId", txn.GetId()))
			continue
		}

		filteredTxns = append(filteredTxns, transactions[indx])
	}

	return filteredTxns, nil

}

func isGivenWorkflowPossibleForSalaryTxn(workflow orderServicePb.OrderWorkflow) bool {
	// order workflow would be NO_OP for salary txns
	return workflow == orderServicePb.OrderWorkflow_NO_OP
}

func isGivenProvenancePossibleForSalaryTxn(provenance orderServicePb.OrderProvenance) bool {
	return provenance != orderServicePb.OrderProvenance_ATM
}

func isDepositTxn(orderTags []orderServicePb.OrderTag) bool {
	return lo.Contains(orderTags, orderServicePb.OrderTag_DEPOSIT)
}

func (s *Service) isGivenTxnNotCategorizedAsIncome(ctx context.Context, beneficiaryActorId string, txn *paymentPb.Transaction) (bool, error) {
	txnCategories, err := s.getCategoriesForTxn(ctx, beneficiaryActorId, txn.GetId())
	if err != nil {
		return false, fmt.Errorf("error fetching txn category details, err : %w", err)
	}

	// set on ontology ids linked to the txn
	txnCategoryOntologyIdsSet := make(map[string]struct{}, len(txnCategories.GetOntologies()))
	for _, ontology := range txnCategories.GetOntologies() {
		txnCategoryOntologyIdsSet[ontology.GetOntologyId()] = struct{}{}
	}

	// check if the txn was tagged with a non income related ontology id.
	for _, nonIncomeOntologyId := range s.dyconf.SalaryProgram().SalaryTransactionFilters().NonIncomeRelatedTxnCategoryOntologyIds().ToStringArray() {
		if _, ok := txnCategoryOntologyIdsSet[nonIncomeOntologyId]; ok {
			return true, nil
		}
	}
	return false, nil
}

func (s *Service) getCategoriesForTxn(ctx context.Context, beneficiaryActorId, txnId string) (*categorizerPb.TransactionCategories, error) {
	txnCategoriesRes, err := s.txnCatClient.GetTxnCategoryDetails(ctx, &categorizerPb.GetTxnCategoryDetailsRequest{
		ActorId:      beneficiaryActorId,
		Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: txnId},
		Provenance:   categorizerPb.Provenance_DS,
		DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
	})
	if err != nil || !txnCategoriesRes.GetStatus().IsSuccess() && !txnCategoriesRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "txnCatClient.GetTxnCategoryDetails rpc call failed", zap.Any(logger.RPC_STATUS, txnCategoriesRes.GetStatus()), zap.Error(err), zap.String("transactionId", txnId))
		return nil, fmt.Errorf("txnCatClient.GetTxnCategoryDetails rpc call failed, err: %w", err)
	}
	if txnCategoriesRes.GetStatus().IsRecordNotFound() {
		return nil, epifierrors.ErrRecordNotFound
	}
	return txnCategoriesRes.GetTxnCategories(), nil
}

// Returns the before token for the given activity list
func getBeforeToken(actorActivities []*actoractivity.GetActivitiesResponse_Activity) *pkgPag.PageToken {
	reverseActorActivities := funk.Reverse(actorActivities).([]*actoractivity.GetActivitiesResponse_Activity)
	firstTimestamp, offsetCount := getArrayEntryAndOffset(reverseActorActivities)
	return &pkgPag.PageToken{
		Timestamp: firstTimestamp,
		Offset:    offsetCount,
	}
}

// Returns the after token for the given activity list
func getAfterToken(actorActivities []*actoractivity.GetActivitiesResponse_Activity) *pkgPag.PageToken {
	lastTimestamp, offsetCount := getArrayEntryAndOffset(actorActivities)
	return &pkgPag.PageToken{
		Timestamp: lastTimestamp,
		Offset:    offsetCount,
	}
}

// Utility method to return the first entry of the array and the count of the same in the array
func getArrayEntryAndOffset(actorActivities []*actoractivity.GetActivitiesResponse_Activity) (*timestamp.Timestamp, uint32) {
	fistTimestampSeen := false
	firstArrayEntry := &timestamp.Timestamp{}
	offsetCount := uint32(0)
	for _, activity := range actorActivities {
		if !fistTimestampSeen {
			firstArrayEntry = activity.GetActivityTimestamp()
			offsetCount++
			fistTimestampSeen = true
			continue
		}

		if !firstArrayEntry.AsTime().Equal(activity.GetActivityTimestamp().AsTime()) {
			break
		}
		offsetCount++
	}
	return firstArrayEntry, offsetCount
}

// get FE transaction widgets from be response
// returns transaction widget list given a list of actor activities
func (s *Service) getTransactionsWidgetList(actorActivities []*actoractivity.GetActivitiesResponse_Activity) []*widget.TransactionsWidget {
	var (
		transactionWidgetList []*widget.TransactionsWidget
		transactionWidget     = &widget.TransactionsWidget{}
		prevDate              *date.Date
	)
	for _, actorActivity := range actorActivities {
		dayTimeStampString := s.getDayFromTimeStamp(actorActivity.GetActivityTimestamp())
		activityTimestamp := actorActivity.GetActivityTimestamp().AsTime().In(datetime.IST)
		currentDate := datetime.TimeToDate(&activityTimestamp)

		if !datetime.DateEquals(prevDate, currentDate) {
			if prevDate != nil {
				transactionWidgetList = append(transactionWidgetList, transactionWidget)
			}
			prevDate = currentDate
			transactionWidget = &widget.TransactionsWidget{}
			transactionWidget.Title = dayTimeStampString
		}
		transactionView := s.getTransactionView(actorActivity)
		transactionWidget.Transactions = append(transactionWidget.Transactions, transactionView)
	}
	transactionWidgetList = append(transactionWidgetList, transactionWidget)
	return transactionWidgetList
}

// getTransactionView returns transaction view given an actor activity
// nolint: funlen
func (s *Service) getTransactionView(actorActivity *actoractivity.GetActivitiesResponse_Activity) *widget.TransactionView {

	var screenOptions *feDeeplink.TransactionReceiptScreenOptions

	transactionView := &widget.TransactionView{
		IconUrl:              actorActivity.GetIconUrl(),
		Title:                actorActivity.GetTitle(),
		ShortDesc:            actorActivity.GetShortDesc(),
		ShortIconUrl:         actorActivity.GetShortDescIconUrl(),
		Amount:               money.ToDisplayStringWithoutSymbol(actorActivity.GetAmount()),
		AmountBadge:          beAmountBadgeToFeAmountBadgeMap[actorActivity.GetAmountBadge()],
		TransactionTimestamp: actorActivity.GetActivityTimestamp(),
		AmountColour:         s.getAmountColour(actorActivity.GetAmountBadge()),
		IconColour:           actor.GetColourCodeForActor(actorActivity.GetSecondActorId()),
	}

	switch {
	case actorActivity.GetActivityEntryPoint() == actoractivity.ActivityEntryPoint_ORDER:
		screenOptions = &feDeeplink.TransactionReceiptScreenOptions{
			OrderId: actorActivity.GetActivityId(),
			Identifier: &feDeeplink.TransactionReceiptScreenOptions_OrdersId{
				OrdersId: actorActivity.GetActivityId(),
			},
		}
	case actorActivity.GetActivityEntryPoint() == actoractivity.ActivityEntryPoint_AA:
		screenOptions = &feDeeplink.TransactionReceiptScreenOptions{
			OrderId: actorActivity.GetActivityId(),
			Identifier: &feDeeplink.TransactionReceiptScreenOptions_AaTxnId{
				AaTxnId: actorActivity.GetActivityId(),
			},
		}
	}

	transactionView.ViewLink = &widget.DeepLinkElement{Link: &feDeeplink.Deeplink{
		Screen: feDeeplink.Screen_TRANSACTION_RECEIPT,
		ScreenOptions: &feDeeplink.Deeplink_TransactionReceiptScreenOptions{
			TransactionReceiptScreenOptions: screenOptions,
		},
	},
	}

	switch actorActivity.GetDeeplinkIdentifier().(type) {
	case *actoractivity.GetActivitiesResponse_Activity_TimelineId:
		transactionView.IconDeepLink = &widget.DeepLinkElement{
			Link: &feDeeplink.Deeplink{
				Screen: feDeeplink.Screen_TIMELINE,
				ScreenOptions: &feDeeplink.Deeplink_TimelineScreenOptions{
					TimelineScreenOptions: &feDeeplink.TimelineScreenOptions{
						TimelineId: actorActivity.GetTimelineId(),
					},
				},
			},
		}
	case *actoractivity.GetActivitiesResponse_Activity_DepositAccountIdentifier_:
		// TODO(harish): handle FD interest credit txns
		//  Monorail: https://monorail.pointz.in/p/fi-app/issues/detail?id=19555
		if actorActivity.GetDepositAccountIdentifier().GetAccountId() == "" {
			transactionView.IconDeepLink = &widget.DeepLinkElement{
				Link: &feDeeplink.Deeplink{
					Screen: feDeeplink.Screen_DEPOSIT_LANDING_SCREEN,
					ScreenOptions: &feDeeplink.Deeplink_DepositAccountLandingScreenOption{
						DepositAccountLandingScreenOption: &feDeeplink.DepositAccountLandingScreenOptions{
							DepositType: actorActivity.GetDepositAccountIdentifier().GetAccountType(),
						},
					},
				},
			}
		} else {
			transactionView.IconDeepLink = &widget.DeepLinkElement{
				Link: &feDeeplink.Deeplink{
					Screen: feDeeplink.Screen_DEPOSIT_ACCOUNT_DETAILS,
					ScreenOptions: &feDeeplink.Deeplink_DepositDetailsScreenOptions{
						DepositDetailsScreenOptions: &feDeeplink.DepositAccountDetailsScreenOptions{
							AccountId:   actorActivity.GetDepositAccountIdentifier().GetAccountId(),
							DepositType: actorActivity.GetDepositAccountIdentifier().GetAccountType(),
						},
					},
				},
			}
		}
	}

	return transactionView
}

// getFullSalaryActivationStatusWithLastVerifiedEmpDetailsForUser checks if the user is currently full salaryprogroam active or not.
// If active it returns the last employer from which user received salary due to which they are salaryprogram active.
func (s *Service) getFullSalaryActivationStatusWithLastVerifiedEmpDetailsForUser(ctx context.Context, actorId string) (isActive bool, lastVerifiedEmpId, lastVerifiedEmployerName string, err error) {
	regStatusRes, err := s.salaryProgramClient.GetCurrentRegStatusAndNextRegStage(ctx, &beSalaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actorId,
		FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE})
	if rpcErr := epifigrpc.RPCError(regStatusRes, err); rpcErr != nil {
		return false, "", "", fmt.Errorf("error fetching salaryprogram registration status, err : %w", rpcErr)
	}
	if regStatusRes.GetRegistrationStatus() != beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		return false, "", "", fmt.Errorf("user is not registered for salaryprogram")
	}

	// get latest activation details
	lastActivationDetailsRes, err := s.salaryProgramClient.GetLatestActivationDetailsActiveAtTime(ctx, &beSalaryPb.LatestActivationDetailsActiveAtTimeRequest{
		RegistrationId: regStatusRes.GetRegistrationId(),
		ActiveAtTime:   timestampPb.Now(),
		// activation_kind is unspecified as latest activation can be used to determine whether an active salary program exists for the user and employer id.
		ActivationKind: beSalaryPb.SalaryProgramActivationKind_SALARY_PROGRAM_ACTIVATION_KIND_UNSPECIFIED,
	})
	if err != nil || !lastActivationDetailsRes.GetStatus().IsSuccess() && !lastActivationDetailsRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "salaryProgramClient.GetLatestActivationDetailsActiveAtTime rpc call failed", zap.Any(logger.RPC_STATUS, lastActivationDetailsRes.GetStatus()), zap.Error(err))
		return false, "", "", fmt.Errorf("salaryProgramClient.GetLatestActivationDetailsActiveAtTime rpc call failed")
	}
	// return false if activation details are not found or user is not full salary active (not allowing salary referrals for salary lite active users)
	if lastActivationDetailsRes.GetStatus().IsRecordNotFound() || lastActivationDetailsRes.GetActivationType() != beSalaryPb.SalaryActivationType_FULL_SALARY_ACTIVATION {
		return false, "", "", nil
	}
	lastVerifiedEmpId = lastActivationDetailsRes.GetSalaryTxnEmployerId()

	// get last verified employer details
	employerInfoRes, err := s.employmentClient.GetEmployer(ctx, &beEmploymentPb.GetEmployerRequest{
		Identifier: &beEmploymentPb.GetEmployerRequest_EmployerId{
			EmployerId: lastVerifiedEmpId,
		},
	})
	if rpcErr := epifigrpc.RPCError(employerInfoRes, err); rpcErr != nil {
		return false, "", "", fmt.Errorf("error fetching salaryprogram registration status, empId : %v,  err : %w", lastVerifiedEmpId, rpcErr)
	}
	return true, lastVerifiedEmpId, employerInfoRes.GetEmployerInfo().GetNameBySource(), nil
}

// getAmountColour returns the amount colour to be displayed on the client given and amount badge
func (s *Service) getAmountColour(amountBadge actoractivity.GetActivitiesResponse_Activity_AmountBadge) string {
	switch amountBadge {
	case actoractivity.GetActivitiesResponse_Activity_DEBIT:
		return s.conf.ActorActivityAmountColourMap.DebitColour
	case actoractivity.GetActivitiesResponse_Activity_CREDIT:
		return s.conf.ActorActivityAmountColourMap.CreditColour
	case actoractivity.GetActivitiesResponse_Activity_SAVINGS:
		return s.conf.ActorActivityAmountColourMap.SavingsColour
	default:
		return s.conf.ActorActivityAmountColourMap.DefaultColour
	}
}

func (s *Service) buildSalaryVerificationStepperTicker(daysLeftToAvailAmazonVoucher int) *ui.IconTextComponent {

	tickerUrl := ""
	// showing 1 day left instead of X days.
	tickerText := fmt.Sprintf("%v day left to claim ₹500 Amazon voucher", daysLeftToAvailAmazonVoucher)
	tickerBgColor := ""
	if daysLeftToAvailAmazonVoucher > 1 {
		tickerText = fmt.Sprintf("%v days left to claim ₹500 Amazon voucher", daysLeftToAvailAmazonVoucher)
	}

	switch {
	case daysLeftToAvailAmazonVoucher > 25:
		tickerUrl = "https://epifi-icons.pointz.in/salaryprogram/white-timer.png"
		tickerBgColor = "#6F62A4"
	case daysLeftToAvailAmazonVoucher > 15:
		tickerUrl = "https://epifi-icons.pointz.in/salaryprogram/white-timer.png"
		tickerBgColor = "#D2AC3D"
	case daysLeftToAvailAmazonVoucher > 0:
		tickerUrl = "https://epifi-icons.pointz.in/salaryprogram/white-timer.png"
		tickerBgColor = "#AA301F"
	default:
		tickerUrl = "https://epifi-icons.pointz.in/salaryprogram/white-star.png"
		tickerText = "Don't miss out on benefits worth ₹30k"
		tickerBgColor = "#4F71AB"
	}
	ticker := &ui.IconTextComponent{
		LeftVisualElement: commontypes.GetVisualElementImageFromUrl(tickerUrl).WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
			Width:  22,
			Height: 22,
		}),
		LeftIcon: &commontypes.Image{
			ImageUrl:  tickerUrl,
			ImageType: commontypes.ImageType_PNG,
			Height:    22,
			Width:     22,
		},
		Texts: []*commontypes.Text{
			&commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: tickerText},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
				},
				FontColor: "#EAE8F1",
			},
		},
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor: tickerBgColor,
		},
	}
	return ticker
}

// populateInAppCsatSurveyInfo populates feedback engine survey information in response object if user is eligible for a survey
func (s *Service) populateInAppCsatSurveyInfo(ctx context.Context, actorId string, res *fePb.SalaryAccountBenefitsLandingPageResponse) {
	surveySegmentIdExpression := s.dyconf.SalaryProgram().SalaryProgramSurvey().BenefitsLandingPageSurveySegmentIdExpression()
	if surveySegmentIdExpression == "" {
		return
	}

	segmentExprRes, err := s.segmentClient.IsMemberOfExpressions(ctx, &segmentPb.IsMemberOfExpressionsRequest{
		ActorId:              actorId,
		SegmentIdExpressions: []string{surveySegmentIdExpression},
	})
	if rpcErr := epifigrpc.RPCError(segmentExprRes, err); rpcErr != nil {
		logger.Error(ctx, "error while calling segmentClient.IsMemberOfExpressions", zap.Error(err))
		return
	}

	segmentExprMembership := segmentExprRes.GetSegmentExpressionMembershipMap()[surveySegmentIdExpression]
	if segmentExprMembership.GetSegmentExpressionStatus() == segmentPb.SegmentExpressionStatus_OK && segmentExprMembership.GetIsActorMember() {
		res.RespHeader.FeedbackEngineInfo = &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_APP_FLOW,
				FlowIdentifier:     types.FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_IN_APP_CSAT.String(),
			},
		}
	}
}
