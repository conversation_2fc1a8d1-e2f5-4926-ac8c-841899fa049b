package epf

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	consentPb "github.com/epifi/gamma/api/consent"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	feHeaderPb "github.com/epifi/gamma/api/frontend/header"
	feEpfPb "github.com/epifi/gamma/api/frontend/insights/epf"
	beEpfPb "github.com/epifi/gamma/api/insights/epf"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	"github.com/epifi/gamma/pkg/feature/release"
)

const (
	discoverUanFailureMsg = "We encountered an error while fetching your UAN. Please try again."
)

// nolint: funlen
func (s *Service) DiscoverUANs(ctx context.Context, req *feEpfPb.DiscoverUANsRequest) (*feEpfPb.DiscoverUANsResponse, error) {
	epfImportSession := s.getEpfImportSessionDetails(ctx, req.GetReq().GetAuth().GetActorId(), req.GetEpfImportSessionId())
	failureDeeplink := epfImportSession.GetEpfImportExitDeeplink().GetFailureDeeplink()

	if req.GetPhoneNumber() == nil {
		logger.Info(ctx, "phone number in discoverUans is nil")
		return &feEpfPb.DiscoverUANsResponse{
			RespHeader: &feHeaderPb.ResponseHeader{
				Status:    rpc.StatusInvalidArgument(),
				ErrorView: FullScreenErrorViewSomethingWentWrongWithoutRetry(discoverUanFailureMsg, "Ok, got it", failureDeeplink),
			},
		}, nil
	}

	epfConfirmPhoneScreenDl, err := s.deeplinkBuilder.EPFConfirmPhoneNumberScreen(ctx, []*commontypes.PhoneNumber{
		req.GetPhoneNumber(),
	}, req.GetReq().GetAuth().GetActorId(), req.GetEpfImportSessionId())

	if err != nil {
		return &feEpfPb.DiscoverUANsResponse{
			RespHeader: &feHeaderPb.ResponseHeader{
				Status:    rpc.StatusInternal(),
				ErrorView: FullScreenErrorViewSomethingWentWrongWithoutRetry(discoverUanFailureMsg, "Ok, got it", failureDeeplink),
			},
		}, nil
	}

	// check if user's epf consent exists, if not create a new consent
	if checkConsentErr := s.checkAndCreateUserConsentIfRequired(ctx, req.GetReq()); checkConsentErr != nil {
		logger.Error(ctx, "failure in consent check-create stage", zap.Error(checkConsentErr))
		return &feEpfPb.DiscoverUANsResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternal(),
				ErrorView: FullScreenErrorViewSomethingWentWrongWithRetry(discoverUanFailureMsg, "Close", "Retry", failureDeeplink, epfConfirmPhoneScreenDl),
			},
		}, nil
	}

	discoverUansResp, err := s.epfClient.DiscoverUANs(ctx, &beEpfPb.DiscoverUANsRequest{
		ActorId:     req.GetReq().GetAuth().GetActorId(),
		PhoneNumber: req.GetPhoneNumber(),
	})
	if rpcErr := epifigrpc.RPCError(discoverUansResp, err); rpcErr != nil && !discoverUansResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "failure in uan discovery", zap.Error(rpcErr))
		return &feEpfPb.DiscoverUANsResponse{
			RespHeader: &feHeaderPb.ResponseHeader{
				Status:    rpc.StatusInternal(),
				ErrorView: FullScreenErrorViewSomethingWentWrongWithRetry(discoverUanFailureMsg, "Close", "Retry", failureDeeplink, epfConfirmPhoneScreenDl),
			},
			RedirectionDeeplink: nil,
		}, nil
	}

	getUansResp, err := s.epfClient.GetUANAccounts(ctx, &beEpfPb.GetUANAccountsRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
	})
	if rpcErr := epifigrpc.RPCError(getUansResp, err); rpcErr != nil && !getUansResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "failed to get uan accounts for actor", zap.Error(rpcErr))
		return &feEpfPb.DiscoverUANsResponse{
			RespHeader: &feHeaderPb.ResponseHeader{
				Status:    rpc.StatusInternal(),
				ErrorView: BottomSheetErrorViewSomethingWentWrong(failureDeeplink),
			},
		}, nil
	}

	switch {
	case len(getUansResp.GetUanAccounts()) == 0 || getUansResp.GetStatus().IsRecordNotFound():
		logger.Info(ctx, "no uan found for actor with given phone number")
		uanEnabled, uanErr := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_MANUAL_UAN_EPF_FLOW).WithActorId(req.GetReq().GetAuth().GetActorId()))
		if uanErr != nil {
			return nil, fmt.Errorf("error in checking manual uan feature is enabled: %w", uanErr)
		}

		epfConfirmUANScreen := s.deeplinkBuilder.EPFConfirmUanScreen(req.GetEpfImportSessionId())

		return &feEpfPb.DiscoverUANsResponse{
			RespHeader: &feHeaderPb.ResponseHeader{
				Status:    rpc.StatusRecordNotFound(),
				ErrorView: s.FullScreenErrorViewNoUanLinkedWithPhone(req.GetPhoneNumber(), failureDeeplink, epfConfirmUANScreen, uanEnabled),
			},
		}, nil
	case len(getUansResp.GetUanAccounts()) == 1:
		// redirect to otp screen directly
		otpScreenOptions := deeplink_builder.GetOtpScreenOptionsForSendingOtp(getUansResp.GetUanAccounts()[0].GetUanNumber(), "", req.GetEpfImportSessionId(), false)
		return &feEpfPb.DiscoverUANsResponse{
			RespHeader: &feHeaderPb.ResponseHeader{
				Status: rpc.StatusOk(),
			},
			RedirectionDeeplink: &deeplinkPb.Deeplink{
				Screen:          deeplinkPb.Screen_EPF_PASSBOOK_IMPORT_OTP_SCREEN,
				ScreenOptionsV2: otpScreenOptions,
			},
		}, nil
	default:
		// redirect to uan connect screen
		uanListScreenDeeplink := deeplink_builder.GetEpfUanListScreenDeeplink(req.GetEpfImportSessionId())
		return &feEpfPb.DiscoverUANsResponse{
			RespHeader: &feHeaderPb.ResponseHeader{
				Status: rpc.StatusOk(),
			},
			RedirectionDeeplink: uanListScreenDeeplink,
		}, nil
	}
}

func (s *Service) checkAndCreateUserConsentIfRequired(ctx context.Context, req *header.RequestHeader) error {
	fetchConsentResp, err := s.consentClient.FetchConsent(ctx, &consentPb.FetchConsentRequest{
		ConsentType: consentPb.ConsentType_EPF_TNC,
		ActorId:     req.GetAuth().GetActorId(),
		Owner:       commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if rpcErr := epifigrpc.RPCError(fetchConsentResp, err); rpcErr != nil && !fetchConsentResp.GetStatus().IsRecordNotFound() {
		return fmt.Errorf("failed to check for actor if consent exists : %w", rpcErr)
	}
	if !fetchConsentResp.GetStatus().IsRecordNotFound() {
		return nil
	}

	// create a new epf consent for user since no consent exists
	recordConsentsResp, recordConsentErr := s.consentClient.RecordConsents(ctx, &consentPb.RecordConsentsRequest{
		Consents: []*consentPb.ConsentRequestInfo{
			{
				ConsentType: consentPb.ConsentType_EPF_TNC,
			},
		},
		ActorId: req.GetAuth().GetActorId(),
		Device:  req.GetAuth().GetDevice(),
		Owner:   commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if rpcErr := epifigrpc.RPCError(recordConsentsResp, recordConsentErr); rpcErr != nil {
		return fmt.Errorf("failed to record user epf consent : %w", rpcErr)
	}
	return nil
}
