package epf

import (
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"

	"github.com/epifi/gamma/api/frontend/deeplink"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/errors"
)

const (
	lightningIconUrl    = "https://epifi-icons.pointz.in/epf/thunder-cloud.png"
	uansNotFoundIconUrl = "https://epifi-icons.pointz.in/epf/uan-not-found.png"
)

func BottomSheetErrorViewSomethingWentWrong(deeplink *deeplinkPb.Deeplink) *errors.ErrorView {
	return &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errors.BottomSheetErrorView{
				Title:    "Something went wrong",
				Subtitle: "We encountered an error while connecting to your UAN. Please try again in some time",
				Ctas: []*errors.CTA{
					{
						Type:         errors.CTA_CUSTOM,
						Text:         "Ok, got it",
						Action:       deeplink,
						DisplayTheme: errors.CTA_SECONDARY,
					},
				},
			},
		},
	}
}

func FullScreenErrorViewSomethingWentWrongWithRetry(subTitle string, gotItText string, retryText string,
	gotItAction, retryAction *deeplinkPb.Deeplink) *errors.ErrorView {

	fullScreenErrView := &errors.FullScreenErrorView{
		ImageUrl: lightningIconUrl,
		Title:    "Something went wrong",
		Subtitle: subTitle,
	}
	if gotItAction != nil {
		fullScreenErrView.Ctas = append(fullScreenErrView.Ctas, &errors.CTA{
			Type:         errors.CTA_CUSTOM,
			Text:         gotItText,
			Action:       gotItAction,
			DisplayTheme: errors.CTA_SECONDARY,
		})
	}
	if retryAction != nil {
		fullScreenErrView.Ctas = append(fullScreenErrView.Ctas, &errors.CTA{
			Type:         errors.CTA_RETRY,
			Text:         retryText,
			Action:       retryAction,
			DisplayTheme: errors.CTA_PRIMARY,
		})
	}
	return &errors.ErrorView{
		Type: errors.ErrorViewType_FULL_SCREEN,
		Options: &errors.ErrorView_FullScreenErrorView{
			FullScreenErrorView: fullScreenErrView,
		},
	}
}

func FullScreenErrorViewSomethingWentWrongWithoutRetry(subTitle string, gotItText string, gotItAction *deeplinkPb.Deeplink) *errors.ErrorView {
	fullScreenErrView := &errors.FullScreenErrorView{
		ImageUrl: lightningIconUrl,
		Title:    "Something went wrong",
		Subtitle: subTitle,
	}
	if gotItAction != nil {
		fullScreenErrView.Ctas = append(fullScreenErrView.Ctas, &errors.CTA{
			Type:         errors.CTA_CUSTOM,
			Text:         gotItText,
			Action:       gotItAction,
			DisplayTheme: errors.CTA_PRIMARY,
		})
	}
	return &errors.ErrorView{
		Type: errors.ErrorViewType_FULL_SCREEN,
		Options: &errors.ErrorView_FullScreenErrorView{
			FullScreenErrorView: fullScreenErrView,
		},
	}
}

func (s *Service) FullScreenErrorViewNoUanLinkedWithPhone(phoneNumber *commontypes.PhoneNumber, deeplink *deeplinkPb.Deeplink, epfConfirmUANScreen *deeplinkPb.Deeplink, uanEnabled bool) *errors.ErrorView {
	okGotIt := &errors.CTA{
		Type:         errors.CTA_CUSTOM,
		Text:         "Ok, Exit",
		Action:       deeplink,
		DisplayTheme: errors.CTA_SECONDARY,
	}

	manualUan := &errors.CTA{
		Type:         errors.CTA_CUSTOM,
		Text:         "Add UAN manually",
		Action:       epfConfirmUANScreen,
		DisplayTheme: errors.CTA_PRIMARY,
	}

	ctas := []*errors.CTA{okGotIt}
	if uanEnabled {
		ctas = append(ctas, manualUan)
	}

	return &errors.ErrorView{
		Type: errors.ErrorViewType_FULL_SCREEN,
		Options: &errors.ErrorView_FullScreenErrorView{
			FullScreenErrorView: &errors.FullScreenErrorView{
				ImageUrl: uansNotFoundIconUrl,
				Title: fmt.Sprintf("We were unable to find any UANs linked to + %d %d",
					phoneNumber.GetCountryCode(), phoneNumber.GetNationalNumber()),
				Subtitle: "Please check if this mobile number is registered with your EPFO account.",
				Ctas:     ctas,
			},
		},
	}
}

func FullScreenErrorViewV2ActivateUan(subTitle string, gotItAction *deeplinkPb.Deeplink) *errors.ErrorView {
	okGotIt := &deeplink.Cta{
		Type:         deeplink.Cta_CUSTOM,
		Text:         "Ok, got it",
		DisplayTheme: deeplink.Cta_PRIMARY,
		Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		Deeplink:     gotItAction,
	}
	epfo := &deeplink.Cta{
		Type:         deeplink.Cta_CUSTOM,
		Text:         "Take to EPFO",
		DisplayTheme: deeplink.Cta_SECONDARY,
		Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEB_PAGE,
			ScreenOptions: &deeplinkPb.Deeplink_WebPageScreenOptions{
				WebPageScreenOptions: &deeplinkPb.WebpageScreenOptions{
					WebpageTitle: "EPFO activate UAN",
					WebpageUrl:   "https://unifiedportal-mem.epfindia.gov.in/memberinterface/",
				},
			},
		},
	}

	fullScreenErrView := &errors.FullScreenErrorViewV2{
		Image:           commontypes.GetVisualElementFromUrlHeightAndWidth(lightningIconUrl, 150, 150),
		Title:           commontypes.GetTextFromStringFontColourFontStyle("Something went wrong", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L),
		Subtitle:        commontypes.GetHtmlText(subTitle).WithAlignment(commontypes.Text_ALIGNMENT_LEFT),
		BackgroundColor: colors.ColorDarkBase,
		Ctas:            []*deeplink.Cta{epfo, okGotIt},
	}

	return &errors.ErrorView{
		Type: errors.ErrorViewType_FULL_SCREEN_V2,
		Options: &errors.ErrorView_FullScreenErrorViewV2{
			FullScreenErrorViewV2: fullScreenErrView,
		},
	}
}

func GetFullScreenErrorView(iconUrl, title, subtitle string, gotItAction *deeplinkPb.Deeplink) *errors.ErrorView {
	okGotIt := &deeplink.Cta{
		Type:         deeplink.Cta_CUSTOM,
		Text:         "Ok, got it",
		DisplayTheme: deeplink.Cta_PRIMARY,
		Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		Deeplink:     gotItAction,
	}

	fullScreenErrView := &errors.FullScreenErrorViewV2{
		Image:           commontypes.GetVisualElementFromUrlHeightAndWidth(iconUrl, 200, 200),
		Title:           commontypes.GetTextFromStringFontColourFontStyle(title, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L),
		Subtitle:        commontypes.GetTextFromStringFontColourFontStyle(subtitle, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_S).WithAlignment(commontypes.Text_ALIGNMENT_CENTER),
		BackgroundColor: colors.ColorDarkBase,
		Ctas:            []*deeplink.Cta{okGotIt},
	}

	return &errors.ErrorView{
		Type: errors.ErrorViewType_FULL_SCREEN_V2,
		Options: &errors.ErrorView_FullScreenErrorViewV2{
			FullScreenErrorViewV2: fullScreenErrView,
		},
	}
}
