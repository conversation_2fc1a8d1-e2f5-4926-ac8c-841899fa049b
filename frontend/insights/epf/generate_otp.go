package epf

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/deeplink"
	feHeaderPb "github.com/epifi/gamma/api/frontend/header"
	feEpfPb "github.com/epifi/gamma/api/frontend/insights/epf"
	beEpfPb "github.com/epifi/gamma/api/insights/epf"
	typesPb "github.com/epifi/gamma/api/typesv2"
	typesUi "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/pkg/feature/release"
)

func (s *Service) GenerateOtp(ctx context.Context, req *feEpfPb.GenerateOtpRequest) (*feEpfPb.GenerateOtpResponse, error) {
	epfImportSession := s.getEpfImportSessionDetails(ctx, req.GetReq().GetAuth().GetActorId(), req.GetEpfImportSessionId())
	if s.config.InsightsParams().EpfConfig().DisableEpfPassbookOtpFlow() {
		logger.Info(ctx, "epf passbook otp flow is disabled due to high otp failure rate from source")
		return getEpfDowntimeResponse(epfImportSession.GetEpfImportExitDeeplink().GetFailureDeeplink())
	}
	// if isConsentTaken is true then only we will check or create new consent for actor id
	if req.GetIsConsentTaken() {
		// check if user's epf consent exists, if not create a new consent
		if checkConsentErr := s.checkAndCreateUserConsentIfRequired(ctx, req.GetReq()); checkConsentErr != nil {
			logger.Error(ctx, "failure in consent check-create stage", zap.Error(checkConsentErr))
			return &feEpfPb.GenerateOtpResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status:    rpcPb.StatusInternal(),
					ErrorView: BottomSheetErrorViewSomethingWentWrong(epfImportSession.GetEpfImportExitDeeplink().GetFailureDeeplink()),
				},
			}, nil
		}
	}

	genOtpResp, err := s.epfClient.GenerateOtp(ctx, &beEpfPb.GenerateOtpRequest{
		ActorId:            req.GetReq().GetAuth().GetActorId(),
		UanNumber:          req.GetUanNumber(),
		ClientRequestId:    req.GetClientReqId(),
		EpfImportSessionId: req.GetEpfImportSessionId(),
	})
	// TODO: handle case of resource exhausted when user attempts otp submission 3 times
	// TODO: this will require a new dao (req initiated count in last x time duration) and corresponding be rpc
	if rpcErr := epifigrpc.RPCError(genOtpResp, err); rpcErr != nil && !isStatusNoAccountAttached(genOtpResp) {
		logger.Error(ctx, "failure in be generate otp", zap.Error(rpcErr))
		return getRpcResponseWithFailureFooterMsg()
	}

	// if no account is attached to the given UAN, then we will show a different message
	if isStatusNoAccountAttached(genOtpResp) {
		logger.Info(ctx, "failure in verifying otp due to epf passbook mismatch status")
		return s.getErrorResponseForNoAccountAttached(ctx, epfImportSession.GetEpfImportExitDeeplink().GetFailureDeeplink())
	}

	return &feEpfPb.GenerateOtpResponse{
		RespHeader: &feHeaderPb.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		InfoText: s.getInfoTextOnOtpScreen(genOtpResp.GetMaskedPhoneNumber(), req.GetUanNumber()),
	}, nil
}

func (s *Service) getInfoTextOnOtpScreen(maskedPhoneNumber string, uanNumber string) *typesUi.IconTextComponent {
	text := ""
	if len(maskedPhoneNumber) == 10 {
		maskedFirstHalf := strings.ReplaceAll(maskedPhoneNumber[:5], "X", "•")
		maskedSecondHalf := strings.ReplaceAll(maskedPhoneNumber[5:], "X", "•")
		text = fmt.Sprintf("Enter the OTP sent to +91 %s %s to connect UAN %s", maskedFirstHalf,
			maskedSecondHalf, uanNumber)
	} else {
		// TODO: add some alerting for these cases
		text = fmt.Sprintf("Enter the OTP sent to your UAN registered number to connect UAN %s", uanNumber)
	}
	return &typesUi.IconTextComponent{
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: text,
				},
			},
		},
	}
}

func isStatusNoAccountAttached(res *beEpfPb.GenerateOtpResponse) bool {
	return res.GetStatus().GetCode() == uint32(beEpfPb.GenerateOtpResponse_NO_ACCOUNT_ATTACHED)
}

func (s *Service) getErrorResponseForNoAccountAttached(ctx context.Context, failureDeeplink *deeplink.Deeplink) (*feEpfPb.GenerateOtpResponse, error) {
	isEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_EPF_GENERIC_ERROR_SCREEN).WithActorId(""))
	if err != nil {
		return &feEpfPb.GenerateOtpResponse{
			RespHeader: &feHeaderPb.ResponseHeader{
				Status:    rpcPb.StatusInternal(),
				ErrorView: BottomSheetErrorViewSomethingWentWrong(failureDeeplink),
			},
		}, nil
	}
	if isEnabled {
		return &feEpfPb.GenerateOtpResponse{
			RespHeader: &feHeaderPb.ResponseHeader{
				Status:    rpcPb.NewStatusWithoutDebug(uint32(feEpfPb.GenerateOtpResponse_NO_ACCOUNT_ATTACHED), "no epf account attached with given uan number"),
				ErrorView: FullScreenErrorViewV2ActivateUan(activateUanHtml, failureDeeplink),
			},
		}, nil
	}
	return getRpcResponseWithFailureFooterMsg()
}

func getRpcResponseWithFailureFooterMsg() (*feEpfPb.GenerateOtpResponse, error) {
	return &feEpfPb.GenerateOtpResponse{
		RespHeader: &feHeaderPb.ResponseHeader{
			Status: rpcPb.StatusInternal(),
		},
		FailureFooterText: &typesUi.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor: "#AA301F",
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "OTP could not be generated.",
					},
				},
			},
		},
	}, nil
}

func getEpfDowntimeResponse(failureDeeplink *deeplink.Deeplink) (*feEpfPb.GenerateOtpResponse, error) {
	return &feEpfPb.GenerateOtpResponse{
		RespHeader: &feHeaderPb.ResponseHeader{
			Status:    rpcPb.NewStatusWithoutDebug(uint32(feEpfPb.GenerateOtpResponse_SERVICE_UNAVAILABLE), "epf service unavailable from source"),
			ErrorView: GetFullScreenErrorView("https://epifi-icons.pointz.in/insights/secrets/red-alert", "EPF Services Unavailable", "We’re currently unable to fetch your EPF details due to a disruption in the service. The government portal is undergoing maintenance.\n Please try again after a few days.", failureDeeplink),
		},
	}, nil
}
