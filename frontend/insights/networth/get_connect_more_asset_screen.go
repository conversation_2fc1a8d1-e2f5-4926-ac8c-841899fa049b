package networth

import (
	"context"
	"fmt"

	moneyPb "github.com/epifi/be-common/pkg/money"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/feature/release"

	"github.com/epifi/gamma/frontend/insights/networth/data_fetcher"

	networthBePb "github.com/epifi/gamma/api/insights/networth"

	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/insights/networth/common"

	enumsPb "github.com/epifi/gamma/api/frontend/insights/networth/enums"
	feNetworthUi "github.com/epifi/gamma/api/frontend/insights/networth/ui"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	feErrors "github.com/epifi/gamma/api/frontend/errors"
	networthDeeplink "github.com/epifi/gamma/frontend/insights/networth/deeplink"

	networthCommon "github.com/epifi/gamma/frontend/insights/networth/common"
	"github.com/epifi/gamma/frontend/insights/networth/generator/widget"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"

	"github.com/epifi/gamma/api/frontend/header"

	feNetworthPb "github.com/epifi/gamma/api/frontend/insights/networth"
)

const (
	cardImage = "https://epifi-icons.pointz.in/wb-landing/card_image"
)

func (s *Service) GetConnectMoreAssetsScreen(ctx context.Context, req *feNetworthPb.GetConnectMoreAssetsScreenRequest) (*feNetworthPb.GetConnectMoreAssetsScreenResponse, error) {
	wealthBuilderLandingSections, err := s.getWealthBuilderLandingSection(ctx, req)
	if err != nil {
		logger.Error(ctx, "failed to get wealth builder landing section: ", zap.Error(err))
		return s.getErrorResponse("failed to get wealth builder landing section"), nil
	}

	return &feNetworthPb.GetConnectMoreAssetsScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Title:                        commontypes.GetTextFromStringFontColourFontStyle("Link all your finances in one place & unlock  insights", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L),
		CardImage:                    commontypes.GetVisualElementImageFromUrl(cardImage).WithProperties(&commontypes.VisualElementProperties{Width: 332, Height: 80}),
		WealthBuilderLandingSections: wealthBuilderLandingSections,
	}, nil
}

func (s *Service) getWealthBuilderLandingSection(ctx context.Context, req *feNetworthPb.GetConnectMoreAssetsScreenRequest) ([]*feNetworthUi.WealthBuilderLandingSection, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	networthDashboardConfig, err := s.networthConfig.GetNetworthDashboardConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to get networth dashboard config: %w", err)
	}

	wbDashboardLiabilitiesEnabled, wbDashboardLiabilitiesEnabledErr := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_WB_DASHBOARD_LIABILITIES).WithActorId(actorId))
	if wbDashboardLiabilitiesEnabledErr != nil {
		logger.Error(ctx, "failed to evaluate wb dashboard liabilities enabled")
	}

	var assetWidgets, liabilityWidgets []*feNetworthUi.WidgetV2
	// Collecting widgets for assets
	for _, assetType := range req.GetRequestParams().GetAssetTypes() {
		widgetData := s.getWidgetDataForAssetType(assetType, networthDashboardConfig)
		wbLandingWidget, widgetErr := s.getWbLandingWidget(ctx, actorId, widgetData)
		if widgetErr != nil {
			return nil, fmt.Errorf("failed to generate wealth builder zero widget for category: %v, error: %w", widgetData, widgetErr)
		}
		if wbLandingWidget != nil {
			assetWidgets = append(assetWidgets, wbLandingWidget)
		}
	}

	// Collecting widgets for liabilities
	for _, liabilityType := range req.GetRequestParams().GetLiabilityTypes() {
		widgetData := s.getWidgetDataForLiabilityType(liabilityType, networthDashboardConfig)
		wbLandingWidget, widgetErr := s.getWbLandingWidget(ctx, actorId, widgetData)
		if widgetErr != nil {
			return nil, fmt.Errorf("failed to generate wealth builder zero widget for category: %v, error: %w", widgetData, widgetErr)
		}
		if wbLandingWidget != nil {
			liabilityWidgets = append(liabilityWidgets, wbLandingWidget)
		}
	}

	// creating WB Landing sections
	var wealthBuilderLandingSections []*feNetworthUi.WealthBuilderLandingSection
	for _, section := range networthDashboardConfig.GetSections() {
		if section.GetSectionType().String() == enumsPb.NetworthSectionType_NETWORTH_SECTION_TYPE_ASSETS.String() && len(assetWidgets) > 0 {
			assetSection := &feNetworthUi.WealthBuilderLandingSection{
				Id:          section.GetWealthLandingSectionId(),
				SectionType: enumsPb.NetworthSectionType_NETWORTH_SECTION_TYPE_ASSETS,
				SectionHeaderGenericState: &feNetworthUi.SectionHeaderGenericState{
					Title: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.AssetsSectionHeader, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS)),
				},
				Widgets: assetWidgets,
			}
			wealthBuilderLandingSections = append(wealthBuilderLandingSections, assetSection)
		}
		if section.GetSectionType().String() == enumsPb.NetworthSectionType_NETWORTH_SECTION_TYPE_LIABILITIES.String() && len(liabilityWidgets) > 0 && wbDashboardLiabilitiesEnabled {
			liabilitySection := &feNetworthUi.WealthBuilderLandingSection{
				Id:          section.GetWealthLandingSectionId(),
				SectionType: enumsPb.NetworthSectionType_NETWORTH_SECTION_TYPE_LIABILITIES,
				SectionHeaderGenericState: &feNetworthUi.SectionHeaderGenericState{
					Title: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(common.LoanSectionHeader, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS)),
				},
				Widgets: liabilityWidgets,
			}
			wealthBuilderLandingSections = append(wealthBuilderLandingSections, liabilitySection)
		}
	}
	return wealthBuilderLandingSections, nil
}

func (s *Service) getWidgetDataForAssetType(assetType string, netWorthDashboardConfig *feNetworthPb.NetWorthDashboardConfig) *widget.WidgetDataDetails {
	widgetData := s.createWidgetData()

	for _, section := range netWorthDashboardConfig.GetSections() {
		if section.GetSectionType().String() == enumsPb.NetworthSectionType_NETWORTH_SECTION_TYPE_ASSETS.String() {
			for _, widgetDetails := range section.GetWidgets() {
				assetEnum := networthBePb.AssetType(networthBePb.AssetType_value[assetType])
				if widgetDetails.GetCategory() == common.AssetTypeToCategoryMap[assetEnum] {
					widgetData.WidgetData.Name = widgetDetails.GetCategory()
					widgetData.WidgetConfig = widgetDetails
					widgetData.NetWorthCategoryType = common.AssetTypeToCategoryMap[assetEnum]
					break
				}
			}
		}
	}
	return widgetData
}

func (s *Service) getWidgetDataForLiabilityType(liabilityType string, netWorthDashboardConfig *feNetworthPb.NetWorthDashboardConfig) *widget.WidgetDataDetails {
	widgetData := s.createWidgetData()

	for _, section := range netWorthDashboardConfig.GetSections() {
		if section.GetSectionType().String() == enumsPb.NetworthSectionType_NETWORTH_SECTION_TYPE_LIABILITIES.String() && liabilityType != "" {
			for _, widgetDetails := range section.GetWidgets() {
				liabilityEnum := networthBePb.LiabilityType(networthBePb.LiabilityType_value[liabilityType])
				if widgetDetails.GetCategory() == common.LiabilityTypeToCategoryMap[liabilityEnum] {
					widgetData.WidgetData.Name = widgetDetails.GetCategory()
					widgetData.WidgetConfig = widgetDetails
					widgetData.NetWorthCategoryType = common.LiabilityTypeToCategoryMap[liabilityEnum]
					break
				}
			}
		}
	}
	return widgetData
}

func (s *Service) createWidgetData() *widget.WidgetDataDetails {
	widgetData := &widget.WidgetDataDetails{}
	widgetData.WidgetWidth = networthCommon.SecondaryWidgetWidth
	widgetData.WidgetState = feNetworthPb.NetworthWidgetState_NETWORTH_WIDGET_STATE_ACTIVE
	widgetData.WidgetData = &data_fetcher.CategoryData{}
	widgetData.WidgetData.Value = moneyPb.ZeroINR().GetPb()
	widgetData.WidgetData.ComputationStatus = networthBePb.ComputationStatus_COMPUTATION_STATUS_NOT_FOUND
	widgetData.NetWorthCategoryStatus = feNetworthPb.NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_UNINITIALIZED
	return widgetData
}

func (s *Service) getWbLandingWidget(ctx context.Context, actorId string, widgetData *widget.WidgetDataDetails) (*feNetworthUi.WidgetV2, error) {
	widgetGenFactory, genErr := s.widgetGeneratorFactory.GetGenerator(widgetData.WidgetConfig)
	if genErr != nil {
		return nil, fmt.Errorf("failed to get wealth builder widget generator for category: %s, err: %w", widgetData.WidgetConfig.GetCategory(), genErr)
	}
	wbLandingWidget, widgetErr := widgetGenFactory.GenerateZeroStateWealthBuilderLandingWidget(ctx, actorId, widgetData)
	if widgetErr != nil {
		return nil, fmt.Errorf("failed to generate wealth builder zero widget for category: %v, error: %w", widgetData, widgetErr)
	}
	return wbLandingWidget, nil
}

func (s *Service) getErrorResponse(errStr string) *feNetworthPb.GetConnectMoreAssetsScreenResponse {
	return &feNetworthPb.GetConnectMoreAssetsScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusInternalWithDebugMsg(errStr),
			// add error view
			ErrorView: &feErrors.ErrorView{
				Type: feErrors.ErrorViewType_FULL_SCREEN,
				Options: &feErrors.ErrorView_FullScreenErrorView{
					FullScreenErrorView: &feErrors.FullScreenErrorView{
						Title:    "Something went wrong",
						Subtitle: "We were unable to load your island. Please try again in some time.",
						Ctas: []*feErrors.CTA{
							{
								Text:         "Ok, got it",
								Action:       networthDeeplink.HomeDeeplink(),
								DisplayTheme: feErrors.CTA_PRIMARY,
							},
						},
						ImageUrl: "https://epifi-icons.pointz.in/networth/thundercloud.png",
					},
				},
			},
		},
	}
}
