package common

const (
	AssetImportInProgressTitle         = "Running some numbers"
	AssetImportFailureTitle            = "Uh-oh, something went wrong while connecting your account"
	AssetImportFooterText              = "3.5 million users trust Fi to save, pay, track & invest their money"
	AssetImportFailureMsg              = "Update failed"
	AssetImportMfSuccessTitle          = "Congrats! Mutual funds added to your Net Worth"
	NetWorth                           = "NET WORTH"
	NetWorthUpdated                    = "Net Worth updated"
	RupeeSymbol                        = "₹"
	FundsAddedText                     = "%v funds"
	EpfAccountsAddedText               = " + %v EPF accounts"
	FundsRefreshedText                 = "%v funds refreshed"
	WealthBuilderComponentsBorderColor = "#4DDCF3EE" // Border color used for wealth builder dashboard and portfolio tracker components

	// Asset import lottie detail frames
	InProgressStartFrame              = 1
	InProgressEndFrame                = 60
	SuccessRollingAnimationStartFrame = 61
	SuccessRollingAnimationEndFrame   = 135
	SuccessNonRollingStartFrame       = 136
	SuccessNonRollingEndFrame         = 175
	UpdatingStartFrame                = 176
	UpdatingEndFrame                  = 220
	FailedStateStartFrame             = 221
	FailedStateEndFrame               = 265

	// wealth builder landing page constants
	AssetWidgetVisibleLimit  = 5
	YourWealth               = "Your wealth"
	ZeroStateTitle           = "The smartest way to maximise your wealth"
	HowItWorksTitle          = "How it works"
	AddMore                  = "Add more"
	MagicImport              = "Magic Import 🪄"
	ExpandText               = "More assets"
	CollapseText             = "Close view"
	LiabilitiesSectionHeader = "Your loans"
	Refresh                  = "Refresh"
	MfSummaryText            = "Get Mutual Funds summary"
	DailyReportText          = "Daily Portfolio Tracker"
	WeeklyReportText         = "Weekly Portfolio Tracker"
	TrackBankBalancesText    = "Track bank balances"
	OneDayText               = "1 Day"
	OneWeekText              = "1 Week"

	// tags for wealth builder widget
	AnalysingTag = "ANALYSING..."
	AddTag       = "ADD"
	InsightsTag  = "VIEW"

	// width of wealth builder widget
	PrimaryWidgetWidth   = 50
	SecondaryWidgetWidth = 33.33

	// add more assets screen
	AssetsSectionHeader = "CHOOSE AN ASSET TO CONNECT"
	LoanSectionHeader   = "LOANS & LIABILITIES"
)
