package networth

// nolint
import (
	"flag"
	"os"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/money"

	mockCa "github.com/epifi/gamma/api/connected_account/mocks"
	mockConsent "github.com/epifi/gamma/api/consent/mocks"
	mockCreditReport "github.com/epifi/gamma/api/creditreportv2/mocks"
	epfMock "github.com/epifi/gamma/api/insights/epf/mocks"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/insights/networth/mocks"
	modelPb "github.com/epifi/gamma/api/insights/networth/model"
	mockMfExternal "github.com/epifi/gamma/api/investment/mutualfund/external/mocks"
	typesPb "github.com/epifi/gamma/api/typesv2"
	mockDeeplinkBuilder "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder/mocks"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth_refresh"
	mockOnb "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/frontend/config"
	genConf "github.com/epifi/gamma/frontend/config/genconf"
	networthConfig "github.com/epifi/gamma/frontend/insights/networth/config"
	mockDataFetcher "github.com/epifi/gamma/frontend/insights/networth/data_fetcher/mocks"
	mockSection "github.com/epifi/gamma/frontend/insights/networth/generator/section/mocks"
	mockVisualisation "github.com/epifi/gamma/frontend/insights/networth/generator/visualisation/mocks"
	mockAssetDashboard "github.com/epifi/gamma/frontend/insights/networth/manual_forms/asset_dashboard/mocks"
	mockFormBuilder "github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder/mocks"
	mockInputValidator "github.com/epifi/gamma/frontend/insights/networth/manual_forms/inputvalidator/mocks"
	"github.com/epifi/gamma/frontend/test"
	mockTime "github.com/epifi/be-common/pkg/datetime/mocks"
	releaseMocks "github.com/epifi/gamma/pkg/feature/release/mocks"
)

var (
	conf    *config.Config
	dynConf *genConf.Config
)

type fields struct {
	networthConfig                 *networthConfig.Config
	networthClient                 *mocks.MockNetWorthClient
	consentClient                  *mockConsent.MockConsentClient
	sectionGenerator               *mockSection.MockIGenerator
	dataFetcher                    *mockDataFetcher.MockNetWorthDataFetcher
	visualisationGeneratorFactory  *mockVisualisation.MockIGeneratorFactory
	formBuilderFactory             *mockFormBuilder.MockFormBuilderFactory
	formInputValidator             *mockInputValidator.MockFormInputValidator
	time                           *mockTime.MockTime
	assetDashboardGeneratorFactory *mockAssetDashboard.MockIAssetDashboardGeneratorFactory
	onbClient                      *mockOnb.MockOnboardingClient
	connectedAccountClient         *mockCa.MockConnectedAccountClient
	deeplinkBuilder                *mockDeeplinkBuilder.MockIDeeplinkBuilder
	epfClient                      *epfMock.MockEpfClient
	mfExternalOrdersClient         *mockMfExternal.MockMFExternalOrdersClient
	creditReportClient             *mockCreditReport.MockCreditReportManagerClient
	mockReleaseEvaluator           *releaseMocks.MockIEvaluator
}

func initFields(ctrl *gomock.Controller) *fields {
	return &fields{
		networthConfig:                 networthConfig.LoadNetWorthConfig(dynConf),
		networthClient:                 mocks.NewMockNetWorthClient(ctrl),
		consentClient:                  mockConsent.NewMockConsentClient(ctrl),
		sectionGenerator:               mockSection.NewMockIGenerator(ctrl),
		dataFetcher:                    mockDataFetcher.NewMockNetWorthDataFetcher(ctrl),
		visualisationGeneratorFactory:  mockVisualisation.NewMockIGeneratorFactory(ctrl),
		formBuilderFactory:             mockFormBuilder.NewMockFormBuilderFactory(ctrl),
		formInputValidator:             mockInputValidator.NewMockFormInputValidator(ctrl),
		time:                           mockTime.NewMockTime(ctrl),
		assetDashboardGeneratorFactory: mockAssetDashboard.NewMockIAssetDashboardGeneratorFactory(ctrl),
		onbClient:                      mockOnb.NewMockOnboardingClient(ctrl),
		connectedAccountClient:         mockCa.NewMockConnectedAccountClient(ctrl),
		deeplinkBuilder:                mockDeeplinkBuilder.NewMockIDeeplinkBuilder(ctrl),
		epfClient:                      epfMock.NewMockEpfClient(ctrl),
		mfExternalOrdersClient:         mockMfExternal.NewMockMFExternalOrdersClient(ctrl),
		creditReportClient:             mockCreditReport.NewMockCreditReportManagerClient(ctrl),
		mockReleaseEvaluator:           releaseMocks.NewMockIEvaluator(ctrl),
	}
}

func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, dynConf, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

var (
	assetRefreshInfo1 = []*networth_refresh.AssetRefreshInfo{
		{
			AssetName: modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF.String(),
			RefreshId: "uan-1",
		},
		{
			AssetName: modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
		},
	}
	assetRefreshInfo2 = []*networth_refresh.AssetRefreshInfo{
		{
			AssetName: modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE.String(),
		},
		{
			AssetName: modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
		},
	}
	assetRefreshInfo3 = []*networth_refresh.AssetRefreshInfo{
		{
			AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE.String(),
			RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
		},
		{
			AssetName: modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
		},
	}
	assetRefreshInfo4 = []*networth_refresh.AssetRefreshInfo{
		{
			AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE.String(),
			RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
		},
		{
			AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
			RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
		},
		{
			AssetName: modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS.String(),
		},
	}
	assetRefreshInfo5 = []*networth_refresh.AssetRefreshInfo{
		{
			AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE.String(),
			RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
		},
		{
			AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
			RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
		},
		{
			AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF.String(),
			RefreshId:     "uan-1",
			RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
		},
	}

	instrumentRefreshSummary1 = []*networthPb.InstrumentRefreshDetails{
		{
			AssetName:         modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS,
			IsRefreshRequired: true,
			ManualAssetRefreshDetails: map[string]*networthPb.ManualAssetRefreshDetails{
				typesPb.InvestmentInstrumentType_REAL_ESTATE.String(): &networthPb.ManualAssetRefreshDetails{
					InvestmentDeclarations: []*modelPb.InvestmentDeclaration{
						{
							ExternalId:     "externalId-1",
							InstrumentType: typesPb.InvestmentInstrumentType_REAL_ESTATE,
							DeclarationDetails: &modelPb.OtherDeclarationDetails{
								Details: &modelPb.OtherDeclarationDetails_RealEstate{
									RealEstate: &modelPb.RealEstate{InvestmentName: "Farm Land",
										CurrentValue: &money.Money{
											CurrencyCode: "INR",
											Units:        1111,
										},
									},
								},
							},
						},
						{
							ExternalId:     "externalId-2",
							InstrumentType: typesPb.InvestmentInstrumentType_REAL_ESTATE,
							DeclarationDetails: &modelPb.OtherDeclarationDetails{
								Details: &modelPb.OtherDeclarationDetails_RealEstate{
									RealEstate: &modelPb.RealEstate{
										InvestmentName: "Garden",
										CurrentValue: &money.Money{
											CurrencyCode: "INR",
											Units:        2222,
										},
									},
								},
							},
						},
					},
				},
				typesPb.InvestmentInstrumentType_CASH.String(): &networthPb.ManualAssetRefreshDetails{
					InvestmentDeclarations: []*modelPb.InvestmentDeclaration{
						{
							ExternalId:     "externalId-3",
							InstrumentType: typesPb.InvestmentInstrumentType_CASH,
							DeclarationDetails: &modelPb.OtherDeclarationDetails{
								Details: &modelPb.OtherDeclarationDetails_Cash{
									Cash: &modelPb.Cash{
										CurrentAmount: &money.Money{
											CurrencyCode: "INR",
											Units:        3333,
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
)
