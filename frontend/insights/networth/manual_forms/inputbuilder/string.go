// nolint:dupl
package inputbuilder

import (
	wrappersPb "google.golang.org/protobuf/types/known/wrapperspb"

	networthPb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthEnumsPb "github.com/epifi/gamma/api/frontend/insights/networth/enums"
)

type StringBuilder struct {
	*networthPb.NetWorthManualFormInputComponent
}

func NewStringBuilder(title, placeholderText, fieldName string) *StringBuilder {
	component := networthPb.NewNetWorthManualFormInputComponent(fieldName, networthEnumsPb.NetworthManualFormInputStyle_NETWORTH_MANUAL_FORM_INPUT_STYLE_TEXT_FIELD)
	component.WithMandatoryDisplayTitle(title)
	component.WithMandatoryPlaceholderText(placeholderText)
	component.InputData = WithInputDataString(fieldName, 1)

	return &StringBuilder{NetWorthManualFormInputComponent: component}
}

func NewOptionalStringBuilder(title, placeholderText, fieldName string) *StringBuilder {
	component := networthPb.NewNetWorthManualFormInputComponent(fieldName, networthEnumsPb.NetworthManualFormInputStyle_NETWORTH_MANUAL_FORM_INPUT_STYLE_TEXT_FIELD)
	component.MakeOptional()
	component.WithDefaultTitle(title)
	component.WithDefaultPlaceholderText(placeholderText)
	component.InputData = WithInputDataString(fieldName, 0)

	return &StringBuilder{NetWorthManualFormInputComponent: component}
}

func (d *StringBuilder) WithFieldName(fieldName string) {

}

func (d *StringBuilder) WithValue(value string) {
	d.NetWorthManualFormInputComponent.GetInputData().GetSingleOption().GetInputValue().GetStringData().Data = &wrappersPb.StringValue{Value: value}
	d.NetWorthManualFormInputComponent.GetInputData().GetSingleOption().GetInputOptionData().GetInputValue().GetStringData().Data = &wrappersPb.StringValue{Value: value}
}

func (d *StringBuilder) WithNonEmptyValue(value string) {
	if value == "" {
		return
	}
	d.NetWorthManualFormInputComponent.GetInputData().GetSingleOption().GetInputValue().GetStringData().Data = &wrappersPb.StringValue{Value: value}
	d.NetWorthManualFormInputComponent.GetInputData().GetSingleOption().GetInputOptionData().GetInputValue().GetStringData().Data = &wrappersPb.StringValue{Value: value}
}

func (d *StringBuilder) WithValidation(validation *networthPb.StringType_StringValidation) {
	d.NetWorthManualFormInputComponent.GetInputData().GetSingleOption().GetInputValue().GetStringData().Validation = validation
	d.NetWorthManualFormInputComponent.GetInputData().GetSingleOption().GetInputOptionData().GetInputValue().GetStringData().Validation = validation
}

func (d *StringBuilder) Build() *networthPb.NetWorthManualFormInputComponent {
	return d.NetWorthManualFormInputComponent
}

func WithInputDataString(fieldName string, minLen int32) *networthPb.NetWorthManualInputData {
	return &networthPb.NetWorthManualInputData{
		FieldName: fieldName,
		DataType:  networthEnumsPb.NetworthManualFormInputDataType_NETWORTH_MANUAL_FORM_INPUT_DATA_TYPE_STRING,
		Input: &networthPb.NetWorthManualInputData_SingleOption{
			SingleOption: &networthPb.SingleInputOption{
				InputValue: &networthPb.InputOptionValue{
					Value: &networthPb.InputOptionValue_StringData{
						StringData: &networthPb.StringType{
							Validation: &networthPb.StringType_StringValidation{
								MinLen:               minLen,
								MaxLen:               50,
								ValidationFailureMsg: "This field should be in between 1-50 characters",
							},
						},
					},
				},
				InputOptionData: &networthPb.InputOptionData{
					InputValue: &networthPb.InputOptionValue{
						Value: &networthPb.InputOptionValue_StringData{
							StringData: &networthPb.StringType{
								Validation: &networthPb.StringType_StringValidation{
									MinLen:               minLen,
									MaxLen:               50,
									ValidationFailureMsg: "This field should be in between 1-50 characters",
								},
							},
						},
					},
				},
			},
		},
	}
}
