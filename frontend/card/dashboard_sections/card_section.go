// nolint: funlen,dupl
package dashboard_sections

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"

	timestampPb2 "google.golang.org/protobuf/types/known/timestamppb"

	"context"
	"fmt"
	"time"

	widgetUi "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	pkggenconf "github.com/epifi/be-common/pkg/frontend/app/genconf"

	cardPb "github.com/epifi/gamma/api/card"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	feCardPb "github.com/epifi/gamma/api/frontend/card"
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	orderActorActivityPb "github.com/epifi/gamma/api/order/actoractivity"
	externalPb "github.com/epifi/gamma/api/tiering/external"
	types "github.com/epifi/gamma/api/typesv2"
	dcScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/debitcard"
	"github.com/epifi/gamma/api/typesv2/ui"
	upiPb "github.com/epifi/gamma/api/upi"
	"github.com/epifi/gamma/card/control"
	"github.com/epifi/gamma/frontend/config/genconf"
	cardFePkg "github.com/epifi/gamma/frontend/pkg/debitcard"
	deeplink2 "github.com/epifi/gamma/pkg/deeplink"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
)

type CardSectionBuilder struct {
	cpClient                 cpPb.CardProvisioningClient
	conf                     *genconf.Card
	upiClient                upiPb.UPIClient
	orderActorActivityClient orderActorActivityPb.ActorActivityClient
	releaseEvaluator         release.IEvaluator
}

func NewCardSection(cpClient cpPb.CardProvisioningClient, conf *genconf.Card, upiClient upiPb.UPIClient, orderActorActivityClient orderActorActivityPb.ActorActivityClient, releaseEvaluator release.IEvaluator) *CardSectionBuilder {
	return &CardSectionBuilder{
		cpClient:                 cpClient,
		conf:                     conf,
		upiClient:                upiClient,
		orderActorActivityClient: orderActorActivityClient,
		releaseEvaluator:         releaseEvaluator,
	}
}

func (c *CardSectionBuilder) BuildSection(ctx context.Context, bReq *SectionBuilderRequest) (*SectionBuilderResponse, error) {
	switch {
	case bReq.CurrentCard.GetState() == cardPb.CardState_BLOCKED:
		return c.buildBlockStateCardSection(ctx, bReq)
	case bReq.CurrentCard.GetState() == cardPb.CardState_SUSPENDED:
		return c.buildFrozenStateCardSection(ctx, bReq)
	case bReq.CurrentCard.GetState() == cardPb.CardState_INITIATED:
		return c.buildCreationInProgressCardSection(ctx, bReq)
	case bReq.CurrentCard.GetState() == cardPb.CardState_CREATED:
		return c.buildCreatedStateCardSection(ctx, bReq)
	default:
		return c.buildDefaultStateCardSection(ctx, bReq)
	}
}

func (c *CardSectionBuilder) buildCreationInProgressCardSection(_ context.Context, req *SectionBuilderRequest) (*SectionBuilderResponse, error) {
	var cardBackground = getActiveCardBackground(req.CardDesignEnhancementEnabled)
	return &SectionBuilderResponse{
		CardSection: &feCardPb.TopHeaderSection{
			FiCardComponent: &feCardPb.TopHeaderSection_FiCardComponent{
				CardTypes: &feCardPb.TopHeaderSection_FiCardComponent_InactiveState{
					InactiveState: &feCardPb.TopHeaderSection_FiCardInactiveState{
						Background: cardBackground,
						PartnerLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/federalbank-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 60, Height: 30}).WithImageType(commontypes.ImageType_PNG),
						NetworkLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/visa-network-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 71, Height: 28}).WithImageType(commontypes.ImageType_PNG),
						FiLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/fi-bank-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 28, Height: 28}).WithImageType(commontypes.ImageType_PNG),
						CvvTitle:               commontypes.GetTextFromStringFontColourFontStyle("CVV", colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
						CvvValue:               commontypes.GetTextFromStringFontColourFontStyle("•••", colorSnow, commontypes.FontStyle_OVERLINE_3),
						ExpiryTitle:            commontypes.GetTextFromStringFontColourFontStyle("EXPIRY", colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
						ExpiryValue:            commontypes.GetTextFromStringFontColourFontStyle("••/••", colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
						StatusBannerBackground: widgetUi.GetBlockBackgroundColour(colorDarkGray),
						StatusBanner: &ui.IconTextComponent{
							Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Creating a new card. This may take some time.", colorSnow, commontypes.FontStyle_SUBTITLE_XS)},
							ContainerProperties: &ui.IconTextComponent_ContainerProperties{
								Height: 36,
							},
						},
					},
				},
			},
			CardActionCtas: c.getInactiveCardActionCtas(req),
			NudgesBanner:   nil,
		},
	}, nil
}

func (c *CardSectionBuilder) buildBlockStateCardSection(_ context.Context, req *SectionBuilderRequest) (*SectionBuilderResponse, error) {
	return &SectionBuilderResponse{
		CardSection: &feCardPb.TopHeaderSection{
			FiCardComponent: &feCardPb.TopHeaderSection_FiCardComponent{
				CardTypes: &feCardPb.TopHeaderSection_FiCardComponent_InactiveState{
					InactiveState: &feCardPb.TopHeaderSection_FiCardInactiveState{
						Background: widgetUi.GetBlockBackgroundColour(colorMetallicGray),
						PartnerLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/federalbank-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 60, Height: 30}).WithImageType(commontypes.ImageType_PNG),
						NetworkLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/visa-network-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 71, Height: 41}).WithImageType(commontypes.ImageType_PNG),
						FiLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/fi-bank-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 28, Height: 28}).WithImageType(commontypes.ImageType_PNG),
						CvvTitle:               commontypes.GetTextFromStringFontColourFontStyle("CVV", colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
						CvvValue:               commontypes.GetTextFromStringFontColourFontStyle("•••", colorSnow, commontypes.FontStyle_OVERLINE_3),
						ExpiryTitle:            commontypes.GetTextFromStringFontColourFontStyle("EXPIRY", colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
						ExpiryValue:            commontypes.GetTextFromStringFontColourFontStyle("••/••", colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
						StatusBannerBackground: widgetUi.GetBlockBackgroundColour(colorDarkGray),
						StatusBanner: &ui.IconTextComponent{
							Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Card permanently deactivated", colorSnow, commontypes.FontStyle_SUBTITLE_XS)},
							ContainerProperties: &ui.IconTextComponent_ContainerProperties{
								Height: 36,
							},
						},
					},
				},
			},
			CardActionCtas: c.getInactiveCardActionCtas(req),
			NudgesBanner:   nil,
		},
	}, nil
}

func (c *CardSectionBuilder) buildFrozenStateCardSection(_ context.Context, req *SectionBuilderRequest) (*SectionBuilderResponse, error) {
	var cardBackground = getActiveCardBackground(req.CardDesignEnhancementEnabled)
	return &SectionBuilderResponse{
		CardSection: &feCardPb.TopHeaderSection{
			FiCardComponent: &feCardPb.TopHeaderSection_FiCardComponent{
				CardTypes: &feCardPb.TopHeaderSection_FiCardComponent_InactiveState{
					InactiveState: &feCardPb.TopHeaderSection_FiCardInactiveState{
						Background: cardBackground,
						PartnerLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/federalbank-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 60, Height: 30}).WithImageType(commontypes.ImageType_PNG),
						NetworkLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/visa-network-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 71, Height: 28}).WithImageType(commontypes.ImageType_PNG),
						FiLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/fi-bank-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 28, Height: 28}).WithImageType(commontypes.ImageType_PNG),
						CvvTitle:               commontypes.GetTextFromStringFontColourFontStyle("CVV", colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
						CvvValue:               commontypes.GetTextFromStringFontColourFontStyle("•••", colorSnow, commontypes.FontStyle_OVERLINE_3),
						ExpiryTitle:            commontypes.GetTextFromStringFontColourFontStyle("EXPIRY", colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
						ExpiryValue:            commontypes.GetTextFromStringFontColourFontStyle("••/••", colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
						StatusBannerBackground: widgetUi.GetBlockBackgroundColour(colorDarkGray),
						StatusBanner: &ui.IconTextComponent{
							Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Card temporarily frozen", colorSnow, commontypes.FontStyle_SUBTITLE_XS)},
							ContainerProperties: &ui.IconTextComponent_ContainerProperties{
								Height: 36,
							},
						},
					},
				},
			},
			CardActionCtas: c.getFrozenCardActionCtas(req.CurrentCard),
		},
	}, nil
}

func (c *CardSectionBuilder) buildCreatedStateCardSection(_ context.Context, req *SectionBuilderRequest) (*SectionBuilderResponse, error) {
	var cardBackground = getActiveCardBackground(req.CardDesignEnhancementEnabled)
	return &SectionBuilderResponse{
		CardSection: &feCardPb.TopHeaderSection{
			FiCardComponent: &feCardPb.TopHeaderSection_FiCardComponent{
				CardTypes: &feCardPb.TopHeaderSection_FiCardComponent_InactiveState{
					InactiveState: &feCardPb.TopHeaderSection_FiCardInactiveState{
						Background: cardBackground,
						PartnerLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/federalbank-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 60, Height: 30}).WithImageType(commontypes.ImageType_PNG),
						NetworkLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/visa-network-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 71, Height: 28}).WithImageType(commontypes.ImageType_PNG),
						FiLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/fi-bank-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 28, Height: 28}).WithImageType(commontypes.ImageType_PNG),
						CvvTitle:               commontypes.GetTextFromStringFontColourFontStyle("CVV", colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
						CvvValue:               commontypes.GetTextFromStringFontColourFontStyle("•••", colorSnow, commontypes.FontStyle_OVERLINE_3),
						ExpiryTitle:            commontypes.GetTextFromStringFontColourFontStyle("EXPIRY", colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
						ExpiryValue:            commontypes.GetTextFromStringFontColourFontStyle("••/••", colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
						StatusBannerBackground: widgetUi.GetBlockBackgroundColour(colorDarkGray),
						StatusBanner: &ui.IconTextComponent{
							Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Activate your card to start transacting!", colorSnow, commontypes.FontStyle_SUBTITLE_XS)},
							ContainerProperties: &ui.IconTextComponent_ContainerProperties{
								Height: 36,
							},
						},
					},
				},
			},
			CardActionCtas: []*feCardPb.TopHeaderSection_FiCardActionCta{
				{
					CardControlInfo: &feCardPb.CardActionControlInfo{
						ActionType:     feCardPb.CardActionType_ACTIVATE,
						CardActionAuth: feCardPb.CardActionAuth_VALIDATE_LIVENESS_PLUS_FACEMATCH,
						CtaType: &feCardPb.CardActionControlInfo_CtaIconTextIcon{
							CtaIconTextIcon: &ui.IconTextComponent{
								Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Activate card", colorForestGreen, commontypes.FontStyle_SUBTITLE_3)},
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									BgColor:       "#28292B",
									CornerRadius:  19,
									LeftPadding:   12,
									RightPadding:  12,
									BottomPadding: 8,
									TopPadding:    8,
								},
							},
						},
					},
				},
				{
					CardControlInfo: &feCardPb.CardActionControlInfo{
						Deeplink: &deepLinkPb.Deeplink{
							Screen: deepLinkPb.Screen_CARD_SETTINGS_SCREEN,
							ScreenOptions: &deepLinkPb.Deeplink_CardSettingsScreenOptions{
								CardSettingsScreenOptions: &deepLinkPb.CardSettingsScreenOptions{
									CardId: req.CurrentCard.GetId(),
								},
							},
						},
						CtaType: &feCardPb.CardActionControlInfo_CtaIconTextIcon{
							CtaIconTextIcon: &ui.IconTextComponent{
								Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("View all settings", colorForestGreen, commontypes.FontStyle_SUBTITLE_3)},
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									BgColor:       "#28292B",
									CornerRadius:  19,
									LeftPadding:   12,
									RightPadding:  12,
									BottomPadding: 8,
									TopPadding:    8,
								},
							},
						},
					},
				},
			},
		},
	}, nil
}

func (c *CardSectionBuilder) buildDefaultStateCardSection(ctx context.Context, beReq *SectionBuilderRequest) (*SectionBuilderResponse, error) {
	cardSectionActiveStateVersion := c.conf.DashboardV2Config().SectionsConfig().CardSectionConfig().ActivatedStateVersion()
	switch {
	case beReq.CardSectionPreferenceByAction == feCardPb.CardActionType_VIEW_DETAILS:
		return c.getViewCardDetailsState(beReq), nil
	case cardSectionActiveStateVersion == 1:
		return c.buildDefaultStateCardSectionV1(ctx, beReq)
	default:
		switch beReq.CurrentCard.GetForm() {
		case cardPb.CardForm_DIGITAL:
			return c.buildDigitalCardFormSection(ctx, beReq)
		case cardPb.CardForm_PHYSICAL:
			return c.buildPhysicalFormCardSection(ctx, beReq)
		default:
			return nil, fmt.Errorf("no component state found for the card %s", beReq.CurrentCard.GetId())
		}
	}
}

func (c *CardSectionBuilder) buildDefaultStateCardSectionV1(ctx context.Context, bReq *SectionBuilderRequest) (*SectionBuilderResponse, error) {
	if bReq.IsTravelModeOn {
		return c.buildTravelModeFormSectionV1(ctx, bReq)
	} else {
		switch bReq.CurrentCard.GetForm() {
		case cardPb.CardForm_DIGITAL:
			return c.buildDigitalCardFormSectionV1(ctx, bReq)
		case cardPb.CardForm_PHYSICAL:
			return c.buildPhysicalFormCardSectionV1(ctx, bReq)
		default:
			return nil, fmt.Errorf("no component state found for the card %s", bReq.CurrentCard.GetId())
		}
	}
}

func (c *CardSectionBuilder) buildTravelModeFormSectionV1(ctx context.Context, bReq *SectionBuilderRequest) (*SectionBuilderResponse, error) {
	var (
		fiCardSectionResp = &SectionBuilderResponse{
			CardSection: &feCardPb.TopHeaderSection{
				FiCardComponent: c.getInfoStateFiCardWithDefaultValues(bReq),
				CardActionCtas:  c.getActiveCardActionCtas(ctx, bReq.CurrentCard),
				NudgesBanner:    nil,
			},
		}
	)

	lastFailedTransactionOrderId := c.isLastTransactionFailed(ctx, bReq)

	switch {
	case lastFailedTransactionOrderId != "":
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoTitle = commontypes.GetTextFromStringFontColourFontStyleFontAlignment("Your recent transaction has declined", "#E7E7E7", commontypes.FontStyle_HEADLINE_S, commontypes.Text_ALIGNMENT_LEFT)
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoAction = &ui.IconTextComponent{
			Texts:              []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Check Now", colorForestGreen, commontypes.FontStyle_BUTTON_M)},
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/card-images/info-ficard-chevron-right-icon-4x.png", 24, 24),
		}
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoDeeplink = &deepLinkPb.Deeplink{
			Screen: deepLinkPb.Screen_TRANSACTION_RECEIPT,
			ScreenOptions: &deepLinkPb.Deeplink_TransactionReceiptScreenOptions{
				TransactionReceiptScreenOptions: &deepLinkPb.TransactionReceiptScreenOptions{
					OrderId: lastFailedTransactionOrderId,
					Identifier: &deepLinkPb.TransactionReceiptScreenOptions_OrdersId{
						OrdersId: lastFailedTransactionOrderId,
					},
				},
			},
		}
	case !control.UsageLocationEnabled(bReq.CurrentCard.GetControls(), cardPb.CardUsageLocationType_INTERNATIONAL):
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoTitle = commontypes.GetTextFromStringFontColourFontStyleFontAlignment("Enable International Usage to start transacting", "#E7E7E7", commontypes.FontStyle_HEADLINE_S, commontypes.Text_ALIGNMENT_LEFT)
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoAction = &ui.IconTextComponent{
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/card-images/info-ficard-chevron-right-icon-4x.png", 24, 24),
		}
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoDeeplink = &deepLinkPb.Deeplink{
			Screen:          deepLinkPb.Screen_DC_USAGE_AND_LIMIT_SETTINGS_SCREEN,
			ScreenOptionsV2: nil,
		}
	case bReq.CurrentTier == externalPb.Tier_TIER_FI_INFINITE || bReq.CurrentTier == externalPb.Tier_TIER_FI_PLUS || bReq.CurrentTier == externalPb.Tier_TIER_FI_SALARY:
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoTitle = commontypes.GetTextFromStringFontColourFontStyleFontAlignment("You are saving 5% on your spends", "#E7E7E7", commontypes.FontStyle_HEADLINE_S, commontypes.Text_ALIGNMENT_LEFT)
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoAction = &ui.IconTextComponent{
			Texts:              []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Learn how", colorForestGreen, commontypes.FontStyle_BUTTON_M)},
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/card-images/info-ficard-chevron-right-icon-4x.png", 24, 24),
		}
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoDeeplink = getForexLearnHowDeeplink()
	default:
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoTitle = commontypes.GetTextFromStringFontColourFontStyleFontAlignment("Accepted in over 180+ countries", "#E7E7E7", commontypes.FontStyle_HEADLINE_S, commontypes.Text_ALIGNMENT_LEFT)
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoAction = &ui.IconTextComponent{
			Texts:              []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Learn more", colorForestGreen, commontypes.FontStyle_BUTTON_M)},
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/card-images/info-ficard-chevron-right-icon-4x.png", 24, 24),
		}
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoDeeplink = deeplink2.GetDeeplinkForPdf(epificontext.AppPlatformFromContext(ctx), acceptedCountries, eligibleCountriesPdfLink)
	}
	fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().RightSectionBgImage = commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/card-images/card_info_travel_mode_bg2.png", 0, 0)
	isFeatureEnabledForTravelModeLottie, _ := c.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_DC_TRAVEL_MODE_LOTTIE).WithActorId(bReq.ActorId))
	if isFeatureEnabledForTravelModeLottie {
		fiCardSectionResp.CardSection.FiCardComponent.CardLoadingLottie = commontypes.GetVisualElementLottieFromUrlHeightAndWidth("https://epifi-icons.pointz.in/card-images/DCPlaneFlyingLottie.json", 180, 320)
	}
	return fiCardSectionResp, nil
}

func (c *CardSectionBuilder) buildDigitalCardFormSectionV1(ctx context.Context, bReq *SectionBuilderRequest) (*SectionBuilderResponse, error) {
	var (
		fiCardSectionResp = &SectionBuilderResponse{
			CardSection: &feCardPb.TopHeaderSection{
				FiCardComponent: c.getInfoStateFiCardWithDefaultValues(bReq),
				CardActionCtas:  c.getActiveCardActionCtas(ctx, bReq.CurrentCard),
				NudgesBanner:    nil,
			},
		}
	)

	switch {
	case bReq.LatestPhysicalDispatchRequest.GetState() == cpPb.RequestState_QUEUED,
		bReq.LatestPhysicalDispatchRequest.GetState() == cpPb.RequestState_MANUAL_INTERVENTION,
		bReq.LatestPhysicalDispatchRequest.GetState() == cpPb.RequestState_INITIATED:
		fiCardSectionResp.CardSection.FiCardComponent = c.getDetailedStateFiCardComponentWithDefaultValues(bReq)

	case bReq.VKYCSummary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REJECTED:
		// if vkyc attempt was rejected hide physical card order flow entryPoints
		fiCardSectionResp.CardSection.FiCardComponent = c.getDetailedStateFiCardComponentWithDefaultValues(bReq)

	default:
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoDeeplink = deeplinkV3.GetDeeplinkV3WithoutError(deepLinkPb.Screen_DEBIT_CARD_RPC_BASED_REDIRECTION, &dcScreenOptionsPb.DebitCardRpcBasedRedirectionScreenOption{
			RpcParams: &dcScreenOptionsPb.DebitCardRpcBasedRedirectionScreenOption_FetchPhysicalCardChargesForUserRequest{
				FetchPhysicalCardChargesForUserRequest: &feCardPb.FetchPhysicalCardChargesForUserRequest{},
			},
		})
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoTitle = commontypes.GetTextFromStringFontColourFontStyleFontAlignment("Get a physical Debit Card", "#E7E7E7", commontypes.FontStyle_HEADLINE_S, commontypes.Text_ALIGNMENT_LEFT)
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoAction = &ui.IconTextComponent{
			Texts:              []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Order now", colorForestGreen, commontypes.FontStyle_BUTTON_M)},
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/card-images/info-ficard-chevron-right-icon-4x.png", 24, 24),
		}
	}
	return fiCardSectionResp, nil
}

func (c *CardSectionBuilder) buildPhysicalFormCardSectionV1(ctx context.Context, bReq *SectionBuilderRequest) (*SectionBuilderResponse, error) {
	var (
		fiCardSectionResp = &SectionBuilderResponse{
			CardSection: &feCardPb.TopHeaderSection{
				FiCardComponent: c.getInfoStateFiCardWithDefaultValues(bReq),
				CardActionCtas:  c.getActiveCardActionCtas(ctx, bReq.CurrentCard),
				NudgesBanner:    nil,
			},
		}
	)

	switch {
	case bReq.PhysicalCardDeliveryState == cpPb.CardDeliveryTrackingState_RECEIVED_BY_USER:
		fiCardSectionResp.CardSection.FiCardComponent = c.getDetailedStateFiCardComponentWithDefaultValues(bReq)

	case bReq.TrackingDetails.GetDeliveryState() == cpPb.CardTrackingDeliveryState_DELIVERED &&
		(pkggenconf.IsFeatureEnabledOnPlatform(ctx, c.conf.EnableSecurePinActivationFlow()) &&
			time.Now().Sub(bReq.LatestPhysicalDispatchRequest.GetCreatedAt().AsTime()) > c.conf.CardDynamicTileDuration().ViewSecurePinActivationTime()):

		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoDeeplink, _ = cardFePkg.GetCardActivationLandingScreenDeeplink(bReq.CurrentCard.GetId(), pkggenconf.IsFeatureEnabledOnPlatform(ctx, c.conf.EnableSecurePinActivationFlow()))
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoTitle = commontypes.GetTextFromStringFontColourFontStyleFontAlignment("Activate your card via UPI PIN", "#E7E7E7", commontypes.FontStyle_HEADLINE_S, commontypes.Text_ALIGNMENT_LEFT)
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoAction = &ui.IconTextComponent{
			Texts:              []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Activate Card", colorForestGreen, commontypes.FontStyle_BUTTON_M)},
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/card-images/info-ficard-chevron-right-icon-4x.png", 24, 24),
		}

	case bReq.TrackingDetails.GetDeliveryState() == cpPb.CardTrackingDeliveryState_DELIVERED:
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoDeeplink, _ = cardFePkg.GetQrCodeDeeplink(bReq.CurrentCard.GetId(), pkggenconf.IsFeatureEnabledOnPlatform(ctx, c.conf.EnableSecurePinActivationFlow()))
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoIcon = commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/card-images/qr-code-icon-4x.png", 24, 24)
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoTitle = commontypes.GetTextFromStringFontColourFontStyleFontAlignment("Scan the QR code in your kit, to activate your physical card", "#E7E7E7", commontypes.FontStyle_HEADLINE_S, commontypes.Text_ALIGNMENT_LEFT)
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoAction = &ui.IconTextComponent{
			Texts:              []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Scan QR", colorForestGreen, commontypes.FontStyle_BUTTON_M)},
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/card-images/info-ficard-chevron-right-icon-4x.png", 24, 24),
		}

	default:
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoDeeplink = &deepLinkPb.Deeplink{
			Screen: deepLinkPb.Screen_DEBIT_CARD_TRACKING_SCREEN,
			ScreenOptions: &deepLinkPb.Deeplink_DebitCardTrackingScreenOptions{
				DebitCardTrackingScreenOptions: &deepLinkPb.DebitCardTrackingScreenOptions{
					CardId: bReq.CurrentCard.GetId(),
				},
			},
		}
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoTitle = commontypes.GetTextFromStringFontColourFontStyleFontAlignment("Your physical \nDebit Card is \non it’s way", "#E7E7E7", commontypes.FontStyle_HEADLINE_S, commontypes.Text_ALIGNMENT_LEFT)
		fiCardSectionResp.CardSection.FiCardComponent.GetInfoState().InfoAction = &ui.IconTextComponent{
			Texts:              []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Track card", colorForestGreen, commontypes.FontStyle_BUTTON_M)},
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/card-images/info-ficard-chevron-right-icon-4x.png", 24, 24),
		}
	}

	return fiCardSectionResp, nil
}

func (c *CardSectionBuilder) buildDigitalCardFormSection(ctx context.Context, bReq *SectionBuilderRequest) (*SectionBuilderResponse, error) {
	var (
		fiComponent = c.getDetailedStateFiCardComponentWithDefaultValues(bReq)
	)
	return &SectionBuilderResponse{
		CardSection: &feCardPb.TopHeaderSection{
			FiCardComponent: fiComponent,
			CardActionCtas:  c.getActiveCardActionCtas(ctx, bReq.CurrentCard),
			NudgesBanner:    nil,
		},
	}, nil
}

func (c *CardSectionBuilder) buildPhysicalFormCardSection(ctx context.Context, bReq *SectionBuilderRequest) (*SectionBuilderResponse, error) {
	var (
		fiComponent = c.getDetailedStateFiCardComponentWithDefaultValues(bReq)
	)
	return &SectionBuilderResponse{
		CardSection: &feCardPb.TopHeaderSection{
			FiCardComponent: fiComponent,
			CardActionCtas:  c.getActiveCardActionCtas(ctx, bReq.CurrentCard),
			NudgesBanner:    nil,
		},
	}, nil
}

func (c *CardSectionBuilder) getDetailedStateFiCardComponentWithDefaultValues(bReq *SectionBuilderRequest) *feCardPb.TopHeaderSection_FiCardComponent {
	var cardBackground = getActiveCardBackground(bReq.CardDesignEnhancementEnabled)
	fiCardComponent := &feCardPb.TopHeaderSection_FiCardComponent{
		CardTypes: &feCardPb.TopHeaderSection_FiCardComponent_DetailedState{
			DetailedState: &feCardPb.TopHeaderSection_FiCardDetailedState{
				Background: cardBackground,
				PartnerLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/federalbank-icon-4x.png").
					WithProperties(&commontypes.VisualElementProperties{Width: 60, Height: 30}).WithImageType(commontypes.ImageType_PNG),
				NetworkLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/visa-network-icon-4x.png").
					WithProperties(&commontypes.VisualElementProperties{Width: 71, Height: 41}).WithImageType(commontypes.ImageType_PNG),
				FiLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/fi-bank-icon-4x.png").
					WithProperties(&commontypes.VisualElementProperties{Width: 28, Height: 28}).WithImageType(commontypes.ImageType_PNG),
				MaskedCardNumber: commontypes.GetTextFromStringFontColourFontStyle(getMaskedCardNumberDetailedStateInDisplayFormat(bReq.CurrentCard.GetBasicInfo().GetMaskedCardNumber()), colorSnow, commontypes.FontStyle_SUBTITLE_2),
				CvvTitle:         commontypes.GetTextFromStringFontColourFontStyle("CVV", colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
				CvvValue:         commontypes.GetTextFromStringFontColourFontStyle("•••", colorSnow, commontypes.FontStyle_OVERLINE_3),
				ExpiryTitle:      commontypes.GetTextFromStringFontColourFontStyle("EXPIRY", colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
				ExpiryValue:      commontypes.GetTextFromStringFontColourFontStyle("••/••", colorSnow, commontypes.FontStyle_OVERLINE_2XS_CAPS),
			},
		},
	}

	return fiCardComponent
}

func (c *CardSectionBuilder) getInfoStateFiCardWithDefaultValues(bReq *SectionBuilderRequest) *feCardPb.TopHeaderSection_FiCardComponent {
	var cardBackground = getActiveCardBackground(bReq.CardDesignEnhancementEnabled)
	return &feCardPb.TopHeaderSection_FiCardComponent{
		CardTypes: &feCardPb.TopHeaderSection_FiCardComponent_InfoState{
			InfoState: &feCardPb.TopHeaderSection_FiCardInfoState{
				Background: cardBackground,
				PartnerLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/federalbank-icon-4x.png").
					WithProperties(&commontypes.VisualElementProperties{Width: 60, Height: 30}).WithImageType(commontypes.ImageType_PNG),
				NetworkLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/visa-network-icon-4x.png").
					WithProperties(&commontypes.VisualElementProperties{Width: 71, Height: 41}).WithImageType(commontypes.ImageType_PNG),
				FiLogo: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/fi-bank-icon-4x.png").
					WithProperties(&commontypes.VisualElementProperties{Width: 28, Height: 28}).WithImageType(commontypes.ImageType_PNG),
				MaskedCardNumber:        commontypes.GetTextFromStringFontColourFontStyle(getMaskedCardNumberInfoStateInDisplayFormat(bReq.CurrentCard.GetBasicInfo().GetMaskedCardNumber()[len(bReq.CurrentCard.GetBasicInfo().GetMaskedCardNumber())-4:]), colorSnow, commontypes.FontStyle_SUBTITLE_2),
				InfoContainerBackground: widgetUi.GetBlockBackgroundColour("#28292B"),
			},
		},
	}
}

func getMaskedCardNumberInfoStateInDisplayFormat(maskedCardNumber string) string {
	return fmt.Sprintf("••• %s", maskedCardNumber)
}

func getMaskedCardNumberDetailedStateInDisplayFormat(maskedCardNumber string) string {
	lastFourDigits := maskedCardNumber[len(maskedCardNumber)-4:]
	firstFourDigits := maskedCardNumber[0:4]
	return fmt.Sprintf("%s •••• •••• %s", firstFourDigits, lastFourDigits)
}

func (c *CardSectionBuilder) getActiveCardActionCtas(ctx context.Context, savedCard *cardPb.Card) []*feCardPb.TopHeaderSection_FiCardActionCta {
	var (
		cardUsageIconUrl      string
		cardUsageText         string
		cardUsageAction       feCardPb.CardActionType
		cardUsageAuth         feCardPb.CardActionAuth
		showTapnPayOnHomeFlag = pkggenconf.IsFeatureEnabledOnPlatform(ctx, c.conf.DashboardV2Config().SectionsConfig().CardSectionConfig().ShowTapnPaySettingOnHome())
		NfcEnableFlag         = savedCard.GetControls().GetTxnStates()[cardPb.CardTransactionType_NFC.String()]
		ecomEnableFlag        = savedCard.GetControls().GetTxnStates()[cardPb.CardTransactionType_ECOMMERCE.String()]
	)
	switch {
	case showTapnPayOnHomeFlag && NfcEnableFlag == cardPb.CardControlAction_ENABLE:
		cardUsageIconUrl = "https://epifi-icons.pointz.in/card-images/online-usage-on-icon-4x.png"
		cardUsageText = "Disable\nTap & Pay"
		cardUsageAction = feCardPb.CardActionType_DISABLE_NFC
		cardUsageAuth = feCardPb.CardActionAuth_CARD_ACTION_AUTH_NONE

	case showTapnPayOnHomeFlag:
		cardUsageIconUrl = "https://epifi-icons.pointz.in/card-images/online-usage-off-icon-4x.png"
		cardUsageText = "Enable\nTap & Pay"
		cardUsageAction = feCardPb.CardActionType_ENABLE_NFC
		cardUsageAuth = feCardPb.CardActionAuth_VALIDATE_UPI_PIN

	case ecomEnableFlag == cardPb.CardControlAction_DISABLE:
		cardUsageIconUrl = "https://epifi-icons.pointz.in/card-images/online-usage-off-icon-4x.png"
		cardUsageText = "Enable\nOnline Usage"
		cardUsageAction = feCardPb.CardActionType_ENABLE_ECOMM
		cardUsageAuth = feCardPb.CardActionAuth_VALIDATE_UPI_PIN

	default:
		cardUsageIconUrl = "https://epifi-icons.pointz.in/card-images/online-usage-on-icon-4x.png"
		cardUsageText = "Disable\nOnline Usage"
		cardUsageAction = feCardPb.CardActionType_DISABLE_ECOMM
		cardUsageAuth = feCardPb.CardActionAuth_CARD_ACTION_AUTH_NONE
	}

	return []*feCardPb.TopHeaderSection_FiCardActionCta{
		{
			CardControlInfo: &feCardPb.CardActionControlInfo{
				ActionType:     feCardPb.CardActionType_VIEW_DETAILS,
				CardActionAuth: feCardPb.CardActionAuth_VALIDATE_UPI_PIN,
				CtaType: &feCardPb.CardActionControlInfo_CtaIconTitleSubtitle{
					CtaIconTitleSubtitle: &widgetUi.VisualElementTitleSubtitleElement{
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/view-card-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 40, Height: 40}).WithImageType(commontypes.ImageType_PNG),
						TitleText: commontypes.GetTextFromStringFontColourFontStyle("View card\ndetails", "#8D8D8D", commontypes.FontStyle_BODY_XS),
					},
				},
			},
		},
		{
			CardControlInfo: &feCardPb.CardActionControlInfo{
				ActionType:     feCardPb.CardActionType_RESET_PIN,
				CardActionAuth: feCardPb.CardActionAuth_VALIDATE_LIVENESS_PLUS_FACEMATCH,
				CtaType: &feCardPb.CardActionControlInfo_CtaIconTitleSubtitle{
					CtaIconTitleSubtitle: &widgetUi.VisualElementTitleSubtitleElement{
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/reset-atm-pin-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 40, Height: 40}).WithImageType(commontypes.ImageType_PNG),
						TitleText: commontypes.GetTextFromStringFontColourFontStyle("Reset\nATM PIN", "#8D8D8D", commontypes.FontStyle_BODY_XS),
					},
				},
			},
			IsNudgeActive: false,
		},
		{
			CardControlInfo: &feCardPb.CardActionControlInfo{
				ActionType:     cardUsageAction,
				CardActionAuth: cardUsageAuth,
				CtaType: &feCardPb.CardActionControlInfo_CtaIconTitleSubtitle{
					CtaIconTitleSubtitle: &widgetUi.VisualElementTitleSubtitleElement{
						VisualElement: commontypes.GetVisualElementImageFromUrl(cardUsageIconUrl).
							WithProperties(&commontypes.VisualElementProperties{Width: 40, Height: 40}).WithImageType(commontypes.ImageType_PNG),
						TitleText: commontypes.GetTextFromStringFontColourFontStyle(cardUsageText, "#8D8D8D", commontypes.FontStyle_BODY_XS),
					},
				},
			},
		},
		{
			CardControlInfo: &feCardPb.CardActionControlInfo{
				ActionType: feCardPb.CardActionType_DEEPLINK,
				Deeplink: &deepLinkPb.Deeplink{
					Screen: deepLinkPb.Screen_CARD_SETTINGS_SCREEN,
					ScreenOptions: &deepLinkPb.Deeplink_CardSettingsScreenOptions{
						CardSettingsScreenOptions: &deepLinkPb.CardSettingsScreenOptions{
							CardId: savedCard.GetId(),
						},
					},
				},
				CtaType: &feCardPb.CardActionControlInfo_CtaIconTitleSubtitle{
					CtaIconTitleSubtitle: &widgetUi.VisualElementTitleSubtitleElement{
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/view-all-setting-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 40, Height: 40}).WithImageType(commontypes.ImageType_PNG),
						TitleText: commontypes.GetTextFromStringFontColourFontStyle("View all\nsettings", "#8D8D8D", commontypes.FontStyle_BODY_XS),
					},
				},
			},
		},
	}
}

func (c *CardSectionBuilder) getViewCardDetailsStateActionInfo() []*feCardPb.TopHeaderSection_FiCardActionCta {
	return []*feCardPb.TopHeaderSection_FiCardActionCta{
		{
			CardControlInfo: &feCardPb.CardActionControlInfo{
				ActionType: feCardPb.CardActionType_COPY_CARD_DETAILS,
				CtaType: &feCardPb.CardActionControlInfo_CtaIconTextIcon{
					CtaIconTextIcon: &ui.IconTextComponent{
						Texts:              []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(copyCardDetailsCtaText, "#00B899", commontypes.FontStyle_SUBTITLE_3)},
						RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(copyCardDetailsImage, 11, 11),
						RightImgTxtPadding: 6,
						ContainerProperties: &ui.IconTextComponent_ContainerProperties{
							BgColor:       "#28292B",
							CornerRadius:  19,
							LeftPadding:   12,
							RightPadding:  12,
							BottomPadding: 8,
							TopPadding:    8,
						},
					},
				},
			},
		},
		{
			CardControlInfo: &feCardPb.CardActionControlInfo{
				ActionType: feCardPb.CardActionType_HIDE_DETAILS,
				CtaType: &feCardPb.CardActionControlInfo_CtaIconTextIcon{
					CtaIconTextIcon: &ui.IconTextComponent{
						Texts:              []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(hideCardDetailsCtaText, "#00B899", commontypes.FontStyle_SUBTITLE_3)},
						RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(hideCardDetailsImage, 13, 14),
						RightImgTxtPadding: 6,
						ContainerProperties: &ui.IconTextComponent_ContainerProperties{
							BgColor:       "#28292B",
							CornerRadius:  19,
							LeftPadding:   12,
							RightPadding:  12,
							BottomPadding: 8,
							TopPadding:    8,
						},
					},
				},
			},
		},
	}
}

func (c *CardSectionBuilder) getFrozenCardActionCtas(savedCard *cardPb.Card) []*feCardPb.TopHeaderSection_FiCardActionCta {
	return []*feCardPb.TopHeaderSection_FiCardActionCta{
		{
			CardControlInfo: &feCardPb.CardActionControlInfo{
				ActionType:     feCardPb.CardActionType_UNLOCK,
				CardActionAuth: feCardPb.CardActionAuth_VALIDATE_UPI_PIN,
				CtaType: &feCardPb.CardActionControlInfo_CtaIconTextIcon{
					CtaIconTextIcon: &ui.IconTextComponent{
						Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Unfreeze card", colorForestGreen, commontypes.FontStyle_SUBTITLE_3)},
						ContainerProperties: &ui.IconTextComponent_ContainerProperties{
							BgColor:       "#28292B",
							CornerRadius:  19,
							LeftPadding:   12,
							RightPadding:  12,
							BottomPadding: 8,
							TopPadding:    8,
						},
					},
				},
			},
		},
		{
			CardControlInfo: &feCardPb.CardActionControlInfo{
				Deeplink: &deepLinkPb.Deeplink{
					Screen: deepLinkPb.Screen_CARD_SETTINGS_SCREEN,
					ScreenOptions: &deepLinkPb.Deeplink_CardSettingsScreenOptions{
						CardSettingsScreenOptions: &deepLinkPb.CardSettingsScreenOptions{
							CardId: savedCard.GetId(),
						},
					},
				},
				CtaType: &feCardPb.CardActionControlInfo_CtaIconTextIcon{
					CtaIconTextIcon: &ui.IconTextComponent{
						Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("View all settings", colorForestGreen, commontypes.FontStyle_SUBTITLE_3)},
						ContainerProperties: &ui.IconTextComponent_ContainerProperties{
							BgColor:       "#28292B",
							CornerRadius:  19,
							LeftPadding:   12,
							RightPadding:  12,
							BottomPadding: 8,
							TopPadding:    8,
						},
					},
				},
			},
		},
	}
}

func (c *CardSectionBuilder) getInactiveCardActionCtas(req *SectionBuilderRequest) []*feCardPb.TopHeaderSection_FiCardActionCta {
	return []*feCardPb.TopHeaderSection_FiCardActionCta{
		{
			CardControlInfo: &feCardPb.CardActionControlInfo{
				CtaType: &feCardPb.CardActionControlInfo_CtaIconTitleSubtitle{
					CtaIconTitleSubtitle: &widgetUi.VisualElementTitleSubtitleElement{
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/card-details-off-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 40, Height: 40}).WithImageType(commontypes.ImageType_PNG),
						TitleText: commontypes.GetTextFromStringFontColourFontStyle("View card\ndetails", "#8D8D8D", commontypes.FontStyle_BODY_XS),
					},
				},
			},
		},
		{
			CardControlInfo: &feCardPb.CardActionControlInfo{
				CtaType: &feCardPb.CardActionControlInfo_CtaIconTitleSubtitle{
					CtaIconTitleSubtitle: &widgetUi.VisualElementTitleSubtitleElement{
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/tap-n-pay-disabled-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 40, Height: 40}).WithImageType(commontypes.ImageType_PNG),
						TitleText: commontypes.GetTextFromStringFontColourFontStyle("Enable\nTap & Pay", "#8D8D8D", commontypes.FontStyle_BODY_XS),
					},
				},
			},
		},
		{
			CardControlInfo: &feCardPb.CardActionControlInfo{
				CtaType: &feCardPb.CardActionControlInfo_CtaIconTitleSubtitle{
					CtaIconTitleSubtitle: &widgetUi.VisualElementTitleSubtitleElement{
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/enabled-online-usaged-off-disabled-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 40, Height: 40}).WithImageType(commontypes.ImageType_PNG),
						TitleText: commontypes.GetTextFromStringFontColourFontStyle("Enable\nOnline Usage", "#8D8D8D", commontypes.FontStyle_BODY_XS),
					},
				},
			},
		},
		{
			CardControlInfo: &feCardPb.CardActionControlInfo{
				Deeplink: &deepLinkPb.Deeplink{
					Screen: deepLinkPb.Screen_CARD_SETTINGS_SCREEN,
					ScreenOptions: &deepLinkPb.Deeplink_CardSettingsScreenOptions{
						CardSettingsScreenOptions: &deepLinkPb.CardSettingsScreenOptions{
							CardId: req.CurrentCard.GetId(),
						},
					},
				},
				CtaType: &feCardPb.CardActionControlInfo_CtaIconTitleSubtitle{
					CtaIconTitleSubtitle: &widgetUi.VisualElementTitleSubtitleElement{
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/view-all-setting-icon-4x.png").
							WithProperties(&commontypes.VisualElementProperties{Width: 40, Height: 40}).WithImageType(commontypes.ImageType_PNG),
						TitleText: commontypes.GetTextFromStringFontColourFontStyle("View all\nsettings", "#8D8D8D", commontypes.FontStyle_BODY_XS),
					},
				},
			},
		},
	}
}

func (c *CardSectionBuilder) getViewCardDetailsState(bReq *SectionBuilderRequest) *SectionBuilderResponse {
	detailedState := c.getDetailedStateFiCardComponentWithDefaultValues(bReq)
	controlCtas := c.getViewCardDetailsStateActionInfo()
	return &SectionBuilderResponse{
		CardSection: &feCardPb.TopHeaderSection{
			FiCardComponent: detailedState,
			CardActionCtas:  controlCtas,
		},
	}
}

// isLastTransactionFailed returns the orderId, if last transaction is a failed transaction, otherwise returns empty string
func (c *CardSectionBuilder) isLastTransactionFailed(ctx context.Context, bReq *SectionBuilderRequest) (orderId string) {
	orderActivitiesResp, err := c.orderActorActivityClient.GetActivities(ctx, &orderActorActivityPb.GetActivitiesRequest{
		CurrentActorId:           bReq.ActorId,
		AccountFilter:            nil,
		ActivitiesStartTimestamp: timestampPb2.New(time.Now().Add(-5 * 24 * time.Hour)),
		PageSize:                 1,
		Descending:               true,
		ActivitiesEndTimestamp:   timestampPb2.Now(),
		PageLandingTimestamp:     timestampPb2.Now(),
		EntryPointType:           orderActorActivityPb.GetActivitiesRequest_ENTRY_POINT_TYPE_UI,
	})

	if err = epifigrpc.RPCError(orderActivitiesResp, err); err != nil {
		return ""
	}

	switch orderActivitiesResp.GetActivities()[0].GetActivityType() {
	case orderActorActivityPb.GetActivitiesResponse_Activity_ATM_TRANSACTION_FAILED,
		orderActorActivityPb.GetActivitiesResponse_Activity_DEBIT_CARD_TRANSACTION_FAILED:
		return orderActivitiesResp.GetActivities()[0].GetActivityId()
	default:
		return ""
	}

}

// getActiveCardBackground returns DC card background based on cardDesignEnhancementEnabled
func getActiveCardBackground(cardDesignEnhancementEnabled bool) *widgetUi.BackgroundColour {
	var cardBackground *widgetUi.BackgroundColour
	if cardDesignEnhancementEnabled {
		cardBackground = widgetUi.GetLinearGradientBackgroundColour(180, []*widgetUi.ColorStop{
			{
				Color:          "#00B899",
				StopPercentage: 0,
			},
			{
				Color:          "#005244",
				StopPercentage: 100,
			},
		})
	} else {
		cardBackground = widgetUi.GetBlockBackgroundColour(colorForestGreen)
	}
	return cardBackground
}
