package p2pinvestment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"

	"context"
	json2 "encoding/json"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	p2pPb "github.com/epifi/gamma/api/frontend/p2pinvestment"
	p2pPbBe "github.com/epifi/gamma/api/p2pinvestment"
	"github.com/epifi/gamma/api/tiering/external"
	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/p2pinvestment/deeplinks"
	"github.com/epifi/gamma/frontend/p2pinvestment/helper"
	displaynames "github.com/epifi/gamma/frontend/tiering/display_names"
)

// nolint: funlen,dupl
func (s *Service) GetBanners(ctx context.Context, req *p2pPb.GetBannersRequest) (*p2pPb.GetBannersResponse, error) {
	res := &p2pPb.GetBannersResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
	}
	grp, grpCtx := errgroup.WithContext(ctx)
	var getInvestmentSummaryDetailsRes *p2pPbBe.GetInvestmentSummaryDetailsResponse
	var gidRes *p2pPbBe.GetInvestmentDashboardResponse
	var checkEligibilityRes *p2pPbBe.CheckEligibilityResponse
	// var investmentAttributesRes *p2pPbBe.GetInvestmentAttributesResponse
	var getMaturityTxnsRes *p2pPbBe.GetMaturityTransactionsForInvestorResponse
	var gidResStatus *rpcPb.Status
	var investmentSummaryResStatus *rpcPb.Status
	appPlatform := req.GetReq().GetAuth().GetDevice().GetPlatform()
	appVersion := req.GetReq().GetAppVersionCode()

	// GetMaturityTransactionsForInvestor
	var err error
	getMaturityTxnsRes, err = s.p2PInvestmentClient.GetMaturityTransactionsForInvestor(grpCtx, &p2pPbBe.GetMaturityTransactionsForInvestorRequest{
		ActorId:      req.GetReq().GetAuth().GetActorId(),
		NumberOfDays: daysUntilMaturityTxnsAreToBeActedOn,
	})
	if te := epifigrpc.RPCError(getMaturityTxnsRes, err); te != nil {
		// just log
		logger.Error(ctx, "error while calling GetMaturityTransactionsForInvestor", zap.Error(te))
		getMaturityTxnsRes = nil
	}

	if req.GetBannerReqPayload().GetRenewInvestmentBannerReq().GetOrderExternalId() != "" {
		isMaturityConsentEnabled, matErr := s.rpcHelper.IsJumpMaturityConsentFlowEnabled(ctx, req.GetReq().GetAuth().GetActorId())
		if matErr != nil {
			logger.Error(ctx, "error in checking if maturity consent is enabled", zap.Error(matErr))
		} else if !isMaturityConsentEnabled {
			// banner should be displayed in order receipt only if maturity consent is not enabled
			// if maturity consent is enabled, user would see the consent card so no need to show banner
			banner := getMaturityBanner(ctx, s.conf, getMaturityTxnsRes, isMaturityConsentEnabled, req.GetBannerReqPayload().GetRenewInvestmentBannerReq().GetOrderExternalId())
			if banner != nil {
				res.Banners = append(res.Banners, banner)
			}
		}
		return res, nil
	}

	isInternal, err := s.rpcHelper.IsInternalUser(ctx, req.GetReq().GetAuth().GetActorId())
	if err != nil {
		logger.Error(ctx, "error in checking if user is internal", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
	}
	// GetInvestmentSummary async
	grp.Go(func() error {
		var err error
		getInvestmentSummaryDetailsRes, err = s.p2PInvestmentClient.GetInvestmentSummaryDetails(grpCtx, &p2pPbBe.GetInvestmentSummaryDetailsRequest{
			ActorId: req.GetReq().GetAuth().GetActorId(),
		})
		investmentSummaryResStatus = getInvestmentSummaryDetailsRes.GetStatus()
		if te := epifigrpc.RPCError(getInvestmentSummaryDetailsRes, err); te != nil {
			// just log
			logger.Error(ctx, "error while calling GetInvestmentSummaryDetails", zap.Error(te))
			getInvestmentSummaryDetailsRes = nil
		}
		return nil
	})
	// GetMaturityTransactionsForInvestor async
	grp.Go(func() error {
		var err error
		getMaturityTxnsRes, err = s.p2PInvestmentClient.GetMaturityTransactionsForInvestor(grpCtx, &p2pPbBe.GetMaturityTransactionsForInvestorRequest{
			ActorId:      req.GetReq().GetAuth().GetActorId(),
			NumberOfDays: daysUntilMaturityTxnsAreToBeActedOn,
		})
		if te := epifigrpc.RPCError(getMaturityTxnsRes, err); te != nil {
			// just log
			logger.Error(ctx, "error while calling GetMaturityTransactionsForInvestor", zap.Error(te))
			getMaturityTxnsRes = nil
		}
		return nil
	})
	// GetInvestorDashboard async
	grp.Go(func() error {
		var err error
		gidRes, err = s.p2PInvestmentClient.GetInvestmentDashboard(grpCtx, &p2pPbBe.GetInvestmentDashboardRequest{
			ActorId: req.GetReq().GetAuth().GetActorId(),
		})
		gidResStatus = gidRes.GetStatus()
		if te := epifigrpc.RPCError(gidRes, err); te != nil {
			// just log
			logger.Error(ctx, "error while calling GetInvestorDashboard", zap.Error(te))
			gidRes = nil
		}
		return nil
	})
	// GetInvestmentAttributes async
	// grp.Go(func() error {
	//	investor, err := s.rpcHelper.GetInvestor(ctx, req.GetReq().GetAuth().GetActorId())
	//	if err != nil {
	//		logger.Error(ctx, "error while calling GetInvestor", zap.Error(err))
	//		return errors.Wrap(err, "error while calling GetInvestor")
	//	}
	//	var giaErr error
	//	investmentAttributesRes, giaErr = s.rpcHelper.GetInvestmentAttributes(ctx, investor.GetId(), p2pPbBe.SchemeName_SCHEME_NAME_UNSPECIFIED)
	//	if giaErr != nil {
	//		return errors.Wrap(giaErr, "error while calling GetInvestmentAttributes")
	//	}
	//	return nil
	// })

	// CheckInvestorEligibility async
	grp.Go(func() error {
		var err error
		checkEligibilityRes, err = s.p2PInvestmentClient.CheckEligibility(ctx, &p2pPbBe.CheckEligibilityRequest{
			ActorId: req.GetReq().GetAuth().GetActorId(),
		})
		if te := epifigrpc.RPCError(checkEligibilityRes, err); te != nil {
			// just log
			logger.Error(ctx, "error while calling CheckEligibility", zap.Error(te))
			checkEligibilityRes = nil
		}
		return nil
	})

	var isTieringAllPlansV2Enabled bool
	grp.Go(func() error {
		tieringConfParams, tieringConfParamsErr := s.tieringClient.GetConfigParams(ctx, &tieringPb.GetConfigParamsRequest{ActorId: req.GetReq().GetAuth().GetActorId()})
		if rpcErr := epifigrpc.RPCError(tieringConfParams, tieringConfParamsErr); rpcErr != nil {
			logger.Error(ctx, "error while getting tiering config params", zap.Error(rpcErr))
		}

		isTieringAllPlansV2Enabled = tieringConfParams.GetIsMultipleWaysToEnterTieringEnabledForActor()
		return nil
	})

	questBanner, bErr := s.getQuestBanner(ctx)
	if bErr != nil {
		// not returning error response here since it's ok to not send any banners in case of error
		logger.Error(ctx, "error in getting quest banner", zap.Error(bErr), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
	} else {
		res.Banners = append(res.Banners, questBanner)
	}

	if err := grp.Wait(); err != nil {
		logger.Error(ctx, "error while getting details for GetBanners async", zap.Error(err))
		if gidRes != nil {
			if gidRes.GetStatus().GetCode() == uint32(p2pPbBe.GetInvestmentDashboardResponse_ACTOR_ACCOUNT_CLOSED) {
				res.GetRespHeader().Status = rpcPb.StatusRecordNotFoundWithDebugMsg(InvestorNotFound)
				return res, nil
			}
			if gidRes.GetInvestmentIneligibilityReason() == p2pPbBe.InvestmentIneligibilityReason_INVESTMENT_INELIGIBLE_LIMITS_EXCEEDED {
				res.Banners = append(res.Banners, getUserLimitsExceededBanner(gidRes))
				return res, nil
			}
		}
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		return res, nil
	}

	if gidResStatus.GetCode() == uint32(p2pPbBe.GetInvestmentDashboardResponse_INVESTOR_NOT_APPLICABLE_FOR_DASHBOARD) ||
		investmentSummaryResStatus.GetCode() == uint32(p2pPbBe.GetInvestmentSummaryDetailsResponse_INVESTOR_NOT_APPLICABLE_FOR_SUMMARY) {
		zeroStateBanner, zErr := s.getZeroStateInfoBanner(ctx, req.GetReq().GetAuth().GetActorId(), checkEligibilityRes,
			appPlatform, appVersion)
		if zErr != nil {
			// not returning error response here since it's ok to not send any banners in case of error
			logger.Error(ctx, "error in getting zero state banner", zap.Error(zErr), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
		}
		if zeroStateBanner != nil {
			res.Banners = append(res.Banners, zeroStateBanner)
		}
		return res, nil
	}
	if getMaturityTxnsRes != nil {
		isMaturityConsentEnabled, err := s.rpcHelper.IsJumpMaturityConsentFlowEnabled(ctx, req.GetReq().GetAuth().GetActorId())
		if err != nil {
			logger.Error(ctx, "error in checking if maturity consent is enabled", zap.Error(err))
		} else {
			banner := getMaturityBanner(ctx, s.conf, getMaturityTxnsRes, isMaturityConsentEnabled, "")
			if banner != nil {
				res.Banners = append(res.Banners, banner)
			}
		}
	}
	if checkEligibilityRes.GetTieringMetadata().GetIsUserInGracePeriod() {
		res.Banners = append(res.Banners, getGracePeriodBanner(checkEligibilityRes.GetTieringMetadata().GetCurrentTier(), isTieringAllPlansV2Enabled))
	}
	if gidRes.GetInvestmentIneligibilityReason() == p2pPbBe.InvestmentIneligibilityReason_INVESTMENT_INELIGIBLE_LIMITS_EXCEEDED {
		res.Banners = append(res.Banners, getUserLimitsExceededBanner(gidRes))
	} else {
		flexiBanner := getFlexi8Banner(s.conf, checkEligibilityRes, appPlatform, appVersion, isInternal)
		if flexiBanner != nil {
			res.Banners = append(res.Banners, flexiBanner)
		} else if boosterBanner, baErr := getBoosterBanner(s.conf, checkEligibilityRes, appPlatform, appVersion); baErr != nil {
			logger.Error(ctx, "error in getting booster banner", zap.Error(baErr))
		} else if boosterBanner != nil {
			res.Banners = append(res.Banners, boosterBanner)
		}
	}

	// if getInvestmentSummaryDetailsRes != nil && investmentAttributesRes != nil && checkEligibilityRes != nil {
	//	res.Banners = append(res.Banners, s.getDefaultSchemeInvestedBanner(ctx, getInvestmentSummaryDetailsRes, investmentAttributesRes, gidRes, checkEligibilityRes,
	//		req.GetReq().GetAuth().GetActorId(), appPlatform, appVersion))
	// }

	if checkEligibilityRes != nil {
		plansUnlockBanner, err := s.getPlansUnlockBannerV2(ctx, checkEligibilityRes, req.GetReq().GetAuth().GetActorId())
		if err != nil {
			logger.Error(ctx, "failed to get plans unlock banner", zap.Error(err))
			return res, nil
		}
		if plansUnlockBanner != nil {
			res.Banners = append(res.Banners, &p2pPb.JumpBanner{
				Banner: &p2pPb.JumpBanner_PlanUnlockBanner{
					PlanUnlockBanner: plansUnlockBanner,
				},
			})
		}
	}

	return res, nil
}

func getFlexi8Banner(conf *genconf.Config, ceRes *p2pPbBe.CheckEligibilityResponse, appPlatform commontypes.Platform,
	appVersion uint32, isInternal bool) *p2pPb.JumpBanner {
	if conf.P2PInvestment().DisableFlexiSchemeBanners() {
		return nil
	}
	if helper.IsSchemeUnavailable(p2pPbBe.SchemeName_SCHEME_NAME_LL_8_FLEXI, conf.P2PInvestment()) {
		return nil
	}
	if ceRes.GetSchemeIneligibilityInfos()[p2pPbBe.SchemeName_SCHEME_NAME_LL_8_FLEXI.String()].GetIsEligible() {
		if conf.P2PInvestment().Flexi8SchemeEnabled() || isInternal {
			bannerCta := &p2pPb.InfoAndPromotionalBannerV2_Cta{
				Text:     commontypes.GetTextFromStringFontColourFontStyle("Invest", "#FFFFFF", commontypes.FontStyle_SUBTITLE_S),
				BgColor:  "#9287BD",
				Deeplink: deeplinks.GetInvestNowDeeplinkWithSchemeName(p2pPbBe.SchemeName_SCHEME_NAME_LL_8_FLEXI.String(), appPlatform, appVersion, conf.P2PInvestment()),
			}
			badgeIconUrl := "https://epifi-icons.pointz.in/p2pinvestment/flexi-7-8-banner-badge-limited.png"
			return &p2pPb.JumpBanner{
				Banner: &p2pPb.JumpBanner_InfoPromoBanner{
					InfoPromoBanner: &p2pPb.InfoAndPromotionalBannerV2{
						Title: commontypes.GetTextFromStringFontColourFontStyle("LIMITED TIME",
							"#9287BD", commontypes.FontStyle_OVERLINE_2XS_CAPS),
						Subtitle: commontypes.GetTextFromStringFontColourFontStyle("Flexi plan now offers up to 8% p.a! Withdraw anytime.",
							"#333333", commontypes.FontStyle_SUBTITLE_M),
						Cta:        bannerCta,
						Background: typesUiPb.GetBlockColor("#CDC6E8"),
						Image:      commontypes.GetImageFromUrl(badgeIconUrl),
						ImageV2: commontypes.GetVisualElementImageFromUrl(badgeIconUrl).WithProperties(&commontypes.VisualElementProperties{
							Width:  80,
							Height: 80,
						}),
					},
				},
			}
		}
	}
	return nil
}

// getBoosterBanner returns the banner to invest in booster scheme
// https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=14487-18357&t=zr5GieCd0u5uC4Pw-0
func getBoosterBanner(conf *genconf.Config, ceRes *p2pPbBe.CheckEligibilityResponse, appPlatform commontypes.Platform, appVersion uint32) (*p2pPb.JumpBanner, error) {
	if helper.IsSchemeUnavailable(p2pPbBe.SchemeName_SCHEME_NAME_LL_BOOSTER_V2, conf.P2PInvestment()) {
		return nil, nil
	}
	var bannerCta *p2pPb.InfoAndPromotionalBannerV2_Cta
	var badgeIconUrl string
	/*
		If user is eligible for  scheme, send invest CTA in banner
		else send unlock scheme CTA
	*/
	if ceRes.GetSchemeIneligibilityInfos()[p2pPbBe.SchemeName_SCHEME_NAME_LL_BOOSTER_V2.String()].GetIsEligible() {
		bannerCta = &p2pPb.InfoAndPromotionalBannerV2_Cta{
			Text:     commontypes.GetTextFromStringFontColourFontStyle("Invest", "#AC7C44", commontypes.FontStyle_SUBTITLE_S),
			BgColor:  "#EAD8A3",
			Deeplink: deeplinks.GetInvestNowDeeplinkWithSchemeName(p2pPbBe.SchemeName_SCHEME_NAME_LL_BOOSTER_V2.String(), appPlatform, appVersion, conf.P2PInvestment()),
		}
		badgeIconUrl = conf.P2PInvestment().SchemeInfos().Get(p2pPbBe.SchemeName_SCHEME_NAME_LL_BOOSTER_V2.String()).Images().PrimaryBadge()
	} else {
		unlockDl, unlockErr := deeplinks.PopulateUnlockPlanDeeplink(conf.P2PInvestment(), p2pPbBe.SchemeName_SCHEME_NAME_LL_BOOSTER_V2, ceRes.GetSchemeIneligibilityInfos()[p2pPbBe.SchemeName_SCHEME_NAME_LL_BOOSTER_V2.String()].GetFailureDl())
		if unlockErr != nil {
			return nil, errors.Wrap(unlockErr, "error in getting unlock deeplink")
		}
		bannerCta = &p2pPb.InfoAndPromotionalBannerV2_Cta{
			Text:     commontypes.GetTextFromStringFontColourFontStyle("Unlock", "#AC7C44", commontypes.FontStyle_SUBTITLE_S),
			BgColor:  "#EAD8A3",
			Deeplink: unlockDl,
		}
		badgeIconUrl = conf.P2PInvestment().SchemeInfos().Get(p2pPbBe.SchemeName_SCHEME_NAME_LL_BOOSTER_V2.String()).Images().SecondaryLockedBadge()
	}
	return &p2pPb.JumpBanner{
		Banner: &p2pPb.JumpBanner_InfoPromoBanner{
			InfoPromoBanner: &p2pPb.InfoAndPromotionalBannerV2{
				Title: commontypes.GetTextFromStringFontColourFontStyle("LIMITED TIME",
					"#AC7C44", commontypes.FontStyle_OVERLINE_2XS_CAPS),
				Subtitle: commontypes.GetTextFromStringFontColourFontStyle("Maximise your returns at 10% p.a in our new plan",
					"#333333", commontypes.FontStyle_SUBTITLE_M),
				Cta:        bannerCta,
				Background: typesUiPb.GetBlockColor("#F4E7BF"),
				Image:      commontypes.GetImageFromUrl(badgeIconUrl),
				ImageV2: commontypes.GetVisualElementImageFromUrl(badgeIconUrl).WithProperties(&commontypes.VisualElementProperties{
					Width:  80,
					Height: 80,
				}),
			},
		},
	}, nil
}

// getGracePeriodBanner returns the banner telling the user that the user is in grace period
// https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11608-15045&t=IB8TLmpXNIWKE0Ru-0
func getGracePeriodBanner(currentTier external.Tier, isTieringAllPlansV2Enabled bool) *p2pPb.JumpBanner {
	tierDisplayString, _ := displaynames.GetTitleCaseDisplayString(currentTier)
	return &p2pPb.JumpBanner{
		Banner: &p2pPb.JumpBanner_InfoPromoBanner{
			InfoPromoBanner: &p2pPb.InfoAndPromotionalBannerV2{
				Subtitle: commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("You will lose access to current Jump benefits if you do not upgrade to %s now", tierDisplayString),
					"#333333", commontypes.FontStyle_SUBTITLE_M),
				Cta: &p2pPb.InfoAndPromotionalBannerV2_Cta{
					Text:     commontypes.GetTextFromStringFontColourFontStyle("View details", "#FFFFFF", commontypes.FontStyle_SUBTITLE_S),
					BgColor:  "#CF8888",
					Deeplink: tiering.AllPlansDeeplink(currentTier, isTieringAllPlansV2Enabled),
				},
				Background: typesUiPb.GetBlockColor("#FAD0D0"),
				Image:      commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/p2pinvestment/grace_period_banner_icon_1.png"),
				ImageV2: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/p2pinvestment/grace_period_banner_icon_1.png").WithProperties(&commontypes.VisualElementProperties{
					Width:  80,
					Height: 80,
				}),
			},
		},
	}
}

/*
Show renewal banner for transaction which is going to mature the soonest.
We would be showing this only if the user has not taken any action on the same
*/
// nolint:funlen
func getMaturityBanner(_ context.Context, conf *genconf.Config, res *p2pPbBe.GetMaturityTransactionsForInvestorResponse, isMaturityConsentEnabled bool, extOrderId string) *p2pPb.JumpBanner {
	if conf.P2PInvestment().DisableFlexiSchemeBanners() {
		return nil
	}
	for _, txn := range res.GetMaturityTransactions() {
		// if extOrderId is provided, try to get maturity banner for that txn
		// else return the banner for the first maturity txn
		if extOrderId != "" && txn.GetOriginalTxnExternalId() != extOrderId {
			continue
		}
		if txn.GetStatus() == p2pPbBe.MaturityTransactionStatus_MATURITY_TRANSACTION_STATUS_NO_MATURITY_ACTION_TAKEN {
			var bannerDl *deeplinkPb.Deeplink
			if isMaturityConsentEnabled {
				showMaturityConsentForm, _ := helper.IsMaturityConsentEditAllowed(txn.GetParentInvestmentTransaction(), txn.GetMaturityConsentType())
				bannerDl = &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_P2P_INVESTMENT_ACTIVITY_DETAILS_SCREEN,
					ScreenOptions: &deeplinkPb.Deeplink_P2PActivityDetailsScreenOptions{
						P2PActivityDetailsScreenOptions: &deeplinkPb.P2PInvestmentActivityDetailsScreenOptions{
							BannerRequestPayload: &deeplinkPb.BannerRequestPayload{
								BannerReq: &deeplinkPb.BannerRequestPayload_RenewInvestmentBannerReq{
									RenewInvestmentBannerReq: &deeplinkPb.RenewInvestmentBannerReq{
										OrderExternalId: txn.GetOriginalTxnExternalId(),
									},
								},
							},
							OrderExternalId:         txn.GetOriginalTxnExternalId(),
							ShowMaturityConsentForm: showMaturityConsentForm,
						},
					},
				}
			} else {
				bannerDl = &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_P2P_CONFIRM_INVESTMENT_RENEWAL_SCREEN,
					ScreenOptions: &deeplinkPb.Deeplink_P2PConfirmInvestmentRenewalScreenOptions{
						P2PConfirmInvestmentRenewalScreenOptions: &deeplinkPb.P2PConfirmInvestmentRenewalScreenOptions{
							OrderExternalId: txn.GetOriginalTxnExternalId(),
						},
					},
				}
			}
			return &p2pPb.JumpBanner{
				Banner: &p2pPb.JumpBanner_InfoPromoBanner{
					InfoPromoBanner: &p2pPb.InfoAndPromotionalBannerV2{
						Title: commontypes.GetTextFromStringFontColourFontStyle(moneyPb.ToDisplayStringWithPrecision(txn.GetAmount(), 0),
							"#4F71AB", commontypes.FontStyle_HEADLINE_XL),
						Subtitle: commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("And its returns will mature on %s. Reinvest it to earn more!", txn.GetMaturityDate().AsTime().Format("Jan 02")),
							"#333333", commontypes.FontStyle_SUBTITLE_S),
						Cta: &p2pPb.InfoAndPromotionalBannerV2_Cta{
							Text:     commontypes.GetTextFromStringFontColourFontStyle("Continue", "#FFFFFF", commontypes.FontStyle_BUTTON_S),
							BgColor:  "#879EDB",
							Deeplink: bannerDl,
						},
						Background: typesUiPb.GetBlockColor("#D1DAF1"),
						Image:      commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/p2pinvestment/rupee_reload_purple.png"),
						ImageV2: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/p2pinvestment/rupee_reload_purple.png").WithProperties(&commontypes.VisualElementProperties{
							Width:  80,
							Height: 80,
						}),
					},
				},
			}
		}
	}
	return nil
}

func (s *Service) getDefaultSchemeInvestedBanner(ctx context.Context, getInvestmentSummaryDetailsRes *p2pPbBe.GetInvestmentSummaryDetailsResponse, investmentAttributesRes *p2pPbBe.GetInvestmentAttributesResponse,
	gidRes *p2pPbBe.GetInvestmentDashboardResponse, checkEligibilityRes *p2pPbBe.CheckEligibilityResponse, actorId string, appPlatform commontypes.Platform, appVersion uint32) *p2pPb.JumpBanner {
	isUserOnlyDefaultSchemeInvested, err := getIsUserOnlyDefaultSchemeInvested(getInvestmentSummaryDetailsRes)
	if err != nil {
		// just log
		logger.Error(ctx, "failed to get is user only default scheme invested, not showing the banner", zap.Error(err))
	}
	if isUserOnlyDefaultSchemeInvested && gidRes.GetInvestmentIneligibilityReason() != p2pPbBe.InvestmentIneligibilityReason_INVESTMENT_INELIGIBLE_LIMITS_EXCEEDED {
		isInternal, err := s.rpcHelper.IsInternalUser(ctx, actorId)
		if err != nil {
			logger.Error(ctx, "error in checking if user is internal", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		}
		choosePlanDl, iErr := deeplinks.GetChoosePlanInfoScreenOptions(s.conf.P2PInvestment(), checkEligibilityRes, investmentAttributesRes,
			isInternal, appPlatform, appVersion, deeplinkPb.P2PInvestmentChoosePlanInfoScreenOption_STANDARD)
		if iErr != nil {
			logger.Error(ctx, "failed to create choose plan screen option", zap.Error(iErr))
			return nil
		}
		return &p2pPb.JumpBanner{
			Banner: &p2pPb.JumpBanner_InfoPromoBanner{
				InfoPromoBanner: &p2pPb.InfoAndPromotionalBannerV2{
					Title: helper.GetText("Jump is open to investments again. Check out the new plans now!", "#333333", commontypes.FontStyle_SUBTITLE_S),
					Cta: &p2pPb.InfoAndPromotionalBannerV2_Cta{
						Text:     helper.GetText("View details", "#FFFFFF", commontypes.FontStyle_SUBTITLE_S),
						BgColor:  "#9287BD",
						Deeplink: choosePlanDl,
					},
					Background: &typesUiPb.BackgroundColour{
						Colour: &typesUiPb.BackgroundColour_BlockColour{
							BlockColour: "#CDC6E8",
						},
					},
					Image: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  "https://epifi-icons.pointz.in/p2pinvestment/magic-hat.png",
					},
					ImageV2: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/p2pinvestment/magic-hat.png").WithProperties(&commontypes.VisualElementProperties{
						Width:  80,
						Height: 80,
					}),
				},
			},
		}
	}
	return nil
}

func getUserLimitsExceededBanner(_ *p2pPbBe.GetInvestmentDashboardResponse) *p2pPb.JumpBanner {
	return &p2pPb.JumpBanner{
		Banner: &p2pPb.JumpBanner_InfoPromoBanner{
			InfoPromoBanner: &p2pPb.InfoAndPromotionalBannerV2{
				Title: helper.GetText("You’ve reached the regulatory prescribed investment limit for this year!", "#333333", commontypes.FontStyle_SUBTITLE_S),
				Cta: &p2pPb.InfoAndPromotionalBannerV2_Cta{
					Text:     helper.GetText("Know more", "#FFFFFF", commontypes.FontStyle_BUTTON_S),
					BgColor:  "#879EDB",
					Deeplink: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_P2P_INVESTMENT_KNOW_MORE_SCREEN},
				},
				Background: &typesUiPb.BackgroundColour{
					Colour: &typesUiPb.BackgroundColour_BlockColour{
						BlockColour: "#BBC8E9",
					},
				},
				Image: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/p2pinvestment/bank.png",
				},
				ImageV2: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/p2pinvestment/bank.png").WithProperties(&commontypes.VisualElementProperties{
					Width:  80,
					Height: 80,
				}),
			},
		},
	}
}

func (s *Service) getQuestBanner(ctx context.Context) (*p2pPb.JumpBanner, error) {
	bannerJson := s.conf.P2PInvestment().QuestDashboardBanners().BannerJson(ctx)
	if bannerJson == "" {
		logger.Debug(ctx, "no quest banner found")
		return nil, nil
	}
	logger.Debug(ctx, "quest banner found", zap.String("banner", bannerJson))
	jBanner := &p2pPb.JumpBanner{}
	err := json2.Unmarshal([]byte(bannerJson), jBanner)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal banner json", zap.Error(err))
		return nil, err
	}
	return jBanner, nil
}

// nolint:funlen
func (s *Service) getZeroStateInfoBanner(ctx context.Context, actorId string, ceRes *p2pPbBe.CheckEligibilityResponse, appPlatform commontypes.Platform,
	appVersion uint32) (*p2pPb.JumpBanner, error) {
	isZeroStateDashEnabled, zErr := s.rpcHelper.IsNonInvestedDashEnabled(ctx, actorId)
	if zErr != nil {
		return nil, errors.Wrap(zErr, "error in checking if zero state dash is enabled")
	}
	/*
	   GetBanners is called to get the banners after user comes onto the dashboard.
	   Returning error here since a user who cannot see the dashboard should not have come into this flow.
	*/
	if !isZeroStateDashEnabled || !ceRes.GetIsEligible() {
		logger.Error(ctx, "zero state dashboard not enabled for user's state", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("isZeroStateDashEnabled", isZeroStateDashEnabled), zap.Any("isUserEligible", ceRes.GetIsEligible()))
		return nil, fmt.Errorf("zero state dashboard not enabled for user")
	}
	boosterBanner, bErr := getBoosterBanner(s.conf, ceRes, appPlatform, appVersion)
	if bErr != nil {
		return nil, errors.Wrap(bErr, "error in getting booster banner")
	}
	return boosterBanner, nil
}

// getIsUserOnlyDefaultSchemeInvested checks if user has only invested in default scheme or not
func getIsUserOnlyDefaultSchemeInvested(res *p2pPbBe.GetInvestmentSummaryDetailsResponse) (bool, error) {

	totalInvestedAmount, err := getAmountInvestedInSchemes(res, nil)
	if err != nil {
		return false, errors.Wrap(err, "failed to get total invested amount")
	}

	defaultSchemeInvestedAmount, err := getAmountInvestedInSchemes(res, []p2pPbBe.SchemeName{
		p2pPbBe.SchemeName_SCHEME_NAME_LL_DEFAULT,
		p2pPbBe.SchemeName_SCHEME_NAME_LL_DEFAULT_NO_PENALTY,
	})
	if err != nil {
		return false, errors.Wrap(err, "failed to get default scheme invested amount")
	}

	// if total invested amount is equal to default scheme invested amount then user has only invested in default scheme
	return moneyPb.Compare(totalInvestedAmount, defaultSchemeInvestedAmount) == 0, nil
}

func getCurrentTierString(currentTier external.Tier) string {
	switch currentTier {
	case external.Tier_TIER_FI_BASIC:
		return "Fi Basic"
	case external.Tier_TIER_FI_PLUS:
		return "Fi Plus"
	case external.Tier_TIER_FI_INFINITE:
		return "Fi Infinite"
	case external.Tier_TIER_FI_SALARY_LITE:
		return "Salary Lite"
	case external.Tier_TIER_FI_SALARY:
		return "Salary"
	case external.Tier_TIER_FI_SALARY_BASIC:
		return "Salary Basic"
	default:
		if currentTier.IsAaSalaryTier() {
			return "Prime"
		}
		return ""
	}
}
