//nolint:all
package preapprovedloan

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/lib/pq"
	pkgErrors "github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/date"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	beAuthPb "github.com/epifi/gamma/api/auth"
	signupPb "github.com/epifi/gamma/api/frontend/account/signup"
	authOrchPb "github.com/epifi/gamma/api/frontend/auth/orchestrator"
	feConsentPb "github.com/epifi/gamma/api/frontend/consent"
	creditreportpb "github.com/epifi/gamma/api/frontend/credit_report"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	fePalPb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	palEnumFePb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	bePalPb "github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	dao2 "github.com/epifi/gamma/auth/dao"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/testing/integration/app"
)

const (
	maxAuthPollCount                 = 30
	maxLoanApplicationPollCount      = 30
	maxCreditReportDownloadPollCount = 30
)

// todo (utkarsh) : should we move it to a pkg config since this is replicated in multiple places ?
var vendorToOwnershipMap = map[bePalPb.Vendor]commontypes.Ownership{
	bePalPb.Vendor_FEDERAL:    commontypes.Ownership_FEDERAL_BANK,
	bePalPb.Vendor_MONEYVIEW:  commontypes.Ownership_MONEYVIEW_PL,
	bePalPb.Vendor_LIQUILOANS: commontypes.Ownership_LIQUILOANS_PL,
	bePalPb.Vendor_IDFC:       commontypes.Ownership_IDFC_PL,
	bePalPb.Vendor_ABFL:       commontypes.Ownership_LOANS_ABFL,
	bePalPb.Vendor_FIFTYFIN:   commontypes.Ownership_FIFTYFIN_LAMF,
	bePalPb.Vendor_EPIFI_TECH: commontypes.Ownership_EPIFI_TECH_V2,
	bePalPb.Vendor_LENDEN:     commontypes.Ownership_LOANS_LENDEN,
}

type PlTestSuite struct {
	loanOfferDao            dao.LoanOffersDao
	loanAccountDao          dao.LoanAccountsDao
	loanApplicant           dao.LoanApplicantDao
	loanStepExecution       dao.LoanStepExecutionsDao
	loanOfferEligibilityDao dao.LoanOfferEligibilityCriteriaDao
	authDao                 dao2.AuthDao
	fePalClient             fePalPb.PreApprovedLoanClient
	feConsentClient         feConsentPb.ConsentClient
	bePalClient             bePalPb.PreApprovedLoanClient
	beAuthClient            beAuthPb.AuthClient
	authOrchClient          authOrchPb.OrchestratorClient
	signupClient            signupPb.SignupClient
	creditReportClient      creditreportpb.CreditReportClient
	loansDbResourceProvider *storage.DBResourceProvider[*gorm.DB]
	OnbDep                  *app.OnbDep
}

func NewPlTestSuite(
	loanOfferDao dao.LoanOffersDao,
	loanAccountDao dao.LoanAccountsDao,
	loanApplicant dao.LoanApplicantDao,
	loanStepExecution dao.LoanStepExecutionsDao,
	loanOfferEligibilityDao dao.LoanOfferEligibilityCriteriaDao,
	authDao dao2.AuthDao,
	fePalClient fePalPb.PreApprovedLoanClient,
	feConsentClient feConsentPb.ConsentClient,
	bePalClient bePalPb.PreApprovedLoanClient,
	beAuthClient beAuthPb.AuthClient,
	authOrchClient authOrchPb.OrchestratorClient,
	signupClient signupPb.SignupClient,
	creditReportClient creditreportpb.CreditReportClient,
	loansDbResourceProvider *storage.DBResourceProvider[*gorm.DB],
	onbDep *app.OnbDep,
) *PlTestSuite {
	return &PlTestSuite{
		loanOfferDao:            loanOfferDao,
		loanAccountDao:          loanAccountDao,
		loanApplicant:           loanApplicant,
		loanStepExecution:       loanStepExecution,
		loanOfferEligibilityDao: loanOfferEligibilityDao,
		fePalClient:             fePalClient,
		feConsentClient:         feConsentClient,
		bePalClient:             bePalClient,
		beAuthClient:            beAuthClient,
		authOrchClient:          authOrchClient,
		signupClient:            signupClient,
		creditReportClient:      creditReportClient,
		authDao:                 authDao,
		loansDbResourceProvider: loansDbResourceProvider,
		OnbDep:                  onbDep,
	}
}

func (ts *PlTestSuite) CreateLoanOffer(ctx context.Context, offer *bePalPb.LoanOffer) (*bePalPb.LoanOffer, error) {
	ownership, ok := vendorToOwnershipMap[offer.GetVendor()]
	if !ok {
		return nil, fmt.Errorf("db ownership is not configured for the loan offer vendor in vendorToOwnershipMap, vendor : %s", offer.GetVendor())
	}
	ctxWithOwnership := epificontext.WithOwnership(ctx, ownership)

	// create offer in db
	createdOffer, createErr := ts.loanOfferDao.Create(ctxWithOwnership, offer)
	if createErr != nil {
		return nil, fmt.Errorf("error creating loan offer in db, err : %w", createErr)
	}

	return createdOffer, nil
}

func (ts *PlTestSuite) DeactivateLoanOffer(ctx context.Context, vendor bePalPb.Vendor, offerId string) error {
	ownership, ok := vendorToOwnershipMap[vendor]
	if !ok {
		return fmt.Errorf("db ownership is not configured for the loan offer vendor in vendorToOwnershipMap, vendor : %s", vendor)
	}
	ctxWithOwnership := epificontext.WithOwnership(ctx, ownership)

	// create offer in db
	_, getOfferErr := ts.loanOfferDao.GetById(ctxWithOwnership, offerId)
	if getOfferErr != nil {
		return fmt.Errorf("error fetching loan offer from db, err : %w", getOfferErr)
	}

	if deactivationErr := ts.loanOfferDao.DeactivateLoanOffer(ctxWithOwnership, offerId); deactivationErr != nil {
		return fmt.Errorf("error deactivating loan offer in db, err : %w", deactivationErr)
	}
	return nil
}

func (ts *PlTestSuite) CloseLoanAccount(ctx context.Context, vendor bePalPb.Vendor, accountId string) error {
	ownership, ok := vendorToOwnershipMap[vendor]
	if !ok {
		return fmt.Errorf("db ownership is not configured for the loan offer vendor in vendorToOwnershipMap, vendor : %s", vendor)
	}
	ctxWithOwnership := epificontext.WithOwnership(ctx, ownership)

	// create offer in db
	account, getAccountErr := ts.loanAccountDao.GetById(ctxWithOwnership, accountId)
	if getAccountErr != nil {
		return fmt.Errorf("error fetching loan offer from db, err : %w", getAccountErr)
	}

	account.Status = bePalPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED
	account.LoanEndDate = datetime.TimeToDateInLoc(time.Now(), datetime.IST)
	if updateErr := ts.loanAccountDao.Update(ctxWithOwnership, account, []bePalPb.LoanAccountFieldMask{bePalPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_STATUS, bePalPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_LOAN_END_DATE}); updateErr != nil {
		return fmt.Errorf("error update loan account state in db, err : %w", updateErr)
	}
	return nil
}

func (ts *PlTestSuite) CreateLoanApplicant(ctx context.Context, applicant *bePalPb.LoanApplicant) (*bePalPb.LoanApplicant, error) {
	ownership, ok := vendorToOwnershipMap[applicant.GetVendor()]
	if !ok {
		return nil, fmt.Errorf("db ownership is not configured for the loan offer vendor in vendorToOwnershipMap, vendor : %s", applicant.GetVendor())
	}
	ctxWithOwnership := epificontext.WithOwnership(ctx, ownership)

	// create loan applicant
	applicant, createErr := ts.loanApplicant.Create(ctxWithOwnership, applicant)
	// ignore the duplicate entry error as applicant might already be created for a user
	// case: user is retrying the application, or repeated user who was whitelisted previous month as well
	if createErr != nil && !errors.Is(createErr, epifierrors.ErrDuplicateEntry) {
		return nil, fmt.Errorf("error creating loan applicant in db, err : %w", createErr)
	}
	return applicant, nil
}

func (ts *PlTestSuite) CreateLoanOfferEligibilityCriteria(ctx context.Context, loec *bePalPb.LoanOfferEligibilityCriteria) (*bePalPb.LoanOfferEligibilityCriteria, error) {
	ownership, ok := vendorToOwnershipMap[loec.GetVendor()]
	if !ok {
		return nil, fmt.Errorf("db ownership is not configured for the loan offer vendor in vendorToOwnershipMap, vendor : %s", loec.GetVendor())
	}
	ctxWithOwnership := epificontext.WithOwnership(ctx, ownership)

	// Create LOEC using the DAO
	createdLoec, createErr := ts.loanOfferEligibilityDao.Create(ctxWithOwnership, loec)
	if createErr != nil {
		return nil, fmt.Errorf("error creating loan offer eligibility criteria in db, err : %w", createErr)
	}

	return createdLoec, nil
}

func (ts *PlTestSuite) getPlLandingInfo(ctx context.Context, t *testing.T, userAuth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader) *fePalPb.GetLandingInfoResponse {
	// get landing page info
	landingInfoRes, err := ts.fePalClient.GetLandingInfo(ctx, &fePalPb.GetLandingInfoRequest{
		Req:        userAuth,
		LoanHeader: loanHeader,
		LoanType:   palEnumFePb.LoanType_LOAN_TYPE_PERSONAL,
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), landingInfoRes.GetRespHeader().GetStatus().GetCode())
	return landingInfoRes
}

func (ts *PlTestSuite) getOfferDetails(ctx context.Context, t *testing.T, request *fePalPb.GetOfferDetailsRequest) *fePalPb.GetOfferDetailsResponse {
	offerDetailsResponse, err := ts.fePalClient.GetOfferDetails(ctx, request)
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), offerDetailsResponse.GetRespHeader().GetStatus().GetCode())
	return offerDetailsResponse
}

func (ts *PlTestSuite) getApplicationDetails(ctx context.Context, t *testing.T, request *fePalPb.GetApplicationDetailsRequest) *fePalPb.GetApplicationDetailsResponse {
	applicationDetailsResponse, err := ts.fePalClient.GetApplicationDetails(ctx, request)
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), applicationDetailsResponse.GetRespHeader().GetStatus().GetCode())
	return applicationDetailsResponse
}

func (ts *PlTestSuite) applyForLoan(ctx context.Context, t *testing.T, request *fePalPb.ApplyForLoanRequest) *fePalPb.ApplyForLoanResponse {
	applyForLoanRes, err := ts.fePalClient.ApplyForLoan(ctx, request)
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), applyForLoanRes.GetRespHeader().GetStatus().GetCode())
	return applyForLoanRes
}

func (ts *PlTestSuite) getApplicationStatus(ctx context.Context, t *testing.T, userAuth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader, loanRequestId string) *fePalPb.GetApplicationStatusResponse {
	applicationStatusRes, err := ts.fePalClient.GetApplicationStatus(ctx, &fePalPb.GetApplicationStatusRequest{
		Req:           userAuth,
		LoanHeader:    loanHeader,
		LoanRequestId: loanRequestId,
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), applicationStatusRes.GetRespHeader().GetStatus().GetCode())
	return applicationStatusRes
}

func (ts *PlTestSuite) pollLoansStatusScreen(ctx context.Context, t *testing.T, lrId string, userAuth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader, expectedScreen deeplink.Screen) *deeplink.Deeplink {
	var res *fePalPb.GetApplicationStatusResponse
	for attempt := 1; attempt <= maxLoanApplicationPollCount; attempt++ {
		res, _ = ts.fePalClient.GetApplicationStatus(ctx, &fePalPb.GetApplicationStatusRequest{
			Req:           userAuth,
			LoanHeader:    loanHeader,
			LoanRequestId: lrId,
		})
		assert.Equal(t, rpc.StatusOk().GetCode(), res.GetRespHeader().GetStatus().GetCode())
		if res.GetDeeplink().GetScreen() == expectedScreen {
			return res.GetDeeplink()
		}
		time.Sleep(2 * time.Second)
	}
	return nil
}

func (ts *PlTestSuite) pollAuthStatus(ctx context.Context, t *testing.T, clientReqId string, userAuth *header.RequestHeader, expectedScreen deeplink.Screen) *deeplink.Deeplink {
	var res *authOrchPb.GetAuthFlowStatusResponse
	for attempt := 1; attempt <= maxAuthPollCount; attempt++ {
		res, _ = ts.authOrchClient.GetAuthFlowStatus(ctx, &authOrchPb.GetAuthFlowStatusRequest{
			Req:             userAuth,
			ClientRequestId: clientReqId,
		})
		assert.Equal(t, rpc.StatusOk().GetCode(), res.GetRespHeader().GetStatus().GetCode())
		if res.GetNextAction().GetScreen() == expectedScreen {
			return res.GetNextAction()
		}
		time.Sleep(2 * time.Second)
	}
	return nil
}

func (ts *PlTestSuite) pollLivenessSummaryStatus(ctx context.Context, t *testing.T, userAuth *header.RequestHeader, clientReqId string, expectedScreen deeplink.Screen) *deeplink.Deeplink {
	for attempt := 1; attempt <= maxAuthPollCount; attempt++ {
		res, _ := ts.authOrchClient.GetLivenessSummaryStatus(ctx, &authOrchPb.GetLivenessSummaryStatusRequest{
			Req:             userAuth,
			ClientRequestId: clientReqId,
			LivenessFlow:    deeplink.LivenessFlow_PRE_APPROVED_LOAN,
		})
		assert.Equal(t, rpc.StatusOk().GetCode(), res.GetRespHeader().GetStatus().GetCode())
		if res.GetNextAction().GetScreen() == expectedScreen {
			return res.GetNextAction()
		}
		time.Sleep(2 * time.Second)
	}
	return nil
}

func (ts *PlTestSuite) pollCreditReportDownloadStatus(ctx context.Context, t *testing.T, clientReqId string, userAuth *header.RequestHeader, expectedScreen deeplink.Screen) *deeplink.Deeplink {
	for attempt := 1; attempt <= maxCreditReportDownloadPollCount; attempt++ {
		creditReportNextAction, err := ts.creditReportClient.GetReportDownloadNextAction(ctx, &creditreportpb.GetReportDownloadNextActionRequest{
			Req:         userAuth,
			ClientReqId: clientReqId,
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk().GetCode(), creditReportNextAction.GetRespHeader().GetStatus().GetCode())

		// here we can get either consent screen (if consent is not present or expired), or polling screen again
		// if we are getting the consent screen, call the consent rpc to register the user consent to move forward
		if creditReportNextAction.GetNextAction().GetScreen() == deeplink.Screen_CREDIT_REPORT_CONSENT_V2 {
			res, err2 := ts.creditReportClient.RecordDownloadConsent(ctx, &creditreportpb.RecordDownloadConsentRequest{
				Req:           userAuth,
				RequestId:     clientReqId,
				ConsentAction: creditreportpb.ConsentAction_CONSENT_ACTION_ACCEPTED,
			})
			assert.Nil(t, err2)
			assert.Equal(t, rpc.StatusOk().GetCode(), res.GetRespHeader().GetStatus().GetCode())
			continue
		}
		if creditReportNextAction.GetNextAction().GetScreen() == expectedScreen {
			return creditReportNextAction.GetNextAction()
		}
		time.Sleep(2 * time.Second)
	}
	return nil
}

func (ts *PlTestSuite) getLoanDashboard(ctx context.Context, t *testing.T, userAuth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader) *fePalPb.GetDashboardResponse {
	dashboardRes, err := ts.fePalClient.GetDashboard(ctx, &fePalPb.GetDashboardRequest{
		Req:        userAuth,
		LoanHeader: loanHeader,
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), dashboardRes.GetRespHeader().GetStatus().GetCode())
	return dashboardRes
}

func (ts *PlTestSuite) getLoanDetails(ctx context.Context, t *testing.T, userAuth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader, loanId string) *fePalPb.GetLoanDetailsResponse {
	loanDetailsRes, err := ts.fePalClient.GetLoanDetails(ctx, &fePalPb.GetLoanDetailsRequest{
		Req:        userAuth,
		LoanHeader: loanHeader,
		LoanId:     loanId,
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), loanDetailsRes.GetRespHeader().GetStatus().GetCode())

	return loanDetailsRes
}

func (ts *PlTestSuite) getLoanAccountDetails(ctx context.Context, t *testing.T, actorId string, loanHeader *bePalPb.LoanHeader, loanAccountId string) *bePalPb.GetLoanAccountDetailsResponse {
	loanAccountDetailsRes, err := ts.bePalClient.GetLoanAccountDetails(ctx, &bePalPb.GetLoanAccountDetailsRequest{
		ActorId:        actorId,
		LoanHeader:     loanHeader,
		LoanAccountIds: []string{loanAccountId},
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), loanAccountDetailsRes.GetStatus().GetCode())
	return loanAccountDetailsRes
}

func (ts *PlTestSuite) getActorIdUsingAuthHeader(t *testing.T, auth *header.RequestHeader) string {
	validateAuthRes, err := ts.beAuthClient.ValidateToken(context.Background(), &beAuthPb.ValidateTokenRequest{
		Token:     auth.Auth.GetAccessToken(),
		TokenType: beAuthPb.TokenType_ACCESS_TOKEN,
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), validateAuthRes.GetStatus().GetCode())
	assert.NotEmpty(t, validateAuthRes.GetActorId())
	return validateAuthRes.GetActorId()
}
func (ts *PlTestSuite) addPanAndDOBdata(t *testing.T, reqId string, auth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader, dob *date.Date, pan string) *deeplink.Deeplink {
	addPanAndDOBdataReq := fePalPb.AddPanAndDobDataRequest{
		Req:           auth,
		LoanHeader:    loanHeader,
		LoanRequestId: reqId,
		Pan:           pan,
		Dob:           fmt.Sprintf(`%v/%v/%v`, dob.GetDay(), dob.GetMonth(), dob.GetYear()),
	}

	addPanAndDOBres, err := ts.fePalClient.AddPanAndDobData(context.Background(), &addPanAndDOBdataReq)
	assert.Nil(t, err)
	return addPanAndDOBres.GetDeeplink()
}

func (ts *PlTestSuite) getMandateData(ctx context.Context, t *testing.T, auth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader, reqId string) error {
	getMandateViewDataRequest := fePalPb.GetMandateViewDataRequest{
		Req:           auth,
		LoanHeader:    loanHeader,
		LoanRequestId: reqId,
	}

	_, err := ts.fePalClient.GetMandateViewData(ctx, &getMandateViewDataRequest)
	return err
}

func (ts *PlTestSuite) confirmApplication(ctx context.Context, t *testing.T, auth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader, reqId, token string) *deeplink.Deeplink {
	otp, err := ts.authDao.GetOtpByToken(ctx, token)
	if err != nil {
		return nil
	}

	confirmApplicationReq := fePalPb.ConfirmApplicationRequest{
		Req:           auth,
		LoanRequestId: reqId,
		LoanHeader:    loanHeader,
		Token:         token,
		Otp:           otp.Otp,
		OtpFlow:       fePalPb.ConfirmApplicationRequest_OTP_FLOW_E_SIGN,
	}
	resp, err := ts.fePalClient.ConfirmApplication(ctx, &confirmApplicationReq)

	assert.Nil(t, err)
	return resp.GetDeeplink()
}
func (ts *PlTestSuite) generateOTp(ctx context.Context, t *testing.T, auth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader, reqId string) (*fePalPb.GenerateConfirmationCodeResponse, error) {
	generateConfrmationCode := fePalPb.GenerateConfirmationCodeRequest{
		Req:           auth,
		LoanRequestId: reqId,
		LoanHeader:    loanHeader,
	}
	resp, err := ts.fePalClient.GenerateConfirmationCode(ctx, &generateConfrmationCode)
	assert.Nil(t, err)
	return resp, nil
}

func (ts *PlTestSuite) verifyDetails(ctx context.Context, t *testing.T, auth *header.RequestHeader, reqId string, detailsType preapprovedloans.DetailsType, value string) *deeplink.Deeplink {
	var verifyDetailsReq fePalPb.VerifyDetailsRequest
	if detailsType == preapprovedloans.DetailsType_DETAILS_TYPE_OCCUPATION {
		var empType types.EmploymentType
		if value == "1" {
			empType = types.EmploymentType_EMPLOYMENT_TYPE_SALARIED
		} else {
			empType = types.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED
		}
		verifyDetailsReq = fePalPb.VerifyDetailsRequest{
			Req:            auth,
			LoanReqId:      reqId,
			DetailsType:    detailsType,
			EmploymentType: empType,
		}
	} else {
		verifyDetailsReq = fePalPb.VerifyDetailsRequest{
			Req:         auth,
			LoanReqId:   reqId,
			DetailsType: detailsType,
			Value:       value,
		}
	}
	verifyDetailsResp, err := ts.fePalClient.VerifyDetails(ctx, &verifyDetailsReq)
	assert.Nil(t, err)
	return verifyDetailsResp.GetDeeplink()
}

func (ts *PlTestSuite) addAddressDetails(ctx context.Context, t *testing.T, userAuth *header.RequestHeader, lrId string, loanHeader *palEnumFePb.LoanHeader) *deeplink.Deeplink {
	addConfRes, err := ts.fePalClient.AddAddressDetails(ctx, &fePalPb.AddAddressDetailsRequest{
		Req:           userAuth,
		LoanRequestId: lrId,
		Address: &types.PostalAddress{
			PostalCode: "560037",
			// because we want any valid state name to be passed
			AdministrativeArea: "KARNATAKA",
			Locality:           "locality 1",
			AddressLines:       []string{"line 1", "line 2"},
		},
		LoanHeader: loanHeader,
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), addConfRes.GetRespHeader().GetStatus().GetCode())
	return addConfRes.GetDeeplink()
}

func (ts *PlTestSuite) addEmploymentDetailsLl(ctx context.Context, t *testing.T, userAuth *header.RequestHeader, lrId string, loanHeader *palEnumFePb.LoanHeader) *deeplink.Deeplink {
	// validate the add employment details call
	empConfRes, err := ts.fePalClient.AddEmploymentDetails(ctx, &fePalPb.AddEmploymentDetailsRequest{
		Req:           userAuth,
		LoanRequestId: lrId,
		EmploymentDetailsList: []*fePalPb.AddEmploymentDetailsRequest_EmploymentDetails{
			{
				DetailsType: fePalPb.DetailsFormField_OCCUPATION_TYPE,
				DetailValue: &fePalPb.AddEmploymentDetailsRequest_EmploymentDetails_EmploymentValue{EmploymentValue: types.EmploymentType_EMPLOYMENT_TYPE_SALARIED},
			},
			{
				DetailsType: fePalPb.DetailsFormField_OCCUPATION_NAME,
				DetailValue: &fePalPb.AddEmploymentDetailsRequest_EmploymentDetails_StringValue{StringValue: "epifi technologies"},
			},
			{
				DetailsType: fePalPb.DetailsFormField_MONTHLY_INCOME,
				DetailValue: &fePalPb.AddEmploymentDetailsRequest_EmploymentDetails_MoneyValue{MoneyValue: &types.Money{
					CurrencyCode: "INR",
					Units:        98065,
					Decimals:     0,
				}},
			},
			{
				DetailsType: fePalPb.DetailsFormField_OFFICE_ADDRESS,
				DetailValue: &fePalPb.AddEmploymentDetailsRequest_EmploymentDetails_AddressValue{AddressValue: &types.PostalAddress{
					PostalCode:         "560037",
					AdministrativeArea: "administrative area 1",
					Locality:           "locality 1",
					AddressLines:       []string{"line 1", "line 2"},
				}},
			},
			{
				DetailsType: fePalPb.DetailsFormField_WORK_EMAIL_ADDRESS,
				DetailValue: &fePalPb.AddEmploymentDetailsRequest_EmploymentDetails_StringValue{StringValue: "<EMAIL>"},
			},
		},
		LoanHeader: loanHeader,
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), empConfRes.GetRespHeader().GetStatus().GetCode())
	return empConfRes.GetDeeplink()
}

// deleteLoansData clears all the loans data in db as currently repeat loans are not supported. so even is loan account is in closed
// state, user can't take another loan. As we are using the same user to run the integration tests across different flows, we need
// to clear the loans data after each run.
func (ts *PlTestSuite) deleteLoansData(ctx context.Context, t *testing.T, userAuth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader, vendor bePalPb.Vendor) error {
	ownership, ok := vendorToOwnershipMap[vendor]
	if !ok {
		return fmt.Errorf("db ownership is not configured for the loan offer vendor in vendorToOwnershipMap, vendor : %s", vendor)
	}
	ctxWithOwnership := epificontext.WithOwnership(ctx, ownership)

	db, err := GetConnFromContextOrProvider(ctxWithOwnership, ts.loansDbResourceProvider)
	if err != nil {
		return fmt.Errorf("error in getConnFromContextOrProvider")
	}

	actorId := ts.getActorIdUsingAuthHeader(t, userAuth)
	lp := bePalPb.LoanProgram_name[int32(loanHeader.GetLoanProgram())]
	var loanAccountIds []string

	if err := db.Raw("SELECT loan_account_id FROM loan_accounts WHERE actor_id = ? and loan_program=?", actorId, lp).Scan(pq.Array(&loanAccountIds)); err.Error != nil {
		return fmt.Errorf("failed to fetch loan account id with given actor id: %w", err.Error)
	}

	if err = db.Exec("DELETE FROM loan_installment_info WHERE account_id IN (?)", loanAccountIds).Error; err != nil {
		return fmt.Errorf("error deleting loan_installment_info relation: %w", err)
	}

	if err = db.Exec("DELETE FROM loan_activities WHERE loan_account_id IN (?)", loanAccountIds).Error; err != nil {
		return fmt.Errorf("error deleting loan_activities relation: %w", err)
	}

	if err = db.Exec("DELETE FROM loan_requests WHERE actor_id = ? and loan_program = ?", actorId, lp).Error; err != nil {
		return fmt.Errorf("error deleting loan_requests relation: %w", err)
	}

	if err = db.Exec("DELETE FROM loan_accounts WHERE actor_id = ? and loan_program=?", actorId, lp).Error; err != nil {
		return fmt.Errorf("error deleting loan_accounts relation: %w", err)
	}

	if err = db.Exec("DELETE FROM loan_step_executions WHERE actor_id = ?", actorId).Error; err != nil {
		return fmt.Errorf("error deleting loan_step_executions relation: %w", err)
	}

	if err = db.Exec("DELETE FROM loan_offers WHERE actor_id = ? and loan_program = ?", actorId, lp).Error; err != nil {
		return fmt.Errorf("error deleting loan_offers relation: %w", err)
	}

	if err = db.Exec("DELETE FROM loan_offer_eligibility_criteria WHERE actor_id = ?", actorId).Error; err != nil {
		return fmt.Errorf("error deleting loan_offer_eligibility_criteria relation: %w", err)
	}

	if err = db.Exec("DELETE FROM loan_applicants WHERE actor_id = ? and loan_program = ?", actorId, lp).Error; err != nil {
		return fmt.Errorf("error deleting loan_applicants relation: %w", err)
	}

	return nil
}

func GetConnFromContextOrProvider(ctx context.Context, dbResourceProvider *storage.DBResourceProvider[*gorm.DB]) (*gorm.DB, error) {
	ow := epificontext.OwnershipFromContext[context.Context](ctx)
	// default to federal bank
	if ow == commontypes.Ownership_EPIFI_TECH {
		ow = commontypes.Ownership_FEDERAL_BANK
	}
	dbConn, err := dbResourceProvider.GetResourceForOwnership(ow)
	if err != nil {
		return nil, pkgErrors.Wrap(err, "err in GetResourceForOwnership")
	}
	return gormctxv2.FromContextOrDefault(ctx, dbConn), nil
}
func (ts *PlTestSuite) InitiateESign(ctx context.Context, t *testing.T, request *fePalPb.InitiateESignRequest) *fePalPb.InitiateESignResponse {
	initoateEsignRes, err := ts.fePalClient.InitiateESign(ctx, request)
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), initoateEsignRes.GetRespHeader().GetStatus().GetCode())
	return initoateEsignRes
}

func (ts *PlTestSuite) checkLoanEligibility(ctx context.Context, t *testing.T, userAuth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader) *fePalPb.CheckLoanEligibilityResponse {
	eligibilityRes, err := ts.fePalClient.CheckLoanEligibility(ctx, &fePalPb.CheckLoanEligibilityRequest{
		Req:        userAuth,
		LoanHeader: loanHeader,
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), eligibilityRes.GetRespHeader().GetStatus().GetCode())
	return eligibilityRes
}

func (ts *PlTestSuite) collectFormData(ctx context.Context, t *testing.T, request *fePalPb.CollectFormDataRequest) *fePalPb.CollectFormDataResponse {
	formDataRes, err := ts.fePalClient.CollectFormData(ctx, request)
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), formDataRes.GetRespHeader().GetStatus().GetCode())
	return formDataRes
}

func (ts *PlTestSuite) getLoanSummaryForHome(ctx context.Context, t *testing.T, userAuth *header.RequestHeader, loanHeader *palEnumFePb.LoanHeader) *fePalPb.GetLoanSummaryForHomeResponse {
	summaryRes, err := ts.fePalClient.GetLoanSummaryForHome(ctx, &fePalPb.GetLoanSummaryForHomeRequest{
		Req:        userAuth,
		LoanHeader: loanHeader,
	})
	assert.Nil(t, err)
	assert.Equal(t, rpc.StatusOk().GetCode(), summaryRes.GetRespHeader().GetStatus().GetCode())
	return summaryRes
}
