-- PAN upload exhaust MI case, updating status and PAN details after manual review

update onboarding_details
set personal_details = personal_details || jsonb_build_object('panDetails', personal_details->'panDetails' || jsonb_build_object('s3Paths', json_build_array('converted_doc_proof_image/AC220717Ci1qa0TjQNq0qZeT8lcBNA==/a97526a5-8f91-4d5e-82f2-140c097b8264/DOCUMENT_PROOF_TYPE_PAN/0.JPEG'))),
	customer_provided_data=customer_provided_data||jsonb_build_object('pan',jsonb_build_object('proofType', 'DOCUMENT_PROOF_TYPE_PAN', 'id', '**********', 's3Paths', json_build_array('converted_doc_proof_image/AC220717Ci1qa0TjQNq0qZeT8lcBNA==/a97526a5-8f91-4d5e-82f2-140c097b8264/DOCUMENT_PROOF_TYPE_PAN/0.JPEG')))
where id = '5de4b382-555c-418e-83f6-fc35389c6713';
