// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/insights/secrets/secret_analyser.proto

package secrets

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AddSecretToFavouritesRequestParams with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *AddSecretToFavouritesRequestParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddSecretToFavouritesRequestParams
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// AddSecretToFavouritesRequestParamsMultiError, or nil if none found.
func (m *AddSecretToFavouritesRequestParams) ValidateAll() error {
	return m.validate(true)
}

func (m *AddSecretToFavouritesRequestParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SecretId

	if len(errors) > 0 {
		return AddSecretToFavouritesRequestParamsMultiError(errors)
	}

	return nil
}

// AddSecretToFavouritesRequestParamsMultiError is an error wrapping multiple
// validation errors returned by
// AddSecretToFavouritesRequestParams.ValidateAll() if the designated
// constraints aren't met.
type AddSecretToFavouritesRequestParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddSecretToFavouritesRequestParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddSecretToFavouritesRequestParamsMultiError) AllErrors() []error { return m }

// AddSecretToFavouritesRequestParamsValidationError is the validation error
// returned by AddSecretToFavouritesRequestParams.Validate if the designated
// constraints aren't met.
type AddSecretToFavouritesRequestParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddSecretToFavouritesRequestParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddSecretToFavouritesRequestParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddSecretToFavouritesRequestParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddSecretToFavouritesRequestParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddSecretToFavouritesRequestParamsValidationError) ErrorName() string {
	return "AddSecretToFavouritesRequestParamsValidationError"
}

// Error satisfies the builtin error interface
func (e AddSecretToFavouritesRequestParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddSecretToFavouritesRequestParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddSecretToFavouritesRequestParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddSecretToFavouritesRequestParamsValidationError{}

// Validate checks the field values on SecretAnalyser with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SecretAnalyser) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecretAnalyser with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SecretAnalyserMultiError,
// or nil if none found.
func (m *SecretAnalyser) ValidateAll() error {
	return m.validate(true)
}

func (m *SecretAnalyser) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretAnalyserValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretAnalyserValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretAnalyserValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetComponentList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserValidationError{
						field:  fmt.Sprintf("ComponentList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserValidationError{
						field:  fmt.Sprintf("ComponentList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserValidationError{
					field:  fmt.Sprintf("ComponentList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetFooter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretAnalyserValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretAnalyserValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretAnalyserValidationError{
				field:  "Footer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRevealAnimationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretAnalyserValidationError{
					field:  "RevealAnimationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretAnalyserValidationError{
					field:  "RevealAnimationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRevealAnimationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretAnalyserValidationError{
				field:  "RevealAnimationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if len(errors) > 0 {
		return SecretAnalyserMultiError(errors)
	}

	return nil
}

// SecretAnalyserMultiError is an error wrapping multiple validation errors
// returned by SecretAnalyser.ValidateAll() if the designated constraints
// aren't met.
type SecretAnalyserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecretAnalyserMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecretAnalyserMultiError) AllErrors() []error { return m }

// SecretAnalyserValidationError is the validation error returned by
// SecretAnalyser.Validate if the designated constraints aren't met.
type SecretAnalyserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecretAnalyserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecretAnalyserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecretAnalyserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecretAnalyserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecretAnalyserValidationError) ErrorName() string { return "SecretAnalyserValidationError" }

// Error satisfies the builtin error interface
func (e SecretAnalyserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecretAnalyser.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecretAnalyserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecretAnalyserValidationError{}

// Validate checks the field values on SecretAnalyserHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecretAnalyserHeader) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecretAnalyserHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecretAnalyserHeaderMultiError, or nil if none found.
func (m *SecretAnalyserHeader) ValidateAll() error {
	return m.validate(true)
}

func (m *SecretAnalyserHeader) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretAnalyserHeaderValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretAnalyserHeaderValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretAnalyserHeaderValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFavouriteIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretAnalyserHeaderValidationError{
					field:  "FavouriteIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretAnalyserHeaderValidationError{
					field:  "FavouriteIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFavouriteIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretAnalyserHeaderValidationError{
				field:  "FavouriteIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSecretFavouriteReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretAnalyserHeaderValidationError{
					field:  "SecretFavouriteReq",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretAnalyserHeaderValidationError{
					field:  "SecretFavouriteReq",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSecretFavouriteReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretAnalyserHeaderValidationError{
				field:  "SecretFavouriteReq",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetShareIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretAnalyserHeaderValidationError{
					field:  "ShareIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretAnalyserHeaderValidationError{
					field:  "ShareIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShareIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretAnalyserHeaderValidationError{
				field:  "ShareIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.ShareIconV2.(type) {
	case *SecretAnalyserHeader_ShareStaticIcon:
		if v == nil {
			err := SecretAnalyserHeaderValidationError{
				field:  "ShareIconV2",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetShareStaticIcon()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserHeaderValidationError{
						field:  "ShareStaticIcon",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserHeaderValidationError{
						field:  "ShareStaticIcon",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetShareStaticIcon()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserHeaderValidationError{
					field:  "ShareStaticIcon",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SecretAnalyserHeaderMultiError(errors)
	}

	return nil
}

// SecretAnalyserHeaderMultiError is an error wrapping multiple validation
// errors returned by SecretAnalyserHeader.ValidateAll() if the designated
// constraints aren't met.
type SecretAnalyserHeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecretAnalyserHeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecretAnalyserHeaderMultiError) AllErrors() []error { return m }

// SecretAnalyserHeaderValidationError is the validation error returned by
// SecretAnalyserHeader.Validate if the designated constraints aren't met.
type SecretAnalyserHeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecretAnalyserHeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecretAnalyserHeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecretAnalyserHeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecretAnalyserHeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecretAnalyserHeaderValidationError) ErrorName() string {
	return "SecretAnalyserHeaderValidationError"
}

// Error satisfies the builtin error interface
func (e SecretAnalyserHeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecretAnalyserHeader.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecretAnalyserHeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecretAnalyserHeaderValidationError{}

// Validate checks the field values on SecretAnalyserShareStaticIcon with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecretAnalyserShareStaticIcon) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecretAnalyserShareStaticIcon with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SecretAnalyserShareStaticIconMultiError, or nil if none found.
func (m *SecretAnalyserShareStaticIcon) ValidateAll() error {
	return m.validate(true)
}

func (m *SecretAnalyserShareStaticIcon) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetShareIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretAnalyserShareStaticIconValidationError{
					field:  "ShareIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretAnalyserShareStaticIconValidationError{
					field:  "ShareIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShareIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretAnalyserShareStaticIconValidationError{
				field:  "ShareIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShareText

	if len(errors) > 0 {
		return SecretAnalyserShareStaticIconMultiError(errors)
	}

	return nil
}

// SecretAnalyserShareStaticIconMultiError is an error wrapping multiple
// validation errors returned by SecretAnalyserShareStaticIcon.ValidateAll()
// if the designated constraints aren't met.
type SecretAnalyserShareStaticIconMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecretAnalyserShareStaticIconMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecretAnalyserShareStaticIconMultiError) AllErrors() []error { return m }

// SecretAnalyserShareStaticIconValidationError is the validation error
// returned by SecretAnalyserShareStaticIcon.Validate if the designated
// constraints aren't met.
type SecretAnalyserShareStaticIconValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecretAnalyserShareStaticIconValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecretAnalyserShareStaticIconValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecretAnalyserShareStaticIconValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecretAnalyserShareStaticIconValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecretAnalyserShareStaticIconValidationError) ErrorName() string {
	return "SecretAnalyserShareStaticIconValidationError"
}

// Error satisfies the builtin error interface
func (e SecretAnalyserShareStaticIconValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecretAnalyserShareStaticIcon.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecretAnalyserShareStaticIconValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecretAnalyserShareStaticIconValidationError{}

// Validate checks the field values on SecretAnalyserComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecretAnalyserComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecretAnalyserComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecretAnalyserComponentMultiError, or nil if none found.
func (m *SecretAnalyserComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *SecretAnalyserComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComponentName

	switch v := m.Component.(type) {
	case *SecretAnalyserComponent_AnalyserCard:
		if v == nil {
			err := SecretAnalyserComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAnalyserCard()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserComponentValidationError{
						field:  "AnalyserCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserComponentValidationError{
						field:  "AnalyserCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAnalyserCard()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserComponentValidationError{
					field:  "AnalyserCard",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretAnalyserComponent_LineItemGroup:
		if v == nil {
			err := SecretAnalyserComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLineItemGroup()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserComponentValidationError{
						field:  "LineItemGroup",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserComponentValidationError{
						field:  "LineItemGroup",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLineItemGroup()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserComponentValidationError{
					field:  "LineItemGroup",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretAnalyserComponent_TextExplainerSection:
		if v == nil {
			err := SecretAnalyserComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTextExplainerSection()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserComponentValidationError{
						field:  "TextExplainerSection",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserComponentValidationError{
						field:  "TextExplainerSection",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTextExplainerSection()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserComponentValidationError{
					field:  "TextExplainerSection",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretAnalyserComponent_LearnOnTheGoSection:
		if v == nil {
			err := SecretAnalyserComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLearnOnTheGoSection()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserComponentValidationError{
						field:  "LearnOnTheGoSection",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserComponentValidationError{
						field:  "LearnOnTheGoSection",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLearnOnTheGoSection()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserComponentValidationError{
					field:  "LearnOnTheGoSection",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretAnalyserComponent_ActionableBanner:
		if v == nil {
			err := SecretAnalyserComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetActionableBanner()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserComponentValidationError{
						field:  "ActionableBanner",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserComponentValidationError{
						field:  "ActionableBanner",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetActionableBanner()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserComponentValidationError{
					field:  "ActionableBanner",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretAnalyserComponent_ActionableBannerV2:
		if v == nil {
			err := SecretAnalyserComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetActionableBannerV2()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserComponentValidationError{
						field:  "ActionableBannerV2",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserComponentValidationError{
						field:  "ActionableBannerV2",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetActionableBannerV2()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserComponentValidationError{
					field:  "ActionableBannerV2",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretAnalyserComponent_SecretCollectionComponent:
		if v == nil {
			err := SecretAnalyserComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSecretCollectionComponent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserComponentValidationError{
						field:  "SecretCollectionComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserComponentValidationError{
						field:  "SecretCollectionComponent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSecretCollectionComponent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserComponentValidationError{
					field:  "SecretCollectionComponent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SecretAnalyserComponentMultiError(errors)
	}

	return nil
}

// SecretAnalyserComponentMultiError is an error wrapping multiple validation
// errors returned by SecretAnalyserComponent.ValidateAll() if the designated
// constraints aren't met.
type SecretAnalyserComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecretAnalyserComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecretAnalyserComponentMultiError) AllErrors() []error { return m }

// SecretAnalyserComponentValidationError is the validation error returned by
// SecretAnalyserComponent.Validate if the designated constraints aren't met.
type SecretAnalyserComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecretAnalyserComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecretAnalyserComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecretAnalyserComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecretAnalyserComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecretAnalyserComponentValidationError) ErrorName() string {
	return "SecretAnalyserComponentValidationError"
}

// Error satisfies the builtin error interface
func (e SecretAnalyserComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecretAnalyserComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecretAnalyserComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecretAnalyserComponentValidationError{}

// Validate checks the field values on SecretsAnalyserCard with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecretsAnalyserCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecretsAnalyserCard with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecretsAnalyserCardMultiError, or nil if none found.
func (m *SecretsAnalyserCard) ValidateAll() error {
	return m.validate(true)
}

func (m *SecretsAnalyserCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetProTip()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretsAnalyserCardValidationError{
					field:  "ProTip",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretsAnalyserCardValidationError{
					field:  "ProTip",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProTip()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretsAnalyserCardValidationError{
				field:  "ProTip",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Visualisation.(type) {
	case *SecretsAnalyserCard_NumberCard:
		if v == nil {
			err := SecretsAnalyserCardValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNumberCard()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "NumberCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "NumberCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNumberCard()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretsAnalyserCardValidationError{
					field:  "NumberCard",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretsAnalyserCard_ImageCard:
		if v == nil {
			err := SecretsAnalyserCardValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetImageCard()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "ImageCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "ImageCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetImageCard()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretsAnalyserCardValidationError{
					field:  "ImageCard",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretsAnalyserCard_BarChartCard:
		if v == nil {
			err := SecretsAnalyserCardValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBarChartCard()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "BarChartCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "BarChartCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBarChartCard()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretsAnalyserCardValidationError{
					field:  "BarChartCard",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretsAnalyserCard_MultiLineChartCard:
		if v == nil {
			err := SecretsAnalyserCardValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMultiLineChartCard()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "MultiLineChartCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "MultiLineChartCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMultiLineChartCard()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretsAnalyserCardValidationError{
					field:  "MultiLineChartCard",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretsAnalyserCard_SpiderGraphCard:
		if v == nil {
			err := SecretsAnalyserCardValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSpiderGraphCard()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "SpiderGraphCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "SpiderGraphCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSpiderGraphCard()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretsAnalyserCardValidationError{
					field:  "SpiderGraphCard",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretsAnalyserCard_DonutCard:
		if v == nil {
			err := SecretsAnalyserCardValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDonutCard()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "DonutCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "DonutCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDonutCard()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretsAnalyserCardValidationError{
					field:  "DonutCard",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretsAnalyserCard_AreaChartCard:
		if v == nil {
			err := SecretsAnalyserCardValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAreaChartCard()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "AreaChartCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "AreaChartCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAreaChartCard()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretsAnalyserCardValidationError{
					field:  "AreaChartCard",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretsAnalyserCard_GridVisualisationCard:
		if v == nil {
			err := SecretsAnalyserCardValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGridVisualisationCard()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "GridVisualisationCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretsAnalyserCardValidationError{
						field:  "GridVisualisationCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGridVisualisationCard()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretsAnalyserCardValidationError{
					field:  "GridVisualisationCard",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SecretsAnalyserCardMultiError(errors)
	}

	return nil
}

// SecretsAnalyserCardMultiError is an error wrapping multiple validation
// errors returned by SecretsAnalyserCard.ValidateAll() if the designated
// constraints aren't met.
type SecretsAnalyserCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecretsAnalyserCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecretsAnalyserCardMultiError) AllErrors() []error { return m }

// SecretsAnalyserCardValidationError is the validation error returned by
// SecretsAnalyserCard.Validate if the designated constraints aren't met.
type SecretsAnalyserCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecretsAnalyserCardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecretsAnalyserCardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecretsAnalyserCardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecretsAnalyserCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecretsAnalyserCardValidationError) ErrorName() string {
	return "SecretsAnalyserCardValidationError"
}

// Error satisfies the builtin error interface
func (e SecretsAnalyserCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecretsAnalyserCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecretsAnalyserCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecretsAnalyserCardValidationError{}

// Validate checks the field values on LineItemCardWithAnalyserFilter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LineItemCardWithAnalyserFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LineItemCardWithAnalyserFilter with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LineItemCardWithAnalyserFilterMultiError, or nil if none found.
func (m *LineItemCardWithAnalyserFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *LineItemCardWithAnalyserFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilterSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemCardWithAnalyserFilterValidationError{
					field:  "FilterSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemCardWithAnalyserFilterValidationError{
					field:  "FilterSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilterSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemCardWithAnalyserFilterValidationError{
				field:  "FilterSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLeftAlignedHeading()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemCardWithAnalyserFilterValidationError{
					field:  "LeftAlignedHeading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemCardWithAnalyserFilterValidationError{
					field:  "LeftAlignedHeading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftAlignedHeading()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemCardWithAnalyserFilterValidationError{
				field:  "LeftAlignedHeading",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightAlignedHeading()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemCardWithAnalyserFilterValidationError{
					field:  "RightAlignedHeading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemCardWithAnalyserFilterValidationError{
					field:  "RightAlignedHeading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightAlignedHeading()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemCardWithAnalyserFilterValidationError{
				field:  "RightAlignedHeading",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLineItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LineItemCardWithAnalyserFilterValidationError{
						field:  fmt.Sprintf("LineItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LineItemCardWithAnalyserFilterValidationError{
						field:  fmt.Sprintf("LineItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LineItemCardWithAnalyserFilterValidationError{
					field:  fmt.Sprintf("LineItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetSeeMore()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemCardWithAnalyserFilterValidationError{
					field:  "SeeMore",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemCardWithAnalyserFilterValidationError{
					field:  "SeeMore",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSeeMore()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemCardWithAnalyserFilterValidationError{
				field:  "SeeMore",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ItemsLimitOnLanding

	// no validation rules for ItemsLimitOnSeeMore

	if len(errors) > 0 {
		return LineItemCardWithAnalyserFilterMultiError(errors)
	}

	return nil
}

// LineItemCardWithAnalyserFilterMultiError is an error wrapping multiple
// validation errors returned by LineItemCardWithAnalyserFilter.ValidateAll()
// if the designated constraints aren't met.
type LineItemCardWithAnalyserFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LineItemCardWithAnalyserFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LineItemCardWithAnalyserFilterMultiError) AllErrors() []error { return m }

// LineItemCardWithAnalyserFilterValidationError is the validation error
// returned by LineItemCardWithAnalyserFilter.Validate if the designated
// constraints aren't met.
type LineItemCardWithAnalyserFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LineItemCardWithAnalyserFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LineItemCardWithAnalyserFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LineItemCardWithAnalyserFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LineItemCardWithAnalyserFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LineItemCardWithAnalyserFilterValidationError) ErrorName() string {
	return "LineItemCardWithAnalyserFilterValidationError"
}

// Error satisfies the builtin error interface
func (e LineItemCardWithAnalyserFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLineItemCardWithAnalyserFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LineItemCardWithAnalyserFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LineItemCardWithAnalyserFilterValidationError{}

// Validate checks the field values on TextExplainerSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TextExplainerSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TextExplainerSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TextExplainerSectionMultiError, or nil if none found.
func (m *TextExplainerSection) ValidateAll() error {
	return m.validate(true)
}

func (m *TextExplainerSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TextExplainerSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TextExplainerSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TextExplainerSectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDescription() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TextExplainerSectionValidationError{
						field:  fmt.Sprintf("Description[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TextExplainerSectionValidationError{
						field:  fmt.Sprintf("Description[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TextExplainerSectionValidationError{
					field:  fmt.Sprintf("Description[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BgColor

	// no validation rules for CornerRadius

	// no validation rules for BorderColor

	if all {
		switch v := interface{}(m.GetCardExpandableControl()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TextExplainerSectionValidationError{
					field:  "CardExpandableControl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TextExplainerSectionValidationError{
					field:  "CardExpandableControl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardExpandableControl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TextExplainerSectionValidationError{
				field:  "CardExpandableControl",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCtas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TextExplainerSectionValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TextExplainerSectionValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TextExplainerSectionValidationError{
					field:  fmt.Sprintf("Ctas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TextExplainerSectionMultiError(errors)
	}

	return nil
}

// TextExplainerSectionMultiError is an error wrapping multiple validation
// errors returned by TextExplainerSection.ValidateAll() if the designated
// constraints aren't met.
type TextExplainerSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TextExplainerSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TextExplainerSectionMultiError) AllErrors() []error { return m }

// TextExplainerSectionValidationError is the validation error returned by
// TextExplainerSection.Validate if the designated constraints aren't met.
type TextExplainerSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TextExplainerSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TextExplainerSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TextExplainerSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TextExplainerSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TextExplainerSectionValidationError) ErrorName() string {
	return "TextExplainerSectionValidationError"
}

// Error satisfies the builtin error interface
func (e TextExplainerSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTextExplainerSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TextExplainerSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TextExplainerSectionValidationError{}

// Validate checks the field values on SecretAnalyserFooter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecretAnalyserFooter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecretAnalyserFooter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecretAnalyserFooterMultiError, or nil if none found.
func (m *SecretAnalyserFooter) ValidateAll() error {
	return m.validate(true)
}

func (m *SecretAnalyserFooter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BgColor

	for idx, item := range m.GetTiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserFooterValidationError{
						field:  fmt.Sprintf("Tiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserFooterValidationError{
						field:  fmt.Sprintf("Tiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserFooterValidationError{
					field:  fmt.Sprintf("Tiles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SecretAnalyserFooterMultiError(errors)
	}

	return nil
}

// SecretAnalyserFooterMultiError is an error wrapping multiple validation
// errors returned by SecretAnalyserFooter.ValidateAll() if the designated
// constraints aren't met.
type SecretAnalyserFooterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecretAnalyserFooterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecretAnalyserFooterMultiError) AllErrors() []error { return m }

// SecretAnalyserFooterValidationError is the validation error returned by
// SecretAnalyserFooter.Validate if the designated constraints aren't met.
type SecretAnalyserFooterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecretAnalyserFooterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecretAnalyserFooterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecretAnalyserFooterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecretAnalyserFooterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecretAnalyserFooterValidationError) ErrorName() string {
	return "SecretAnalyserFooterValidationError"
}

// Error satisfies the builtin error interface
func (e SecretAnalyserFooterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecretAnalyserFooter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecretAnalyserFooterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecretAnalyserFooterValidationError{}

// Validate checks the field values on SecretsSpiderGraph with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecretsSpiderGraph) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecretsSpiderGraph with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecretsSpiderGraphMultiError, or nil if none found.
func (m *SecretsSpiderGraph) ValidateAll() error {
	return m.validate(true)
}

func (m *SecretsSpiderGraph) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleValuePair()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretsSpiderGraphValidationError{
					field:  "TitleValuePair",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretsSpiderGraphValidationError{
					field:  "TitleValuePair",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleValuePair()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretsSpiderGraphValidationError{
				field:  "TitleValuePair",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScoreInsight()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretsSpiderGraphValidationError{
					field:  "ScoreInsight",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretsSpiderGraphValidationError{
					field:  "ScoreInsight",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScoreInsight()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretsSpiderGraphValidationError{
				field:  "ScoreInsight",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSpiderWebParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretsSpiderGraphValidationError{
					field:  "SpiderWebParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretsSpiderGraphValidationError{
					field:  "SpiderWebParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSpiderWebParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretsSpiderGraphValidationError{
				field:  "SpiderWebParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFooter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretsSpiderGraphValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretsSpiderGraphValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretsSpiderGraphValidationError{
				field:  "Footer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if len(errors) > 0 {
		return SecretsSpiderGraphMultiError(errors)
	}

	return nil
}

// SecretsSpiderGraphMultiError is an error wrapping multiple validation errors
// returned by SecretsSpiderGraph.ValidateAll() if the designated constraints
// aren't met.
type SecretsSpiderGraphMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecretsSpiderGraphMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecretsSpiderGraphMultiError) AllErrors() []error { return m }

// SecretsSpiderGraphValidationError is the validation error returned by
// SecretsSpiderGraph.Validate if the designated constraints aren't met.
type SecretsSpiderGraphValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecretsSpiderGraphValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecretsSpiderGraphValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecretsSpiderGraphValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecretsSpiderGraphValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecretsSpiderGraphValidationError) ErrorName() string {
	return "SecretsSpiderGraphValidationError"
}

// Error satisfies the builtin error interface
func (e SecretsSpiderGraphValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecretsSpiderGraph.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecretsSpiderGraphValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecretsSpiderGraphValidationError{}

// Validate checks the field values on LearnOnTheGoSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LearnOnTheGoSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LearnOnTheGoSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LearnOnTheGoSectionMultiError, or nil if none found.
func (m *LearnOnTheGoSection) ValidateAll() error {
	return m.validate(true)
}

func (m *LearnOnTheGoSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LearnOnTheGoSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LearnOnTheGoSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LearnOnTheGoSectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSectionItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LearnOnTheGoSectionValidationError{
						field:  fmt.Sprintf("SectionItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LearnOnTheGoSectionValidationError{
						field:  fmt.Sprintf("SectionItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LearnOnTheGoSectionValidationError{
					field:  fmt.Sprintf("SectionItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return LearnOnTheGoSectionMultiError(errors)
	}

	return nil
}

// LearnOnTheGoSectionMultiError is an error wrapping multiple validation
// errors returned by LearnOnTheGoSection.ValidateAll() if the designated
// constraints aren't met.
type LearnOnTheGoSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LearnOnTheGoSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LearnOnTheGoSectionMultiError) AllErrors() []error { return m }

// LearnOnTheGoSectionValidationError is the validation error returned by
// LearnOnTheGoSection.Validate if the designated constraints aren't met.
type LearnOnTheGoSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LearnOnTheGoSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LearnOnTheGoSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LearnOnTheGoSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LearnOnTheGoSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LearnOnTheGoSectionValidationError) ErrorName() string {
	return "LearnOnTheGoSectionValidationError"
}

// Error satisfies the builtin error interface
func (e LearnOnTheGoSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLearnOnTheGoSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LearnOnTheGoSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LearnOnTheGoSectionValidationError{}

// Validate checks the field values on LearnOnTheGoSectionItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LearnOnTheGoSectionItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LearnOnTheGoSectionItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LearnOnTheGoSectionItemMultiError, or nil if none found.
func (m *LearnOnTheGoSectionItem) ValidateAll() error {
	return m.validate(true)
}

func (m *LearnOnTheGoSectionItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LearnOnTheGoSectionItemValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInfoTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemValidationError{
					field:  "InfoTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemValidationError{
					field:  "InfoTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LearnOnTheGoSectionItemValidationError{
				field:  "InfoTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LearnOnTheGoSectionItemValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOverlayColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemValidationError{
					field:  "OverlayColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemValidationError{
					field:  "OverlayColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOverlayColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LearnOnTheGoSectionItemValidationError{
				field:  "OverlayColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LearnOnTheGoSectionItemValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if len(errors) > 0 {
		return LearnOnTheGoSectionItemMultiError(errors)
	}

	return nil
}

// LearnOnTheGoSectionItemMultiError is an error wrapping multiple validation
// errors returned by LearnOnTheGoSectionItem.ValidateAll() if the designated
// constraints aren't met.
type LearnOnTheGoSectionItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LearnOnTheGoSectionItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LearnOnTheGoSectionItemMultiError) AllErrors() []error { return m }

// LearnOnTheGoSectionItemValidationError is the validation error returned by
// LearnOnTheGoSectionItem.Validate if the designated constraints aren't met.
type LearnOnTheGoSectionItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LearnOnTheGoSectionItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LearnOnTheGoSectionItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LearnOnTheGoSectionItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LearnOnTheGoSectionItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LearnOnTheGoSectionItemValidationError) ErrorName() string {
	return "LearnOnTheGoSectionItemValidationError"
}

// Error satisfies the builtin error interface
func (e LearnOnTheGoSectionItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLearnOnTheGoSectionItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LearnOnTheGoSectionItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LearnOnTheGoSectionItemValidationError{}

// Validate checks the field values on ActionableBanner with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ActionableBanner) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActionableBanner with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActionableBannerMultiError, or nil if none found.
func (m *ActionableBanner) ValidateAll() error {
	return m.validate(true)
}

func (m *ActionableBanner) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLeftImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerValidationError{
					field:  "LeftImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerValidationError{
					field:  "LeftImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerValidationError{
				field:  "LeftImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerValidationError{
					field:  "RightComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerValidationError{
					field:  "RightComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerValidationError{
				field:  "RightComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CornerRadius

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EventProperties

	if all {
		switch v := interface{}(m.GetBorderProperty()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerValidationError{
					field:  "BorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerValidationError{
					field:  "BorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorderProperty()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerValidationError{
				field:  "BorderProperty",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ActionableBannerMultiError(errors)
	}

	return nil
}

// ActionableBannerMultiError is an error wrapping multiple validation errors
// returned by ActionableBanner.ValidateAll() if the designated constraints
// aren't met.
type ActionableBannerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionableBannerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionableBannerMultiError) AllErrors() []error { return m }

// ActionableBannerValidationError is the validation error returned by
// ActionableBanner.Validate if the designated constraints aren't met.
type ActionableBannerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionableBannerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionableBannerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionableBannerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionableBannerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionableBannerValidationError) ErrorName() string { return "ActionableBannerValidationError" }

// Error satisfies the builtin error interface
func (e ActionableBannerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActionableBanner.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionableBannerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionableBannerValidationError{}

// Validate checks the field values on ActionableBannerV2 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActionableBannerV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActionableBannerV2 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActionableBannerV2MultiError, or nil if none found.
func (m *ActionableBannerV2) ValidateAll() error {
	return m.validate(true)
}

func (m *ActionableBannerV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerV2ValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerV2ValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerV2ValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerV2ValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerV2ValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerV2ValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerV2ValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerV2ValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerV2ValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CornerRadius

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerV2ValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerV2ValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerV2ValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerV2ValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerV2ValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerV2ValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EventProperties

	if len(errors) > 0 {
		return ActionableBannerV2MultiError(errors)
	}

	return nil
}

// ActionableBannerV2MultiError is an error wrapping multiple validation errors
// returned by ActionableBannerV2.ValidateAll() if the designated constraints
// aren't met.
type ActionableBannerV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionableBannerV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionableBannerV2MultiError) AllErrors() []error { return m }

// ActionableBannerV2ValidationError is the validation error returned by
// ActionableBannerV2.Validate if the designated constraints aren't met.
type ActionableBannerV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionableBannerV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionableBannerV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionableBannerV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionableBannerV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionableBannerV2ValidationError) ErrorName() string {
	return "ActionableBannerV2ValidationError"
}

// Error satisfies the builtin error interface
func (e ActionableBannerV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActionableBannerV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionableBannerV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionableBannerV2ValidationError{}

// Validate checks the field values on
// LineItemCardWithAnalyserFilter_FilterSection with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LineItemCardWithAnalyserFilter_FilterSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LineItemCardWithAnalyserFilter_FilterSection with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// LineItemCardWithAnalyserFilter_FilterSectionMultiError, or nil if none found.
func (m *LineItemCardWithAnalyserFilter_FilterSection) ValidateAll() error {
	return m.validate(true)
}

func (m *LineItemCardWithAnalyserFilter_FilterSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFilterParams() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LineItemCardWithAnalyserFilter_FilterSectionValidationError{
						field:  fmt.Sprintf("FilterParams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LineItemCardWithAnalyserFilter_FilterSectionValidationError{
						field:  fmt.Sprintf("FilterParams[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LineItemCardWithAnalyserFilter_FilterSectionValidationError{
					field:  fmt.Sprintf("FilterParams[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BgColor

	if len(errors) > 0 {
		return LineItemCardWithAnalyserFilter_FilterSectionMultiError(errors)
	}

	return nil
}

// LineItemCardWithAnalyserFilter_FilterSectionMultiError is an error wrapping
// multiple validation errors returned by
// LineItemCardWithAnalyserFilter_FilterSection.ValidateAll() if the
// designated constraints aren't met.
type LineItemCardWithAnalyserFilter_FilterSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LineItemCardWithAnalyserFilter_FilterSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LineItemCardWithAnalyserFilter_FilterSectionMultiError) AllErrors() []error { return m }

// LineItemCardWithAnalyserFilter_FilterSectionValidationError is the
// validation error returned by
// LineItemCardWithAnalyserFilter_FilterSection.Validate if the designated
// constraints aren't met.
type LineItemCardWithAnalyserFilter_FilterSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LineItemCardWithAnalyserFilter_FilterSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LineItemCardWithAnalyserFilter_FilterSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LineItemCardWithAnalyserFilter_FilterSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LineItemCardWithAnalyserFilter_FilterSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LineItemCardWithAnalyserFilter_FilterSectionValidationError) ErrorName() string {
	return "LineItemCardWithAnalyserFilter_FilterSectionValidationError"
}

// Error satisfies the builtin error interface
func (e LineItemCardWithAnalyserFilter_FilterSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLineItemCardWithAnalyserFilter_FilterSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LineItemCardWithAnalyserFilter_FilterSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LineItemCardWithAnalyserFilter_FilterSectionValidationError{}

// Validate checks the field values on
// LineItemCardWithAnalyserFilter_FilterParameter with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LineItemCardWithAnalyserFilter_FilterParameter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LineItemCardWithAnalyserFilter_FilterParameter with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// LineItemCardWithAnalyserFilter_FilterParameterMultiError, or nil if none found.
func (m *LineItemCardWithAnalyserFilter_FilterParameter) ValidateAll() error {
	return m.validate(true)
}

func (m *LineItemCardWithAnalyserFilter_FilterParameter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDisplayValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemCardWithAnalyserFilter_FilterParameterValidationError{
					field:  "DisplayValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemCardWithAnalyserFilter_FilterParameterValidationError{
					field:  "DisplayValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemCardWithAnalyserFilter_FilterParameterValidationError{
				field:  "DisplayValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFilterWidget()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemCardWithAnalyserFilter_FilterParameterValidationError{
					field:  "FilterWidget",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemCardWithAnalyserFilter_FilterParameterValidationError{
					field:  "FilterWidget",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilterWidget()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemCardWithAnalyserFilter_FilterParameterValidationError{
				field:  "FilterWidget",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LineItemCardWithAnalyserFilter_FilterParameterMultiError(errors)
	}

	return nil
}

// LineItemCardWithAnalyserFilter_FilterParameterMultiError is an error
// wrapping multiple validation errors returned by
// LineItemCardWithAnalyserFilter_FilterParameter.ValidateAll() if the
// designated constraints aren't met.
type LineItemCardWithAnalyserFilter_FilterParameterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LineItemCardWithAnalyserFilter_FilterParameterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LineItemCardWithAnalyserFilter_FilterParameterMultiError) AllErrors() []error { return m }

// LineItemCardWithAnalyserFilter_FilterParameterValidationError is the
// validation error returned by
// LineItemCardWithAnalyserFilter_FilterParameter.Validate if the designated
// constraints aren't met.
type LineItemCardWithAnalyserFilter_FilterParameterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LineItemCardWithAnalyserFilter_FilterParameterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LineItemCardWithAnalyserFilter_FilterParameterValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LineItemCardWithAnalyserFilter_FilterParameterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LineItemCardWithAnalyserFilter_FilterParameterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LineItemCardWithAnalyserFilter_FilterParameterValidationError) ErrorName() string {
	return "LineItemCardWithAnalyserFilter_FilterParameterValidationError"
}

// Error satisfies the builtin error interface
func (e LineItemCardWithAnalyserFilter_FilterParameterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLineItemCardWithAnalyserFilter_FilterParameter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LineItemCardWithAnalyserFilter_FilterParameterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LineItemCardWithAnalyserFilter_FilterParameterValidationError{}

// Validate checks the field values on TextExplainerSection_ExpandableControl
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *TextExplainerSection_ExpandableControl) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// TextExplainerSection_ExpandableControl with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// TextExplainerSection_ExpandableControlMultiError, or nil if none found.
func (m *TextExplainerSection_ExpandableControl) ValidateAll() error {
	return m.validate(true)
}

func (m *TextExplainerSection_ExpandableControl) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsExpanded

	if all {
		switch v := interface{}(m.GetChevronImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TextExplainerSection_ExpandableControlValidationError{
					field:  "ChevronImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TextExplainerSection_ExpandableControlValidationError{
					field:  "ChevronImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChevronImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TextExplainerSection_ExpandableControlValidationError{
				field:  "ChevronImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TextExplainerSection_ExpandableControlMultiError(errors)
	}

	return nil
}

// TextExplainerSection_ExpandableControlMultiError is an error wrapping
// multiple validation errors returned by
// TextExplainerSection_ExpandableControl.ValidateAll() if the designated
// constraints aren't met.
type TextExplainerSection_ExpandableControlMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TextExplainerSection_ExpandableControlMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TextExplainerSection_ExpandableControlMultiError) AllErrors() []error { return m }

// TextExplainerSection_ExpandableControlValidationError is the validation
// error returned by TextExplainerSection_ExpandableControl.Validate if the
// designated constraints aren't met.
type TextExplainerSection_ExpandableControlValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TextExplainerSection_ExpandableControlValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TextExplainerSection_ExpandableControlValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TextExplainerSection_ExpandableControlValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TextExplainerSection_ExpandableControlValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TextExplainerSection_ExpandableControlValidationError) ErrorName() string {
	return "TextExplainerSection_ExpandableControlValidationError"
}

// Error satisfies the builtin error interface
func (e TextExplainerSection_ExpandableControlValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTextExplainerSection_ExpandableControl.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TextExplainerSection_ExpandableControlValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TextExplainerSection_ExpandableControlValidationError{}
