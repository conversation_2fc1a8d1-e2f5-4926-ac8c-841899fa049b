package secrets

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"

	"github.com/samber/lo"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/be-common/pkg/colors"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	investementUiPb "github.com/epifi/gamma/api/frontend/investment/ui"
	secretsPb "github.com/epifi/gamma/api/insights/secrets/config"
	"github.com/epifi/gamma/api/typesv2/ui"
	typesUiSecretsPb "github.com/epifi/gamma/api/typesv2/ui/insights/secrets"
)

type LineItemCardWithAnalyserFilterBuilder struct {
	config    *secretsPb.LineItemsConfig
	lineItems *LineItemCardWithAnalyserFilter
}

func NewLineItemCardWithAnalyserFilter(config *secretsPb.LineItemsConfig) *LineItemCardWithAnalyserFilterBuilder {
	var leftHeading, rightHeading *commontypes.Text
	if config.GetLeftAlignedHeadingConfig() != nil && config.GetLeftAlignedHeadingConfig().GetText() != "" {
		leftHeading = commontypes.GetTextFromStringFontColourFontStyle(config.GetLeftAlignedHeadingConfig().GetText(), config.GetLeftAlignedHeadingConfig().GetColor(), config.GetLeftAlignedHeadingConfig().GetFontStyle())
	}
	if config.GetRightAlignedHeadingConfig() != nil && config.GetRightAlignedHeadingConfig().GetText() != "" {
		rightHeading = commontypes.GetTextFromStringFontColourFontStyle(config.GetRightAlignedHeadingConfig().GetText(), config.GetRightAlignedHeadingConfig().GetColor(), config.GetRightAlignedHeadingConfig().GetFontStyle())
	}
	return &LineItemCardWithAnalyserFilterBuilder{
		config: config,
		lineItems: &LineItemCardWithAnalyserFilter{
			FilterSection: &LineItemCardWithAnalyserFilter_FilterSection{
				BgColor: "#EFF2F6",
			},
			LeftAlignedHeading:  leftHeading,
			RightAlignedHeading: rightHeading,
		},
	}
}

func (l *LineItemCardWithAnalyserFilterBuilder) AddLeftHeading(leftHeading *commontypes.Text) *LineItemCardWithAnalyserFilterBuilder {
	l.lineItems.LeftAlignedHeading = leftHeading
	return l
}

func (l *LineItemCardWithAnalyserFilterBuilder) AddRightHeading(rightHeading *commontypes.Text) *LineItemCardWithAnalyserFilterBuilder {
	l.lineItems.RightAlignedHeading = rightHeading
	return l
}

func (l *LineItemCardWithAnalyserFilterBuilder) AddLineItem(lineItem *investementUiPb.LineItem) *LineItemCardWithAnalyserFilterBuilder {
	l.lineItems.LineItems = append(l.lineItems.GetLineItems(), lineItem)
	return l
}

func (l *LineItemCardWithAnalyserFilterBuilder) AddSeeMoreButton() *LineItemCardWithAnalyserFilterBuilder {
	l.lineItems.ItemsLimitOnLanding = 10
	if len(l.lineItems.GetLineItems()) <= int(l.lineItems.GetItemsLimitOnLanding()) {
		return l
	}
	l.lineItems.SeeMore = ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("SEE MORE", colors.ColorLightPrimaryAction, commontypes.FontStyle_BUTTON_S)).WithRightImageUrlHeightAndWidth("https://epifi-icons.pointz.in/secrets/add_more_cta.png", 9, 6).WithRightImagePadding(10).WithContainerPadding(8, 16, 8, 16).WithContainer(32, 0, 0, "")
	l.lineItems.ItemsLimitOnSeeMore = 10
	return l
}
func (l *LineItemCardWithAnalyserFilterBuilder) Build() *LineItemCardWithAnalyserFilter {
	return l.lineItems
}

type LineItemBuilder struct {
	config   *secretsPb.LineItemConfig
	lineItem *investementUiPb.LineItem
}

func NewLineItemBuilder(config *secretsPb.LineItemConfig) *LineItemBuilder {
	return &LineItemBuilder{
		config:   config,
		lineItem: &investementUiPb.LineItem{},
	}
}

func (l *LineItemBuilder) SetLeftIcon(url string) *LineItemBuilder {
	if url == "" {
		return l
	}
	height := int32(44)
	width := int32(44)
	if l.config.GetLeftImageConfig() != nil {
		height = l.config.GetLeftImageConfig().GetHeight()
		width = l.config.GetLeftImageConfig().GetWidth()
	}
	l.lineItem.LeftIcon = commontypes.GetVisualElementFromUrlHeightAndWidth(url, height, width)
	return l
}

func (l *LineItemBuilder) SetSolidColorForLeftIcon(color string) *LineItemBuilder {
	if color == "" {
		return l
	}
	// TODO (Aniket): Move this to be-common as a helper function
	l.lineItem.LeftIcon = &commontypes.VisualElement{
		Asset: &commontypes.VisualElement_ColorComponent_{
			ColorComponent: &commontypes.VisualElement_ColorComponent{
				Color: color,
				Properties: &commontypes.VisualElementProperties{
					Height:       20,
					Width:        20,
					CornerRadius: 8,
				},
			},
		},
	}
	return l
}

func (l *LineItemBuilder) SetLeftHeading(text string) *LineItemBuilder {
	defaultTextConfig := &secretsPb.TextConfiguration{
		Color:     colors.ColorNight,
		FontStyle: commontypes.FontStyle_SUBTITLE_M,
	}
	l.lineItem.LeftHeading = typesUiSecretsPb.BuildItcWithConfig(l.config.GetLeftHeadingConfig(), defaultTextConfig, text)
	return l
}

func (l *LineItemBuilder) SetCustomLeftHeading(heading *typesUiPb.IconTextComponent) *LineItemBuilder {
	l.lineItem.LeftHeading = heading
	return l
}

func (l *LineItemBuilder) SetRightHeading(text string) *LineItemBuilder {
	defaultTextConfig := &secretsPb.TextConfiguration{
		Color:     colors.ColorOnLightHighEmphasis,
		FontStyle: commontypes.FontStyle_SUBTITLE_M,
	}
	l.lineItem.RightHeading = typesUiSecretsPb.BuildItcWithConfig(l.config.GetRightHeadingConfig(), defaultTextConfig, text)
	return l
}

func (l *LineItemBuilder) AddLeftTag(text string) *LineItemBuilder {
	if text == "" {
		return l
	}
	defaultTextConfig := &secretsPb.TextConfiguration{
		Color:     colors.ColorSlate,
		FontStyle: commontypes.FontStyle_SUBTITLE_XS,
	}
	if l.lineItem.GetLeftTags() == nil {
		l.lineItem.LeftTags = []*investementUiPb.Tags{{}}
	}
	// :(
	l.lineItem.GetLeftTags()[0].Tags = append(l.lineItem.GetLeftTags()[0].GetTags(), typesUiSecretsPb.BuildItcWithConfig(l.config.GetLeftTagsConfig(), defaultTextConfig, text))

	return l
}

func (l *LineItemBuilder) AddRightTag(text string) *LineItemBuilder {
	if text == "" {
		return l
	}
	defaultTextConfig := &secretsPb.TextConfiguration{
		Color:     colors.ColorSlate,
		FontStyle: commontypes.FontStyle_SUBTITLE_XS,
	}
	if l.lineItem.GetRightTags() == nil {
		l.lineItem.RightTags = []*investementUiPb.Tags{{}}
	}
	l.lineItem.GetRightTags()[0].Tags = append(l.lineItem.GetRightTags()[0].GetTags(), typesUiSecretsPb.BuildItcWithConfig(l.config.GetRightTagsConfig(), defaultTextConfig, text))

	return l
}

func (l *LineItemBuilder) SetDeeplink(dl *deeplinkPb.Deeplink) *LineItemBuilder {
	l.lineItem.Deeplink = dl
	return l
}

func (l *LineItemBuilder) Build() *investementUiPb.LineItem {
	return l.lineItem
}

type Component interface {
	*SecretsAnalyserCard | *LineItemCardWithAnalyserFilter | *TextExplainerSection | *LearnOnTheGoSection | *ActionableBanner | *ActionableBannerV2 | *SecretCollectionComponent
}

func NewSecretAnalyserComponent[T Component](component T, config *secretsPb.SecretAnalyserUIConfig) *SecretAnalyserComponent {
	secretAnalyserComponent := &SecretAnalyserComponent{ComponentName: config.GetComponentName()}
	switch any(component).(type) {
	case *SecretsAnalyserCard:
		secretAnalyserComponent.Component = &SecretAnalyserComponent_AnalyserCard{AnalyserCard: any(component).(*SecretsAnalyserCard)}
	case *LineItemCardWithAnalyserFilter:
		secretAnalyserComponent.Component = &SecretAnalyserComponent_LineItemGroup{LineItemGroup: any(component).(*LineItemCardWithAnalyserFilter)}
	case *TextExplainerSection:
		secretAnalyserComponent.Component = &SecretAnalyserComponent_TextExplainerSection{TextExplainerSection: any(component).(*TextExplainerSection)}
	case *LearnOnTheGoSection:
		secretAnalyserComponent.Component = &SecretAnalyserComponent_LearnOnTheGoSection{LearnOnTheGoSection: any(component).(*LearnOnTheGoSection)}
	case *ActionableBanner:
		secretAnalyserComponent.Component = &SecretAnalyserComponent_ActionableBanner{ActionableBanner: any(component).(*ActionableBanner)}
	case *ActionableBannerV2:
		secretAnalyserComponent.Component = &SecretAnalyserComponent_ActionableBannerV2{ActionableBannerV2: any(component).(*ActionableBannerV2)}
	case *SecretCollectionComponent:
		secretAnalyserComponent.Component = &SecretAnalyserComponent_SecretCollectionComponent{SecretCollectionComponent: any(component).(*SecretCollectionComponent)}
	}
	return secretAnalyserComponent
}

func (s *SecretsAnalyserCard) SetDefaultProTip(text string, deeplink *deeplinkPb.Deeplink) {
	proTip := typesUiSecretsPb.BuildItcWithConfig(nil, &secretsPb.TextConfiguration{
		Color:     colors.ColorOnLightHighEmphasis,
		FontStyle: commontypes.FontStyle_SUBTITLE_S,
	}, text).
		WithContainerBackgroundColor(colors.ColorSnow).
		WithRightImageUrlHeightAndWidth("https://epifi-icons.pointz.in/insights/secrets/right_green_white_chevron.png", 28, 28).
		WithRightImagePadding(12).
		WithLeftImageUrlHeightAndWidth("https://epifi-icons.pointz.in/insights/secrets/bulb.png", 32, 32).
		WithLeftImagePadding(8).
		WithContainerPadding(12, 16, 16, 16)
	if deeplink != nil {
		proTip.Deeplink = deeplink
	}
	s.ProTip = proTip
}

type LearnOnTheGoSectionBuilder struct {
	config              *secretsPb.LearnOnTheGoSectionConfig
	learnOnTheGoSection *LearnOnTheGoSection
}

func NewLearnOnTheGoSectionBuilder(config *secretsPb.LearnOnTheGoSectionConfig) *LearnOnTheGoSectionBuilder {
	return &LearnOnTheGoSectionBuilder{
		config:              config,
		learnOnTheGoSection: &LearnOnTheGoSection{},
	}
}

func (f *LearnOnTheGoSectionBuilder) Build() *SecretAnalyserComponent {
	a := NewLearnOnTheGoItemListBuilder(f.config.GetSectionItemsConfig()).BuildLearnOnTheGoSectionItemsFromConfig()
	titleCfg := f.config.GetTitleConfig()
	textColor := titleCfg.GetColor()
	if textColor == "" {
		textColor = colors.ColorDarkLayer2
	}
	fontStyle := titleCfg.GetFontStyle()
	if fontStyle == commontypes.FontStyle_FONT_STYLE_UNSPECIFIED {
		fontStyle = commontypes.FontStyle_HEADLINE_M
	}
	return &SecretAnalyserComponent{
		Component: &SecretAnalyserComponent_LearnOnTheGoSection{
			LearnOnTheGoSection: &LearnOnTheGoSection{
				Title:        commontypes.GetTextFromStringFontColourFontStyle(titleCfg.GetText(), textColor, fontStyle),
				SectionItems: a,
			},
		},
	}
}

type LearnOnTheGoSectionItemListBuilder struct {
	config  []*secretsPb.LearnOnTheGoSectionItemConfig
	section []*LearnOnTheGoSectionItem
}

func NewLearnOnTheGoItemListBuilder(config []*secretsPb.LearnOnTheGoSectionItemConfig) *LearnOnTheGoSectionItemListBuilder {
	return &LearnOnTheGoSectionItemListBuilder{
		config:  config,
		section: []*LearnOnTheGoSectionItem{},
	}
}

func (f *LearnOnTheGoSectionItemListBuilder) BuildLearnOnTheGoSectionItemsFromConfig() []*LearnOnTheGoSectionItem {
	sectionItems := make([]*LearnOnTheGoSectionItem, 0)
	for _, itemConfig := range f.config {
		sectionItems = append(sectionItems, NewLearnOnTheGoItemBuilder(itemConfig).Build())
	}
	return sectionItems
}

type LearnOnTheGoSectionItemBuilder struct {
	config *secretsPb.LearnOnTheGoSectionItemConfig
	item   *LearnOnTheGoSectionItem
}

func NewLearnOnTheGoItemBuilder(config *secretsPb.LearnOnTheGoSectionItemConfig) *LearnOnTheGoSectionItemBuilder {
	builder := &LearnOnTheGoSectionItemBuilder{
		config: config,
		item: &LearnOnTheGoSectionItem{
			OverlayColour: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{
					BlockColour: config.GetOverlayColor(),
				},
			},
			BgColor: colors.ColorOnDarkHighEmphasis,
		},
	}
	infoTagCfg := config.GetInfoTagConfig()
	if len(infoTagCfg.GetTextConfigs()) > 0 && config.GetInfoTagConfig().GetTextConfigs()[0].GetText() != "" {
		builder.SetInfoTag(infoTagCfg.GetTextConfigs()[0].GetText())
	}
	if config.GetDescriptionConfig().GetText() != "" {
		builder.SetDescription(config.GetDescriptionConfig().GetText())
	}
	if config.GetImageConfig().GetImageUrl() != "" {
		builder.SetIcon(config.GetImageConfig().GetImageUrl())
	}
	redirectionCfg := config.GetRedirectionConfig()
	if redirectionCfg.GetDeeplink() != nil {
		builder.SetDeeplink(redirectionCfg.GetDeeplink())
	}
	if redirectionCfg.GetStorifymeUrl() != "" {
		builder.SetDeeplink(&deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_STORY_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_StoryScreenOptions{
				StoryScreenOptions: &deeplinkPb.StoryScreenOptions{
					StoryUrl: redirectionCfg.GetStorifymeUrl(),
				},
			},
		})
	}
	return builder
}

func (f *LearnOnTheGoSectionItemBuilder) Build() *LearnOnTheGoSectionItem {
	return f.item
}

func (f *LearnOnTheGoSectionItemBuilder) SetDescription(text string) *LearnOnTheGoSectionItemBuilder {
	fontColor := f.config.GetDescriptionConfig().GetColor()
	if fontColor == "" {
		fontColor = colors.ColorOnDarkLowEmphasis
	}
	fontStyle := f.config.GetDescriptionConfig().GetFontStyle()
	if fontStyle == commontypes.FontStyle_FONT_STYLE_UNSPECIFIED {
		fontStyle = commontypes.FontStyle_SUBTITLE_XS
	}
	f.item.Description = commontypes.GetTextFromStringFontColourFontStyle(text, fontColor, fontStyle)
	return f
}

func (f *LearnOnTheGoSectionItemBuilder) SetIcon(url string) *LearnOnTheGoSectionItemBuilder {
	f.item.Icon = commontypes.GetVisualElementFromUrlHeightAndWidth(url, f.config.GetImageConfig().GetHeight(), f.config.GetImageConfig().GetWidth())
	return f
}

func (f *LearnOnTheGoSectionItemBuilder) SetInfoTag(text string) *LearnOnTheGoSectionItemBuilder {
	f.item.InfoTag = typesUiSecretsPb.BuildItcWithConfig(f.config.GetInfoTagConfig(), nil, text).WithContainerProperties(&ui.IconTextComponent_ContainerProperties{
		BgColor:       colors.ColorOnDarkHighEmphasis,
		CornerRadius:  20,
		TopPadding:    2,
		BottomPadding: 2,
		LeftPadding:   4,
		RightPadding:  4,
	})
	return f
}

func (f *LearnOnTheGoSectionItemBuilder) SetDeeplink(dl *deeplinkPb.Deeplink) *LearnOnTheGoSectionItemBuilder {
	f.item.Deeplink = dl
	return f
}

type TextExplainerSectionBuilder struct {
	config               *secretsPb.TextExplainerSectionConfig
	textExplainerSection *TextExplainerSection
}

func NewTextExplainerSectionBuilder(config *secretsPb.TextExplainerSectionConfig) *TextExplainerSectionBuilder {

	builder := &TextExplainerSectionBuilder{
		config: config,
		textExplainerSection: &TextExplainerSection{
			BgColor:      config.GetBgColor(),
			CornerRadius: 16,
			BorderColor:  config.GetBorderColor(),
		},
	}

	if len(config.GetTitle().GetTextConfigs()) > 0 && config.GetTitle().GetTextConfigs()[0].GetText() != "" {
		builder.SetTitle(getTextStringFromListOfTextsConfiguration(config.GetTitle().GetTextConfigs())...)
	}

	descriptions := config.GetDescriptions()
	if len(descriptions) > 0 && descriptions[0].GetText() != "" {
		builder.SetDescriptions(getTextStringFromListOfTextsConfiguration(descriptions)...)
	}
	if config.GetExpandableControlConfig() != nil {
		builder.SetExpandableControl(config.GetExpandableControlConfig().GetIsExpanded())
		builder.SetExpandableControlToggleImage(config.GetExpandableControlConfig().GetToggleImageConfig().GetImageUrl())
	}
	for _, ctaCfg := range config.GetCtaConfigs() {
		if len(ctaCfg.GetTextConfigs()) > 0 && ctaCfg.GetTextConfigs()[0].GetText() != "" {
			cta := typesUiSecretsPb.BuildItcWithConfig(ctaCfg, nil, ctaCfg.GetTextConfigs()[0].GetText()).
				WithContainerCornerRadius(20).
				WithContainerPaddingSymmetrical(12, 8).
				WithContainerBackgroundColor(ctaCfg.GetContainerProperties().GetBgColor())
			builder.SetCta(cta)
		}
	}
	return builder
}

func (t *TextExplainerSectionBuilder) SetTitle(text ...string) {
	defaultTitleTextConfig := &secretsPb.TextConfiguration{
		Color:     "#6BCDB6",
		FontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS,
	}

	t.textExplainerSection.Title = typesUiSecretsPb.BuildItcWithConfig(t.config.GetTitle(), defaultTitleTextConfig, text...)
}

func (t *TextExplainerSectionBuilder) SetDescriptions(texts ...string) *TextExplainerSectionBuilder {
	defaultDescriptionTextConfig := &secretsPb.TextConfiguration{
		Color:     "#004E2D",
		FontStyle: commontypes.FontStyle_SUBTITLE_S,
	}

	descriptions := typesUiSecretsPb.BuildHtmlTextWithConfig(defaultDescriptionTextConfig, t.config.GetDescriptions(), texts...)
	t.textExplainerSection.Description = lo.Map(descriptions, func(text *commontypes.Text, _ int) *commontypes.Text {
		return text.WithAlignment(commontypes.Text_ALIGNMENT_LEFT)
	})

	return t
}

func (t *TextExplainerSectionBuilder) initExpandableControl() {
	if t.textExplainerSection.GetCardExpandableControl() == nil {
		t.textExplainerSection.CardExpandableControl = &TextExplainerSection_ExpandableControl{}
	}
}

func (t *TextExplainerSectionBuilder) SetExpandableControl(isExpanded bool) *TextExplainerSectionBuilder {
	t.initExpandableControl()
	t.textExplainerSection.CardExpandableControl.IsExpanded = isExpanded
	return t
}

func (t *TextExplainerSectionBuilder) SetExpandableControlToggleImage(url string) *TextExplainerSectionBuilder {
	t.initExpandableControl()
	imageCfg := t.config.GetExpandableControlConfig().GetToggleImageConfig()
	t.textExplainerSection.GetCardExpandableControl().ChevronImage = commontypes.GetImageFromUrl(url).
		WithHeight(imageCfg.GetHeight()).
		WithWidth(imageCfg.GetWidth())
	return t
}

func (t *TextExplainerSectionBuilder) SetCta(cta *typesUiPb.IconTextComponent) *TextExplainerSectionBuilder {
	t.textExplainerSection.Ctas = append(t.textExplainerSection.Ctas, cta)
	return t
}

func (t *TextExplainerSectionBuilder) Build() *TextExplainerSection {
	return t.textExplainerSection
}

func getTextStringFromListOfTextsConfiguration(textConfiguration []*secretsPb.TextConfiguration) []string {
	titleStrs := make([]string, 0)
	for _, text := range textConfiguration {
		titleStrs = append(titleStrs, text.GetText())
	}
	return titleStrs
}

type ActionableBannerBuilder struct {
	config *secretsPb.ActionableBannerConfig
	banner *ActionableBanner
}

func NewActionableBannerBuilder(config *secretsPb.ActionableBannerConfig) *ActionableBannerBuilder {
	builder := &ActionableBannerBuilder{
		config: config,
		banner: &ActionableBanner{
			CornerRadius: 16,
			BgColor:      config.GetBgColor(),
		},
	}
	if config.GetLeftImageConfig() != nil {
		builder.SetLeftImage(config.GetLeftImageConfig().GetImageUrl())
	}
	if config.GetDescriptionConfig().GetText() != "" {
		builder.SetDescription(config.GetDescriptionConfig().GetText())
	}
	if config.GetDeeplink() != nil {
		builder.SetDeeplink(config.GetDeeplink())
	}
	if config.GetRightComponentConfig() != nil {
		texts := config.GetRightComponentConfig().GetTextConfigs()
		if len(texts) > 0 {
			builder.SetRightComponent(getTextStringFromListOfTextsConfiguration(texts)...)
		} else {
			builder.SetRightComponent("")
		}
	}
	if config.GetBorderProperty() != nil {
		builder.SetBorderProperty(config.GetBorderProperty())
	}
	return builder
}

type ActionableBannerV2Builder struct {
	config *secretsPb.ActionableBannerV2Config
	banner *ActionableBannerV2
}

func NewActionableBannerV2Builder(config *secretsPb.ActionableBannerV2Config) *ActionableBannerV2Builder {
	builder := &ActionableBannerV2Builder{
		config: config,
		banner: &ActionableBannerV2{
			CornerRadius: 21,
			BgColor:      config.GetBgColor(),
		},
	}
	if config.GetRightImageConfig() != nil {
		builder.RightImage(config.GetRightImageConfig().GetImageUrl())
	}
	if config.GetTitleConfig().GetText() != "" {
		builder.SetDescription(config.GetTitleConfig().GetText())
	}
	if config.GetDeeplink() != nil {
		builder.SetDeeplink(config.GetDeeplink())
	}
	if config.GetTitleConfig() != nil {
		builder.SetTitle(config.GetTitleConfig().GetText())
	}
	if config.GetCtaComponentConfig() != nil {
		builder.SetCTAComponent()
	}
	return builder
}

func (a *ActionableBannerV2Builder) SetText(text string) *ActionableBannerV2Builder {
	textConfig := &secretsPb.TextConfiguration{
		Color:     colors.ColorOnLightHighEmphasis,
		FontStyle: commontypes.FontStyle_SUBTITLE_M,
	}
	if a.config.GetTitleConfig() != nil {
		textConfig = a.config.GetTitleConfig()
	}
	a.banner.Text = commontypes.GetTextFromStringFontColourFontStyle(text, textConfig.GetColor(), textConfig.GetFontStyle()).
		WithAlignment(commontypes.Text_ALIGNMENT_LEFT)
	return a
}

func (a *ActionableBannerV2Builder) RightImage(url string) *ActionableBannerV2Builder {
	imageConfig := a.config.GetRightImageConfig()
	if imageConfig != nil {
		a.banner.Image = &commontypes.Image{
			ImageUrl: url,
			Width:    imageConfig.GetWidth(),
			Height:   imageConfig.GetHeight(),
		}
	} else {
		a.banner.Image = &commontypes.Image{
			ImageUrl: url,
		}
	}
	return a
}

func (a *ActionableBannerV2Builder) SetDeeplink(dl *deeplinkPb.Deeplink) *ActionableBannerV2Builder {
	a.banner.Deeplink = dl
	return a
}

func (a *ActionableBannerV2Builder) SetTitle(text string) *ActionableBannerV2Builder {
	return a.SetText(text)
}

func (a *ActionableBannerV2Builder) SetDescription(text string) *ActionableBannerV2Builder {
	return a.SetText(text)
}

func (a *ActionableBannerV2Builder) SetCTAComponent() *ActionableBannerV2Builder {
	textConfig := &secretsPb.TextConfiguration{
		Color:     colors.ColorLightPrimaryAction,
		FontStyle: commontypes.FontStyle_BUTTON_S,
	}

	ctaConfig := a.config.GetCtaComponentConfig()
	if ctaConfig != nil {
		texts := getTextStringFromListOfTextsConfiguration(ctaConfig.GetTextConfigs())
		a.banner.Cta = typesUiSecretsPb.BuildItcWithConfig(ctaConfig, textConfig, texts...).
			WithContainerCornerRadius(20).
			WithContainerPaddingSymmetrical(16, 8)
	}
	return a
}

func (a *ActionableBannerV2Builder) SetEventProperties(properties map[string]string) *ActionableBannerV2Builder {
	a.banner.EventProperties = properties
	return a
}

func (a *ActionableBannerV2Builder) Build() *ActionableBannerV2 {
	return a.banner
}

func (a *ActionableBannerBuilder) SetLeftImage(url string) *ActionableBannerBuilder {
	a.banner.LeftImage = commontypes.GetVisualElementFromUrlHeightAndWidth(url, a.config.GetLeftImageConfig().GetHeight(), a.config.GetLeftImageConfig().GetWidth())
	return a
}

func (a *ActionableBannerBuilder) SetDescription(text string) *ActionableBannerBuilder {
	textConfig := &secretsPb.TextConfiguration{
		Color:     colors.ColorOnLightHighEmphasis,
		FontStyle: commontypes.FontStyle_SUBTITLE_M,
	}
	if a.config.GetDescriptionConfig() != nil {
		textConfig = a.config.GetDescriptionConfig()
	}
	a.banner.Description = commontypes.GetTextFromStringFontColourFontStyle(text, textConfig.GetColor(), textConfig.GetFontStyle()).
		WithAlignment(commontypes.Text_ALIGNMENT_LEFT)
	return a
}

func (a *ActionableBannerBuilder) SetRightComponent(text ...string) *ActionableBannerBuilder {
	textConfig := &secretsPb.TextConfiguration{
		Color:     colors.ColorLightPrimaryAction,
		FontStyle: commontypes.FontStyle_BUTTON_S,
	}
	a.banner.RightComponent = typesUiSecretsPb.BuildItcWithConfig(a.config.GetRightComponentConfig(), textConfig, text...).
		WithContainerCornerRadius(20).
		WithContainerPaddingSymmetrical(16, 8)
	return a
}
func (a *ActionableBannerBuilder) SetRightComponentRightImage(url string, width int32, height int32) *ActionableBannerBuilder {
	if a.banner.GetRightComponent() == nil {
		a.SetRightComponent("")
	}
	a.banner.RightComponent.RightIcon = &common.Image{
		ImageUrl: url,
		Width:    width,
		Height:   height,
	}
	return a
}
func (a *ActionableBannerBuilder) SetDeeplink(dl *deeplinkPb.Deeplink) *ActionableBannerBuilder {
	a.banner.Deeplink = dl
	return a
}

func (a *ActionableBannerBuilder) SetBorderProperty(property *ui.BorderProperty) *ActionableBannerBuilder {
	a.banner.BorderProperty = property
	return a
}

func (a *ActionableBannerBuilder) Build() *ActionableBanner {
	return a.banner
}

type GridVisualisationCardBuilder struct {
	config                *secretsPb.GridVisualisationCardConfig
	gridVisualisationCard *typesUiSecretsPb.GridVisualisationCard
}

func NewGridVisualisationCardBuilder(config *secretsPb.GridVisualisationCardConfig) *GridVisualisationCardBuilder {
	builder := &GridVisualisationCardBuilder{
		config: config,
		gridVisualisationCard: &typesUiSecretsPb.GridVisualisationCard{
			TitleValuePair: &typesUiPb.VerticalKeyValuePair{
				VerticalPaddingBtwTitleValue: 8,
			},
			GridComponent: &typesUiSecretsPb.GridComponent{
				GridCard: &typesUiPb.GridCard{
					ColumnCount: config.GetGridComponentConfig().GetGridCardConfig().GetColumnCount(),
					SelectedBorderProperties: &typesUiPb.BorderProperty{
						BorderThickness: 2,
						BorderColor:     "#00B899",
						BorderType:      typesUiPb.BorderStyle_SOLID_LINE,
					},
				},
				CornerRadius: 24,
			},
		},
	}
	gridComponentConfig := config.GetGridComponentConfig()
	if gridComponentConfig.GetTitleConfig().GetText() != "" {
		builder.SetGridComponentTitle(config.GetGridComponentConfig().GetTitleConfig().GetText())
	}
	if len(config.GetTitleValueConfig().GetTitleConfig().GetTextConfigs()) > 0 {
		texts := make([]string, 0)
		for _, textVal := range config.GetTitleValueConfig().GetTitleConfig().GetTextConfigs() {
			texts = append(texts, textVal.GetText())
		}
		builder = builder.SetGridVisualisationCardTitle(texts...)
	}
	if gridComponentConfig.GetBgColor() != "" {
		builder = builder.SetGridComponentBackgroundColor(gridComponentConfig.GetBgColor())
	}

	return builder
}

func (g *GridVisualisationCardBuilder) initGridVisualisationCardTitleValuePair() {
	if g.gridVisualisationCard.GetTitleValuePair() == nil {
		g.gridVisualisationCard.TitleValuePair = &typesUiPb.VerticalKeyValuePair{}
	}
}

func (g *GridVisualisationCardBuilder) SetGridVisualisationCardTitle(texts ...string) *GridVisualisationCardBuilder {
	g.initGridVisualisationCardTitleValuePair()
	defaultTextCfg := &secretsPb.TextConfiguration{
		Color:     colors.ColorMoss700,
		FontStyle: commontypes.FontStyle_SUBTITLE_L,
	}
	g.gridVisualisationCard.TitleValuePair.Title = typesUiSecretsPb.BuildItcWithConfig(g.config.GetTitleValueConfig().GetTitleConfig(), defaultTextCfg, texts...)
	return g
}

func (g *GridVisualisationCardBuilder) SetGridVisualisationCardValue(value string) *GridVisualisationCardBuilder {
	g.initGridVisualisationCardTitleValuePair()
	defaultTextCfg := &secretsPb.TextConfiguration{
		Color:     colors.ColorDarkBase,
		FontStyle: commontypes.FontStyle_SUBTITLE_XL,
	}
	g.gridVisualisationCard.TitleValuePair.Value = typesUiSecretsPb.BuildItcWithConfig(g.config.GetTitleValueConfig().GetValueConfig(), defaultTextCfg, value)
	return g
}

func (g *GridVisualisationCardBuilder) SetGridComponentTitle(title string) *GridVisualisationCardBuilder {
	gridComponentTitleCfg := g.config.GetGridComponentConfig().GetTitleConfig()
	g.gridVisualisationCard.GetGridComponent().Title = commontypes.GetTextFromStringFontColourFontStyle(title, gridComponentTitleCfg.GetColor(), gridComponentTitleCfg.GetFontStyle())
	return g
}

func (g *GridVisualisationCardBuilder) SetGridComponentBackgroundColor(color string) *GridVisualisationCardBuilder {
	g.gridVisualisationCard.GetGridComponent().BgColor = &widget.BackgroundColour{
		Colour: &widget.BackgroundColour_BlockColour{BlockColour: g.config.GetGridComponentConfig().GetBgColor()},
	}
	return g
}

func (g *GridVisualisationCardBuilder) SetGridCardSelectionProperty(selectedIdx int32) *GridVisualisationCardBuilder {
	g.gridVisualisationCard.GetGridComponent().GetGridCard().SelectionIndex = selectedIdx
	g.gridVisualisationCard.GetGridComponent().GetGridCard().SelectedBorderProperties = &typesUiPb.BorderProperty{
		BorderThickness: 2,
		BorderColor:     colors.ColorLightPrimaryAction,
		BorderType:      typesUiPb.BorderStyle_SOLID_LINE,
	}
	return g
}

func (g *GridVisualisationCardBuilder) AddGridCardTile(tile *typesUiPb.GridTile) *GridVisualisationCardBuilder {
	g.gridVisualisationCard.GetGridComponent().GetGridCard().Tiles = append(g.gridVisualisationCard.GetGridComponent().GetGridCard().Tiles, tile)
	return g
}

func (g *GridVisualisationCardBuilder) Build() *typesUiSecretsPb.GridVisualisationCard {
	return g.gridVisualisationCard
}

type GridTileBuilder struct {
	config   *secretsPb.GridTileConfig
	gridTile *typesUiPb.GridTile
}

func NewGridTileBuilder(config *secretsPb.GridTileConfig) *GridTileBuilder {
	return &GridTileBuilder{
		config:   config,
		gridTile: &typesUiPb.GridTile{CornerRadius: 8},
	}
}

func (g *GridTileBuilder) SetTitle(title string) *GridTileBuilder {
	defaultTextCfg := &secretsPb.TextConfiguration{
		Color:     colors.ColorOnDarkMediumEmphasis,
		FontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
	}
	g.gridTile.Title = typesUiSecretsPb.BuildItcWithConfig(g.config.GetTitleConfig(), defaultTextCfg, title)
	return g
}

func (g *GridTileBuilder) SetValue(value string) *GridTileBuilder {
	defaultTextCfg := &secretsPb.TextConfiguration{
		Color:     colors.ColorOnLightHighEmphasis,
		FontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS,
	}
	g.gridTile.Value = typesUiSecretsPb.BuildItcWithConfig(g.config.GetValueConfig(), defaultTextCfg, value)
	return g
}

func (g *GridTileBuilder) SetIcon(url string) *GridTileBuilder {
	g.gridTile.Icon = commontypes.GetVisualElementFromUrlHeightAndWidth(url, g.config.GetIconConfig().GetHeight(), g.config.GetIconConfig().GetWidth())
	return g
}

func (g *GridTileBuilder) SetBackgroundColor(color string) *GridTileBuilder {
	g.gridTile.BgColor = &widget.BackgroundColour{
		Colour: &widget.BackgroundColour_BlockColour{BlockColour: color},
	}
	return g
}

func (g *GridTileBuilder) SetTitleColor(color string) *GridTileBuilder {
	for _, text := range g.gridTile.GetTitle().GetTexts() {
		text.BgColor = color
	}
	return g
}

func (g *GridTileBuilder) SetValueColor(color string) *GridTileBuilder {
	for _, text := range g.gridTile.GetValue().GetTexts() {
		text.BgColor = color
	}
	return g
}

func (g *GridTileBuilder) SetBorderProperties(borderThickness int32, borderColor string, borderType typesUiPb.BorderStyle) *GridTileBuilder {
	g.gridTile.BorderProperties = &typesUiPb.BorderProperty{
		BorderThickness: borderThickness,
		BorderColor:     borderColor,
		BorderType:      borderType,
	}
	return g
}

func (g *GridTileBuilder) SetBottomCardComponent(bottomCard *typesUiPb.GridComponentBottomCard) *GridTileBuilder {
	g.gridTile.Action = &typesUiPb.GridTile_GridTileTapAction{
		Action: &typesUiPb.GridTile_GridTileTapAction_BottomCard{
			BottomCard: bottomCard,
		},
	}
	return g
}

func (g *GridTileBuilder) Build() *typesUiPb.GridTile {
	return g.gridTile
}

type GridComponentBottomCardBuilder struct {
	config     *secretsPb.GridComponentBottomCardConfig
	bottomCard *typesUiPb.GridComponentBottomCard
}

func NewGridComponentBottomCardBuilder(config *secretsPb.GridComponentBottomCardConfig) *GridComponentBottomCardBuilder {
	return &GridComponentBottomCardBuilder{
		config: config,
		bottomCard: &typesUiPb.GridComponentBottomCard{
			CornerRadius: 8,
			BgColor:      &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#F6F9FD"}},
		},
	}
}

func (g *GridComponentBottomCardBuilder) SetTitle(title string) *GridComponentBottomCardBuilder {
	defaultTextCfg := &secretsPb.TextConfiguration{
		Color:     colors.ColorOnDarkMediumEmphasis,
		FontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
	}
	g.bottomCard.Title = typesUiSecretsPb.BuildItcWithConfig(g.config.GetTitleConfig(), defaultTextCfg, title)
	return g
}

func (g *GridComponentBottomCardBuilder) AddBottomCardKeyValue(iconUrl, key, value string) *GridComponentBottomCardBuilder {
	defaultKeyCfg := &secretsPb.TextConfiguration{
		Color:     colors.ColorMonochromeAsh,
		FontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
	}
	defaultValueCfg := &secretsPb.TextConfiguration{
		Color:     colors.ColorOnDarkLowEmphasis,
		FontStyle: commontypes.FontStyle_NUMBER_XS,
	}
	bottomCardValue := &typesUiPb.BottomCardValue{
		Key: typesUiSecretsPb.BuildItcWithConfig(g.config.GetValueConfig().GetKeyConfig(), defaultKeyCfg, key).
			WithLeftImageUrlHeightAndWidth(iconUrl, 16, 16),
		Value: typesUiSecretsPb.BuildItcWithConfig(g.config.GetValueConfig().GetKeyConfig(), defaultValueCfg, value),
	}
	g.bottomCard.Values = append(g.bottomCard.Values, bottomCardValue)
	return g
}

func (g *GridComponentBottomCardBuilder) SetBackgroundColor(color string) *GridComponentBottomCardBuilder {
	g.bottomCard.BgColor = &widget.BackgroundColour{
		Colour: &widget.BackgroundColour_BlockColour{BlockColour: color},
	}
	return g
}

func (g *GridComponentBottomCardBuilder) SetDividerProperties(color string, height int32) *GridComponentBottomCardBuilder {
	g.bottomCard.DividerProperties = &typesUiPb.DividerProperties{
		FontColor: color,
		Height:    height,
	}
	return g
}

func (g *GridComponentBottomCardBuilder) Build() *typesUiPb.GridComponentBottomCard {
	return g.bottomCard
}

type DonutCardBuilder struct {
	config                 *secretsPb.DonutCardConfig
	donutVisualisationCard *typesUiSecretsPb.DonutCard
}

func NewDonutCardBuilder(config *secretsPb.DonutCardConfig) *DonutCardBuilder {
	builder := &DonutCardBuilder{
		config: config,
		donutVisualisationCard: &typesUiSecretsPb.DonutCard{
			InfoComponent: &typesUiSecretsPb.CardInfoComponent{},
			BgColor:       config.GetBgColor(),
		},
	}
	return builder
}

func (d *DonutCardBuilder) AddTitleValuePair(value string) *DonutCardBuilder {
	defaultTitleTextConfig := &secretsPb.TextConfiguration{
		Color:     "#D48647",
		FontStyle: commontypes.FontStyle_SUBTITLE_L,
	}
	defaultValueTextConfig := &secretsPb.TextConfiguration{
		Color:     "#18191B",
		FontStyle: commontypes.FontStyle_SUBTITLE_L,
	}
	d.donutVisualisationCard.InfoComponent.TitleValuePair = &typesUiPb.VerticalKeyValuePair{
		Title:                        typesUiSecretsPb.BuildItcWithConfig(d.config.GetTitleValueConfig().GetTitleConfig(), defaultTitleTextConfig, getTextStringFromListOfTextsConfiguration(d.config.GetTitleValueConfig().GetTitleConfig().GetTextConfigs())...),
		Value:                        typesUiSecretsPb.BuildItcWithConfig(d.config.GetTitleValueConfig().GetValueConfig(), defaultValueTextConfig, value),
		VerticalPaddingBtwTitleValue: 8,
	}
	return d
}

func (d *DonutCardBuilder) AddDonutSlice(donutSlice *typesUiSecretsPb.DonutSlice) *DonutCardBuilder {
	d.donutVisualisationCard.Slices = append(d.donutVisualisationCard.Slices, donutSlice)
	return d
}

func (d *DonutCardBuilder) Build() *typesUiSecretsPb.DonutCard {
	return d.donutVisualisationCard
}

type DonutSliceBuilder struct {
	donutSliceConfig *secretsPb.DonutSliceConfig
	donutSlice       *typesUiSecretsPb.DonutSlice
}

func NewDonutSliceBuilder(config *secretsPb.DonutSliceConfig) *DonutSliceBuilder {
	builder := &DonutSliceBuilder{
		donutSliceConfig: config,
		donutSlice: &typesUiSecretsPb.DonutSlice{
			Color: config.GetDonutColor(),
		},
	}
	return builder
}

func (d *DonutSliceBuilder) AddDonutSliceValue(percent float64) *DonutSliceBuilder {
	d.donutSlice.Percent = percent
	return d
}

func (d *DonutSliceBuilder) AddDonutSliceColor(color string) *DonutSliceBuilder {
	d.donutSlice.Color = color
	return d
}

func (d *DonutSliceBuilder) AddToolTip(title string, value string) *DonutSliceBuilder {
	d.donutSlice.Tooltip = &ui.Tooltip{
		BgColor:      colors.ColorSnow,
		CornerRadius: 8,
		Infos: []*ui.IconTextComponent{
			ui.NewITC().WithTexts(commontypes.GetPlainStringText(title).WithFontStyle(commontypes.FontStyle_OVERLINE_2XS_CAPS).WithFontColor(colors.ColorOnDarkMediumEmphasis)),
			ui.NewITC().WithTexts(commontypes.GetPlainStringText(value).WithFontStyle(commontypes.FontStyle_NUMBER_XS).WithFontColor(colors.ColorOnDarkLowEmphasis)),
		},
	}
	return d
}

func (d *DonutSliceBuilder) Build() *typesUiSecretsPb.DonutSlice {
	return d.donutSlice
}
