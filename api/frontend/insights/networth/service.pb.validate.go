// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/insights/networth/service.proto

package networth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MagicImportFilesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MagicImportFilesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MagicImportFilesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MagicImportFilesRequestMultiError, or nil if none found.
func (m *MagicImportFilesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MagicImportFilesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MagicImportFilesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MagicImportFilesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MagicImportFilesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MagicImportFilesRequestValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MagicImportFilesRequestValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MagicImportFilesRequestValidationError{
					field:  fmt.Sprintf("Files[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MagicImportFilesRequestMultiError(errors)
	}

	return nil
}

// MagicImportFilesRequestMultiError is an error wrapping multiple validation
// errors returned by MagicImportFilesRequest.ValidateAll() if the designated
// constraints aren't met.
type MagicImportFilesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MagicImportFilesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MagicImportFilesRequestMultiError) AllErrors() []error { return m }

// MagicImportFilesRequestValidationError is the validation error returned by
// MagicImportFilesRequest.Validate if the designated constraints aren't met.
type MagicImportFilesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MagicImportFilesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MagicImportFilesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MagicImportFilesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MagicImportFilesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MagicImportFilesRequestValidationError) ErrorName() string {
	return "MagicImportFilesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MagicImportFilesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMagicImportFilesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MagicImportFilesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MagicImportFilesRequestValidationError{}

// Validate checks the field values on MagicImportFilesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MagicImportFilesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MagicImportFilesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MagicImportFilesResponseMultiError, or nil if none found.
func (m *MagicImportFilesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MagicImportFilesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MagicImportFilesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MagicImportFilesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MagicImportFilesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImportedAssetsListScreen()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MagicImportFilesResponseValidationError{
					field:  "ImportedAssetsListScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MagicImportFilesResponseValidationError{
					field:  "ImportedAssetsListScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImportedAssetsListScreen()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MagicImportFilesResponseValidationError{
				field:  "ImportedAssetsListScreen",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MagicImportFilesResponseMultiError(errors)
	}

	return nil
}

// MagicImportFilesResponseMultiError is an error wrapping multiple validation
// errors returned by MagicImportFilesResponse.ValidateAll() if the designated
// constraints aren't met.
type MagicImportFilesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MagicImportFilesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MagicImportFilesResponseMultiError) AllErrors() []error { return m }

// MagicImportFilesResponseValidationError is the validation error returned by
// MagicImportFilesResponse.Validate if the designated constraints aren't met.
type MagicImportFilesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MagicImportFilesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MagicImportFilesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MagicImportFilesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MagicImportFilesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MagicImportFilesResponseValidationError) ErrorName() string {
	return "MagicImportFilesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MagicImportFilesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMagicImportFilesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MagicImportFilesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MagicImportFilesResponseValidationError{}

// Validate checks the field values on DeleteManualAssetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteManualAssetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteManualAssetRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteManualAssetRequestMultiError, or nil if none found.
func (m *DeleteManualAssetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteManualAssetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteManualAssetRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteManualAssetRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteManualAssetRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetExternalId()) < 1 {
		err := DeleteManualAssetRequestValidationError{
			field:  "ExternalId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteManualAssetRequestMultiError(errors)
	}

	return nil
}

// DeleteManualAssetRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteManualAssetRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteManualAssetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteManualAssetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteManualAssetRequestMultiError) AllErrors() []error { return m }

// DeleteManualAssetRequestValidationError is the validation error returned by
// DeleteManualAssetRequest.Validate if the designated constraints aren't met.
type DeleteManualAssetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteManualAssetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteManualAssetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteManualAssetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteManualAssetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteManualAssetRequestValidationError) ErrorName() string {
	return "DeleteManualAssetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteManualAssetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteManualAssetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteManualAssetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteManualAssetRequestValidationError{}

// Validate checks the field values on DeleteManualAssetResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteManualAssetResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteManualAssetResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteManualAssetResponseMultiError, or nil if none found.
func (m *DeleteManualAssetResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteManualAssetResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteManualAssetResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteManualAssetResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteManualAssetResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteManualAssetResponseMultiError(errors)
	}

	return nil
}

// DeleteManualAssetResponseMultiError is an error wrapping multiple validation
// errors returned by DeleteManualAssetResponse.ValidateAll() if the
// designated constraints aren't met.
type DeleteManualAssetResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteManualAssetResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteManualAssetResponseMultiError) AllErrors() []error { return m }

// DeleteManualAssetResponseValidationError is the validation error returned by
// DeleteManualAssetResponse.Validate if the designated constraints aren't met.
type DeleteManualAssetResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteManualAssetResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteManualAssetResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteManualAssetResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteManualAssetResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteManualAssetResponseValidationError) ErrorName() string {
	return "DeleteManualAssetResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteManualAssetResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteManualAssetResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteManualAssetResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteManualAssetResponseValidationError{}

// Validate checks the field values on GetManualAssetDashboardRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetManualAssetDashboardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetManualAssetDashboardRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetManualAssetDashboardRequestMultiError, or nil if none found.
func (m *GetManualAssetDashboardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetManualAssetDashboardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetManualAssetDashboardRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetManualAssetDashboardRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetManualAssetDashboardRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetAssetType()) < 1 {
		err := GetManualAssetDashboardRequestValidationError{
			field:  "AssetType",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetManualAssetDashboardRequestMultiError(errors)
	}

	return nil
}

// GetManualAssetDashboardRequestMultiError is an error wrapping multiple
// validation errors returned by GetManualAssetDashboardRequest.ValidateAll()
// if the designated constraints aren't met.
type GetManualAssetDashboardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetManualAssetDashboardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetManualAssetDashboardRequestMultiError) AllErrors() []error { return m }

// GetManualAssetDashboardRequestValidationError is the validation error
// returned by GetManualAssetDashboardRequest.Validate if the designated
// constraints aren't met.
type GetManualAssetDashboardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetManualAssetDashboardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetManualAssetDashboardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetManualAssetDashboardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetManualAssetDashboardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetManualAssetDashboardRequestValidationError) ErrorName() string {
	return "GetManualAssetDashboardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetManualAssetDashboardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetManualAssetDashboardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetManualAssetDashboardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetManualAssetDashboardRequestValidationError{}

// Validate checks the field values on GetManualAssetDashboardResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetManualAssetDashboardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetManualAssetDashboardResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetManualAssetDashboardResponseMultiError, or nil if none found.
func (m *GetManualAssetDashboardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetManualAssetDashboardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetManualAssetDashboardResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetManualAssetDashboardResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetManualAssetDashboardResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDashboard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetManualAssetDashboardResponseValidationError{
					field:  "Dashboard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetManualAssetDashboardResponseValidationError{
					field:  "Dashboard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDashboard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetManualAssetDashboardResponseValidationError{
				field:  "Dashboard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetManualAssetDashboardResponseMultiError(errors)
	}

	return nil
}

// GetManualAssetDashboardResponseMultiError is an error wrapping multiple
// validation errors returned by GetManualAssetDashboardResponse.ValidateAll()
// if the designated constraints aren't met.
type GetManualAssetDashboardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetManualAssetDashboardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetManualAssetDashboardResponseMultiError) AllErrors() []error { return m }

// GetManualAssetDashboardResponseValidationError is the validation error
// returned by GetManualAssetDashboardResponse.Validate if the designated
// constraints aren't met.
type GetManualAssetDashboardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetManualAssetDashboardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetManualAssetDashboardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetManualAssetDashboardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetManualAssetDashboardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetManualAssetDashboardResponseValidationError) ErrorName() string {
	return "GetManualAssetDashboardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetManualAssetDashboardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetManualAssetDashboardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetManualAssetDashboardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetManualAssetDashboardResponseValidationError{}

// Validate checks the field values on SubmitManualFormRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitManualFormRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitManualFormRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitManualFormRequestMultiError, or nil if none found.
func (m *SubmitManualFormRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitManualFormRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitManualFormRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitManualFormRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitManualFormRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFormIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitManualFormRequestValidationError{
					field:  "FormIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitManualFormRequestValidationError{
					field:  "FormIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFormIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitManualFormRequestValidationError{
				field:  "FormIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFormData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SubmitManualFormRequestValidationError{
						field:  fmt.Sprintf("FormData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SubmitManualFormRequestValidationError{
						field:  fmt.Sprintf("FormData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SubmitManualFormRequestValidationError{
					field:  fmt.Sprintf("FormData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SubmitManualFormRequestMultiError(errors)
	}

	return nil
}

// SubmitManualFormRequestMultiError is an error wrapping multiple validation
// errors returned by SubmitManualFormRequest.ValidateAll() if the designated
// constraints aren't met.
type SubmitManualFormRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitManualFormRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitManualFormRequestMultiError) AllErrors() []error { return m }

// SubmitManualFormRequestValidationError is the validation error returned by
// SubmitManualFormRequest.Validate if the designated constraints aren't met.
type SubmitManualFormRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitManualFormRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitManualFormRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitManualFormRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitManualFormRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitManualFormRequestValidationError) ErrorName() string {
	return "SubmitManualFormRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitManualFormRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitManualFormRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitManualFormRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitManualFormRequestValidationError{}

// Validate checks the field values on SubmitManualFormResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitManualFormResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitManualFormResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitManualFormResponseMultiError, or nil if none found.
func (m *SubmitManualFormResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitManualFormResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitManualFormResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitManualFormResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitManualFormResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubmitManualFormResponseMultiError(errors)
	}

	return nil
}

// SubmitManualFormResponseMultiError is an error wrapping multiple validation
// errors returned by SubmitManualFormResponse.ValidateAll() if the designated
// constraints aren't met.
type SubmitManualFormResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitManualFormResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitManualFormResponseMultiError) AllErrors() []error { return m }

// SubmitManualFormResponseValidationError is the validation error returned by
// SubmitManualFormResponse.Validate if the designated constraints aren't met.
type SubmitManualFormResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitManualFormResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitManualFormResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitManualFormResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitManualFormResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitManualFormResponseValidationError) ErrorName() string {
	return "SubmitManualFormResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitManualFormResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitManualFormResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitManualFormResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitManualFormResponseValidationError{}

// Validate checks the field values on GetManualFormConfigRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetManualFormConfigRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetManualFormConfigRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetManualFormConfigRequestMultiError, or nil if none found.
func (m *GetManualFormConfigRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetManualFormConfigRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetManualFormConfigRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetManualFormConfigRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetManualFormConfigRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFormIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetManualFormConfigRequestValidationError{
					field:  "FormIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetManualFormConfigRequestValidationError{
					field:  "FormIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFormIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetManualFormConfigRequestValidationError{
				field:  "FormIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetManualFormConfigRequestMultiError(errors)
	}

	return nil
}

// GetManualFormConfigRequestMultiError is an error wrapping multiple
// validation errors returned by GetManualFormConfigRequest.ValidateAll() if
// the designated constraints aren't met.
type GetManualFormConfigRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetManualFormConfigRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetManualFormConfigRequestMultiError) AllErrors() []error { return m }

// GetManualFormConfigRequestValidationError is the validation error returned
// by GetManualFormConfigRequest.Validate if the designated constraints aren't met.
type GetManualFormConfigRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetManualFormConfigRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetManualFormConfigRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetManualFormConfigRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetManualFormConfigRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetManualFormConfigRequestValidationError) ErrorName() string {
	return "GetManualFormConfigRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetManualFormConfigRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetManualFormConfigRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetManualFormConfigRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetManualFormConfigRequestValidationError{}

// Validate checks the field values on GetManualFormConfigResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetManualFormConfigResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetManualFormConfigResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetManualFormConfigResponseMultiError, or nil if none found.
func (m *GetManualFormConfigResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetManualFormConfigResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetManualFormConfigResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetManualFormConfigResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetManualFormConfigResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFormResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetManualFormConfigResponseValidationError{
					field:  "FormResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetManualFormConfigResponseValidationError{
					field:  "FormResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFormResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetManualFormConfigResponseValidationError{
				field:  "FormResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetManualFormConfigResponseMultiError(errors)
	}

	return nil
}

// GetManualFormConfigResponseMultiError is an error wrapping multiple
// validation errors returned by GetManualFormConfigResponse.ValidateAll() if
// the designated constraints aren't met.
type GetManualFormConfigResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetManualFormConfigResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetManualFormConfigResponseMultiError) AllErrors() []error { return m }

// GetManualFormConfigResponseValidationError is the validation error returned
// by GetManualFormConfigResponse.Validate if the designated constraints
// aren't met.
type GetManualFormConfigResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetManualFormConfigResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetManualFormConfigResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetManualFormConfigResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetManualFormConfigResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetManualFormConfigResponseValidationError) ErrorName() string {
	return "GetManualFormConfigResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetManualFormConfigResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetManualFormConfigResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetManualFormConfigResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetManualFormConfigResponseValidationError{}

// Validate checks the field values on DepositDeclarationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DepositDeclarationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DepositDeclarationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DepositDeclarationRequestMultiError, or nil if none found.
func (m *DepositDeclarationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DepositDeclarationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositDeclarationRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositDeclarationRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositDeclarationRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetAmount() == nil {
		err := DepositDeclarationRequestValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositDeclarationRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositDeclarationRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositDeclarationRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetInterestRate(); val <= 0 || val >= 100 {
		err := DepositDeclarationRequestValidationError{
			field:  "InterestRate",
			reason: "value must be inside range (0, 100)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetStartDate() == nil {
		err := DepositDeclarationRequestValidationError{
			field:  "StartDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetEndDate() == nil {
		err := DepositDeclarationRequestValidationError{
			field:  "EndDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _DepositDeclarationRequest_DepositType_NotInLookup[m.GetDepositType()]; ok {
		err := DepositDeclarationRequestValidationError{
			field:  "DepositType",
			reason: "value must not be in list [DEPOSIT_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetDepositName()); l < 1 || l > 100 {
		err := DepositDeclarationRequestValidationError{
			field:  "DepositName",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DepositDeclarationRequestMultiError(errors)
	}

	return nil
}

// DepositDeclarationRequestMultiError is an error wrapping multiple validation
// errors returned by DepositDeclarationRequest.ValidateAll() if the
// designated constraints aren't met.
type DepositDeclarationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DepositDeclarationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DepositDeclarationRequestMultiError) AllErrors() []error { return m }

// DepositDeclarationRequestValidationError is the validation error returned by
// DepositDeclarationRequest.Validate if the designated constraints aren't met.
type DepositDeclarationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DepositDeclarationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DepositDeclarationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DepositDeclarationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DepositDeclarationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DepositDeclarationRequestValidationError) ErrorName() string {
	return "DepositDeclarationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DepositDeclarationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDepositDeclarationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DepositDeclarationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DepositDeclarationRequestValidationError{}

var _DepositDeclarationRequest_DepositType_NotInLookup = map[ManualAddDepositType]struct{}{
	0: {},
}

// Validate checks the field values on DepositDeclarationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DepositDeclarationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DepositDeclarationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DepositDeclarationResponseMultiError, or nil if none found.
func (m *DepositDeclarationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DepositDeclarationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DepositDeclarationResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DepositDeclarationResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DepositDeclarationResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DepositDeclarationResponseMultiError(errors)
	}

	return nil
}

// DepositDeclarationResponseMultiError is an error wrapping multiple
// validation errors returned by DepositDeclarationResponse.ValidateAll() if
// the designated constraints aren't met.
type DepositDeclarationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DepositDeclarationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DepositDeclarationResponseMultiError) AllErrors() []error { return m }

// DepositDeclarationResponseValidationError is the validation error returned
// by DepositDeclarationResponse.Validate if the designated constraints aren't met.
type DepositDeclarationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DepositDeclarationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DepositDeclarationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DepositDeclarationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DepositDeclarationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DepositDeclarationResponseValidationError) ErrorName() string {
	return "DepositDeclarationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DepositDeclarationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDepositDeclarationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DepositDeclarationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DepositDeclarationResponseValidationError{}

// Validate checks the field values on GetNetWorthDashboardRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNetWorthDashboardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNetWorthDashboardRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNetWorthDashboardRequestMultiError, or nil if none found.
func (m *GetNetWorthDashboardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNetWorthDashboardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetWorthDashboardRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetWorthDashboardRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetWorthDashboardRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DashboardType

	if len(errors) > 0 {
		return GetNetWorthDashboardRequestMultiError(errors)
	}

	return nil
}

// GetNetWorthDashboardRequestMultiError is an error wrapping multiple
// validation errors returned by GetNetWorthDashboardRequest.ValidateAll() if
// the designated constraints aren't met.
type GetNetWorthDashboardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNetWorthDashboardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNetWorthDashboardRequestMultiError) AllErrors() []error { return m }

// GetNetWorthDashboardRequestValidationError is the validation error returned
// by GetNetWorthDashboardRequest.Validate if the designated constraints
// aren't met.
type GetNetWorthDashboardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNetWorthDashboardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNetWorthDashboardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNetWorthDashboardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNetWorthDashboardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNetWorthDashboardRequestValidationError) ErrorName() string {
	return "GetNetWorthDashboardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNetWorthDashboardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNetWorthDashboardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNetWorthDashboardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNetWorthDashboardRequestValidationError{}

// Validate checks the field values on GetNetWorthDashboardResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNetWorthDashboardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNetWorthDashboardResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNetWorthDashboardResponseMultiError, or nil if none found.
func (m *GetNetWorthDashboardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNetWorthDashboardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetWorthDashboardResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetWorthDashboardResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetWorthDashboardResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Title

	if all {
		switch v := interface{}(m.GetRefreshBanner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetWorthDashboardResponseValidationError{
					field:  "RefreshBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetWorthDashboardResponseValidationError{
					field:  "RefreshBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRefreshBanner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetWorthDashboardResponseValidationError{
				field:  "RefreshBanner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVisualisation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetWorthDashboardResponseValidationError{
					field:  "Visualisation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetWorthDashboardResponseValidationError{
					field:  "Visualisation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVisualisation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetWorthDashboardResponseValidationError{
				field:  "Visualisation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNetWorthDashboardResponseValidationError{
						field:  fmt.Sprintf("Sections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNetWorthDashboardResponseValidationError{
						field:  fmt.Sprintf("Sections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNetWorthDashboardResponseValidationError{
					field:  fmt.Sprintf("Sections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetFooter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetWorthDashboardResponseValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetWorthDashboardResponseValidationError{
					field:  "Footer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetWorthDashboardResponseValidationError{
				field:  "Footer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetComponents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNetWorthDashboardResponseValidationError{
						field:  fmt.Sprintf("Components[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNetWorthDashboardResponseValidationError{
						field:  fmt.Sprintf("Components[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNetWorthDashboardResponseValidationError{
					field:  fmt.Sprintf("Components[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetConnectAssetsCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetWorthDashboardResponseValidationError{
					field:  "ConnectAssetsCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetWorthDashboardResponseValidationError{
					field:  "ConnectAssetsCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConnectAssetsCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetWorthDashboardResponseValidationError{
				field:  "ConnectAssetsCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNetWorthDashboardResponseMultiError(errors)
	}

	return nil
}

// GetNetWorthDashboardResponseMultiError is an error wrapping multiple
// validation errors returned by GetNetWorthDashboardResponse.ValidateAll() if
// the designated constraints aren't met.
type GetNetWorthDashboardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNetWorthDashboardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNetWorthDashboardResponseMultiError) AllErrors() []error { return m }

// GetNetWorthDashboardResponseValidationError is the validation error returned
// by GetNetWorthDashboardResponse.Validate if the designated constraints
// aren't met.
type GetNetWorthDashboardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNetWorthDashboardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNetWorthDashboardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNetWorthDashboardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNetWorthDashboardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNetWorthDashboardResponseValidationError) ErrorName() string {
	return "GetNetWorthDashboardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNetWorthDashboardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNetWorthDashboardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNetWorthDashboardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNetWorthDashboardResponseValidationError{}

// Validate checks the field values on Component with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Component) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Component with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ComponentMultiError, or nil
// if none found.
func (m *Component) ValidateAll() error {
	return m.validate(true)
}

func (m *Component) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Component.(type) {
	case *Component_Section:
		if v == nil {
			err := ComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSection()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ComponentValidationError{
						field:  "Section",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ComponentValidationError{
						field:  "Section",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSection()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComponentValidationError{
					field:  "Section",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Component_SecretsSection:
		if v == nil {
			err := ComponentValidationError{
				field:  "Component",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSecretsSection()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ComponentValidationError{
						field:  "SecretsSection",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ComponentValidationError{
						field:  "SecretsSection",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSecretsSection()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ComponentValidationError{
					field:  "SecretsSection",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ComponentMultiError(errors)
	}

	return nil
}

// ComponentMultiError is an error wrapping multiple validation errors returned
// by Component.ValidateAll() if the designated constraints aren't met.
type ComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ComponentMultiError) AllErrors() []error { return m }

// ComponentValidationError is the validation error returned by
// Component.Validate if the designated constraints aren't met.
type ComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ComponentValidationError) ErrorName() string { return "ComponentValidationError" }

// Error satisfies the builtin error interface
func (e ComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ComponentValidationError{}

// Validate checks the field values on SecretsSection with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SecretsSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecretsSection with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SecretsSectionMultiError,
// or nil if none found.
func (m *SecretsSection) ValidateAll() error {
	return m.validate(true)
}

func (m *SecretsSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretsSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretsSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretsSectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretsSectionValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretsSectionValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretsSectionValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSecretSummaries() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretsSectionValidationError{
						field:  fmt.Sprintf("SecretSummaries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretsSectionValidationError{
						field:  fmt.Sprintf("SecretSummaries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretsSectionValidationError{
					field:  fmt.Sprintf("SecretSummaries[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretsSectionValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretsSectionValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretsSectionValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SecretsSectionMultiError(errors)
	}

	return nil
}

// SecretsSectionMultiError is an error wrapping multiple validation errors
// returned by SecretsSection.ValidateAll() if the designated constraints
// aren't met.
type SecretsSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecretsSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecretsSectionMultiError) AllErrors() []error { return m }

// SecretsSectionValidationError is the validation error returned by
// SecretsSection.Validate if the designated constraints aren't met.
type SecretsSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecretsSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecretsSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecretsSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecretsSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecretsSectionValidationError) ErrorName() string { return "SecretsSectionValidationError" }

// Error satisfies the builtin error interface
func (e SecretsSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecretsSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecretsSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecretsSectionValidationError{}

// Validate checks the field values on NetWorthVisualisation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NetWorthVisualisation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetWorthVisualisation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetWorthVisualisationMultiError, or nil if none found.
func (m *NetWorthVisualisation) ValidateAll() error {
	return m.validate(true)
}

func (m *NetWorthVisualisation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VisualisationType

	switch v := m.Params.(type) {
	case *NetWorthVisualisation_IslandParams:
		if v == nil {
			err := NetWorthVisualisationValidationError{
				field:  "Params",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetIslandParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NetWorthVisualisationValidationError{
						field:  "IslandParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NetWorthVisualisationValidationError{
						field:  "IslandParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIslandParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NetWorthVisualisationValidationError{
					field:  "IslandParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return NetWorthVisualisationMultiError(errors)
	}

	return nil
}

// NetWorthVisualisationMultiError is an error wrapping multiple validation
// errors returned by NetWorthVisualisation.ValidateAll() if the designated
// constraints aren't met.
type NetWorthVisualisationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetWorthVisualisationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetWorthVisualisationMultiError) AllErrors() []error { return m }

// NetWorthVisualisationValidationError is the validation error returned by
// NetWorthVisualisation.Validate if the designated constraints aren't met.
type NetWorthVisualisationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetWorthVisualisationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetWorthVisualisationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetWorthVisualisationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetWorthVisualisationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetWorthVisualisationValidationError) ErrorName() string {
	return "NetWorthVisualisationValidationError"
}

// Error satisfies the builtin error interface
func (e NetWorthVisualisationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetWorthVisualisation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetWorthVisualisationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetWorthVisualisationValidationError{}

// Validate checks the field values on IslandParams with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IslandParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IslandParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IslandParamsMultiError, or
// nil if none found.
func (m *IslandParams) ValidateAll() error {
	return m.validate(true)
}

func (m *IslandParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCategories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, IslandParamsValidationError{
						field:  fmt.Sprintf("Categories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, IslandParamsValidationError{
						field:  fmt.Sprintf("Categories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return IslandParamsValidationError{
					field:  fmt.Sprintf("Categories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBackgroundImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IslandParamsValidationError{
					field:  "BackgroundImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IslandParamsValidationError{
					field:  "BackgroundImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IslandParamsValidationError{
				field:  "BackgroundImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IslandParamsValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IslandParamsValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IslandParamsValidationError{
				field:  "Summary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IslandParamsValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IslandParamsValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IslandParamsValidationError{
				field:  "Message",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IslandParamsMultiError(errors)
	}

	return nil
}

// IslandParamsMultiError is an error wrapping multiple validation errors
// returned by IslandParams.ValidateAll() if the designated constraints aren't met.
type IslandParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IslandParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IslandParamsMultiError) AllErrors() []error { return m }

// IslandParamsValidationError is the validation error returned by
// IslandParams.Validate if the designated constraints aren't met.
type IslandParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IslandParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IslandParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IslandParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IslandParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IslandParamsValidationError) ErrorName() string { return "IslandParamsValidationError" }

// Error satisfies the builtin error interface
func (e IslandParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIslandParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IslandParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IslandParamsValidationError{}

// Validate checks the field values on Image with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Image) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Image with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ImageMultiError, or nil if none found.
func (m *Image) ValidateAll() error {
	return m.validate(true)
}

func (m *Image) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ImageUrl

	// no validation rules for ImageToScreenWidthRatio

	// no validation rules for ImageAspectRatio

	if len(errors) > 0 {
		return ImageMultiError(errors)
	}

	return nil
}

// ImageMultiError is an error wrapping multiple validation errors returned by
// Image.ValidateAll() if the designated constraints aren't met.
type ImageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageMultiError) AllErrors() []error { return m }

// ImageValidationError is the validation error returned by Image.Validate if
// the designated constraints aren't met.
type ImageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageValidationError) ErrorName() string { return "ImageValidationError" }

// Error satisfies the builtin error interface
func (e ImageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageValidationError{}

// Validate checks the field values on VisualisationSummary with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VisualisationSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VisualisationSummary with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VisualisationSummaryMultiError, or nil if none found.
func (m *VisualisationSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *VisualisationSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VisualisationSummaryValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VisualisationSummaryValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VisualisationSummaryValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VisualisationSummaryValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VisualisationSummaryValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VisualisationSummaryValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CategoriesAdded

	// no validation rules for TotalCategories

	// no validation rules for BorderColor

	// no validation rules for ShadowColor

	if len(errors) > 0 {
		return VisualisationSummaryMultiError(errors)
	}

	return nil
}

// VisualisationSummaryMultiError is an error wrapping multiple validation
// errors returned by VisualisationSummary.ValidateAll() if the designated
// constraints aren't met.
type VisualisationSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VisualisationSummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VisualisationSummaryMultiError) AllErrors() []error { return m }

// VisualisationSummaryValidationError is the validation error returned by
// VisualisationSummary.Validate if the designated constraints aren't met.
type VisualisationSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VisualisationSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VisualisationSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VisualisationSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VisualisationSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VisualisationSummaryValidationError) ErrorName() string {
	return "VisualisationSummaryValidationError"
}

// Error satisfies the builtin error interface
func (e VisualisationSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVisualisationSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VisualisationSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VisualisationSummaryValidationError{}

// Validate checks the field values on CategoryVisualisationItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CategoryVisualisationItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CategoryVisualisationItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CategoryVisualisationItemMultiError, or nil if none found.
func (m *CategoryVisualisationItem) ValidateAll() error {
	return m.validate(true)
}

func (m *CategoryVisualisationItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCategoryIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CategoryVisualisationItemValidationError{
					field:  "CategoryIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CategoryVisualisationItemValidationError{
					field:  "CategoryIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCategoryIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CategoryVisualisationItemValidationError{
				field:  "CategoryIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for X

	// no validation rules for Y

	// no validation rules for Width

	// no validation rules for Height

	if len(errors) > 0 {
		return CategoryVisualisationItemMultiError(errors)
	}

	return nil
}

// CategoryVisualisationItemMultiError is an error wrapping multiple validation
// errors returned by CategoryVisualisationItem.ValidateAll() if the
// designated constraints aren't met.
type CategoryVisualisationItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CategoryVisualisationItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CategoryVisualisationItemMultiError) AllErrors() []error { return m }

// CategoryVisualisationItemValidationError is the validation error returned by
// CategoryVisualisationItem.Validate if the designated constraints aren't met.
type CategoryVisualisationItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CategoryVisualisationItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CategoryVisualisationItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CategoryVisualisationItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CategoryVisualisationItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CategoryVisualisationItemValidationError) ErrorName() string {
	return "CategoryVisualisationItemValidationError"
}

// Error satisfies the builtin error interface
func (e CategoryVisualisationItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCategoryVisualisationItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CategoryVisualisationItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CategoryVisualisationItemValidationError{}

// Validate checks the field values on Section with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Section) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Section with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SectionMultiError, or nil if none found.
func (m *Section) ValidateAll() error {
	return m.validate(true)
}

func (m *Section) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionValidationError{
				field:  "Summary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddMoreButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionValidationError{
					field:  "AddMoreButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionValidationError{
					field:  "AddMoreButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddMoreButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionValidationError{
				field:  "AddMoreButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddMoreWidgets()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionValidationError{
					field:  "AddMoreWidgets",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionValidationError{
					field:  "AddMoreWidgets",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddMoreWidgets()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionValidationError{
				field:  "AddMoreWidgets",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetWidgets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  fmt.Sprintf("Widgets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SectionValidationError{
						field:  fmt.Sprintf("Widgets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SectionValidationError{
					field:  fmt.Sprintf("Widgets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Limit

	if all {
		switch v := interface{}(m.GetSectionHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SectionValidationError{
					field:  "SectionHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SectionValidationError{
					field:  "SectionHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSectionHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SectionValidationError{
				field:  "SectionHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SectionMultiError(errors)
	}

	return nil
}

// SectionMultiError is an error wrapping multiple validation errors returned
// by Section.ValidateAll() if the designated constraints aren't met.
type SectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SectionMultiError) AllErrors() []error { return m }

// SectionValidationError is the validation error returned by Section.Validate
// if the designated constraints aren't met.
type SectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SectionValidationError) ErrorName() string { return "SectionValidationError" }

// Error satisfies the builtin error interface
func (e SectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SectionValidationError{}

// Validate checks the field values on AddMoreWidgets with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddMoreWidgets) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddMoreWidgets with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddMoreWidgetsMultiError,
// or nil if none found.
func (m *AddMoreWidgets) ValidateAll() error {
	return m.validate(true)
}

func (m *AddMoreWidgets) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetWidgets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddMoreWidgetsValidationError{
						field:  fmt.Sprintf("Widgets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddMoreWidgetsValidationError{
						field:  fmt.Sprintf("Widgets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddMoreWidgetsValidationError{
					field:  fmt.Sprintf("Widgets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AddMoreWidgetsMultiError(errors)
	}

	return nil
}

// AddMoreWidgetsMultiError is an error wrapping multiple validation errors
// returned by AddMoreWidgets.ValidateAll() if the designated constraints
// aren't met.
type AddMoreWidgetsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddMoreWidgetsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddMoreWidgetsMultiError) AllErrors() []error { return m }

// AddMoreWidgetsValidationError is the validation error returned by
// AddMoreWidgets.Validate if the designated constraints aren't met.
type AddMoreWidgetsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddMoreWidgetsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddMoreWidgetsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddMoreWidgetsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddMoreWidgetsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddMoreWidgetsValidationError) ErrorName() string { return "AddMoreWidgetsValidationError" }

// Error satisfies the builtin error interface
func (e AddMoreWidgetsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddMoreWidgets.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddMoreWidgetsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddMoreWidgetsValidationError{}

// Validate checks the field values on Widget with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Widget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Widget with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in WidgetMultiError, or nil if none found.
func (m *Widget) ValidateAll() error {
	return m.validate(true)
}

func (m *Widget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Type

	// no validation rules for CacheControl

	// no validation rules for State

	// no validation rules for WidgetAnalyticsPayload

	switch v := m.Params.(type) {
	case *Widget_CardWidgetParams:
		if v == nil {
			err := WidgetValidationError{
				field:  "Params",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCardWidgetParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WidgetValidationError{
						field:  "CardWidgetParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WidgetValidationError{
						field:  "CardWidgetParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCardWidgetParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WidgetValidationError{
					field:  "CardWidgetParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return WidgetMultiError(errors)
	}

	return nil
}

// WidgetMultiError is an error wrapping multiple validation errors returned by
// Widget.ValidateAll() if the designated constraints aren't met.
type WidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WidgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WidgetMultiError) AllErrors() []error { return m }

// WidgetValidationError is the validation error returned by Widget.Validate if
// the designated constraints aren't met.
type WidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WidgetValidationError) ErrorName() string { return "WidgetValidationError" }

// Error satisfies the builtin error interface
func (e WidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WidgetValidationError{}

// Validate checks the field values on CardWidgetParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CardWidgetParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardWidgetParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardWidgetParamsMultiError, or nil if none found.
func (m *CardWidgetParams) ValidateAll() error {
	return m.validate(true)
}

func (m *CardWidgetParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardWidgetParamsValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardWidgetParamsValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardWidgetParamsValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardWidgetParamsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardWidgetParamsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardWidgetParamsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrimaryText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardWidgetParamsValidationError{
					field:  "PrimaryText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardWidgetParamsValidationError{
					field:  "PrimaryText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrimaryText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardWidgetParamsValidationError{
				field:  "PrimaryText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardWidgetParamsValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardWidgetParamsValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardWidgetParamsValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardWidgetParamsValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardWidgetParamsValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardWidgetParamsValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BackgroundColor

	if all {
		switch v := interface{}(m.GetTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardWidgetParamsValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardWidgetParamsValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardWidgetParamsValidationError{
				field:  "Tag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardWidgetParamsMultiError(errors)
	}

	return nil
}

// CardWidgetParamsMultiError is an error wrapping multiple validation errors
// returned by CardWidgetParams.ValidateAll() if the designated constraints
// aren't met.
type CardWidgetParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardWidgetParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardWidgetParamsMultiError) AllErrors() []error { return m }

// CardWidgetParamsValidationError is the validation error returned by
// CardWidgetParams.Validate if the designated constraints aren't met.
type CardWidgetParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardWidgetParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardWidgetParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardWidgetParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardWidgetParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardWidgetParamsValidationError) ErrorName() string { return "CardWidgetParamsValidationError" }

// Error satisfies the builtin error interface
func (e CardWidgetParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardWidgetParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardWidgetParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardWidgetParamsValidationError{}

// Validate checks the field values on GetNetWorthSummaryForHomeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetNetWorthSummaryForHomeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNetWorthSummaryForHomeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetNetWorthSummaryForHomeRequestMultiError, or nil if none found.
func (m *GetNetWorthSummaryForHomeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNetWorthSummaryForHomeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetNetWorthSummaryForHomeRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetWorthSummaryForHomeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetWorthSummaryForHomeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetWorthSummaryForHomeRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNetWorthSummaryForHomeRequestMultiError(errors)
	}

	return nil
}

// GetNetWorthSummaryForHomeRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetNetWorthSummaryForHomeRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNetWorthSummaryForHomeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNetWorthSummaryForHomeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNetWorthSummaryForHomeRequestMultiError) AllErrors() []error { return m }

// GetNetWorthSummaryForHomeRequestValidationError is the validation error
// returned by GetNetWorthSummaryForHomeRequest.Validate if the designated
// constraints aren't met.
type GetNetWorthSummaryForHomeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNetWorthSummaryForHomeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNetWorthSummaryForHomeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNetWorthSummaryForHomeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNetWorthSummaryForHomeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNetWorthSummaryForHomeRequestValidationError) ErrorName() string {
	return "GetNetWorthSummaryForHomeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNetWorthSummaryForHomeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNetWorthSummaryForHomeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNetWorthSummaryForHomeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNetWorthSummaryForHomeRequestValidationError{}

// Validate checks the field values on GetNetWorthSummaryForHomeResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetNetWorthSummaryForHomeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNetWorthSummaryForHomeResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetNetWorthSummaryForHomeResponseMultiError, or nil if none found.
func (m *GetNetWorthSummaryForHomeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNetWorthSummaryForHomeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetWorthSummaryForHomeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetWorthSummaryForHomeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetWorthSummaryForHomeResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDashboardInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetWorthSummaryForHomeResponseValidationError{
					field:  "DashboardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetWorthSummaryForHomeResponseValidationError{
					field:  "DashboardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDashboardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetWorthSummaryForHomeResponseValidationError{
				field:  "DashboardInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNetWorthSummaryForHomeResponseMultiError(errors)
	}

	return nil
}

// GetNetWorthSummaryForHomeResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetNetWorthSummaryForHomeResponse.ValidateAll() if the designated
// constraints aren't met.
type GetNetWorthSummaryForHomeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNetWorthSummaryForHomeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNetWorthSummaryForHomeResponseMultiError) AllErrors() []error { return m }

// GetNetWorthSummaryForHomeResponseValidationError is the validation error
// returned by GetNetWorthSummaryForHomeResponse.Validate if the designated
// constraints aren't met.
type GetNetWorthSummaryForHomeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNetWorthSummaryForHomeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNetWorthSummaryForHomeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNetWorthSummaryForHomeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNetWorthSummaryForHomeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNetWorthSummaryForHomeResponseValidationError) ErrorName() string {
	return "GetNetWorthSummaryForHomeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNetWorthSummaryForHomeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNetWorthSummaryForHomeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNetWorthSummaryForHomeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNetWorthSummaryForHomeResponseValidationError{}

// Validate checks the field values on GetNextNetWorthRefreshActionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetNextNetWorthRefreshActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNextNetWorthRefreshActionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetNextNetWorthRefreshActionRequestMultiError, or nil if none found.
func (m *GetNextNetWorthRefreshActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNextNetWorthRefreshActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetNextNetWorthRefreshActionRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNextNetWorthRefreshActionRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNextNetWorthRefreshActionRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNextNetWorthRefreshActionRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRequestParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNextNetWorthRefreshActionRequestValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNextNetWorthRefreshActionRequestValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNextNetWorthRefreshActionRequestValidationError{
				field:  "RequestParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNextNetWorthRefreshActionRequestMultiError(errors)
	}

	return nil
}

// GetNextNetWorthRefreshActionRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetNextNetWorthRefreshActionRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNextNetWorthRefreshActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNextNetWorthRefreshActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNextNetWorthRefreshActionRequestMultiError) AllErrors() []error { return m }

// GetNextNetWorthRefreshActionRequestValidationError is the validation error
// returned by GetNextNetWorthRefreshActionRequest.Validate if the designated
// constraints aren't met.
type GetNextNetWorthRefreshActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNextNetWorthRefreshActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNextNetWorthRefreshActionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNextNetWorthRefreshActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNextNetWorthRefreshActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNextNetWorthRefreshActionRequestValidationError) ErrorName() string {
	return "GetNextNetWorthRefreshActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNextNetWorthRefreshActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNextNetWorthRefreshActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNextNetWorthRefreshActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNextNetWorthRefreshActionRequestValidationError{}

// Validate checks the field values on GetNextNetWorthRefreshActionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetNextNetWorthRefreshActionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNextNetWorthRefreshActionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetNextNetWorthRefreshActionResponseMultiError, or nil if none found.
func (m *GetNextNetWorthRefreshActionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNextNetWorthRefreshActionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNextNetWorthRefreshActionResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNextNetWorthRefreshActionResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNextNetWorthRefreshActionResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNextNetWorthRefreshActionResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNextNetWorthRefreshActionResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNextNetWorthRefreshActionResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRefreshHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNextNetWorthRefreshActionResponseValidationError{
					field:  "RefreshHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNextNetWorthRefreshActionResponseValidationError{
					field:  "RefreshHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRefreshHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNextNetWorthRefreshActionResponseValidationError{
				field:  "RefreshHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNextNetWorthRefreshActionResponseMultiError(errors)
	}

	return nil
}

// GetNextNetWorthRefreshActionResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetNextNetWorthRefreshActionResponse.ValidateAll() if the designated
// constraints aren't met.
type GetNextNetWorthRefreshActionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNextNetWorthRefreshActionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNextNetWorthRefreshActionResponseMultiError) AllErrors() []error { return m }

// GetNextNetWorthRefreshActionResponseValidationError is the validation error
// returned by GetNextNetWorthRefreshActionResponse.Validate if the designated
// constraints aren't met.
type GetNextNetWorthRefreshActionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNextNetWorthRefreshActionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNextNetWorthRefreshActionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNextNetWorthRefreshActionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNextNetWorthRefreshActionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNextNetWorthRefreshActionResponseValidationError) ErrorName() string {
	return "GetNextNetWorthRefreshActionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNextNetWorthRefreshActionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNextNetWorthRefreshActionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNextNetWorthRefreshActionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNextNetWorthRefreshActionResponseValidationError{}

// Validate checks the field values on UpdateManualAssetsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateManualAssetsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateManualAssetsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateManualAssetsRequestMultiError, or nil if none found.
func (m *UpdateManualAssetsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateManualAssetsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := UpdateManualAssetsRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateManualAssetsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateManualAssetsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateManualAssetsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetUpdatedAssetCurrentValuesV2()))
		i := 0
		for key := range m.GetUpdatedAssetCurrentValuesV2() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetUpdatedAssetCurrentValuesV2()[key]
			_ = val

			// no validation rules for UpdatedAssetCurrentValuesV2[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, UpdateManualAssetsRequestValidationError{
							field:  fmt.Sprintf("UpdatedAssetCurrentValuesV2[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, UpdateManualAssetsRequestValidationError{
							field:  fmt.Sprintf("UpdatedAssetCurrentValuesV2[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return UpdateManualAssetsRequestValidationError{
						field:  fmt.Sprintf("UpdatedAssetCurrentValuesV2[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return UpdateManualAssetsRequestMultiError(errors)
	}

	return nil
}

// UpdateManualAssetsRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateManualAssetsRequest.ValidateAll() if the
// designated constraints aren't met.
type UpdateManualAssetsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateManualAssetsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateManualAssetsRequestMultiError) AllErrors() []error { return m }

// UpdateManualAssetsRequestValidationError is the validation error returned by
// UpdateManualAssetsRequest.Validate if the designated constraints aren't met.
type UpdateManualAssetsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateManualAssetsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateManualAssetsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateManualAssetsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateManualAssetsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateManualAssetsRequestValidationError) ErrorName() string {
	return "UpdateManualAssetsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateManualAssetsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateManualAssetsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateManualAssetsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateManualAssetsRequestValidationError{}

// Validate checks the field values on UpdateManualAssetsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateManualAssetsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateManualAssetsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateManualAssetsResponseMultiError, or nil if none found.
func (m *UpdateManualAssetsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateManualAssetsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateManualAssetsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateManualAssetsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateManualAssetsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextScreen()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateManualAssetsResponseValidationError{
					field:  "NextScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateManualAssetsResponseValidationError{
					field:  "NextScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextScreen()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateManualAssetsResponseValidationError{
				field:  "NextScreen",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateManualAssetsResponseMultiError(errors)
	}

	return nil
}

// UpdateManualAssetsResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateManualAssetsResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateManualAssetsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateManualAssetsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateManualAssetsResponseMultiError) AllErrors() []error { return m }

// UpdateManualAssetsResponseValidationError is the validation error returned
// by UpdateManualAssetsResponse.Validate if the designated constraints aren't met.
type UpdateManualAssetsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateManualAssetsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateManualAssetsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateManualAssetsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateManualAssetsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateManualAssetsResponseValidationError) ErrorName() string {
	return "UpdateManualAssetsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateManualAssetsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateManualAssetsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateManualAssetsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateManualAssetsResponseValidationError{}

// Validate checks the field values on SearchAssetFormFieldOptionsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SearchAssetFormFieldOptionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchAssetFormFieldOptionsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SearchAssetFormFieldOptionsRequestMultiError, or nil if none found.
func (m *SearchAssetFormFieldOptionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchAssetFormFieldOptionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := SearchAssetFormFieldOptionsRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchAssetFormFieldOptionsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchAssetFormFieldOptionsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchAssetFormFieldOptionsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SearchIdentifier

	// no validation rules for SearchText

	if len(errors) > 0 {
		return SearchAssetFormFieldOptionsRequestMultiError(errors)
	}

	return nil
}

// SearchAssetFormFieldOptionsRequestMultiError is an error wrapping multiple
// validation errors returned by
// SearchAssetFormFieldOptionsRequest.ValidateAll() if the designated
// constraints aren't met.
type SearchAssetFormFieldOptionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchAssetFormFieldOptionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchAssetFormFieldOptionsRequestMultiError) AllErrors() []error { return m }

// SearchAssetFormFieldOptionsRequestValidationError is the validation error
// returned by SearchAssetFormFieldOptionsRequest.Validate if the designated
// constraints aren't met.
type SearchAssetFormFieldOptionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchAssetFormFieldOptionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchAssetFormFieldOptionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchAssetFormFieldOptionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchAssetFormFieldOptionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchAssetFormFieldOptionsRequestValidationError) ErrorName() string {
	return "SearchAssetFormFieldOptionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SearchAssetFormFieldOptionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchAssetFormFieldOptionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchAssetFormFieldOptionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchAssetFormFieldOptionsRequestValidationError{}

// Validate checks the field values on SearchAssetFormFieldOptionsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SearchAssetFormFieldOptionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchAssetFormFieldOptionsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SearchAssetFormFieldOptionsResponseMultiError, or nil if none found.
func (m *SearchAssetFormFieldOptionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchAssetFormFieldOptionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchAssetFormFieldOptionsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetChoices() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
						field:  fmt.Sprintf("Choices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
						field:  fmt.Sprintf("Choices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchAssetFormFieldOptionsResponseValidationError{
					field:  fmt.Sprintf("Choices[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	switch v := m.SearchResponse.(type) {
	case *SearchAssetFormFieldOptionsResponse_PresetChoicesList:
		if v == nil {
			err := SearchAssetFormFieldOptionsResponseValidationError{
				field:  "SearchResponse",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPresetChoicesList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
						field:  "PresetChoicesList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
						field:  "PresetChoicesList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPresetChoicesList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchAssetFormFieldOptionsResponseValidationError{
					field:  "PresetChoicesList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SearchAssetFormFieldOptionsResponse_ErrorView_:
		if v == nil {
			err := SearchAssetFormFieldOptionsResponseValidationError{
				field:  "SearchResponse",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetErrorView()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
						field:  "ErrorView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchAssetFormFieldOptionsResponseValidationError{
						field:  "ErrorView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetErrorView()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchAssetFormFieldOptionsResponseValidationError{
					field:  "ErrorView",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SearchAssetFormFieldOptionsResponseMultiError(errors)
	}

	return nil
}

// SearchAssetFormFieldOptionsResponseMultiError is an error wrapping multiple
// validation errors returned by
// SearchAssetFormFieldOptionsResponse.ValidateAll() if the designated
// constraints aren't met.
type SearchAssetFormFieldOptionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchAssetFormFieldOptionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchAssetFormFieldOptionsResponseMultiError) AllErrors() []error { return m }

// SearchAssetFormFieldOptionsResponseValidationError is the validation error
// returned by SearchAssetFormFieldOptionsResponse.Validate if the designated
// constraints aren't met.
type SearchAssetFormFieldOptionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchAssetFormFieldOptionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchAssetFormFieldOptionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchAssetFormFieldOptionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchAssetFormFieldOptionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchAssetFormFieldOptionsResponseValidationError) ErrorName() string {
	return "SearchAssetFormFieldOptionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SearchAssetFormFieldOptionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchAssetFormFieldOptionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchAssetFormFieldOptionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchAssetFormFieldOptionsResponseValidationError{}

// Validate checks the field values on PresetChoicesList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PresetChoicesList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PresetChoicesList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PresetChoicesListMultiError, or nil if none found.
func (m *PresetChoicesList) ValidateAll() error {
	return m.validate(true)
}

func (m *PresetChoicesList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPresetChoices() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PresetChoicesListValidationError{
						field:  fmt.Sprintf("PresetChoices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PresetChoicesListValidationError{
						field:  fmt.Sprintf("PresetChoices[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PresetChoicesListValidationError{
					field:  fmt.Sprintf("PresetChoices[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PresetChoicesListMultiError(errors)
	}

	return nil
}

// PresetChoicesListMultiError is an error wrapping multiple validation errors
// returned by PresetChoicesList.ValidateAll() if the designated constraints
// aren't met.
type PresetChoicesListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PresetChoicesListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PresetChoicesListMultiError) AllErrors() []error { return m }

// PresetChoicesListValidationError is the validation error returned by
// PresetChoicesList.Validate if the designated constraints aren't met.
type PresetChoicesListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PresetChoicesListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PresetChoicesListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PresetChoicesListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PresetChoicesListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PresetChoicesListValidationError) ErrorName() string {
	return "PresetChoicesListValidationError"
}

// Error satisfies the builtin error interface
func (e PresetChoicesListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPresetChoicesList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PresetChoicesListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PresetChoicesListValidationError{}

// Validate checks the field values on GetCreditScoreSummaryForHomeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCreditScoreSummaryForHomeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditScoreSummaryForHomeRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCreditScoreSummaryForHomeRequestMultiError, or nil if none found.
func (m *GetCreditScoreSummaryForHomeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditScoreSummaryForHomeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetCreditScoreSummaryForHomeRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditScoreSummaryForHomeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditScoreSummaryForHomeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditScoreSummaryForHomeRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCreditScoreSummaryForHomeRequestMultiError(errors)
	}

	return nil
}

// GetCreditScoreSummaryForHomeRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetCreditScoreSummaryForHomeRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCreditScoreSummaryForHomeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditScoreSummaryForHomeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditScoreSummaryForHomeRequestMultiError) AllErrors() []error { return m }

// GetCreditScoreSummaryForHomeRequestValidationError is the validation error
// returned by GetCreditScoreSummaryForHomeRequest.Validate if the designated
// constraints aren't met.
type GetCreditScoreSummaryForHomeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditScoreSummaryForHomeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditScoreSummaryForHomeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditScoreSummaryForHomeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditScoreSummaryForHomeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditScoreSummaryForHomeRequestValidationError) ErrorName() string {
	return "GetCreditScoreSummaryForHomeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditScoreSummaryForHomeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditScoreSummaryForHomeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditScoreSummaryForHomeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditScoreSummaryForHomeRequestValidationError{}

// Validate checks the field values on GetCreditScoreSummaryForHomeResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCreditScoreSummaryForHomeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditScoreSummaryForHomeResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCreditScoreSummaryForHomeResponseMultiError, or nil if none found.
func (m *GetCreditScoreSummaryForHomeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditScoreSummaryForHomeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditScoreSummaryForHomeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditScoreSummaryForHomeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditScoreSummaryForHomeResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDashboardInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditScoreSummaryForHomeResponseValidationError{
					field:  "DashboardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditScoreSummaryForHomeResponseValidationError{
					field:  "DashboardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDashboardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditScoreSummaryForHomeResponseValidationError{
				field:  "DashboardInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCreditScoreSummaryForHomeResponseMultiError(errors)
	}

	return nil
}

// GetCreditScoreSummaryForHomeResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetCreditScoreSummaryForHomeResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCreditScoreSummaryForHomeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditScoreSummaryForHomeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditScoreSummaryForHomeResponseMultiError) AllErrors() []error { return m }

// GetCreditScoreSummaryForHomeResponseValidationError is the validation error
// returned by GetCreditScoreSummaryForHomeResponse.Validate if the designated
// constraints aren't met.
type GetCreditScoreSummaryForHomeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditScoreSummaryForHomeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditScoreSummaryForHomeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditScoreSummaryForHomeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditScoreSummaryForHomeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditScoreSummaryForHomeResponseValidationError) ErrorName() string {
	return "GetCreditScoreSummaryForHomeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditScoreSummaryForHomeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditScoreSummaryForHomeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditScoreSummaryForHomeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditScoreSummaryForHomeResponseValidationError{}

// Validate checks the field values on GetEpfSummaryForHomeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEpfSummaryForHomeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEpfSummaryForHomeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEpfSummaryForHomeRequestMultiError, or nil if none found.
func (m *GetEpfSummaryForHomeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEpfSummaryForHomeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetEpfSummaryForHomeRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEpfSummaryForHomeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEpfSummaryForHomeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEpfSummaryForHomeRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetEpfSummaryForHomeRequestMultiError(errors)
	}

	return nil
}

// GetEpfSummaryForHomeRequestMultiError is an error wrapping multiple
// validation errors returned by GetEpfSummaryForHomeRequest.ValidateAll() if
// the designated constraints aren't met.
type GetEpfSummaryForHomeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEpfSummaryForHomeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEpfSummaryForHomeRequestMultiError) AllErrors() []error { return m }

// GetEpfSummaryForHomeRequestValidationError is the validation error returned
// by GetEpfSummaryForHomeRequest.Validate if the designated constraints
// aren't met.
type GetEpfSummaryForHomeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEpfSummaryForHomeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEpfSummaryForHomeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEpfSummaryForHomeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEpfSummaryForHomeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEpfSummaryForHomeRequestValidationError) ErrorName() string {
	return "GetEpfSummaryForHomeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEpfSummaryForHomeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEpfSummaryForHomeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEpfSummaryForHomeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEpfSummaryForHomeRequestValidationError{}

// Validate checks the field values on GetEpfSummaryForHomeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEpfSummaryForHomeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEpfSummaryForHomeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEpfSummaryForHomeResponseMultiError, or nil if none found.
func (m *GetEpfSummaryForHomeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEpfSummaryForHomeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEpfSummaryForHomeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEpfSummaryForHomeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEpfSummaryForHomeResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDashboardInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEpfSummaryForHomeResponseValidationError{
					field:  "DashboardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEpfSummaryForHomeResponseValidationError{
					field:  "DashboardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDashboardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEpfSummaryForHomeResponseValidationError{
				field:  "DashboardInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetEpfSummaryForHomeResponseMultiError(errors)
	}

	return nil
}

// GetEpfSummaryForHomeResponseMultiError is an error wrapping multiple
// validation errors returned by GetEpfSummaryForHomeResponse.ValidateAll() if
// the designated constraints aren't met.
type GetEpfSummaryForHomeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEpfSummaryForHomeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEpfSummaryForHomeResponseMultiError) AllErrors() []error { return m }

// GetEpfSummaryForHomeResponseValidationError is the validation error returned
// by GetEpfSummaryForHomeResponse.Validate if the designated constraints
// aren't met.
type GetEpfSummaryForHomeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEpfSummaryForHomeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEpfSummaryForHomeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEpfSummaryForHomeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEpfSummaryForHomeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEpfSummaryForHomeResponseValidationError) ErrorName() string {
	return "GetEpfSummaryForHomeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEpfSummaryForHomeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEpfSummaryForHomeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEpfSummaryForHomeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEpfSummaryForHomeResponseValidationError{}

// Validate checks the field values on GetMfSummaryForHomeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMfSummaryForHomeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMfSummaryForHomeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMfSummaryForHomeRequestMultiError, or nil if none found.
func (m *GetMfSummaryForHomeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMfSummaryForHomeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetMfSummaryForHomeRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMfSummaryForHomeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMfSummaryForHomeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMfSummaryForHomeRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMfSummaryForHomeRequestMultiError(errors)
	}

	return nil
}

// GetMfSummaryForHomeRequestMultiError is an error wrapping multiple
// validation errors returned by GetMfSummaryForHomeRequest.ValidateAll() if
// the designated constraints aren't met.
type GetMfSummaryForHomeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMfSummaryForHomeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMfSummaryForHomeRequestMultiError) AllErrors() []error { return m }

// GetMfSummaryForHomeRequestValidationError is the validation error returned
// by GetMfSummaryForHomeRequest.Validate if the designated constraints aren't met.
type GetMfSummaryForHomeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMfSummaryForHomeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMfSummaryForHomeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMfSummaryForHomeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMfSummaryForHomeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMfSummaryForHomeRequestValidationError) ErrorName() string {
	return "GetMfSummaryForHomeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMfSummaryForHomeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMfSummaryForHomeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMfSummaryForHomeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMfSummaryForHomeRequestValidationError{}

// Validate checks the field values on GetMfSummaryForHomeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMfSummaryForHomeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMfSummaryForHomeResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMfSummaryForHomeResponseMultiError, or nil if none found.
func (m *GetMfSummaryForHomeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMfSummaryForHomeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMfSummaryForHomeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMfSummaryForHomeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMfSummaryForHomeResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDashboardInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMfSummaryForHomeResponseValidationError{
					field:  "DashboardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMfSummaryForHomeResponseValidationError{
					field:  "DashboardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDashboardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMfSummaryForHomeResponseValidationError{
				field:  "DashboardInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMfSummaryForHomeResponseMultiError(errors)
	}

	return nil
}

// GetMfSummaryForHomeResponseMultiError is an error wrapping multiple
// validation errors returned by GetMfSummaryForHomeResponse.ValidateAll() if
// the designated constraints aren't met.
type GetMfSummaryForHomeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMfSummaryForHomeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMfSummaryForHomeResponseMultiError) AllErrors() []error { return m }

// GetMfSummaryForHomeResponseValidationError is the validation error returned
// by GetMfSummaryForHomeResponse.Validate if the designated constraints
// aren't met.
type GetMfSummaryForHomeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMfSummaryForHomeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMfSummaryForHomeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMfSummaryForHomeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMfSummaryForHomeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMfSummaryForHomeResponseValidationError) ErrorName() string {
	return "GetMfSummaryForHomeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMfSummaryForHomeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMfSummaryForHomeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMfSummaryForHomeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMfSummaryForHomeResponseValidationError{}

// Validate checks the field values on GetWealthBuilderLandingPageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetWealthBuilderLandingPageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetWealthBuilderLandingPageRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetWealthBuilderLandingPageRequestMultiError, or nil if none found.
func (m *GetWealthBuilderLandingPageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWealthBuilderLandingPageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetWealthBuilderLandingPageRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWealthBuilderLandingPageRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWealthBuilderLandingPageRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWealthBuilderLandingPageRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetWealthBuilderLandingPageRequestMultiError(errors)
	}

	return nil
}

// GetWealthBuilderLandingPageRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetWealthBuilderLandingPageRequest.ValidateAll() if the designated
// constraints aren't met.
type GetWealthBuilderLandingPageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWealthBuilderLandingPageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWealthBuilderLandingPageRequestMultiError) AllErrors() []error { return m }

// GetWealthBuilderLandingPageRequestValidationError is the validation error
// returned by GetWealthBuilderLandingPageRequest.Validate if the designated
// constraints aren't met.
type GetWealthBuilderLandingPageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWealthBuilderLandingPageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWealthBuilderLandingPageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWealthBuilderLandingPageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWealthBuilderLandingPageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWealthBuilderLandingPageRequestValidationError) ErrorName() string {
	return "GetWealthBuilderLandingPageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetWealthBuilderLandingPageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWealthBuilderLandingPageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWealthBuilderLandingPageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWealthBuilderLandingPageRequestValidationError{}

// Validate checks the field values on GetWealthBuilderLandingPageResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetWealthBuilderLandingPageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetWealthBuilderLandingPageResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetWealthBuilderLandingPageResponseMultiError, or nil if none found.
func (m *GetWealthBuilderLandingPageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWealthBuilderLandingPageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWealthBuilderLandingPageResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWealthBuilderLandingPageResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWealthBuilderLandingPageResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetScrollableComponents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetWealthBuilderLandingPageResponseValidationError{
						field:  fmt.Sprintf("ScrollableComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetWealthBuilderLandingPageResponseValidationError{
						field:  fmt.Sprintf("ScrollableComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetWealthBuilderLandingPageResponseValidationError{
					field:  fmt.Sprintf("ScrollableComponents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetWealthBuilderLandingPageResponseMultiError(errors)
	}

	return nil
}

// GetWealthBuilderLandingPageResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetWealthBuilderLandingPageResponse.ValidateAll() if the designated
// constraints aren't met.
type GetWealthBuilderLandingPageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWealthBuilderLandingPageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWealthBuilderLandingPageResponseMultiError) AllErrors() []error { return m }

// GetWealthBuilderLandingPageResponseValidationError is the validation error
// returned by GetWealthBuilderLandingPageResponse.Validate if the designated
// constraints aren't met.
type GetWealthBuilderLandingPageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWealthBuilderLandingPageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWealthBuilderLandingPageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWealthBuilderLandingPageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWealthBuilderLandingPageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWealthBuilderLandingPageResponseValidationError) ErrorName() string {
	return "GetWealthBuilderLandingPageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetWealthBuilderLandingPageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWealthBuilderLandingPageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWealthBuilderLandingPageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWealthBuilderLandingPageResponseValidationError{}

// Validate checks the field values on GetAssetImportFlowStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAssetImportFlowStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetImportFlowStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAssetImportFlowStatusRequestMultiError, or nil if none found.
func (m *GetAssetImportFlowStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetImportFlowStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAssetImportFlowStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAssetImportFlowStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAssetImportFlowStatusRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AssetType

	// no validation rules for FlowId

	if len(errors) > 0 {
		return GetAssetImportFlowStatusRequestMultiError(errors)
	}

	return nil
}

// GetAssetImportFlowStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetAssetImportFlowStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type GetAssetImportFlowStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetImportFlowStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetImportFlowStatusRequestMultiError) AllErrors() []error { return m }

// GetAssetImportFlowStatusRequestValidationError is the validation error
// returned by GetAssetImportFlowStatusRequest.Validate if the designated
// constraints aren't met.
type GetAssetImportFlowStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetImportFlowStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetImportFlowStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetImportFlowStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetImportFlowStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetImportFlowStatusRequestValidationError) ErrorName() string {
	return "GetAssetImportFlowStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetImportFlowStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetImportFlowStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetImportFlowStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetImportFlowStatusRequestValidationError{}

// Validate checks the field values on GetAssetImportFlowStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAssetImportFlowStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAssetImportFlowStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAssetImportFlowStatusResponseMultiError, or nil if none found.
func (m *GetAssetImportFlowStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAssetImportFlowStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAssetImportFlowStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAssetImportFlowStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAssetImportFlowStatusResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.StatusUpdate.(type) {
	case *GetAssetImportFlowStatusResponse_Status:
		if v == nil {
			err := GetAssetImportFlowStatusResponseValidationError{
				field:  "StatusUpdate",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStatus()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAssetImportFlowStatusResponseValidationError{
						field:  "Status",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAssetImportFlowStatusResponseValidationError{
						field:  "Status",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAssetImportFlowStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetAssetImportFlowStatusResponse_Results:
		if v == nil {
			err := GetAssetImportFlowStatusResponseValidationError{
				field:  "StatusUpdate",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetResults()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAssetImportFlowStatusResponseValidationError{
						field:  "Results",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAssetImportFlowStatusResponseValidationError{
						field:  "Results",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetResults()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAssetImportFlowStatusResponseValidationError{
					field:  "Results",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetAssetImportFlowStatusResponseMultiError(errors)
	}

	return nil
}

// GetAssetImportFlowStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetAssetImportFlowStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAssetImportFlowStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAssetImportFlowStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAssetImportFlowStatusResponseMultiError) AllErrors() []error { return m }

// GetAssetImportFlowStatusResponseValidationError is the validation error
// returned by GetAssetImportFlowStatusResponse.Validate if the designated
// constraints aren't met.
type GetAssetImportFlowStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAssetImportFlowStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAssetImportFlowStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAssetImportFlowStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAssetImportFlowStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAssetImportFlowStatusResponseValidationError) ErrorName() string {
	return "GetAssetImportFlowStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAssetImportFlowStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAssetImportFlowStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAssetImportFlowStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAssetImportFlowStatusResponseValidationError{}

// Validate checks the field values on AssetImportResultsDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AssetImportResultsDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssetImportResultsDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssetImportResultsDetailsMultiError, or nil if none found.
func (m *AssetImportResultsDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AssetImportResultsDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetImportResultsDetailsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetImportResultsDetailsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetImportResultsDetailsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Results.(type) {
	case *AssetImportResultsDetails_ImportInProgressDetails:
		if v == nil {
			err := AssetImportResultsDetailsValidationError{
				field:  "Results",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetImportInProgressDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AssetImportResultsDetailsValidationError{
						field:  "ImportInProgressDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AssetImportResultsDetailsValidationError{
						field:  "ImportInProgressDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetImportInProgressDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AssetImportResultsDetailsValidationError{
					field:  "ImportInProgressDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AssetImportResultsDetails_ImportSuccessDetails:
		if v == nil {
			err := AssetImportResultsDetailsValidationError{
				field:  "Results",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetImportSuccessDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AssetImportResultsDetailsValidationError{
						field:  "ImportSuccessDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AssetImportResultsDetailsValidationError{
						field:  "ImportSuccessDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetImportSuccessDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AssetImportResultsDetailsValidationError{
					field:  "ImportSuccessDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AssetImportResultsDetails_ImportFailureDetails:
		if v == nil {
			err := AssetImportResultsDetailsValidationError{
				field:  "Results",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetImportFailureDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AssetImportResultsDetailsValidationError{
						field:  "ImportFailureDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AssetImportResultsDetailsValidationError{
						field:  "ImportFailureDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetImportFailureDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AssetImportResultsDetailsValidationError{
					field:  "ImportFailureDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AssetImportResultsDetailsMultiError(errors)
	}

	return nil
}

// AssetImportResultsDetailsMultiError is an error wrapping multiple validation
// errors returned by AssetImportResultsDetails.ValidateAll() if the
// designated constraints aren't met.
type AssetImportResultsDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssetImportResultsDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssetImportResultsDetailsMultiError) AllErrors() []error { return m }

// AssetImportResultsDetailsValidationError is the validation error returned by
// AssetImportResultsDetails.Validate if the designated constraints aren't met.
type AssetImportResultsDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssetImportResultsDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssetImportResultsDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssetImportResultsDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssetImportResultsDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssetImportResultsDetailsValidationError) ErrorName() string {
	return "AssetImportResultsDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AssetImportResultsDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssetImportResultsDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssetImportResultsDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssetImportResultsDetailsValidationError{}

// Validate checks the field values on AssetImportSuccessDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AssetImportSuccessDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssetImportSuccessDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssetImportSuccessDetailsMultiError, or nil if none found.
func (m *AssetImportSuccessDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AssetImportSuccessDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetImportSuccessDetailsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetImportSuccessDetailsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetImportSuccessDetailsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRollingAnimationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetImportSuccessDetailsValidationError{
					field:  "RollingAnimationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetImportSuccessDetailsValidationError{
					field:  "RollingAnimationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRollingAnimationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetImportSuccessDetailsValidationError{
				field:  "RollingAnimationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAssetsUpdateLabel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetImportSuccessDetailsValidationError{
					field:  "AssetsUpdateLabel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetImportSuccessDetailsValidationError{
					field:  "AssetsUpdateLabel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAssetsUpdateLabel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetImportSuccessDetailsValidationError{
				field:  "AssetsUpdateLabel",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConnectMoreAssetsComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetImportSuccessDetailsValidationError{
					field:  "ConnectMoreAssetsComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetImportSuccessDetailsValidationError{
					field:  "ConnectMoreAssetsComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConnectMoreAssetsComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetImportSuccessDetailsValidationError{
				field:  "ConnectMoreAssetsComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExitCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetImportSuccessDetailsValidationError{
					field:  "ExitCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetImportSuccessDetailsValidationError{
					field:  "ExitCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExitCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetImportSuccessDetailsValidationError{
				field:  "ExitCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLottieDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetImportSuccessDetailsValidationError{
					field:  "LottieDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetImportSuccessDetailsValidationError{
					field:  "LottieDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLottieDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetImportSuccessDetailsValidationError{
				field:  "LottieDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetShareCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AssetImportSuccessDetailsValidationError{
					field:  "ShareCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AssetImportSuccessDetailsValidationError{
					field:  "ShareCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShareCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AssetImportSuccessDetailsValidationError{
				field:  "ShareCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AssetImportSuccessDetailsMultiError(errors)
	}

	return nil
}

// AssetImportSuccessDetailsMultiError is an error wrapping multiple validation
// errors returned by AssetImportSuccessDetails.ValidateAll() if the
// designated constraints aren't met.
type AssetImportSuccessDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssetImportSuccessDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssetImportSuccessDetailsMultiError) AllErrors() []error { return m }

// AssetImportSuccessDetailsValidationError is the validation error returned by
// AssetImportSuccessDetails.Validate if the designated constraints aren't met.
type AssetImportSuccessDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssetImportSuccessDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssetImportSuccessDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssetImportSuccessDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssetImportSuccessDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssetImportSuccessDetailsValidationError) ErrorName() string {
	return "AssetImportSuccessDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AssetImportSuccessDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssetImportSuccessDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssetImportSuccessDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssetImportSuccessDetailsValidationError{}

// Validate checks the field values on NetworthRollingAnimationDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NetworthRollingAnimationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetworthRollingAnimationDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// NetworthRollingAnimationDetailsMultiError, or nil if none found.
func (m *NetworthRollingAnimationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *NetworthRollingAnimationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUpdatedNetworth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthRollingAnimationDetailsValidationError{
					field:  "UpdatedNetworth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthRollingAnimationDetailsValidationError{
					field:  "UpdatedNetworth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedNetworth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthRollingAnimationDetailsValidationError{
				field:  "UpdatedNetworth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrencySymbol()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthRollingAnimationDetailsValidationError{
					field:  "CurrencySymbol",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthRollingAnimationDetailsValidationError{
					field:  "CurrencySymbol",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrencySymbol()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthRollingAnimationDetailsValidationError{
				field:  "CurrencySymbol",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedNetworthText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthRollingAnimationDetailsValidationError{
					field:  "UpdatedNetworthText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthRollingAnimationDetailsValidationError{
					field:  "UpdatedNetworthText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedNetworthText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthRollingAnimationDetailsValidationError{
				field:  "UpdatedNetworthText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NetworthRollingAnimationDetailsMultiError(errors)
	}

	return nil
}

// NetworthRollingAnimationDetailsMultiError is an error wrapping multiple
// validation errors returned by NetworthRollingAnimationDetails.ValidateAll()
// if the designated constraints aren't met.
type NetworthRollingAnimationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetworthRollingAnimationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetworthRollingAnimationDetailsMultiError) AllErrors() []error { return m }

// NetworthRollingAnimationDetailsValidationError is the validation error
// returned by NetworthRollingAnimationDetails.Validate if the designated
// constraints aren't met.
type NetworthRollingAnimationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetworthRollingAnimationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetworthRollingAnimationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetworthRollingAnimationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetworthRollingAnimationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetworthRollingAnimationDetailsValidationError) ErrorName() string {
	return "NetworthRollingAnimationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e NetworthRollingAnimationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetworthRollingAnimationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetworthRollingAnimationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetworthRollingAnimationDetailsValidationError{}

// Validate checks the field values on ConnectMoreAssetsComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConnectMoreAssetsComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConnectMoreAssetsComponent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConnectMoreAssetsComponentMultiError, or nil if none found.
func (m *ConnectMoreAssetsComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *ConnectMoreAssetsComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectMoreAssetsComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectMoreAssetsComponentValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectMoreAssetsComponentValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConnectMoreCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectMoreAssetsComponentValidationError{
					field:  "ConnectMoreCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectMoreAssetsComponentValidationError{
					field:  "ConnectMoreCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConnectMoreCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectMoreAssetsComponentValidationError{
				field:  "ConnectMoreCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConnectMore()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectMoreAssetsComponentValidationError{
					field:  "ConnectMore",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectMoreAssetsComponentValidationError{
					field:  "ConnectMore",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConnectMore()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectMoreAssetsComponentValidationError{
				field:  "ConnectMore",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BackgroundColor

	// no validation rules for CornerRadius

	if all {
		switch v := interface{}(m.GetProgressBarDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectMoreAssetsComponentValidationError{
					field:  "ProgressBarDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectMoreAssetsComponentValidationError{
					field:  "ProgressBarDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProgressBarDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectMoreAssetsComponentValidationError{
				field:  "ProgressBarDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConnectMoreAssetsComponentMultiError(errors)
	}

	return nil
}

// ConnectMoreAssetsComponentMultiError is an error wrapping multiple
// validation errors returned by ConnectMoreAssetsComponent.ValidateAll() if
// the designated constraints aren't met.
type ConnectMoreAssetsComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConnectMoreAssetsComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConnectMoreAssetsComponentMultiError) AllErrors() []error { return m }

// ConnectMoreAssetsComponentValidationError is the validation error returned
// by ConnectMoreAssetsComponent.Validate if the designated constraints aren't met.
type ConnectMoreAssetsComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConnectMoreAssetsComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConnectMoreAssetsComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConnectMoreAssetsComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConnectMoreAssetsComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConnectMoreAssetsComponentValidationError) ErrorName() string {
	return "ConnectMoreAssetsComponentValidationError"
}

// Error satisfies the builtin error interface
func (e ConnectMoreAssetsComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConnectMoreAssetsComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConnectMoreAssetsComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConnectMoreAssetsComponentValidationError{}

// Validate checks the field values on
// GetWealthBuilderDashboardComponentRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetWealthBuilderDashboardComponentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetWealthBuilderDashboardComponentRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetWealthBuilderDashboardComponentRequestMultiError, or nil if none found.
func (m *GetWealthBuilderDashboardComponentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWealthBuilderDashboardComponentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWealthBuilderDashboardComponentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWealthBuilderDashboardComponentRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWealthBuilderDashboardComponentRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetWealthBuilderDashboardComponentRequestMultiError(errors)
	}

	return nil
}

// GetWealthBuilderDashboardComponentRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetWealthBuilderDashboardComponentRequest.ValidateAll() if the designated
// constraints aren't met.
type GetWealthBuilderDashboardComponentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWealthBuilderDashboardComponentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWealthBuilderDashboardComponentRequestMultiError) AllErrors() []error { return m }

// GetWealthBuilderDashboardComponentRequestValidationError is the validation
// error returned by GetWealthBuilderDashboardComponentRequest.Validate if the
// designated constraints aren't met.
type GetWealthBuilderDashboardComponentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWealthBuilderDashboardComponentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWealthBuilderDashboardComponentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWealthBuilderDashboardComponentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWealthBuilderDashboardComponentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWealthBuilderDashboardComponentRequestValidationError) ErrorName() string {
	return "GetWealthBuilderDashboardComponentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetWealthBuilderDashboardComponentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWealthBuilderDashboardComponentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWealthBuilderDashboardComponentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWealthBuilderDashboardComponentRequestValidationError{}

// Validate checks the field values on
// GetWealthBuilderDashboardComponentResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetWealthBuilderDashboardComponentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetWealthBuilderDashboardComponentResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetWealthBuilderDashboardComponentResponseMultiError, or nil if none found.
func (m *GetWealthBuilderDashboardComponentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWealthBuilderDashboardComponentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWealthBuilderDashboardComponentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWealthBuilderDashboardComponentResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWealthBuilderDashboardComponentResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWealthBuilderLandingComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWealthBuilderDashboardComponentResponseValidationError{
					field:  "WealthBuilderLandingComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWealthBuilderDashboardComponentResponseValidationError{
					field:  "WealthBuilderLandingComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWealthBuilderLandingComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWealthBuilderDashboardComponentResponseValidationError{
				field:  "WealthBuilderLandingComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetWealthBuilderDashboardComponentResponseMultiError(errors)
	}

	return nil
}

// GetWealthBuilderDashboardComponentResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetWealthBuilderDashboardComponentResponse.ValidateAll() if the designated
// constraints aren't met.
type GetWealthBuilderDashboardComponentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWealthBuilderDashboardComponentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWealthBuilderDashboardComponentResponseMultiError) AllErrors() []error { return m }

// GetWealthBuilderDashboardComponentResponseValidationError is the validation
// error returned by GetWealthBuilderDashboardComponentResponse.Validate if
// the designated constraints aren't met.
type GetWealthBuilderDashboardComponentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWealthBuilderDashboardComponentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWealthBuilderDashboardComponentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWealthBuilderDashboardComponentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWealthBuilderDashboardComponentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWealthBuilderDashboardComponentResponseValidationError) ErrorName() string {
	return "GetWealthBuilderDashboardComponentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetWealthBuilderDashboardComponentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWealthBuilderDashboardComponentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWealthBuilderDashboardComponentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWealthBuilderDashboardComponentResponseValidationError{}

// Validate checks the field values on GetConnectMoreAssetsScreenRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetConnectMoreAssetsScreenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConnectMoreAssetsScreenRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetConnectMoreAssetsScreenRequestMultiError, or nil if none found.
func (m *GetConnectMoreAssetsScreenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConnectMoreAssetsScreenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectMoreAssetsScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectMoreAssetsScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectMoreAssetsScreenRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRequestParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectMoreAssetsScreenRequestValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectMoreAssetsScreenRequestValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectMoreAssetsScreenRequestValidationError{
				field:  "RequestParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetConnectMoreAssetsScreenRequestMultiError(errors)
	}

	return nil
}

// GetConnectMoreAssetsScreenRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetConnectMoreAssetsScreenRequest.ValidateAll() if the designated
// constraints aren't met.
type GetConnectMoreAssetsScreenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConnectMoreAssetsScreenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConnectMoreAssetsScreenRequestMultiError) AllErrors() []error { return m }

// GetConnectMoreAssetsScreenRequestValidationError is the validation error
// returned by GetConnectMoreAssetsScreenRequest.Validate if the designated
// constraints aren't met.
type GetConnectMoreAssetsScreenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConnectMoreAssetsScreenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConnectMoreAssetsScreenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConnectMoreAssetsScreenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConnectMoreAssetsScreenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConnectMoreAssetsScreenRequestValidationError) ErrorName() string {
	return "GetConnectMoreAssetsScreenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetConnectMoreAssetsScreenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConnectMoreAssetsScreenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConnectMoreAssetsScreenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConnectMoreAssetsScreenRequestValidationError{}

// Validate checks the field values on GetConnectMoreAssetsScreenResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetConnectMoreAssetsScreenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConnectMoreAssetsScreenResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetConnectMoreAssetsScreenResponseMultiError, or nil if none found.
func (m *GetConnectMoreAssetsScreenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConnectMoreAssetsScreenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectMoreAssetsScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectMoreAssetsScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectMoreAssetsScreenResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectMoreAssetsScreenResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectMoreAssetsScreenResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectMoreAssetsScreenResponseValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectMoreAssetsScreenResponseValidationError{
					field:  "CardImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectMoreAssetsScreenResponseValidationError{
					field:  "CardImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectMoreAssetsScreenResponseValidationError{
				field:  "CardImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetWealthBuilderLandingSections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetConnectMoreAssetsScreenResponseValidationError{
						field:  fmt.Sprintf("WealthBuilderLandingSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetConnectMoreAssetsScreenResponseValidationError{
						field:  fmt.Sprintf("WealthBuilderLandingSections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetConnectMoreAssetsScreenResponseValidationError{
					field:  fmt.Sprintf("WealthBuilderLandingSections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetConnectMoreAssetsScreenResponseMultiError(errors)
	}

	return nil
}

// GetConnectMoreAssetsScreenResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetConnectMoreAssetsScreenResponse.ValidateAll() if the designated
// constraints aren't met.
type GetConnectMoreAssetsScreenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConnectMoreAssetsScreenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConnectMoreAssetsScreenResponseMultiError) AllErrors() []error { return m }

// GetConnectMoreAssetsScreenResponseValidationError is the validation error
// returned by GetConnectMoreAssetsScreenResponse.Validate if the designated
// constraints aren't met.
type GetConnectMoreAssetsScreenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConnectMoreAssetsScreenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConnectMoreAssetsScreenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConnectMoreAssetsScreenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConnectMoreAssetsScreenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConnectMoreAssetsScreenResponseValidationError) ErrorName() string {
	return "GetConnectMoreAssetsScreenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetConnectMoreAssetsScreenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConnectMoreAssetsScreenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConnectMoreAssetsScreenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConnectMoreAssetsScreenResponseValidationError{}

// Validate checks the field values on ConnectMoreAssetsScreenRequestParams
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ConnectMoreAssetsScreenRequestParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConnectMoreAssetsScreenRequestParams
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ConnectMoreAssetsScreenRequestParamsMultiError, or nil if none found.
func (m *ConnectMoreAssetsScreenRequestParams) ValidateAll() error {
	return m.validate(true)
}

func (m *ConnectMoreAssetsScreenRequestParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ConnectMoreAssetsScreenRequestParamsMultiError(errors)
	}

	return nil
}

// ConnectMoreAssetsScreenRequestParamsMultiError is an error wrapping multiple
// validation errors returned by
// ConnectMoreAssetsScreenRequestParams.ValidateAll() if the designated
// constraints aren't met.
type ConnectMoreAssetsScreenRequestParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConnectMoreAssetsScreenRequestParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConnectMoreAssetsScreenRequestParamsMultiError) AllErrors() []error { return m }

// ConnectMoreAssetsScreenRequestParamsValidationError is the validation error
// returned by ConnectMoreAssetsScreenRequestParams.Validate if the designated
// constraints aren't met.
type ConnectMoreAssetsScreenRequestParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConnectMoreAssetsScreenRequestParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConnectMoreAssetsScreenRequestParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConnectMoreAssetsScreenRequestParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConnectMoreAssetsScreenRequestParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConnectMoreAssetsScreenRequestParamsValidationError) ErrorName() string {
	return "ConnectMoreAssetsScreenRequestParamsValidationError"
}

// Error satisfies the builtin error interface
func (e ConnectMoreAssetsScreenRequestParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConnectMoreAssetsScreenRequestParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConnectMoreAssetsScreenRequestParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConnectMoreAssetsScreenRequestParamsValidationError{}

// Validate checks the field values on SubmitManualFormsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitManualFormsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitManualFormsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitManualFormsRequestMultiError, or nil if none found.
func (m *SubmitManualFormsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitManualFormsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitManualFormsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitManualFormsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitManualFormsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFormSubmissionData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SubmitManualFormsRequestValidationError{
						field:  fmt.Sprintf("FormSubmissionData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SubmitManualFormsRequestValidationError{
						field:  fmt.Sprintf("FormSubmissionData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SubmitManualFormsRequestValidationError{
					field:  fmt.Sprintf("FormSubmissionData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SubmitManualFormsRequestMultiError(errors)
	}

	return nil
}

// SubmitManualFormsRequestMultiError is an error wrapping multiple validation
// errors returned by SubmitManualFormsRequest.ValidateAll() if the designated
// constraints aren't met.
type SubmitManualFormsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitManualFormsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitManualFormsRequestMultiError) AllErrors() []error { return m }

// SubmitManualFormsRequestValidationError is the validation error returned by
// SubmitManualFormsRequest.Validate if the designated constraints aren't met.
type SubmitManualFormsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitManualFormsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitManualFormsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitManualFormsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitManualFormsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitManualFormsRequestValidationError) ErrorName() string {
	return "SubmitManualFormsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitManualFormsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitManualFormsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitManualFormsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitManualFormsRequestValidationError{}

// Validate checks the field values on SubmitManualFormsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitManualFormsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitManualFormsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitManualFormsResponseMultiError, or nil if none found.
func (m *SubmitManualFormsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitManualFormsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitManualFormsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitManualFormsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitManualFormsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExitDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitManualFormsResponseValidationError{
					field:  "ExitDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitManualFormsResponseValidationError{
					field:  "ExitDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExitDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitManualFormsResponseValidationError{
				field:  "ExitDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubmitManualFormsResponseMultiError(errors)
	}

	return nil
}

// SubmitManualFormsResponseMultiError is an error wrapping multiple validation
// errors returned by SubmitManualFormsResponse.ValidateAll() if the
// designated constraints aren't met.
type SubmitManualFormsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitManualFormsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitManualFormsResponseMultiError) AllErrors() []error { return m }

// SubmitManualFormsResponseValidationError is the validation error returned by
// SubmitManualFormsResponse.Validate if the designated constraints aren't met.
type SubmitManualFormsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitManualFormsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitManualFormsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitManualFormsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitManualFormsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitManualFormsResponseValidationError) ErrorName() string {
	return "SubmitManualFormsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitManualFormsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitManualFormsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitManualFormsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitManualFormsResponseValidationError{}

// Validate checks the field values on GetNetworthDataFileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNetworthDataFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNetworthDataFileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNetworthDataFileRequestMultiError, or nil if none found.
func (m *GetNetworthDataFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNetworthDataFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetworthDataFileRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetworthDataFileRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetworthDataFileRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNetworthDataFileRequestMultiError(errors)
	}

	return nil
}

// GetNetworthDataFileRequestMultiError is an error wrapping multiple
// validation errors returned by GetNetworthDataFileRequest.ValidateAll() if
// the designated constraints aren't met.
type GetNetworthDataFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNetworthDataFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNetworthDataFileRequestMultiError) AllErrors() []error { return m }

// GetNetworthDataFileRequestValidationError is the validation error returned
// by GetNetworthDataFileRequest.Validate if the designated constraints aren't met.
type GetNetworthDataFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNetworthDataFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNetworthDataFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNetworthDataFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNetworthDataFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNetworthDataFileRequestValidationError) ErrorName() string {
	return "GetNetworthDataFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNetworthDataFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNetworthDataFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNetworthDataFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNetworthDataFileRequestValidationError{}

// Validate checks the field values on GetNetworthDataFileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNetworthDataFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNetworthDataFileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNetworthDataFileResponseMultiError, or nil if none found.
func (m *GetNetworthDataFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNetworthDataFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetworthDataFileResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetworthDataFileResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetworthDataFileResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNetworthFile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNetworthDataFileResponseValidationError{
					field:  "NetworthFile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNetworthDataFileResponseValidationError{
					field:  "NetworthFile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetworthFile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNetworthDataFileResponseValidationError{
				field:  "NetworthFile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNetworthDataFileResponseMultiError(errors)
	}

	return nil
}

// GetNetworthDataFileResponseMultiError is an error wrapping multiple
// validation errors returned by GetNetworthDataFileResponse.ValidateAll() if
// the designated constraints aren't met.
type GetNetworthDataFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNetworthDataFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNetworthDataFileResponseMultiError) AllErrors() []error { return m }

// GetNetworthDataFileResponseValidationError is the validation error returned
// by GetNetworthDataFileResponse.Validate if the designated constraints
// aren't met.
type GetNetworthDataFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNetworthDataFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNetworthDataFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNetworthDataFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNetworthDataFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNetworthDataFileResponseValidationError) ErrorName() string {
	return "GetNetworthDataFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNetworthDataFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNetworthDataFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNetworthDataFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNetworthDataFileResponseValidationError{}

// Validate checks the field values on Section_SectionHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Section_SectionHeader) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Section_SectionHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Section_SectionHeaderMultiError, or nil if none found.
func (m *Section_SectionHeader) ValidateAll() error {
	return m.validate(true)
}

func (m *Section_SectionHeader) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Section_SectionHeaderValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Section_SectionHeaderValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Section_SectionHeaderValidationError{
				field:  "Summary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddMoreButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, Section_SectionHeaderValidationError{
					field:  "AddMoreButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, Section_SectionHeaderValidationError{
					field:  "AddMoreButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddMoreButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return Section_SectionHeaderValidationError{
				field:  "AddMoreButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return Section_SectionHeaderMultiError(errors)
	}

	return nil
}

// Section_SectionHeaderMultiError is an error wrapping multiple validation
// errors returned by Section_SectionHeader.ValidateAll() if the designated
// constraints aren't met.
type Section_SectionHeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Section_SectionHeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Section_SectionHeaderMultiError) AllErrors() []error { return m }

// Section_SectionHeaderValidationError is the validation error returned by
// Section_SectionHeader.Validate if the designated constraints aren't met.
type Section_SectionHeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Section_SectionHeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Section_SectionHeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Section_SectionHeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Section_SectionHeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Section_SectionHeaderValidationError) ErrorName() string {
	return "Section_SectionHeaderValidationError"
}

// Error satisfies the builtin error interface
func (e Section_SectionHeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSection_SectionHeader.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Section_SectionHeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Section_SectionHeaderValidationError{}

// Validate checks the field values on
// SearchAssetFormFieldOptionsResponse_ErrorView with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchAssetFormFieldOptionsResponse_ErrorView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SearchAssetFormFieldOptionsResponse_ErrorView with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// SearchAssetFormFieldOptionsResponse_ErrorViewMultiError, or nil if none found.
func (m *SearchAssetFormFieldOptionsResponse_ErrorView) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchAssetFormFieldOptionsResponse_ErrorView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchAssetFormFieldOptionsResponse_ErrorViewValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchAssetFormFieldOptionsResponse_ErrorViewValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchAssetFormFieldOptionsResponse_ErrorViewValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetKeyValuePair()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchAssetFormFieldOptionsResponse_ErrorViewValidationError{
					field:  "KeyValuePair",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchAssetFormFieldOptionsResponse_ErrorViewValidationError{
					field:  "KeyValuePair",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKeyValuePair()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchAssetFormFieldOptionsResponse_ErrorViewValidationError{
				field:  "KeyValuePair",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInfoRedirection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchAssetFormFieldOptionsResponse_ErrorViewValidationError{
					field:  "InfoRedirection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchAssetFormFieldOptionsResponse_ErrorViewValidationError{
					field:  "InfoRedirection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoRedirection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchAssetFormFieldOptionsResponse_ErrorViewValidationError{
				field:  "InfoRedirection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SearchAssetFormFieldOptionsResponse_ErrorViewMultiError(errors)
	}

	return nil
}

// SearchAssetFormFieldOptionsResponse_ErrorViewMultiError is an error wrapping
// multiple validation errors returned by
// SearchAssetFormFieldOptionsResponse_ErrorView.ValidateAll() if the
// designated constraints aren't met.
type SearchAssetFormFieldOptionsResponse_ErrorViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchAssetFormFieldOptionsResponse_ErrorViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchAssetFormFieldOptionsResponse_ErrorViewMultiError) AllErrors() []error { return m }

// SearchAssetFormFieldOptionsResponse_ErrorViewValidationError is the
// validation error returned by
// SearchAssetFormFieldOptionsResponse_ErrorView.Validate if the designated
// constraints aren't met.
type SearchAssetFormFieldOptionsResponse_ErrorViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchAssetFormFieldOptionsResponse_ErrorViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchAssetFormFieldOptionsResponse_ErrorViewValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e SearchAssetFormFieldOptionsResponse_ErrorViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchAssetFormFieldOptionsResponse_ErrorViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchAssetFormFieldOptionsResponse_ErrorViewValidationError) ErrorName() string {
	return "SearchAssetFormFieldOptionsResponse_ErrorViewValidationError"
}

// Error satisfies the builtin error interface
func (e SearchAssetFormFieldOptionsResponse_ErrorViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchAssetFormFieldOptionsResponse_ErrorView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchAssetFormFieldOptionsResponse_ErrorViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchAssetFormFieldOptionsResponse_ErrorViewValidationError{}

// Validate checks the field values on
// ConnectMoreAssetsComponent_ProgressBarDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ConnectMoreAssetsComponent_ProgressBarDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ConnectMoreAssetsComponent_ProgressBarDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ConnectMoreAssetsComponent_ProgressBarDetailsMultiError, or nil if none found.
func (m *ConnectMoreAssetsComponent_ProgressBarDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ConnectMoreAssetsComponent_ProgressBarDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompletedPercentage

	// no validation rules for ForegroundColor

	// no validation rules for BackgroundColor

	if len(errors) > 0 {
		return ConnectMoreAssetsComponent_ProgressBarDetailsMultiError(errors)
	}

	return nil
}

// ConnectMoreAssetsComponent_ProgressBarDetailsMultiError is an error wrapping
// multiple validation errors returned by
// ConnectMoreAssetsComponent_ProgressBarDetails.ValidateAll() if the
// designated constraints aren't met.
type ConnectMoreAssetsComponent_ProgressBarDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConnectMoreAssetsComponent_ProgressBarDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConnectMoreAssetsComponent_ProgressBarDetailsMultiError) AllErrors() []error { return m }

// ConnectMoreAssetsComponent_ProgressBarDetailsValidationError is the
// validation error returned by
// ConnectMoreAssetsComponent_ProgressBarDetails.Validate if the designated
// constraints aren't met.
type ConnectMoreAssetsComponent_ProgressBarDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConnectMoreAssetsComponent_ProgressBarDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConnectMoreAssetsComponent_ProgressBarDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ConnectMoreAssetsComponent_ProgressBarDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConnectMoreAssetsComponent_ProgressBarDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConnectMoreAssetsComponent_ProgressBarDetailsValidationError) ErrorName() string {
	return "ConnectMoreAssetsComponent_ProgressBarDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ConnectMoreAssetsComponent_ProgressBarDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConnectMoreAssetsComponent_ProgressBarDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConnectMoreAssetsComponent_ProgressBarDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConnectMoreAssetsComponent_ProgressBarDetailsValidationError{}

// Validate checks the field values on
// SubmitManualFormsRequest_FormSubmissionData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SubmitManualFormsRequest_FormSubmissionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SubmitManualFormsRequest_FormSubmissionData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// SubmitManualFormsRequest_FormSubmissionDataMultiError, or nil if none found.
func (m *SubmitManualFormsRequest_FormSubmissionData) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitManualFormsRequest_FormSubmissionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFormIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitManualFormsRequest_FormSubmissionDataValidationError{
					field:  "FormIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitManualFormsRequest_FormSubmissionDataValidationError{
					field:  "FormIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFormIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitManualFormsRequest_FormSubmissionDataValidationError{
				field:  "FormIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFormData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SubmitManualFormsRequest_FormSubmissionDataValidationError{
						field:  fmt.Sprintf("FormData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SubmitManualFormsRequest_FormSubmissionDataValidationError{
						field:  fmt.Sprintf("FormData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SubmitManualFormsRequest_FormSubmissionDataValidationError{
					field:  fmt.Sprintf("FormData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SubmitManualFormsRequest_FormSubmissionDataMultiError(errors)
	}

	return nil
}

// SubmitManualFormsRequest_FormSubmissionDataMultiError is an error wrapping
// multiple validation errors returned by
// SubmitManualFormsRequest_FormSubmissionData.ValidateAll() if the designated
// constraints aren't met.
type SubmitManualFormsRequest_FormSubmissionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitManualFormsRequest_FormSubmissionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitManualFormsRequest_FormSubmissionDataMultiError) AllErrors() []error { return m }

// SubmitManualFormsRequest_FormSubmissionDataValidationError is the validation
// error returned by SubmitManualFormsRequest_FormSubmissionData.Validate if
// the designated constraints aren't met.
type SubmitManualFormsRequest_FormSubmissionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitManualFormsRequest_FormSubmissionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitManualFormsRequest_FormSubmissionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitManualFormsRequest_FormSubmissionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitManualFormsRequest_FormSubmissionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitManualFormsRequest_FormSubmissionDataValidationError) ErrorName() string {
	return "SubmitManualFormsRequest_FormSubmissionDataValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitManualFormsRequest_FormSubmissionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitManualFormsRequest_FormSubmissionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitManualFormsRequest_FormSubmissionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitManualFormsRequest_FormSubmissionDataValidationError{}
