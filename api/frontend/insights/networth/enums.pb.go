// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/insights/networth/internal/enums.proto

package networth

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NetworthCategory int32

const (
	NetworthCategory_NETWORTH_CATEGORY_UNSPECIFIED                       NetworthCategory = 0
	NetworthCategory_NETWORTH_CATEGORY_ASSET_SAVINGS_ACCOUNTS            NetworthCategory = 1
	NetworthCategory_NETWORTH_CATEGORY_ASSET_MUTUAL_FUNDS                NetworthCategory = 2
	NetworthCategory_NETWORTH_CATEGORY_ASSET_EPF                         NetworthCategory = 3
	NetworthCategory_NETWORTH_CATEGORY_ASSET_DEPOSITS                    NetworthCategory = 4
	NetworthCategory_NETWORTH_CATEGORY_ASSET_INDIAN_SECURITIES           NetworthCategory = 5
	NetworthCategory_NETWORTH_CATEGORY_ASSET_US_SECURITIES               NetworthCategory = 6
	NetworthCategory_NETWORTH_CATEGORY_ASSET_JUMP                        NetworthCategory = 7
	NetworthCategory_NETWORTH_CATEGORY_ASSET_FI_SD                       NetworthCategory = 8
	NetworthCategory_NETWORTH_CATEGORY_ASSET_FI_FD                       NetworthCategory = 9
	NetworthCategory_NETWORTH_CATEGORY_LIABILITY_CREDIT_CARD_OUTSTANDING NetworthCategory = 10
	NetworthCategory_NETWORTH_CATEGORY_LIABILITY_HOME_LOAN               NetworthCategory = 11
	NetworthCategory_NETWORTH_CATEGORY_LIABILITY_PERSONAL_LOAN           NetworthCategory = 12
	NetworthCategory_NETWORTH_CATEGORY_LIABILITY_VEHICLE_LOAN            NetworthCategory = 13
	NetworthCategory_NETWORTH_CATEGORY_LIABILITY_EDUCATION_LOAN          NetworthCategory = 14
	NetworthCategory_NETWORTH_CATEGORY_LIABILITY_OTHER_LOANS             NetworthCategory = 15
	NetworthCategory_NETWORTH_CATEGORY_ASSET_NPS                         NetworthCategory = 16
	NetworthCategory_NETWORTH_CATEGORY_AIF                               NetworthCategory = 17
	NetworthCategory_NETWORTH_CATEGORY_ART_ARTEFACTS                     NetworthCategory = 18
	NetworthCategory_NETWORTH_CATEGORY_BONDS                             NetworthCategory = 19
	NetworthCategory_NETWORTH_CATEGORY_CASH                              NetworthCategory = 20
	NetworthCategory_NETWORTH_CATEGORY_DIGITAL_GOLD                      NetworthCategory = 21
	NetworthCategory_NETWORTH_CATEGORY_DIGITAL_SILVER                    NetworthCategory = 22
	NetworthCategory_NETWORTH_CATEGORY_PRIVATE_EQUITY                    NetworthCategory = 23
	NetworthCategory_NETWORTH_CATEGORY_REAL_ESTATE                       NetworthCategory = 24
	NetworthCategory_NETWORTH_CATEGORY_PORTFOLIO_MANAGEMENT_SERVICE      NetworthCategory = 25
	NetworthCategory_NETWORTH_CATEGORY_PUBLIC_PROVIDENT_FUND             NetworthCategory = 26
	NetworthCategory_NETWORTH_CATEGORY_EMPLOYEE_STOCK_OPTION             NetworthCategory = 27
)

// Enum value maps for NetworthCategory.
var (
	NetworthCategory_name = map[int32]string{
		0:  "NETWORTH_CATEGORY_UNSPECIFIED",
		1:  "NETWORTH_CATEGORY_ASSET_SAVINGS_ACCOUNTS",
		2:  "NETWORTH_CATEGORY_ASSET_MUTUAL_FUNDS",
		3:  "NETWORTH_CATEGORY_ASSET_EPF",
		4:  "NETWORTH_CATEGORY_ASSET_DEPOSITS",
		5:  "NETWORTH_CATEGORY_ASSET_INDIAN_SECURITIES",
		6:  "NETWORTH_CATEGORY_ASSET_US_SECURITIES",
		7:  "NETWORTH_CATEGORY_ASSET_JUMP",
		8:  "NETWORTH_CATEGORY_ASSET_FI_SD",
		9:  "NETWORTH_CATEGORY_ASSET_FI_FD",
		10: "NETWORTH_CATEGORY_LIABILITY_CREDIT_CARD_OUTSTANDING",
		11: "NETWORTH_CATEGORY_LIABILITY_HOME_LOAN",
		12: "NETWORTH_CATEGORY_LIABILITY_PERSONAL_LOAN",
		13: "NETWORTH_CATEGORY_LIABILITY_VEHICLE_LOAN",
		14: "NETWORTH_CATEGORY_LIABILITY_EDUCATION_LOAN",
		15: "NETWORTH_CATEGORY_LIABILITY_OTHER_LOANS",
		16: "NETWORTH_CATEGORY_ASSET_NPS",
		17: "NETWORTH_CATEGORY_AIF",
		18: "NETWORTH_CATEGORY_ART_ARTEFACTS",
		19: "NETWORTH_CATEGORY_BONDS",
		20: "NETWORTH_CATEGORY_CASH",
		21: "NETWORTH_CATEGORY_DIGITAL_GOLD",
		22: "NETWORTH_CATEGORY_DIGITAL_SILVER",
		23: "NETWORTH_CATEGORY_PRIVATE_EQUITY",
		24: "NETWORTH_CATEGORY_REAL_ESTATE",
		25: "NETWORTH_CATEGORY_PORTFOLIO_MANAGEMENT_SERVICE",
		26: "NETWORTH_CATEGORY_PUBLIC_PROVIDENT_FUND",
		27: "NETWORTH_CATEGORY_EMPLOYEE_STOCK_OPTION",
	}
	NetworthCategory_value = map[string]int32{
		"NETWORTH_CATEGORY_UNSPECIFIED":                       0,
		"NETWORTH_CATEGORY_ASSET_SAVINGS_ACCOUNTS":            1,
		"NETWORTH_CATEGORY_ASSET_MUTUAL_FUNDS":                2,
		"NETWORTH_CATEGORY_ASSET_EPF":                         3,
		"NETWORTH_CATEGORY_ASSET_DEPOSITS":                    4,
		"NETWORTH_CATEGORY_ASSET_INDIAN_SECURITIES":           5,
		"NETWORTH_CATEGORY_ASSET_US_SECURITIES":               6,
		"NETWORTH_CATEGORY_ASSET_JUMP":                        7,
		"NETWORTH_CATEGORY_ASSET_FI_SD":                       8,
		"NETWORTH_CATEGORY_ASSET_FI_FD":                       9,
		"NETWORTH_CATEGORY_LIABILITY_CREDIT_CARD_OUTSTANDING": 10,
		"NETWORTH_CATEGORY_LIABILITY_HOME_LOAN":               11,
		"NETWORTH_CATEGORY_LIABILITY_PERSONAL_LOAN":           12,
		"NETWORTH_CATEGORY_LIABILITY_VEHICLE_LOAN":            13,
		"NETWORTH_CATEGORY_LIABILITY_EDUCATION_LOAN":          14,
		"NETWORTH_CATEGORY_LIABILITY_OTHER_LOANS":             15,
		"NETWORTH_CATEGORY_ASSET_NPS":                         16,
		"NETWORTH_CATEGORY_AIF":                               17,
		"NETWORTH_CATEGORY_ART_ARTEFACTS":                     18,
		"NETWORTH_CATEGORY_BONDS":                             19,
		"NETWORTH_CATEGORY_CASH":                              20,
		"NETWORTH_CATEGORY_DIGITAL_GOLD":                      21,
		"NETWORTH_CATEGORY_DIGITAL_SILVER":                    22,
		"NETWORTH_CATEGORY_PRIVATE_EQUITY":                    23,
		"NETWORTH_CATEGORY_REAL_ESTATE":                       24,
		"NETWORTH_CATEGORY_PORTFOLIO_MANAGEMENT_SERVICE":      25,
		"NETWORTH_CATEGORY_PUBLIC_PROVIDENT_FUND":             26,
		"NETWORTH_CATEGORY_EMPLOYEE_STOCK_OPTION":             27,
	}
)

func (x NetworthCategory) Enum() *NetworthCategory {
	p := new(NetworthCategory)
	*p = x
	return p
}

func (x NetworthCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetworthCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_networth_internal_enums_proto_enumTypes[0].Descriptor()
}

func (NetworthCategory) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_networth_internal_enums_proto_enumTypes[0]
}

func (x NetworthCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetworthCategory.Descriptor instead.
func (NetworthCategory) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_internal_enums_proto_rawDescGZIP(), []int{0}
}

type NetworthSectionType int32

const (
	NetworthSectionType_NETWORTH_SECTION_TYPE_UNSPECIFIED NetworthSectionType = 0
	NetworthSectionType_NETWORTH_SECTION_TYPE_ASSETS      NetworthSectionType = 1
	NetworthSectionType_NETWORTH_SECTION_TYPE_LIABILITIES NetworthSectionType = 2
)

// Enum value maps for NetworthSectionType.
var (
	NetworthSectionType_name = map[int32]string{
		0: "NETWORTH_SECTION_TYPE_UNSPECIFIED",
		1: "NETWORTH_SECTION_TYPE_ASSETS",
		2: "NETWORTH_SECTION_TYPE_LIABILITIES",
	}
	NetworthSectionType_value = map[string]int32{
		"NETWORTH_SECTION_TYPE_UNSPECIFIED": 0,
		"NETWORTH_SECTION_TYPE_ASSETS":      1,
		"NETWORTH_SECTION_TYPE_LIABILITIES": 2,
	}
)

func (x NetworthSectionType) Enum() *NetworthSectionType {
	p := new(NetworthSectionType)
	*p = x
	return p
}

func (x NetworthSectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetworthSectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_networth_internal_enums_proto_enumTypes[1].Descriptor()
}

func (NetworthSectionType) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_networth_internal_enums_proto_enumTypes[1]
}

func (x NetworthSectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetworthSectionType.Descriptor instead.
func (NetworthSectionType) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_internal_enums_proto_rawDescGZIP(), []int{1}
}

type NetworthCategoryStatus int32

const (
	NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_UNSPECIFIED NetworthCategoryStatus = 0
	// This refers to the state when a user has not successfully completed the initialisation of category.
	// eg. A user has not completed the credit report import flow successfully.
	NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_UNINITIALIZED NetworthCategoryStatus = 1
	// This refers to the state when a user has successfully completed the initialisation of category, but the category value does not exist for them.
	// eg. User has fetched credit report but has not taken a loan, so the loan category is not applicable for the user.
	NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_NOT_APPLICABLE NetworthCategoryStatus = 2
	// This refers to the state when a user has successfully completed the initialisation of category and the category value also exists for them.
	NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_INITIALIZED NetworthCategoryStatus = 3
	// This refers to the state when the category value fetch failed for a given request.
	NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_VALUE_FETCH_FAILED NetworthCategoryStatus = 4
	// This refers to the state when the computation of category value is in process
	NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_IN_PROCESS NetworthCategoryStatus = 5
)

// Enum value maps for NetworthCategoryStatus.
var (
	NetworthCategoryStatus_name = map[int32]string{
		0: "NETWORTH_CATEGORY_STATUS_UNSPECIFIED",
		1: "NETWORTH_CATEGORY_STATUS_UNINITIALIZED",
		2: "NETWORTH_CATEGORY_STATUS_NOT_APPLICABLE",
		3: "NETWORTH_CATEGORY_STATUS_INITIALIZED",
		4: "NETWORTH_CATEGORY_STATUS_VALUE_FETCH_FAILED",
		5: "NETWORTH_CATEGORY_STATUS_IN_PROCESS",
	}
	NetworthCategoryStatus_value = map[string]int32{
		"NETWORTH_CATEGORY_STATUS_UNSPECIFIED":        0,
		"NETWORTH_CATEGORY_STATUS_UNINITIALIZED":      1,
		"NETWORTH_CATEGORY_STATUS_NOT_APPLICABLE":     2,
		"NETWORTH_CATEGORY_STATUS_INITIALIZED":        3,
		"NETWORTH_CATEGORY_STATUS_VALUE_FETCH_FAILED": 4,
		"NETWORTH_CATEGORY_STATUS_IN_PROCESS":         5,
	}
)

func (x NetworthCategoryStatus) Enum() *NetworthCategoryStatus {
	p := new(NetworthCategoryStatus)
	*p = x
	return p
}

func (x NetworthCategoryStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetworthCategoryStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_networth_internal_enums_proto_enumTypes[2].Descriptor()
}

func (NetworthCategoryStatus) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_networth_internal_enums_proto_enumTypes[2]
}

func (x NetworthCategoryStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetworthCategoryStatus.Descriptor instead.
func (NetworthCategoryStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_internal_enums_proto_rawDescGZIP(), []int{2}
}

type NetworthWidgetState int32

const (
	NetworthWidgetState_NETWORTH_WIDGET_STATE_UNSPECIFIED NetworthWidgetState = 0
	// Widget will have interactions enabled
	NetworthWidgetState_NETWORTH_WIDGET_STATE_ACTIVE NetworthWidgetState = 1
	// Widget is not supported yet. It will come in future.
	NetworthWidgetState_NETWORTH_WIDGET_STATE_COMING_SOON NetworthWidgetState = 2
)

// Enum value maps for NetworthWidgetState.
var (
	NetworthWidgetState_name = map[int32]string{
		0: "NETWORTH_WIDGET_STATE_UNSPECIFIED",
		1: "NETWORTH_WIDGET_STATE_ACTIVE",
		2: "NETWORTH_WIDGET_STATE_COMING_SOON",
	}
	NetworthWidgetState_value = map[string]int32{
		"NETWORTH_WIDGET_STATE_UNSPECIFIED": 0,
		"NETWORTH_WIDGET_STATE_ACTIVE":      1,
		"NETWORTH_WIDGET_STATE_COMING_SOON": 2,
	}
)

func (x NetworthWidgetState) Enum() *NetworthWidgetState {
	p := new(NetworthWidgetState)
	*p = x
	return p
}

func (x NetworthWidgetState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetworthWidgetState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_networth_internal_enums_proto_enumTypes[3].Descriptor()
}

func (NetworthWidgetState) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_networth_internal_enums_proto_enumTypes[3]
}

func (x NetworthWidgetState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetworthWidgetState.Descriptor instead.
func (NetworthWidgetState) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_internal_enums_proto_rawDescGZIP(), []int{3}
}

// NetworthManualFormFieldName denotes different field names
// in networth manual forms
type NetworthManualFormFieldName int32

const (
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_UNSPECIFIED          NetworthManualFormFieldName = 0
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_NAME      NetworthManualFormFieldName = 1
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTED_VALUE       NetworthManualFormFieldName = 2
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_CURRENT_VALUE        NetworthManualFormFieldName = 3
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_DATE      NetworthManualFormFieldName = 4
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_EVALUATION_DATE      NetworthManualFormFieldName = 5
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_AMC_NAME             NetworthManualFormFieldName = 6
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_CATEGORY             NetworthManualFormFieldName = 7
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_FOLIO_ID             NetworthManualFormFieldName = 8
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_BROKER_CODE          NetworthManualFormFieldName = 9
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_REMARKS              NetworthManualFormFieldName = 10
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_MATURITY_DATE        NetworthManualFormFieldName = 11
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_NUM_UNITS            NetworthManualFormFieldName = 12
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_QUANTITY_GRAMS       NetworthManualFormFieldName = 13
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_GOLD_TYPE            NetworthManualFormFieldName = 14
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_FREQUENCY NetworthManualFormFieldName = 15
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_CURRENCY_TYPE        NetworthManualFormFieldName = 16
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_VESTING_SCHEDULE     NetworthManualFormFieldName = 17
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INTEREST_RATE        NetworthManualFormFieldName = 18
	NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_NAME_DEPOSIT_TYPE    NetworthManualFormFieldName = 19
)

// Enum value maps for NetworthManualFormFieldName.
var (
	NetworthManualFormFieldName_name = map[int32]string{
		0:  "NETWORTH_MANUAL_FORM_FIELD_UNSPECIFIED",
		1:  "NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_NAME",
		2:  "NETWORTH_MANUAL_FORM_FIELD_INVESTED_VALUE",
		3:  "NETWORTH_MANUAL_FORM_FIELD_CURRENT_VALUE",
		4:  "NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_DATE",
		5:  "NETWORTH_MANUAL_FORM_FIELD_EVALUATION_DATE",
		6:  "NETWORTH_MANUAL_FORM_FIELD_AMC_NAME",
		7:  "NETWORTH_MANUAL_FORM_FIELD_CATEGORY",
		8:  "NETWORTH_MANUAL_FORM_FIELD_FOLIO_ID",
		9:  "NETWORTH_MANUAL_FORM_FIELD_BROKER_CODE",
		10: "NETWORTH_MANUAL_FORM_FIELD_REMARKS",
		11: "NETWORTH_MANUAL_FORM_FIELD_MATURITY_DATE",
		12: "NETWORTH_MANUAL_FORM_FIELD_NUM_UNITS",
		13: "NETWORTH_MANUAL_FORM_FIELD_QUANTITY_GRAMS",
		14: "NETWORTH_MANUAL_FORM_FIELD_GOLD_TYPE",
		15: "NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_FREQUENCY",
		16: "NETWORTH_MANUAL_FORM_FIELD_CURRENCY_TYPE",
		17: "NETWORTH_MANUAL_FORM_FIELD_VESTING_SCHEDULE",
		18: "NETWORTH_MANUAL_FORM_FIELD_INTEREST_RATE",
		19: "NETWORTH_MANUAL_FORM_FIELD_NAME_DEPOSIT_TYPE",
	}
	NetworthManualFormFieldName_value = map[string]int32{
		"NETWORTH_MANUAL_FORM_FIELD_UNSPECIFIED":          0,
		"NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_NAME":      1,
		"NETWORTH_MANUAL_FORM_FIELD_INVESTED_VALUE":       2,
		"NETWORTH_MANUAL_FORM_FIELD_CURRENT_VALUE":        3,
		"NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_DATE":      4,
		"NETWORTH_MANUAL_FORM_FIELD_EVALUATION_DATE":      5,
		"NETWORTH_MANUAL_FORM_FIELD_AMC_NAME":             6,
		"NETWORTH_MANUAL_FORM_FIELD_CATEGORY":             7,
		"NETWORTH_MANUAL_FORM_FIELD_FOLIO_ID":             8,
		"NETWORTH_MANUAL_FORM_FIELD_BROKER_CODE":          9,
		"NETWORTH_MANUAL_FORM_FIELD_REMARKS":              10,
		"NETWORTH_MANUAL_FORM_FIELD_MATURITY_DATE":        11,
		"NETWORTH_MANUAL_FORM_FIELD_NUM_UNITS":            12,
		"NETWORTH_MANUAL_FORM_FIELD_QUANTITY_GRAMS":       13,
		"NETWORTH_MANUAL_FORM_FIELD_GOLD_TYPE":            14,
		"NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_FREQUENCY": 15,
		"NETWORTH_MANUAL_FORM_FIELD_CURRENCY_TYPE":        16,
		"NETWORTH_MANUAL_FORM_FIELD_VESTING_SCHEDULE":     17,
		"NETWORTH_MANUAL_FORM_FIELD_INTEREST_RATE":        18,
		"NETWORTH_MANUAL_FORM_FIELD_NAME_DEPOSIT_TYPE":    19,
	}
)

func (x NetworthManualFormFieldName) Enum() *NetworthManualFormFieldName {
	p := new(NetworthManualFormFieldName)
	*p = x
	return p
}

func (x NetworthManualFormFieldName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetworthManualFormFieldName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_networth_internal_enums_proto_enumTypes[4].Descriptor()
}

func (NetworthManualFormFieldName) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_networth_internal_enums_proto_enumTypes[4]
}

func (x NetworthManualFormFieldName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetworthManualFormFieldName.Descriptor instead.
func (NetworthManualFormFieldName) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_internal_enums_proto_rawDescGZIP(), []int{4}
}

type NetworthAggregateType int32

const (
	NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_UNSPECIFIED NetworthAggregateType = 0
	NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_SAVINGS     NetworthAggregateType = 1
	NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_ASSETS      NetworthAggregateType = 2
	NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_LIABILITIES NetworthAggregateType = 3
	NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_INVESTMENTS NetworthAggregateType = 4
)

// Enum value maps for NetworthAggregateType.
var (
	NetworthAggregateType_name = map[int32]string{
		0: "NETWORTH_AGGREGATE_TYPE_UNSPECIFIED",
		1: "NETWORTH_AGGREGATE_TYPE_SAVINGS",
		2: "NETWORTH_AGGREGATE_TYPE_ASSETS",
		3: "NETWORTH_AGGREGATE_TYPE_LIABILITIES",
		4: "NETWORTH_AGGREGATE_TYPE_INVESTMENTS",
	}
	NetworthAggregateType_value = map[string]int32{
		"NETWORTH_AGGREGATE_TYPE_UNSPECIFIED": 0,
		"NETWORTH_AGGREGATE_TYPE_SAVINGS":     1,
		"NETWORTH_AGGREGATE_TYPE_ASSETS":      2,
		"NETWORTH_AGGREGATE_TYPE_LIABILITIES": 3,
		"NETWORTH_AGGREGATE_TYPE_INVESTMENTS": 4,
	}
)

func (x NetworthAggregateType) Enum() *NetworthAggregateType {
	p := new(NetworthAggregateType)
	*p = x
	return p
}

func (x NetworthAggregateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetworthAggregateType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_networth_internal_enums_proto_enumTypes[5].Descriptor()
}

func (NetworthAggregateType) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_networth_internal_enums_proto_enumTypes[5]
}

func (x NetworthAggregateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetworthAggregateType.Descriptor instead.
func (NetworthAggregateType) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_internal_enums_proto_rawDescGZIP(), []int{5}
}

var File_api_frontend_insights_networth_internal_enums_proto protoreflect.FileDescriptor

var file_api_frontend_insights_networth_internal_enums_proto_rawDesc = []byte{
	0x0a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2a, 0xef, 0x08, 0x0a, 0x10, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x1d, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52,
	0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2c, 0x0a, 0x28, 0x4e, 0x45, 0x54,
	0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41,
	0x53, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x53, 0x10, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x4e, 0x45, 0x54, 0x57, 0x4f,
	0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x53, 0x53,
	0x45, 0x54, 0x5f, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10,
	0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x45, 0x50, 0x46,
	0x10, 0x03, 0x12, 0x24, 0x0a, 0x20, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x44, 0x45,
	0x50, 0x4f, 0x53, 0x49, 0x54, 0x53, 0x10, 0x04, 0x12, 0x2d, 0x0a, 0x29, 0x4e, 0x45, 0x54, 0x57,
	0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x49, 0x4e, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52,
	0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x05, 0x12, 0x29, 0x0a, 0x25, 0x4e, 0x45, 0x54, 0x57, 0x4f,
	0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x53, 0x53,
	0x45, 0x54, 0x5f, 0x55, 0x53, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x49, 0x45, 0x53,
	0x10, 0x06, 0x12, 0x20, 0x0a, 0x1c, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x4a, 0x55,
	0x4d, 0x50, 0x10, 0x07, 0x12, 0x21, 0x0a, 0x1d, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f,
	0x46, 0x49, 0x5f, 0x53, 0x44, 0x10, 0x08, 0x12, 0x21, 0x0a, 0x1d, 0x4e, 0x45, 0x54, 0x57, 0x4f,
	0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x53, 0x53,
	0x45, 0x54, 0x5f, 0x46, 0x49, 0x5f, 0x46, 0x44, 0x10, 0x09, 0x12, 0x37, 0x0a, 0x33, 0x4e, 0x45,
	0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x55, 0x54, 0x53, 0x54, 0x41, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x0a, 0x12, 0x29, 0x0a, 0x25, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49,
	0x54, 0x59, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x0b, 0x12, 0x2d,
	0x0a, 0x29, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x45,
	0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x0c, 0x12, 0x2c, 0x0a,
	0x28, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x56, 0x45, 0x48,
	0x49, 0x43, 0x4c, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x0d, 0x12, 0x2e, 0x0a, 0x2a, 0x4e,
	0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x5f, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x45, 0x44, 0x55, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x0e, 0x12, 0x2b, 0x0a, 0x27, 0x4e,
	0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x5f, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52,
	0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x10, 0x0f, 0x12, 0x1f, 0x0a, 0x1b, 0x4e, 0x45, 0x54, 0x57,
	0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x4e, 0x50, 0x53, 0x10, 0x10, 0x12, 0x19, 0x0a, 0x15, 0x4e, 0x45, 0x54,
	0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41,
	0x49, 0x46, 0x10, 0x11, 0x12, 0x23, 0x0a, 0x1f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x52, 0x54, 0x5f, 0x41, 0x52,
	0x54, 0x45, 0x46, 0x41, 0x43, 0x54, 0x53, 0x10, 0x12, 0x12, 0x1b, 0x0a, 0x17, 0x4e, 0x45, 0x54,
	0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x42,
	0x4f, 0x4e, 0x44, 0x53, 0x10, 0x13, 0x12, 0x1a, 0x0a, 0x16, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52,
	0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x41, 0x53, 0x48,
	0x10, 0x14, 0x12, 0x22, 0x0a, 0x1e, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x44, 0x49, 0x47, 0x49, 0x54, 0x41, 0x4c, 0x5f,
	0x47, 0x4f, 0x4c, 0x44, 0x10, 0x15, 0x12, 0x24, 0x0a, 0x20, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52,
	0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x44, 0x49, 0x47, 0x49,
	0x54, 0x41, 0x4c, 0x5f, 0x53, 0x49, 0x4c, 0x56, 0x45, 0x52, 0x10, 0x16, 0x12, 0x24, 0x0a, 0x20,
	0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x54, 0x59,
	0x10, 0x17, 0x12, 0x21, 0x0a, 0x1d, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x45, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x10, 0x18, 0x12, 0x32, 0x0a, 0x2e, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54,
	0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46,
	0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x19, 0x12, 0x2b, 0x0a, 0x27, 0x4e, 0x45, 0x54,
	0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x50,
	0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f,
	0x46, 0x55, 0x4e, 0x44, 0x10, 0x1a, 0x12, 0x2b, 0x0a, 0x27, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52,
	0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x45, 0x4d, 0x50, 0x4c,
	0x4f, 0x59, 0x45, 0x45, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x1b, 0x2a, 0x85, 0x01, 0x0a, 0x13, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x4e,
	0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x53,
	0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x53, 0x53, 0x45,
	0x54, 0x53, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48,
	0x5f, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49,
	0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x02, 0x2a, 0x9f, 0x02, 0x0a, 0x16,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x24, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52,
	0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x2a, 0x0a, 0x26, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x49,
	0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x01, 0x12, 0x2b, 0x0a, 0x27,
	0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x50, 0x50,
	0x4c, 0x49, 0x43, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x28, 0x0a, 0x24, 0x4e, 0x45, 0x54,
	0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x49, 0x5a, 0x45,
	0x44, 0x10, 0x03, 0x12, 0x2f, 0x0a, 0x2b, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f,
	0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x04, 0x12, 0x27, 0x0a, 0x23, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x10, 0x05, 0x2a, 0x85, 0x01,
	0x0a, 0x13, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54,
	0x48, 0x5f, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c,
	0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x25,
	0x0a, 0x21, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x57, 0x49, 0x44, 0x47, 0x45,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x53,
	0x4f, 0x4f, 0x4e, 0x10, 0x02, 0x2a, 0xaa, 0x07, 0x0a, 0x1b, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x26, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54,
	0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x2e, 0x0a, 0x2a, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x4d, 0x41,
	0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10,
	0x01, 0x12, 0x2d, 0x0a, 0x29, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x4d, 0x41,
	0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0x02,
	0x12, 0x2c, 0x0a, 0x28, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x4d, 0x41, 0x4e,
	0x55, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x43,
	0x55, 0x52, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0x03, 0x12, 0x2e,
	0x0a, 0x2a, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41,
	0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x49, 0x4e, 0x56,
	0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x04, 0x12, 0x2e,
	0x0a, 0x2a, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41,
	0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x45, 0x56, 0x41,
	0x4c, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x05, 0x12, 0x27,
	0x0a, 0x23, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41,
	0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x41, 0x4d, 0x43,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x06, 0x12, 0x27, 0x0a, 0x23, 0x4e, 0x45, 0x54, 0x57, 0x4f,
	0x52, 0x54, 0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x10, 0x07,
	0x12, 0x27, 0x0a, 0x23, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x4d, 0x41, 0x4e,
	0x55, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x46,
	0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x49, 0x44, 0x10, 0x08, 0x12, 0x2a, 0x0a, 0x26, 0x4e, 0x45, 0x54,
	0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52,
	0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x42, 0x52, 0x4f, 0x4b, 0x45, 0x52, 0x5f, 0x43,
	0x4f, 0x44, 0x45, 0x10, 0x09, 0x12, 0x26, 0x0a, 0x22, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54,
	0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x52, 0x45, 0x4d, 0x41, 0x52, 0x4b, 0x53, 0x10, 0x0a, 0x12, 0x2c, 0x0a,
	0x28, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c,
	0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x54, 0x55,
	0x52, 0x49, 0x54, 0x59, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x0b, 0x12, 0x28, 0x0a, 0x24, 0x4e,
	0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x46,
	0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4e, 0x55, 0x4d, 0x5f, 0x55, 0x4e,
	0x49, 0x54, 0x53, 0x10, 0x0c, 0x12, 0x2d, 0x0a, 0x29, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54,
	0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x51, 0x55, 0x41, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x47, 0x52, 0x41,
	0x4d, 0x53, 0x10, 0x0d, 0x12, 0x28, 0x0a, 0x24, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48,
	0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x47, 0x4f, 0x4c, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x0e, 0x12, 0x33,
	0x0a, 0x2f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41,
	0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x49, 0x4e, 0x56,
	0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43,
	0x59, 0x10, 0x0f, 0x12, 0x2c, 0x0a, 0x28, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f,
	0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10,
	0x10, 0x12, 0x2f, 0x0a, 0x2b, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x4d, 0x41,
	0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x56, 0x45, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45,
	0x10, 0x11, 0x12, 0x2c, 0x0a, 0x28, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x4d,
	0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x10, 0x12,
	0x12, 0x30, 0x0a, 0x2c, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x4d, 0x41, 0x4e,
	0x55, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x10, 0x13, 0x2a, 0xdb, 0x01, 0x0a, 0x15, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x41,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x23,
	0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41,
	0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54,
	0x48, 0x5f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x4e, 0x45,
	0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x53, 0x10, 0x02, 0x12, 0x27,
	0x0a, 0x23, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x41, 0x47, 0x47, 0x52, 0x45,
	0x47, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c,
	0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x03, 0x12, 0x27, 0x0a, 0x23, 0x4e, 0x45, 0x54, 0x57, 0x4f,
	0x52, 0x54, 0x48, 0x5f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x04,
	0x42, 0x6e, 0x0a, 0x35, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5a, 0x35, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_insights_networth_internal_enums_proto_rawDescOnce sync.Once
	file_api_frontend_insights_networth_internal_enums_proto_rawDescData = file_api_frontend_insights_networth_internal_enums_proto_rawDesc
)

func file_api_frontend_insights_networth_internal_enums_proto_rawDescGZIP() []byte {
	file_api_frontend_insights_networth_internal_enums_proto_rawDescOnce.Do(func() {
		file_api_frontend_insights_networth_internal_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_insights_networth_internal_enums_proto_rawDescData)
	})
	return file_api_frontend_insights_networth_internal_enums_proto_rawDescData
}

var file_api_frontend_insights_networth_internal_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_frontend_insights_networth_internal_enums_proto_goTypes = []interface{}{
	(NetworthCategory)(0),            // 0: frontend.insights.networth.NetworthCategory
	(NetworthSectionType)(0),         // 1: frontend.insights.networth.NetworthSectionType
	(NetworthCategoryStatus)(0),      // 2: frontend.insights.networth.NetworthCategoryStatus
	(NetworthWidgetState)(0),         // 3: frontend.insights.networth.NetworthWidgetState
	(NetworthManualFormFieldName)(0), // 4: frontend.insights.networth.NetworthManualFormFieldName
	(NetworthAggregateType)(0),       // 5: frontend.insights.networth.NetworthAggregateType
}
var file_api_frontend_insights_networth_internal_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_frontend_insights_networth_internal_enums_proto_init() }
func file_api_frontend_insights_networth_internal_enums_proto_init() {
	if File_api_frontend_insights_networth_internal_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_insights_networth_internal_enums_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_insights_networth_internal_enums_proto_goTypes,
		DependencyIndexes: file_api_frontend_insights_networth_internal_enums_proto_depIdxs,
		EnumInfos:         file_api_frontend_insights_networth_internal_enums_proto_enumTypes,
	}.Build()
	File_api_frontend_insights_networth_internal_enums_proto = out.File
	file_api_frontend_insights_networth_internal_enums_proto_rawDesc = nil
	file_api_frontend_insights_networth_internal_enums_proto_goTypes = nil
	file_api_frontend_insights_networth_internal_enums_proto_depIdxs = nil
}
