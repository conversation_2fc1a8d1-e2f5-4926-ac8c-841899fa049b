// Code generated by MockGen. DO NOT EDIT.
// Source: api/frontend/rewards/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	rewards "github.com/epifi/gamma/api/frontend/rewards"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockRewardsClient is a mock of RewardsClient interface.
type MockRewardsClient struct {
	ctrl     *gomock.Controller
	recorder *MockRewardsClientMockRecorder
}

// MockRewardsClientMockRecorder is the mock recorder for MockRewardsClient.
type MockRewardsClientMockRecorder struct {
	mock *MockRewardsClient
}

// NewMockRewardsClient creates a new mock instance.
func NewMockRewardsClient(ctrl *gomock.Controller) *MockRewardsClient {
	mock := &MockRewardsClient{ctrl: ctrl}
	mock.recorder = &MockRewardsClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRewardsClient) EXPECT() *MockRewardsClientMockRecorder {
	return m.recorder
}

// BulkClaimRewards mocks base method.
func (m *MockRewardsClient) BulkClaimRewards(ctx context.Context, in *rewards.BulkClaimRewardsRequest, opts ...grpc.CallOption) (*rewards.BulkClaimRewardsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BulkClaimRewards", varargs...)
	ret0, _ := ret[0].(*rewards.BulkClaimRewardsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkClaimRewards indicates an expected call of BulkClaimRewards.
func (mr *MockRewardsClientMockRecorder) BulkClaimRewards(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkClaimRewards", reflect.TypeOf((*MockRewardsClient)(nil).BulkClaimRewards), varargs...)
}

// ChooseExchangerOrderOption mocks base method.
func (m *MockRewardsClient) ChooseExchangerOrderOption(ctx context.Context, in *rewards.ChooseExchangerOrderOptionRequest, opts ...grpc.CallOption) (*rewards.ChooseExchangerOrderOptionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ChooseExchangerOrderOption", varargs...)
	ret0, _ := ret[0].(*rewards.ChooseExchangerOrderOptionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChooseExchangerOrderOption indicates an expected call of ChooseExchangerOrderOption.
func (mr *MockRewardsClientMockRecorder) ChooseExchangerOrderOption(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChooseExchangerOrderOption", reflect.TypeOf((*MockRewardsClient)(nil).ChooseExchangerOrderOption), varargs...)
}

// ClaimReward mocks base method.
func (m *MockRewardsClient) ClaimReward(ctx context.Context, in *rewards.ClaimRewardRequest, opts ...grpc.CallOption) (*rewards.ClaimRewardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClaimReward", varargs...)
	ret0, _ := ret[0].(*rewards.ClaimRewardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClaimReward indicates an expected call of ClaimReward.
func (mr *MockRewardsClientMockRecorder) ClaimReward(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClaimReward", reflect.TypeOf((*MockRewardsClient)(nil).ClaimReward), varargs...)
}

// ConfirmRedemption mocks base method.
func (m *MockRewardsClient) ConfirmRedemption(ctx context.Context, in *rewards.ConfirmRedemptionRequest, opts ...grpc.CallOption) (*rewards.ConfirmRedemptionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmRedemption", varargs...)
	ret0, _ := ret[0].(*rewards.ConfirmRedemptionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmRedemption indicates an expected call of ConfirmRedemption.
func (mr *MockRewardsClientMockRecorder) ConfirmRedemption(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmRedemption", reflect.TypeOf((*MockRewardsClient)(nil).ConfirmRedemption), varargs...)
}

// ForceRetryRewardProcessing mocks base method.
func (m *MockRewardsClient) ForceRetryRewardProcessing(ctx context.Context, in *rewards.ForceRetryRewardProcessingRequest, opts ...grpc.CallOption) (*rewards.ForceRetryRewardProcessingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ForceRetryRewardProcessing", varargs...)
	ret0, _ := ret[0].(*rewards.ForceRetryRewardProcessingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ForceRetryRewardProcessing indicates an expected call of ForceRetryRewardProcessing.
func (mr *MockRewardsClientMockRecorder) ForceRetryRewardProcessing(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ForceRetryRewardProcessing", reflect.TypeOf((*MockRewardsClient)(nil).ForceRetryRewardProcessing), varargs...)
}

// GetActiveOffers mocks base method.
func (m *MockRewardsClient) GetActiveOffers(ctx context.Context, in *rewards.GetActiveOffersRequest, opts ...grpc.CallOption) (*rewards.GetActiveOffersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetActiveOffers", varargs...)
	ret0, _ := ret[0].(*rewards.GetActiveOffersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveOffers indicates an expected call of GetActiveOffers.
func (mr *MockRewardsClientMockRecorder) GetActiveOffers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveOffers", reflect.TypeOf((*MockRewardsClient)(nil).GetActiveOffers), varargs...)
}

// GetActiveRedeemedOffers mocks base method.
func (m *MockRewardsClient) GetActiveRedeemedOffers(ctx context.Context, in *rewards.GetActiveRedeemedOffersRequest, opts ...grpc.CallOption) (*rewards.GetActiveRedeemedOffersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetActiveRedeemedOffers", varargs...)
	ret0, _ := ret[0].(*rewards.GetActiveRedeemedOffersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveRedeemedOffers indicates an expected call of GetActiveRedeemedOffers.
func (mr *MockRewardsClientMockRecorder) GetActiveRedeemedOffers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveRedeemedOffers", reflect.TypeOf((*MockRewardsClient)(nil).GetActiveRedeemedOffers), varargs...)
}

// GetActiveRewardOffers mocks base method.
func (m *MockRewardsClient) GetActiveRewardOffers(ctx context.Context, in *rewards.GetActiveRewardOffersRequest, opts ...grpc.CallOption) (*rewards.GetActiveRewardOffersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetActiveRewardOffers", varargs...)
	ret0, _ := ret[0].(*rewards.GetActiveRewardOffersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveRewardOffers indicates an expected call of GetActiveRewardOffers.
func (mr *MockRewardsClientMockRecorder) GetActiveRewardOffers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveRewardOffers", reflect.TypeOf((*MockRewardsClient)(nil).GetActiveRewardOffers), varargs...)
}

// GetCardOfferDetails mocks base method.
func (m *MockRewardsClient) GetCardOfferDetails(ctx context.Context, in *rewards.GetCardOfferDetailsRequest, opts ...grpc.CallOption) (*rewards.GetCardOfferDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCardOfferDetails", varargs...)
	ret0, _ := ret[0].(*rewards.GetCardOfferDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardOfferDetails indicates an expected call of GetCardOfferDetails.
func (mr *MockRewardsClientMockRecorder) GetCardOfferDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardOfferDetails", reflect.TypeOf((*MockRewardsClient)(nil).GetCardOfferDetails), varargs...)
}

// GetCardOffers mocks base method.
func (m *MockRewardsClient) GetCardOffers(ctx context.Context, in *rewards.GetCardOffersRequest, opts ...grpc.CallOption) (*rewards.GetCardOffersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCardOffers", varargs...)
	ret0, _ := ret[0].(*rewards.GetCardOffersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardOffers indicates an expected call of GetCardOffers.
func (mr *MockRewardsClientMockRecorder) GetCardOffers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardOffers", reflect.TypeOf((*MockRewardsClient)(nil).GetCardOffers), varargs...)
}

// GetCardOffersCatalogLayout mocks base method.
func (m *MockRewardsClient) GetCardOffersCatalogLayout(ctx context.Context, in *rewards.GetCardOffersCatalogLayoutRequest, opts ...grpc.CallOption) (*rewards.GetCardOffersCatalogLayoutResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCardOffersCatalogLayout", varargs...)
	ret0, _ := ret[0].(*rewards.GetCardOffersCatalogLayoutResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardOffersCatalogLayout indicates an expected call of GetCardOffersCatalogLayout.
func (mr *MockRewardsClientMockRecorder) GetCardOffersCatalogLayout(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardOffersCatalogLayout", reflect.TypeOf((*MockRewardsClient)(nil).GetCardOffersCatalogLayout), varargs...)
}

// GetCardOffersTabs mocks base method.
func (m *MockRewardsClient) GetCardOffersTabs(ctx context.Context, in *rewards.GetCardOffersTabsRequest, opts ...grpc.CallOption) (*rewards.GetCardOffersTabsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCardOffersTabs", varargs...)
	ret0, _ := ret[0].(*rewards.GetCardOffersTabsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardOffersTabs indicates an expected call of GetCardOffersTabs.
func (mr *MockRewardsClientMockRecorder) GetCardOffersTabs(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardOffersTabs", reflect.TypeOf((*MockRewardsClient)(nil).GetCardOffersTabs), varargs...)
}

// GetCatalogOffersAndFilters mocks base method.
func (m *MockRewardsClient) GetCatalogOffersAndFilters(ctx context.Context, in *rewards.GetCatalogOffersAndFiltersRequest, opts ...grpc.CallOption) (*rewards.GetCatalogOffersAndFiltersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCatalogOffersAndFilters", varargs...)
	ret0, _ := ret[0].(*rewards.GetCatalogOffersAndFiltersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCatalogOffersAndFilters indicates an expected call of GetCatalogOffersAndFilters.
func (mr *MockRewardsClientMockRecorder) GetCatalogOffersAndFilters(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCatalogOffersAndFilters", reflect.TypeOf((*MockRewardsClient)(nil).GetCatalogOffersAndFilters), varargs...)
}

// GetClaimRewardInputScreen mocks base method.
func (m *MockRewardsClient) GetClaimRewardInputScreen(ctx context.Context, in *rewards.ClaimRewardInputScreenRequest, opts ...grpc.CallOption) (*rewards.ClaimRewardInputScreenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClaimRewardInputScreen", varargs...)
	ret0, _ := ret[0].(*rewards.ClaimRewardInputScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClaimRewardInputScreen indicates an expected call of GetClaimRewardInputScreen.
func (mr *MockRewardsClientMockRecorder) GetClaimRewardInputScreen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClaimRewardInputScreen", reflect.TypeOf((*MockRewardsClient)(nil).GetClaimRewardInputScreen), varargs...)
}

// GetClaimedRewardDetails mocks base method.
func (m *MockRewardsClient) GetClaimedRewardDetails(ctx context.Context, in *rewards.GetClaimedRewardDetailsRequest, opts ...grpc.CallOption) (*rewards.GetClaimedRewardDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClaimedRewardDetails", varargs...)
	ret0, _ := ret[0].(*rewards.GetClaimedRewardDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClaimedRewardDetails indicates an expected call of GetClaimedRewardDetails.
func (mr *MockRewardsClientMockRecorder) GetClaimedRewardDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClaimedRewardDetails", reflect.TypeOf((*MockRewardsClient)(nil).GetClaimedRewardDetails), varargs...)
}

// GetConvertFiCoinsOfferRedemptionScreen mocks base method.
func (m *MockRewardsClient) GetConvertFiCoinsOfferRedemptionScreen(ctx context.Context, in *rewards.GetConvertFiCoinsOfferRedemptionScreenRequest, opts ...grpc.CallOption) (*rewards.GetConvertFiCoinsOfferRedemptionScreenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConvertFiCoinsOfferRedemptionScreen", varargs...)
	ret0, _ := ret[0].(*rewards.GetConvertFiCoinsOfferRedemptionScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConvertFiCoinsOfferRedemptionScreen indicates an expected call of GetConvertFiCoinsOfferRedemptionScreen.
func (mr *MockRewardsClientMockRecorder) GetConvertFiCoinsOfferRedemptionScreen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConvertFiCoinsOfferRedemptionScreen", reflect.TypeOf((*MockRewardsClient)(nil).GetConvertFiCoinsOfferRedemptionScreen), varargs...)
}

// GetDynamicUrlForWebPageScreen mocks base method.
func (m *MockRewardsClient) GetDynamicUrlForWebPageScreen(ctx context.Context, in *rewards.GetDynamicUrlForWebPageScreenRequest, opts ...grpc.CallOption) (*rewards.GetDynamicUrlForWebPageScreenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDynamicUrlForWebPageScreen", varargs...)
	ret0, _ := ret[0].(*rewards.GetDynamicUrlForWebPageScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDynamicUrlForWebPageScreen indicates an expected call of GetDynamicUrlForWebPageScreen.
func (mr *MockRewardsClientMockRecorder) GetDynamicUrlForWebPageScreen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDynamicUrlForWebPageScreen", reflect.TypeOf((*MockRewardsClient)(nil).GetDynamicUrlForWebPageScreen), varargs...)
}

// GetEarnedRewards mocks base method.
func (m *MockRewardsClient) GetEarnedRewards(ctx context.Context, in *rewards.GetEarnedRewardsRequest, opts ...grpc.CallOption) (*rewards.GetEarnedRewardsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEarnedRewards", varargs...)
	ret0, _ := ret[0].(*rewards.GetEarnedRewardsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEarnedRewards indicates an expected call of GetEarnedRewards.
func (mr *MockRewardsClientMockRecorder) GetEarnedRewards(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEarnedRewards", reflect.TypeOf((*MockRewardsClient)(nil).GetEarnedRewards), varargs...)
}

// GetEarnedRewardsHistory mocks base method.
func (m *MockRewardsClient) GetEarnedRewardsHistory(ctx context.Context, in *rewards.GetEarnedRewardsHistoryRequest, opts ...grpc.CallOption) (*rewards.GetEarnedRewardsHistoryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEarnedRewardsHistory", varargs...)
	ret0, _ := ret[0].(*rewards.GetEarnedRewardsHistoryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEarnedRewardsHistory indicates an expected call of GetEarnedRewardsHistory.
func (mr *MockRewardsClientMockRecorder) GetEarnedRewardsHistory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEarnedRewardsHistory", reflect.TypeOf((*MockRewardsClient)(nil).GetEarnedRewardsHistory), varargs...)
}

// GetExchangerOfferById mocks base method.
func (m *MockRewardsClient) GetExchangerOfferById(ctx context.Context, in *rewards.GetExchangerOfferByIdRequest, opts ...grpc.CallOption) (*rewards.GetExchangerOfferByIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangerOfferById", varargs...)
	ret0, _ := ret[0].(*rewards.GetExchangerOfferByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOfferById indicates an expected call of GetExchangerOfferById.
func (mr *MockRewardsClientMockRecorder) GetExchangerOfferById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOfferById", reflect.TypeOf((*MockRewardsClient)(nil).GetExchangerOfferById), varargs...)
}

// GetExchangerOrderInputScreen mocks base method.
func (m *MockRewardsClient) GetExchangerOrderInputScreen(ctx context.Context, in *rewards.GetExchangerOrderInputScreenRequest, opts ...grpc.CallOption) (*rewards.GetExchangerOrderInputScreenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangerOrderInputScreen", varargs...)
	ret0, _ := ret[0].(*rewards.GetExchangerOrderInputScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOrderInputScreen indicates an expected call of GetExchangerOrderInputScreen.
func (mr *MockRewardsClientMockRecorder) GetExchangerOrderInputScreen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOrderInputScreen", reflect.TypeOf((*MockRewardsClient)(nil).GetExchangerOrderInputScreen), varargs...)
}

// GetExchangerOrdersForActor mocks base method.
func (m *MockRewardsClient) GetExchangerOrdersForActor(ctx context.Context, in *rewards.GetExchangerOrdersForActorRequest, opts ...grpc.CallOption) (*rewards.GetExchangerOrdersForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExchangerOrdersForActor", varargs...)
	ret0, _ := ret[0].(*rewards.GetExchangerOrdersForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOrdersForActor indicates an expected call of GetExchangerOrdersForActor.
func (mr *MockRewardsClientMockRecorder) GetExchangerOrdersForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOrdersForActor", reflect.TypeOf((*MockRewardsClient)(nil).GetExchangerOrdersForActor), varargs...)
}

// GetExpiredRedeemedOffers mocks base method.
func (m *MockRewardsClient) GetExpiredRedeemedOffers(ctx context.Context, in *rewards.GetExpiredRedeemedOffersRequest, opts ...grpc.CallOption) (*rewards.GetExpiredRedeemedOffersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExpiredRedeemedOffers", varargs...)
	ret0, _ := ret[0].(*rewards.GetExpiredRedeemedOffersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExpiredRedeemedOffers indicates an expected call of GetExpiredRedeemedOffers.
func (mr *MockRewardsClientMockRecorder) GetExpiredRedeemedOffers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExpiredRedeemedOffers", reflect.TypeOf((*MockRewardsClient)(nil).GetExpiredRedeemedOffers), varargs...)
}

// GetMyRewardsScreenDetails mocks base method.
func (m *MockRewardsClient) GetMyRewardsScreenDetails(ctx context.Context, in *rewards.GetMyRewardsScreenDetailsRequest, opts ...grpc.CallOption) (*rewards.GetMyRewardsScreenDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMyRewardsScreenDetails", varargs...)
	ret0, _ := ret[0].(*rewards.GetMyRewardsScreenDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyRewardsScreenDetails indicates an expected call of GetMyRewardsScreenDetails.
func (mr *MockRewardsClientMockRecorder) GetMyRewardsScreenDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyRewardsScreenDetails", reflect.TypeOf((*MockRewardsClient)(nil).GetMyRewardsScreenDetails), varargs...)
}

// GetOfferCatalogV1 mocks base method.
func (m *MockRewardsClient) GetOfferCatalogV1(ctx context.Context, in *rewards.GetOfferCatalogV1Request, opts ...grpc.CallOption) (*rewards.GetOfferCatalogV1Response, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOfferCatalogV1", varargs...)
	ret0, _ := ret[0].(*rewards.GetOfferCatalogV1Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOfferCatalogV1 indicates an expected call of GetOfferCatalogV1.
func (mr *MockRewardsClientMockRecorder) GetOfferCatalogV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOfferCatalogV1", reflect.TypeOf((*MockRewardsClient)(nil).GetOfferCatalogV1), varargs...)
}

// GetOfferDetailsById mocks base method.
func (m *MockRewardsClient) GetOfferDetailsById(ctx context.Context, in *rewards.GetOfferDetailsByIdRequest, opts ...grpc.CallOption) (*rewards.GetOfferDetailsByIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOfferDetailsById", varargs...)
	ret0, _ := ret[0].(*rewards.GetOfferDetailsByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOfferDetailsById indicates an expected call of GetOfferDetailsById.
func (mr *MockRewardsClientMockRecorder) GetOfferDetailsById(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOfferDetailsById", reflect.TypeOf((*MockRewardsClient)(nil).GetOfferDetailsById), varargs...)
}

// GetOffersCatalogPage mocks base method.
func (m *MockRewardsClient) GetOffersCatalogPage(ctx context.Context, in *rewards.GetOffersCatalogPageRequest, opts ...grpc.CallOption) (*rewards.GetOffersCatalogPageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOffersCatalogPage", varargs...)
	ret0, _ := ret[0].(*rewards.GetOffersCatalogPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOffersCatalogPage indicates an expected call of GetOffersCatalogPage.
func (mr *MockRewardsClientMockRecorder) GetOffersCatalogPage(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOffersCatalogPage", reflect.TypeOf((*MockRewardsClient)(nil).GetOffersCatalogPage), varargs...)
}

// GetOffersCatalogPageVersionToRender mocks base method.
func (m *MockRewardsClient) GetOffersCatalogPageVersionToRender(ctx context.Context, in *rewards.GetOffersCatalogPageVersionToRenderRequest, opts ...grpc.CallOption) (*rewards.GetOffersCatalogPageVersionToRenderResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOffersCatalogPageVersionToRender", varargs...)
	ret0, _ := ret[0].(*rewards.GetOffersCatalogPageVersionToRenderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOffersCatalogPageVersionToRender indicates an expected call of GetOffersCatalogPageVersionToRender.
func (mr *MockRewardsClientMockRecorder) GetOffersCatalogPageVersionToRender(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOffersCatalogPageVersionToRender", reflect.TypeOf((*MockRewardsClient)(nil).GetOffersCatalogPageVersionToRender), varargs...)
}

// GetRedeemOfferInputScreen mocks base method.
func (m *MockRewardsClient) GetRedeemOfferInputScreen(ctx context.Context, in *rewards.RedeemOfferInputScreenRequest, opts ...grpc.CallOption) (*rewards.RedeemOfferInputScreenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRedeemOfferInputScreen", varargs...)
	ret0, _ := ret[0].(*rewards.RedeemOfferInputScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedeemOfferInputScreen indicates an expected call of GetRedeemOfferInputScreen.
func (mr *MockRewardsClientMockRecorder) GetRedeemOfferInputScreen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedeemOfferInputScreen", reflect.TypeOf((*MockRewardsClient)(nil).GetRedeemOfferInputScreen), varargs...)
}

// GetRedeemedOfferDetailsRedirectionInfo mocks base method.
func (m *MockRewardsClient) GetRedeemedOfferDetailsRedirectionInfo(ctx context.Context, in *rewards.GetRedeemedOfferDetailsRedirectionInfoRequest, opts ...grpc.CallOption) (*rewards.GetRedeemedOfferDetailsRedirectionInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRedeemedOfferDetailsRedirectionInfo", varargs...)
	ret0, _ := ret[0].(*rewards.GetRedeemedOfferDetailsRedirectionInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedeemedOfferDetailsRedirectionInfo indicates an expected call of GetRedeemedOfferDetailsRedirectionInfo.
func (mr *MockRewardsClientMockRecorder) GetRedeemedOfferDetailsRedirectionInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedeemedOfferDetailsRedirectionInfo", reflect.TypeOf((*MockRewardsClient)(nil).GetRedeemedOfferDetailsRedirectionInfo), varargs...)
}

// GetRedeemedOffersCatalogLayout mocks base method.
func (m *MockRewardsClient) GetRedeemedOffersCatalogLayout(ctx context.Context, in *rewards.GetRedeemedOffersCatalogLayoutRequest, opts ...grpc.CallOption) (*rewards.GetRedeemedOffersCatalogLayoutResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRedeemedOffersCatalogLayout", varargs...)
	ret0, _ := ret[0].(*rewards.GetRedeemedOffersCatalogLayoutResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedeemedOffersCatalogLayout indicates an expected call of GetRedeemedOffersCatalogLayout.
func (mr *MockRewardsClientMockRecorder) GetRedeemedOffersCatalogLayout(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedeemedOffersCatalogLayout", reflect.TypeOf((*MockRewardsClient)(nil).GetRedeemedOffersCatalogLayout), varargs...)
}

// GetRedeemedOffersV1 mocks base method.
func (m *MockRewardsClient) GetRedeemedOffersV1(ctx context.Context, in *rewards.GetRedeemedOffersV1Request, opts ...grpc.CallOption) (*rewards.GetRedeemedOffersV1Response, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRedeemedOffersV1", varargs...)
	ret0, _ := ret[0].(*rewards.GetRedeemedOffersV1Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedeemedOffersV1 indicates an expected call of GetRedeemedOffersV1.
func (mr *MockRewardsClientMockRecorder) GetRedeemedOffersV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedeemedOffersV1", reflect.TypeOf((*MockRewardsClient)(nil).GetRedeemedOffersV1), varargs...)
}

// GetRewardDigest mocks base method.
func (m *MockRewardsClient) GetRewardDigest(ctx context.Context, in *rewards.GetRewardDigestRequest, opts ...grpc.CallOption) (*rewards.GetRewardDigestResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRewardDigest", varargs...)
	ret0, _ := ret[0].(*rewards.GetRewardDigestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRewardDigest indicates an expected call of GetRewardDigest.
func (mr *MockRewardsClientMockRecorder) GetRewardDigest(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardDigest", reflect.TypeOf((*MockRewardsClient)(nil).GetRewardDigest), varargs...)
}

// GetRewardOfferDetails mocks base method.
func (m *MockRewardsClient) GetRewardOfferDetails(ctx context.Context, in *rewards.GetRewardOfferDetailsRequest, opts ...grpc.CallOption) (*rewards.GetRewardOfferDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRewardOfferDetails", varargs...)
	ret0, _ := ret[0].(*rewards.GetRewardOfferDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRewardOfferDetails indicates an expected call of GetRewardOfferDetails.
func (mr *MockRewardsClientMockRecorder) GetRewardOfferDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardOfferDetails", reflect.TypeOf((*MockRewardsClient)(nil).GetRewardOfferDetails), varargs...)
}

// GetRewardSummary mocks base method.
func (m *MockRewardsClient) GetRewardSummary(ctx context.Context, in *rewards.GetRewardSummaryRequest, opts ...grpc.CallOption) (*rewards.GetRewardSummaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRewardSummary", varargs...)
	ret0, _ := ret[0].(*rewards.GetRewardSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRewardSummary indicates an expected call of GetRewardSummary.
func (mr *MockRewardsClientMockRecorder) GetRewardSummary(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardSummary", reflect.TypeOf((*MockRewardsClient)(nil).GetRewardSummary), varargs...)
}

// GetRewardsAndOffersForHome mocks base method.
func (m *MockRewardsClient) GetRewardsAndOffersForHome(ctx context.Context, in *rewards.GetRewardsAndOffersForHomeRequest, opts ...grpc.CallOption) (*rewards.GetRewardsAndOffersForHomeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRewardsAndOffersForHome", varargs...)
	ret0, _ := ret[0].(*rewards.GetRewardsAndOffersForHomeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRewardsAndOffersForHome indicates an expected call of GetRewardsAndOffersForHome.
func (mr *MockRewardsClientMockRecorder) GetRewardsAndOffersForHome(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardsAndOffersForHome", reflect.TypeOf((*MockRewardsClient)(nil).GetRewardsAndOffersForHome), varargs...)
}

// GetRewardsAndOffersWidget mocks base method.
func (m *MockRewardsClient) GetRewardsAndOffersWidget(ctx context.Context, in *rewards.GetRewardsAndOffersWidgetRequest, opts ...grpc.CallOption) (*rewards.GetRewardsAndOffersWidgetResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRewardsAndOffersWidget", varargs...)
	ret0, _ := ret[0].(*rewards.GetRewardsAndOffersWidgetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRewardsAndOffersWidget indicates an expected call of GetRewardsAndOffersWidget.
func (mr *MockRewardsClientMockRecorder) GetRewardsAndOffersWidget(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardsAndOffersWidget", reflect.TypeOf((*MockRewardsClient)(nil).GetRewardsAndOffersWidget), varargs...)
}

// GetRewardsForActorV1 mocks base method.
func (m *MockRewardsClient) GetRewardsForActorV1(ctx context.Context, in *rewards.GetRewardsForActorV1Request, opts ...grpc.CallOption) (*rewards.GetRewardsForActorV1Response, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRewardsForActorV1", varargs...)
	ret0, _ := ret[0].(*rewards.GetRewardsForActorV1Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRewardsForActorV1 indicates an expected call of GetRewardsForActorV1.
func (mr *MockRewardsClientMockRecorder) GetRewardsForActorV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardsForActorV1", reflect.TypeOf((*MockRewardsClient)(nil).GetRewardsForActorV1), varargs...)
}

// GetUserDetailsInputOfferRedemptionScreen mocks base method.
func (m *MockRewardsClient) GetUserDetailsInputOfferRedemptionScreen(ctx context.Context, in *rewards.GetUserDetailsInputOfferRedemptionScreenRequest, opts ...grpc.CallOption) (*rewards.GetUserDetailsInputOfferRedemptionScreenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserDetailsInputOfferRedemptionScreen", varargs...)
	ret0, _ := ret[0].(*rewards.GetUserDetailsInputOfferRedemptionScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDetailsInputOfferRedemptionScreen indicates an expected call of GetUserDetailsInputOfferRedemptionScreen.
func (mr *MockRewardsClientMockRecorder) GetUserDetailsInputOfferRedemptionScreen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDetailsInputOfferRedemptionScreen", reflect.TypeOf((*MockRewardsClient)(nil).GetUserDetailsInputOfferRedemptionScreen), varargs...)
}

// GetWaysToEarnRewardDetailScreen mocks base method.
func (m *MockRewardsClient) GetWaysToEarnRewardDetailScreen(ctx context.Context, in *rewards.GetWaysToEarnRewardDetailRequest, opts ...grpc.CallOption) (*rewards.GetWaysToEarnRewardDetailResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWaysToEarnRewardDetailScreen", varargs...)
	ret0, _ := ret[0].(*rewards.GetWaysToEarnRewardDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWaysToEarnRewardDetailScreen indicates an expected call of GetWaysToEarnRewardDetailScreen.
func (mr *MockRewardsClientMockRecorder) GetWaysToEarnRewardDetailScreen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWaysToEarnRewardDetailScreen", reflect.TypeOf((*MockRewardsClient)(nil).GetWaysToEarnRewardDetailScreen), varargs...)
}

// GetWaysToEarnRewardsScreen mocks base method.
func (m *MockRewardsClient) GetWaysToEarnRewardsScreen(ctx context.Context, in *rewards.GetWaysToEarnRewardsRequest, opts ...grpc.CallOption) (*rewards.GetWaysToEarnRewardsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWaysToEarnRewardsScreen", varargs...)
	ret0, _ := ret[0].(*rewards.GetWaysToEarnRewardsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWaysToEarnRewardsScreen indicates an expected call of GetWaysToEarnRewardsScreen.
func (mr *MockRewardsClientMockRecorder) GetWaysToEarnRewardsScreen(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWaysToEarnRewardsScreen", reflect.TypeOf((*MockRewardsClient)(nil).GetWaysToEarnRewardsScreen), varargs...)
}

// GetWaysToEarnRewardsScreenVersionToRender mocks base method.
func (m *MockRewardsClient) GetWaysToEarnRewardsScreenVersionToRender(ctx context.Context, in *rewards.GetWaysToEarnRewardsScreenVersionToRenderRequest, opts ...grpc.CallOption) (*rewards.GetWaysToEarnRewardsScreenVersionToRenderResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetWaysToEarnRewardsScreenVersionToRender", varargs...)
	ret0, _ := ret[0].(*rewards.GetWaysToEarnRewardsScreenVersionToRenderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWaysToEarnRewardsScreenVersionToRender indicates an expected call of GetWaysToEarnRewardsScreenVersionToRender.
func (mr *MockRewardsClientMockRecorder) GetWaysToEarnRewardsScreenVersionToRender(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWaysToEarnRewardsScreenVersionToRender", reflect.TypeOf((*MockRewardsClient)(nil).GetWaysToEarnRewardsScreenVersionToRender), varargs...)
}

// InitiateRedemption mocks base method.
func (m *MockRewardsClient) InitiateRedemption(ctx context.Context, in *rewards.InitiateRedemptionRequest, opts ...grpc.CallOption) (*rewards.InitiateRedemptionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateRedemption", varargs...)
	ret0, _ := ret[0].(*rewards.InitiateRedemptionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateRedemption indicates an expected call of InitiateRedemption.
func (mr *MockRewardsClientMockRecorder) InitiateRedemption(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateRedemption", reflect.TypeOf((*MockRewardsClient)(nil).InitiateRedemption), varargs...)
}

// RedeemExchangerOffer mocks base method.
func (m *MockRewardsClient) RedeemExchangerOffer(ctx context.Context, in *rewards.RedeemExchangerOfferRequest, opts ...grpc.CallOption) (*rewards.RedeemExchangerOfferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RedeemExchangerOffer", varargs...)
	ret0, _ := ret[0].(*rewards.RedeemExchangerOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RedeemExchangerOffer indicates an expected call of RedeemExchangerOffer.
func (mr *MockRewardsClientMockRecorder) RedeemExchangerOffer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedeemExchangerOffer", reflect.TypeOf((*MockRewardsClient)(nil).RedeemExchangerOffer), varargs...)
}

// SubmitExchangerOrderUserInput mocks base method.
func (m *MockRewardsClient) SubmitExchangerOrderUserInput(ctx context.Context, in *rewards.SubmitExchangerOrderUserInputRequest, opts ...grpc.CallOption) (*rewards.SubmitExchangerOrderUserInputResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitExchangerOrderUserInput", varargs...)
	ret0, _ := ret[0].(*rewards.SubmitExchangerOrderUserInputResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitExchangerOrderUserInput indicates an expected call of SubmitExchangerOrderUserInput.
func (mr *MockRewardsClientMockRecorder) SubmitExchangerOrderUserInput(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitExchangerOrderUserInput", reflect.TypeOf((*MockRewardsClient)(nil).SubmitExchangerOrderUserInput), varargs...)
}

// MockRewardsServer is a mock of RewardsServer interface.
type MockRewardsServer struct {
	ctrl     *gomock.Controller
	recorder *MockRewardsServerMockRecorder
}

// MockRewardsServerMockRecorder is the mock recorder for MockRewardsServer.
type MockRewardsServerMockRecorder struct {
	mock *MockRewardsServer
}

// NewMockRewardsServer creates a new mock instance.
func NewMockRewardsServer(ctrl *gomock.Controller) *MockRewardsServer {
	mock := &MockRewardsServer{ctrl: ctrl}
	mock.recorder = &MockRewardsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRewardsServer) EXPECT() *MockRewardsServerMockRecorder {
	return m.recorder
}

// BulkClaimRewards mocks base method.
func (m *MockRewardsServer) BulkClaimRewards(arg0 context.Context, arg1 *rewards.BulkClaimRewardsRequest) (*rewards.BulkClaimRewardsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkClaimRewards", arg0, arg1)
	ret0, _ := ret[0].(*rewards.BulkClaimRewardsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkClaimRewards indicates an expected call of BulkClaimRewards.
func (mr *MockRewardsServerMockRecorder) BulkClaimRewards(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkClaimRewards", reflect.TypeOf((*MockRewardsServer)(nil).BulkClaimRewards), arg0, arg1)
}

// ChooseExchangerOrderOption mocks base method.
func (m *MockRewardsServer) ChooseExchangerOrderOption(arg0 context.Context, arg1 *rewards.ChooseExchangerOrderOptionRequest) (*rewards.ChooseExchangerOrderOptionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChooseExchangerOrderOption", arg0, arg1)
	ret0, _ := ret[0].(*rewards.ChooseExchangerOrderOptionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChooseExchangerOrderOption indicates an expected call of ChooseExchangerOrderOption.
func (mr *MockRewardsServerMockRecorder) ChooseExchangerOrderOption(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChooseExchangerOrderOption", reflect.TypeOf((*MockRewardsServer)(nil).ChooseExchangerOrderOption), arg0, arg1)
}

// ClaimReward mocks base method.
func (m *MockRewardsServer) ClaimReward(arg0 context.Context, arg1 *rewards.ClaimRewardRequest) (*rewards.ClaimRewardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClaimReward", arg0, arg1)
	ret0, _ := ret[0].(*rewards.ClaimRewardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClaimReward indicates an expected call of ClaimReward.
func (mr *MockRewardsServerMockRecorder) ClaimReward(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClaimReward", reflect.TypeOf((*MockRewardsServer)(nil).ClaimReward), arg0, arg1)
}

// ConfirmRedemption mocks base method.
func (m *MockRewardsServer) ConfirmRedemption(arg0 context.Context, arg1 *rewards.ConfirmRedemptionRequest) (*rewards.ConfirmRedemptionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmRedemption", arg0, arg1)
	ret0, _ := ret[0].(*rewards.ConfirmRedemptionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmRedemption indicates an expected call of ConfirmRedemption.
func (mr *MockRewardsServerMockRecorder) ConfirmRedemption(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmRedemption", reflect.TypeOf((*MockRewardsServer)(nil).ConfirmRedemption), arg0, arg1)
}

// ForceRetryRewardProcessing mocks base method.
func (m *MockRewardsServer) ForceRetryRewardProcessing(arg0 context.Context, arg1 *rewards.ForceRetryRewardProcessingRequest) (*rewards.ForceRetryRewardProcessingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ForceRetryRewardProcessing", arg0, arg1)
	ret0, _ := ret[0].(*rewards.ForceRetryRewardProcessingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ForceRetryRewardProcessing indicates an expected call of ForceRetryRewardProcessing.
func (mr *MockRewardsServerMockRecorder) ForceRetryRewardProcessing(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ForceRetryRewardProcessing", reflect.TypeOf((*MockRewardsServer)(nil).ForceRetryRewardProcessing), arg0, arg1)
}

// GetActiveOffers mocks base method.
func (m *MockRewardsServer) GetActiveOffers(arg0 context.Context, arg1 *rewards.GetActiveOffersRequest) (*rewards.GetActiveOffersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveOffers", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetActiveOffersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveOffers indicates an expected call of GetActiveOffers.
func (mr *MockRewardsServerMockRecorder) GetActiveOffers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveOffers", reflect.TypeOf((*MockRewardsServer)(nil).GetActiveOffers), arg0, arg1)
}

// GetActiveRedeemedOffers mocks base method.
func (m *MockRewardsServer) GetActiveRedeemedOffers(arg0 context.Context, arg1 *rewards.GetActiveRedeemedOffersRequest) (*rewards.GetActiveRedeemedOffersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveRedeemedOffers", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetActiveRedeemedOffersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveRedeemedOffers indicates an expected call of GetActiveRedeemedOffers.
func (mr *MockRewardsServerMockRecorder) GetActiveRedeemedOffers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveRedeemedOffers", reflect.TypeOf((*MockRewardsServer)(nil).GetActiveRedeemedOffers), arg0, arg1)
}

// GetActiveRewardOffers mocks base method.
func (m *MockRewardsServer) GetActiveRewardOffers(arg0 context.Context, arg1 *rewards.GetActiveRewardOffersRequest) (*rewards.GetActiveRewardOffersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveRewardOffers", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetActiveRewardOffersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveRewardOffers indicates an expected call of GetActiveRewardOffers.
func (mr *MockRewardsServerMockRecorder) GetActiveRewardOffers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveRewardOffers", reflect.TypeOf((*MockRewardsServer)(nil).GetActiveRewardOffers), arg0, arg1)
}

// GetCardOfferDetails mocks base method.
func (m *MockRewardsServer) GetCardOfferDetails(arg0 context.Context, arg1 *rewards.GetCardOfferDetailsRequest) (*rewards.GetCardOfferDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardOfferDetails", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetCardOfferDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardOfferDetails indicates an expected call of GetCardOfferDetails.
func (mr *MockRewardsServerMockRecorder) GetCardOfferDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardOfferDetails", reflect.TypeOf((*MockRewardsServer)(nil).GetCardOfferDetails), arg0, arg1)
}

// GetCardOffers mocks base method.
func (m *MockRewardsServer) GetCardOffers(arg0 context.Context, arg1 *rewards.GetCardOffersRequest) (*rewards.GetCardOffersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardOffers", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetCardOffersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardOffers indicates an expected call of GetCardOffers.
func (mr *MockRewardsServerMockRecorder) GetCardOffers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardOffers", reflect.TypeOf((*MockRewardsServer)(nil).GetCardOffers), arg0, arg1)
}

// GetCardOffersCatalogLayout mocks base method.
func (m *MockRewardsServer) GetCardOffersCatalogLayout(arg0 context.Context, arg1 *rewards.GetCardOffersCatalogLayoutRequest) (*rewards.GetCardOffersCatalogLayoutResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardOffersCatalogLayout", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetCardOffersCatalogLayoutResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardOffersCatalogLayout indicates an expected call of GetCardOffersCatalogLayout.
func (mr *MockRewardsServerMockRecorder) GetCardOffersCatalogLayout(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardOffersCatalogLayout", reflect.TypeOf((*MockRewardsServer)(nil).GetCardOffersCatalogLayout), arg0, arg1)
}

// GetCardOffersTabs mocks base method.
func (m *MockRewardsServer) GetCardOffersTabs(arg0 context.Context, arg1 *rewards.GetCardOffersTabsRequest) (*rewards.GetCardOffersTabsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCardOffersTabs", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetCardOffersTabsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardOffersTabs indicates an expected call of GetCardOffersTabs.
func (mr *MockRewardsServerMockRecorder) GetCardOffersTabs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardOffersTabs", reflect.TypeOf((*MockRewardsServer)(nil).GetCardOffersTabs), arg0, arg1)
}

// GetCatalogOffersAndFilters mocks base method.
func (m *MockRewardsServer) GetCatalogOffersAndFilters(arg0 context.Context, arg1 *rewards.GetCatalogOffersAndFiltersRequest) (*rewards.GetCatalogOffersAndFiltersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCatalogOffersAndFilters", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetCatalogOffersAndFiltersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCatalogOffersAndFilters indicates an expected call of GetCatalogOffersAndFilters.
func (mr *MockRewardsServerMockRecorder) GetCatalogOffersAndFilters(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCatalogOffersAndFilters", reflect.TypeOf((*MockRewardsServer)(nil).GetCatalogOffersAndFilters), arg0, arg1)
}

// GetClaimRewardInputScreen mocks base method.
func (m *MockRewardsServer) GetClaimRewardInputScreen(arg0 context.Context, arg1 *rewards.ClaimRewardInputScreenRequest) (*rewards.ClaimRewardInputScreenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClaimRewardInputScreen", arg0, arg1)
	ret0, _ := ret[0].(*rewards.ClaimRewardInputScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClaimRewardInputScreen indicates an expected call of GetClaimRewardInputScreen.
func (mr *MockRewardsServerMockRecorder) GetClaimRewardInputScreen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClaimRewardInputScreen", reflect.TypeOf((*MockRewardsServer)(nil).GetClaimRewardInputScreen), arg0, arg1)
}

// GetClaimedRewardDetails mocks base method.
func (m *MockRewardsServer) GetClaimedRewardDetails(arg0 context.Context, arg1 *rewards.GetClaimedRewardDetailsRequest) (*rewards.GetClaimedRewardDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClaimedRewardDetails", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetClaimedRewardDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClaimedRewardDetails indicates an expected call of GetClaimedRewardDetails.
func (mr *MockRewardsServerMockRecorder) GetClaimedRewardDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClaimedRewardDetails", reflect.TypeOf((*MockRewardsServer)(nil).GetClaimedRewardDetails), arg0, arg1)
}

// GetConvertFiCoinsOfferRedemptionScreen mocks base method.
func (m *MockRewardsServer) GetConvertFiCoinsOfferRedemptionScreen(arg0 context.Context, arg1 *rewards.GetConvertFiCoinsOfferRedemptionScreenRequest) (*rewards.GetConvertFiCoinsOfferRedemptionScreenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConvertFiCoinsOfferRedemptionScreen", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetConvertFiCoinsOfferRedemptionScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConvertFiCoinsOfferRedemptionScreen indicates an expected call of GetConvertFiCoinsOfferRedemptionScreen.
func (mr *MockRewardsServerMockRecorder) GetConvertFiCoinsOfferRedemptionScreen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConvertFiCoinsOfferRedemptionScreen", reflect.TypeOf((*MockRewardsServer)(nil).GetConvertFiCoinsOfferRedemptionScreen), arg0, arg1)
}

// GetDynamicUrlForWebPageScreen mocks base method.
func (m *MockRewardsServer) GetDynamicUrlForWebPageScreen(arg0 context.Context, arg1 *rewards.GetDynamicUrlForWebPageScreenRequest) (*rewards.GetDynamicUrlForWebPageScreenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDynamicUrlForWebPageScreen", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetDynamicUrlForWebPageScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDynamicUrlForWebPageScreen indicates an expected call of GetDynamicUrlForWebPageScreen.
func (mr *MockRewardsServerMockRecorder) GetDynamicUrlForWebPageScreen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDynamicUrlForWebPageScreen", reflect.TypeOf((*MockRewardsServer)(nil).GetDynamicUrlForWebPageScreen), arg0, arg1)
}

// GetEarnedRewards mocks base method.
func (m *MockRewardsServer) GetEarnedRewards(arg0 context.Context, arg1 *rewards.GetEarnedRewardsRequest) (*rewards.GetEarnedRewardsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEarnedRewards", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetEarnedRewardsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEarnedRewards indicates an expected call of GetEarnedRewards.
func (mr *MockRewardsServerMockRecorder) GetEarnedRewards(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEarnedRewards", reflect.TypeOf((*MockRewardsServer)(nil).GetEarnedRewards), arg0, arg1)
}

// GetEarnedRewardsHistory mocks base method.
func (m *MockRewardsServer) GetEarnedRewardsHistory(arg0 context.Context, arg1 *rewards.GetEarnedRewardsHistoryRequest) (*rewards.GetEarnedRewardsHistoryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEarnedRewardsHistory", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetEarnedRewardsHistoryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEarnedRewardsHistory indicates an expected call of GetEarnedRewardsHistory.
func (mr *MockRewardsServerMockRecorder) GetEarnedRewardsHistory(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEarnedRewardsHistory", reflect.TypeOf((*MockRewardsServer)(nil).GetEarnedRewardsHistory), arg0, arg1)
}

// GetExchangerOfferById mocks base method.
func (m *MockRewardsServer) GetExchangerOfferById(arg0 context.Context, arg1 *rewards.GetExchangerOfferByIdRequest) (*rewards.GetExchangerOfferByIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangerOfferById", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetExchangerOfferByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOfferById indicates an expected call of GetExchangerOfferById.
func (mr *MockRewardsServerMockRecorder) GetExchangerOfferById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOfferById", reflect.TypeOf((*MockRewardsServer)(nil).GetExchangerOfferById), arg0, arg1)
}

// GetExchangerOrderInputScreen mocks base method.
func (m *MockRewardsServer) GetExchangerOrderInputScreen(arg0 context.Context, arg1 *rewards.GetExchangerOrderInputScreenRequest) (*rewards.GetExchangerOrderInputScreenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangerOrderInputScreen", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetExchangerOrderInputScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOrderInputScreen indicates an expected call of GetExchangerOrderInputScreen.
func (mr *MockRewardsServerMockRecorder) GetExchangerOrderInputScreen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOrderInputScreen", reflect.TypeOf((*MockRewardsServer)(nil).GetExchangerOrderInputScreen), arg0, arg1)
}

// GetExchangerOrdersForActor mocks base method.
func (m *MockRewardsServer) GetExchangerOrdersForActor(arg0 context.Context, arg1 *rewards.GetExchangerOrdersForActorRequest) (*rewards.GetExchangerOrdersForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangerOrdersForActor", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetExchangerOrdersForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExchangerOrdersForActor indicates an expected call of GetExchangerOrdersForActor.
func (mr *MockRewardsServerMockRecorder) GetExchangerOrdersForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangerOrdersForActor", reflect.TypeOf((*MockRewardsServer)(nil).GetExchangerOrdersForActor), arg0, arg1)
}

// GetExpiredRedeemedOffers mocks base method.
func (m *MockRewardsServer) GetExpiredRedeemedOffers(arg0 context.Context, arg1 *rewards.GetExpiredRedeemedOffersRequest) (*rewards.GetExpiredRedeemedOffersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExpiredRedeemedOffers", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetExpiredRedeemedOffersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExpiredRedeemedOffers indicates an expected call of GetExpiredRedeemedOffers.
func (mr *MockRewardsServerMockRecorder) GetExpiredRedeemedOffers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExpiredRedeemedOffers", reflect.TypeOf((*MockRewardsServer)(nil).GetExpiredRedeemedOffers), arg0, arg1)
}

// GetMyRewardsScreenDetails mocks base method.
func (m *MockRewardsServer) GetMyRewardsScreenDetails(arg0 context.Context, arg1 *rewards.GetMyRewardsScreenDetailsRequest) (*rewards.GetMyRewardsScreenDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyRewardsScreenDetails", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetMyRewardsScreenDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyRewardsScreenDetails indicates an expected call of GetMyRewardsScreenDetails.
func (mr *MockRewardsServerMockRecorder) GetMyRewardsScreenDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyRewardsScreenDetails", reflect.TypeOf((*MockRewardsServer)(nil).GetMyRewardsScreenDetails), arg0, arg1)
}

// GetOfferCatalogV1 mocks base method.
func (m *MockRewardsServer) GetOfferCatalogV1(arg0 context.Context, arg1 *rewards.GetOfferCatalogV1Request) (*rewards.GetOfferCatalogV1Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOfferCatalogV1", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetOfferCatalogV1Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOfferCatalogV1 indicates an expected call of GetOfferCatalogV1.
func (mr *MockRewardsServerMockRecorder) GetOfferCatalogV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOfferCatalogV1", reflect.TypeOf((*MockRewardsServer)(nil).GetOfferCatalogV1), arg0, arg1)
}

// GetOfferDetailsById mocks base method.
func (m *MockRewardsServer) GetOfferDetailsById(arg0 context.Context, arg1 *rewards.GetOfferDetailsByIdRequest) (*rewards.GetOfferDetailsByIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOfferDetailsById", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetOfferDetailsByIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOfferDetailsById indicates an expected call of GetOfferDetailsById.
func (mr *MockRewardsServerMockRecorder) GetOfferDetailsById(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOfferDetailsById", reflect.TypeOf((*MockRewardsServer)(nil).GetOfferDetailsById), arg0, arg1)
}

// GetOffersCatalogPage mocks base method.
func (m *MockRewardsServer) GetOffersCatalogPage(arg0 context.Context, arg1 *rewards.GetOffersCatalogPageRequest) (*rewards.GetOffersCatalogPageResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOffersCatalogPage", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetOffersCatalogPageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOffersCatalogPage indicates an expected call of GetOffersCatalogPage.
func (mr *MockRewardsServerMockRecorder) GetOffersCatalogPage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOffersCatalogPage", reflect.TypeOf((*MockRewardsServer)(nil).GetOffersCatalogPage), arg0, arg1)
}

// GetOffersCatalogPageVersionToRender mocks base method.
func (m *MockRewardsServer) GetOffersCatalogPageVersionToRender(arg0 context.Context, arg1 *rewards.GetOffersCatalogPageVersionToRenderRequest) (*rewards.GetOffersCatalogPageVersionToRenderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOffersCatalogPageVersionToRender", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetOffersCatalogPageVersionToRenderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOffersCatalogPageVersionToRender indicates an expected call of GetOffersCatalogPageVersionToRender.
func (mr *MockRewardsServerMockRecorder) GetOffersCatalogPageVersionToRender(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOffersCatalogPageVersionToRender", reflect.TypeOf((*MockRewardsServer)(nil).GetOffersCatalogPageVersionToRender), arg0, arg1)
}

// GetRedeemOfferInputScreen mocks base method.
func (m *MockRewardsServer) GetRedeemOfferInputScreen(arg0 context.Context, arg1 *rewards.RedeemOfferInputScreenRequest) (*rewards.RedeemOfferInputScreenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedeemOfferInputScreen", arg0, arg1)
	ret0, _ := ret[0].(*rewards.RedeemOfferInputScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedeemOfferInputScreen indicates an expected call of GetRedeemOfferInputScreen.
func (mr *MockRewardsServerMockRecorder) GetRedeemOfferInputScreen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedeemOfferInputScreen", reflect.TypeOf((*MockRewardsServer)(nil).GetRedeemOfferInputScreen), arg0, arg1)
}

// GetRedeemedOfferDetailsRedirectionInfo mocks base method.
func (m *MockRewardsServer) GetRedeemedOfferDetailsRedirectionInfo(arg0 context.Context, arg1 *rewards.GetRedeemedOfferDetailsRedirectionInfoRequest) (*rewards.GetRedeemedOfferDetailsRedirectionInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedeemedOfferDetailsRedirectionInfo", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetRedeemedOfferDetailsRedirectionInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedeemedOfferDetailsRedirectionInfo indicates an expected call of GetRedeemedOfferDetailsRedirectionInfo.
func (mr *MockRewardsServerMockRecorder) GetRedeemedOfferDetailsRedirectionInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedeemedOfferDetailsRedirectionInfo", reflect.TypeOf((*MockRewardsServer)(nil).GetRedeemedOfferDetailsRedirectionInfo), arg0, arg1)
}

// GetRedeemedOffersCatalogLayout mocks base method.
func (m *MockRewardsServer) GetRedeemedOffersCatalogLayout(arg0 context.Context, arg1 *rewards.GetRedeemedOffersCatalogLayoutRequest) (*rewards.GetRedeemedOffersCatalogLayoutResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedeemedOffersCatalogLayout", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetRedeemedOffersCatalogLayoutResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedeemedOffersCatalogLayout indicates an expected call of GetRedeemedOffersCatalogLayout.
func (mr *MockRewardsServerMockRecorder) GetRedeemedOffersCatalogLayout(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedeemedOffersCatalogLayout", reflect.TypeOf((*MockRewardsServer)(nil).GetRedeemedOffersCatalogLayout), arg0, arg1)
}

// GetRedeemedOffersV1 mocks base method.
func (m *MockRewardsServer) GetRedeemedOffersV1(arg0 context.Context, arg1 *rewards.GetRedeemedOffersV1Request) (*rewards.GetRedeemedOffersV1Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRedeemedOffersV1", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetRedeemedOffersV1Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRedeemedOffersV1 indicates an expected call of GetRedeemedOffersV1.
func (mr *MockRewardsServerMockRecorder) GetRedeemedOffersV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRedeemedOffersV1", reflect.TypeOf((*MockRewardsServer)(nil).GetRedeemedOffersV1), arg0, arg1)
}

// GetRewardDigest mocks base method.
func (m *MockRewardsServer) GetRewardDigest(arg0 context.Context, arg1 *rewards.GetRewardDigestRequest) (*rewards.GetRewardDigestResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRewardDigest", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetRewardDigestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRewardDigest indicates an expected call of GetRewardDigest.
func (mr *MockRewardsServerMockRecorder) GetRewardDigest(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardDigest", reflect.TypeOf((*MockRewardsServer)(nil).GetRewardDigest), arg0, arg1)
}

// GetRewardOfferDetails mocks base method.
func (m *MockRewardsServer) GetRewardOfferDetails(arg0 context.Context, arg1 *rewards.GetRewardOfferDetailsRequest) (*rewards.GetRewardOfferDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRewardOfferDetails", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetRewardOfferDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRewardOfferDetails indicates an expected call of GetRewardOfferDetails.
func (mr *MockRewardsServerMockRecorder) GetRewardOfferDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardOfferDetails", reflect.TypeOf((*MockRewardsServer)(nil).GetRewardOfferDetails), arg0, arg1)
}

// GetRewardSummary mocks base method.
func (m *MockRewardsServer) GetRewardSummary(arg0 context.Context, arg1 *rewards.GetRewardSummaryRequest) (*rewards.GetRewardSummaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRewardSummary", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetRewardSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRewardSummary indicates an expected call of GetRewardSummary.
func (mr *MockRewardsServerMockRecorder) GetRewardSummary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardSummary", reflect.TypeOf((*MockRewardsServer)(nil).GetRewardSummary), arg0, arg1)
}

// GetRewardsAndOffersForHome mocks base method.
func (m *MockRewardsServer) GetRewardsAndOffersForHome(arg0 context.Context, arg1 *rewards.GetRewardsAndOffersForHomeRequest) (*rewards.GetRewardsAndOffersForHomeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRewardsAndOffersForHome", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetRewardsAndOffersForHomeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRewardsAndOffersForHome indicates an expected call of GetRewardsAndOffersForHome.
func (mr *MockRewardsServerMockRecorder) GetRewardsAndOffersForHome(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardsAndOffersForHome", reflect.TypeOf((*MockRewardsServer)(nil).GetRewardsAndOffersForHome), arg0, arg1)
}

// GetRewardsAndOffersWidget mocks base method.
func (m *MockRewardsServer) GetRewardsAndOffersWidget(arg0 context.Context, arg1 *rewards.GetRewardsAndOffersWidgetRequest) (*rewards.GetRewardsAndOffersWidgetResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRewardsAndOffersWidget", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetRewardsAndOffersWidgetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRewardsAndOffersWidget indicates an expected call of GetRewardsAndOffersWidget.
func (mr *MockRewardsServerMockRecorder) GetRewardsAndOffersWidget(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardsAndOffersWidget", reflect.TypeOf((*MockRewardsServer)(nil).GetRewardsAndOffersWidget), arg0, arg1)
}

// GetRewardsForActorV1 mocks base method.
func (m *MockRewardsServer) GetRewardsForActorV1(arg0 context.Context, arg1 *rewards.GetRewardsForActorV1Request) (*rewards.GetRewardsForActorV1Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRewardsForActorV1", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetRewardsForActorV1Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRewardsForActorV1 indicates an expected call of GetRewardsForActorV1.
func (mr *MockRewardsServerMockRecorder) GetRewardsForActorV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRewardsForActorV1", reflect.TypeOf((*MockRewardsServer)(nil).GetRewardsForActorV1), arg0, arg1)
}

// GetUserDetailsInputOfferRedemptionScreen mocks base method.
func (m *MockRewardsServer) GetUserDetailsInputOfferRedemptionScreen(arg0 context.Context, arg1 *rewards.GetUserDetailsInputOfferRedemptionScreenRequest) (*rewards.GetUserDetailsInputOfferRedemptionScreenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDetailsInputOfferRedemptionScreen", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetUserDetailsInputOfferRedemptionScreenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDetailsInputOfferRedemptionScreen indicates an expected call of GetUserDetailsInputOfferRedemptionScreen.
func (mr *MockRewardsServerMockRecorder) GetUserDetailsInputOfferRedemptionScreen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDetailsInputOfferRedemptionScreen", reflect.TypeOf((*MockRewardsServer)(nil).GetUserDetailsInputOfferRedemptionScreen), arg0, arg1)
}

// GetWaysToEarnRewardDetailScreen mocks base method.
func (m *MockRewardsServer) GetWaysToEarnRewardDetailScreen(arg0 context.Context, arg1 *rewards.GetWaysToEarnRewardDetailRequest) (*rewards.GetWaysToEarnRewardDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWaysToEarnRewardDetailScreen", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetWaysToEarnRewardDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWaysToEarnRewardDetailScreen indicates an expected call of GetWaysToEarnRewardDetailScreen.
func (mr *MockRewardsServerMockRecorder) GetWaysToEarnRewardDetailScreen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWaysToEarnRewardDetailScreen", reflect.TypeOf((*MockRewardsServer)(nil).GetWaysToEarnRewardDetailScreen), arg0, arg1)
}

// GetWaysToEarnRewardsScreen mocks base method.
func (m *MockRewardsServer) GetWaysToEarnRewardsScreen(arg0 context.Context, arg1 *rewards.GetWaysToEarnRewardsRequest) (*rewards.GetWaysToEarnRewardsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWaysToEarnRewardsScreen", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetWaysToEarnRewardsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWaysToEarnRewardsScreen indicates an expected call of GetWaysToEarnRewardsScreen.
func (mr *MockRewardsServerMockRecorder) GetWaysToEarnRewardsScreen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWaysToEarnRewardsScreen", reflect.TypeOf((*MockRewardsServer)(nil).GetWaysToEarnRewardsScreen), arg0, arg1)
}

// GetWaysToEarnRewardsScreenVersionToRender mocks base method.
func (m *MockRewardsServer) GetWaysToEarnRewardsScreenVersionToRender(arg0 context.Context, arg1 *rewards.GetWaysToEarnRewardsScreenVersionToRenderRequest) (*rewards.GetWaysToEarnRewardsScreenVersionToRenderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWaysToEarnRewardsScreenVersionToRender", arg0, arg1)
	ret0, _ := ret[0].(*rewards.GetWaysToEarnRewardsScreenVersionToRenderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWaysToEarnRewardsScreenVersionToRender indicates an expected call of GetWaysToEarnRewardsScreenVersionToRender.
func (mr *MockRewardsServerMockRecorder) GetWaysToEarnRewardsScreenVersionToRender(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWaysToEarnRewardsScreenVersionToRender", reflect.TypeOf((*MockRewardsServer)(nil).GetWaysToEarnRewardsScreenVersionToRender), arg0, arg1)
}

// InitiateRedemption mocks base method.
func (m *MockRewardsServer) InitiateRedemption(arg0 context.Context, arg1 *rewards.InitiateRedemptionRequest) (*rewards.InitiateRedemptionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateRedemption", arg0, arg1)
	ret0, _ := ret[0].(*rewards.InitiateRedemptionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateRedemption indicates an expected call of InitiateRedemption.
func (mr *MockRewardsServerMockRecorder) InitiateRedemption(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateRedemption", reflect.TypeOf((*MockRewardsServer)(nil).InitiateRedemption), arg0, arg1)
}

// RedeemExchangerOffer mocks base method.
func (m *MockRewardsServer) RedeemExchangerOffer(arg0 context.Context, arg1 *rewards.RedeemExchangerOfferRequest) (*rewards.RedeemExchangerOfferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RedeemExchangerOffer", arg0, arg1)
	ret0, _ := ret[0].(*rewards.RedeemExchangerOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RedeemExchangerOffer indicates an expected call of RedeemExchangerOffer.
func (mr *MockRewardsServerMockRecorder) RedeemExchangerOffer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RedeemExchangerOffer", reflect.TypeOf((*MockRewardsServer)(nil).RedeemExchangerOffer), arg0, arg1)
}

// SubmitExchangerOrderUserInput mocks base method.
func (m *MockRewardsServer) SubmitExchangerOrderUserInput(arg0 context.Context, arg1 *rewards.SubmitExchangerOrderUserInputRequest) (*rewards.SubmitExchangerOrderUserInputResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitExchangerOrderUserInput", arg0, arg1)
	ret0, _ := ret[0].(*rewards.SubmitExchangerOrderUserInputResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitExchangerOrderUserInput indicates an expected call of SubmitExchangerOrderUserInput.
func (mr *MockRewardsServerMockRecorder) SubmitExchangerOrderUserInput(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitExchangerOrderUserInput", reflect.TypeOf((*MockRewardsServer)(nil).SubmitExchangerOrderUserInput), arg0, arg1)
}

// MockUnsafeRewardsServer is a mock of UnsafeRewardsServer interface.
type MockUnsafeRewardsServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeRewardsServerMockRecorder
}

// MockUnsafeRewardsServerMockRecorder is the mock recorder for MockUnsafeRewardsServer.
type MockUnsafeRewardsServerMockRecorder struct {
	mock *MockUnsafeRewardsServer
}

// NewMockUnsafeRewardsServer creates a new mock instance.
func NewMockUnsafeRewardsServer(ctrl *gomock.Controller) *MockUnsafeRewardsServer {
	mock := &MockUnsafeRewardsServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeRewardsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeRewardsServer) EXPECT() *MockUnsafeRewardsServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedRewardsServer mocks base method.
func (m *MockUnsafeRewardsServer) mustEmbedUnimplementedRewardsServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedRewardsServer")
}

// mustEmbedUnimplementedRewardsServer indicates an expected call of mustEmbedUnimplementedRewardsServer.
func (mr *MockUnsafeRewardsServerMockRecorder) mustEmbedUnimplementedRewardsServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedRewardsServer", reflect.TypeOf((*MockUnsafeRewardsServer)(nil).mustEmbedUnimplementedRewardsServer))
}
