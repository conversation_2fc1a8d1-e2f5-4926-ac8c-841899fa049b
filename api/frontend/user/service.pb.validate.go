// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/user/service.proto

package user

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	accounts "github.com/epifi/gamma/api/accounts"

	analytics "github.com/epifi/gamma/api/frontend/analytics"

	typesv2 "github.com/epifi/gamma/api/typesv2"

	upi "github.com/epifi/gamma/api/frontend/account/upi"

	user_activity "github.com/epifi/gamma/api/frontend/user/user_activity"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = accounts.Type(0)

	_ = analytics.AnalyticsScreenName(0)

	_ = typesv2.AddressType(0)

	_ = upi.PinSetState(0)

	_ = user_activity.UserActivity(0)
)

// Validate checks the field values on GetFeatureBenefitsScreenOptionsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetFeatureBenefitsScreenOptionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFeatureBenefitsScreenOptionsRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetFeatureBenefitsScreenOptionsRequestMultiError, or nil if none found.
func (m *GetFeatureBenefitsScreenOptionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFeatureBenefitsScreenOptionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFeatureBenefitsScreenOptionsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFeatureBenefitsScreenOptionsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFeatureBenefitsScreenOptionsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntryPoint

	if len(errors) > 0 {
		return GetFeatureBenefitsScreenOptionsRequestMultiError(errors)
	}

	return nil
}

// GetFeatureBenefitsScreenOptionsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetFeatureBenefitsScreenOptionsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFeatureBenefitsScreenOptionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFeatureBenefitsScreenOptionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFeatureBenefitsScreenOptionsRequestMultiError) AllErrors() []error { return m }

// GetFeatureBenefitsScreenOptionsRequestValidationError is the validation
// error returned by GetFeatureBenefitsScreenOptionsRequest.Validate if the
// designated constraints aren't met.
type GetFeatureBenefitsScreenOptionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFeatureBenefitsScreenOptionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFeatureBenefitsScreenOptionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFeatureBenefitsScreenOptionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFeatureBenefitsScreenOptionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFeatureBenefitsScreenOptionsRequestValidationError) ErrorName() string {
	return "GetFeatureBenefitsScreenOptionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFeatureBenefitsScreenOptionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFeatureBenefitsScreenOptionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFeatureBenefitsScreenOptionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFeatureBenefitsScreenOptionsRequestValidationError{}

// Validate checks the field values on GetFeatureBenefitsScreenOptionsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetFeatureBenefitsScreenOptionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetFeatureBenefitsScreenOptionsResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetFeatureBenefitsScreenOptionsResponseMultiError, or nil if none found.
func (m *GetFeatureBenefitsScreenOptionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFeatureBenefitsScreenOptionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFeatureBenefitsScreenOptionsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFeatureBenefitsScreenOptionsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFeatureBenefitsScreenOptionsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeatureBenefitsScreenOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFeatureBenefitsScreenOptionsResponseValidationError{
					field:  "FeatureBenefitsScreenOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFeatureBenefitsScreenOptionsResponseValidationError{
					field:  "FeatureBenefitsScreenOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeatureBenefitsScreenOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFeatureBenefitsScreenOptionsResponseValidationError{
				field:  "FeatureBenefitsScreenOptions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFeatureBenefitsScreenOptionsResponseMultiError(errors)
	}

	return nil
}

// GetFeatureBenefitsScreenOptionsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetFeatureBenefitsScreenOptionsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetFeatureBenefitsScreenOptionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFeatureBenefitsScreenOptionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFeatureBenefitsScreenOptionsResponseMultiError) AllErrors() []error { return m }

// GetFeatureBenefitsScreenOptionsResponseValidationError is the validation
// error returned by GetFeatureBenefitsScreenOptionsResponse.Validate if the
// designated constraints aren't met.
type GetFeatureBenefitsScreenOptionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFeatureBenefitsScreenOptionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFeatureBenefitsScreenOptionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFeatureBenefitsScreenOptionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFeatureBenefitsScreenOptionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFeatureBenefitsScreenOptionsResponseValidationError) ErrorName() string {
	return "GetFeatureBenefitsScreenOptionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetFeatureBenefitsScreenOptionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFeatureBenefitsScreenOptionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFeatureBenefitsScreenOptionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFeatureBenefitsScreenOptionsResponseValidationError{}

// Validate checks the field values on GetProfileSettingPageSectionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetProfileSettingPageSectionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetProfileSettingPageSectionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetProfileSettingPageSectionRequestMultiError, or nil if none found.
func (m *GetProfileSettingPageSectionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProfileSettingPageSectionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetProfileSettingPageSectionRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetProfileSettingPageSectionRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetProfileSettingPageSectionRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetProfileSettingPageSectionRequestMultiError(errors)
	}

	return nil
}

// GetProfileSettingPageSectionRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetProfileSettingPageSectionRequest.ValidateAll() if the designated
// constraints aren't met.
type GetProfileSettingPageSectionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProfileSettingPageSectionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProfileSettingPageSectionRequestMultiError) AllErrors() []error { return m }

// GetProfileSettingPageSectionRequestValidationError is the validation error
// returned by GetProfileSettingPageSectionRequest.Validate if the designated
// constraints aren't met.
type GetProfileSettingPageSectionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProfileSettingPageSectionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetProfileSettingPageSectionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetProfileSettingPageSectionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetProfileSettingPageSectionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetProfileSettingPageSectionRequestValidationError) ErrorName() string {
	return "GetProfileSettingPageSectionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetProfileSettingPageSectionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProfileSettingPageSectionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProfileSettingPageSectionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProfileSettingPageSectionRequestValidationError{}

// Validate checks the field values on SetCallLanguagePreferencesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SetCallLanguagePreferencesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetCallLanguagePreferencesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SetCallLanguagePreferencesRequestMultiError, or nil if none found.
func (m *SetCallLanguagePreferencesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetCallLanguagePreferencesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetCallLanguagePreferencesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetCallLanguagePreferencesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetCallLanguagePreferencesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntryPoint

	if all {
		switch v := interface{}(m.GetPreferences()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetCallLanguagePreferencesRequestValidationError{
					field:  "Preferences",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetCallLanguagePreferencesRequestValidationError{
					field:  "Preferences",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreferences()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetCallLanguagePreferencesRequestValidationError{
				field:  "Preferences",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SetCallLanguagePreferencesRequestMultiError(errors)
	}

	return nil
}

// SetCallLanguagePreferencesRequestMultiError is an error wrapping multiple
// validation errors returned by
// SetCallLanguagePreferencesRequest.ValidateAll() if the designated
// constraints aren't met.
type SetCallLanguagePreferencesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetCallLanguagePreferencesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetCallLanguagePreferencesRequestMultiError) AllErrors() []error { return m }

// SetCallLanguagePreferencesRequestValidationError is the validation error
// returned by SetCallLanguagePreferencesRequest.Validate if the designated
// constraints aren't met.
type SetCallLanguagePreferencesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetCallLanguagePreferencesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetCallLanguagePreferencesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetCallLanguagePreferencesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetCallLanguagePreferencesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetCallLanguagePreferencesRequestValidationError) ErrorName() string {
	return "SetCallLanguagePreferencesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetCallLanguagePreferencesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetCallLanguagePreferencesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetCallLanguagePreferencesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetCallLanguagePreferencesRequestValidationError{}

// Validate checks the field values on SetCallLanguagePreferencesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SetCallLanguagePreferencesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetCallLanguagePreferencesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SetCallLanguagePreferencesResponseMultiError, or nil if none found.
func (m *SetCallLanguagePreferencesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SetCallLanguagePreferencesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetCallLanguagePreferencesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetCallLanguagePreferencesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetCallLanguagePreferencesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetCallLanguagePreferencesResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetCallLanguagePreferencesResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetCallLanguagePreferencesResponseValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDesc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetCallLanguagePreferencesResponseValidationError{
					field:  "Desc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetCallLanguagePreferencesResponseValidationError{
					field:  "Desc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDesc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetCallLanguagePreferencesResponseValidationError{
				field:  "Desc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEditPrefInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetCallLanguagePreferencesResponseValidationError{
					field:  "EditPrefInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetCallLanguagePreferencesResponseValidationError{
					field:  "EditPrefInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEditPrefInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetCallLanguagePreferencesResponseValidationError{
				field:  "EditPrefInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInfoIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetCallLanguagePreferencesResponseValidationError{
					field:  "InfoIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetCallLanguagePreferencesResponseValidationError{
					field:  "InfoIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetCallLanguagePreferencesResponseValidationError{
				field:  "InfoIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SetCallLanguagePreferencesResponseMultiError(errors)
	}

	return nil
}

// SetCallLanguagePreferencesResponseMultiError is an error wrapping multiple
// validation errors returned by
// SetCallLanguagePreferencesResponse.ValidateAll() if the designated
// constraints aren't met.
type SetCallLanguagePreferencesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetCallLanguagePreferencesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetCallLanguagePreferencesResponseMultiError) AllErrors() []error { return m }

// SetCallLanguagePreferencesResponseValidationError is the validation error
// returned by SetCallLanguagePreferencesResponse.Validate if the designated
// constraints aren't met.
type SetCallLanguagePreferencesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetCallLanguagePreferencesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetCallLanguagePreferencesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetCallLanguagePreferencesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetCallLanguagePreferencesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetCallLanguagePreferencesResponseValidationError) ErrorName() string {
	return "SetCallLanguagePreferencesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SetCallLanguagePreferencesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetCallLanguagePreferencesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetCallLanguagePreferencesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetCallLanguagePreferencesResponseValidationError{}

// Validate checks the field values on GetProfileSettingPageSectionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetProfileSettingPageSectionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetProfileSettingPageSectionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetProfileSettingPageSectionResponseMultiError, or nil if none found.
func (m *GetProfileSettingPageSectionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProfileSettingPageSectionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetProfileSettingPageSectionResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetProfileSettingPageSectionResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetProfileSettingPageSectionResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccountClosureText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetProfileSettingPageSectionResponseValidationError{
					field:  "AccountClosureText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetProfileSettingPageSectionResponseValidationError{
					field:  "AccountClosureText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountClosureText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetProfileSettingPageSectionResponseValidationError{
				field:  "AccountClosureText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccountClosureHyperlinks()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetProfileSettingPageSectionResponseValidationError{
					field:  "AccountClosureHyperlinks",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetProfileSettingPageSectionResponseValidationError{
					field:  "AccountClosureHyperlinks",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountClosureHyperlinks()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetProfileSettingPageSectionResponseValidationError{
				field:  "AccountClosureHyperlinks",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetProfileSettingPageSectionResponseMultiError(errors)
	}

	return nil
}

// GetProfileSettingPageSectionResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetProfileSettingPageSectionResponse.ValidateAll() if the designated
// constraints aren't met.
type GetProfileSettingPageSectionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProfileSettingPageSectionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProfileSettingPageSectionResponseMultiError) AllErrors() []error { return m }

// GetProfileSettingPageSectionResponseValidationError is the validation error
// returned by GetProfileSettingPageSectionResponse.Validate if the designated
// constraints aren't met.
type GetProfileSettingPageSectionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProfileSettingPageSectionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetProfileSettingPageSectionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetProfileSettingPageSectionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetProfileSettingPageSectionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetProfileSettingPageSectionResponseValidationError) ErrorName() string {
	return "GetProfileSettingPageSectionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetProfileSettingPageSectionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProfileSettingPageSectionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProfileSettingPageSectionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProfileSettingPageSectionResponseValidationError{}

// Validate checks the field values on UpdateDOBForDedupeRetryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateDOBForDedupeRetryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateDOBForDedupeRetryRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateDOBForDedupeRetryRequestMultiError, or nil if none found.
func (m *UpdateDOBForDedupeRetryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateDOBForDedupeRetryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateDOBForDedupeRetryRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateDOBForDedupeRetryRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateDOBForDedupeRetryRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDateOfBirth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateDOBForDedupeRetryRequestValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateDOBForDedupeRetryRequestValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateOfBirth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateDOBForDedupeRetryRequestValidationError{
				field:  "DateOfBirth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateDOBForDedupeRetryRequestMultiError(errors)
	}

	return nil
}

// UpdateDOBForDedupeRetryRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateDOBForDedupeRetryRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateDOBForDedupeRetryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateDOBForDedupeRetryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateDOBForDedupeRetryRequestMultiError) AllErrors() []error { return m }

// UpdateDOBForDedupeRetryRequestValidationError is the validation error
// returned by UpdateDOBForDedupeRetryRequest.Validate if the designated
// constraints aren't met.
type UpdateDOBForDedupeRetryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateDOBForDedupeRetryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateDOBForDedupeRetryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateDOBForDedupeRetryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateDOBForDedupeRetryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateDOBForDedupeRetryRequestValidationError) ErrorName() string {
	return "UpdateDOBForDedupeRetryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateDOBForDedupeRetryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateDOBForDedupeRetryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateDOBForDedupeRetryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateDOBForDedupeRetryRequestValidationError{}

// Validate checks the field values on UpdateDOBForDedupeRetryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateDOBForDedupeRetryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateDOBForDedupeRetryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateDOBForDedupeRetryResponseMultiError, or nil if none found.
func (m *UpdateDOBForDedupeRetryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateDOBForDedupeRetryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateDOBForDedupeRetryResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateDOBForDedupeRetryResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateDOBForDedupeRetryResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateDOBForDedupeRetryResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateDOBForDedupeRetryResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateDOBForDedupeRetryResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaxDobDedupeAttempts

	// no validation rules for CurrentDobDedupeAttempts

	if len(errors) > 0 {
		return UpdateDOBForDedupeRetryResponseMultiError(errors)
	}

	return nil
}

// UpdateDOBForDedupeRetryResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateDOBForDedupeRetryResponse.ValidateAll()
// if the designated constraints aren't met.
type UpdateDOBForDedupeRetryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateDOBForDedupeRetryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateDOBForDedupeRetryResponseMultiError) AllErrors() []error { return m }

// UpdateDOBForDedupeRetryResponseValidationError is the validation error
// returned by UpdateDOBForDedupeRetryResponse.Validate if the designated
// constraints aren't met.
type UpdateDOBForDedupeRetryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateDOBForDedupeRetryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateDOBForDedupeRetryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateDOBForDedupeRetryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateDOBForDedupeRetryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateDOBForDedupeRetryResponseValidationError) ErrorName() string {
	return "UpdateDOBForDedupeRetryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateDOBForDedupeRetryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateDOBForDedupeRetryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateDOBForDedupeRetryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateDOBForDedupeRetryResponseValidationError{}

// Validate checks the field values on GetDebitCardNameRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDebitCardNameRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDebitCardNameRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDebitCardNameRequestMultiError, or nil if none found.
func (m *GetDebitCardNameRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDebitCardNameRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDebitCardNameRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDebitCardNameRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDebitCardNameRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDebitCardNameRequestMultiError(errors)
	}

	return nil
}

// GetDebitCardNameRequestMultiError is an error wrapping multiple validation
// errors returned by GetDebitCardNameRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDebitCardNameRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDebitCardNameRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDebitCardNameRequestMultiError) AllErrors() []error { return m }

// GetDebitCardNameRequestValidationError is the validation error returned by
// GetDebitCardNameRequest.Validate if the designated constraints aren't met.
type GetDebitCardNameRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDebitCardNameRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDebitCardNameRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDebitCardNameRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDebitCardNameRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDebitCardNameRequestValidationError) ErrorName() string {
	return "GetDebitCardNameRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDebitCardNameRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDebitCardNameRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDebitCardNameRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDebitCardNameRequestValidationError{}

// Validate checks the field values on GetDebitCardNameResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDebitCardNameResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDebitCardNameResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDebitCardNameResponseMultiError, or nil if none found.
func (m *GetDebitCardNameResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDebitCardNameResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDebitCardNameResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDebitCardNameResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDebitCardNameResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDebitCardName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDebitCardNameResponseValidationError{
					field:  "DebitCardName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDebitCardNameResponseValidationError{
					field:  "DebitCardName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDebitCardName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDebitCardNameResponseValidationError{
				field:  "DebitCardName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDebitCardNameResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDebitCardNameResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDebitCardNameResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDebitCardNameResponseMultiError(errors)
	}

	return nil
}

// GetDebitCardNameResponseMultiError is an error wrapping multiple validation
// errors returned by GetDebitCardNameResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDebitCardNameResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDebitCardNameResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDebitCardNameResponseMultiError) AllErrors() []error { return m }

// GetDebitCardNameResponseValidationError is the validation error returned by
// GetDebitCardNameResponse.Validate if the designated constraints aren't met.
type GetDebitCardNameResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDebitCardNameResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDebitCardNameResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDebitCardNameResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDebitCardNameResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDebitCardNameResponseValidationError) ErrorName() string {
	return "GetDebitCardNameResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDebitCardNameResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDebitCardNameResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDebitCardNameResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDebitCardNameResponseValidationError{}

// Validate checks the field values on AddAddressRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddAddressRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddAddressRequestMultiError, or nil if none found.
func (m *AddAddressRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddAddressRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddAddressRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddAddressRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddAddressRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddAddressRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddAddressRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddAddressRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	if m.GetAddress() == nil {
		err := AddAddressRequestValidationError{
			field:  "Address",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddAddressRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddAddressRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddAddressRequestValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LocationToken

	if len(errors) > 0 {
		return AddAddressRequestMultiError(errors)
	}

	return nil
}

// AddAddressRequestMultiError is an error wrapping multiple validation errors
// returned by AddAddressRequest.ValidateAll() if the designated constraints
// aren't met.
type AddAddressRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddAddressRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddAddressRequestMultiError) AllErrors() []error { return m }

// AddAddressRequestValidationError is the validation error returned by
// AddAddressRequest.Validate if the designated constraints aren't met.
type AddAddressRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddAddressRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddAddressRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddAddressRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddAddressRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddAddressRequestValidationError) ErrorName() string {
	return "AddAddressRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddAddressRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddAddressRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddAddressRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddAddressRequestValidationError{}

// Validate checks the field values on AddAddressResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddAddressResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddAddressResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddAddressResponseMultiError, or nil if none found.
func (m *AddAddressResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddAddressResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddAddressResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddAddressResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddAddressResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddAddressResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddAddressResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddAddressResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddAddressResponseMultiError(errors)
	}

	return nil
}

// AddAddressResponseMultiError is an error wrapping multiple validation errors
// returned by AddAddressResponse.ValidateAll() if the designated constraints
// aren't met.
type AddAddressResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddAddressResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddAddressResponseMultiError) AllErrors() []error { return m }

// AddAddressResponseValidationError is the validation error returned by
// AddAddressResponse.Validate if the designated constraints aren't met.
type AddAddressResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddAddressResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddAddressResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddAddressResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddAddressResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddAddressResponseValidationError) ErrorName() string {
	return "AddAddressResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddAddressResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddAddressResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddAddressResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddAddressResponseValidationError{}

// Validate checks the field values on UpdateAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAddressRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAddressRequestMultiError, or nil if none found.
func (m *UpdateAddressRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAddressRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAddressRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAddressRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAddressRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAddressRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAddressRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAddressRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	// no validation rules for Id

	if m.GetAddress() == nil {
		err := UpdateAddressRequestValidationError{
			field:  "Address",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAddressRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAddressRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAddressRequestValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateAddressRequestMultiError(errors)
	}

	return nil
}

// UpdateAddressRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateAddressRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateAddressRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAddressRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAddressRequestMultiError) AllErrors() []error { return m }

// UpdateAddressRequestValidationError is the validation error returned by
// UpdateAddressRequest.Validate if the designated constraints aren't met.
type UpdateAddressRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAddressRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAddressRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAddressRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAddressRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAddressRequestValidationError) ErrorName() string {
	return "UpdateAddressRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAddressRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAddressRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAddressRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAddressRequestValidationError{}

// Validate checks the field values on UpdateAddressResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAddressResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAddressResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAddressResponseMultiError, or nil if none found.
func (m *UpdateAddressResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAddressResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAddressResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAddressResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAddressResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAddressResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAddressResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAddressResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAddressResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAddressResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAddressResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateAddressResponseMultiError(errors)
	}

	return nil
}

// UpdateAddressResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateAddressResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateAddressResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAddressResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAddressResponseMultiError) AllErrors() []error { return m }

// UpdateAddressResponseValidationError is the validation error returned by
// UpdateAddressResponse.Validate if the designated constraints aren't met.
type UpdateAddressResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAddressResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAddressResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAddressResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAddressResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAddressResponseValidationError) ErrorName() string {
	return "UpdateAddressResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAddressResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAddressResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAddressResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAddressResponseValidationError{}

// Validate checks the field values on GetAddressRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetAddressRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAddressRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAddressRequestMultiError, or nil if none found.
func (m *GetAddressRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAddressRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddressRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddressRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddressRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddressRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddressRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddressRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	if len(errors) > 0 {
		return GetAddressRequestMultiError(errors)
	}

	return nil
}

// GetAddressRequestMultiError is an error wrapping multiple validation errors
// returned by GetAddressRequest.ValidateAll() if the designated constraints
// aren't met.
type GetAddressRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAddressRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAddressRequestMultiError) AllErrors() []error { return m }

// GetAddressRequestValidationError is the validation error returned by
// GetAddressRequest.Validate if the designated constraints aren't met.
type GetAddressRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAddressRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAddressRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAddressRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAddressRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAddressRequestValidationError) ErrorName() string {
	return "GetAddressRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAddressRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAddressRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAddressRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAddressRequestValidationError{}

// Validate checks the field values on GetAddressResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAddressResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAddressResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAddressResponseMultiError, or nil if none found.
func (m *GetAddressResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAddressResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddressResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddressResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddressResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAddresses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAddressResponseValidationError{
						field:  fmt.Sprintf("Addresses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAddressResponseValidationError{
						field:  fmt.Sprintf("Addresses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAddressResponseValidationError{
					field:  fmt.Sprintf("Addresses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddressResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddressResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddressResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAddressResponseMultiError(errors)
	}

	return nil
}

// GetAddressResponseMultiError is an error wrapping multiple validation errors
// returned by GetAddressResponse.ValidateAll() if the designated constraints
// aren't met.
type GetAddressResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAddressResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAddressResponseMultiError) AllErrors() []error { return m }

// GetAddressResponseValidationError is the validation error returned by
// GetAddressResponse.Validate if the designated constraints aren't met.
type GetAddressResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAddressResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAddressResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAddressResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAddressResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAddressResponseValidationError) ErrorName() string {
	return "GetAddressResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAddressResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAddressResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAddressResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAddressResponseValidationError{}

// Validate checks the field values on GetCardDeliveryAddressesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCardDeliveryAddressesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardDeliveryAddressesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCardDeliveryAddressesRequestMultiError, or nil if none found.
func (m *GetCardDeliveryAddressesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardDeliveryAddressesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardDeliveryAddressesRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardDeliveryAddressesRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardDeliveryAddressesRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardDeliveryAddressesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardDeliveryAddressesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardDeliveryAddressesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AddressFlow

	if len(errors) > 0 {
		return GetCardDeliveryAddressesRequestMultiError(errors)
	}

	return nil
}

// GetCardDeliveryAddressesRequestMultiError is an error wrapping multiple
// validation errors returned by GetCardDeliveryAddressesRequest.ValidateAll()
// if the designated constraints aren't met.
type GetCardDeliveryAddressesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardDeliveryAddressesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardDeliveryAddressesRequestMultiError) AllErrors() []error { return m }

// GetCardDeliveryAddressesRequestValidationError is the validation error
// returned by GetCardDeliveryAddressesRequest.Validate if the designated
// constraints aren't met.
type GetCardDeliveryAddressesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardDeliveryAddressesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardDeliveryAddressesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardDeliveryAddressesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardDeliveryAddressesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardDeliveryAddressesRequestValidationError) ErrorName() string {
	return "GetCardDeliveryAddressesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardDeliveryAddressesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardDeliveryAddressesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardDeliveryAddressesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardDeliveryAddressesRequestValidationError{}

// Validate checks the field values on GetCardDeliveryAddressesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCardDeliveryAddressesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCardDeliveryAddressesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCardDeliveryAddressesResponseMultiError, or nil if none found.
func (m *GetCardDeliveryAddressesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCardDeliveryAddressesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardDeliveryAddressesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardDeliveryAddressesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardDeliveryAddressesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAddresses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCardDeliveryAddressesResponseValidationError{
						field:  fmt.Sprintf("Addresses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCardDeliveryAddressesResponseValidationError{
						field:  fmt.Sprintf("Addresses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCardDeliveryAddressesResponseValidationError{
					field:  fmt.Sprintf("Addresses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetAddressesWithType() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCardDeliveryAddressesResponseValidationError{
						field:  fmt.Sprintf("AddressesWithType[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCardDeliveryAddressesResponseValidationError{
						field:  fmt.Sprintf("AddressesWithType[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCardDeliveryAddressesResponseValidationError{
					field:  fmt.Sprintf("AddressesWithType[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for EnableAddressUpdate

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCardDeliveryAddressesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCardDeliveryAddressesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCardDeliveryAddressesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCardDeliveryAddressesResponseMultiError(errors)
	}

	return nil
}

// GetCardDeliveryAddressesResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetCardDeliveryAddressesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCardDeliveryAddressesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCardDeliveryAddressesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCardDeliveryAddressesResponseMultiError) AllErrors() []error { return m }

// GetCardDeliveryAddressesResponseValidationError is the validation error
// returned by GetCardDeliveryAddressesResponse.Validate if the designated
// constraints aren't met.
type GetCardDeliveryAddressesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCardDeliveryAddressesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCardDeliveryAddressesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCardDeliveryAddressesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCardDeliveryAddressesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCardDeliveryAddressesResponseValidationError) ErrorName() string {
	return "GetCardDeliveryAddressesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCardDeliveryAddressesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCardDeliveryAddressesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCardDeliveryAddressesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCardDeliveryAddressesResponseValidationError{}

// Validate checks the field values on AddressInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddressInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddressInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddressInfoMultiError, or
// nil if none found.
func (m *AddressInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AddressInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressInfoValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressInfoValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressInfoValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddressInfoMultiError(errors)
	}

	return nil
}

// AddressInfoMultiError is an error wrapping multiple validation errors
// returned by AddressInfo.ValidateAll() if the designated constraints aren't met.
type AddressInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddressInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddressInfoMultiError) AllErrors() []error { return m }

// AddressInfoValidationError is the validation error returned by
// AddressInfo.Validate if the designated constraints aren't met.
type AddressInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddressInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddressInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddressInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddressInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddressInfoValidationError) ErrorName() string { return "AddressInfoValidationError" }

// Error satisfies the builtin error interface
func (e AddressInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddressInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddressInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddressInfoValidationError{}

// Validate checks the field values on GetPinCodeDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPinCodeDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPinCodeDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPinCodeDetailsRequestMultiError, or nil if none found.
func (m *GetPinCodeDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPinCodeDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPinCodeDetailsRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPinCodeDetailsRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPinCodeDetailsRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPinCodeDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPinCodeDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPinCodeDetailsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Pincode

	if len(errors) > 0 {
		return GetPinCodeDetailsRequestMultiError(errors)
	}

	return nil
}

// GetPinCodeDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetPinCodeDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPinCodeDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPinCodeDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPinCodeDetailsRequestMultiError) AllErrors() []error { return m }

// GetPinCodeDetailsRequestValidationError is the validation error returned by
// GetPinCodeDetailsRequest.Validate if the designated constraints aren't met.
type GetPinCodeDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPinCodeDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPinCodeDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPinCodeDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPinCodeDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPinCodeDetailsRequestValidationError) ErrorName() string {
	return "GetPinCodeDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPinCodeDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPinCodeDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPinCodeDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPinCodeDetailsRequestValidationError{}

// Validate checks the field values on GetPinCodeDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPinCodeDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPinCodeDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPinCodeDetailsResponseMultiError, or nil if none found.
func (m *GetPinCodeDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPinCodeDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPinCodeDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPinCodeDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPinCodeDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for City

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPinCodeDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPinCodeDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPinCodeDetailsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPinCodeDetailsResponseMultiError(errors)
	}

	return nil
}

// GetPinCodeDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetPinCodeDetailsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetPinCodeDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPinCodeDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPinCodeDetailsResponseMultiError) AllErrors() []error { return m }

// GetPinCodeDetailsResponseValidationError is the validation error returned by
// GetPinCodeDetailsResponse.Validate if the designated constraints aren't met.
type GetPinCodeDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPinCodeDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPinCodeDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPinCodeDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPinCodeDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPinCodeDetailsResponseValidationError) ErrorName() string {
	return "GetPinCodeDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPinCodeDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPinCodeDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPinCodeDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPinCodeDetailsResponseValidationError{}

// Validate checks the field values on UpdateMotherFatherNameRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateMotherFatherNameRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateMotherFatherNameRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateMotherFatherNameRequestMultiError, or nil if none found.
func (m *UpdateMotherFatherNameRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateMotherFatherNameRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateMotherFatherNameRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateMotherFatherNameRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateMotherFatherNameRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateMotherFatherNameRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateMotherFatherNameRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateMotherFatherNameRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MotherName

	// no validation rules for FatherName

	if len(errors) > 0 {
		return UpdateMotherFatherNameRequestMultiError(errors)
	}

	return nil
}

// UpdateMotherFatherNameRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateMotherFatherNameRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateMotherFatherNameRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateMotherFatherNameRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateMotherFatherNameRequestMultiError) AllErrors() []error { return m }

// UpdateMotherFatherNameRequestValidationError is the validation error
// returned by UpdateMotherFatherNameRequest.Validate if the designated
// constraints aren't met.
type UpdateMotherFatherNameRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateMotherFatherNameRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateMotherFatherNameRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateMotherFatherNameRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateMotherFatherNameRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateMotherFatherNameRequestValidationError) ErrorName() string {
	return "UpdateMotherFatherNameRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateMotherFatherNameRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateMotherFatherNameRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateMotherFatherNameRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateMotherFatherNameRequestValidationError{}

// Validate checks the field values on UpdateMotherFatherNameResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateMotherFatherNameResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateMotherFatherNameResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateMotherFatherNameResponseMultiError, or nil if none found.
func (m *UpdateMotherFatherNameResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateMotherFatherNameResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateMotherFatherNameResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateMotherFatherNameResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateMotherFatherNameResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateMotherFatherNameResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateMotherFatherNameResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateMotherFatherNameResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateMotherFatherNameResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateMotherFatherNameResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateMotherFatherNameResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateMotherFatherNameResponseMultiError(errors)
	}

	return nil
}

// UpdateMotherFatherNameResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateMotherFatherNameResponse.ValidateAll()
// if the designated constraints aren't met.
type UpdateMotherFatherNameResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateMotherFatherNameResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateMotherFatherNameResponseMultiError) AllErrors() []error { return m }

// UpdateMotherFatherNameResponseValidationError is the validation error
// returned by UpdateMotherFatherNameResponse.Validate if the designated
// constraints aren't met.
type UpdateMotherFatherNameResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateMotherFatherNameResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateMotherFatherNameResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateMotherFatherNameResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateMotherFatherNameResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateMotherFatherNameResponseValidationError) ErrorName() string {
	return "UpdateMotherFatherNameResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateMotherFatherNameResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateMotherFatherNameResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateMotherFatherNameResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateMotherFatherNameResponseValidationError{}

// Validate checks the field values on GetUserSessionDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserSessionDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserSessionDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserSessionDetailsRequestMultiError, or nil if none found.
func (m *GetUserSessionDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserSessionDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserSessionDetailsRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserSessionDetailsRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserSessionDetailsRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserSessionDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserSessionDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserSessionDetailsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserSessionDetailsRequestMultiError(errors)
	}

	return nil
}

// GetUserSessionDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by GetUserSessionDetailsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetUserSessionDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserSessionDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserSessionDetailsRequestMultiError) AllErrors() []error { return m }

// GetUserSessionDetailsRequestValidationError is the validation error returned
// by GetUserSessionDetailsRequest.Validate if the designated constraints
// aren't met.
type GetUserSessionDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserSessionDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserSessionDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserSessionDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserSessionDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserSessionDetailsRequestValidationError) ErrorName() string {
	return "GetUserSessionDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserSessionDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserSessionDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserSessionDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserSessionDetailsRequestValidationError{}

// Validate checks the field values on GetUserSessionDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserSessionDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserSessionDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetUserSessionDetailsResponseMultiError, or nil if none found.
func (m *GetUserSessionDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserSessionDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserSessionDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUserSessionDetailsResponseValidationError{
						field:  fmt.Sprintf("AccountDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUserSessionDetailsResponseValidationError{
						field:  fmt.Sprintf("AccountDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUserSessionDetailsResponseValidationError{
					field:  fmt.Sprintf("AccountDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for FirebaseId

	// no validation rules for FirebaseUserProperties

	if all {
		switch v := interface{}(m.GetDisplayName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponseValidationError{
					field:  "DisplayName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponseValidationError{
					field:  "DisplayName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserSessionDetailsResponseValidationError{
				field:  "DisplayName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserSessionDetailsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUserSessionDetailsResponseValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUserSessionDetailsResponseValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUserSessionDetailsResponseValidationError{
					field:  fmt.Sprintf("Accounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetFeatureMap()))
		i := 0
		for key := range m.GetFeatureMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetFeatureMap()[key]
			_ = val

			// no validation rules for FeatureMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetUserSessionDetailsResponseValidationError{
							field:  fmt.Sprintf("FeatureMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetUserSessionDetailsResponseValidationError{
							field:  fmt.Sprintf("FeatureMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetUserSessionDetailsResponseValidationError{
						field:  fmt.Sprintf("FeatureMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for IsSaOnboarded

	// no validation rules for IsFiLiteUser

	// no validation rules for IsHomeAccessible

	// no validation rules for CurrentFeature

	if all {
		switch v := interface{}(m.GetMsClarityConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponseValidationError{
					field:  "MsClarityConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponseValidationError{
					field:  "MsClarityConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMsClarityConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserSessionDetailsResponseValidationError{
				field:  "MsClarityConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScienapticSmsScannerConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponseValidationError{
					field:  "ScienapticSmsScannerConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponseValidationError{
					field:  "ScienapticSmsScannerConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScienapticSmsScannerConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserSessionDetailsResponseValidationError{
				field:  "ScienapticSmsScannerConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MsClarityId

	if all {
		switch v := interface{}(m.GetCardTabs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponseValidationError{
					field:  "CardTabs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponseValidationError{
					field:  "CardTabs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardTabs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserSessionDetailsResponseValidationError{
				field:  "CardTabs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserSessionDetailsResponseMultiError(errors)
	}

	return nil
}

// GetUserSessionDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by GetUserSessionDetailsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetUserSessionDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserSessionDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserSessionDetailsResponseMultiError) AllErrors() []error { return m }

// GetUserSessionDetailsResponseValidationError is the validation error
// returned by GetUserSessionDetailsResponse.Validate if the designated
// constraints aren't met.
type GetUserSessionDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserSessionDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserSessionDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserSessionDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserSessionDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserSessionDetailsResponseValidationError) ErrorName() string {
	return "GetUserSessionDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserSessionDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserSessionDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserSessionDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserSessionDetailsResponseValidationError{}

// Validate checks the field values on CreateNomineeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateNomineeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNomineeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateNomineeRequestMultiError, or nil if none found.
func (m *CreateNomineeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNomineeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNomineeRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNomineeRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNomineeRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNomineeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNomineeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNomineeRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNominee()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNomineeRequestValidationError{
					field:  "Nominee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNomineeRequestValidationError{
					field:  "Nominee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNominee()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNomineeRequestValidationError{
				field:  "Nominee",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntryPoint

	if all {
		switch v := interface{}(m.GetNomineeV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNomineeRequestValidationError{
					field:  "NomineeV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNomineeRequestValidationError{
					field:  "NomineeV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNomineeV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNomineeRequestValidationError{
				field:  "NomineeV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateNomineeRequestMultiError(errors)
	}

	return nil
}

// CreateNomineeRequestMultiError is an error wrapping multiple validation
// errors returned by CreateNomineeRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateNomineeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNomineeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNomineeRequestMultiError) AllErrors() []error { return m }

// CreateNomineeRequestValidationError is the validation error returned by
// CreateNomineeRequest.Validate if the designated constraints aren't met.
type CreateNomineeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNomineeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNomineeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNomineeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNomineeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNomineeRequestValidationError) ErrorName() string {
	return "CreateNomineeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNomineeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNomineeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNomineeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNomineeRequestValidationError{}

// Validate checks the field values on CreateNomineeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateNomineeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNomineeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateNomineeResponseMultiError, or nil if none found.
func (m *CreateNomineeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNomineeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNomineeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNomineeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNomineeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NomineeId

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNomineeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNomineeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNomineeResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNomineeResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNomineeResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNomineeResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateNomineeResponseMultiError(errors)
	}

	return nil
}

// CreateNomineeResponseMultiError is an error wrapping multiple validation
// errors returned by CreateNomineeResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateNomineeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNomineeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNomineeResponseMultiError) AllErrors() []error { return m }

// CreateNomineeResponseValidationError is the validation error returned by
// CreateNomineeResponse.Validate if the designated constraints aren't met.
type CreateNomineeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNomineeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNomineeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNomineeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNomineeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNomineeResponseValidationError) ErrorName() string {
	return "CreateNomineeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNomineeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNomineeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNomineeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNomineeResponseValidationError{}

// Validate checks the field values on GetNomineesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNomineesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNomineesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNomineesRequestMultiError, or nil if none found.
func (m *GetNomineesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNomineesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNomineesRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNomineesRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNomineesRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNomineesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNomineesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNomineesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FlowType

	if len(errors) > 0 {
		return GetNomineesRequestMultiError(errors)
	}

	return nil
}

// GetNomineesRequestMultiError is an error wrapping multiple validation errors
// returned by GetNomineesRequest.ValidateAll() if the designated constraints
// aren't met.
type GetNomineesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNomineesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNomineesRequestMultiError) AllErrors() []error { return m }

// GetNomineesRequestValidationError is the validation error returned by
// GetNomineesRequest.Validate if the designated constraints aren't met.
type GetNomineesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNomineesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNomineesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNomineesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNomineesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNomineesRequestValidationError) ErrorName() string {
	return "GetNomineesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNomineesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNomineesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNomineesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNomineesRequestValidationError{}

// Validate checks the field values on GetNomineesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNomineesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNomineesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNomineesResponseMultiError, or nil if none found.
func (m *GetNomineesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNomineesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNomineesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNomineesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNomineesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetNominees() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNomineesResponseValidationError{
						field:  fmt.Sprintf("Nominees[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNomineesResponseValidationError{
						field:  fmt.Sprintf("Nominees[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNomineesResponseValidationError{
					field:  fmt.Sprintf("Nominees[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNomineesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNomineesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNomineesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetNomineesV2() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNomineesResponseValidationError{
						field:  fmt.Sprintf("NomineesV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNomineesResponseValidationError{
						field:  fmt.Sprintf("NomineesV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNomineesResponseValidationError{
					field:  fmt.Sprintf("NomineesV2[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetNomineesResponseMultiError(errors)
	}

	return nil
}

// GetNomineesResponseMultiError is an error wrapping multiple validation
// errors returned by GetNomineesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetNomineesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNomineesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNomineesResponseMultiError) AllErrors() []error { return m }

// GetNomineesResponseValidationError is the validation error returned by
// GetNomineesResponse.Validate if the designated constraints aren't met.
type GetNomineesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNomineesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNomineesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNomineesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNomineesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNomineesResponseValidationError) ErrorName() string {
	return "GetNomineesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNomineesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNomineesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNomineesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNomineesResponseValidationError{}

// Validate checks the field values on GetNomineeRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetNomineeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNomineeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNomineeRequestMultiError, or nil if none found.
func (m *GetNomineeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNomineeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNomineeRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNomineeRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNomineeRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNomineeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNomineeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNomineeRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NomineeId

	if len(errors) > 0 {
		return GetNomineeRequestMultiError(errors)
	}

	return nil
}

// GetNomineeRequestMultiError is an error wrapping multiple validation errors
// returned by GetNomineeRequest.ValidateAll() if the designated constraints
// aren't met.
type GetNomineeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNomineeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNomineeRequestMultiError) AllErrors() []error { return m }

// GetNomineeRequestValidationError is the validation error returned by
// GetNomineeRequest.Validate if the designated constraints aren't met.
type GetNomineeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNomineeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNomineeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNomineeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNomineeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNomineeRequestValidationError) ErrorName() string {
	return "GetNomineeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNomineeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNomineeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNomineeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNomineeRequestValidationError{}

// Validate checks the field values on GetNomineeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNomineeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNomineeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNomineeResponseMultiError, or nil if none found.
func (m *GetNomineeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNomineeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNomineeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNomineeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNomineeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNominee()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNomineeResponseValidationError{
					field:  "Nominee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNomineeResponseValidationError{
					field:  "Nominee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNominee()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNomineeResponseValidationError{
				field:  "Nominee",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNomineeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNomineeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNomineeResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNomineeV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNomineeResponseValidationError{
					field:  "NomineeV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNomineeResponseValidationError{
					field:  "NomineeV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNomineeV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNomineeResponseValidationError{
				field:  "NomineeV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNomineeResponseMultiError(errors)
	}

	return nil
}

// GetNomineeResponseMultiError is an error wrapping multiple validation errors
// returned by GetNomineeResponse.ValidateAll() if the designated constraints
// aren't met.
type GetNomineeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNomineeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNomineeResponseMultiError) AllErrors() []error { return m }

// GetNomineeResponseValidationError is the validation error returned by
// GetNomineeResponse.Validate if the designated constraints aren't met.
type GetNomineeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNomineeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNomineeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNomineeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNomineeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNomineeResponseValidationError) ErrorName() string {
	return "GetNomineeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNomineeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNomineeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNomineeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNomineeResponseValidationError{}

// Validate checks the field values on ConfirmCardShippingPreferenceRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ConfirmCardShippingPreferenceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConfirmCardShippingPreferenceRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ConfirmCardShippingPreferenceRequestMultiError, or nil if none found.
func (m *ConfirmCardShippingPreferenceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmCardShippingPreferenceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardShippingPreferenceRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardShippingPreferenceRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardShippingPreferenceRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardShippingPreferenceRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardShippingPreferenceRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardShippingPreferenceRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AddressType

	if len(errors) > 0 {
		return ConfirmCardShippingPreferenceRequestMultiError(errors)
	}

	return nil
}

// ConfirmCardShippingPreferenceRequestMultiError is an error wrapping multiple
// validation errors returned by
// ConfirmCardShippingPreferenceRequest.ValidateAll() if the designated
// constraints aren't met.
type ConfirmCardShippingPreferenceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmCardShippingPreferenceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmCardShippingPreferenceRequestMultiError) AllErrors() []error { return m }

// ConfirmCardShippingPreferenceRequestValidationError is the validation error
// returned by ConfirmCardShippingPreferenceRequest.Validate if the designated
// constraints aren't met.
type ConfirmCardShippingPreferenceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmCardShippingPreferenceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConfirmCardShippingPreferenceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConfirmCardShippingPreferenceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConfirmCardShippingPreferenceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfirmCardShippingPreferenceRequestValidationError) ErrorName() string {
	return "ConfirmCardShippingPreferenceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmCardShippingPreferenceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmCardShippingPreferenceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmCardShippingPreferenceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmCardShippingPreferenceRequestValidationError{}

// Validate checks the field values on ConfirmCardShippingPreferenceResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ConfirmCardShippingPreferenceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConfirmCardShippingPreferenceResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ConfirmCardShippingPreferenceResponseMultiError, or nil if none found.
func (m *ConfirmCardShippingPreferenceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmCardShippingPreferenceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardShippingPreferenceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardShippingPreferenceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardShippingPreferenceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardShippingPreferenceResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardShippingPreferenceResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardShippingPreferenceResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardShippingPreferenceResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardShippingPreferenceResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardShippingPreferenceResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConfirmCardShippingPreferenceResponseMultiError(errors)
	}

	return nil
}

// ConfirmCardShippingPreferenceResponseMultiError is an error wrapping
// multiple validation errors returned by
// ConfirmCardShippingPreferenceResponse.ValidateAll() if the designated
// constraints aren't met.
type ConfirmCardShippingPreferenceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmCardShippingPreferenceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmCardShippingPreferenceResponseMultiError) AllErrors() []error { return m }

// ConfirmCardShippingPreferenceResponseValidationError is the validation error
// returned by ConfirmCardShippingPreferenceResponse.Validate if the
// designated constraints aren't met.
type ConfirmCardShippingPreferenceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmCardShippingPreferenceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConfirmCardShippingPreferenceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConfirmCardShippingPreferenceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConfirmCardShippingPreferenceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfirmCardShippingPreferenceResponseValidationError) ErrorName() string {
	return "ConfirmCardShippingPreferenceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmCardShippingPreferenceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmCardShippingPreferenceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmCardShippingPreferenceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmCardShippingPreferenceResponseValidationError{}

// Validate checks the field values on RecordHashedContactsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecordHashedContactsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordHashedContactsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecordHashedContactsRequestMultiError, or nil if none found.
func (m *RecordHashedContactsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordHashedContactsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordHashedContactsRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordHashedContactsRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordHashedContactsRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordHashedContactsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordHashedContactsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordHashedContactsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetContact()) > 250 {
		err := RecordHashedContactsRequestValidationError{
			field:  "Contact",
			reason: "value must contain no more than 250 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetContact() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RecordHashedContactsRequestValidationError{
						field:  fmt.Sprintf("Contact[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RecordHashedContactsRequestValidationError{
						field:  fmt.Sprintf("Contact[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RecordHashedContactsRequestValidationError{
					field:  fmt.Sprintf("Contact[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RecordHashedContactsRequestMultiError(errors)
	}

	return nil
}

// RecordHashedContactsRequestMultiError is an error wrapping multiple
// validation errors returned by RecordHashedContactsRequest.ValidateAll() if
// the designated constraints aren't met.
type RecordHashedContactsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordHashedContactsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordHashedContactsRequestMultiError) AllErrors() []error { return m }

// RecordHashedContactsRequestValidationError is the validation error returned
// by RecordHashedContactsRequest.Validate if the designated constraints
// aren't met.
type RecordHashedContactsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordHashedContactsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordHashedContactsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordHashedContactsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordHashedContactsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordHashedContactsRequestValidationError) ErrorName() string {
	return "RecordHashedContactsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecordHashedContactsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordHashedContactsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordHashedContactsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordHashedContactsRequestValidationError{}

// Validate checks the field values on RecordHashedContactsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecordHashedContactsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordHashedContactsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecordHashedContactsResponseMultiError, or nil if none found.
func (m *RecordHashedContactsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordHashedContactsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordHashedContactsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordHashedContactsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordHashedContactsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordHashedContactsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordHashedContactsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordHashedContactsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecordHashedContactsResponseMultiError(errors)
	}

	return nil
}

// RecordHashedContactsResponseMultiError is an error wrapping multiple
// validation errors returned by RecordHashedContactsResponse.ValidateAll() if
// the designated constraints aren't met.
type RecordHashedContactsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordHashedContactsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordHashedContactsResponseMultiError) AllErrors() []error { return m }

// RecordHashedContactsResponseValidationError is the validation error returned
// by RecordHashedContactsResponse.Validate if the designated constraints
// aren't met.
type RecordHashedContactsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordHashedContactsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordHashedContactsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordHashedContactsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordHashedContactsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordHashedContactsResponseValidationError) ErrorName() string {
	return "RecordHashedContactsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RecordHashedContactsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordHashedContactsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordHashedContactsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordHashedContactsResponseValidationError{}

// Validate checks the field values on AddressWithType with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddressWithType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddressWithType with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddressWithTypeMultiError, or nil if none found.
func (m *AddressWithType) ValidateAll() error {
	return m.validate(true)
}

func (m *AddressWithType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressWithTypeValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressWithTypeValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressWithTypeValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsEditable

	// no validation rules for IsActive

	if all {
		switch v := interface{}(m.GetInfoText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressWithTypeValidationError{
					field:  "InfoText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressWithTypeValidationError{
					field:  "InfoText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressWithTypeValidationError{
				field:  "InfoText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddressWithTypeMultiError(errors)
	}

	return nil
}

// AddressWithTypeMultiError is an error wrapping multiple validation errors
// returned by AddressWithType.ValidateAll() if the designated constraints
// aren't met.
type AddressWithTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddressWithTypeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddressWithTypeMultiError) AllErrors() []error { return m }

// AddressWithTypeValidationError is the validation error returned by
// AddressWithType.Validate if the designated constraints aren't met.
type AddressWithTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddressWithTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddressWithTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddressWithTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddressWithTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddressWithTypeValidationError) ErrorName() string { return "AddressWithTypeValidationError" }

// Error satisfies the builtin error interface
func (e AddressWithTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddressWithType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddressWithTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddressWithTypeValidationError{}

// Validate checks the field values on SyncContactDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncContactDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncContactDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncContactDetailsRequestMultiError, or nil if none found.
func (m *SyncContactDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncContactDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SyncContactDetailsRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SyncContactDetailsRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SyncContactDetailsRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Cursor

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SyncContactDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SyncContactDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SyncContactDetailsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SyncContactDetailsRequestMultiError(errors)
	}

	return nil
}

// SyncContactDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by SyncContactDetailsRequest.ValidateAll() if the
// designated constraints aren't met.
type SyncContactDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncContactDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncContactDetailsRequestMultiError) AllErrors() []error { return m }

// SyncContactDetailsRequestValidationError is the validation error returned by
// SyncContactDetailsRequest.Validate if the designated constraints aren't met.
type SyncContactDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncContactDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncContactDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncContactDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncContactDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncContactDetailsRequestValidationError) ErrorName() string {
	return "SyncContactDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SyncContactDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncContactDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncContactDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncContactDetailsRequestValidationError{}

// Validate checks the field values on SyncContactDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncContactDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncContactDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncContactDetailsResponseMultiError, or nil if none found.
func (m *SyncContactDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncContactDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SyncContactDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SyncContactDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SyncContactDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetContactDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncContactDetailsResponseValidationError{
						field:  fmt.Sprintf("ContactDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncContactDetailsResponseValidationError{
						field:  fmt.Sprintf("ContactDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncContactDetailsResponseValidationError{
					field:  fmt.Sprintf("ContactDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Cursor

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SyncContactDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SyncContactDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SyncContactDetailsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SyncContactDetailsResponseMultiError(errors)
	}

	return nil
}

// SyncContactDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by SyncContactDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type SyncContactDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncContactDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncContactDetailsResponseMultiError) AllErrors() []error { return m }

// SyncContactDetailsResponseValidationError is the validation error returned
// by SyncContactDetailsResponse.Validate if the designated constraints aren't met.
type SyncContactDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncContactDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncContactDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncContactDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncContactDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncContactDetailsResponseValidationError) ErrorName() string {
	return "SyncContactDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SyncContactDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncContactDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncContactDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncContactDetailsResponseValidationError{}

// Validate checks the field values on GetByPhoneNumberRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetByPhoneNumberRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetByPhoneNumberRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetByPhoneNumberRequestMultiError, or nil if none found.
func (m *GetByPhoneNumberRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetByPhoneNumberRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetByPhoneNumberRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetByPhoneNumberRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetByPhoneNumberRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetByPhoneNumberRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetByPhoneNumberRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetByPhoneNumberRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetByPhoneNumberRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetByPhoneNumberRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetByPhoneNumberRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetByPhoneNumberRequestMultiError(errors)
	}

	return nil
}

// GetByPhoneNumberRequestMultiError is an error wrapping multiple validation
// errors returned by GetByPhoneNumberRequest.ValidateAll() if the designated
// constraints aren't met.
type GetByPhoneNumberRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetByPhoneNumberRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetByPhoneNumberRequestMultiError) AllErrors() []error { return m }

// GetByPhoneNumberRequestValidationError is the validation error returned by
// GetByPhoneNumberRequest.Validate if the designated constraints aren't met.
type GetByPhoneNumberRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetByPhoneNumberRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetByPhoneNumberRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetByPhoneNumberRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetByPhoneNumberRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetByPhoneNumberRequestValidationError) ErrorName() string {
	return "GetByPhoneNumberRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetByPhoneNumberRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetByPhoneNumberRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetByPhoneNumberRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetByPhoneNumberRequestValidationError{}

// Validate checks the field values on GetByPhoneNumberResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetByPhoneNumberResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetByPhoneNumberResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetByPhoneNumberResponseMultiError, or nil if none found.
func (m *GetByPhoneNumberResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetByPhoneNumberResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetByPhoneNumberResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetByPhoneNumberResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetByPhoneNumberResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetChatHead()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetByPhoneNumberResponseValidationError{
					field:  "ChatHead",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetByPhoneNumberResponseValidationError{
					field:  "ChatHead",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChatHead()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetByPhoneNumberResponseValidationError{
				field:  "ChatHead",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetChatHeads() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetByPhoneNumberResponseValidationError{
						field:  fmt.Sprintf("ChatHeads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetByPhoneNumberResponseValidationError{
						field:  fmt.Sprintf("ChatHeads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetByPhoneNumberResponseValidationError{
					field:  fmt.Sprintf("ChatHeads[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetByPhoneNumberResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetByPhoneNumberResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetByPhoneNumberResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetByPhoneNumberResponseMultiError(errors)
	}

	return nil
}

// GetByPhoneNumberResponseMultiError is an error wrapping multiple validation
// errors returned by GetByPhoneNumberResponse.ValidateAll() if the designated
// constraints aren't met.
type GetByPhoneNumberResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetByPhoneNumberResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetByPhoneNumberResponseMultiError) AllErrors() []error { return m }

// GetByPhoneNumberResponseValidationError is the validation error returned by
// GetByPhoneNumberResponse.Validate if the designated constraints aren't met.
type GetByPhoneNumberResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetByPhoneNumberResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetByPhoneNumberResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetByPhoneNumberResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetByPhoneNumberResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetByPhoneNumberResponseValidationError) ErrorName() string {
	return "GetByPhoneNumberResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetByPhoneNumberResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetByPhoneNumberResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetByPhoneNumberResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetByPhoneNumberResponseValidationError{}

// Validate checks the field values on GetLegalNameRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLegalNameRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLegalNameRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLegalNameRequestMultiError, or nil if none found.
func (m *GetLegalNameRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLegalNameRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLegalNameRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLegalNameRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLegalNameRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLegalNameRequestMultiError(errors)
	}

	return nil
}

// GetLegalNameRequestMultiError is an error wrapping multiple validation
// errors returned by GetLegalNameRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLegalNameRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLegalNameRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLegalNameRequestMultiError) AllErrors() []error { return m }

// GetLegalNameRequestValidationError is the validation error returned by
// GetLegalNameRequest.Validate if the designated constraints aren't met.
type GetLegalNameRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLegalNameRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLegalNameRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLegalNameRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLegalNameRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLegalNameRequestValidationError) ErrorName() string {
	return "GetLegalNameRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLegalNameRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLegalNameRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLegalNameRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLegalNameRequestValidationError{}

// Validate checks the field values on GetLegalNameResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLegalNameResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLegalNameResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLegalNameResponseMultiError, or nil if none found.
func (m *GetLegalNameResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLegalNameResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLegalNameResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLegalNameResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLegalNameResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLegalName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLegalNameResponseValidationError{
					field:  "LegalName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLegalNameResponseValidationError{
					field:  "LegalName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLegalName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLegalNameResponseValidationError{
				field:  "LegalName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLegalNameResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLegalNameResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLegalNameResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLegalNameResponseMultiError(errors)
	}

	return nil
}

// GetLegalNameResponseMultiError is an error wrapping multiple validation
// errors returned by GetLegalNameResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLegalNameResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLegalNameResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLegalNameResponseMultiError) AllErrors() []error { return m }

// GetLegalNameResponseValidationError is the validation error returned by
// GetLegalNameResponse.Validate if the designated constraints aren't met.
type GetLegalNameResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLegalNameResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLegalNameResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLegalNameResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLegalNameResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLegalNameResponseValidationError) ErrorName() string {
	return "GetLegalNameResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLegalNameResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLegalNameResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLegalNameResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLegalNameResponseValidationError{}

// Validate checks the field values on ConfirmCardPreferencesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConfirmCardPreferencesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConfirmCardPreferencesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ConfirmCardPreferencesRequestMultiError, or nil if none found.
func (m *ConfirmCardPreferencesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmCardPreferencesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardPreferencesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardPreferencesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardPreferencesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDebitCardName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardPreferencesRequestValidationError{
					field:  "DebitCardName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardPreferencesRequestValidationError{
					field:  "DebitCardName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDebitCardName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardPreferencesRequestValidationError{
				field:  "DebitCardName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SkipNameCheck

	// no validation rules for AddressType

	// no validation rules for PurposeOfSavingsAccount

	if len(errors) > 0 {
		return ConfirmCardPreferencesRequestMultiError(errors)
	}

	return nil
}

// ConfirmCardPreferencesRequestMultiError is an error wrapping multiple
// validation errors returned by ConfirmCardPreferencesRequest.ValidateAll()
// if the designated constraints aren't met.
type ConfirmCardPreferencesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmCardPreferencesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmCardPreferencesRequestMultiError) AllErrors() []error { return m }

// ConfirmCardPreferencesRequestValidationError is the validation error
// returned by ConfirmCardPreferencesRequest.Validate if the designated
// constraints aren't met.
type ConfirmCardPreferencesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmCardPreferencesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConfirmCardPreferencesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConfirmCardPreferencesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConfirmCardPreferencesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfirmCardPreferencesRequestValidationError) ErrorName() string {
	return "ConfirmCardPreferencesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmCardPreferencesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmCardPreferencesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmCardPreferencesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmCardPreferencesRequestValidationError{}

// Validate checks the field values on ConfirmCardPreferencesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConfirmCardPreferencesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConfirmCardPreferencesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ConfirmCardPreferencesResponseMultiError, or nil if none found.
func (m *ConfirmCardPreferencesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmCardPreferencesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardPreferencesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardPreferencesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardPreferencesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardPreferencesResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardPreferencesResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardPreferencesResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmCardPreferencesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmCardPreferencesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmCardPreferencesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConfirmCardPreferencesResponseMultiError(errors)
	}

	return nil
}

// ConfirmCardPreferencesResponseMultiError is an error wrapping multiple
// validation errors returned by ConfirmCardPreferencesResponse.ValidateAll()
// if the designated constraints aren't met.
type ConfirmCardPreferencesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmCardPreferencesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmCardPreferencesResponseMultiError) AllErrors() []error { return m }

// ConfirmCardPreferencesResponseValidationError is the validation error
// returned by ConfirmCardPreferencesResponse.Validate if the designated
// constraints aren't met.
type ConfirmCardPreferencesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmCardPreferencesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConfirmCardPreferencesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConfirmCardPreferencesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConfirmCardPreferencesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfirmCardPreferencesResponseValidationError) ErrorName() string {
	return "ConfirmCardPreferencesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmCardPreferencesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmCardPreferencesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmCardPreferencesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmCardPreferencesResponseValidationError{}

// Validate checks the field values on PostUserActivityRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PostUserActivityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PostUserActivityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PostUserActivityRequestMultiError, or nil if none found.
func (m *PostUserActivityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PostUserActivityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostUserActivityRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostUserActivityRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostUserActivityRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountId

	// no validation rules for UserActivity

	if utf8.RuneCountInString(m.GetDerivedAccountId()) < 1 {
		err := PostUserActivityRequestValidationError{
			field:  "DerivedAccountId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return PostUserActivityRequestMultiError(errors)
	}

	return nil
}

// PostUserActivityRequestMultiError is an error wrapping multiple validation
// errors returned by PostUserActivityRequest.ValidateAll() if the designated
// constraints aren't met.
type PostUserActivityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostUserActivityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostUserActivityRequestMultiError) AllErrors() []error { return m }

// PostUserActivityRequestValidationError is the validation error returned by
// PostUserActivityRequest.Validate if the designated constraints aren't met.
type PostUserActivityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostUserActivityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostUserActivityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostUserActivityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostUserActivityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostUserActivityRequestValidationError) ErrorName() string {
	return "PostUserActivityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PostUserActivityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostUserActivityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostUserActivityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostUserActivityRequestValidationError{}

// Validate checks the field values on PostUserActivityResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PostUserActivityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PostUserActivityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PostUserActivityResponseMultiError, or nil if none found.
func (m *PostUserActivityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PostUserActivityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostUserActivityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostUserActivityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostUserActivityResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostUserActivityResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostUserActivityResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostUserActivityResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PostUserActivityResponseMultiError(errors)
	}

	return nil
}

// PostUserActivityResponseMultiError is an error wrapping multiple validation
// errors returned by PostUserActivityResponse.ValidateAll() if the designated
// constraints aren't met.
type PostUserActivityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostUserActivityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostUserActivityResponseMultiError) AllErrors() []error { return m }

// PostUserActivityResponseValidationError is the validation error returned by
// PostUserActivityResponse.Validate if the designated constraints aren't met.
type PostUserActivityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostUserActivityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostUserActivityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostUserActivityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostUserActivityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostUserActivityResponseValidationError) ErrorName() string {
	return "PostUserActivityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PostUserActivityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostUserActivityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostUserActivityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostUserActivityResponseValidationError{}

// Validate checks the field values on BlockActorRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BlockActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlockActorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BlockActorRequestMultiError, or nil if none found.
func (m *BlockActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BlockActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BlockActorRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BlockActorRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BlockActorRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := BlockActorRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IsSpam

	if len(errors) > 0 {
		return BlockActorRequestMultiError(errors)
	}

	return nil
}

// BlockActorRequestMultiError is an error wrapping multiple validation errors
// returned by BlockActorRequest.ValidateAll() if the designated constraints
// aren't met.
type BlockActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlockActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlockActorRequestMultiError) AllErrors() []error { return m }

// BlockActorRequestValidationError is the validation error returned by
// BlockActorRequest.Validate if the designated constraints aren't met.
type BlockActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlockActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlockActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlockActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlockActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlockActorRequestValidationError) ErrorName() string {
	return "BlockActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BlockActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlockActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlockActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlockActorRequestValidationError{}

// Validate checks the field values on BlockActorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BlockActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlockActorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BlockActorResponseMultiError, or nil if none found.
func (m *BlockActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BlockActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BlockActorResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BlockActorResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BlockActorResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BlockActorResponseMultiError(errors)
	}

	return nil
}

// BlockActorResponseMultiError is an error wrapping multiple validation errors
// returned by BlockActorResponse.ValidateAll() if the designated constraints
// aren't met.
type BlockActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlockActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlockActorResponseMultiError) AllErrors() []error { return m }

// BlockActorResponseValidationError is the validation error returned by
// BlockActorResponse.Validate if the designated constraints aren't met.
type BlockActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlockActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlockActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlockActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlockActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlockActorResponseValidationError) ErrorName() string {
	return "BlockActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BlockActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlockActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlockActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlockActorResponseValidationError{}

// Validate checks the field values on UnblockActorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnblockActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnblockActorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnblockActorRequestMultiError, or nil if none found.
func (m *UnblockActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UnblockActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnblockActorRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnblockActorRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnblockActorRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := UnblockActorRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UnblockActorRequestMultiError(errors)
	}

	return nil
}

// UnblockActorRequestMultiError is an error wrapping multiple validation
// errors returned by UnblockActorRequest.ValidateAll() if the designated
// constraints aren't met.
type UnblockActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnblockActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnblockActorRequestMultiError) AllErrors() []error { return m }

// UnblockActorRequestValidationError is the validation error returned by
// UnblockActorRequest.Validate if the designated constraints aren't met.
type UnblockActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnblockActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnblockActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnblockActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnblockActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnblockActorRequestValidationError) ErrorName() string {
	return "UnblockActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UnblockActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnblockActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnblockActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnblockActorRequestValidationError{}

// Validate checks the field values on UnblockActorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnblockActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnblockActorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnblockActorResponseMultiError, or nil if none found.
func (m *UnblockActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UnblockActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnblockActorResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnblockActorResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnblockActorResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UnblockActorResponseMultiError(errors)
	}

	return nil
}

// UnblockActorResponseMultiError is an error wrapping multiple validation
// errors returned by UnblockActorResponse.ValidateAll() if the designated
// constraints aren't met.
type UnblockActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnblockActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnblockActorResponseMultiError) AllErrors() []error { return m }

// UnblockActorResponseValidationError is the validation error returned by
// UnblockActorResponse.Validate if the designated constraints aren't met.
type UnblockActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnblockActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnblockActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnblockActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnblockActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnblockActorResponseValidationError) ErrorName() string {
	return "UnblockActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UnblockActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnblockActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnblockActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnblockActorResponseValidationError{}

// Validate checks the field values on ReportSpamRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReportSpamRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportSpamRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReportSpamRequestMultiError, or nil if none found.
func (m *ReportSpamRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportSpamRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReportSpamRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReportSpamRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReportSpamRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := ReportSpamRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ReportSpamRequestMultiError(errors)
	}

	return nil
}

// ReportSpamRequestMultiError is an error wrapping multiple validation errors
// returned by ReportSpamRequest.ValidateAll() if the designated constraints
// aren't met.
type ReportSpamRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportSpamRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportSpamRequestMultiError) AllErrors() []error { return m }

// ReportSpamRequestValidationError is the validation error returned by
// ReportSpamRequest.Validate if the designated constraints aren't met.
type ReportSpamRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportSpamRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportSpamRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportSpamRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportSpamRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportSpamRequestValidationError) ErrorName() string {
	return "ReportSpamRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReportSpamRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportSpamRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportSpamRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportSpamRequestValidationError{}

// Validate checks the field values on ReportSpamResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReportSpamResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportSpamResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReportSpamResponseMultiError, or nil if none found.
func (m *ReportSpamResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportSpamResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReportSpamResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReportSpamResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReportSpamResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReportSpamResponseMultiError(errors)
	}

	return nil
}

// ReportSpamResponseMultiError is an error wrapping multiple validation errors
// returned by ReportSpamResponse.ValidateAll() if the designated constraints
// aren't met.
type ReportSpamResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportSpamResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportSpamResponseMultiError) AllErrors() []error { return m }

// ReportSpamResponseValidationError is the validation error returned by
// ReportSpamResponse.Validate if the designated constraints aren't met.
type ReportSpamResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportSpamResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportSpamResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportSpamResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportSpamResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportSpamResponseValidationError) ErrorName() string {
	return "ReportSpamResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReportSpamResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportSpamResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportSpamResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportSpamResponseValidationError{}

// Validate checks the field values on AddAppInstanceIdentifiersRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *AddAppInstanceIdentifiersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddAppInstanceIdentifiersRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AddAppInstanceIdentifiersRequestMultiError, or nil if none found.
func (m *AddAppInstanceIdentifiersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddAppInstanceIdentifiersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := AddAppInstanceIdentifiersRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddAppInstanceIdentifiersRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddAppInstanceIdentifiersRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddAppInstanceIdentifiersRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetIdentifiers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddAppInstanceIdentifiersRequestValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddAppInstanceIdentifiersRequestValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddAppInstanceIdentifiersRequestValidationError{
					field:  fmt.Sprintf("Identifiers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AddAppInstanceIdentifiersRequestMultiError(errors)
	}

	return nil
}

// AddAppInstanceIdentifiersRequestMultiError is an error wrapping multiple
// validation errors returned by
// AddAppInstanceIdentifiersRequest.ValidateAll() if the designated
// constraints aren't met.
type AddAppInstanceIdentifiersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddAppInstanceIdentifiersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddAppInstanceIdentifiersRequestMultiError) AllErrors() []error { return m }

// AddAppInstanceIdentifiersRequestValidationError is the validation error
// returned by AddAppInstanceIdentifiersRequest.Validate if the designated
// constraints aren't met.
type AddAppInstanceIdentifiersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddAppInstanceIdentifiersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddAppInstanceIdentifiersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddAppInstanceIdentifiersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddAppInstanceIdentifiersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddAppInstanceIdentifiersRequestValidationError) ErrorName() string {
	return "AddAppInstanceIdentifiersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddAppInstanceIdentifiersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddAppInstanceIdentifiersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddAppInstanceIdentifiersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddAppInstanceIdentifiersRequestValidationError{}

// Validate checks the field values on AddAppInstanceIdentifiersResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *AddAppInstanceIdentifiersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddAppInstanceIdentifiersResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// AddAppInstanceIdentifiersResponseMultiError, or nil if none found.
func (m *AddAppInstanceIdentifiersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddAppInstanceIdentifiersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddAppInstanceIdentifiersResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddAppInstanceIdentifiersResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddAppInstanceIdentifiersResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddAppInstanceIdentifiersResponseMultiError(errors)
	}

	return nil
}

// AddAppInstanceIdentifiersResponseMultiError is an error wrapping multiple
// validation errors returned by
// AddAppInstanceIdentifiersResponse.ValidateAll() if the designated
// constraints aren't met.
type AddAppInstanceIdentifiersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddAppInstanceIdentifiersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddAppInstanceIdentifiersResponseMultiError) AllErrors() []error { return m }

// AddAppInstanceIdentifiersResponseValidationError is the validation error
// returned by AddAppInstanceIdentifiersResponse.Validate if the designated
// constraints aren't met.
type AddAppInstanceIdentifiersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddAppInstanceIdentifiersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddAppInstanceIdentifiersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddAppInstanceIdentifiersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddAppInstanceIdentifiersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddAppInstanceIdentifiersResponseValidationError) ErrorName() string {
	return "AddAppInstanceIdentifiersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddAppInstanceIdentifiersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddAppInstanceIdentifiersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddAppInstanceIdentifiersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddAppInstanceIdentifiersResponseValidationError{}

// Validate checks the field values on SyncVendorIDsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncVendorIDsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncVendorIDsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncVendorIDsRequestMultiError, or nil if none found.
func (m *SyncVendorIDsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncVendorIDsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SyncVendorIDsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SyncVendorIDsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SyncVendorIDsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetIdentifiers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SyncVendorIDsRequestValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SyncVendorIDsRequestValidationError{
						field:  fmt.Sprintf("Identifiers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SyncVendorIDsRequestValidationError{
					field:  fmt.Sprintf("Identifiers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SyncVendorIDsRequestMultiError(errors)
	}

	return nil
}

// SyncVendorIDsRequestMultiError is an error wrapping multiple validation
// errors returned by SyncVendorIDsRequest.ValidateAll() if the designated
// constraints aren't met.
type SyncVendorIDsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncVendorIDsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncVendorIDsRequestMultiError) AllErrors() []error { return m }

// SyncVendorIDsRequestValidationError is the validation error returned by
// SyncVendorIDsRequest.Validate if the designated constraints aren't met.
type SyncVendorIDsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncVendorIDsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncVendorIDsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncVendorIDsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncVendorIDsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncVendorIDsRequestValidationError) ErrorName() string {
	return "SyncVendorIDsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SyncVendorIDsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncVendorIDsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncVendorIDsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncVendorIDsRequestValidationError{}

// Validate checks the field values on SyncVendorIDsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncVendorIDsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncVendorIDsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncVendorIDsResponseMultiError, or nil if none found.
func (m *SyncVendorIDsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncVendorIDsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SyncVendorIDsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SyncVendorIDsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SyncVendorIDsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SyncVendorIDsResponseMultiError(errors)
	}

	return nil
}

// SyncVendorIDsResponseMultiError is an error wrapping multiple validation
// errors returned by SyncVendorIDsResponse.ValidateAll() if the designated
// constraints aren't met.
type SyncVendorIDsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncVendorIDsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncVendorIDsResponseMultiError) AllErrors() []error { return m }

// SyncVendorIDsResponseValidationError is the validation error returned by
// SyncVendorIDsResponse.Validate if the designated constraints aren't met.
type SyncVendorIDsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncVendorIDsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncVendorIDsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncVendorIDsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncVendorIDsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncVendorIDsResponseValidationError) ErrorName() string {
	return "SyncVendorIDsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SyncVendorIDsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncVendorIDsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncVendorIDsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncVendorIDsResponseValidationError{}

// Validate checks the field values on ValidateExternalAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateExternalAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateExternalAccountRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ValidateExternalAccountRequestMultiError, or nil if none found.
func (m *ValidateExternalAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateExternalAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateExternalAccountRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateExternalAccountRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateExternalAccountRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := utf8.RuneCountInString(m.GetAccountNumber()); l < 5 || l > 32 {
		err := ValidateExternalAccountRequestValidationError{
			field:  "AccountNumber",
			reason: "value length must be between 5 and 32 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetIfscCode()) != 11 {
		err := ValidateExternalAccountRequestValidationError{
			field:  "IfscCode",
			reason: "value length must be 11 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)

	}

	if l := utf8.RuneCountInString(m.GetUserGivenName()); l < 1 || l > 100 {
		err := ValidateExternalAccountRequestValidationError{
			field:  "UserGivenName",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ValidateExternalAccountRequestMultiError(errors)
	}

	return nil
}

// ValidateExternalAccountRequestMultiError is an error wrapping multiple
// validation errors returned by ValidateExternalAccountRequest.ValidateAll()
// if the designated constraints aren't met.
type ValidateExternalAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateExternalAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateExternalAccountRequestMultiError) AllErrors() []error { return m }

// ValidateExternalAccountRequestValidationError is the validation error
// returned by ValidateExternalAccountRequest.Validate if the designated
// constraints aren't met.
type ValidateExternalAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateExternalAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateExternalAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateExternalAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateExternalAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateExternalAccountRequestValidationError) ErrorName() string {
	return "ValidateExternalAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateExternalAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateExternalAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateExternalAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateExternalAccountRequestValidationError{}

// Validate checks the field values on ValidateExternalAccountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateExternalAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateExternalAccountResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ValidateExternalAccountResponseMultiError, or nil if none found.
func (m *ValidateExternalAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateExternalAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateExternalAccountResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateExternalAccountResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateExternalAccountResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateExternalAccountResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateExternalAccountResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateExternalAccountResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return ValidateExternalAccountResponseMultiError(errors)
	}

	return nil
}

// ValidateExternalAccountResponseMultiError is an error wrapping multiple
// validation errors returned by ValidateExternalAccountResponse.ValidateAll()
// if the designated constraints aren't met.
type ValidateExternalAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateExternalAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateExternalAccountResponseMultiError) AllErrors() []error { return m }

// ValidateExternalAccountResponseValidationError is the validation error
// returned by ValidateExternalAccountResponse.Validate if the designated
// constraints aren't met.
type ValidateExternalAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateExternalAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateExternalAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateExternalAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateExternalAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateExternalAccountResponseValidationError) ErrorName() string {
	return "ValidateExternalAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateExternalAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateExternalAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateExternalAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateExternalAccountResponseValidationError{}

// Validate checks the field values on GetExternalAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExternalAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExternalAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetExternalAccountRequestMultiError, or nil if none found.
func (m *GetExternalAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExternalAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExternalAccountRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExternalAccountRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExternalAccountRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetExternalAccountRequestMultiError(errors)
	}

	return nil
}

// GetExternalAccountRequestMultiError is an error wrapping multiple validation
// errors returned by GetExternalAccountRequest.ValidateAll() if the
// designated constraints aren't met.
type GetExternalAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExternalAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExternalAccountRequestMultiError) AllErrors() []error { return m }

// GetExternalAccountRequestValidationError is the validation error returned by
// GetExternalAccountRequest.Validate if the designated constraints aren't met.
type GetExternalAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExternalAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExternalAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExternalAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExternalAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExternalAccountRequestValidationError) ErrorName() string {
	return "GetExternalAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExternalAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExternalAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExternalAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExternalAccountRequestValidationError{}

// Validate checks the field values on GetExternalAccountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExternalAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExternalAccountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetExternalAccountResponseMultiError, or nil if none found.
func (m *GetExternalAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExternalAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExternalAccountResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExternalAccountResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExternalAccountResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountNumber

	// no validation rules for IfscCode

	// no validation rules for AccountHolderName

	// no validation rules for BankLogoUrl

	// no validation rules for Title

	// no validation rules for Line1

	// no validation rules for Line2

	if len(errors) > 0 {
		return GetExternalAccountResponseMultiError(errors)
	}

	return nil
}

// GetExternalAccountResponseMultiError is an error wrapping multiple
// validation errors returned by GetExternalAccountResponse.ValidateAll() if
// the designated constraints aren't met.
type GetExternalAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExternalAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExternalAccountResponseMultiError) AllErrors() []error { return m }

// GetExternalAccountResponseValidationError is the validation error returned
// by GetExternalAccountResponse.Validate if the designated constraints aren't met.
type GetExternalAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExternalAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExternalAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExternalAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExternalAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExternalAccountResponseValidationError) ErrorName() string {
	return "GetExternalAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExternalAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExternalAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExternalAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExternalAccountResponseValidationError{}

// Validate checks the field values on FeatureInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FeatureInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeatureInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FeatureInfoMultiError, or
// nil if none found.
func (m *FeatureInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FeatureInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Enable

	if len(errors) > 0 {
		return FeatureInfoMultiError(errors)
	}

	return nil
}

// FeatureInfoMultiError is an error wrapping multiple validation errors
// returned by FeatureInfo.ValidateAll() if the designated constraints aren't met.
type FeatureInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeatureInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeatureInfoMultiError) AllErrors() []error { return m }

// FeatureInfoValidationError is the validation error returned by
// FeatureInfo.Validate if the designated constraints aren't met.
type FeatureInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeatureInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeatureInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeatureInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeatureInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeatureInfoValidationError) ErrorName() string { return "FeatureInfoValidationError" }

// Error satisfies the builtin error interface
func (e FeatureInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeatureInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeatureInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeatureInfoValidationError{}

// Validate checks the field values on SetUserPreferencesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetUserPreferencesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetUserPreferencesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetUserPreferencesRequestMultiError, or nil if none found.
func (m *SetUserPreferencesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SetUserPreferencesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetUserPreferencesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetUserPreferencesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetUserPreferencesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(m.GetPreferences()) < 1 {
		err := SetUserPreferencesRequestValidationError{
			field:  "Preferences",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetPreferences() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetUserPreferencesRequestValidationError{
						field:  fmt.Sprintf("Preferences[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetUserPreferencesRequestValidationError{
						field:  fmt.Sprintf("Preferences[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetUserPreferencesRequestValidationError{
					field:  fmt.Sprintf("Preferences[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SetUserPreferencesRequestMultiError(errors)
	}

	return nil
}

// SetUserPreferencesRequestMultiError is an error wrapping multiple validation
// errors returned by SetUserPreferencesRequest.ValidateAll() if the
// designated constraints aren't met.
type SetUserPreferencesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetUserPreferencesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetUserPreferencesRequestMultiError) AllErrors() []error { return m }

// SetUserPreferencesRequestValidationError is the validation error returned by
// SetUserPreferencesRequest.Validate if the designated constraints aren't met.
type SetUserPreferencesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetUserPreferencesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetUserPreferencesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetUserPreferencesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetUserPreferencesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetUserPreferencesRequestValidationError) ErrorName() string {
	return "SetUserPreferencesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SetUserPreferencesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetUserPreferencesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetUserPreferencesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetUserPreferencesRequestValidationError{}

// Validate checks the field values on SetUserPreferencesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetUserPreferencesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetUserPreferencesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetUserPreferencesResponseMultiError, or nil if none found.
func (m *SetUserPreferencesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SetUserPreferencesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetUserPreferencesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetUserPreferencesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetUserPreferencesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SetUserPreferencesResponseMultiError(errors)
	}

	return nil
}

// SetUserPreferencesResponseMultiError is an error wrapping multiple
// validation errors returned by SetUserPreferencesResponse.ValidateAll() if
// the designated constraints aren't met.
type SetUserPreferencesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetUserPreferencesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetUserPreferencesResponseMultiError) AllErrors() []error { return m }

// SetUserPreferencesResponseValidationError is the validation error returned
// by SetUserPreferencesResponse.Validate if the designated constraints aren't met.
type SetUserPreferencesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetUserPreferencesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetUserPreferencesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetUserPreferencesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetUserPreferencesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetUserPreferencesResponseValidationError) ErrorName() string {
	return "SetUserPreferencesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SetUserPreferencesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetUserPreferencesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetUserPreferencesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetUserPreferencesResponseValidationError{}

// Validate checks the field values on GetUserPreferencesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserPreferencesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserPreferencesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserPreferencesRequestMultiError, or nil if none found.
func (m *GetUserPreferencesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserPreferencesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserPreferencesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserPreferencesRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserPreferencesRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserPreferencesRequestMultiError(errors)
	}

	return nil
}

// GetUserPreferencesRequestMultiError is an error wrapping multiple validation
// errors returned by GetUserPreferencesRequest.ValidateAll() if the
// designated constraints aren't met.
type GetUserPreferencesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserPreferencesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserPreferencesRequestMultiError) AllErrors() []error { return m }

// GetUserPreferencesRequestValidationError is the validation error returned by
// GetUserPreferencesRequest.Validate if the designated constraints aren't met.
type GetUserPreferencesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserPreferencesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserPreferencesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserPreferencesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserPreferencesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserPreferencesRequestValidationError) ErrorName() string {
	return "GetUserPreferencesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserPreferencesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserPreferencesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserPreferencesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserPreferencesRequestValidationError{}

// Validate checks the field values on GetUserPreferencesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserPreferencesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserPreferencesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserPreferencesResponseMultiError, or nil if none found.
func (m *GetUserPreferencesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserPreferencesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserPreferencesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserPreferencesResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserPreferencesResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetUserPreferences() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUserPreferencesResponseValidationError{
						field:  fmt.Sprintf("UserPreferences[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUserPreferencesResponseValidationError{
						field:  fmt.Sprintf("UserPreferences[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUserPreferencesResponseValidationError{
					field:  fmt.Sprintf("UserPreferences[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetUserPreferencesResponseMultiError(errors)
	}

	return nil
}

// GetUserPreferencesResponseMultiError is an error wrapping multiple
// validation errors returned by GetUserPreferencesResponse.ValidateAll() if
// the designated constraints aren't met.
type GetUserPreferencesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserPreferencesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserPreferencesResponseMultiError) AllErrors() []error { return m }

// GetUserPreferencesResponseValidationError is the validation error returned
// by GetUserPreferencesResponse.Validate if the designated constraints aren't met.
type GetUserPreferencesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserPreferencesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserPreferencesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserPreferencesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserPreferencesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserPreferencesResponseValidationError) ErrorName() string {
	return "GetUserPreferencesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserPreferencesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserPreferencesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserPreferencesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserPreferencesResponseValidationError{}

// Validate checks the field values on GetAccountDetailsByDerivedIdRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAccountDetailsByDerivedIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountDetailsByDerivedIdRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAccountDetailsByDerivedIdRequestMultiError, or nil if none found.
func (m *GetAccountDetailsByDerivedIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountDetailsByDerivedIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountDetailsByDerivedIdRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountDetailsByDerivedIdRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountDetailsByDerivedIdRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DerivedAccountId

	if len(errors) > 0 {
		return GetAccountDetailsByDerivedIdRequestMultiError(errors)
	}

	return nil
}

// GetAccountDetailsByDerivedIdRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAccountDetailsByDerivedIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAccountDetailsByDerivedIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountDetailsByDerivedIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountDetailsByDerivedIdRequestMultiError) AllErrors() []error { return m }

// GetAccountDetailsByDerivedIdRequestValidationError is the validation error
// returned by GetAccountDetailsByDerivedIdRequest.Validate if the designated
// constraints aren't met.
type GetAccountDetailsByDerivedIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountDetailsByDerivedIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountDetailsByDerivedIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountDetailsByDerivedIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountDetailsByDerivedIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountDetailsByDerivedIdRequestValidationError) ErrorName() string {
	return "GetAccountDetailsByDerivedIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountDetailsByDerivedIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountDetailsByDerivedIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountDetailsByDerivedIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountDetailsByDerivedIdRequestValidationError{}

// Validate checks the field values on GetAccountDetailsByDerivedIdResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetAccountDetailsByDerivedIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountDetailsByDerivedIdResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAccountDetailsByDerivedIdResponseMultiError, or nil if none found.
func (m *GetAccountDetailsByDerivedIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountDetailsByDerivedIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountDetailsByDerivedIdResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountDetailsByDerivedIdResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountDetailsByDerivedIdResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountDetailsByDerivedIdResponseValidationError{
					field:  "UserAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountDetailsByDerivedIdResponseValidationError{
					field:  "UserAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountDetailsByDerivedIdResponseValidationError{
				field:  "UserAccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAccountDetailsByDerivedIdResponseMultiError(errors)
	}

	return nil
}

// GetAccountDetailsByDerivedIdResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetAccountDetailsByDerivedIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAccountDetailsByDerivedIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountDetailsByDerivedIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountDetailsByDerivedIdResponseMultiError) AllErrors() []error { return m }

// GetAccountDetailsByDerivedIdResponseValidationError is the validation error
// returned by GetAccountDetailsByDerivedIdResponse.Validate if the designated
// constraints aren't met.
type GetAccountDetailsByDerivedIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountDetailsByDerivedIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountDetailsByDerivedIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountDetailsByDerivedIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountDetailsByDerivedIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountDetailsByDerivedIdResponseValidationError) ErrorName() string {
	return "GetAccountDetailsByDerivedIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountDetailsByDerivedIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountDetailsByDerivedIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountDetailsByDerivedIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountDetailsByDerivedIdResponseValidationError{}

// Validate checks the field values on GetMoEngageIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMoEngageIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMoEngageIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMoEngageIdRequestMultiError, or nil if none found.
func (m *GetMoEngageIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMoEngageIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetMoEngageIdRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMoEngageIdRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMoEngageIdRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMoEngageIdRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMoEngageIdRequestMultiError(errors)
	}

	return nil
}

// GetMoEngageIdRequestMultiError is an error wrapping multiple validation
// errors returned by GetMoEngageIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMoEngageIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMoEngageIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMoEngageIdRequestMultiError) AllErrors() []error { return m }

// GetMoEngageIdRequestValidationError is the validation error returned by
// GetMoEngageIdRequest.Validate if the designated constraints aren't met.
type GetMoEngageIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMoEngageIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMoEngageIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMoEngageIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMoEngageIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMoEngageIdRequestValidationError) ErrorName() string {
	return "GetMoEngageIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMoEngageIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMoEngageIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMoEngageIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMoEngageIdRequestValidationError{}

// Validate checks the field values on GetMoEngageIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetMoEngageIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMoEngageIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMoEngageIdResponseMultiError, or nil if none found.
func (m *GetMoEngageIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMoEngageIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMoEngageIdResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMoEngageIdResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMoEngageIdResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MoengageId

	if len(errors) > 0 {
		return GetMoEngageIdResponseMultiError(errors)
	}

	return nil
}

// GetMoEngageIdResponseMultiError is an error wrapping multiple validation
// errors returned by GetMoEngageIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMoEngageIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMoEngageIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMoEngageIdResponseMultiError) AllErrors() []error { return m }

// GetMoEngageIdResponseValidationError is the validation error returned by
// GetMoEngageIdResponse.Validate if the designated constraints aren't met.
type GetMoEngageIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMoEngageIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMoEngageIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMoEngageIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMoEngageIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMoEngageIdResponseValidationError) ErrorName() string {
	return "GetMoEngageIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMoEngageIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMoEngageIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMoEngageIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMoEngageIdResponseValidationError{}

// Validate checks the field values on UpdateFormDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFormDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFormDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFormDetailsRequestMultiError, or nil if none found.
func (m *UpdateFormDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFormDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := UpdateFormDetailsRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFormDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFormDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFormDetailsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Source

	{
		sorted_keys := make([]string, len(m.GetValues()))
		i := 0
		for key := range m.GetValues() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetValues()[key]
			_ = val

			// no validation rules for Values[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, UpdateFormDetailsRequestValidationError{
							field:  fmt.Sprintf("Values[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, UpdateFormDetailsRequestValidationError{
							field:  fmt.Sprintf("Values[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return UpdateFormDetailsRequestValidationError{
						field:  fmt.Sprintf("Values[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return UpdateFormDetailsRequestMultiError(errors)
	}

	return nil
}

// UpdateFormDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateFormDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateFormDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFormDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFormDetailsRequestMultiError) AllErrors() []error { return m }

// UpdateFormDetailsRequestValidationError is the validation error returned by
// UpdateFormDetailsRequest.Validate if the designated constraints aren't met.
type UpdateFormDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFormDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFormDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFormDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFormDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFormDetailsRequestValidationError) ErrorName() string {
	return "UpdateFormDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFormDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFormDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFormDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFormDetailsRequestValidationError{}

// Validate checks the field values on UpdateFormDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFormDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFormDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFormDetailsResponseMultiError, or nil if none found.
func (m *UpdateFormDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFormDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateFormDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateFormDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateFormDetailsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetFieldInlineError()))
		i := 0
		for key := range m.GetFieldInlineError() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetFieldInlineError()[key]
			_ = val

			// no validation rules for FieldInlineError[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, UpdateFormDetailsResponseValidationError{
							field:  fmt.Sprintf("FieldInlineError[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, UpdateFormDetailsResponseValidationError{
							field:  fmt.Sprintf("FieldInlineError[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return UpdateFormDetailsResponseValidationError{
						field:  fmt.Sprintf("FieldInlineError[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return UpdateFormDetailsResponseMultiError(errors)
	}

	return nil
}

// UpdateFormDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateFormDetailsResponse.ValidateAll() if the
// designated constraints aren't met.
type UpdateFormDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFormDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFormDetailsResponseMultiError) AllErrors() []error { return m }

// UpdateFormDetailsResponseValidationError is the validation error returned by
// UpdateFormDetailsResponse.Validate if the designated constraints aren't met.
type UpdateFormDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFormDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFormDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFormDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFormDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFormDetailsResponseValidationError) ErrorName() string {
	return "UpdateFormDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFormDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFormDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFormDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFormDetailsResponseValidationError{}

// Validate checks the field values on FetchFormDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchFormDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchFormDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchFormDetailsRequestMultiError, or nil if none found.
func (m *FetchFormDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchFormDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := FetchFormDetailsRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchFormDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchFormDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchFormDetailsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Source

	if len(errors) > 0 {
		return FetchFormDetailsRequestMultiError(errors)
	}

	return nil
}

// FetchFormDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by FetchFormDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchFormDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchFormDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchFormDetailsRequestMultiError) AllErrors() []error { return m }

// FetchFormDetailsRequestValidationError is the validation error returned by
// FetchFormDetailsRequest.Validate if the designated constraints aren't met.
type FetchFormDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchFormDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchFormDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchFormDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchFormDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchFormDetailsRequestValidationError) ErrorName() string {
	return "FetchFormDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchFormDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchFormDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchFormDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchFormDetailsRequestValidationError{}

// Validate checks the field values on FetchFormDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchFormDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchFormDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchFormDetailsResponseMultiError, or nil if none found.
func (m *FetchFormDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchFormDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchFormDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchFormDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchFormDetailsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetValues()))
		i := 0
		for key := range m.GetValues() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetValues()[key]
			_ = val

			// no validation rules for Values[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, FetchFormDetailsResponseValidationError{
							field:  fmt.Sprintf("Values[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, FetchFormDetailsResponseValidationError{
							field:  fmt.Sprintf("Values[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return FetchFormDetailsResponseValidationError{
						field:  fmt.Sprintf("Values[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return FetchFormDetailsResponseMultiError(errors)
	}

	return nil
}

// FetchFormDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by FetchFormDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchFormDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchFormDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchFormDetailsResponseMultiError) AllErrors() []error { return m }

// FetchFormDetailsResponseValidationError is the validation error returned by
// FetchFormDetailsResponse.Validate if the designated constraints aren't met.
type FetchFormDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchFormDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchFormDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchFormDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchFormDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchFormDetailsResponseValidationError) ErrorName() string {
	return "FetchFormDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchFormDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchFormDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchFormDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchFormDetailsResponseValidationError{}

// Validate checks the field values on GetAddressEntryScreenFromLocationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetAddressEntryScreenFromLocationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetAddressEntryScreenFromLocationRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetAddressEntryScreenFromLocationRequestMultiError, or nil if none found.
func (m *GetAddressEntryScreenFromLocationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAddressEntryScreenFromLocationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := GetAddressEntryScreenFromLocationRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddressEntryScreenFromLocationRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddressEntryScreenFromLocationRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddressEntryScreenFromLocationRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLatLng()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddressEntryScreenFromLocationRequestValidationError{
					field:  "LatLng",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddressEntryScreenFromLocationRequestValidationError{
					field:  "LatLng",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatLng()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddressEntryScreenFromLocationRequestValidationError{
				field:  "LatLng",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	if len(errors) > 0 {
		return GetAddressEntryScreenFromLocationRequestMultiError(errors)
	}

	return nil
}

// GetAddressEntryScreenFromLocationRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetAddressEntryScreenFromLocationRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAddressEntryScreenFromLocationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAddressEntryScreenFromLocationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAddressEntryScreenFromLocationRequestMultiError) AllErrors() []error { return m }

// GetAddressEntryScreenFromLocationRequestValidationError is the validation
// error returned by GetAddressEntryScreenFromLocationRequest.Validate if the
// designated constraints aren't met.
type GetAddressEntryScreenFromLocationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAddressEntryScreenFromLocationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAddressEntryScreenFromLocationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAddressEntryScreenFromLocationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAddressEntryScreenFromLocationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAddressEntryScreenFromLocationRequestValidationError) ErrorName() string {
	return "GetAddressEntryScreenFromLocationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAddressEntryScreenFromLocationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAddressEntryScreenFromLocationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAddressEntryScreenFromLocationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAddressEntryScreenFromLocationRequestValidationError{}

// Validate checks the field values on
// GetAddressEntryScreenFromLocationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetAddressEntryScreenFromLocationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetAddressEntryScreenFromLocationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetAddressEntryScreenFromLocationResponseMultiError, or nil if none found.
func (m *GetAddressEntryScreenFromLocationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAddressEntryScreenFromLocationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddressEntryScreenFromLocationResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddressEntryScreenFromLocationResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddressEntryScreenFromLocationResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAddressEntryScreenFromLocationResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAddressEntryScreenFromLocationResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAddressEntryScreenFromLocationResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAddressEntryScreenFromLocationResponseMultiError(errors)
	}

	return nil
}

// GetAddressEntryScreenFromLocationResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetAddressEntryScreenFromLocationResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAddressEntryScreenFromLocationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAddressEntryScreenFromLocationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAddressEntryScreenFromLocationResponseMultiError) AllErrors() []error { return m }

// GetAddressEntryScreenFromLocationResponseValidationError is the validation
// error returned by GetAddressEntryScreenFromLocationResponse.Validate if the
// designated constraints aren't met.
type GetAddressEntryScreenFromLocationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAddressEntryScreenFromLocationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAddressEntryScreenFromLocationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAddressEntryScreenFromLocationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAddressEntryScreenFromLocationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAddressEntryScreenFromLocationResponseValidationError) ErrorName() string {
	return "GetAddressEntryScreenFromLocationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAddressEntryScreenFromLocationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAddressEntryScreenFromLocationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAddressEntryScreenFromLocationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAddressEntryScreenFromLocationResponseValidationError{}

// Validate checks the field values on SendSmsDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendSmsDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendSmsDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendSmsDataRequestMultiError, or nil if none found.
func (m *SendSmsDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendSmsDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetReq() == nil {
		err := SendSmsDataRequestValidationError{
			field:  "Req",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendSmsDataRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendSmsDataRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendSmsDataRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSmsData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SendSmsDataRequestValidationError{
						field:  fmt.Sprintf("SmsData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SendSmsDataRequestValidationError{
						field:  fmt.Sprintf("SmsData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SendSmsDataRequestValidationError{
					field:  fmt.Sprintf("SmsData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SendSmsDataRequestMultiError(errors)
	}

	return nil
}

// SendSmsDataRequestMultiError is an error wrapping multiple validation errors
// returned by SendSmsDataRequest.ValidateAll() if the designated constraints
// aren't met.
type SendSmsDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendSmsDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendSmsDataRequestMultiError) AllErrors() []error { return m }

// SendSmsDataRequestValidationError is the validation error returned by
// SendSmsDataRequest.Validate if the designated constraints aren't met.
type SendSmsDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendSmsDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendSmsDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendSmsDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendSmsDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendSmsDataRequestValidationError) ErrorName() string {
	return "SendSmsDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendSmsDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendSmsDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendSmsDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendSmsDataRequestValidationError{}

// Validate checks the field values on SmsData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SmsData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SmsData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SmsDataMultiError, or nil if none found.
func (m *SmsData) ValidateAll() error {
	return m.validate(true)
}

func (m *SmsData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SmsDataError

	switch v := m.SmsDataType.(type) {
	case *SmsData_Pan:
		if v == nil {
			err := SmsDataValidationError{
				field:  "SmsDataType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Pan
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SmsDataMultiError(errors)
	}

	return nil
}

// SmsDataMultiError is an error wrapping multiple validation errors returned
// by SmsData.ValidateAll() if the designated constraints aren't met.
type SmsDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SmsDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SmsDataMultiError) AllErrors() []error { return m }

// SmsDataValidationError is the validation error returned by SmsData.Validate
// if the designated constraints aren't met.
type SmsDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SmsDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SmsDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SmsDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SmsDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SmsDataValidationError) ErrorName() string { return "SmsDataValidationError" }

// Error satisfies the builtin error interface
func (e SmsDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSmsData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SmsDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SmsDataValidationError{}

// Validate checks the field values on SendSmsDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendSmsDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendSmsDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendSmsDataResponseMultiError, or nil if none found.
func (m *SendSmsDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SendSmsDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendSmsDataResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendSmsDataResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendSmsDataResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SendSmsDataResponseMultiError(errors)
	}

	return nil
}

// SendSmsDataResponseMultiError is an error wrapping multiple validation
// errors returned by SendSmsDataResponse.ValidateAll() if the designated
// constraints aren't met.
type SendSmsDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendSmsDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendSmsDataResponseMultiError) AllErrors() []error { return m }

// SendSmsDataResponseValidationError is the validation error returned by
// SendSmsDataResponse.Validate if the designated constraints aren't met.
type SendSmsDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendSmsDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendSmsDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendSmsDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendSmsDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendSmsDataResponseValidationError) ErrorName() string {
	return "SendSmsDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SendSmsDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendSmsDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendSmsDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendSmsDataResponseValidationError{}

// Validate checks the field values on
// GetUserSessionDetailsResponse_AccountDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetUserSessionDetailsResponse_AccountDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetUserSessionDetailsResponse_AccountDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetUserSessionDetailsResponse_AccountDetailsMultiError, or nil if none found.
func (m *GetUserSessionDetailsResponse_AccountDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserSessionDetailsResponse_AccountDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	// no validation rules for IsUpiPinSet

	// no validation rules for PinSetState

	// no validation rules for MaskedAccountNumber

	// no validation rules for PartnerBank

	// no validation rules for BankLogoUrl

	// no validation rules for AccountType

	if len(errors) > 0 {
		return GetUserSessionDetailsResponse_AccountDetailsMultiError(errors)
	}

	return nil
}

// GetUserSessionDetailsResponse_AccountDetailsMultiError is an error wrapping
// multiple validation errors returned by
// GetUserSessionDetailsResponse_AccountDetails.ValidateAll() if the
// designated constraints aren't met.
type GetUserSessionDetailsResponse_AccountDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserSessionDetailsResponse_AccountDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserSessionDetailsResponse_AccountDetailsMultiError) AllErrors() []error { return m }

// GetUserSessionDetailsResponse_AccountDetailsValidationError is the
// validation error returned by
// GetUserSessionDetailsResponse_AccountDetails.Validate if the designated
// constraints aren't met.
type GetUserSessionDetailsResponse_AccountDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserSessionDetailsResponse_AccountDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserSessionDetailsResponse_AccountDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserSessionDetailsResponse_AccountDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserSessionDetailsResponse_AccountDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserSessionDetailsResponse_AccountDetailsValidationError) ErrorName() string {
	return "GetUserSessionDetailsResponse_AccountDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserSessionDetailsResponse_AccountDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserSessionDetailsResponse_AccountDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserSessionDetailsResponse_AccountDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserSessionDetailsResponse_AccountDetailsValidationError{}

// Validate checks the field values on GetUserSessionDetailsResponse_CardTabs
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetUserSessionDetailsResponse_CardTabs) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetUserSessionDetailsResponse_CardTabs with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetUserSessionDetailsResponse_CardTabsMultiError, or nil if none found.
func (m *GetUserSessionDetailsResponse_CardTabs) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserSessionDetailsResponse_CardTabs) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBgTabs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponse_CardTabsValidationError{
					field:  "BgTabs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponse_CardTabsValidationError{
					field:  "BgTabs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgTabs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserSessionDetailsResponse_CardTabsValidationError{
				field:  "BgTabs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CornerRadius

	for idx, item := range m.GetCardsTab() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUserSessionDetailsResponse_CardTabsValidationError{
						field:  fmt.Sprintf("CardsTab[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUserSessionDetailsResponse_CardTabsValidationError{
						field:  fmt.Sprintf("CardsTab[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUserSessionDetailsResponse_CardTabsValidationError{
					field:  fmt.Sprintf("CardsTab[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetUserSessionDetailsResponse_CardTabsMultiError(errors)
	}

	return nil
}

// GetUserSessionDetailsResponse_CardTabsMultiError is an error wrapping
// multiple validation errors returned by
// GetUserSessionDetailsResponse_CardTabs.ValidateAll() if the designated
// constraints aren't met.
type GetUserSessionDetailsResponse_CardTabsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserSessionDetailsResponse_CardTabsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserSessionDetailsResponse_CardTabsMultiError) AllErrors() []error { return m }

// GetUserSessionDetailsResponse_CardTabsValidationError is the validation
// error returned by GetUserSessionDetailsResponse_CardTabs.Validate if the
// designated constraints aren't met.
type GetUserSessionDetailsResponse_CardTabsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserSessionDetailsResponse_CardTabsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserSessionDetailsResponse_CardTabsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserSessionDetailsResponse_CardTabsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserSessionDetailsResponse_CardTabsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserSessionDetailsResponse_CardTabsValidationError) ErrorName() string {
	return "GetUserSessionDetailsResponse_CardTabsValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserSessionDetailsResponse_CardTabsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserSessionDetailsResponse_CardTabs.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserSessionDetailsResponse_CardTabsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserSessionDetailsResponse_CardTabsValidationError{}

// Validate checks the field values on
// GetUserSessionDetailsResponse_CardTabs_Tab with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetUserSessionDetailsResponse_CardTabs_Tab) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetUserSessionDetailsResponse_CardTabs_Tab with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetUserSessionDetailsResponse_CardTabs_TabMultiError, or nil if none found.
func (m *GetUserSessionDetailsResponse_CardTabs_Tab) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserSessionDetailsResponse_CardTabs_Tab) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TabType

	if all {
		switch v := interface{}(m.GetUnselectedTab()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponse_CardTabs_TabValidationError{
					field:  "UnselectedTab",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponse_CardTabs_TabValidationError{
					field:  "UnselectedTab",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnselectedTab()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserSessionDetailsResponse_CardTabs_TabValidationError{
				field:  "UnselectedTab",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSelectedTab()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponse_CardTabs_TabValidationError{
					field:  "SelectedTab",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserSessionDetailsResponse_CardTabs_TabValidationError{
					field:  "SelectedTab",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSelectedTab()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserSessionDetailsResponse_CardTabs_TabValidationError{
				field:  "SelectedTab",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserSessionDetailsResponse_CardTabs_TabMultiError(errors)
	}

	return nil
}

// GetUserSessionDetailsResponse_CardTabs_TabMultiError is an error wrapping
// multiple validation errors returned by
// GetUserSessionDetailsResponse_CardTabs_Tab.ValidateAll() if the designated
// constraints aren't met.
type GetUserSessionDetailsResponse_CardTabs_TabMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserSessionDetailsResponse_CardTabs_TabMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserSessionDetailsResponse_CardTabs_TabMultiError) AllErrors() []error { return m }

// GetUserSessionDetailsResponse_CardTabs_TabValidationError is the validation
// error returned by GetUserSessionDetailsResponse_CardTabs_Tab.Validate if
// the designated constraints aren't met.
type GetUserSessionDetailsResponse_CardTabs_TabValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserSessionDetailsResponse_CardTabs_TabValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserSessionDetailsResponse_CardTabs_TabValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserSessionDetailsResponse_CardTabs_TabValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserSessionDetailsResponse_CardTabs_TabValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserSessionDetailsResponse_CardTabs_TabValidationError) ErrorName() string {
	return "GetUserSessionDetailsResponse_CardTabs_TabValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserSessionDetailsResponse_CardTabs_TabValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserSessionDetailsResponse_CardTabs_Tab.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserSessionDetailsResponse_CardTabs_TabValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserSessionDetailsResponse_CardTabs_TabValidationError{}

// Validate checks the field values on RecordHashedContactsRequest_Contact with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecordHashedContactsRequest_Contact) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordHashedContactsRequest_Contact
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecordHashedContactsRequest_ContactMultiError, or nil if none found.
func (m *RecordHashedContactsRequest_Contact) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordHashedContactsRequest_Contact) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PhoneNumberHash

	// no validation rules for IsDeleted

	if len(errors) > 0 {
		return RecordHashedContactsRequest_ContactMultiError(errors)
	}

	return nil
}

// RecordHashedContactsRequest_ContactMultiError is an error wrapping multiple
// validation errors returned by
// RecordHashedContactsRequest_Contact.ValidateAll() if the designated
// constraints aren't met.
type RecordHashedContactsRequest_ContactMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordHashedContactsRequest_ContactMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordHashedContactsRequest_ContactMultiError) AllErrors() []error { return m }

// RecordHashedContactsRequest_ContactValidationError is the validation error
// returned by RecordHashedContactsRequest_Contact.Validate if the designated
// constraints aren't met.
type RecordHashedContactsRequest_ContactValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordHashedContactsRequest_ContactValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordHashedContactsRequest_ContactValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordHashedContactsRequest_ContactValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordHashedContactsRequest_ContactValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordHashedContactsRequest_ContactValidationError) ErrorName() string {
	return "RecordHashedContactsRequest_ContactValidationError"
}

// Error satisfies the builtin error interface
func (e RecordHashedContactsRequest_ContactValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordHashedContactsRequest_Contact.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordHashedContactsRequest_ContactValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordHashedContactsRequest_ContactValidationError{}

// Validate checks the field values on
// SyncContactDetailsResponse_ContactDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SyncContactDetailsResponse_ContactDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SyncContactDetailsResponse_ContactDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// SyncContactDetailsResponse_ContactDetailsMultiError, or nil if none found.
func (m *SyncContactDetailsResponse_ContactDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncContactDetailsResponse_ContactDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PhoneNumberHash

	// no validation rules for VerifiedName

	// no validation rules for IconUrl

	// no validation rules for IsNewOnFi

	// no validation rules for ColourCode

	if len(errors) > 0 {
		return SyncContactDetailsResponse_ContactDetailsMultiError(errors)
	}

	return nil
}

// SyncContactDetailsResponse_ContactDetailsMultiError is an error wrapping
// multiple validation errors returned by
// SyncContactDetailsResponse_ContactDetails.ValidateAll() if the designated
// constraints aren't met.
type SyncContactDetailsResponse_ContactDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncContactDetailsResponse_ContactDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncContactDetailsResponse_ContactDetailsMultiError) AllErrors() []error { return m }

// SyncContactDetailsResponse_ContactDetailsValidationError is the validation
// error returned by SyncContactDetailsResponse_ContactDetails.Validate if the
// designated constraints aren't met.
type SyncContactDetailsResponse_ContactDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncContactDetailsResponse_ContactDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncContactDetailsResponse_ContactDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncContactDetailsResponse_ContactDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncContactDetailsResponse_ContactDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncContactDetailsResponse_ContactDetailsValidationError) ErrorName() string {
	return "SyncContactDetailsResponse_ContactDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e SyncContactDetailsResponse_ContactDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncContactDetailsResponse_ContactDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncContactDetailsResponse_ContactDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncContactDetailsResponse_ContactDetailsValidationError{}
