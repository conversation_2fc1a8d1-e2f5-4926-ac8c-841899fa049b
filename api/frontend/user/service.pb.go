// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/user/service.proto

package user

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	accounts "github.com/epifi/gamma/api/accounts"
	frontend "github.com/epifi/gamma/api/frontend"
	account "github.com/epifi/gamma/api/frontend/account"
	upi "github.com/epifi/gamma/api/frontend/account/upi"
	analytics "github.com/epifi/gamma/api/frontend/analytics"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	header "github.com/epifi/gamma/api/frontend/header"
	smsscanner "github.com/epifi/gamma/api/frontend/smsscanner"
	onboarding1 "github.com/epifi/gamma/api/frontend/user/onboarding"
	user_activity "github.com/epifi/gamma/api/frontend/user/user_activity"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	onboarding "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	form "github.com/epifi/gamma/api/typesv2/form"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	date "google.golang.org/genproto/googleapis/type/date"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Flow for which addresses needs to be fetched
type AddressFlow int32

const (
	AddressFlow_ADDRESS_FLOW_UNSPECIFIED AddressFlow = 0
	AddressFlow_ADDRESS_FLOW_ONBOARDING  AddressFlow = 1
	AddressFlow_ADDRESS_FLOW_DEBIT_CARD  AddressFlow = 2
	AddressFlow_ADDRESS_FLOW_CREDIT_CARD AddressFlow = 3
)

// Enum value maps for AddressFlow.
var (
	AddressFlow_name = map[int32]string{
		0: "ADDRESS_FLOW_UNSPECIFIED",
		1: "ADDRESS_FLOW_ONBOARDING",
		2: "ADDRESS_FLOW_DEBIT_CARD",
		3: "ADDRESS_FLOW_CREDIT_CARD",
	}
	AddressFlow_value = map[string]int32{
		"ADDRESS_FLOW_UNSPECIFIED": 0,
		"ADDRESS_FLOW_ONBOARDING":  1,
		"ADDRESS_FLOW_DEBIT_CARD":  2,
		"ADDRESS_FLOW_CREDIT_CARD": 3,
	}
)

func (x AddressFlow) Enum() *AddressFlow {
	p := new(AddressFlow)
	*p = x
	return p
}

func (x AddressFlow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AddressFlow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[0].Descriptor()
}

func (AddressFlow) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[0]
}

func (x AddressFlow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AddressFlow.Descriptor instead.
func (AddressFlow) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{0}
}

type SmsDataError int32

const (
	SmsDataError_SMS_DATA_ERRORS_UNSPECIFIED       SmsDataError = 0
	SmsDataError_SMS_DATA_ERRORS_PERMISSION_DENIED SmsDataError = 1
	SmsDataError_SMS_DATA_ERRORS_DATA_NOT_FOUND    SmsDataError = 2
)

// Enum value maps for SmsDataError.
var (
	SmsDataError_name = map[int32]string{
		0: "SMS_DATA_ERRORS_UNSPECIFIED",
		1: "SMS_DATA_ERRORS_PERMISSION_DENIED",
		2: "SMS_DATA_ERRORS_DATA_NOT_FOUND",
	}
	SmsDataError_value = map[string]int32{
		"SMS_DATA_ERRORS_UNSPECIFIED":       0,
		"SMS_DATA_ERRORS_PERMISSION_DENIED": 1,
		"SMS_DATA_ERRORS_DATA_NOT_FOUND":    2,
	}
)

func (x SmsDataError) Enum() *SmsDataError {
	p := new(SmsDataError)
	*p = x
	return p
}

func (x SmsDataError) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SmsDataError) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[1].Descriptor()
}

func (SmsDataError) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[1]
}

func (x SmsDataError) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SmsDataError.Descriptor instead.
func (SmsDataError) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{1}
}

type AddAddressResponse_Status int32

const (
	AddAddressResponse_OK AddAddressResponse_Status = 0
)

// Enum value maps for AddAddressResponse_Status.
var (
	AddAddressResponse_Status_name = map[int32]string{
		0: "OK",
	}
	AddAddressResponse_Status_value = map[string]int32{
		"OK": 0,
	}
)

func (x AddAddressResponse_Status) Enum() *AddAddressResponse_Status {
	p := new(AddAddressResponse_Status)
	*p = x
	return p
}

func (x AddAddressResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AddAddressResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[2].Descriptor()
}

func (AddAddressResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[2]
}

func (x AddAddressResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AddAddressResponse_Status.Descriptor instead.
func (AddAddressResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{11, 0}
}

type UpdateAddressResponse_Status int32

const (
	UpdateAddressResponse_OK UpdateAddressResponse_Status = 0
)

// Enum value maps for UpdateAddressResponse_Status.
var (
	UpdateAddressResponse_Status_name = map[int32]string{
		0: "OK",
	}
	UpdateAddressResponse_Status_value = map[string]int32{
		"OK": 0,
	}
)

func (x UpdateAddressResponse_Status) Enum() *UpdateAddressResponse_Status {
	p := new(UpdateAddressResponse_Status)
	*p = x
	return p
}

func (x UpdateAddressResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateAddressResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[3].Descriptor()
}

func (UpdateAddressResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[3]
}

func (x UpdateAddressResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateAddressResponse_Status.Descriptor instead.
func (UpdateAddressResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{13, 0}
}

type GetAddressResponse_Status int32

const (
	GetAddressResponse_OK GetAddressResponse_Status = 0
)

// Enum value maps for GetAddressResponse_Status.
var (
	GetAddressResponse_Status_name = map[int32]string{
		0: "OK",
	}
	GetAddressResponse_Status_value = map[string]int32{
		"OK": 0,
	}
)

func (x GetAddressResponse_Status) Enum() *GetAddressResponse_Status {
	p := new(GetAddressResponse_Status)
	*p = x
	return p
}

func (x GetAddressResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAddressResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[4].Descriptor()
}

func (GetAddressResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[4]
}

func (x GetAddressResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAddressResponse_Status.Descriptor instead.
func (GetAddressResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{15, 0}
}

type GetPinCodeDetailsResponse_Status int32

const (
	GetPinCodeDetailsResponse_OK GetPinCodeDetailsResponse_Status = 0
)

// Enum value maps for GetPinCodeDetailsResponse_Status.
var (
	GetPinCodeDetailsResponse_Status_name = map[int32]string{
		0: "OK",
	}
	GetPinCodeDetailsResponse_Status_value = map[string]int32{
		"OK": 0,
	}
)

func (x GetPinCodeDetailsResponse_Status) Enum() *GetPinCodeDetailsResponse_Status {
	p := new(GetPinCodeDetailsResponse_Status)
	*p = x
	return p
}

func (x GetPinCodeDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetPinCodeDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[5].Descriptor()
}

func (GetPinCodeDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[5]
}

func (x GetPinCodeDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetPinCodeDetailsResponse_Status.Descriptor instead.
func (GetPinCodeDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{20, 0}
}

type GetUserSessionDetailsResponse_Status int32

const (
	GetUserSessionDetailsResponse_OK GetUserSessionDetailsResponse_Status = 0
	// record not found
	GetUserSessionDetailsResponse_RECORD_NOT_FOUND GetUserSessionDetailsResponse_Status = 5
	// internal server error
	GetUserSessionDetailsResponse_INTERNAL GetUserSessionDetailsResponse_Status = 13
)

// Enum value maps for GetUserSessionDetailsResponse_Status.
var (
	GetUserSessionDetailsResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	GetUserSessionDetailsResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x GetUserSessionDetailsResponse_Status) Enum() *GetUserSessionDetailsResponse_Status {
	p := new(GetUserSessionDetailsResponse_Status)
	*p = x
	return p
}

func (x GetUserSessionDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetUserSessionDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[6].Descriptor()
}

func (GetUserSessionDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[6]
}

func (x GetUserSessionDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetUserSessionDetailsResponse_Status.Descriptor instead.
func (GetUserSessionDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{24, 0}
}

type GetUserSessionDetailsResponse_CardTabs_TabType int32

const (
	GetUserSessionDetailsResponse_CardTabs_TAB_TYPE_UNSPECIFIED GetUserSessionDetailsResponse_CardTabs_TabType = 0
	GetUserSessionDetailsResponse_CardTabs_DEBIT_CARD           GetUserSessionDetailsResponse_CardTabs_TabType = 1
	GetUserSessionDetailsResponse_CardTabs_CREDIT_CARD          GetUserSessionDetailsResponse_CardTabs_TabType = 2
)

// Enum value maps for GetUserSessionDetailsResponse_CardTabs_TabType.
var (
	GetUserSessionDetailsResponse_CardTabs_TabType_name = map[int32]string{
		0: "TAB_TYPE_UNSPECIFIED",
		1: "DEBIT_CARD",
		2: "CREDIT_CARD",
	}
	GetUserSessionDetailsResponse_CardTabs_TabType_value = map[string]int32{
		"TAB_TYPE_UNSPECIFIED": 0,
		"DEBIT_CARD":           1,
		"CREDIT_CARD":          2,
	}
)

func (x GetUserSessionDetailsResponse_CardTabs_TabType) Enum() *GetUserSessionDetailsResponse_CardTabs_TabType {
	p := new(GetUserSessionDetailsResponse_CardTabs_TabType)
	*p = x
	return p
}

func (x GetUserSessionDetailsResponse_CardTabs_TabType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetUserSessionDetailsResponse_CardTabs_TabType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[7].Descriptor()
}

func (GetUserSessionDetailsResponse_CardTabs_TabType) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[7]
}

func (x GetUserSessionDetailsResponse_CardTabs_TabType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetUserSessionDetailsResponse_CardTabs_TabType.Descriptor instead.
func (GetUserSessionDetailsResponse_CardTabs_TabType) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{24, 3, 0}
}

type CreateNomineeResponse_Status int32

const (
	// Success
	CreateNomineeResponse_OK CreateNomineeResponse_Status = 0
	// Internal error
	CreateNomineeResponse_INTERNAL CreateNomineeResponse_Status = 13
	// Failed precondition: Nominee specified by the client cannot be the same is account holder
	CreateNomineeResponse_FAILED_PRECONDITION_NOMINEE_SAME_AS_ACCOUNT_HOLDER CreateNomineeResponse_Status = 461
	// Failed precondition: Nominee's guardian specified by the client can not be the same is account holder
	CreateNomineeResponse_FAILED_PRECONDITION_GUARDIAN_SAME_AS_ACCOUNT_HOLDER CreateNomineeResponse_Status = 462
)

// Enum value maps for CreateNomineeResponse_Status.
var (
	CreateNomineeResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		461: "FAILED_PRECONDITION_NOMINEE_SAME_AS_ACCOUNT_HOLDER",
		462: "FAILED_PRECONDITION_GUARDIAN_SAME_AS_ACCOUNT_HOLDER",
	}
	CreateNomineeResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
		"FAILED_PRECONDITION_NOMINEE_SAME_AS_ACCOUNT_HOLDER":  461,
		"FAILED_PRECONDITION_GUARDIAN_SAME_AS_ACCOUNT_HOLDER": 462,
	}
)

func (x CreateNomineeResponse_Status) Enum() *CreateNomineeResponse_Status {
	p := new(CreateNomineeResponse_Status)
	*p = x
	return p
}

func (x CreateNomineeResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateNomineeResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[8].Descriptor()
}

func (CreateNomineeResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[8]
}

func (x CreateNomineeResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateNomineeResponse_Status.Descriptor instead.
func (CreateNomineeResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{26, 0}
}

type RecordHashedContactsResponse_Status int32

const (
	// rpc status ok
	RecordHashedContactsResponse_OK RecordHashedContactsResponse_Status = 0
	// server error
	RecordHashedContactsResponse_INTERNAL RecordHashedContactsResponse_Status = 13
	// rpc has reached the rate limit
	// client should retry again in the next retry interval
	RecordHashedContactsResponse_RATE_LIMIT_EXCEEDED RecordHashedContactsResponse_Status = 100
)

// Enum value maps for RecordHashedContactsResponse_Status.
var (
	RecordHashedContactsResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		100: "RATE_LIMIT_EXCEEDED",
	}
	RecordHashedContactsResponse_Status_value = map[string]int32{
		"OK":                  0,
		"INTERNAL":            13,
		"RATE_LIMIT_EXCEEDED": 100,
	}
)

func (x RecordHashedContactsResponse_Status) Enum() *RecordHashedContactsResponse_Status {
	p := new(RecordHashedContactsResponse_Status)
	*p = x
	return p
}

func (x RecordHashedContactsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecordHashedContactsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[9].Descriptor()
}

func (RecordHashedContactsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[9]
}

func (x RecordHashedContactsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecordHashedContactsResponse_Status.Descriptor instead.
func (RecordHashedContactsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{34, 0}
}

type SyncContactDetailsResponse_Status int32

const (
	// rpc status ok
	SyncContactDetailsResponse_OK SyncContactDetailsResponse_Status = 0
	// server error
	SyncContactDetailsResponse_INTERNAL SyncContactDetailsResponse_Status = 13
	// rpc has reached the rate limit
	// client should retry again in the next retry interval
	SyncContactDetailsResponse_RATE_LIMIT_EXCEEDED SyncContactDetailsResponse_Status = 100
)

// Enum value maps for SyncContactDetailsResponse_Status.
var (
	SyncContactDetailsResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		100: "RATE_LIMIT_EXCEEDED",
	}
	SyncContactDetailsResponse_Status_value = map[string]int32{
		"OK":                  0,
		"INTERNAL":            13,
		"RATE_LIMIT_EXCEEDED": 100,
	}
)

func (x SyncContactDetailsResponse_Status) Enum() *SyncContactDetailsResponse_Status {
	p := new(SyncContactDetailsResponse_Status)
	*p = x
	return p
}

func (x SyncContactDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SyncContactDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[10].Descriptor()
}

func (SyncContactDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[10]
}

func (x SyncContactDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SyncContactDetailsResponse_Status.Descriptor instead.
func (SyncContactDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{37, 0}
}

type GetByPhoneNumberResponse_Status int32

const (
	// success
	GetByPhoneNumberResponse_OK GetByPhoneNumberResponse_Status = 0
	// no user found for the given input identifier
	GetByPhoneNumberResponse_NOT_FOUND GetByPhoneNumberResponse_Status = 5
	// internal server error
	GetByPhoneNumberResponse_INTERNAL GetByPhoneNumberResponse_Status = 13
)

// Enum value maps for GetByPhoneNumberResponse_Status.
var (
	GetByPhoneNumberResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetByPhoneNumberResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetByPhoneNumberResponse_Status) Enum() *GetByPhoneNumberResponse_Status {
	p := new(GetByPhoneNumberResponse_Status)
	*p = x
	return p
}

func (x GetByPhoneNumberResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetByPhoneNumberResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[11].Descriptor()
}

func (GetByPhoneNumberResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[11]
}

func (x GetByPhoneNumberResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetByPhoneNumberResponse_Status.Descriptor instead.
func (GetByPhoneNumberResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{39, 0}
}

type PostUserActivityResponse_Status int32

const (
	// success
	PostUserActivityResponse_OK PostUserActivityResponse_Status = 0
	// no user found for the given input identifier
	PostUserActivityResponse_NOT_FOUND PostUserActivityResponse_Status = 5
	// internal server error
	PostUserActivityResponse_INTERNAL PostUserActivityResponse_Status = 13
)

// Enum value maps for PostUserActivityResponse_Status.
var (
	PostUserActivityResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	PostUserActivityResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x PostUserActivityResponse_Status) Enum() *PostUserActivityResponse_Status {
	p := new(PostUserActivityResponse_Status)
	*p = x
	return p
}

func (x PostUserActivityResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PostUserActivityResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[12].Descriptor()
}

func (PostUserActivityResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[12]
}

func (x PostUserActivityResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PostUserActivityResponse_Status.Descriptor instead.
func (PostUserActivityResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{45, 0}
}

type BlockActorResponse_Status int32

const (
	// call successful
	BlockActorResponse_OK BlockActorResponse_Status = 0
	// invalid request arguments
	BlockActorResponse_INVALID_ARGUMENT BlockActorResponse_Status = 3
	// internal server error
	BlockActorResponse_INTERNAL BlockActorResponse_Status = 13
	// actor is already blocked by logged in actor
	BlockActorResponse_ALREADY_PROCESSED BlockActorResponse_Status = 50
)

// Enum value maps for BlockActorResponse_Status.
var (
	BlockActorResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
		50: "ALREADY_PROCESSED",
	}
	BlockActorResponse_Status_value = map[string]int32{
		"OK":                0,
		"INVALID_ARGUMENT":  3,
		"INTERNAL":          13,
		"ALREADY_PROCESSED": 50,
	}
)

func (x BlockActorResponse_Status) Enum() *BlockActorResponse_Status {
	p := new(BlockActorResponse_Status)
	*p = x
	return p
}

func (x BlockActorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BlockActorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[13].Descriptor()
}

func (BlockActorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[13]
}

func (x BlockActorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BlockActorResponse_Status.Descriptor instead.
func (BlockActorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{47, 0}
}

type UnblockActorResponse_Status int32

const (
	// call successful
	UnblockActorResponse_OK UnblockActorResponse_Status = 0
	// invalid request arguments
	UnblockActorResponse_INVALID_ARGUMENT UnblockActorResponse_Status = 3
	// internal server error
	UnblockActorResponse_INTERNAL UnblockActorResponse_Status = 13
	// actor already unblocked
	UnblockActorResponse_ALREADY_PROCESSED UnblockActorResponse_Status = 50
)

// Enum value maps for UnblockActorResponse_Status.
var (
	UnblockActorResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
		50: "ALREADY_PROCESSED",
	}
	UnblockActorResponse_Status_value = map[string]int32{
		"OK":                0,
		"INVALID_ARGUMENT":  3,
		"INTERNAL":          13,
		"ALREADY_PROCESSED": 50,
	}
)

func (x UnblockActorResponse_Status) Enum() *UnblockActorResponse_Status {
	p := new(UnblockActorResponse_Status)
	*p = x
	return p
}

func (x UnblockActorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UnblockActorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[14].Descriptor()
}

func (UnblockActorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[14]
}

func (x UnblockActorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UnblockActorResponse_Status.Descriptor instead.
func (UnblockActorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{49, 0}
}

type ReportSpamResponse_Status int32

const (
	// call successful
	ReportSpamResponse_OK ReportSpamResponse_Status = 0
	// invalid request arguments
	ReportSpamResponse_INVALID_ARGUMENT ReportSpamResponse_Status = 3
	// internal server error
	ReportSpamResponse_INTERNAL ReportSpamResponse_Status = 13
	// actor is already marked as spam by logged in actor
	ReportSpamResponse_ALREADY_PROCESSED ReportSpamResponse_Status = 50
	// actor not blocked by the logged in actor
	ReportSpamResponse_ACTOR_NOT_BLOCKED ReportSpamResponse_Status = 100
)

// Enum value maps for ReportSpamResponse_Status.
var (
	ReportSpamResponse_Status_name = map[int32]string{
		0:   "OK",
		3:   "INVALID_ARGUMENT",
		13:  "INTERNAL",
		50:  "ALREADY_PROCESSED",
		100: "ACTOR_NOT_BLOCKED",
	}
	ReportSpamResponse_Status_value = map[string]int32{
		"OK":                0,
		"INVALID_ARGUMENT":  3,
		"INTERNAL":          13,
		"ALREADY_PROCESSED": 50,
		"ACTOR_NOT_BLOCKED": 100,
	}
)

func (x ReportSpamResponse_Status) Enum() *ReportSpamResponse_Status {
	p := new(ReportSpamResponse_Status)
	*p = x
	return p
}

func (x ReportSpamResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReportSpamResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_user_service_proto_enumTypes[15].Descriptor()
}

func (ReportSpamResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_user_service_proto_enumTypes[15]
}

func (x ReportSpamResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReportSpamResponse_Status.Descriptor instead.
func (ReportSpamResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{51, 0}
}

type GetFeatureBenefitsScreenOptionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// entry point from where the deeplink got invoked
	// string of enum api.typesv2.deeplink_screen_option.onboarding.FeatureOnboardingEntryPoint
	EntryPoint string `protobuf:"bytes,2,opt,name=entry_point,json=entryPoint,proto3" json:"entry_point,omitempty"`
}

func (x *GetFeatureBenefitsScreenOptionsRequest) Reset() {
	*x = GetFeatureBenefitsScreenOptionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFeatureBenefitsScreenOptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFeatureBenefitsScreenOptionsRequest) ProtoMessage() {}

func (x *GetFeatureBenefitsScreenOptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFeatureBenefitsScreenOptionsRequest.ProtoReflect.Descriptor instead.
func (*GetFeatureBenefitsScreenOptionsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetFeatureBenefitsScreenOptionsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetFeatureBenefitsScreenOptionsRequest) GetEntryPoint() string {
	if x != nil {
		return x.EntryPoint
	}
	return ""
}

type GetFeatureBenefitsScreenOptionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader                   *header.ResponseHeader                   `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	FeatureBenefitsScreenOptions *onboarding.FeatureBenefitsScreenOptions `protobuf:"bytes,2,opt,name=feature_benefits_screen_options,json=featureBenefitsScreenOptions,proto3" json:"feature_benefits_screen_options,omitempty"`
}

func (x *GetFeatureBenefitsScreenOptionsResponse) Reset() {
	*x = GetFeatureBenefitsScreenOptionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFeatureBenefitsScreenOptionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFeatureBenefitsScreenOptionsResponse) ProtoMessage() {}

func (x *GetFeatureBenefitsScreenOptionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFeatureBenefitsScreenOptionsResponse.ProtoReflect.Descriptor instead.
func (*GetFeatureBenefitsScreenOptionsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetFeatureBenefitsScreenOptionsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetFeatureBenefitsScreenOptionsResponse) GetFeatureBenefitsScreenOptions() *onboarding.FeatureBenefitsScreenOptions {
	if x != nil {
		return x.FeatureBenefitsScreenOptions
	}
	return nil
}

type GetProfileSettingPageSectionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetProfileSettingPageSectionRequest) Reset() {
	*x = GetProfileSettingPageSectionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileSettingPageSectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileSettingPageSectionRequest) ProtoMessage() {}

func (x *GetProfileSettingPageSectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileSettingPageSectionRequest.ProtoReflect.Descriptor instead.
func (*GetProfileSettingPageSectionRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetProfileSettingPageSectionRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type SetCallLanguagePreferencesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Screen Name from where this request is coming from eg: Home, Explore
	EntryPoint analytics.AnalyticsScreenName `protobuf:"varint,2,opt,name=entry_point,json=entryPoint,proto3,enum=frontend.analytics.AnalyticsScreenName" json:"entry_point,omitempty"`
	// list of preferences to be set for the requesting user(actor_id)
	Preferences *PreferenceTypeValuePair `protobuf:"bytes,3,opt,name=preferences,proto3" json:"preferences,omitempty"`
}

func (x *SetCallLanguagePreferencesRequest) Reset() {
	*x = SetCallLanguagePreferencesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCallLanguagePreferencesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCallLanguagePreferencesRequest) ProtoMessage() {}

func (x *SetCallLanguagePreferencesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCallLanguagePreferencesRequest.ProtoReflect.Descriptor instead.
func (*SetCallLanguagePreferencesRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{3}
}

func (x *SetCallLanguagePreferencesRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *SetCallLanguagePreferencesRequest) GetEntryPoint() analytics.AnalyticsScreenName {
	if x != nil {
		return x.EntryPoint
	}
	return analytics.AnalyticsScreenName(0)
}

func (x *SetCallLanguagePreferencesRequest) GetPreferences() *PreferenceTypeValuePair {
	if x != nil {
		return x.Preferences
	}
	return nil
}

type SetCallLanguagePreferencesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Title of the section eg: "Call language preference has been set successfully"
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Desc of the section eg: "You can edit your language preference from Profile > Settings"
	Desc *common.Text `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// Info text for edit language preferences
	EditPrefInfo *common.Text `protobuf:"bytes,4,opt,name=edit_pref_info,json=editPrefInfo,proto3" json:"edit_pref_info,omitempty"`
	// Info Icon Component to display additional information
	InfoIcon *ui.IconTextComponent `protobuf:"bytes,5,opt,name=info_icon,json=infoIcon,proto3" json:"info_icon,omitempty"`
}

func (x *SetCallLanguagePreferencesResponse) Reset() {
	*x = SetCallLanguagePreferencesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetCallLanguagePreferencesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCallLanguagePreferencesResponse) ProtoMessage() {}

func (x *SetCallLanguagePreferencesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCallLanguagePreferencesResponse.ProtoReflect.Descriptor instead.
func (*SetCallLanguagePreferencesResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{4}
}

func (x *SetCallLanguagePreferencesResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *SetCallLanguagePreferencesResponse) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *SetCallLanguagePreferencesResponse) GetDesc() *common.Text {
	if x != nil {
		return x.Desc
	}
	return nil
}

func (x *SetCallLanguagePreferencesResponse) GetEditPrefInfo() *common.Text {
	if x != nil {
		return x.EditPrefInfo
	}
	return nil
}

func (x *SetCallLanguagePreferencesResponse) GetInfoIcon() *ui.IconTextComponent {
	if x != nil {
		return x.InfoIcon
	}
	return nil
}

type GetProfileSettingPageSectionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// html text for closing Fi savings account with an deeplink action that takes user to account closure flow
	//
	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	AccountClosureText *common.Text `protobuf:"bytes,2,opt,name=account_closure_text,json=accountClosureText,proto3" json:"account_closure_text,omitempty"`
	// html text with deeplink to navigate to a screen [required for iOS as application is getting relaunch on click of Deeplink]
	AccountClosureHyperlinks *ui.TextWithHyperlinks `protobuf:"bytes,3,opt,name=account_closure_hyperlinks,json=accountClosureHyperlinks,proto3" json:"account_closure_hyperlinks,omitempty"`
}

func (x *GetProfileSettingPageSectionResponse) Reset() {
	*x = GetProfileSettingPageSectionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileSettingPageSectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileSettingPageSectionResponse) ProtoMessage() {}

func (x *GetProfileSettingPageSectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileSettingPageSectionResponse.ProtoReflect.Descriptor instead.
func (*GetProfileSettingPageSectionResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetProfileSettingPageSectionResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetProfileSettingPageSectionResponse) GetAccountClosureText() *common.Text {
	if x != nil {
		return x.AccountClosureText
	}
	return nil
}

func (x *GetProfileSettingPageSectionResponse) GetAccountClosureHyperlinks() *ui.TextWithHyperlinks {
	if x != nil {
		return x.AccountClosureHyperlinks
	}
	return nil
}

type UpdateDOBForDedupeRetryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req         *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	DateOfBirth *date.Date            `protobuf:"bytes,4,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
}

func (x *UpdateDOBForDedupeRetryRequest) Reset() {
	*x = UpdateDOBForDedupeRetryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDOBForDedupeRetryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDOBForDedupeRetryRequest) ProtoMessage() {}

func (x *UpdateDOBForDedupeRetryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDOBForDedupeRetryRequest.ProtoReflect.Descriptor instead.
func (*UpdateDOBForDedupeRetryRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateDOBForDedupeRetryRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *UpdateDOBForDedupeRetryRequest) GetDateOfBirth() *date.Date {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

type UpdateDOBForDedupeRetryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	NextAction *deeplink.Deeplink     `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	// max attempts for dedupe dob mismatch
	MaxDobDedupeAttempts int32 `protobuf:"varint,3,opt,name=max_dob_dedupe_attempts,json=maxDobDedupeAttempts,proto3" json:"max_dob_dedupe_attempts,omitempty"`
	// current count of attempts for dedupe dob mismatch
	CurrentDobDedupeAttempts int32 `protobuf:"varint,4,opt,name=current_dob_dedupe_attempts,json=currentDobDedupeAttempts,proto3" json:"current_dob_dedupe_attempts,omitempty"`
}

func (x *UpdateDOBForDedupeRetryResponse) Reset() {
	*x = UpdateDOBForDedupeRetryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDOBForDedupeRetryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDOBForDedupeRetryResponse) ProtoMessage() {}

func (x *UpdateDOBForDedupeRetryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDOBForDedupeRetryResponse.ProtoReflect.Descriptor instead.
func (*UpdateDOBForDedupeRetryResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateDOBForDedupeRetryResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *UpdateDOBForDedupeRetryResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *UpdateDOBForDedupeRetryResponse) GetMaxDobDedupeAttempts() int32 {
	if x != nil {
		return x.MaxDobDedupeAttempts
	}
	return 0
}

func (x *UpdateDOBForDedupeRetryResponse) GetCurrentDobDedupeAttempts() int32 {
	if x != nil {
		return x.CurrentDobDedupeAttempts
	}
	return 0
}

type GetDebitCardNameRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetDebitCardNameRequest) Reset() {
	*x = GetDebitCardNameRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDebitCardNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDebitCardNameRequest) ProtoMessage() {}

func (x *GetDebitCardNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDebitCardNameRequest.ProtoReflect.Descriptor instead.
func (*GetDebitCardNameRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetDebitCardNameRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetDebitCardNameResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	DebitCardName *common.Name           `protobuf:"bytes,2,opt,name=debit_card_name,json=debitCardName,proto3" json:"debit_card_name,omitempty"`
	RespHeader    *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *GetDebitCardNameResponse) Reset() {
	*x = GetDebitCardNameResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDebitCardNameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDebitCardNameResponse) ProtoMessage() {}

func (x *GetDebitCardNameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDebitCardNameResponse.ProtoReflect.Descriptor instead.
func (*GetDebitCardNameResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetDebitCardNameResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetDebitCardNameResponse) GetDebitCardName() *common.Name {
	if x != nil {
		return x.DebitCardName
	}
	return nil
}

func (x *GetDebitCardNameResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type AddAddressRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Auth    *header.AuthHeader     `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	Req     *header.RequestHeader  `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	Type    typesv2.AddressType    `protobuf:"varint,2,opt,name=type,proto3,enum=api.typesv2.AddressType" json:"type,omitempty"`
	Address *typesv2.PostalAddress `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	// location token captured against collected address, if exists
	LocationToken string `protobuf:"bytes,5,opt,name=location_token,json=locationToken,proto3" json:"location_token,omitempty"`
}

func (x *AddAddressRequest) Reset() {
	*x = AddAddressRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAddressRequest) ProtoMessage() {}

func (x *AddAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAddressRequest.ProtoReflect.Descriptor instead.
func (*AddAddressRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{10}
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *AddAddressRequest) GetAuth() *header.AuthHeader {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *AddAddressRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *AddAddressRequest) GetType() typesv2.AddressType {
	if x != nil {
		return x.Type
	}
	return typesv2.AddressType(0)
}

func (x *AddAddressRequest) GetAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *AddAddressRequest) GetLocationToken() string {
	if x != nil {
		return x.LocationToken
	}
	return ""
}

type AddAddressResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Id         string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *AddAddressResponse) Reset() {
	*x = AddAddressResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAddressResponse) ProtoMessage() {}

func (x *AddAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAddressResponse.ProtoReflect.Descriptor instead.
func (*AddAddressResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{11}
}

func (x *AddAddressResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *AddAddressResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AddAddressResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type UpdateAddressRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Auth    *header.AuthHeader     `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	Req     *header.RequestHeader  `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	Type    typesv2.AddressType    `protobuf:"varint,2,opt,name=type,proto3,enum=api.typesv2.AddressType" json:"type,omitempty"`
	Id      string                 `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	Address *typesv2.PostalAddress `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *UpdateAddressRequest) Reset() {
	*x = UpdateAddressRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAddressRequest) ProtoMessage() {}

func (x *UpdateAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAddressRequest.ProtoReflect.Descriptor instead.
func (*UpdateAddressRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{12}
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *UpdateAddressRequest) GetAuth() *header.AuthHeader {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *UpdateAddressRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *UpdateAddressRequest) GetType() typesv2.AddressType {
	if x != nil {
		return x.Type
	}
	return typesv2.AddressType(0)
}

func (x *UpdateAddressRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateAddressRequest) GetAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

type UpdateAddressResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NextAction *deeplink.Deeplink     `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *UpdateAddressResponse) Reset() {
	*x = UpdateAddressResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAddressResponse) ProtoMessage() {}

func (x *UpdateAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAddressResponse.ProtoReflect.Descriptor instead.
func (*UpdateAddressResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateAddressResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateAddressResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *UpdateAddressResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type GetAddressRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Auth *header.AuthHeader    `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	Req  *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	Type typesv2.AddressType   `protobuf:"varint,2,opt,name=type,proto3,enum=api.typesv2.AddressType" json:"type,omitempty"`
}

func (x *GetAddressRequest) Reset() {
	*x = GetAddressRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressRequest) ProtoMessage() {}

func (x *GetAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressRequest.ProtoReflect.Descriptor instead.
func (*GetAddressRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{14}
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetAddressRequest) GetAuth() *header.AuthHeader {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *GetAddressRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetAddressRequest) GetType() typesv2.AddressType {
	if x != nil {
		return x.Type
	}
	return typesv2.AddressType(0)
}

type GetAddressResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Addresses  []*AddressInfo         `protobuf:"bytes,2,rep,name=addresses,proto3" json:"addresses,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *GetAddressResponse) Reset() {
	*x = GetAddressResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressResponse) ProtoMessage() {}

func (x *GetAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressResponse.ProtoReflect.Descriptor instead.
func (*GetAddressResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetAddressResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAddressResponse) GetAddresses() []*AddressInfo {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *GetAddressResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type GetCardDeliveryAddressesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Auth *header.AuthHeader    `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	Req  *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	// flow for which addresses needs to be fetched
	// if not sent we will return all the addresses present for a user
	AddressFlow AddressFlow `protobuf:"varint,2,opt,name=address_flow,json=addressFlow,proto3,enum=frontend.user.AddressFlow" json:"address_flow,omitempty"`
}

func (x *GetCardDeliveryAddressesRequest) Reset() {
	*x = GetCardDeliveryAddressesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCardDeliveryAddressesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardDeliveryAddressesRequest) ProtoMessage() {}

func (x *GetCardDeliveryAddressesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardDeliveryAddressesRequest.ProtoReflect.Descriptor instead.
func (*GetCardDeliveryAddressesRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{16}
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetCardDeliveryAddressesRequest) GetAuth() *header.AuthHeader {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *GetCardDeliveryAddressesRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetCardDeliveryAddressesRequest) GetAddressFlow() AddressFlow {
	if x != nil {
		return x.AddressFlow
	}
	return AddressFlow_ADDRESS_FLOW_UNSPECIFIED
}

type GetCardDeliveryAddressesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// deprecated: use addressesWithType instead
	Addresses           []*typesv2.PostalAddress `protobuf:"bytes,2,rep,name=addresses,proto3" json:"addresses,omitempty"`
	AddressesWithType   []*AddressWithType       `protobuf:"bytes,3,rep,name=addressesWithType,proto3" json:"addressesWithType,omitempty"`
	EnableAddressUpdate bool                     `protobuf:"varint,4,opt,name=enable_address_update,json=enableAddressUpdate,proto3" json:"enable_address_update,omitempty"`
	RespHeader          *header.ResponseHeader   `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *GetCardDeliveryAddressesResponse) Reset() {
	*x = GetCardDeliveryAddressesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCardDeliveryAddressesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardDeliveryAddressesResponse) ProtoMessage() {}

func (x *GetCardDeliveryAddressesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardDeliveryAddressesResponse.ProtoReflect.Descriptor instead.
func (*GetCardDeliveryAddressesResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetCardDeliveryAddressesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCardDeliveryAddressesResponse) GetAddresses() []*typesv2.PostalAddress {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *GetCardDeliveryAddressesResponse) GetAddressesWithType() []*AddressWithType {
	if x != nil {
		return x.AddressesWithType
	}
	return nil
}

func (x *GetCardDeliveryAddressesResponse) GetEnableAddressUpdate() bool {
	if x != nil {
		return x.EnableAddressUpdate
	}
	return false
}

func (x *GetCardDeliveryAddressesResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type AddressInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type    typesv2.AddressType    `protobuf:"varint,2,opt,name=type,proto3,enum=api.typesv2.AddressType" json:"type,omitempty"`
	Address *typesv2.PostalAddress `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *AddressInfo) Reset() {
	*x = AddressInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressInfo) ProtoMessage() {}

func (x *AddressInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressInfo.ProtoReflect.Descriptor instead.
func (*AddressInfo) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{18}
}

func (x *AddressInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AddressInfo) GetType() typesv2.AddressType {
	if x != nil {
		return x.Type
	}
	return typesv2.AddressType(0)
}

func (x *AddressInfo) GetAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

// Deprecated: Use GetPinCodeAreaRequest instead
type GetPinCodeDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Auth    *header.AuthHeader    `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	Req     *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	Pincode string                `protobuf:"bytes,2,opt,name=pincode,proto3" json:"pincode,omitempty"`
}

func (x *GetPinCodeDetailsRequest) Reset() {
	*x = GetPinCodeDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPinCodeDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPinCodeDetailsRequest) ProtoMessage() {}

func (x *GetPinCodeDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPinCodeDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetPinCodeDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{19}
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetPinCodeDetailsRequest) GetAuth() *header.AuthHeader {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *GetPinCodeDetailsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetPinCodeDetailsRequest) GetPincode() string {
	if x != nil {
		return x.Pincode
	}
	return ""
}

// Deprecated: Use GetPinCodeAreaResponse instead
type GetPinCodeDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	City       string                 `protobuf:"bytes,2,opt,name=city,proto3" json:"city,omitempty"`
	State      string                 `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *GetPinCodeDetailsResponse) Reset() {
	*x = GetPinCodeDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPinCodeDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPinCodeDetailsResponse) ProtoMessage() {}

func (x *GetPinCodeDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPinCodeDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetPinCodeDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetPinCodeDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPinCodeDetailsResponse) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *GetPinCodeDetailsResponse) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *GetPinCodeDetailsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type UpdateMotherFatherNameRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Auth header
	//
	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Auth *header.AuthHeader    `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	Req  *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	// mother name
	MotherName string `protobuf:"bytes,2,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
	// father name
	FatherName string `protobuf:"bytes,3,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
}

func (x *UpdateMotherFatherNameRequest) Reset() {
	*x = UpdateMotherFatherNameRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMotherFatherNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMotherFatherNameRequest) ProtoMessage() {}

func (x *UpdateMotherFatherNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMotherFatherNameRequest.ProtoReflect.Descriptor instead.
func (*UpdateMotherFatherNameRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{21}
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *UpdateMotherFatherNameRequest) GetAuth() *header.AuthHeader {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *UpdateMotherFatherNameRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *UpdateMotherFatherNameRequest) GetMotherName() string {
	if x != nil {
		return x.MotherName
	}
	return ""
}

func (x *UpdateMotherFatherNameRequest) GetFatherName() string {
	if x != nil {
		return x.FatherName
	}
	return ""
}

type UpdateMotherFatherNameResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// next action
	NextAction *deeplink.Deeplink     `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *UpdateMotherFatherNameResponse) Reset() {
	*x = UpdateMotherFatherNameResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMotherFatherNameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMotherFatherNameResponse) ProtoMessage() {}

func (x *UpdateMotherFatherNameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMotherFatherNameResponse.ProtoReflect.Descriptor instead.
func (*UpdateMotherFatherNameResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{22}
}

func (x *UpdateMotherFatherNameResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateMotherFatherNameResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *UpdateMotherFatherNameResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type GetUserSessionDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Auth header
	//
	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Auth *header.AuthHeader    `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	Req  *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetUserSessionDetailsRequest) Reset() {
	*x = GetUserSessionDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserSessionDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSessionDetailsRequest) ProtoMessage() {}

func (x *GetUserSessionDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSessionDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetUserSessionDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{23}
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetUserSessionDetailsRequest) GetAuth() *header.AuthHeader {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *GetUserSessionDetailsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetUserSessionDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of account details for the user
	// Deprecated: Account object is standardized. Use accounts field instead of account_details.
	//
	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	AccountDetails []*GetUserSessionDetailsResponse_AccountDetails `protobuf:"bytes,2,rep,name=account_details,json=accountDetails,proto3" json:"account_details,omitempty"`
	// list of card ids that exist for the user
	CardIds []string `protobuf:"bytes,3,rep,name=card_ids,json=cardIds,proto3" json:"card_ids,omitempty"`
	// id to be passed when fetching remote config from firebase
	FirebaseId string `protobuf:"bytes,4,opt,name=firebase_id,json=firebaseId,proto3" json:"firebase_id,omitempty"`
	// user properties to be passed while fetching remote config from firebase
	FirebaseUserProperties map[string]string `protobuf:"bytes,5,rep,name=firebase_user_properties,json=firebaseUserProperties,proto3" json:"firebase_user_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Display Name entered by the user during Onboarding.
	// This name is also the one printed on the user's Debit card.
	// TODO(team): Currently Display Name is not a savings account level property but a user level property.
	//
	//	In future, if multiple savings accounts exist per user, each account can have it's own
	//	display name, making display name a savings account level property.
	//	When this happens, amendments will be required here as well here as well
	DisplayName *common.Name           `protobuf:"bytes,6,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	RespHeader  *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// list of account details for the user
	Accounts []*account.Account `protobuf:"bytes,7,rep,name=accounts,proto3" json:"accounts,omitempty"`
	// map of feature as key to value defined via FeatureInfo
	// Feature enum is defined in api/types/feature.proto
	// For eg:
	//
	//	{
	//	   "INVESTMENT_MF_UI" : {"enable": "true"}
	//	   "PAY_VIA_PHONE_NUMBER" : {"enable": "false"},
	//	   "MF_ADVANCE_FILTER" : {"enable": "true"},
	//	}
	FeatureMap map[string]*FeatureInfo `protobuf:"bytes,8,rep,name=feature_map,json=featureMap,proto3" json:"feature_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// true specifies user has completed savings account onboarding
	IsSaOnboarded bool `protobuf:"varint,9,opt,name=is_sa_onboarded,json=isSaOnboarded,proto3" json:"is_sa_onboarded,omitempty"`
	// indicates if the it's is a fi lite user
	IsFiLiteUser bool `protobuf:"varint,10,opt,name=is_fi_lite_user,json=isFiLiteUser,proto3" json:"is_fi_lite_user,omitempty"`
	// indicates whether user has access to home
	IsHomeAccessible bool `protobuf:"varint,11,opt,name=is_home_accessible,json=isHomeAccessible,proto3" json:"is_home_accessible,omitempty"`
	// current feature user is onboarding on (CC/SA/PL)
	CurrentFeature  string                     `protobuf:"bytes,12,opt,name=current_feature,json=currentFeature,proto3" json:"current_feature,omitempty"`
	MsClarityConfig *analytics.MsClarityConfig `protobuf:"bytes,13,opt,name=ms_clarity_config,json=msClarityConfig,proto3" json:"ms_clarity_config,omitempty"`
	// If this config is nil, then client will not change the existing config
	// If this config is not nil, then client will update the existing config with this config
	ScienapticSmsScannerConfig *smsscanner.ScienapticSmsScannerConfig `protobuf:"bytes,14,opt,name=scienaptic_sms_scanner_config,json=scienapticSmsScannerConfig,proto3" json:"scienaptic_sms_scanner_config,omitempty"`
	// Custom user id for Ms Clarity for a user, genereated by the backend in BE vendor
	// mapping. Clarity Ref: https://learn.microsoft.com/en-us/clarity/mobile-sdk/android-sdk?tabs=kotlin#setcustomuserid
	MsClarityId string `protobuf:"bytes,16,opt,name=ms_clarity_id,json=msClarityId,proto3" json:"ms_clarity_id,omitempty"`
	// Config for cards tab UI.
	// Figma : https://www.figma.com/design/FWW6omNbhzvzwBqlMT03am/D'fixit---workfile?node-id=816-102651&t=43YzeFO8SVuQNz6g-4
	// https://www.figma.com/design/FWW6omNbhzvzwBqlMT03am/D'fixit---workfile?node-id=816-102847&t=43YzeFO8SVuQNz6g-4
	CardTabs *GetUserSessionDetailsResponse_CardTabs `protobuf:"bytes,17,opt,name=card_tabs,json=cardTabs,proto3" json:"card_tabs,omitempty"`
}

func (x *GetUserSessionDetailsResponse) Reset() {
	*x = GetUserSessionDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserSessionDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSessionDetailsResponse) ProtoMessage() {}

func (x *GetUserSessionDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSessionDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetUserSessionDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetUserSessionDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetUserSessionDetailsResponse) GetAccountDetails() []*GetUserSessionDetailsResponse_AccountDetails {
	if x != nil {
		return x.AccountDetails
	}
	return nil
}

func (x *GetUserSessionDetailsResponse) GetCardIds() []string {
	if x != nil {
		return x.CardIds
	}
	return nil
}

func (x *GetUserSessionDetailsResponse) GetFirebaseId() string {
	if x != nil {
		return x.FirebaseId
	}
	return ""
}

func (x *GetUserSessionDetailsResponse) GetFirebaseUserProperties() map[string]string {
	if x != nil {
		return x.FirebaseUserProperties
	}
	return nil
}

func (x *GetUserSessionDetailsResponse) GetDisplayName() *common.Name {
	if x != nil {
		return x.DisplayName
	}
	return nil
}

func (x *GetUserSessionDetailsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetUserSessionDetailsResponse) GetAccounts() []*account.Account {
	if x != nil {
		return x.Accounts
	}
	return nil
}

func (x *GetUserSessionDetailsResponse) GetFeatureMap() map[string]*FeatureInfo {
	if x != nil {
		return x.FeatureMap
	}
	return nil
}

func (x *GetUserSessionDetailsResponse) GetIsSaOnboarded() bool {
	if x != nil {
		return x.IsSaOnboarded
	}
	return false
}

func (x *GetUserSessionDetailsResponse) GetIsFiLiteUser() bool {
	if x != nil {
		return x.IsFiLiteUser
	}
	return false
}

func (x *GetUserSessionDetailsResponse) GetIsHomeAccessible() bool {
	if x != nil {
		return x.IsHomeAccessible
	}
	return false
}

func (x *GetUserSessionDetailsResponse) GetCurrentFeature() string {
	if x != nil {
		return x.CurrentFeature
	}
	return ""
}

func (x *GetUserSessionDetailsResponse) GetMsClarityConfig() *analytics.MsClarityConfig {
	if x != nil {
		return x.MsClarityConfig
	}
	return nil
}

func (x *GetUserSessionDetailsResponse) GetScienapticSmsScannerConfig() *smsscanner.ScienapticSmsScannerConfig {
	if x != nil {
		return x.ScienapticSmsScannerConfig
	}
	return nil
}

func (x *GetUserSessionDetailsResponse) GetMsClarityId() string {
	if x != nil {
		return x.MsClarityId
	}
	return ""
}

func (x *GetUserSessionDetailsResponse) GetCardTabs() *GetUserSessionDetailsResponse_CardTabs {
	if x != nil {
		return x.CardTabs
	}
	return nil
}

type CreateNomineeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Auth *header.AuthHeader    `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	Req  *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	// Deprecated in favour of nominee_v2 [frontend.user.Nominee]
	//
	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Nominee *typesv2.Nominee `protobuf:"bytes,2,opt,name=nominee,proto3" json:"nominee,omitempty"`
	// Entrypoint from where the nominee update is initiated
	// For entry_point 'wealth_onboarding' backend has checks if nominee and user's PAN are the same
	// maps to typesV2.NomineeEntryPoint
	EntryPoint string   `protobuf:"bytes,3,opt,name=entry_point,json=entryPoint,proto3" json:"entry_point,omitempty"`
	NomineeV2  *Nominee `protobuf:"bytes,4,opt,name=nominee_v2,json=nomineeV2,proto3" json:"nominee_v2,omitempty"`
}

func (x *CreateNomineeRequest) Reset() {
	*x = CreateNomineeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateNomineeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNomineeRequest) ProtoMessage() {}

func (x *CreateNomineeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNomineeRequest.ProtoReflect.Descriptor instead.
func (*CreateNomineeRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{25}
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *CreateNomineeRequest) GetAuth() *header.AuthHeader {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *CreateNomineeRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *CreateNomineeRequest) GetNominee() *typesv2.Nominee {
	if x != nil {
		return x.Nominee
	}
	return nil
}

func (x *CreateNomineeRequest) GetEntryPoint() string {
	if x != nil {
		return x.EntryPoint
	}
	return ""
}

func (x *CreateNomineeRequest) GetNomineeV2() *Nominee {
	if x != nil {
		return x.NomineeV2
	}
	return nil
}

type CreateNomineeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NomineeId  string                 `protobuf:"bytes,2,opt,name=nominee_id,json=nomineeId,proto3" json:"nominee_id,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Deeplink to redirect user incase nominee update success
	// optional
	Deeplink *deeplink.Deeplink `protobuf:"bytes,3,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *CreateNomineeResponse) Reset() {
	*x = CreateNomineeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateNomineeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNomineeResponse) ProtoMessage() {}

func (x *CreateNomineeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNomineeResponse.ProtoReflect.Descriptor instead.
func (*CreateNomineeResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{26}
}

func (x *CreateNomineeResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateNomineeResponse) GetNomineeId() string {
	if x != nil {
		return x.NomineeId
	}
	return ""
}

func (x *CreateNomineeResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *CreateNomineeResponse) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

type GetNomineesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Auth *header.AuthHeader    `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	Req  *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	// using flow_type to show nominees based on certain conditions
	// Usecase: For flow_type 'wealth_onboarding' only nominees who have documents
	// like pan, aadhaar or driving license, email and mobile number will be shown
	// maps to typesV2.NomineeEntryPoint
	FlowType string `protobuf:"bytes,2,opt,name=flow_type,json=flowType,proto3" json:"flow_type,omitempty"`
}

func (x *GetNomineesRequest) Reset() {
	*x = GetNomineesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNomineesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNomineesRequest) ProtoMessage() {}

func (x *GetNomineesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNomineesRequest.ProtoReflect.Descriptor instead.
func (*GetNomineesRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{27}
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetNomineesRequest) GetAuth() *header.AuthHeader {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *GetNomineesRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetNomineesRequest) GetFlowType() string {
	if x != nil {
		return x.FlowType
	}
	return ""
}

type GetNomineesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Deprecated in favour of nominees_v2 [frontend.user.Nominee]
	//
	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Nominees   []*typesv2.Nominee     `protobuf:"bytes,2,rep,name=nominees,proto3" json:"nominees,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	NomineesV2 []*Nominee             `protobuf:"bytes,3,rep,name=nominees_v2,json=nomineesV2,proto3" json:"nominees_v2,omitempty"`
}

func (x *GetNomineesResponse) Reset() {
	*x = GetNomineesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNomineesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNomineesResponse) ProtoMessage() {}

func (x *GetNomineesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNomineesResponse.ProtoReflect.Descriptor instead.
func (*GetNomineesResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{28}
}

func (x *GetNomineesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetNomineesResponse) GetNominees() []*typesv2.Nominee {
	if x != nil {
		return x.Nominees
	}
	return nil
}

func (x *GetNomineesResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetNomineesResponse) GetNomineesV2() []*Nominee {
	if x != nil {
		return x.NomineesV2
	}
	return nil
}

type GetNomineeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Auth      *header.AuthHeader    `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	Req       *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	NomineeId string                `protobuf:"bytes,2,opt,name=nominee_id,json=nomineeId,proto3" json:"nominee_id,omitempty"`
}

func (x *GetNomineeRequest) Reset() {
	*x = GetNomineeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNomineeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNomineeRequest) ProtoMessage() {}

func (x *GetNomineeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNomineeRequest.ProtoReflect.Descriptor instead.
func (*GetNomineeRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{29}
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetNomineeRequest) GetAuth() *header.AuthHeader {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *GetNomineeRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetNomineeRequest) GetNomineeId() string {
	if x != nil {
		return x.NomineeId
	}
	return ""
}

type GetNomineeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Deprecated in favour of nominee_v2 [frontend.user.Nominee]
	//
	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Nominee    *typesv2.Nominee       `protobuf:"bytes,2,opt,name=nominee,proto3" json:"nominee,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	NomineeV2  *Nominee               `protobuf:"bytes,3,opt,name=nominee_v2,json=nomineeV2,proto3" json:"nominee_v2,omitempty"`
}

func (x *GetNomineeResponse) Reset() {
	*x = GetNomineeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNomineeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNomineeResponse) ProtoMessage() {}

func (x *GetNomineeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNomineeResponse.ProtoReflect.Descriptor instead.
func (*GetNomineeResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{30}
}

func (x *GetNomineeResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetNomineeResponse) GetNominee() *typesv2.Nominee {
	if x != nil {
		return x.Nominee
	}
	return nil
}

func (x *GetNomineeResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetNomineeResponse) GetNomineeV2() *Nominee {
	if x != nil {
		return x.NomineeV2
	}
	return nil
}

type ConfirmCardShippingPreferenceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Auth *header.AuthHeader    `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	Req  *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	// Type of address chosen by the user.
	// This can be either PERMANENT or MAILING (for address fetched from KYC) or SHIPPING (for address manually entered by the user)
	AddressType typesv2.AddressType `protobuf:"varint,2,opt,name=address_type,json=addressType,proto3,enum=api.typesv2.AddressType" json:"address_type,omitempty"`
}

func (x *ConfirmCardShippingPreferenceRequest) Reset() {
	*x = ConfirmCardShippingPreferenceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmCardShippingPreferenceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmCardShippingPreferenceRequest) ProtoMessage() {}

func (x *ConfirmCardShippingPreferenceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmCardShippingPreferenceRequest.ProtoReflect.Descriptor instead.
func (*ConfirmCardShippingPreferenceRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{31}
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *ConfirmCardShippingPreferenceRequest) GetAuth() *header.AuthHeader {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *ConfirmCardShippingPreferenceRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *ConfirmCardShippingPreferenceRequest) GetAddressType() typesv2.AddressType {
	if x != nil {
		return x.AddressType
	}
	return typesv2.AddressType(0)
}

type ConfirmCardShippingPreferenceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NextAction *deeplink.Deeplink     `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *ConfirmCardShippingPreferenceResponse) Reset() {
	*x = ConfirmCardShippingPreferenceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmCardShippingPreferenceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmCardShippingPreferenceResponse) ProtoMessage() {}

func (x *ConfirmCardShippingPreferenceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmCardShippingPreferenceResponse.ProtoReflect.Descriptor instead.
func (*ConfirmCardShippingPreferenceResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{32}
}

func (x *ConfirmCardShippingPreferenceResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ConfirmCardShippingPreferenceResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *ConfirmCardShippingPreferenceResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type RecordHashedContactsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Auth *header.AuthHeader    `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	Req  *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	// list of MD5 hashed contacts. Maximum of 250 contacts can be sent per rpc call by the client
	Contact []*RecordHashedContactsRequest_Contact `protobuf:"bytes,2,rep,name=contact,proto3" json:"contact,omitempty"`
}

func (x *RecordHashedContactsRequest) Reset() {
	*x = RecordHashedContactsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordHashedContactsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordHashedContactsRequest) ProtoMessage() {}

func (x *RecordHashedContactsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordHashedContactsRequest.ProtoReflect.Descriptor instead.
func (*RecordHashedContactsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{33}
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *RecordHashedContactsRequest) GetAuth() *header.AuthHeader {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *RecordHashedContactsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *RecordHashedContactsRequest) GetContact() []*RecordHashedContactsRequest_Contact {
	if x != nil {
		return x.Contact
	}
	return nil
}

type RecordHashedContactsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *RecordHashedContactsResponse) Reset() {
	*x = RecordHashedContactsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordHashedContactsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordHashedContactsResponse) ProtoMessage() {}

func (x *RecordHashedContactsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordHashedContactsResponse.ProtoReflect.Descriptor instead.
func (*RecordHashedContactsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{34}
}

func (x *RecordHashedContactsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *RecordHashedContactsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type AddressWithType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    typesv2.AddressType    `protobuf:"varint,1,opt,name=type,proto3,enum=api.typesv2.AddressType" json:"type,omitempty"`
	Address *typesv2.PostalAddress `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// We are reusing this rpc to fill the nominee address
	IsEditable bool `protobuf:"varint,3,opt,name=is_editable,json=isEditable,proto3" json:"is_editable,omitempty"`
	// Indicates whether the address is enabled or disabled for selection as delivery address for card
	IsActive bool `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// Additional information associated with the address, such as the reason it cannot be selected as a delivery address.
	InfoText *ui.IconTextComponent `protobuf:"bytes,5,opt,name=info_text,json=infoText,proto3" json:"info_text,omitempty"`
}

func (x *AddressWithType) Reset() {
	*x = AddressWithType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressWithType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressWithType) ProtoMessage() {}

func (x *AddressWithType) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressWithType.ProtoReflect.Descriptor instead.
func (*AddressWithType) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{35}
}

func (x *AddressWithType) GetType() typesv2.AddressType {
	if x != nil {
		return x.Type
	}
	return typesv2.AddressType(0)
}

func (x *AddressWithType) GetAddress() *typesv2.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *AddressWithType) GetIsEditable() bool {
	if x != nil {
		return x.IsEditable
	}
	return false
}

func (x *AddressWithType) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *AddressWithType) GetInfoText() *ui.IconTextComponent {
	if x != nil {
		return x.InfoText
	}
	return nil
}

type SyncContactDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Auth *header.AuthHeader `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	// cursor is a bytes field which encodes the information needed by the backend to perform optimisations on db calls to fetch contact information.
	// It stores info about the contacts already synced and ensures that only the diff in contacts is sent to the client.
	// This field should be populated from the SyncContactDetailsResponse. It's an optional field. It can be empty when called for the first time.
	// Subsequent calls should have this populated from the last response.
	Cursor []byte                `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
	Req    *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *SyncContactDetailsRequest) Reset() {
	*x = SyncContactDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncContactDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncContactDetailsRequest) ProtoMessage() {}

func (x *SyncContactDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncContactDetailsRequest.ProtoReflect.Descriptor instead.
func (*SyncContactDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{36}
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *SyncContactDetailsRequest) GetAuth() *header.AuthHeader {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *SyncContactDetailsRequest) GetCursor() []byte {
	if x != nil {
		return x.Cursor
	}
	return nil
}

func (x *SyncContactDetailsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type SyncContactDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of contact details
	ContactDetails []*SyncContactDetailsResponse_ContactDetails `protobuf:"bytes,2,rep,name=contact_details,json=contactDetails,proto3" json:"contact_details,omitempty"`
	// Cursor sent by backend which the client stores at their end such that it is sent in next request for backend to
	// perform filtered queries
	Cursor     []byte                 `protobuf:"bytes,3,opt,name=cursor,proto3" json:"cursor,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *SyncContactDetailsResponse) Reset() {
	*x = SyncContactDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncContactDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncContactDetailsResponse) ProtoMessage() {}

func (x *SyncContactDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncContactDetailsResponse.ProtoReflect.Descriptor instead.
func (*SyncContactDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{37}
}

func (x *SyncContactDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *SyncContactDetailsResponse) GetContactDetails() []*SyncContactDetailsResponse_ContactDetails {
	if x != nil {
		return x.ContactDetails
	}
	return nil
}

func (x *SyncContactDetailsResponse) GetCursor() []byte {
	if x != nil {
		return x.Cursor
	}
	return nil
}

func (x *SyncContactDetailsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type GetByPhoneNumberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	Auth *header.AuthHeader    `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	Req  *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	// phone number of the user to be searched
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *GetByPhoneNumberRequest) Reset() {
	*x = GetByPhoneNumberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetByPhoneNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetByPhoneNumberRequest) ProtoMessage() {}

func (x *GetByPhoneNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetByPhoneNumberRequest.ProtoReflect.Descriptor instead.
func (*GetByPhoneNumberRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{38}
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetByPhoneNumberRequest) GetAuth() *header.AuthHeader {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *GetByPhoneNumberRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetByPhoneNumberRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

type GetByPhoneNumberResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Denotes the status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// chat head of the fi user
	//
	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	ChatHead *frontend.ChatHead `protobuf:"bytes,2,opt,name=chat_head,json=chatHead,proto3" json:"chat_head,omitempty"`
	// List fo chat heads for the phone number
	ChatHeads  []*frontend.ChatHead   `protobuf:"bytes,3,rep,name=chat_heads,json=chatHeads,proto3" json:"chat_heads,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *GetByPhoneNumberResponse) Reset() {
	*x = GetByPhoneNumberResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetByPhoneNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetByPhoneNumberResponse) ProtoMessage() {}

func (x *GetByPhoneNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetByPhoneNumberResponse.ProtoReflect.Descriptor instead.
func (*GetByPhoneNumberResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{39}
}

func (x *GetByPhoneNumberResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetByPhoneNumberResponse) GetChatHead() *frontend.ChatHead {
	if x != nil {
		return x.ChatHead
	}
	return nil
}

func (x *GetByPhoneNumberResponse) GetChatHeads() []*frontend.ChatHead {
	if x != nil {
		return x.ChatHeads
	}
	return nil
}

func (x *GetByPhoneNumberResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type GetLegalNameRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetLegalNameRequest) Reset() {
	*x = GetLegalNameRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLegalNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLegalNameRequest) ProtoMessage() {}

func (x *GetLegalNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLegalNameRequest.ProtoReflect.Descriptor instead.
func (*GetLegalNameRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{40}
}

func (x *GetLegalNameRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetLegalNameResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LegalName  *common.Name           `protobuf:"bytes,2,opt,name=legal_name,json=legalName,proto3" json:"legal_name,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *GetLegalNameResponse) Reset() {
	*x = GetLegalNameResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLegalNameResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLegalNameResponse) ProtoMessage() {}

func (x *GetLegalNameResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLegalNameResponse.ProtoReflect.Descriptor instead.
func (*GetLegalNameResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{41}
}

func (x *GetLegalNameResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLegalNameResponse) GetLegalName() *common.Name {
	if x != nil {
		return x.LegalName
	}
	return nil
}

func (x *GetLegalNameResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type ConfirmCardPreferencesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// debit card name entered by user
	DebitCardName *common.Name `protobuf:"bytes,2,opt,name=debit_card_name,json=debitCardName,proto3" json:"debit_card_name,omitempty"`
	// name check can be skipped if user uses the existing legal name as debit card name.
	// If user does not changes his legal name UI needs to be pass this flag as true.
	SkipNameCheck bool `protobuf:"varint,3,opt,name=skip_name_check,json=skipNameCheck,proto3" json:"skip_name_check,omitempty"`
	// Type of address chosen by the user.
	// This can be either PERMANENT or MAILING (for address fetched from KYC) or SHIPPING (for address manually entered by the user)
	AddressType             typesv2.AddressType `protobuf:"varint,4,opt,name=address_type,json=addressType,proto3,enum=api.typesv2.AddressType" json:"address_type,omitempty"`
	PurposeOfSavingsAccount string              `protobuf:"bytes,5,opt,name=purpose_of_savings_account,json=purposeOfSavingsAccount,proto3" json:"purpose_of_savings_account,omitempty"`
}

func (x *ConfirmCardPreferencesRequest) Reset() {
	*x = ConfirmCardPreferencesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmCardPreferencesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmCardPreferencesRequest) ProtoMessage() {}

func (x *ConfirmCardPreferencesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmCardPreferencesRequest.ProtoReflect.Descriptor instead.
func (*ConfirmCardPreferencesRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{42}
}

func (x *ConfirmCardPreferencesRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *ConfirmCardPreferencesRequest) GetDebitCardName() *common.Name {
	if x != nil {
		return x.DebitCardName
	}
	return nil
}

func (x *ConfirmCardPreferencesRequest) GetSkipNameCheck() bool {
	if x != nil {
		return x.SkipNameCheck
	}
	return false
}

func (x *ConfirmCardPreferencesRequest) GetAddressType() typesv2.AddressType {
	if x != nil {
		return x.AddressType
	}
	return typesv2.AddressType(0)
}

func (x *ConfirmCardPreferencesRequest) GetPurposeOfSavingsAccount() string {
	if x != nil {
		return x.PurposeOfSavingsAccount
	}
	return ""
}

type ConfirmCardPreferencesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NextAction *deeplink.Deeplink     `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *ConfirmCardPreferencesResponse) Reset() {
	*x = ConfirmCardPreferencesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfirmCardPreferencesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmCardPreferencesResponse) ProtoMessage() {}

func (x *ConfirmCardPreferencesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmCardPreferencesResponse.ProtoReflect.Descriptor instead.
func (*ConfirmCardPreferencesResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{43}
}

func (x *ConfirmCardPreferencesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ConfirmCardPreferencesResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *ConfirmCardPreferencesResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

// request to post user activity on screen
type PostUserActivityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	// use derived_account_id instead
	//
	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// activity made by user than need to be tracked
	UserActivity     user_activity.UserActivity `protobuf:"varint,2,opt,name=user_activity,json=userActivity,proto3,enum=frontend.user.user_activity.UserActivity" json:"user_activity,omitempty"`
	DerivedAccountId string                     `protobuf:"bytes,3,opt,name=derived_account_id,json=derivedAccountId,proto3" json:"derived_account_id,omitempty"`
}

func (x *PostUserActivityRequest) Reset() {
	*x = PostUserActivityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostUserActivityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostUserActivityRequest) ProtoMessage() {}

func (x *PostUserActivityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostUserActivityRequest.ProtoReflect.Descriptor instead.
func (*PostUserActivityRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{44}
}

func (x *PostUserActivityRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *PostUserActivityRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *PostUserActivityRequest) GetUserActivity() user_activity.UserActivity {
	if x != nil {
		return x.UserActivity
	}
	return user_activity.UserActivity(0)
}

func (x *PostUserActivityRequest) GetDerivedAccountId() string {
	if x != nil {
		return x.DerivedAccountId
	}
	return ""
}

type PostUserActivityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Denotes the status of the request
	Status     *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *PostUserActivityResponse) Reset() {
	*x = PostUserActivityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostUserActivityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostUserActivityResponse) ProtoMessage() {}

func (x *PostUserActivityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostUserActivityResponse.ProtoReflect.Descriptor instead.
func (*PostUserActivityResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{45}
}

func (x *PostUserActivityResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *PostUserActivityResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type BlockActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// identifier of the actor who is supposed to be blocked
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// flag specifying whether to report actor as spam along with blocking
	IsSpam bool `protobuf:"varint,3,opt,name=is_spam,json=isSpam,proto3" json:"is_spam,omitempty"`
}

func (x *BlockActorRequest) Reset() {
	*x = BlockActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockActorRequest) ProtoMessage() {}

func (x *BlockActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockActorRequest.ProtoReflect.Descriptor instead.
func (*BlockActorRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{46}
}

func (x *BlockActorRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *BlockActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *BlockActorRequest) GetIsSpam() bool {
	if x != nil {
		return x.IsSpam
	}
	return false
}

type BlockActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *BlockActorResponse) Reset() {
	*x = BlockActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockActorResponse) ProtoMessage() {}

func (x *BlockActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockActorResponse.ProtoReflect.Descriptor instead.
func (*BlockActorResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{47}
}

func (x *BlockActorResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type UnblockActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// identifier of the actor who is supposed to be unblocked
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *UnblockActorRequest) Reset() {
	*x = UnblockActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnblockActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnblockActorRequest) ProtoMessage() {}

func (x *UnblockActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnblockActorRequest.ProtoReflect.Descriptor instead.
func (*UnblockActorRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{48}
}

func (x *UnblockActorRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *UnblockActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type UnblockActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *UnblockActorResponse) Reset() {
	*x = UnblockActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnblockActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnblockActorResponse) ProtoMessage() {}

func (x *UnblockActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnblockActorResponse.ProtoReflect.Descriptor instead.
func (*UnblockActorResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{49}
}

func (x *UnblockActorResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type ReportSpamRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// identifier of the actor who is supposed to be reported as spam.
	// the actor must be already blocked and not marked as spam by the logged in actor for the
	// RPC to succeed
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *ReportSpamRequest) Reset() {
	*x = ReportSpamRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportSpamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportSpamRequest) ProtoMessage() {}

func (x *ReportSpamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportSpamRequest.ProtoReflect.Descriptor instead.
func (*ReportSpamRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{50}
}

func (x *ReportSpamRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *ReportSpamRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type ReportSpamResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *ReportSpamResponse) Reset() {
	*x = ReportSpamResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportSpamResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportSpamResponse) ProtoMessage() {}

func (x *ReportSpamResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportSpamResponse.ProtoReflect.Descriptor instead.
func (*ReportSpamResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{51}
}

func (x *ReportSpamResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type AddAppInstanceIdentifiersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// List of various IDs form different vendors as well as internal app instance IDs (eg. Prospect ID)
	// This can be considered as a mapping of ID name -> ID value, where ID name is expected to be unique
	Identifiers []*typesv2.AppInstanceId `protobuf:"bytes,2,rep,name=identifiers,proto3" json:"identifiers,omitempty"`
}

func (x *AddAppInstanceIdentifiersRequest) Reset() {
	*x = AddAppInstanceIdentifiersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAppInstanceIdentifiersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAppInstanceIdentifiersRequest) ProtoMessage() {}

func (x *AddAppInstanceIdentifiersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAppInstanceIdentifiersRequest.ProtoReflect.Descriptor instead.
func (*AddAppInstanceIdentifiersRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{52}
}

func (x *AddAppInstanceIdentifiersRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *AddAppInstanceIdentifiersRequest) GetIdentifiers() []*typesv2.AppInstanceId {
	if x != nil {
		return x.Identifiers
	}
	return nil
}

type AddAppInstanceIdentifiersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *AddAppInstanceIdentifiersResponse) Reset() {
	*x = AddAppInstanceIdentifiersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAppInstanceIdentifiersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAppInstanceIdentifiersResponse) ProtoMessage() {}

func (x *AddAppInstanceIdentifiersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAppInstanceIdentifiersResponse.ProtoReflect.Descriptor instead.
func (*AddAppInstanceIdentifiersResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{53}
}

func (x *AddAppInstanceIdentifiersResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type SyncVendorIDsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// List of various IDs form different vendors as well as internal app instance IDs (eg. Prospect ID)
	// Identifiers passed wil be be synced in DB.
	Identifiers []*typesv2.AppInstanceId `protobuf:"bytes,2,rep,name=identifiers,proto3" json:"identifiers,omitempty"`
}

func (x *SyncVendorIDsRequest) Reset() {
	*x = SyncVendorIDsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncVendorIDsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncVendorIDsRequest) ProtoMessage() {}

func (x *SyncVendorIDsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncVendorIDsRequest.ProtoReflect.Descriptor instead.
func (*SyncVendorIDsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{54}
}

func (x *SyncVendorIDsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *SyncVendorIDsRequest) GetIdentifiers() []*typesv2.AppInstanceId {
	if x != nil {
		return x.Identifiers
	}
	return nil
}

type SyncVendorIDsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *SyncVendorIDsResponse) Reset() {
	*x = SyncVendorIDsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncVendorIDsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncVendorIDsResponse) ProtoMessage() {}

func (x *SyncVendorIDsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncVendorIDsResponse.ProtoReflect.Descriptor instead.
func (*SyncVendorIDsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{55}
}

func (x *SyncVendorIDsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type ValidateExternalAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req           *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	AccountNumber string                `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	IfscCode      string                `protobuf:"bytes,3,opt,name=ifsc_code,json=ifscCode,proto3" json:"ifsc_code,omitempty"`
	UserGivenName string                `protobuf:"bytes,4,opt,name=user_given_name,json=userGivenName,proto3" json:"user_given_name,omitempty"`
}

func (x *ValidateExternalAccountRequest) Reset() {
	*x = ValidateExternalAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateExternalAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateExternalAccountRequest) ProtoMessage() {}

func (x *ValidateExternalAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateExternalAccountRequest.ProtoReflect.Descriptor instead.
func (*ValidateExternalAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{56}
}

func (x *ValidateExternalAccountRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *ValidateExternalAccountRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *ValidateExternalAccountRequest) GetIfscCode() string {
	if x != nil {
		return x.IfscCode
	}
	return ""
}

func (x *ValidateExternalAccountRequest) GetUserGivenName() string {
	if x != nil {
		return x.UserGivenName
	}
	return ""
}

type ValidateExternalAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader    *header.ResponseHeader         `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	NextAction    *deeplink.Deeplink             `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	FailureReason AccountValidationFailureReason `protobuf:"varint,3,opt,name=failure_reason,json=failureReason,proto3,enum=frontend.user.AccountValidationFailureReason" json:"failure_reason,omitempty"`
}

func (x *ValidateExternalAccountResponse) Reset() {
	*x = ValidateExternalAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateExternalAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateExternalAccountResponse) ProtoMessage() {}

func (x *ValidateExternalAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateExternalAccountResponse.ProtoReflect.Descriptor instead.
func (*ValidateExternalAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{57}
}

func (x *ValidateExternalAccountResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *ValidateExternalAccountResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *ValidateExternalAccountResponse) GetFailureReason() AccountValidationFailureReason {
	if x != nil {
		return x.FailureReason
	}
	return AccountValidationFailureReason_ACCOUNT_VALIDATION_FAILURE_REASON_UNSPECIFIED
}

type GetExternalAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetExternalAccountRequest) Reset() {
	*x = GetExternalAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExternalAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExternalAccountRequest) ProtoMessage() {}

func (x *GetExternalAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExternalAccountRequest.ProtoReflect.Descriptor instead.
func (*GetExternalAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{58}
}

func (x *GetExternalAccountRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetExternalAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	AccountNumber string `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	IfscCode string `protobuf:"bytes,3,opt,name=ifsc_code,json=ifscCode,proto3" json:"ifsc_code,omitempty"`
	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	AccountHolderName string `protobuf:"bytes,4,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	BankLogoUrl       string `protobuf:"bytes,5,opt,name=bank_logo_url,json=bankLogoUrl,proto3" json:"bank_logo_url,omitempty"`
	// represents name
	Title string `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	// can be any of the bank details like bank name, branch
	Line1 string `protobuf:"bytes,7,opt,name=line1,proto3" json:"line1,omitempty"`
	// can be any of the bank details like bank name, branch
	Line2 string `protobuf:"bytes,8,opt,name=line2,proto3" json:"line2,omitempty"`
}

func (x *GetExternalAccountResponse) Reset() {
	*x = GetExternalAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExternalAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExternalAccountResponse) ProtoMessage() {}

func (x *GetExternalAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExternalAccountResponse.ProtoReflect.Descriptor instead.
func (*GetExternalAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{59}
}

func (x *GetExternalAccountResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetExternalAccountResponse) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetExternalAccountResponse) GetIfscCode() string {
	if x != nil {
		return x.IfscCode
	}
	return ""
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetExternalAccountResponse) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

func (x *GetExternalAccountResponse) GetBankLogoUrl() string {
	if x != nil {
		return x.BankLogoUrl
	}
	return ""
}

func (x *GetExternalAccountResponse) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GetExternalAccountResponse) GetLine1() string {
	if x != nil {
		return x.Line1
	}
	return ""
}

func (x *GetExternalAccountResponse) GetLine2() string {
	if x != nil {
		return x.Line2
	}
	return ""
}

type FeatureInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable bool `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *FeatureInfo) Reset() {
	*x = FeatureInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureInfo) ProtoMessage() {}

func (x *FeatureInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureInfo.ProtoReflect.Descriptor instead.
func (*FeatureInfo) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{60}
}

func (x *FeatureInfo) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type SetUserPreferencesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// list of preferences to be set for the requesting user(actor_id)
	Preferences []*PreferenceTypeValuePair `protobuf:"bytes,2,rep,name=preferences,proto3" json:"preferences,omitempty"`
}

func (x *SetUserPreferencesRequest) Reset() {
	*x = SetUserPreferencesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetUserPreferencesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUserPreferencesRequest) ProtoMessage() {}

func (x *SetUserPreferencesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUserPreferencesRequest.ProtoReflect.Descriptor instead.
func (*SetUserPreferencesRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{61}
}

func (x *SetUserPreferencesRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *SetUserPreferencesRequest) GetPreferences() []*PreferenceTypeValuePair {
	if x != nil {
		return x.Preferences
	}
	return nil
}

type SetUserPreferencesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *SetUserPreferencesResponse) Reset() {
	*x = SetUserPreferencesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetUserPreferencesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUserPreferencesResponse) ProtoMessage() {}

func (x *SetUserPreferencesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUserPreferencesResponse.ProtoReflect.Descriptor instead.
func (*SetUserPreferencesResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{62}
}

func (x *SetUserPreferencesResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type GetUserPreferencesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// List of preference_types to be fetched
	// optional field: if the list is empty all preference_types for the user will be fetched
	PreferenceTypes []PreferenceType `protobuf:"varint,2,rep,packed,name=preference_types,json=preferenceTypes,proto3,enum=frontend.user.PreferenceType" json:"preference_types,omitempty"`
}

func (x *GetUserPreferencesRequest) Reset() {
	*x = GetUserPreferencesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserPreferencesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPreferencesRequest) ProtoMessage() {}

func (x *GetUserPreferencesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPreferencesRequest.ProtoReflect.Descriptor instead.
func (*GetUserPreferencesRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{63}
}

func (x *GetUserPreferencesRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetUserPreferencesRequest) GetPreferenceTypes() []PreferenceType {
	if x != nil {
		return x.PreferenceTypes
	}
	return nil
}

type GetUserPreferencesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// list of preferences for the given user and preference_types
	UserPreferences []*UserPreference `protobuf:"bytes,2,rep,name=user_preferences,json=userPreferences,proto3" json:"user_preferences,omitempty"`
}

func (x *GetUserPreferencesResponse) Reset() {
	*x = GetUserPreferencesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserPreferencesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPreferencesResponse) ProtoMessage() {}

func (x *GetUserPreferencesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPreferencesResponse.ProtoReflect.Descriptor instead.
func (*GetUserPreferencesResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{64}
}

func (x *GetUserPreferencesResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetUserPreferencesResponse) GetUserPreferences() []*UserPreference {
	if x != nil {
		return x.UserPreferences
	}
	return nil
}

type GetAccountDetailsByDerivedIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// derived_account_id - account id derived from combination of :
	// internal account id, tpap account id, connected account id, deposit account id
	DerivedAccountId string `protobuf:"bytes,2,opt,name=derived_account_id,json=derivedAccountId,proto3" json:"derived_account_id,omitempty"`
}

func (x *GetAccountDetailsByDerivedIdRequest) Reset() {
	*x = GetAccountDetailsByDerivedIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountDetailsByDerivedIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountDetailsByDerivedIdRequest) ProtoMessage() {}

func (x *GetAccountDetailsByDerivedIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountDetailsByDerivedIdRequest.ProtoReflect.Descriptor instead.
func (*GetAccountDetailsByDerivedIdRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{65}
}

func (x *GetAccountDetailsByDerivedIdRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetAccountDetailsByDerivedIdRequest) GetDerivedAccountId() string {
	if x != nil {
		return x.DerivedAccountId
	}
	return ""
}

type GetAccountDetailsByDerivedIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// user account details fetched from derived account id
	UserAccountDetails *UserAccountDetails `protobuf:"bytes,2,opt,name=user_account_details,json=userAccountDetails,proto3" json:"user_account_details,omitempty"`
}

func (x *GetAccountDetailsByDerivedIdResponse) Reset() {
	*x = GetAccountDetailsByDerivedIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountDetailsByDerivedIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountDetailsByDerivedIdResponse) ProtoMessage() {}

func (x *GetAccountDetailsByDerivedIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountDetailsByDerivedIdResponse.ProtoReflect.Descriptor instead.
func (*GetAccountDetailsByDerivedIdResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{66}
}

func (x *GetAccountDetailsByDerivedIdResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetAccountDetailsByDerivedIdResponse) GetUserAccountDetails() *UserAccountDetails {
	if x != nil {
		return x.UserAccountDetails
	}
	return nil
}

type GetMoEngageIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetMoEngageIdRequest) Reset() {
	*x = GetMoEngageIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMoEngageIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMoEngageIdRequest) ProtoMessage() {}

func (x *GetMoEngageIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMoEngageIdRequest.ProtoReflect.Descriptor instead.
func (*GetMoEngageIdRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{67}
}

func (x *GetMoEngageIdRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetMoEngageIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Moengage ID from VendorMapping
	MoengageId string `protobuf:"bytes,2,opt,name=moengage_id,json=moengageId,proto3" json:"moengage_id,omitempty"`
}

func (x *GetMoEngageIdResponse) Reset() {
	*x = GetMoEngageIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMoEngageIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMoEngageIdResponse) ProtoMessage() {}

func (x *GetMoEngageIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMoEngageIdResponse.ProtoReflect.Descriptor instead.
func (*GetMoEngageIdResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{68}
}

func (x *GetMoEngageIdResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetMoEngageIdResponse) GetMoengageId() string {
	if x != nil {
		return x.MoengageId
	}
	return ""
}

type UpdateFormDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// source flow for analytics purposes. For simplicity, any business logic on this is not advised.
	Source string `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	// data collected from the user in the form. The key for the map is the field_id corresponding to the respective InputField
	Values map[string]*form.FieldValue `protobuf:"bytes,3,rep,name=values,proto3" json:"values,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// list of consents collected from the user
	ConsentIds []string `protobuf:"bytes,4,rep,name=consent_ids,json=consentIds,proto3" json:"consent_ids,omitempty"`
}

func (x *UpdateFormDetailsRequest) Reset() {
	*x = UpdateFormDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateFormDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFormDetailsRequest) ProtoMessage() {}

func (x *UpdateFormDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFormDetailsRequest.ProtoReflect.Descriptor instead.
func (*UpdateFormDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{69}
}

func (x *UpdateFormDetailsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *UpdateFormDetailsRequest) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *UpdateFormDetailsRequest) GetValues() map[string]*form.FieldValue {
	if x != nil {
		return x.Values
	}
	return nil
}

func (x *UpdateFormDetailsRequest) GetConsentIds() []string {
	if x != nil {
		return x.ConsentIds
	}
	return nil
}

type UpdateFormDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// field_id <> error message mapping. The error message has to be shown inline
	FieldInlineError map[string]*common.Text `protobuf:"bytes,2,rep,name=field_inline_error,json=fieldInlineError,proto3" json:"field_inline_error,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdateFormDetailsResponse) Reset() {
	*x = UpdateFormDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateFormDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFormDetailsResponse) ProtoMessage() {}

func (x *UpdateFormDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFormDetailsResponse.ProtoReflect.Descriptor instead.
func (*UpdateFormDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{70}
}

func (x *UpdateFormDetailsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *UpdateFormDetailsResponse) GetFieldInlineError() map[string]*common.Text {
	if x != nil {
		return x.FieldInlineError
	}
	return nil
}

type FetchFormDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// source flow for analytics purposes. For simplicity, any business logic on this is not advised.
	Source string `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	// Client needs to pass the field_id sent with EditableFields in the UPDATE_USER_DETAILS screen options
	// On backend, it maps to ENUM api.typesv2.Form.FieldIdentifier
	FieldIds []string `protobuf:"bytes,3,rep,name=field_ids,json=fieldIds,proto3" json:"field_ids,omitempty"`
}

func (x *FetchFormDetailsRequest) Reset() {
	*x = FetchFormDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchFormDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchFormDetailsRequest) ProtoMessage() {}

func (x *FetchFormDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchFormDetailsRequest.ProtoReflect.Descriptor instead.
func (*FetchFormDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{71}
}

func (x *FetchFormDetailsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *FetchFormDetailsRequest) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *FetchFormDetailsRequest) GetFieldIds() []string {
	if x != nil {
		return x.FieldIds
	}
	return nil
}

type FetchFormDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// PII data that needs to be pre-populated on the form. Key of the map is the field_id corresponding to the respective InputField or Card
	Values map[string]*form.FieldValue `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *FetchFormDetailsResponse) Reset() {
	*x = FetchFormDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchFormDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchFormDetailsResponse) ProtoMessage() {}

func (x *FetchFormDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchFormDetailsResponse.ProtoReflect.Descriptor instead.
func (*FetchFormDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{72}
}

func (x *FetchFormDetailsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *FetchFormDetailsResponse) GetValues() map[string]*form.FieldValue {
	if x != nil {
		return x.Values
	}
	return nil
}

type GetAddressEntryScreenFromLocationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// location reference for the address entry
	LatLng *latlng.LatLng      `protobuf:"bytes,2,opt,name=lat_lng,json=latLng,proto3" json:"lat_lng,omitempty"`
	Type   typesv2.AddressType `protobuf:"varint,3,opt,name=type,proto3,enum=api.typesv2.AddressType" json:"type,omitempty"`
}

func (x *GetAddressEntryScreenFromLocationRequest) Reset() {
	*x = GetAddressEntryScreenFromLocationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressEntryScreenFromLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressEntryScreenFromLocationRequest) ProtoMessage() {}

func (x *GetAddressEntryScreenFromLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressEntryScreenFromLocationRequest.ProtoReflect.Descriptor instead.
func (*GetAddressEntryScreenFromLocationRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{73}
}

func (x *GetAddressEntryScreenFromLocationRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetAddressEntryScreenFromLocationRequest) GetLatLng() *latlng.LatLng {
	if x != nil {
		return x.LatLng
	}
	return nil
}

func (x *GetAddressEntryScreenFromLocationRequest) GetType() typesv2.AddressType {
	if x != nil {
		return x.Type
	}
	return typesv2.AddressType(0)
}

type GetAddressEntryScreenFromLocationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// address entry screen
	NextAction *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *GetAddressEntryScreenFromLocationResponse) Reset() {
	*x = GetAddressEntryScreenFromLocationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressEntryScreenFromLocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressEntryScreenFromLocationResponse) ProtoMessage() {}

func (x *GetAddressEntryScreenFromLocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressEntryScreenFromLocationResponse.ProtoReflect.Descriptor instead.
func (*GetAddressEntryScreenFromLocationResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{74}
}

func (x *GetAddressEntryScreenFromLocationResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetAddressEntryScreenFromLocationResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type SendSmsDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req     *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	SmsData []*SmsData            `protobuf:"bytes,2,rep,name=sms_data,json=smsData,proto3" json:"sms_data,omitempty"`
}

func (x *SendSmsDataRequest) Reset() {
	*x = SendSmsDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSmsDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsDataRequest) ProtoMessage() {}

func (x *SendSmsDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsDataRequest.ProtoReflect.Descriptor instead.
func (*SendSmsDataRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{75}
}

func (x *SendSmsDataRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *SendSmsDataRequest) GetSmsData() []*SmsData {
	if x != nil {
		return x.SmsData
	}
	return nil
}

type SmsData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SmsDataError SmsDataError `protobuf:"varint,1,opt,name=sms_data_error,json=smsDataError,proto3,enum=frontend.user.SmsDataError" json:"sms_data_error,omitempty"`
	// Type of data to be stored
	//
	// Types that are assignable to SmsDataType:
	//
	//	*SmsData_Pan
	SmsDataType isSmsData_SmsDataType `protobuf_oneof:"sms_data_type"`
}

func (x *SmsData) Reset() {
	*x = SmsData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmsData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmsData) ProtoMessage() {}

func (x *SmsData) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmsData.ProtoReflect.Descriptor instead.
func (*SmsData) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{76}
}

func (x *SmsData) GetSmsDataError() SmsDataError {
	if x != nil {
		return x.SmsDataError
	}
	return SmsDataError_SMS_DATA_ERRORS_UNSPECIFIED
}

func (m *SmsData) GetSmsDataType() isSmsData_SmsDataType {
	if m != nil {
		return m.SmsDataType
	}
	return nil
}

func (x *SmsData) GetPan() string {
	if x, ok := x.GetSmsDataType().(*SmsData_Pan); ok {
		return x.Pan
	}
	return ""
}

type isSmsData_SmsDataType interface {
	isSmsData_SmsDataType()
}

type SmsData_Pan struct {
	Pan string `protobuf:"bytes,2,opt,name=pan,proto3,oneof"`
}

func (*SmsData_Pan) isSmsData_SmsDataType() {}

type SendSmsDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *SendSmsDataResponse) Reset() {
	*x = SendSmsDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSmsDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsDataResponse) ProtoMessage() {}

func (x *SendSmsDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsDataResponse.ProtoReflect.Descriptor instead.
func (*SendSmsDataResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{77}
}

func (x *SendSmsDataResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type GetUserSessionDetailsResponse_AccountDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id belonging to the user
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// boolean denoting if UPI pin is set for the account
	//
	// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
	IsUpiPinSet bool `protobuf:"varint,2,opt,name=is_upi_pin_set,json=isUpiPinSet,proto3" json:"is_upi_pin_set,omitempty"`
	// PIN_NOT_SET -> UPI pin is not set
	// PIN_SET -> UPI pin is not set
	// REOOBE_PIN_NOT_SET -> UPI pin is not set after reoobe
	PinSetState upi.PinSetState `protobuf:"varint,3,opt,name=pin_set_state,json=pinSetState,proto3,enum=frontend.account.upi.PinSetState" json:"pin_set_state,omitempty"`
	// masked account number
	MaskedAccountNumber string `protobuf:"bytes,4,opt,name=masked_account_number,json=maskedAccountNumber,proto3" json:"masked_account_number,omitempty"`
	// partner bank name
	PartnerBank string `protobuf:"bytes,5,opt,name=partner_bank,json=partnerBank,proto3" json:"partner_bank,omitempty"`
	// bank logo url of partner bank
	BankLogoUrl string `protobuf:"bytes,6,opt,name=bank_logo_url,json=bankLogoUrl,proto3" json:"bank_logo_url,omitempty"`
	// account type (ex. Saving account, current account etc.)
	AccountType accounts.Type `protobuf:"varint,7,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
}

func (x *GetUserSessionDetailsResponse_AccountDetails) Reset() {
	*x = GetUserSessionDetailsResponse_AccountDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserSessionDetailsResponse_AccountDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSessionDetailsResponse_AccountDetails) ProtoMessage() {}

func (x *GetUserSessionDetailsResponse_AccountDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSessionDetailsResponse_AccountDetails.ProtoReflect.Descriptor instead.
func (*GetUserSessionDetailsResponse_AccountDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{24, 0}
}

func (x *GetUserSessionDetailsResponse_AccountDetails) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/frontend/user/service.proto.
func (x *GetUserSessionDetailsResponse_AccountDetails) GetIsUpiPinSet() bool {
	if x != nil {
		return x.IsUpiPinSet
	}
	return false
}

func (x *GetUserSessionDetailsResponse_AccountDetails) GetPinSetState() upi.PinSetState {
	if x != nil {
		return x.PinSetState
	}
	return upi.PinSetState(0)
}

func (x *GetUserSessionDetailsResponse_AccountDetails) GetMaskedAccountNumber() string {
	if x != nil {
		return x.MaskedAccountNumber
	}
	return ""
}

func (x *GetUserSessionDetailsResponse_AccountDetails) GetPartnerBank() string {
	if x != nil {
		return x.PartnerBank
	}
	return ""
}

func (x *GetUserSessionDetailsResponse_AccountDetails) GetBankLogoUrl() string {
	if x != nil {
		return x.BankLogoUrl
	}
	return ""
}

func (x *GetUserSessionDetailsResponse_AccountDetails) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

type GetUserSessionDetailsResponse_CardTabs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Background color for tab container
	BgTabs *widget.BackgroundColour `protobuf:"bytes,1,opt,name=bg_tabs,json=bgTabs,proto3" json:"bg_tabs,omitempty"`
	// Corner radius of the tab container
	CornerRadius int32                                         `protobuf:"varint,2,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
	CardsTab     []*GetUserSessionDetailsResponse_CardTabs_Tab `protobuf:"bytes,3,rep,name=cards_tab,json=cardsTab,proto3" json:"cards_tab,omitempty"`
}

func (x *GetUserSessionDetailsResponse_CardTabs) Reset() {
	*x = GetUserSessionDetailsResponse_CardTabs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserSessionDetailsResponse_CardTabs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSessionDetailsResponse_CardTabs) ProtoMessage() {}

func (x *GetUserSessionDetailsResponse_CardTabs) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSessionDetailsResponse_CardTabs.ProtoReflect.Descriptor instead.
func (*GetUserSessionDetailsResponse_CardTabs) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{24, 3}
}

func (x *GetUserSessionDetailsResponse_CardTabs) GetBgTabs() *widget.BackgroundColour {
	if x != nil {
		return x.BgTabs
	}
	return nil
}

func (x *GetUserSessionDetailsResponse_CardTabs) GetCornerRadius() int32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

func (x *GetUserSessionDetailsResponse_CardTabs) GetCardsTab() []*GetUserSessionDetailsResponse_CardTabs_Tab {
	if x != nil {
		return x.CardsTab
	}
	return nil
}

// Tab contains the tab type and the selected/unselected tab UI
type GetUserSessionDetailsResponse_CardTabs_Tab struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TabType       GetUserSessionDetailsResponse_CardTabs_TabType `protobuf:"varint,1,opt,name=tab_type,json=tabType,proto3,enum=frontend.user.GetUserSessionDetailsResponse_CardTabs_TabType" json:"tab_type,omitempty"`
	UnselectedTab *ui.IconTextComponent                          `protobuf:"bytes,2,opt,name=unselected_tab,json=unselectedTab,proto3" json:"unselected_tab,omitempty"`
	SelectedTab   *ui.IconTextComponent                          `protobuf:"bytes,3,opt,name=selected_tab,json=selectedTab,proto3" json:"selected_tab,omitempty"`
}

func (x *GetUserSessionDetailsResponse_CardTabs_Tab) Reset() {
	*x = GetUserSessionDetailsResponse_CardTabs_Tab{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserSessionDetailsResponse_CardTabs_Tab) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSessionDetailsResponse_CardTabs_Tab) ProtoMessage() {}

func (x *GetUserSessionDetailsResponse_CardTabs_Tab) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSessionDetailsResponse_CardTabs_Tab.ProtoReflect.Descriptor instead.
func (*GetUserSessionDetailsResponse_CardTabs_Tab) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{24, 3, 0}
}

func (x *GetUserSessionDetailsResponse_CardTabs_Tab) GetTabType() GetUserSessionDetailsResponse_CardTabs_TabType {
	if x != nil {
		return x.TabType
	}
	return GetUserSessionDetailsResponse_CardTabs_TAB_TYPE_UNSPECIFIED
}

func (x *GetUserSessionDetailsResponse_CardTabs_Tab) GetUnselectedTab() *ui.IconTextComponent {
	if x != nil {
		return x.UnselectedTab
	}
	return nil
}

func (x *GetUserSessionDetailsResponse_CardTabs_Tab) GetSelectedTab() *ui.IconTextComponent {
	if x != nil {
		return x.SelectedTab
	}
	return nil
}

type RecordHashedContactsRequest_Contact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// MD5 hashed phone number
	PhoneNumberHash string `protobuf:"bytes,1,opt,name=phone_number_hash,json=phoneNumberHash,proto3" json:"phone_number_hash,omitempty"`
	// boolean denoting if the user has deleted this
	// contact from the contact list
	IsDeleted bool `protobuf:"varint,2,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
}

func (x *RecordHashedContactsRequest_Contact) Reset() {
	*x = RecordHashedContactsRequest_Contact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordHashedContactsRequest_Contact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordHashedContactsRequest_Contact) ProtoMessage() {}

func (x *RecordHashedContactsRequest_Contact) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordHashedContactsRequest_Contact.ProtoReflect.Descriptor instead.
func (*RecordHashedContactsRequest_Contact) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{33, 0}
}

func (x *RecordHashedContactsRequest_Contact) GetPhoneNumberHash() string {
	if x != nil {
		return x.PhoneNumberHash
	}
	return ""
}

func (x *RecordHashedContactsRequest_Contact) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

type SyncContactDetailsResponse_ContactDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// phone number hash for the contact details
	PhoneNumberHash string `protobuf:"bytes,1,opt,name=phone_number_hash,json=phoneNumberHash,proto3" json:"phone_number_hash,omitempty"`
	// verified name of the contact as stored on the server
	// will be empty in case the contact is not on fi
	VerifiedName string `protobuf:"bytes,2,opt,name=verified_name,json=verifiedName,proto3" json:"verified_name,omitempty"`
	// profile url of the contact user
	// will be empty in case the contact is not on fi
	IconUrl string `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	// bool denoting if the contact is to be shown as new on fi or not
	IsNewOnFi bool `protobuf:"varint,4,opt,name=is_new_on_fi,json=isNewOnFi,proto3" json:"is_new_on_fi,omitempty"`
	// optional: in case icon image is missing
	// then the client needs to generate the image
	// using name full_title and color_code as background
	// rgb hex will be returned as colour code
	ColourCode string `protobuf:"bytes,5,opt,name=colour_code,json=colourCode,proto3" json:"colour_code,omitempty"`
}

func (x *SyncContactDetailsResponse_ContactDetails) Reset() {
	*x = SyncContactDetailsResponse_ContactDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_user_service_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncContactDetailsResponse_ContactDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncContactDetailsResponse_ContactDetails) ProtoMessage() {}

func (x *SyncContactDetailsResponse_ContactDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_user_service_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncContactDetailsResponse_ContactDetails.ProtoReflect.Descriptor instead.
func (*SyncContactDetailsResponse_ContactDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_user_service_proto_rawDescGZIP(), []int{37, 0}
}

func (x *SyncContactDetailsResponse_ContactDetails) GetPhoneNumberHash() string {
	if x != nil {
		return x.PhoneNumberHash
	}
	return ""
}

func (x *SyncContactDetailsResponse_ContactDetails) GetVerifiedName() string {
	if x != nil {
		return x.VerifiedName
	}
	return ""
}

func (x *SyncContactDetailsResponse_ContactDetails) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *SyncContactDetailsResponse_ContactDetails) GetIsNewOnFi() bool {
	if x != nil {
		return x.IsNewOnFi
	}
	return false
}

func (x *SyncContactDetailsResponse_ContactDetails) GetColourCode() string {
	if x != nil {
		return x.ColourCode
	}
	return ""
}

var File_api_frontend_user_service_proto protoreflect.FileDescriptor

var file_api_frontend_user_service_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0d, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x75, 0x70, 0x69, 0x2f,
	0x75, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x61, 0x6e, 0x61, 0x6c,
	0x79, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x6d, 0x73, 0x5f, 0x63, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x68, 0x61, 0x74,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x73, 0x6d, 0x73, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x2f, 0x73, 0x63, 0x69, 0x65, 0x6e, 0x61, 0x70, 0x74, 0x69, 0x63, 0x5f, 0x73, 0x6d, 0x73,
	0x5f, 0x73, 0x63, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x6e, 0x6f,
	0x6d, 0x69, 0x6e, 0x65, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63,
	0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e,
	0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x4a, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2f, 0x66, 0x69, 0x5f, 0x6c, 0x69, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6e,
	0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f,
	0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x77, 0x69, 0x74, 0x68,
	0x5f, 0x68, 0x79, 0x70, 0x65, 0x72, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7b, 0x0a, 0x26,
	0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69,
	0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65,
	0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x22, 0x80, 0x02, 0x0a, 0x27, 0x47, 0x65,
	0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x92, 0x01, 0x0a, 0x1f, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x4b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x1c,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x57, 0x0a, 0x23,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x67, 0x65, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xe9, 0x01, 0x0a, 0x21, 0x53, 0x65, 0x74, 0x43, 0x61, 0x6c,
	0x6c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72,
	0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x48, 0x0a,
	0x0b, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x65, 0x6e, 0x74,
	0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x48, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x50, 0x61, 0x69, 0x72, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x73, 0x22, 0xc4, 0x02, 0x0a, 0x22, 0x53, 0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x4c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x3e, 0x0a, 0x0e, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x70, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0c, 0x65, 0x64, 0x69, 0x74,
	0x50, 0x72, 0x65, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3e, 0x0a, 0x09, 0x69, 0x6e, 0x66, 0x6f,
	0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08,
	0x69, 0x6e, 0x66, 0x6f, 0x49, 0x63, 0x6f, 0x6e, 0x22, 0x9a, 0x02, 0x0a, 0x24, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x67, 0x65, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x14, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x54,
	0x65, 0x78, 0x74, 0x12, 0x60, 0x0a, 0x1a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x68, 0x79, 0x70, 0x65, 0x72, 0x6c, 0x69, 0x6e, 0x6b,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x57, 0x69, 0x74,
	0x68, 0x48, 0x79, 0x70, 0x65, 0x72, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x52, 0x18, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x48, 0x79, 0x70, 0x65, 0x72,
	0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x22, 0x89, 0x01, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x4f, 0x42, 0x46, 0x6f, 0x72, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x52, 0x65, 0x74, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x0d, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74,
	0x68, 0x22, 0x97, 0x02, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x4f, 0x42, 0x46,
	0x6f, 0x72, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x17, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x6f, 0x62,
	0x5f, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x6d, 0x61, 0x78, 0x44, 0x6f, 0x62, 0x44, 0x65,
	0x64, 0x75, 0x70, 0x65, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x1b,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x6f, 0x62, 0x5f, 0x64, 0x65, 0x64, 0x75,
	0x70, 0x65, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x18, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x44, 0x6f, 0x62, 0x44, 0x65, 0x64,
	0x75, 0x70, 0x65, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x22, 0x4b, 0x0a, 0x17, 0x47,
	0x65, 0x74, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xc3, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0f, 0x64, 0x65,
	0x62, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0d, 0x64,
	0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0b,
	0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x8f,
	0x02, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x2c, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3e, 0x0a, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x22, 0x9d, 0x01, 0x0a, 0x12, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x40, 0x0a, 0x0b,
	0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x10,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x22, 0xfb, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x04, 0x61, 0x75, 0x74,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x12, 0x30,
	0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71,
	0x12, 0x2c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3e,
	0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f,
	0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xce,
	0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a,
	0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52,
	0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x0b, 0x72,
	0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x10, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x22,
	0xa8, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65,
	0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x2c, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0xc7, 0x01, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73,
	0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x22, 0x10, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x22, 0xc7, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x12, 0x30, 0x0a,
	0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12,
	0x3d, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x6f,
	0x77, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x6f, 0x77, 0x22, 0xc5,
	0x02, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x65, 0x73, 0x12, 0x4c, 0x0a, 0x11, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x57,
	0x69, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x57, 0x69, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x57, 0x69, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x32, 0x0a, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x81, 0x01, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x9b, 0x01, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x50, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x12, 0x30, 0x0a, 0x03,
	0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xbe, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x50, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x10, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x22, 0xc8, 0x01, 0x0a, 0x1d, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x46, 0x61, 0x74, 0x68, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x04, 0x61,
	0x75, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68,
	0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72,
	0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0xc5, 0x01, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d,
	0x6f, 0x74, 0x68, 0x65, 0x72, 0x46, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0b,
	0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a,
	0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65,
	0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x85, 0x01, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a,
	0x04, 0x61, 0x75, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x75,
	0x74, 0x68, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x61, 0x75,
	0x74, 0x68, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x03, 0x72, 0x65, 0x71, 0x22, 0x9e, 0x11, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x68, 0x0a, 0x0f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x72, 0x65, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x72, 0x65, 0x62, 0x61, 0x73, 0x65, 0x49,
	0x64, 0x12, 0x82, 0x01, 0x0a, 0x18, 0x66, 0x69, 0x72, 0x65, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x46, 0x69, 0x72, 0x65, 0x62, 0x61, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x16,
	0x66, 0x69, 0x72, 0x65, 0x62, 0x61, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x5d, 0x0a, 0x0b,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0a, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4d, 0x61, 0x70, 0x12, 0x26, 0x0a, 0x0f, 0x69,
	0x73, 0x5f, 0x73, 0x61, 0x5f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x65, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x53, 0x61, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x5f, 0x6c, 0x69, 0x74,
	0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73,
	0x46, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x73,
	0x5f, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x48, 0x6f, 0x6d, 0x65, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x12, 0x4f, 0x0a, 0x11, 0x6d, 0x73, 0x5f, 0x63, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x2e, 0x4d, 0x73, 0x43, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0f, 0x6d, 0x73, 0x43, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x72, 0x0a, 0x1d, 0x73, 0x63, 0x69, 0x65, 0x6e, 0x61, 0x70, 0x74, 0x69, 0x63,
	0x5f, 0x73, 0x6d, 0x73, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x73, 0x6d, 0x73, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e,
	0x53, 0x63, 0x69, 0x65, 0x6e, 0x61, 0x70, 0x74, 0x69, 0x63, 0x53, 0x6d, 0x73, 0x53, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x1a, 0x73, 0x63, 0x69, 0x65,
	0x6e, 0x61, 0x70, 0x74, 0x69, 0x63, 0x53, 0x6d, 0x73, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x22, 0x0a, 0x0d, 0x6d, 0x73, 0x5f, 0x63, 0x6c, 0x61,
	0x72, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d,
	0x73, 0x43, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x52, 0x0a, 0x09, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x74, 0x61, 0x62, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x54, 0x61, 0x62, 0x73, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x54, 0x61, 0x62, 0x73, 0x1a, 0xcd,
	0x02, 0x0a, 0x0e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x27, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x75, 0x70, 0x69, 0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x73,
	0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x69, 0x73,
	0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x12, 0x45, 0x0a, 0x0d, 0x70, 0x69, 0x6e,
	0x5f, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x21, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x0b, 0x70, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x32, 0x0a, 0x15, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f,
	0x62, 0x61, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x6e, 0x6b, 0x5f,
	0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x62, 0x61, 0x6e, 0x6b, 0x4c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x31, 0x0a, 0x0c, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x49,
	0x0a, 0x1b, 0x46, 0x69, 0x72, 0x65, 0x62, 0x61, 0x73, 0x65, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x59, 0x0a, 0x0f, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x88, 0x04, 0x0a, 0x08, 0x43, 0x61, 0x72, 0x64, 0x54, 0x61, 0x62,
	0x73, 0x12, 0x47, 0x0a, 0x07, 0x62, 0x67, 0x5f, 0x74, 0x61, 0x62, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x52, 0x06, 0x62, 0x67, 0x54, 0x61, 0x62, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f,
	0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12,
	0x56, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x73, 0x5f, 0x74, 0x61, 0x62, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x39, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x61, 0x62, 0x73, 0x2e, 0x54, 0x61, 0x62, 0x52, 0x08, 0x63,
	0x61, 0x72, 0x64, 0x73, 0x54, 0x61, 0x62, 0x1a, 0xef, 0x01, 0x0a, 0x03, 0x54, 0x61, 0x62, 0x12,
	0x58, 0x0a, 0x08, 0x74, 0x61, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x3d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x43, 0x61, 0x72, 0x64, 0x54, 0x61, 0x62, 0x73, 0x2e, 0x54, 0x61, 0x62, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x07, 0x74, 0x61, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x48, 0x0a, 0x0e, 0x75, 0x6e, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x61, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x75, 0x6e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x54, 0x61, 0x62, 0x12, 0x44, 0x0a, 0x0c, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x74, 0x61, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x61, 0x62, 0x22, 0x44, 0x0a, 0x07, 0x54, 0x61, 0x62,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x41, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e,
	0x0a, 0x0a, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x0f,
	0x0a, 0x0b, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x02, 0x22,
	0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x89, 0x02, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33,
	0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x41,
	0x75, 0x74, 0x68, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x61,
	0x75, 0x74, 0x68, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x32, 0x0a, 0x07, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x07, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x65, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x0a, 0x6e, 0x6f,
	0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x76, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x4e,
	0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x52, 0x09, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x56,
	0x32, 0x22, 0xea, 0x02, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x6d, 0x69,
	0x6e, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x49, 0x64, 0x12,
	0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0x91, 0x01, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x37, 0x0a, 0x32, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x45, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x4d, 0x49, 0x4e, 0x45, 0x45, 0x5f, 0x53, 0x41, 0x4d, 0x45, 0x5f,
	0x41, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x45,
	0x52, 0x10, 0xcd, 0x03, 0x12, 0x38, 0x0a, 0x33, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x50,
	0x52, 0x45, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x47, 0x55, 0x41, 0x52,
	0x44, 0x49, 0x41, 0x4e, 0x5f, 0x53, 0x41, 0x4d, 0x45, 0x5f, 0x41, 0x53, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x45, 0x52, 0x10, 0xce, 0x03, 0x22, 0x98,
	0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65,
	0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x22, 0xeb, 0x01, 0x0a, 0x13, 0x47, 0x65,
	0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x08, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x08, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x0b,
	0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x37,
	0x0a, 0x0b, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x73, 0x5f, 0x76, 0x32, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x52, 0x0a, 0x6e, 0x6f, 0x6d,
	0x69, 0x6e, 0x65, 0x65, 0x73, 0x56, 0x32, 0x22, 0x99, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4e,
	0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a,
	0x04, 0x61, 0x75, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x75,
	0x74, 0x68, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x61, 0x75,
	0x74, 0x68, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x03, 0x72, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65,
	0x65, 0x49, 0x64, 0x22, 0xe6, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e,
	0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x32, 0x0a, 0x07, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e,
	0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x6e, 0x6f, 0x6d, 0x69,
	0x6e, 0x65, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x0a, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65,
	0x5f, 0x76, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65,
	0x65, 0x52, 0x09, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x56, 0x32, 0x22, 0xca, 0x01, 0x0a,
	0x24, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x53, 0x68, 0x69, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65,
	0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x3b, 0x0a, 0x0c,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0xcc, 0x01, 0x0a, 0x25, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x72, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x53, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65,
	0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xb3, 0x02, 0x0a, 0x1b, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x48, 0x61, 0x73, 0x68, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x12, 0x30, 0x0a,
	0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12,
	0x57, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x48, 0x61, 0x73, 0x68, 0x65, 0x64, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x92, 0x01, 0x03, 0x10, 0xfa, 0x01, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x1a, 0x54, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x48, 0x61, 0x73, 0x68, 0x12,
	0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x22, 0xbe,
	0x01, 0x0a, 0x1c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x48, 0x61, 0x73, 0x68, 0x65, 0x64, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x37, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4c,
	0x49, 0x4d, 0x49, 0x54, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x64, 0x22,
	0xf3, 0x01, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x57, 0x69, 0x74, 0x68, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x34, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x65, 0x64,
	0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73,
	0x45, 0x64, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x3e, 0x0a, 0x09, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x69, 0x6e, 0x66,
	0x6f, 0x54, 0x65, 0x78, 0x74, 0x22, 0x9a, 0x01, 0x0a, 0x19, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73,
	0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72,
	0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72,
	0x65, 0x71, 0x22, 0xf8, 0x03, 0x0a, 0x1a, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x61, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x38, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72,
	0x73, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f,
	0x72, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x1a, 0xbe, 0x01, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x48, 0x61,
	0x73, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55,
	0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x6f, 0x6e, 0x5f,
	0x66, 0x69, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x4f,
	0x6e, 0x46, 0x69, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x22, 0x37, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x0d, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x49, 0x4d,
	0x49, 0x54, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x64, 0x22, 0xc4, 0x01,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x04, 0x61, 0x75, 0x74,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x12, 0x30,
	0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71,
	0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x22, 0x98, 0x02, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x42, 0x79, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x48, 0x65, 0x61, 0x64, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x08, 0x63, 0x68, 0x61, 0x74, 0x48, 0x65, 0x61, 0x64, 0x12, 0x31, 0x0a, 0x0a, 0x63,
	0x68, 0x61, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x52, 0x09, 0x63, 0x68, 0x61, 0x74, 0x48, 0x65, 0x61, 0x64, 0x73, 0x12, 0x40,
	0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10,
	0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22,
	0x47, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xb6, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74,
	0x4c, 0x65, 0x67, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x0a, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x4e, 0x61, 0x6d, 0x65, 0x52, 0x09, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0xb5, 0x02, 0x0a, 0x1d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x43, 0x61, 0x72,
	0x64, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x40, 0x0a, 0x0f, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0d, 0x64, 0x65, 0x62, 0x69, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x6b, 0x69, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12,
	0x3b, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x1a,
	0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x73, 0x61, 0x76, 0x69, 0x6e,
	0x67, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x17, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x4f, 0x66, 0x53, 0x61, 0x76, 0x69, 0x6e,
	0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xc5, 0x01, 0x0a, 0x1e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x72, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0xf5, 0x01, 0x0a, 0x17, 0x50, 0x6f, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a,
	0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12,
	0x21, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x4e, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x12, 0x35, 0x0a, 0x12, 0x64, 0x65, 0x72, 0x69, 0x76, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x10, 0x64, 0x65, 0x72, 0x69, 0x76, 0x65, 0x64,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xb0, 0x01, 0x0a, 0x18, 0x50, 0x6f,
	0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0b, 0x72,
	0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x2d, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x82, 0x01, 0x0a,
	0x11, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x03, 0x72, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x73,
	0x70, 0x61, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x53, 0x70, 0x61,
	0x6d, 0x22, 0xa3, 0x01, 0x0a, 0x12, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x4b, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d,
	0x12, 0x15, 0x0a, 0x11, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x43,
	0x45, 0x53, 0x53, 0x45, 0x44, 0x10, 0x32, 0x22, 0x6b, 0x0a, 0x13, 0x55, 0x6e, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30,
	0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71,
	0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x22, 0xa5, 0x01, 0x0a, 0x14, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a,
	0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22,
	0x4b, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47,
	0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59,
	0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x10, 0x32, 0x22, 0x69, 0x0a, 0x11,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x70, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03,
	0x72, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xba, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x70, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40,
	0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x22, 0x62, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52,
	0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44,
	0x59, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x10, 0x32, 0x12, 0x15, 0x0a,
	0x11, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b,
	0x45, 0x44, 0x10, 0x64, 0x22, 0x9c, 0x01, 0x0a, 0x20, 0x41, 0x64, 0x64, 0x41, 0x70, 0x70, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x3c, 0x0a, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x52, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x73, 0x22, 0x65, 0x0a, 0x21, 0x41, 0x64, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x86, 0x01, 0x0a, 0x14, 0x53,
	0x79, 0x6e, 0x63, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x49, 0x44, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x3c, 0x0a, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x52, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x73, 0x22, 0x59, 0x0a, 0x15, 0x53, 0x79, 0x6e, 0x63, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x49, 0x44, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b,
	0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xde,
	0x01, 0x0a, 0x1e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03,
	0x72, 0x65, 0x71, 0x12, 0x30, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x05, 0x18, 0x20, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x69, 0x66, 0x73, 0x63, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x98,
	0x01, 0x0b, 0x52, 0x08, 0x69, 0x66, 0x73, 0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x31, 0x0a, 0x0f,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x69, 0x76, 0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64,
	0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x47, 0x69, 0x76, 0x65, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0xf7, 0x01, 0x0a, 0x1f, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x54, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x4d, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xc4, 0x02, 0x0a, 0x1a, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72,
	0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x0e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x09, 0x69, 0x66, 0x73, 0x63, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x69, 0x66, 0x73,
	0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x32, 0x0a, 0x13, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x6e,
	0x6b, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x4c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6e,
	0x65, 0x32, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x22,
	0x25, 0x0a, 0x0b, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16,
	0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xa1, 0x01, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x52, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50,
	0x61, 0x69, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0b, 0x70,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x22, 0x5e, 0x0a, 0x1a, 0x53, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x97, 0x01, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x48, 0x0a, 0x10, 0x70, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x22, 0xa8, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x48, 0x0a, 0x10, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0f,
	0x75, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x22,
	0x85, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x79, 0x44, 0x65, 0x72, 0x69, 0x76, 0x65, 0x64, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x65, 0x72,
	0x69, 0x76, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x65, 0x72, 0x69, 0x76, 0x65, 0x64, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xbd, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x79, 0x44,
	0x65, 0x72, 0x69, 0x76, 0x65, 0x64, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x53, 0x0a, 0x14, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x12, 0x75, 0x73, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x52, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4d, 0x6f,
	0x45, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0x7a, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x4d, 0x6f, 0x45, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x65,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0xb5, 0x02, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x4b, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46,
	0x6f, 0x72, 0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x1a, 0x57, 0x0a, 0x0b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xaa, 0x02, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a,
	0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x6c, 0x0a, 0x12, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x69, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x49, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x49, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x1a, 0x5d, 0x0a,
	0x15, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x49, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78,
	0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8a, 0x01, 0x0a,
	0x17, 0x46, 0x65, 0x74, 0x63, 0x68, 0x46, 0x6f, 0x72, 0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x03, 0x72, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x49, 0x64, 0x73, 0x22, 0x82, 0x02, 0x0a, 0x18, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x46, 0x6f, 0x72, 0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65,
	0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4b, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x46, 0x6f,
	0x72, 0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x1a, 0x57, 0x0a, 0x0b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc2,
	0x01, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72,
	0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x2c, 0x0a, 0x07, 0x6c, 0x61, 0x74, 0x5f, 0x6c,
	0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x06, 0x6c,
	0x61, 0x74, 0x4c, 0x6e, 0x67, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0xab, 0x01, 0x0a, 0x29, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x46, 0x72, 0x6f,
	0x6d, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x83, 0x01, 0x0a, 0x12, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x03, 0x72, 0x65, 0x71, 0x12, 0x31, 0x0a, 0x08, 0x73, 0x6d, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07,
	0x73, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61, 0x22, 0x71, 0x0a, 0x07, 0x53, 0x6d, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x41, 0x0a, 0x0e, 0x73, 0x6d, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x6d, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x0c, 0x73, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x73, 0x6d, 0x73,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x57, 0x0a, 0x13, 0x53, 0x65,
	0x6e, 0x64, 0x53, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2a, 0x83, 0x01, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x46,
	0x6c, 0x6f, 0x77, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x1b,
	0x0a, 0x17, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x44,
	0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x41,
	0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x03, 0x2a, 0x7a, 0x0a, 0x0c, 0x53, 0x6d, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x4d, 0x53,
	0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x4d,
	0x53, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x53, 0x5f, 0x50, 0x45,
	0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x4d, 0x53, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x53, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x02, 0x32, 0xa9, 0x31, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x62,
	0x0a, 0x0a, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x20, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x41,
	0x64, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7,
	0x0a, 0x00, 0x12, 0x6b, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x23, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f,
	0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12,
	0x62, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x20, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e,
	0xd7, 0x0a, 0x00, 0x12, 0x8c, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x44,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73,
	0x12, 0x2e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7,
	0x0a, 0x00, 0x12, 0x77, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a,
	0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x41, 0x72, 0x65, 0x61, 0x73, 0x12,
	0x30, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x41, 0x72, 0x65, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x31, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x41, 0x72, 0x65, 0x61, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00,
	0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x70, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x48, 0x6f, 0x6d, 0x65, 0x12, 0x28, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x05, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x12, 0x89, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e,
	0xd7, 0x0a, 0x00, 0x12, 0x7a, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x75,
	0x6c, 0x6c, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x46, 0x75, 0x6c, 0x6c, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x75, 0x6c, 0x6c, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f,
	0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12,
	0x7a, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x41, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x41, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a,
	0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x73, 0x0a, 0x13, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x05, 0x80, 0x9e, 0xd7, 0x0a, 0x01,
	0x12, 0x7a, 0x0a, 0x12, 0x45, 0x64, 0x69, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x45, 0x64, 0x69, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7,
	0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x71, 0x0a, 0x0f,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x25, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f,
	0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12,
	0x77, 0x0a, 0x16, 0x53, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x69, 0x76, 0x61, 0x63,
	0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x2c, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x50, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x2c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x69, 0x76, 0x61,
	0x63, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x69, 0x76, 0x61, 0x63, 0x79,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a,
	0x00, 0x12, 0x86, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x46, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x46, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x46, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01,
	0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x83, 0x01, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x2b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00,
	0x12, 0x6b, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65,
	0x65, 0x12, 0x23, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x6d,
	0x69, 0x6e, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e,
	0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x65, 0x0a,
	0x0b, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x73, 0x12, 0x21, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x22, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0,
	0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x62, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e,
	0x65, 0x65, 0x12, 0x20, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e,
	0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x9b, 0x01, 0x0a, 0x1d, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x53, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x33, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x53, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x50, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x53, 0x68, 0x69, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a,
	0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x80, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x48, 0x61, 0x73, 0x68, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73, 0x12,
	0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x48, 0x61, 0x73, 0x68, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x48, 0x61, 0x73, 0x68, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88,
	0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x70, 0x0a, 0x12, 0x53, 0x79, 0x6e,
	0x63, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x05, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x12, 0x6e, 0x0a, 0x0e, 0x47,
	0x65, 0x74, 0x41, 0x6c, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x24, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a,
	0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x74, 0x0a, 0x10, 0x47,
	0x65, 0x74, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x26, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x79, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a,
	0x00, 0x12, 0x6d, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x05, 0x80, 0x9e, 0xd7, 0x0a, 0x01,
	0x12, 0x68, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x22, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x65, 0x67, 0x61, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01,
	0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x16, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x2c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x43, 0x61, 0x72,
	0x64, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x50,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e,
	0xd7, 0x0a, 0x00, 0x12, 0x6a, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x62, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x05, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x12,
	0x6a, 0x0a, 0x10, 0x50, 0x6f, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x12, 0x26, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x73, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x05, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x12, 0x58, 0x0a, 0x0a, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x20, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x05,
	0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x12, 0x5e, 0x0a, 0x0c, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x22, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x05,
	0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x12, 0x58, 0x0a, 0x0a, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53,
	0x70, 0x61, 0x6d, 0x12, 0x20, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x70, 0x61, 0x6d, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x70, 0x61, 0x6d,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x05, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x12,
	0x8f, 0x01, 0x0a, 0x19, 0x41, 0x64, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x12, 0x2f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x41, 0x64,
	0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x41,
	0x64, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a,
	0x00, 0x12, 0x71, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x46, 0x75, 0x6c, 0x6c, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x25, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x46, 0x75, 0x6c, 0x6c, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x46, 0x75, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0,
	0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x70, 0x0a, 0x0d, 0x53, 0x79, 0x6e, 0x63, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x49, 0x44, 0x73, 0x12, 0x23, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x49, 0x44, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x56,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x49, 0x44, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x00, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a,
	0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x89, 0x01, 0x0a, 0x17, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7,
	0x0a, 0x00, 0x12, 0x7a, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80,
	0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x7a,
	0x0a, 0x12, 0x53, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x73, 0x12, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01,
	0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x97, 0x01, 0x0a, 0x1a, 0x53,
	0x65, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x50, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x30, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x43, 0x61, 0x6c,
	0x6c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x74, 0x43,
	0x61, 0x6c, 0x6c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14,
	0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0,
	0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x7a, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x28, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00,
	0x12, 0x89, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x4f, 0x42, 0x46, 0x6f,
	0x72, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x52, 0x65, 0x74, 0x72, 0x79, 0x12, 0x2d, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x4f, 0x42, 0x46, 0x6f, 0x72, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x52,
	0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x4f, 0x42, 0x46, 0x6f, 0x72, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x52, 0x65,
	0x74, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7,
	0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x98, 0x01, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x42, 0x79, 0x44, 0x65, 0x72, 0x69, 0x76, 0x65, 0x64, 0x49, 0x64, 0x12, 0x32, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42,
	0x79, 0x44, 0x65, 0x72, 0x69, 0x76, 0x65, 0x64, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x42, 0x79, 0x44, 0x65, 0x72, 0x69, 0x76, 0x65, 0x64, 0x49, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7,
	0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x6b, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4d, 0x6f,
	0x45, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x23, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x45, 0x6e,
	0x67, 0x61, 0x67, 0x65, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x4d, 0x6f, 0x45, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0,
	0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x50, 0x61, 0x67, 0x65, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x2c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x67, 0x65, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x67, 0x65, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e,
	0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x98, 0x01,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x50, 0x61, 0x67, 0x65, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e,
	0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x77, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x27, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72,
	0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a,
	0x00, 0x12, 0x74, 0x0a, 0x10, 0x46, 0x65, 0x74, 0x63, 0x68, 0x46, 0x6f, 0x72, 0x6d, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x26, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x46, 0x6f, 0x72, 0x6d, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x46, 0x6f, 0x72, 0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7,
	0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xac, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x46, 0x72, 0x6f, 0x6d,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xa8, 0x9e, 0xd7, 0x0a,
	0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xa1, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x35, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x36, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01,
	0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x65, 0x0a, 0x0b, 0x53, 0x65,
	0x6e, 0x64, 0x53, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x21, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x6d,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6e,
	0x64, 0x53, 0x6d, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a,
	0x00, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5a, 0x28, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_user_service_proto_rawDescOnce sync.Once
	file_api_frontend_user_service_proto_rawDescData = file_api_frontend_user_service_proto_rawDesc
)

func file_api_frontend_user_service_proto_rawDescGZIP() []byte {
	file_api_frontend_user_service_proto_rawDescOnce.Do(func() {
		file_api_frontend_user_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_user_service_proto_rawDescData)
	})
	return file_api_frontend_user_service_proto_rawDescData
}

var file_api_frontend_user_service_proto_enumTypes = make([]protoimpl.EnumInfo, 16)
var file_api_frontend_user_service_proto_msgTypes = make([]protoimpl.MessageInfo, 88)
var file_api_frontend_user_service_proto_goTypes = []interface{}{
	(AddressFlow)(0),                                     // 0: frontend.user.AddressFlow
	(SmsDataError)(0),                                    // 1: frontend.user.SmsDataError
	(AddAddressResponse_Status)(0),                       // 2: frontend.user.AddAddressResponse.Status
	(UpdateAddressResponse_Status)(0),                    // 3: frontend.user.UpdateAddressResponse.Status
	(GetAddressResponse_Status)(0),                       // 4: frontend.user.GetAddressResponse.Status
	(GetPinCodeDetailsResponse_Status)(0),                // 5: frontend.user.GetPinCodeDetailsResponse.Status
	(GetUserSessionDetailsResponse_Status)(0),            // 6: frontend.user.GetUserSessionDetailsResponse.Status
	(GetUserSessionDetailsResponse_CardTabs_TabType)(0),  // 7: frontend.user.GetUserSessionDetailsResponse.CardTabs.TabType
	(CreateNomineeResponse_Status)(0),                    // 8: frontend.user.CreateNomineeResponse.Status
	(RecordHashedContactsResponse_Status)(0),             // 9: frontend.user.RecordHashedContactsResponse.Status
	(SyncContactDetailsResponse_Status)(0),               // 10: frontend.user.SyncContactDetailsResponse.Status
	(GetByPhoneNumberResponse_Status)(0),                 // 11: frontend.user.GetByPhoneNumberResponse.Status
	(PostUserActivityResponse_Status)(0),                 // 12: frontend.user.PostUserActivityResponse.Status
	(BlockActorResponse_Status)(0),                       // 13: frontend.user.BlockActorResponse.Status
	(UnblockActorResponse_Status)(0),                     // 14: frontend.user.UnblockActorResponse.Status
	(ReportSpamResponse_Status)(0),                       // 15: frontend.user.ReportSpamResponse.Status
	(*GetFeatureBenefitsScreenOptionsRequest)(nil),       // 16: frontend.user.GetFeatureBenefitsScreenOptionsRequest
	(*GetFeatureBenefitsScreenOptionsResponse)(nil),      // 17: frontend.user.GetFeatureBenefitsScreenOptionsResponse
	(*GetProfileSettingPageSectionRequest)(nil),          // 18: frontend.user.GetProfileSettingPageSectionRequest
	(*SetCallLanguagePreferencesRequest)(nil),            // 19: frontend.user.SetCallLanguagePreferencesRequest
	(*SetCallLanguagePreferencesResponse)(nil),           // 20: frontend.user.SetCallLanguagePreferencesResponse
	(*GetProfileSettingPageSectionResponse)(nil),         // 21: frontend.user.GetProfileSettingPageSectionResponse
	(*UpdateDOBForDedupeRetryRequest)(nil),               // 22: frontend.user.UpdateDOBForDedupeRetryRequest
	(*UpdateDOBForDedupeRetryResponse)(nil),              // 23: frontend.user.UpdateDOBForDedupeRetryResponse
	(*GetDebitCardNameRequest)(nil),                      // 24: frontend.user.GetDebitCardNameRequest
	(*GetDebitCardNameResponse)(nil),                     // 25: frontend.user.GetDebitCardNameResponse
	(*AddAddressRequest)(nil),                            // 26: frontend.user.AddAddressRequest
	(*AddAddressResponse)(nil),                           // 27: frontend.user.AddAddressResponse
	(*UpdateAddressRequest)(nil),                         // 28: frontend.user.UpdateAddressRequest
	(*UpdateAddressResponse)(nil),                        // 29: frontend.user.UpdateAddressResponse
	(*GetAddressRequest)(nil),                            // 30: frontend.user.GetAddressRequest
	(*GetAddressResponse)(nil),                           // 31: frontend.user.GetAddressResponse
	(*GetCardDeliveryAddressesRequest)(nil),              // 32: frontend.user.GetCardDeliveryAddressesRequest
	(*GetCardDeliveryAddressesResponse)(nil),             // 33: frontend.user.GetCardDeliveryAddressesResponse
	(*AddressInfo)(nil),                                  // 34: frontend.user.AddressInfo
	(*GetPinCodeDetailsRequest)(nil),                     // 35: frontend.user.GetPinCodeDetailsRequest
	(*GetPinCodeDetailsResponse)(nil),                    // 36: frontend.user.GetPinCodeDetailsResponse
	(*UpdateMotherFatherNameRequest)(nil),                // 37: frontend.user.UpdateMotherFatherNameRequest
	(*UpdateMotherFatherNameResponse)(nil),               // 38: frontend.user.UpdateMotherFatherNameResponse
	(*GetUserSessionDetailsRequest)(nil),                 // 39: frontend.user.GetUserSessionDetailsRequest
	(*GetUserSessionDetailsResponse)(nil),                // 40: frontend.user.GetUserSessionDetailsResponse
	(*CreateNomineeRequest)(nil),                         // 41: frontend.user.CreateNomineeRequest
	(*CreateNomineeResponse)(nil),                        // 42: frontend.user.CreateNomineeResponse
	(*GetNomineesRequest)(nil),                           // 43: frontend.user.GetNomineesRequest
	(*GetNomineesResponse)(nil),                          // 44: frontend.user.GetNomineesResponse
	(*GetNomineeRequest)(nil),                            // 45: frontend.user.GetNomineeRequest
	(*GetNomineeResponse)(nil),                           // 46: frontend.user.GetNomineeResponse
	(*ConfirmCardShippingPreferenceRequest)(nil),         // 47: frontend.user.ConfirmCardShippingPreferenceRequest
	(*ConfirmCardShippingPreferenceResponse)(nil),        // 48: frontend.user.ConfirmCardShippingPreferenceResponse
	(*RecordHashedContactsRequest)(nil),                  // 49: frontend.user.RecordHashedContactsRequest
	(*RecordHashedContactsResponse)(nil),                 // 50: frontend.user.RecordHashedContactsResponse
	(*AddressWithType)(nil),                              // 51: frontend.user.AddressWithType
	(*SyncContactDetailsRequest)(nil),                    // 52: frontend.user.SyncContactDetailsRequest
	(*SyncContactDetailsResponse)(nil),                   // 53: frontend.user.SyncContactDetailsResponse
	(*GetByPhoneNumberRequest)(nil),                      // 54: frontend.user.GetByPhoneNumberRequest
	(*GetByPhoneNumberResponse)(nil),                     // 55: frontend.user.GetByPhoneNumberResponse
	(*GetLegalNameRequest)(nil),                          // 56: frontend.user.GetLegalNameRequest
	(*GetLegalNameResponse)(nil),                         // 57: frontend.user.GetLegalNameResponse
	(*ConfirmCardPreferencesRequest)(nil),                // 58: frontend.user.ConfirmCardPreferencesRequest
	(*ConfirmCardPreferencesResponse)(nil),               // 59: frontend.user.ConfirmCardPreferencesResponse
	(*PostUserActivityRequest)(nil),                      // 60: frontend.user.PostUserActivityRequest
	(*PostUserActivityResponse)(nil),                     // 61: frontend.user.PostUserActivityResponse
	(*BlockActorRequest)(nil),                            // 62: frontend.user.BlockActorRequest
	(*BlockActorResponse)(nil),                           // 63: frontend.user.BlockActorResponse
	(*UnblockActorRequest)(nil),                          // 64: frontend.user.UnblockActorRequest
	(*UnblockActorResponse)(nil),                         // 65: frontend.user.UnblockActorResponse
	(*ReportSpamRequest)(nil),                            // 66: frontend.user.ReportSpamRequest
	(*ReportSpamResponse)(nil),                           // 67: frontend.user.ReportSpamResponse
	(*AddAppInstanceIdentifiersRequest)(nil),             // 68: frontend.user.AddAppInstanceIdentifiersRequest
	(*AddAppInstanceIdentifiersResponse)(nil),            // 69: frontend.user.AddAppInstanceIdentifiersResponse
	(*SyncVendorIDsRequest)(nil),                         // 70: frontend.user.SyncVendorIDsRequest
	(*SyncVendorIDsResponse)(nil),                        // 71: frontend.user.SyncVendorIDsResponse
	(*ValidateExternalAccountRequest)(nil),               // 72: frontend.user.ValidateExternalAccountRequest
	(*ValidateExternalAccountResponse)(nil),              // 73: frontend.user.ValidateExternalAccountResponse
	(*GetExternalAccountRequest)(nil),                    // 74: frontend.user.GetExternalAccountRequest
	(*GetExternalAccountResponse)(nil),                   // 75: frontend.user.GetExternalAccountResponse
	(*FeatureInfo)(nil),                                  // 76: frontend.user.FeatureInfo
	(*SetUserPreferencesRequest)(nil),                    // 77: frontend.user.SetUserPreferencesRequest
	(*SetUserPreferencesResponse)(nil),                   // 78: frontend.user.SetUserPreferencesResponse
	(*GetUserPreferencesRequest)(nil),                    // 79: frontend.user.GetUserPreferencesRequest
	(*GetUserPreferencesResponse)(nil),                   // 80: frontend.user.GetUserPreferencesResponse
	(*GetAccountDetailsByDerivedIdRequest)(nil),          // 81: frontend.user.GetAccountDetailsByDerivedIdRequest
	(*GetAccountDetailsByDerivedIdResponse)(nil),         // 82: frontend.user.GetAccountDetailsByDerivedIdResponse
	(*GetMoEngageIdRequest)(nil),                         // 83: frontend.user.GetMoEngageIdRequest
	(*GetMoEngageIdResponse)(nil),                        // 84: frontend.user.GetMoEngageIdResponse
	(*UpdateFormDetailsRequest)(nil),                     // 85: frontend.user.UpdateFormDetailsRequest
	(*UpdateFormDetailsResponse)(nil),                    // 86: frontend.user.UpdateFormDetailsResponse
	(*FetchFormDetailsRequest)(nil),                      // 87: frontend.user.FetchFormDetailsRequest
	(*FetchFormDetailsResponse)(nil),                     // 88: frontend.user.FetchFormDetailsResponse
	(*GetAddressEntryScreenFromLocationRequest)(nil),     // 89: frontend.user.GetAddressEntryScreenFromLocationRequest
	(*GetAddressEntryScreenFromLocationResponse)(nil),    // 90: frontend.user.GetAddressEntryScreenFromLocationResponse
	(*SendSmsDataRequest)(nil),                           // 91: frontend.user.SendSmsDataRequest
	(*SmsData)(nil),                                      // 92: frontend.user.SmsData
	(*SendSmsDataResponse)(nil),                          // 93: frontend.user.SendSmsDataResponse
	(*GetUserSessionDetailsResponse_AccountDetails)(nil), // 94: frontend.user.GetUserSessionDetailsResponse.AccountDetails
	nil, // 95: frontend.user.GetUserSessionDetailsResponse.FirebaseUserPropertiesEntry
	nil, // 96: frontend.user.GetUserSessionDetailsResponse.FeatureMapEntry
	(*GetUserSessionDetailsResponse_CardTabs)(nil),     // 97: frontend.user.GetUserSessionDetailsResponse.CardTabs
	(*GetUserSessionDetailsResponse_CardTabs_Tab)(nil), // 98: frontend.user.GetUserSessionDetailsResponse.CardTabs.Tab
	(*RecordHashedContactsRequest_Contact)(nil),        // 99: frontend.user.RecordHashedContactsRequest.Contact
	(*SyncContactDetailsResponse_ContactDetails)(nil),  // 100: frontend.user.SyncContactDetailsResponse.ContactDetails
	nil,                           // 101: frontend.user.UpdateFormDetailsRequest.ValuesEntry
	nil,                           // 102: frontend.user.UpdateFormDetailsResponse.FieldInlineErrorEntry
	nil,                           // 103: frontend.user.FetchFormDetailsResponse.ValuesEntry
	(*header.RequestHeader)(nil),  // 104: frontend.header.RequestHeader
	(*header.ResponseHeader)(nil), // 105: frontend.header.ResponseHeader
	(*onboarding.FeatureBenefitsScreenOptions)(nil), // 106: api.typesv2.deeplink_screen_option.onboarding.FeatureBenefitsScreenOptions
	(analytics.AnalyticsScreenName)(0),              // 107: frontend.analytics.AnalyticsScreenName
	(*PreferenceTypeValuePair)(nil),                 // 108: frontend.user.PreferenceTypeValuePair
	(*common.Text)(nil),                             // 109: api.typesv2.common.Text
	(*ui.IconTextComponent)(nil),                    // 110: api.typesv2.ui.IconTextComponent
	(*ui.TextWithHyperlinks)(nil),                   // 111: api.typesv2.ui.TextWithHyperlinks
	(*date.Date)(nil),                               // 112: google.type.Date
	(*deeplink.Deeplink)(nil),                       // 113: frontend.deeplink.Deeplink
	(*rpc.Status)(nil),                              // 114: rpc.Status
	(*common.Name)(nil),                             // 115: api.typesv2.common.Name
	(*header.AuthHeader)(nil),                       // 116: frontend.header.AuthHeader
	(typesv2.AddressType)(0),                        // 117: api.typesv2.AddressType
	(*typesv2.PostalAddress)(nil),                   // 118: api.typesv2.PostalAddress
	(*account.Account)(nil),                         // 119: frontend.account.Account
	(*analytics.MsClarityConfig)(nil),               // 120: frontend.analytics.MsClarityConfig
	(*smsscanner.ScienapticSmsScannerConfig)(nil),   // 121: frontend.smsscanner.ScienapticSmsScannerConfig
	(*typesv2.Nominee)(nil),                         // 122: api.typesv2.Nominee
	(*Nominee)(nil),                                 // 123: frontend.user.Nominee
	(*common.PhoneNumber)(nil),                      // 124: api.typesv2.common.PhoneNumber
	(*frontend.ChatHead)(nil),                       // 125: frontend.ChatHead
	(user_activity.UserActivity)(0),                 // 126: frontend.user.user_activity.UserActivity
	(*typesv2.AppInstanceId)(nil),                   // 127: api.typesv2.AppInstanceId
	(AccountValidationFailureReason)(0),             // 128: frontend.user.AccountValidationFailureReason
	(PreferenceType)(0),                             // 129: frontend.user.PreferenceType
	(*UserPreference)(nil),                          // 130: frontend.user.UserPreference
	(*UserAccountDetails)(nil),                      // 131: frontend.user.UserAccountDetails
	(*latlng.LatLng)(nil),                           // 132: google.type.LatLng
	(upi.PinSetState)(0),                            // 133: frontend.account.upi.PinSetState
	(accounts.Type)(0),                              // 134: accounts.Type
	(*widget.BackgroundColour)(nil),                 // 135: api.typesv2.common.ui.widget.BackgroundColour
	(*form.FieldValue)(nil),                         // 136: api.typesv2.form.FieldValue
	(*onboarding1.GetPinCodeAreasRequest)(nil),      // 137: frontend.user.onboarding.GetPinCodeAreasRequest
	(*GetUserProfileHomeRequest)(nil),               // 138: frontend.user.GetUserProfileHomeRequest
	(*GetProfileHeaderSectionRequest)(nil),          // 139: frontend.user.GetProfileHeaderSectionRequest
	(*GetUserFullProfileRequest)(nil),               // 140: frontend.user.GetUserFullProfileRequest
	(*GetLegalAgreementsRequest)(nil),               // 141: frontend.user.GetLegalAgreementsRequest
	(*EmailDocumentToUserRequest)(nil),              // 142: frontend.user.EmailDocumentToUserRequest
	(*EditUserPropertiesRequest)(nil),               // 143: frontend.user.EditUserPropertiesRequest
	(*DeleteUserImageRequest)(nil),                  // 144: frontend.user.DeleteUserImageRequest
	(*SetUserPrivacySettingsRequest)(nil),           // 145: frontend.user.SetUserPrivacySettingsRequest
	(*GetUserPrivacySettingsRequest)(nil),           // 146: frontend.user.GetUserPrivacySettingsRequest
	(*GetAllAccountsRequest)(nil),                   // 147: frontend.user.GetAllAccountsRequest
	(*GetAccountDetailsRequest)(nil),                // 148: frontend.user.GetAccountDetailsRequest
	(*UserFullDetailsRequest)(nil),                  // 149: frontend.user.UserFullDetailsRequest
	(*GetProfilePageSectionsRequest)(nil),           // 150: frontend.user.GetProfilePageSectionsRequest
	(*onboarding1.GetPinCodeAreasResponse)(nil),     // 151: frontend.user.onboarding.GetPinCodeAreasResponse
	(*GetUserProfileHomeResponse)(nil),              // 152: frontend.user.GetUserProfileHomeResponse
	(*GetProfileHeaderSectionResponse)(nil),         // 153: frontend.user.GetProfileHeaderSectionResponse
	(*GetUserFullProfileResponse)(nil),              // 154: frontend.user.GetUserFullProfileResponse
	(*GetLegalAgreementsResponse)(nil),              // 155: frontend.user.GetLegalAgreementsResponse
	(*EmailDocumentToUserResponse)(nil),             // 156: frontend.user.EmailDocumentToUserResponse
	(*EditUserPropertiesResponse)(nil),              // 157: frontend.user.EditUserPropertiesResponse
	(*DeleteUserImageResponse)(nil),                 // 158: frontend.user.DeleteUserImageResponse
	(*SetUserPrivacySettingsResponse)(nil),          // 159: frontend.user.SetUserPrivacySettingsResponse
	(*GetUserPrivacySettingsResponse)(nil),          // 160: frontend.user.GetUserPrivacySettingsResponse
	(*GetAllAccountsResponse)(nil),                  // 161: frontend.user.GetAllAccountsResponse
	(*GetAccountDetailsResponse)(nil),               // 162: frontend.user.GetAccountDetailsResponse
	(*UserFullDetailsResponse)(nil),                 // 163: frontend.user.UserFullDetailsResponse
	(*GetProfilePageSectionsResponse)(nil),          // 164: frontend.user.GetProfilePageSectionsResponse
}
var file_api_frontend_user_service_proto_depIdxs = []int32{
	104, // 0: frontend.user.GetFeatureBenefitsScreenOptionsRequest.req:type_name -> frontend.header.RequestHeader
	105, // 1: frontend.user.GetFeatureBenefitsScreenOptionsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	106, // 2: frontend.user.GetFeatureBenefitsScreenOptionsResponse.feature_benefits_screen_options:type_name -> api.typesv2.deeplink_screen_option.onboarding.FeatureBenefitsScreenOptions
	104, // 3: frontend.user.GetProfileSettingPageSectionRequest.req:type_name -> frontend.header.RequestHeader
	104, // 4: frontend.user.SetCallLanguagePreferencesRequest.req:type_name -> frontend.header.RequestHeader
	107, // 5: frontend.user.SetCallLanguagePreferencesRequest.entry_point:type_name -> frontend.analytics.AnalyticsScreenName
	108, // 6: frontend.user.SetCallLanguagePreferencesRequest.preferences:type_name -> frontend.user.PreferenceTypeValuePair
	105, // 7: frontend.user.SetCallLanguagePreferencesResponse.resp_header:type_name -> frontend.header.ResponseHeader
	109, // 8: frontend.user.SetCallLanguagePreferencesResponse.title:type_name -> api.typesv2.common.Text
	109, // 9: frontend.user.SetCallLanguagePreferencesResponse.desc:type_name -> api.typesv2.common.Text
	109, // 10: frontend.user.SetCallLanguagePreferencesResponse.edit_pref_info:type_name -> api.typesv2.common.Text
	110, // 11: frontend.user.SetCallLanguagePreferencesResponse.info_icon:type_name -> api.typesv2.ui.IconTextComponent
	105, // 12: frontend.user.GetProfileSettingPageSectionResponse.resp_header:type_name -> frontend.header.ResponseHeader
	109, // 13: frontend.user.GetProfileSettingPageSectionResponse.account_closure_text:type_name -> api.typesv2.common.Text
	111, // 14: frontend.user.GetProfileSettingPageSectionResponse.account_closure_hyperlinks:type_name -> api.typesv2.ui.TextWithHyperlinks
	104, // 15: frontend.user.UpdateDOBForDedupeRetryRequest.req:type_name -> frontend.header.RequestHeader
	112, // 16: frontend.user.UpdateDOBForDedupeRetryRequest.date_of_birth:type_name -> google.type.Date
	105, // 17: frontend.user.UpdateDOBForDedupeRetryResponse.resp_header:type_name -> frontend.header.ResponseHeader
	113, // 18: frontend.user.UpdateDOBForDedupeRetryResponse.next_action:type_name -> frontend.deeplink.Deeplink
	104, // 19: frontend.user.GetDebitCardNameRequest.req:type_name -> frontend.header.RequestHeader
	114, // 20: frontend.user.GetDebitCardNameResponse.status:type_name -> rpc.Status
	115, // 21: frontend.user.GetDebitCardNameResponse.debit_card_name:type_name -> api.typesv2.common.Name
	105, // 22: frontend.user.GetDebitCardNameResponse.resp_header:type_name -> frontend.header.ResponseHeader
	116, // 23: frontend.user.AddAddressRequest.auth:type_name -> frontend.header.AuthHeader
	104, // 24: frontend.user.AddAddressRequest.req:type_name -> frontend.header.RequestHeader
	117, // 25: frontend.user.AddAddressRequest.type:type_name -> api.typesv2.AddressType
	118, // 26: frontend.user.AddAddressRequest.address:type_name -> api.typesv2.PostalAddress
	114, // 27: frontend.user.AddAddressResponse.status:type_name -> rpc.Status
	105, // 28: frontend.user.AddAddressResponse.resp_header:type_name -> frontend.header.ResponseHeader
	116, // 29: frontend.user.UpdateAddressRequest.auth:type_name -> frontend.header.AuthHeader
	104, // 30: frontend.user.UpdateAddressRequest.req:type_name -> frontend.header.RequestHeader
	117, // 31: frontend.user.UpdateAddressRequest.type:type_name -> api.typesv2.AddressType
	118, // 32: frontend.user.UpdateAddressRequest.address:type_name -> api.typesv2.PostalAddress
	114, // 33: frontend.user.UpdateAddressResponse.status:type_name -> rpc.Status
	113, // 34: frontend.user.UpdateAddressResponse.next_action:type_name -> frontend.deeplink.Deeplink
	105, // 35: frontend.user.UpdateAddressResponse.resp_header:type_name -> frontend.header.ResponseHeader
	116, // 36: frontend.user.GetAddressRequest.auth:type_name -> frontend.header.AuthHeader
	104, // 37: frontend.user.GetAddressRequest.req:type_name -> frontend.header.RequestHeader
	117, // 38: frontend.user.GetAddressRequest.type:type_name -> api.typesv2.AddressType
	114, // 39: frontend.user.GetAddressResponse.status:type_name -> rpc.Status
	34,  // 40: frontend.user.GetAddressResponse.addresses:type_name -> frontend.user.AddressInfo
	105, // 41: frontend.user.GetAddressResponse.resp_header:type_name -> frontend.header.ResponseHeader
	116, // 42: frontend.user.GetCardDeliveryAddressesRequest.auth:type_name -> frontend.header.AuthHeader
	104, // 43: frontend.user.GetCardDeliveryAddressesRequest.req:type_name -> frontend.header.RequestHeader
	0,   // 44: frontend.user.GetCardDeliveryAddressesRequest.address_flow:type_name -> frontend.user.AddressFlow
	114, // 45: frontend.user.GetCardDeliveryAddressesResponse.status:type_name -> rpc.Status
	118, // 46: frontend.user.GetCardDeliveryAddressesResponse.addresses:type_name -> api.typesv2.PostalAddress
	51,  // 47: frontend.user.GetCardDeliveryAddressesResponse.addressesWithType:type_name -> frontend.user.AddressWithType
	105, // 48: frontend.user.GetCardDeliveryAddressesResponse.resp_header:type_name -> frontend.header.ResponseHeader
	117, // 49: frontend.user.AddressInfo.type:type_name -> api.typesv2.AddressType
	118, // 50: frontend.user.AddressInfo.address:type_name -> api.typesv2.PostalAddress
	116, // 51: frontend.user.GetPinCodeDetailsRequest.auth:type_name -> frontend.header.AuthHeader
	104, // 52: frontend.user.GetPinCodeDetailsRequest.req:type_name -> frontend.header.RequestHeader
	114, // 53: frontend.user.GetPinCodeDetailsResponse.status:type_name -> rpc.Status
	105, // 54: frontend.user.GetPinCodeDetailsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	116, // 55: frontend.user.UpdateMotherFatherNameRequest.auth:type_name -> frontend.header.AuthHeader
	104, // 56: frontend.user.UpdateMotherFatherNameRequest.req:type_name -> frontend.header.RequestHeader
	114, // 57: frontend.user.UpdateMotherFatherNameResponse.status:type_name -> rpc.Status
	113, // 58: frontend.user.UpdateMotherFatherNameResponse.next_action:type_name -> frontend.deeplink.Deeplink
	105, // 59: frontend.user.UpdateMotherFatherNameResponse.resp_header:type_name -> frontend.header.ResponseHeader
	116, // 60: frontend.user.GetUserSessionDetailsRequest.auth:type_name -> frontend.header.AuthHeader
	104, // 61: frontend.user.GetUserSessionDetailsRequest.req:type_name -> frontend.header.RequestHeader
	114, // 62: frontend.user.GetUserSessionDetailsResponse.status:type_name -> rpc.Status
	94,  // 63: frontend.user.GetUserSessionDetailsResponse.account_details:type_name -> frontend.user.GetUserSessionDetailsResponse.AccountDetails
	95,  // 64: frontend.user.GetUserSessionDetailsResponse.firebase_user_properties:type_name -> frontend.user.GetUserSessionDetailsResponse.FirebaseUserPropertiesEntry
	115, // 65: frontend.user.GetUserSessionDetailsResponse.display_name:type_name -> api.typesv2.common.Name
	105, // 66: frontend.user.GetUserSessionDetailsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	119, // 67: frontend.user.GetUserSessionDetailsResponse.accounts:type_name -> frontend.account.Account
	96,  // 68: frontend.user.GetUserSessionDetailsResponse.feature_map:type_name -> frontend.user.GetUserSessionDetailsResponse.FeatureMapEntry
	120, // 69: frontend.user.GetUserSessionDetailsResponse.ms_clarity_config:type_name -> frontend.analytics.MsClarityConfig
	121, // 70: frontend.user.GetUserSessionDetailsResponse.scienaptic_sms_scanner_config:type_name -> frontend.smsscanner.ScienapticSmsScannerConfig
	97,  // 71: frontend.user.GetUserSessionDetailsResponse.card_tabs:type_name -> frontend.user.GetUserSessionDetailsResponse.CardTabs
	116, // 72: frontend.user.CreateNomineeRequest.auth:type_name -> frontend.header.AuthHeader
	104, // 73: frontend.user.CreateNomineeRequest.req:type_name -> frontend.header.RequestHeader
	122, // 74: frontend.user.CreateNomineeRequest.nominee:type_name -> api.typesv2.Nominee
	123, // 75: frontend.user.CreateNomineeRequest.nominee_v2:type_name -> frontend.user.Nominee
	114, // 76: frontend.user.CreateNomineeResponse.status:type_name -> rpc.Status
	105, // 77: frontend.user.CreateNomineeResponse.resp_header:type_name -> frontend.header.ResponseHeader
	113, // 78: frontend.user.CreateNomineeResponse.deeplink:type_name -> frontend.deeplink.Deeplink
	116, // 79: frontend.user.GetNomineesRequest.auth:type_name -> frontend.header.AuthHeader
	104, // 80: frontend.user.GetNomineesRequest.req:type_name -> frontend.header.RequestHeader
	114, // 81: frontend.user.GetNomineesResponse.status:type_name -> rpc.Status
	122, // 82: frontend.user.GetNomineesResponse.nominees:type_name -> api.typesv2.Nominee
	105, // 83: frontend.user.GetNomineesResponse.resp_header:type_name -> frontend.header.ResponseHeader
	123, // 84: frontend.user.GetNomineesResponse.nominees_v2:type_name -> frontend.user.Nominee
	116, // 85: frontend.user.GetNomineeRequest.auth:type_name -> frontend.header.AuthHeader
	104, // 86: frontend.user.GetNomineeRequest.req:type_name -> frontend.header.RequestHeader
	114, // 87: frontend.user.GetNomineeResponse.status:type_name -> rpc.Status
	122, // 88: frontend.user.GetNomineeResponse.nominee:type_name -> api.typesv2.Nominee
	105, // 89: frontend.user.GetNomineeResponse.resp_header:type_name -> frontend.header.ResponseHeader
	123, // 90: frontend.user.GetNomineeResponse.nominee_v2:type_name -> frontend.user.Nominee
	116, // 91: frontend.user.ConfirmCardShippingPreferenceRequest.auth:type_name -> frontend.header.AuthHeader
	104, // 92: frontend.user.ConfirmCardShippingPreferenceRequest.req:type_name -> frontend.header.RequestHeader
	117, // 93: frontend.user.ConfirmCardShippingPreferenceRequest.address_type:type_name -> api.typesv2.AddressType
	114, // 94: frontend.user.ConfirmCardShippingPreferenceResponse.status:type_name -> rpc.Status
	113, // 95: frontend.user.ConfirmCardShippingPreferenceResponse.next_action:type_name -> frontend.deeplink.Deeplink
	105, // 96: frontend.user.ConfirmCardShippingPreferenceResponse.resp_header:type_name -> frontend.header.ResponseHeader
	116, // 97: frontend.user.RecordHashedContactsRequest.auth:type_name -> frontend.header.AuthHeader
	104, // 98: frontend.user.RecordHashedContactsRequest.req:type_name -> frontend.header.RequestHeader
	99,  // 99: frontend.user.RecordHashedContactsRequest.contact:type_name -> frontend.user.RecordHashedContactsRequest.Contact
	114, // 100: frontend.user.RecordHashedContactsResponse.status:type_name -> rpc.Status
	105, // 101: frontend.user.RecordHashedContactsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	117, // 102: frontend.user.AddressWithType.type:type_name -> api.typesv2.AddressType
	118, // 103: frontend.user.AddressWithType.address:type_name -> api.typesv2.PostalAddress
	110, // 104: frontend.user.AddressWithType.info_text:type_name -> api.typesv2.ui.IconTextComponent
	116, // 105: frontend.user.SyncContactDetailsRequest.auth:type_name -> frontend.header.AuthHeader
	104, // 106: frontend.user.SyncContactDetailsRequest.req:type_name -> frontend.header.RequestHeader
	114, // 107: frontend.user.SyncContactDetailsResponse.status:type_name -> rpc.Status
	100, // 108: frontend.user.SyncContactDetailsResponse.contact_details:type_name -> frontend.user.SyncContactDetailsResponse.ContactDetails
	105, // 109: frontend.user.SyncContactDetailsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	116, // 110: frontend.user.GetByPhoneNumberRequest.auth:type_name -> frontend.header.AuthHeader
	104, // 111: frontend.user.GetByPhoneNumberRequest.req:type_name -> frontend.header.RequestHeader
	124, // 112: frontend.user.GetByPhoneNumberRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	114, // 113: frontend.user.GetByPhoneNumberResponse.status:type_name -> rpc.Status
	125, // 114: frontend.user.GetByPhoneNumberResponse.chat_head:type_name -> frontend.ChatHead
	125, // 115: frontend.user.GetByPhoneNumberResponse.chat_heads:type_name -> frontend.ChatHead
	105, // 116: frontend.user.GetByPhoneNumberResponse.resp_header:type_name -> frontend.header.ResponseHeader
	104, // 117: frontend.user.GetLegalNameRequest.req:type_name -> frontend.header.RequestHeader
	114, // 118: frontend.user.GetLegalNameResponse.status:type_name -> rpc.Status
	115, // 119: frontend.user.GetLegalNameResponse.legal_name:type_name -> api.typesv2.common.Name
	105, // 120: frontend.user.GetLegalNameResponse.resp_header:type_name -> frontend.header.ResponseHeader
	104, // 121: frontend.user.ConfirmCardPreferencesRequest.req:type_name -> frontend.header.RequestHeader
	115, // 122: frontend.user.ConfirmCardPreferencesRequest.debit_card_name:type_name -> api.typesv2.common.Name
	117, // 123: frontend.user.ConfirmCardPreferencesRequest.address_type:type_name -> api.typesv2.AddressType
	114, // 124: frontend.user.ConfirmCardPreferencesResponse.status:type_name -> rpc.Status
	113, // 125: frontend.user.ConfirmCardPreferencesResponse.next_action:type_name -> frontend.deeplink.Deeplink
	105, // 126: frontend.user.ConfirmCardPreferencesResponse.resp_header:type_name -> frontend.header.ResponseHeader
	104, // 127: frontend.user.PostUserActivityRequest.req:type_name -> frontend.header.RequestHeader
	126, // 128: frontend.user.PostUserActivityRequest.user_activity:type_name -> frontend.user.user_activity.UserActivity
	114, // 129: frontend.user.PostUserActivityResponse.status:type_name -> rpc.Status
	105, // 130: frontend.user.PostUserActivityResponse.resp_header:type_name -> frontend.header.ResponseHeader
	104, // 131: frontend.user.BlockActorRequest.req:type_name -> frontend.header.RequestHeader
	105, // 132: frontend.user.BlockActorResponse.resp_header:type_name -> frontend.header.ResponseHeader
	104, // 133: frontend.user.UnblockActorRequest.req:type_name -> frontend.header.RequestHeader
	105, // 134: frontend.user.UnblockActorResponse.resp_header:type_name -> frontend.header.ResponseHeader
	104, // 135: frontend.user.ReportSpamRequest.req:type_name -> frontend.header.RequestHeader
	105, // 136: frontend.user.ReportSpamResponse.resp_header:type_name -> frontend.header.ResponseHeader
	104, // 137: frontend.user.AddAppInstanceIdentifiersRequest.req:type_name -> frontend.header.RequestHeader
	127, // 138: frontend.user.AddAppInstanceIdentifiersRequest.identifiers:type_name -> api.typesv2.AppInstanceId
	105, // 139: frontend.user.AddAppInstanceIdentifiersResponse.resp_header:type_name -> frontend.header.ResponseHeader
	104, // 140: frontend.user.SyncVendorIDsRequest.req:type_name -> frontend.header.RequestHeader
	127, // 141: frontend.user.SyncVendorIDsRequest.identifiers:type_name -> api.typesv2.AppInstanceId
	105, // 142: frontend.user.SyncVendorIDsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	104, // 143: frontend.user.ValidateExternalAccountRequest.req:type_name -> frontend.header.RequestHeader
	105, // 144: frontend.user.ValidateExternalAccountResponse.resp_header:type_name -> frontend.header.ResponseHeader
	113, // 145: frontend.user.ValidateExternalAccountResponse.next_action:type_name -> frontend.deeplink.Deeplink
	128, // 146: frontend.user.ValidateExternalAccountResponse.failure_reason:type_name -> frontend.user.AccountValidationFailureReason
	104, // 147: frontend.user.GetExternalAccountRequest.req:type_name -> frontend.header.RequestHeader
	105, // 148: frontend.user.GetExternalAccountResponse.resp_header:type_name -> frontend.header.ResponseHeader
	104, // 149: frontend.user.SetUserPreferencesRequest.req:type_name -> frontend.header.RequestHeader
	108, // 150: frontend.user.SetUserPreferencesRequest.preferences:type_name -> frontend.user.PreferenceTypeValuePair
	105, // 151: frontend.user.SetUserPreferencesResponse.resp_header:type_name -> frontend.header.ResponseHeader
	104, // 152: frontend.user.GetUserPreferencesRequest.req:type_name -> frontend.header.RequestHeader
	129, // 153: frontend.user.GetUserPreferencesRequest.preference_types:type_name -> frontend.user.PreferenceType
	105, // 154: frontend.user.GetUserPreferencesResponse.resp_header:type_name -> frontend.header.ResponseHeader
	130, // 155: frontend.user.GetUserPreferencesResponse.user_preferences:type_name -> frontend.user.UserPreference
	104, // 156: frontend.user.GetAccountDetailsByDerivedIdRequest.req:type_name -> frontend.header.RequestHeader
	105, // 157: frontend.user.GetAccountDetailsByDerivedIdResponse.resp_header:type_name -> frontend.header.ResponseHeader
	131, // 158: frontend.user.GetAccountDetailsByDerivedIdResponse.user_account_details:type_name -> frontend.user.UserAccountDetails
	104, // 159: frontend.user.GetMoEngageIdRequest.req:type_name -> frontend.header.RequestHeader
	105, // 160: frontend.user.GetMoEngageIdResponse.resp_header:type_name -> frontend.header.ResponseHeader
	104, // 161: frontend.user.UpdateFormDetailsRequest.req:type_name -> frontend.header.RequestHeader
	101, // 162: frontend.user.UpdateFormDetailsRequest.values:type_name -> frontend.user.UpdateFormDetailsRequest.ValuesEntry
	105, // 163: frontend.user.UpdateFormDetailsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	102, // 164: frontend.user.UpdateFormDetailsResponse.field_inline_error:type_name -> frontend.user.UpdateFormDetailsResponse.FieldInlineErrorEntry
	104, // 165: frontend.user.FetchFormDetailsRequest.req:type_name -> frontend.header.RequestHeader
	105, // 166: frontend.user.FetchFormDetailsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	103, // 167: frontend.user.FetchFormDetailsResponse.values:type_name -> frontend.user.FetchFormDetailsResponse.ValuesEntry
	104, // 168: frontend.user.GetAddressEntryScreenFromLocationRequest.req:type_name -> frontend.header.RequestHeader
	132, // 169: frontend.user.GetAddressEntryScreenFromLocationRequest.lat_lng:type_name -> google.type.LatLng
	117, // 170: frontend.user.GetAddressEntryScreenFromLocationRequest.type:type_name -> api.typesv2.AddressType
	105, // 171: frontend.user.GetAddressEntryScreenFromLocationResponse.resp_header:type_name -> frontend.header.ResponseHeader
	113, // 172: frontend.user.GetAddressEntryScreenFromLocationResponse.next_action:type_name -> frontend.deeplink.Deeplink
	104, // 173: frontend.user.SendSmsDataRequest.req:type_name -> frontend.header.RequestHeader
	92,  // 174: frontend.user.SendSmsDataRequest.sms_data:type_name -> frontend.user.SmsData
	1,   // 175: frontend.user.SmsData.sms_data_error:type_name -> frontend.user.SmsDataError
	105, // 176: frontend.user.SendSmsDataResponse.resp_header:type_name -> frontend.header.ResponseHeader
	133, // 177: frontend.user.GetUserSessionDetailsResponse.AccountDetails.pin_set_state:type_name -> frontend.account.upi.PinSetState
	134, // 178: frontend.user.GetUserSessionDetailsResponse.AccountDetails.account_type:type_name -> accounts.Type
	76,  // 179: frontend.user.GetUserSessionDetailsResponse.FeatureMapEntry.value:type_name -> frontend.user.FeatureInfo
	135, // 180: frontend.user.GetUserSessionDetailsResponse.CardTabs.bg_tabs:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	98,  // 181: frontend.user.GetUserSessionDetailsResponse.CardTabs.cards_tab:type_name -> frontend.user.GetUserSessionDetailsResponse.CardTabs.Tab
	7,   // 182: frontend.user.GetUserSessionDetailsResponse.CardTabs.Tab.tab_type:type_name -> frontend.user.GetUserSessionDetailsResponse.CardTabs.TabType
	110, // 183: frontend.user.GetUserSessionDetailsResponse.CardTabs.Tab.unselected_tab:type_name -> api.typesv2.ui.IconTextComponent
	110, // 184: frontend.user.GetUserSessionDetailsResponse.CardTabs.Tab.selected_tab:type_name -> api.typesv2.ui.IconTextComponent
	136, // 185: frontend.user.UpdateFormDetailsRequest.ValuesEntry.value:type_name -> api.typesv2.form.FieldValue
	109, // 186: frontend.user.UpdateFormDetailsResponse.FieldInlineErrorEntry.value:type_name -> api.typesv2.common.Text
	136, // 187: frontend.user.FetchFormDetailsResponse.ValuesEntry.value:type_name -> api.typesv2.form.FieldValue
	26,  // 188: frontend.user.User.AddAddress:input_type -> frontend.user.AddAddressRequest
	28,  // 189: frontend.user.User.UpdateAddress:input_type -> frontend.user.UpdateAddressRequest
	30,  // 190: frontend.user.User.GetAddress:input_type -> frontend.user.GetAddressRequest
	32,  // 191: frontend.user.User.GetCardDeliveryAddresses:input_type -> frontend.user.GetCardDeliveryAddressesRequest
	35,  // 192: frontend.user.User.GetPinCodeDetails:input_type -> frontend.user.GetPinCodeDetailsRequest
	137, // 193: frontend.user.User.GetPinCodeAreas:input_type -> frontend.user.onboarding.GetPinCodeAreasRequest
	138, // 194: frontend.user.User.GetUserProfileHome:input_type -> frontend.user.GetUserProfileHomeRequest
	139, // 195: frontend.user.User.GetProfileHeaderSection:input_type -> frontend.user.GetProfileHeaderSectionRequest
	140, // 196: frontend.user.User.GetUserFullProfile:input_type -> frontend.user.GetUserFullProfileRequest
	141, // 197: frontend.user.User.GetLegalAgreements:input_type -> frontend.user.GetLegalAgreementsRequest
	142, // 198: frontend.user.User.EmailDocumentToUser:input_type -> frontend.user.EmailDocumentToUserRequest
	143, // 199: frontend.user.User.EditUserProperties:input_type -> frontend.user.EditUserPropertiesRequest
	144, // 200: frontend.user.User.DeleteUserImage:input_type -> frontend.user.DeleteUserImageRequest
	145, // 201: frontend.user.User.SetUserPrivacySettings:input_type -> frontend.user.SetUserPrivacySettingsRequest
	146, // 202: frontend.user.User.GetUserPrivacySettings:input_type -> frontend.user.GetUserPrivacySettingsRequest
	37,  // 203: frontend.user.User.UpdateMotherFatherName:input_type -> frontend.user.UpdateMotherFatherNameRequest
	39,  // 204: frontend.user.User.GetUserSessionDetails:input_type -> frontend.user.GetUserSessionDetailsRequest
	41,  // 205: frontend.user.User.CreateNominee:input_type -> frontend.user.CreateNomineeRequest
	43,  // 206: frontend.user.User.GetNominees:input_type -> frontend.user.GetNomineesRequest
	45,  // 207: frontend.user.User.GetNominee:input_type -> frontend.user.GetNomineeRequest
	47,  // 208: frontend.user.User.ConfirmCardShippingPreference:input_type -> frontend.user.ConfirmCardShippingPreferenceRequest
	49,  // 209: frontend.user.User.RecordHashedContacts:input_type -> frontend.user.RecordHashedContactsRequest
	52,  // 210: frontend.user.User.SyncContactDetails:input_type -> frontend.user.SyncContactDetailsRequest
	147, // 211: frontend.user.User.GetAllAccounts:input_type -> frontend.user.GetAllAccountsRequest
	54,  // 212: frontend.user.User.GetByPhoneNumber:input_type -> frontend.user.GetByPhoneNumberRequest
	148, // 213: frontend.user.User.GetAccountDetails:input_type -> frontend.user.GetAccountDetailsRequest
	56,  // 214: frontend.user.User.GetLegalName:input_type -> frontend.user.GetLegalNameRequest
	58,  // 215: frontend.user.User.ConfirmCardPreferences:input_type -> frontend.user.ConfirmCardPreferencesRequest
	24,  // 216: frontend.user.User.GetDebitCardName:input_type -> frontend.user.GetDebitCardNameRequest
	60,  // 217: frontend.user.User.PostUserActivity:input_type -> frontend.user.PostUserActivityRequest
	62,  // 218: frontend.user.User.BlockActor:input_type -> frontend.user.BlockActorRequest
	64,  // 219: frontend.user.User.UnblockActor:input_type -> frontend.user.UnblockActorRequest
	66,  // 220: frontend.user.User.ReportSpam:input_type -> frontend.user.ReportSpamRequest
	68,  // 221: frontend.user.User.AddAppInstanceIdentifiers:input_type -> frontend.user.AddAppInstanceIdentifiersRequest
	149, // 222: frontend.user.User.UserFullDetails:input_type -> frontend.user.UserFullDetailsRequest
	70,  // 223: frontend.user.User.SyncVendorIDs:input_type -> frontend.user.SyncVendorIDsRequest
	72,  // 224: frontend.user.User.ValidateExternalAccount:input_type -> frontend.user.ValidateExternalAccountRequest
	74,  // 225: frontend.user.User.GetExternalAccount:input_type -> frontend.user.GetExternalAccountRequest
	77,  // 226: frontend.user.User.SetUserPreferences:input_type -> frontend.user.SetUserPreferencesRequest
	19,  // 227: frontend.user.User.SetCallLanguagePreferences:input_type -> frontend.user.SetCallLanguagePreferencesRequest
	79,  // 228: frontend.user.User.GetUserPreferences:input_type -> frontend.user.GetUserPreferencesRequest
	22,  // 229: frontend.user.User.UpdateDOBForDedupeRetry:input_type -> frontend.user.UpdateDOBForDedupeRetryRequest
	81,  // 230: frontend.user.User.GetAccountDetailsByDerivedId:input_type -> frontend.user.GetAccountDetailsByDerivedIdRequest
	83,  // 231: frontend.user.User.GetMoEngageId:input_type -> frontend.user.GetMoEngageIdRequest
	150, // 232: frontend.user.User.GetProfilePageSections:input_type -> frontend.user.GetProfilePageSectionsRequest
	18,  // 233: frontend.user.User.GetProfileSettingPageSection:input_type -> frontend.user.GetProfileSettingPageSectionRequest
	85,  // 234: frontend.user.User.UpdateFormDetails:input_type -> frontend.user.UpdateFormDetailsRequest
	87,  // 235: frontend.user.User.FetchFormDetails:input_type -> frontend.user.FetchFormDetailsRequest
	89,  // 236: frontend.user.User.GetAddressEntryScreenFromLocation:input_type -> frontend.user.GetAddressEntryScreenFromLocationRequest
	16,  // 237: frontend.user.User.GetFeatureBenefitsScreenOptions:input_type -> frontend.user.GetFeatureBenefitsScreenOptionsRequest
	91,  // 238: frontend.user.User.SendSmsData:input_type -> frontend.user.SendSmsDataRequest
	27,  // 239: frontend.user.User.AddAddress:output_type -> frontend.user.AddAddressResponse
	29,  // 240: frontend.user.User.UpdateAddress:output_type -> frontend.user.UpdateAddressResponse
	31,  // 241: frontend.user.User.GetAddress:output_type -> frontend.user.GetAddressResponse
	33,  // 242: frontend.user.User.GetCardDeliveryAddresses:output_type -> frontend.user.GetCardDeliveryAddressesResponse
	36,  // 243: frontend.user.User.GetPinCodeDetails:output_type -> frontend.user.GetPinCodeDetailsResponse
	151, // 244: frontend.user.User.GetPinCodeAreas:output_type -> frontend.user.onboarding.GetPinCodeAreasResponse
	152, // 245: frontend.user.User.GetUserProfileHome:output_type -> frontend.user.GetUserProfileHomeResponse
	153, // 246: frontend.user.User.GetProfileHeaderSection:output_type -> frontend.user.GetProfileHeaderSectionResponse
	154, // 247: frontend.user.User.GetUserFullProfile:output_type -> frontend.user.GetUserFullProfileResponse
	155, // 248: frontend.user.User.GetLegalAgreements:output_type -> frontend.user.GetLegalAgreementsResponse
	156, // 249: frontend.user.User.EmailDocumentToUser:output_type -> frontend.user.EmailDocumentToUserResponse
	157, // 250: frontend.user.User.EditUserProperties:output_type -> frontend.user.EditUserPropertiesResponse
	158, // 251: frontend.user.User.DeleteUserImage:output_type -> frontend.user.DeleteUserImageResponse
	159, // 252: frontend.user.User.SetUserPrivacySettings:output_type -> frontend.user.SetUserPrivacySettingsResponse
	160, // 253: frontend.user.User.GetUserPrivacySettings:output_type -> frontend.user.GetUserPrivacySettingsResponse
	38,  // 254: frontend.user.User.UpdateMotherFatherName:output_type -> frontend.user.UpdateMotherFatherNameResponse
	40,  // 255: frontend.user.User.GetUserSessionDetails:output_type -> frontend.user.GetUserSessionDetailsResponse
	42,  // 256: frontend.user.User.CreateNominee:output_type -> frontend.user.CreateNomineeResponse
	44,  // 257: frontend.user.User.GetNominees:output_type -> frontend.user.GetNomineesResponse
	46,  // 258: frontend.user.User.GetNominee:output_type -> frontend.user.GetNomineeResponse
	48,  // 259: frontend.user.User.ConfirmCardShippingPreference:output_type -> frontend.user.ConfirmCardShippingPreferenceResponse
	50,  // 260: frontend.user.User.RecordHashedContacts:output_type -> frontend.user.RecordHashedContactsResponse
	53,  // 261: frontend.user.User.SyncContactDetails:output_type -> frontend.user.SyncContactDetailsResponse
	161, // 262: frontend.user.User.GetAllAccounts:output_type -> frontend.user.GetAllAccountsResponse
	55,  // 263: frontend.user.User.GetByPhoneNumber:output_type -> frontend.user.GetByPhoneNumberResponse
	162, // 264: frontend.user.User.GetAccountDetails:output_type -> frontend.user.GetAccountDetailsResponse
	57,  // 265: frontend.user.User.GetLegalName:output_type -> frontend.user.GetLegalNameResponse
	59,  // 266: frontend.user.User.ConfirmCardPreferences:output_type -> frontend.user.ConfirmCardPreferencesResponse
	25,  // 267: frontend.user.User.GetDebitCardName:output_type -> frontend.user.GetDebitCardNameResponse
	61,  // 268: frontend.user.User.PostUserActivity:output_type -> frontend.user.PostUserActivityResponse
	63,  // 269: frontend.user.User.BlockActor:output_type -> frontend.user.BlockActorResponse
	65,  // 270: frontend.user.User.UnblockActor:output_type -> frontend.user.UnblockActorResponse
	67,  // 271: frontend.user.User.ReportSpam:output_type -> frontend.user.ReportSpamResponse
	69,  // 272: frontend.user.User.AddAppInstanceIdentifiers:output_type -> frontend.user.AddAppInstanceIdentifiersResponse
	163, // 273: frontend.user.User.UserFullDetails:output_type -> frontend.user.UserFullDetailsResponse
	71,  // 274: frontend.user.User.SyncVendorIDs:output_type -> frontend.user.SyncVendorIDsResponse
	73,  // 275: frontend.user.User.ValidateExternalAccount:output_type -> frontend.user.ValidateExternalAccountResponse
	75,  // 276: frontend.user.User.GetExternalAccount:output_type -> frontend.user.GetExternalAccountResponse
	78,  // 277: frontend.user.User.SetUserPreferences:output_type -> frontend.user.SetUserPreferencesResponse
	20,  // 278: frontend.user.User.SetCallLanguagePreferences:output_type -> frontend.user.SetCallLanguagePreferencesResponse
	80,  // 279: frontend.user.User.GetUserPreferences:output_type -> frontend.user.GetUserPreferencesResponse
	23,  // 280: frontend.user.User.UpdateDOBForDedupeRetry:output_type -> frontend.user.UpdateDOBForDedupeRetryResponse
	82,  // 281: frontend.user.User.GetAccountDetailsByDerivedId:output_type -> frontend.user.GetAccountDetailsByDerivedIdResponse
	84,  // 282: frontend.user.User.GetMoEngageId:output_type -> frontend.user.GetMoEngageIdResponse
	164, // 283: frontend.user.User.GetProfilePageSections:output_type -> frontend.user.GetProfilePageSectionsResponse
	21,  // 284: frontend.user.User.GetProfileSettingPageSection:output_type -> frontend.user.GetProfileSettingPageSectionResponse
	86,  // 285: frontend.user.User.UpdateFormDetails:output_type -> frontend.user.UpdateFormDetailsResponse
	88,  // 286: frontend.user.User.FetchFormDetails:output_type -> frontend.user.FetchFormDetailsResponse
	90,  // 287: frontend.user.User.GetAddressEntryScreenFromLocation:output_type -> frontend.user.GetAddressEntryScreenFromLocationResponse
	17,  // 288: frontend.user.User.GetFeatureBenefitsScreenOptions:output_type -> frontend.user.GetFeatureBenefitsScreenOptionsResponse
	93,  // 289: frontend.user.User.SendSmsData:output_type -> frontend.user.SendSmsDataResponse
	239, // [239:290] is the sub-list for method output_type
	188, // [188:239] is the sub-list for method input_type
	188, // [188:188] is the sub-list for extension type_name
	188, // [188:188] is the sub-list for extension extendee
	0,   // [0:188] is the sub-list for field type_name
}

func init() { file_api_frontend_user_service_proto_init() }
func file_api_frontend_user_service_proto_init() {
	if File_api_frontend_user_service_proto != nil {
		return
	}
	file_api_frontend_user_enums_proto_init()
	file_api_frontend_user_nominee_proto_init()
	file_api_frontend_user_profile_proto_init()
	file_api_frontend_user_user_preference_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_user_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFeatureBenefitsScreenOptionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFeatureBenefitsScreenOptionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileSettingPageSectionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetCallLanguagePreferencesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetCallLanguagePreferencesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileSettingPageSectionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDOBForDedupeRetryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDOBForDedupeRetryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDebitCardNameRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDebitCardNameResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAddressRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAddressResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAddressRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAddressResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCardDeliveryAddressesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCardDeliveryAddressesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPinCodeDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPinCodeDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMotherFatherNameRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMotherFatherNameResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserSessionDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserSessionDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateNomineeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateNomineeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNomineesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNomineesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNomineeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNomineeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmCardShippingPreferenceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmCardShippingPreferenceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordHashedContactsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordHashedContactsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressWithType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncContactDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncContactDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetByPhoneNumberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetByPhoneNumberResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLegalNameRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLegalNameResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmCardPreferencesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfirmCardPreferencesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostUserActivityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostUserActivityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnblockActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnblockActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportSpamRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportSpamResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAppInstanceIdentifiersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAppInstanceIdentifiersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncVendorIDsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncVendorIDsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateExternalAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateExternalAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExternalAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExternalAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetUserPreferencesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetUserPreferencesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserPreferencesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserPreferencesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountDetailsByDerivedIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountDetailsByDerivedIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMoEngageIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMoEngageIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateFormDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateFormDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchFormDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchFormDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressEntryScreenFromLocationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressEntryScreenFromLocationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSmsDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmsData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSmsDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserSessionDetailsResponse_AccountDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserSessionDetailsResponse_CardTabs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserSessionDetailsResponse_CardTabs_Tab); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordHashedContactsRequest_Contact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_user_service_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncContactDetailsResponse_ContactDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_user_service_proto_msgTypes[76].OneofWrappers = []interface{}{
		(*SmsData_Pan)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_user_service_proto_rawDesc,
			NumEnums:      16,
			NumMessages:   88,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_frontend_user_service_proto_goTypes,
		DependencyIndexes: file_api_frontend_user_service_proto_depIdxs,
		EnumInfos:         file_api_frontend_user_service_proto_enumTypes,
		MessageInfos:      file_api_frontend_user_service_proto_msgTypes,
	}.Build()
	File_api_frontend_user_service_proto = out.File
	file_api_frontend_user_service_proto_rawDesc = nil
	file_api_frontend_user_service_proto_goTypes = nil
	file_api_frontend_user_service_proto_depIdxs = nil
}
