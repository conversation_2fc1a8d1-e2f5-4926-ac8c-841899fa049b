// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/inapphelp_feedback_engine.proto

package typesv2

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// enum to identify type of flow
type FeedbackFlowIdentifierType int32

const (
	FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_UNSPECIFIED FeedbackFlowIdentifierType = 0
	FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_APP_FLOW    FeedbackFlowIdentifierType = 1
	FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF    FeedbackFlowIdentifierType = 2
	FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_CTA         FeedbackFlowIdentifierType = 3
)

// Enum value maps for FeedbackFlowIdentifierType.
var (
	FeedbackFlowIdentifierType_name = map[int32]string{
		0: "FEEDBACK_FLOW_IDENTIFIER_TYPE_UNSPECIFIED",
		1: "FEEDBACK_FLOW_IDENTIFIER_TYPE_APP_FLOW",
		2: "FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF",
		3: "FEEDBACK_FLOW_IDENTIFIER_TYPE_CTA",
	}
	FeedbackFlowIdentifierType_value = map[string]int32{
		"FEEDBACK_FLOW_IDENTIFIER_TYPE_UNSPECIFIED": 0,
		"FEEDBACK_FLOW_IDENTIFIER_TYPE_APP_FLOW":    1,
		"FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF":    2,
		"FEEDBACK_FLOW_IDENTIFIER_TYPE_CTA":         3,
	}
)

func (x FeedbackFlowIdentifierType) Enum() *FeedbackFlowIdentifierType {
	p := new(FeedbackFlowIdentifierType)
	*p = x
	return p
}

func (x FeedbackFlowIdentifierType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeedbackFlowIdentifierType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_inapphelp_feedback_engine_proto_enumTypes[0].Descriptor()
}

func (FeedbackFlowIdentifierType) Type() protoreflect.EnumType {
	return &file_api_typesv2_inapphelp_feedback_engine_proto_enumTypes[0]
}

func (x FeedbackFlowIdentifierType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeedbackFlowIdentifierType.Descriptor instead.
func (FeedbackFlowIdentifierType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_inapphelp_feedback_engine_proto_rawDescGZIP(), []int{0}
}

// enum to identify the feedback flow for feedback platform
type FeedbackAppFlowIdentifier int32

const (
	FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_UNSPECIFIED FeedbackAppFlowIdentifier = 0
	// to ask feedback flow after funds are withdrawn from jump
	// to be used in response of GetP2POrderStatus frontend server RPC
	FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_JUMP_POST_WITHDRAWAL FeedbackAppFlowIdentifier = 1
	// to trigger in app csat survey
	// to be used in response of GetSupportTicketByIdForApp frontend server RPC
	FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_IN_APP_CSAT FeedbackAppFlowIdentifier = 2
	// to trigger language preference survey
	// to be used in  GetProfileSettingPageSection
	FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_LANGUAGE_PREFERENCE_SURVEY FeedbackAppFlowIdentifier = 3
	// to trigger in app feedback flow when user dismisses a nudge
	FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_NUDGE_DISMISSAL FeedbackAppFlowIdentifier = 4
	// trigger in app during usstocks withdraw fund screen
	FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_USS_WITHDRAW_FUND FeedbackAppFlowIdentifier = 24
	// referrals feedback app flow identifier for happy flow
	FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_HAPPY_FLOW_REFERRALS FeedbackAppFlowIdentifier = 25
	// Re vkyc nudge feedback flow identifier
	FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_RE_VKYC_NUDGE FeedbackAppFlowIdentifier = 26
	// to trigger in app csat survey for auto ids
	FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_IN_APP_CSAT_AUTO_IDS FeedbackAppFlowIdentifier = 27
)

// Enum value maps for FeedbackAppFlowIdentifier.
var (
	FeedbackAppFlowIdentifier_name = map[int32]string{
		0:  "FEEDBACK_APP_FLOW_IDENTIFIER_UNSPECIFIED",
		1:  "FEEDBACK_APP_FLOW_IDENTIFIER_JUMP_POST_WITHDRAWAL",
		2:  "FEEDBACK_APP_FLOW_IDENTIFIER_IN_APP_CSAT",
		3:  "FEEDBACK_APP_FLOW_IDENTIFIER_LANGUAGE_PREFERENCE_SURVEY",
		4:  "FEEDBACK_APP_FLOW_IDENTIFIER_NUDGE_DISMISSAL",
		24: "FEEDBACK_APP_FLOW_IDENTIFIER_USS_WITHDRAW_FUND",
		25: "FEEDBACK_APP_FLOW_IDENTIFIER_HAPPY_FLOW_REFERRALS",
		26: "FEEDBACK_APP_FLOW_IDENTIFIER_RE_VKYC_NUDGE",
		27: "FEEDBACK_APP_FLOW_IDENTIFIER_IN_APP_CSAT_AUTO_IDS",
	}
	FeedbackAppFlowIdentifier_value = map[string]int32{
		"FEEDBACK_APP_FLOW_IDENTIFIER_UNSPECIFIED":                0,
		"FEEDBACK_APP_FLOW_IDENTIFIER_JUMP_POST_WITHDRAWAL":       1,
		"FEEDBACK_APP_FLOW_IDENTIFIER_IN_APP_CSAT":                2,
		"FEEDBACK_APP_FLOW_IDENTIFIER_LANGUAGE_PREFERENCE_SURVEY": 3,
		"FEEDBACK_APP_FLOW_IDENTIFIER_NUDGE_DISMISSAL":            4,
		"FEEDBACK_APP_FLOW_IDENTIFIER_USS_WITHDRAW_FUND":          24,
		"FEEDBACK_APP_FLOW_IDENTIFIER_HAPPY_FLOW_REFERRALS":       25,
		"FEEDBACK_APP_FLOW_IDENTIFIER_RE_VKYC_NUDGE":              26,
		"FEEDBACK_APP_FLOW_IDENTIFIER_IN_APP_CSAT_AUTO_IDS":       27,
	}
)

func (x FeedbackAppFlowIdentifier) Enum() *FeedbackAppFlowIdentifier {
	p := new(FeedbackAppFlowIdentifier)
	*p = x
	return p
}

func (x FeedbackAppFlowIdentifier) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeedbackAppFlowIdentifier) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_inapphelp_feedback_engine_proto_enumTypes[1].Descriptor()
}

func (FeedbackAppFlowIdentifier) Type() protoreflect.EnumType {
	return &file_api_typesv2_inapphelp_feedback_engine_proto_enumTypes[1]
}

func (x FeedbackAppFlowIdentifier) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeedbackAppFlowIdentifier.Descriptor instead.
func (FeedbackAppFlowIdentifier) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_inapphelp_feedback_engine_proto_rawDescGZIP(), []int{1}
}

type FeedbackDropOffFlowIdentifier int32

const (
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_UNSPECIFIED FeedbackDropOffFlowIdentifier = 0
	// to ask feedback flow if user drops off during us stocks onboarding flow
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_ONBOARDING FeedbackDropOffFlowIdentifier = 1
	// To trigger feedback flow after user exits Networth dashboard,
	// to be used in response of GetNetWorthDashboard FE RPC
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_NETWORTH_DASHBOARD FeedbackDropOffFlowIdentifier = 2
	// to trigger the feedback flow when a user drops off from loan origination (onboarding of a loan account) journey.
	// will be used as a flow identifier from rpcs which are needed in the Loan Origination Flow.
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOAN_ORIGINATION FeedbackDropOffFlowIdentifier = 3
	// Feedback flow that will be triggered when a user drops off from tier all plans page which is GetTierAllPlans FE RPC,
	// when user have been downgraded or when user is in grace (and belongs to Plus and infinite tier)
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_PLUS_USER_IN_GRACE_PERIOD     FeedbackDropOffFlowIdentifier = 4
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_INFINITE_USER_IN_GRACE_PERIOD FeedbackDropOffFlowIdentifier = 5
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_PLUS_USER_DOWNGRADED          FeedbackDropOffFlowIdentifier = 6
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_INFINITE_USER_DOWNGRADED      FeedbackDropOffFlowIdentifier = 7
	// to trigger chat survey once the back button is hit
	// to be used in GetChatInitInformationForActor
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CHATBOT_DROPOFF_SURVEY FeedbackDropOffFlowIdentifier = 8
	// to ask feedback flow if user drops off during us stocks landing page
	//
	// Deprecated: Marked as deprecated in api/typesv2/inapphelp_feedback_engine.proto.
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_LANDING_PAGE FeedbackDropOffFlowIdentifier = 9
	// to ask feedback flow if user drops off during us wallet page has added fund atleast once in  wallet
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_WALLET_PAGE_FOR_EXISTING_WALLET_USER FeedbackDropOffFlowIdentifier = 10
	// to ask feedback flow if user drops off during us wallet page never added fund in  wallet
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_WALLET_PAGE_FOR_NEW_WALLET_USER FeedbackDropOffFlowIdentifier = 11
	// to ask feedback flow if user has added fund atleast once in  wallet and drops off during us landing page
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_LANDING_PAGE_FOR_EXISTING_WALLET_USER FeedbackDropOffFlowIdentifier = 12
	// to ask feedback flow if user never added fund in wallet and drops off during us landing page
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_LANDING_PAGE_FOR_NEW_WALLET_USER FeedbackDropOffFlowIdentifier = 13
	// to ask feedback flow if not onboarded usstocks user and drops off during us landing page
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_LANDING_PAGE_FOR_NEW_USER FeedbackDropOffFlowIdentifier = 14
	// Example Use Case: These identifiers (15, 16, 17) represent the feedback flows for drop-offs during the
	// introduction screens of different Credit Card programs (Amplifi, Magnifi, Simplifi).
	// Users dropping off during these screens will trigger the respective feedback flow for analysis.
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_AMPLIFI  FeedbackDropOffFlowIdentifier = 15
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_MAGNIFI  FeedbackDropOffFlowIdentifier = 16
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_SIMPLIFI FeedbackDropOffFlowIdentifier = 17
	// Example Use Case: These identifiers (18, 19, 20) represent the feedback flows for drop-offs during the
	// address selection screens of different Credit Card programs (Amplifi, Magnifi, Simplifi).
	// Users dropping off during these screens will trigger the respective feedback flow for analysis.
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_ADDRESS_SELECTION_SCREEN_AMPLIFI FeedbackDropOffFlowIdentifier = 18
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_ADDRESS_SELECTION_SCREEN_MAGNIFI FeedbackDropOffFlowIdentifier = 19
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FD_CREATION_SCREEN_SIMPLIFI      FeedbackDropOffFlowIdentifier = 20
	// Example Use Case: These identifiers (21, 22, 23) represent the feedback flows for drop-offs during the
	// final consent screens of different Credit Card programs (Amplifi, Magnifi, Simplifi).
	// Users dropping off during these screens will trigger the respective feedback flow for analysis.
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FINAL_CONSENT_SCREEN_AMPLIFI  FeedbackDropOffFlowIdentifier = 21
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FINAL_CONSENT_SCREEN_MAGNIFI  FeedbackDropOffFlowIdentifier = 22
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FINAL_CONSENT_SCREEN_SIMPLIFI FeedbackDropOffFlowIdentifier = 23
	// user drop off during withdraw fund screen
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_WITHDRAW_FUND FeedbackDropOffFlowIdentifier = 24
	// user drop off while exploring home explore screen
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_HOME_EXPLORE FeedbackDropOffFlowIdentifier = 25
	// user drop of from salary lite intro screen
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ENACH_SALARYLITE_INTRO_SCREEN FeedbackDropOffFlowIdentifier = 26
	// user drop of from salary lite mandate setup screen
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_SALARYLITE_MANDATE_SETUP FeedbackDropOffFlowIdentifier = 27
	// user drop of from DC order screen
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_DC_ORDER_SCREEN                FeedbackDropOffFlowIdentifier = 28
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_EKYC_SCREEN        FeedbackDropOffFlowIdentifier = 29
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_PAN_SCREEN         FeedbackDropOffFlowIdentifier = 30
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_LIVENESS_SCREEN    FeedbackDropOffFlowIdentifier = 31
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_PASSPORT_SCREEN    FeedbackDropOffFlowIdentifier = 32
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_EMIRATES_ID_SCREEN FeedbackDropOffFlowIdentifier = 33
	// referral happy flow identifier on user drop off
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_HAPPY_FLOW_REFERRALS                      FeedbackDropOffFlowIdentifier = 34
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDING_VKYC_SCREEN                    FeedbackDropOffFlowIdentifier = 35
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_PAN_DETAILS                         FeedbackDropOffFlowIdentifier = 36
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_OTHER_DETAILS                       FeedbackDropOffFlowIdentifier = 37
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_ADDRESS_DETAILS                     FeedbackDropOffFlowIdentifier = 38
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_LOAN_REQUIREMENT_DETAILS            FeedbackDropOffFlowIdentifier = 39
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_EMPLOYMENT_DETAILS                  FeedbackDropOffFlowIdentifier = 40
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CREDIT_REPORT_CONSENT                     FeedbackDropOffFlowIdentifier = 41
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_LANDING_INFO_V2_LENDER_OFFER_SCREEN FeedbackDropOffFlowIdentifier = 42
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_OFFER_INTRO_SCREEN                  FeedbackDropOffFlowIdentifier = 43
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_PWA_REDIRECTION_SCREEN                    FeedbackDropOffFlowIdentifier = 44
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN     FeedbackDropOffFlowIdentifier = 45
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_CONSENT_V2_SCREEN                   FeedbackDropOffFlowIdentifier = 46
	// user drop off from cc intro screen feedback survey
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_V2 FeedbackDropOffFlowIdentifier = 47
	// user drop off from LLM Query search terminal screen
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LLM_IN_APP_CSAT FeedbackDropOffFlowIdentifier = 48
	// user drop off from pay search v2 screen
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_PAY_SEARCH_V2 FeedbackDropOffFlowIdentifier = 49
	// user drop off from all plans v2 screen
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_SCREEN_V2 FeedbackDropOffFlowIdentifier = 50
	// user drop off from plans home page
	FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIERING_EARNED_BENEFIT_SCREEN FeedbackDropOffFlowIdentifier = 51
)

// Enum value maps for FeedbackDropOffFlowIdentifier.
var (
	FeedbackDropOffFlowIdentifier_name = map[int32]string{
		0:  "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_UNSPECIFIED",
		1:  "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_ONBOARDING",
		2:  "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_NETWORTH_DASHBOARD",
		3:  "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOAN_ORIGINATION",
		4:  "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_PLUS_USER_IN_GRACE_PERIOD",
		5:  "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_INFINITE_USER_IN_GRACE_PERIOD",
		6:  "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_PLUS_USER_DOWNGRADED",
		7:  "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_INFINITE_USER_DOWNGRADED",
		8:  "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CHATBOT_DROPOFF_SURVEY",
		9:  "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_LANDING_PAGE",
		10: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_WALLET_PAGE_FOR_EXISTING_WALLET_USER",
		11: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_WALLET_PAGE_FOR_NEW_WALLET_USER",
		12: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_LANDING_PAGE_FOR_EXISTING_WALLET_USER",
		13: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_LANDING_PAGE_FOR_NEW_WALLET_USER",
		14: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_LANDING_PAGE_FOR_NEW_USER",
		15: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_AMPLIFI",
		16: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_MAGNIFI",
		17: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_SIMPLIFI",
		18: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_ADDRESS_SELECTION_SCREEN_AMPLIFI",
		19: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_ADDRESS_SELECTION_SCREEN_MAGNIFI",
		20: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FD_CREATION_SCREEN_SIMPLIFI",
		21: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FINAL_CONSENT_SCREEN_AMPLIFI",
		22: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FINAL_CONSENT_SCREEN_MAGNIFI",
		23: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FINAL_CONSENT_SCREEN_SIMPLIFI",
		24: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_WITHDRAW_FUND",
		25: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_HOME_EXPLORE",
		26: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ENACH_SALARYLITE_INTRO_SCREEN",
		27: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_SALARYLITE_MANDATE_SETUP",
		28: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_DC_ORDER_SCREEN",
		29: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_EKYC_SCREEN",
		30: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_PAN_SCREEN",
		31: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_LIVENESS_SCREEN",
		32: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_PASSPORT_SCREEN",
		33: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_EMIRATES_ID_SCREEN",
		34: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_HAPPY_FLOW_REFERRALS",
		35: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDING_VKYC_SCREEN",
		36: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_PAN_DETAILS",
		37: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_OTHER_DETAILS",
		38: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_ADDRESS_DETAILS",
		39: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_LOAN_REQUIREMENT_DETAILS",
		40: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_EMPLOYMENT_DETAILS",
		41: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CREDIT_REPORT_CONSENT",
		42: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_LANDING_INFO_V2_LENDER_OFFER_SCREEN",
		43: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_OFFER_INTRO_SCREEN",
		44: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_PWA_REDIRECTION_SCREEN",
		45: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN",
		46: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_CONSENT_V2_SCREEN",
		47: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_V2",
		48: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LLM_IN_APP_CSAT",
		49: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_PAY_SEARCH_V2",
		50: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_SCREEN_V2",
		51: "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIERING_EARNED_BENEFIT_SCREEN",
	}
	FeedbackDropOffFlowIdentifier_value = map[string]int32{
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_UNSPECIFIED":                                  0,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_ONBOARDING":                         1,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_NETWORTH_DASHBOARD":                           2,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOAN_ORIGINATION":                             3,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_PLUS_USER_IN_GRACE_PERIOD":     4,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_INFINITE_USER_IN_GRACE_PERIOD": 5,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_PLUS_USER_DOWNGRADED":          6,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_INFINITE_USER_DOWNGRADED":      7,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CHATBOT_DROPOFF_SURVEY":                       8,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_LANDING_PAGE":                       9,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_WALLET_PAGE_FOR_EXISTING_WALLET_USER":     10,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_WALLET_PAGE_FOR_NEW_WALLET_USER":          11,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_LANDING_PAGE_FOR_EXISTING_WALLET_USER":    12,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_LANDING_PAGE_FOR_NEW_WALLET_USER":         13,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_LANDING_PAGE_FOR_NEW_USER":                14,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_AMPLIFI":                      15,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_MAGNIFI":                      16,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_SIMPLIFI":                     17,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_ADDRESS_SELECTION_SCREEN_AMPLIFI":          18,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_ADDRESS_SELECTION_SCREEN_MAGNIFI":          19,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FD_CREATION_SCREEN_SIMPLIFI":               20,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FINAL_CONSENT_SCREEN_AMPLIFI":              21,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FINAL_CONSENT_SCREEN_MAGNIFI":              22,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FINAL_CONSENT_SCREEN_SIMPLIFI":             23,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_WITHDRAW_FUND":                            24,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_HOME_EXPLORE":                                 25,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ENACH_SALARYLITE_INTRO_SCREEN":                26,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_SALARYLITE_MANDATE_SETUP":                     27,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_DC_ORDER_SCREEN":                              28,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_EKYC_SCREEN":                      29,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_PAN_SCREEN":                       30,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_LIVENESS_SCREEN":                  31,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_PASSPORT_SCREEN":                  32,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_EMIRATES_ID_SCREEN":               33,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_HAPPY_FLOW_REFERRALS":                         34,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDING_VKYC_SCREEN":                       35,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_PAN_DETAILS":                            36,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_OTHER_DETAILS":                          37,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_ADDRESS_DETAILS":                        38,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_LOAN_REQUIREMENT_DETAILS":               39,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_EMPLOYMENT_DETAILS":                     40,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CREDIT_REPORT_CONSENT":                        41,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_LANDING_INFO_V2_LENDER_OFFER_SCREEN":    42,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_OFFER_INTRO_SCREEN":                     43,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_PWA_REDIRECTION_SCREEN":                       44,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN":        45,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_CONSENT_V2_SCREEN":                      46,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_V2":                           47,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LLM_IN_APP_CSAT":                              48,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_PAY_SEARCH_V2":                                49,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_SCREEN_V2":                     50,
		"FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIERING_EARNED_BENEFIT_SCREEN":                51,
	}
)

func (x FeedbackDropOffFlowIdentifier) Enum() *FeedbackDropOffFlowIdentifier {
	p := new(FeedbackDropOffFlowIdentifier)
	*p = x
	return p
}

func (x FeedbackDropOffFlowIdentifier) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeedbackDropOffFlowIdentifier) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_inapphelp_feedback_engine_proto_enumTypes[2].Descriptor()
}

func (FeedbackDropOffFlowIdentifier) Type() protoreflect.EnumType {
	return &file_api_typesv2_inapphelp_feedback_engine_proto_enumTypes[2]
}

func (x FeedbackDropOffFlowIdentifier) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeedbackDropOffFlowIdentifier.Descriptor instead.
func (FeedbackDropOffFlowIdentifier) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_inapphelp_feedback_engine_proto_rawDescGZIP(), []int{2}
}

type FeedbackCTAFlowIdentifier int32

const (
	FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_UNSPECIFIED                 FeedbackCTAFlowIdentifier = 0
	FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_CREDIT_CARD_USER_INELIGIBLE FeedbackCTAFlowIdentifier = 1
	FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_REWARDS_POLL                FeedbackCTAFlowIdentifier = 2
	FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_HOME_EXPLORE                FeedbackCTAFlowIdentifier = 3
	// feedback taken after responding to user's query
	FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_USERS_CX_QUERY_RESPONSE        FeedbackCTAFlowIdentifier = 4
	FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_MONEY_SECRETS                  FeedbackCTAFlowIdentifier = 5
	FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_SHARE_AND_UNLOCK_MONEY_SECRETS FeedbackCTAFlowIdentifier = 6
	FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_PORTFOLIO_TRACKER              FeedbackCTAFlowIdentifier = 7
)

// Enum value maps for FeedbackCTAFlowIdentifier.
var (
	FeedbackCTAFlowIdentifier_name = map[int32]string{
		0: "FEEDBACK_CTA_FLOW_IDENTIFIER_UNSPECIFIED",
		1: "FEEDBACK_CTA_FLOW_IDENTIFIER_CREDIT_CARD_USER_INELIGIBLE",
		2: "FEEDBACK_CTA_FLOW_IDENTIFIER_REWARDS_POLL",
		3: "FEEDBACK_CTA_FLOW_IDENTIFIER_HOME_EXPLORE",
		4: "FEEDBACK_CTA_FLOW_IDENTIFIER_USERS_CX_QUERY_RESPONSE",
		5: "FEEDBACK_CTA_FLOW_IDENTIFIER_MONEY_SECRETS",
		6: "FEEDBACK_CTA_FLOW_IDENTIFIER_SHARE_AND_UNLOCK_MONEY_SECRETS",
		7: "FEEDBACK_CTA_FLOW_IDENTIFIER_PORTFOLIO_TRACKER",
	}
	FeedbackCTAFlowIdentifier_value = map[string]int32{
		"FEEDBACK_CTA_FLOW_IDENTIFIER_UNSPECIFIED":                    0,
		"FEEDBACK_CTA_FLOW_IDENTIFIER_CREDIT_CARD_USER_INELIGIBLE":    1,
		"FEEDBACK_CTA_FLOW_IDENTIFIER_REWARDS_POLL":                   2,
		"FEEDBACK_CTA_FLOW_IDENTIFIER_HOME_EXPLORE":                   3,
		"FEEDBACK_CTA_FLOW_IDENTIFIER_USERS_CX_QUERY_RESPONSE":        4,
		"FEEDBACK_CTA_FLOW_IDENTIFIER_MONEY_SECRETS":                  5,
		"FEEDBACK_CTA_FLOW_IDENTIFIER_SHARE_AND_UNLOCK_MONEY_SECRETS": 6,
		"FEEDBACK_CTA_FLOW_IDENTIFIER_PORTFOLIO_TRACKER":              7,
	}
)

func (x FeedbackCTAFlowIdentifier) Enum() *FeedbackCTAFlowIdentifier {
	p := new(FeedbackCTAFlowIdentifier)
	*p = x
	return p
}

func (x FeedbackCTAFlowIdentifier) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeedbackCTAFlowIdentifier) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_inapphelp_feedback_engine_proto_enumTypes[3].Descriptor()
}

func (FeedbackCTAFlowIdentifier) Type() protoreflect.EnumType {
	return &file_api_typesv2_inapphelp_feedback_engine_proto_enumTypes[3]
}

func (x FeedbackCTAFlowIdentifier) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeedbackCTAFlowIdentifier.Descriptor instead.
func (FeedbackCTAFlowIdentifier) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_inapphelp_feedback_engine_proto_rawDescGZIP(), []int{3}
}

var File_api_typesv2_inapphelp_feedback_engine_proto protoreflect.FileDescriptor

var file_api_typesv2_inapphelp_feedback_engine_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x69, 0x6e,
	0x61, 0x70, 0x70, 0x68, 0x65, 0x6c, 0x70, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b,
	0x5f, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2a, 0xca, 0x01, 0x0a, 0x1a, 0x46,
	0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x29, 0x46, 0x45, 0x45,
	0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54,
	0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2a, 0x0a, 0x26, 0x46, 0x45, 0x45, 0x44,
	0x42, 0x41, 0x43, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49,
	0x46, 0x49, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x46, 0x4c,
	0x4f, 0x57, 0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x02,
	0x12, 0x25, 0x0a, 0x21, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x43, 0x54, 0x41, 0x10, 0x03, 0x2a, 0xef, 0x03, 0x0a, 0x19, 0x46, 0x65, 0x65, 0x64,
	0x62, 0x61, 0x63, 0x6b, 0x41, 0x70, 0x70, 0x46, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x28, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43,
	0x4b, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54,
	0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x35, 0x0a, 0x31, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f,
	0x41, 0x50, 0x50, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46,
	0x49, 0x45, 0x52, 0x5f, 0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x50, 0x4f, 0x53, 0x54, 0x5f, 0x57, 0x49,
	0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x2c, 0x0a, 0x28, 0x46, 0x45,
	0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x50,
	0x50, 0x5f, 0x43, 0x53, 0x41, 0x54, 0x10, 0x02, 0x12, 0x3b, 0x0a, 0x37, 0x46, 0x45, 0x45, 0x44,
	0x42, 0x41, 0x43, 0x4b, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47,
	0x45, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x55, 0x52,
	0x56, 0x45, 0x59, 0x10, 0x03, 0x12, 0x30, 0x0a, 0x2c, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43,
	0x4b, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54,
	0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4e, 0x55, 0x44, 0x47, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x4d,
	0x49, 0x53, 0x53, 0x41, 0x4c, 0x10, 0x04, 0x12, 0x32, 0x0a, 0x2e, 0x46, 0x45, 0x45, 0x44, 0x42,
	0x41, 0x43, 0x4b, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45,
	0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x55, 0x53, 0x53, 0x5f, 0x57, 0x49, 0x54, 0x48,
	0x44, 0x52, 0x41, 0x57, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x18, 0x12, 0x35, 0x0a, 0x31, 0x46,
	0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x48, 0x41, 0x50, 0x50,
	0x59, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53,
	0x10, 0x19, 0x12, 0x2e, 0x0a, 0x2a, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x41,
	0x50, 0x50, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49,
	0x45, 0x52, 0x5f, 0x52, 0x45, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x55, 0x44, 0x47, 0x45,
	0x10, 0x1a, 0x12, 0x35, 0x0a, 0x31, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x41,
	0x50, 0x50, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49,
	0x45, 0x52, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x43, 0x53, 0x41, 0x54, 0x5f, 0x41,
	0x55, 0x54, 0x4f, 0x5f, 0x49, 0x44, 0x53, 0x10, 0x1b, 0x2a, 0x91, 0x1b, 0x0a, 0x1d, 0x46, 0x65,
	0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x72, 0x6f, 0x70, 0x4f, 0x66, 0x66, 0x46, 0x6c, 0x6f,
	0x77, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x31, 0x0a, 0x2d, 0x46,
	0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x3a,
	0x0a, 0x36, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f,
	0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46,
	0x49, 0x45, 0x52, 0x5f, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x38, 0x0a, 0x34, 0x46, 0x45,
	0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f,
	0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x10, 0x02, 0x12, 0x36, 0x0a, 0x32, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b,
	0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f,
	0x52, 0x49, 0x47, 0x49, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x12, 0x4e, 0x0a, 0x4a,
	0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46,
	0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45,
	0x52, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x53,
	0x5f, 0x50, 0x4c, 0x55, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x5f, 0x47, 0x52,
	0x41, 0x43, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x49, 0x4f, 0x44, 0x10, 0x04, 0x12, 0x52, 0x0a, 0x4e,
	0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46,
	0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45,
	0x52, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x53,
	0x5f, 0x49, 0x4e, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49,
	0x4e, 0x5f, 0x47, 0x52, 0x41, 0x43, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x49, 0x4f, 0x44, 0x10, 0x05,
	0x12, 0x49, 0x0a, 0x45, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f,
	0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54,
	0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x50,
	0x4c, 0x41, 0x4e, 0x53, 0x5f, 0x50, 0x4c, 0x55, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44,
	0x4f, 0x57, 0x4e, 0x47, 0x52, 0x41, 0x44, 0x45, 0x44, 0x10, 0x06, 0x12, 0x4d, 0x0a, 0x49, 0x46,
	0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52,
	0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x53, 0x5f,
	0x49, 0x4e, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x4f,
	0x57, 0x4e, 0x47, 0x52, 0x41, 0x44, 0x45, 0x44, 0x10, 0x07, 0x12, 0x3c, 0x0a, 0x38, 0x46, 0x45,
	0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f,
	0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x4f, 0x46, 0x46, 0x5f,
	0x53, 0x55, 0x52, 0x56, 0x45, 0x59, 0x10, 0x08, 0x12, 0x40, 0x0a, 0x38, 0x46, 0x45, 0x45, 0x44,
	0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c,
	0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x55, 0x53,
	0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x50, 0x41, 0x47, 0x45, 0x10, 0x09, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x4e, 0x0a, 0x4a, 0x46, 0x45,
	0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f,
	0x55, 0x53, 0x53, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x41, 0x4c,
	0x4c, 0x45, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x0a, 0x12, 0x49, 0x0a, 0x45, 0x46, 0x45,
	0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f,
	0x55, 0x53, 0x53, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x55,
	0x53, 0x45, 0x52, 0x10, 0x0b, 0x12, 0x4f, 0x0a, 0x4b, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43,
	0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x55, 0x53, 0x53, 0x5f, 0x4c,
	0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f,
	0x45, 0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x10, 0x0c, 0x12, 0x4a, 0x0a, 0x46, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41,
	0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x55, 0x53, 0x53, 0x5f,
	0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x4f, 0x52,
	0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x10, 0x0d, 0x12, 0x43, 0x0a, 0x3f, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44,
	0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45,
	0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x55, 0x53, 0x53, 0x5f, 0x4c, 0x41, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4e, 0x45, 0x57,
	0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x0e, 0x12, 0x3d, 0x0a, 0x39, 0x46, 0x45, 0x45, 0x44, 0x42,
	0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x43, 0x43, 0x5f,
	0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x41, 0x4d, 0x50,
	0x4c, 0x49, 0x46, 0x49, 0x10, 0x0f, 0x12, 0x3d, 0x0a, 0x39, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41,
	0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x43, 0x43, 0x5f, 0x49,
	0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4d, 0x41, 0x47, 0x4e,
	0x49, 0x46, 0x49, 0x10, 0x10, 0x12, 0x3e, 0x0a, 0x3a, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43,
	0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x43, 0x43, 0x5f, 0x49, 0x4e,
	0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x53, 0x49, 0x4d, 0x50, 0x4c,
	0x49, 0x46, 0x49, 0x10, 0x11, 0x12, 0x49, 0x0a, 0x45, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43,
	0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x43, 0x43, 0x5f, 0x41, 0x44,
	0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x41, 0x4d, 0x50, 0x4c, 0x49, 0x46, 0x49, 0x10, 0x12,
	0x12, 0x49, 0x0a, 0x45, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f,
	0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54,
	0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x43, 0x43, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53,
	0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x5f, 0x4d, 0x41, 0x47, 0x4e, 0x49, 0x46, 0x49, 0x10, 0x13, 0x12, 0x44, 0x0a, 0x40, 0x46,
	0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52,
	0x5f, 0x43, 0x43, 0x5f, 0x46, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x53, 0x49, 0x4d, 0x50, 0x4c, 0x49, 0x46, 0x49, 0x10,
	0x14, 0x12, 0x45, 0x0a, 0x41, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52,
	0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e,
	0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x43, 0x43, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4c, 0x5f,
	0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x41,
	0x4d, 0x50, 0x4c, 0x49, 0x46, 0x49, 0x10, 0x15, 0x12, 0x45, 0x0a, 0x41, 0x46, 0x45, 0x45, 0x44,
	0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c,
	0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x43, 0x43,
	0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4d, 0x41, 0x47, 0x4e, 0x49, 0x46, 0x49, 0x10, 0x16, 0x12,
	0x46, 0x0a, 0x42, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50,
	0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49,
	0x46, 0x49, 0x45, 0x52, 0x5f, 0x43, 0x43, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x43, 0x4f,
	0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x53, 0x49, 0x4d,
	0x50, 0x4c, 0x49, 0x46, 0x49, 0x10, 0x17, 0x12, 0x37, 0x0a, 0x33, 0x46, 0x45, 0x45, 0x44, 0x42,
	0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x55, 0x53, 0x53,
	0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x18,
	0x12, 0x32, 0x0a, 0x2e, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f,
	0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54,
	0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x45, 0x58, 0x50, 0x4c, 0x4f,
	0x52, 0x45, 0x10, 0x19, 0x12, 0x43, 0x0a, 0x3f, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b,
	0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f,
	0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x1a, 0x12, 0x3e, 0x0a, 0x3a, 0x46, 0x45, 0x45,
	0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x53,
	0x41, 0x4c, 0x41, 0x52, 0x59, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x53, 0x45, 0x54, 0x55, 0x50, 0x10, 0x1b, 0x12, 0x35, 0x0a, 0x31, 0x46, 0x45, 0x45,
	0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x44,
	0x43, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x1c,
	0x12, 0x3d, 0x0a, 0x39, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f,
	0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54,
	0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x45, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x1d, 0x12,
	0x3c, 0x0a, 0x38, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50,
	0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49,
	0x46, 0x49, 0x45, 0x52, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x1e, 0x12, 0x41, 0x0a,
	0x3d, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f,
	0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49,
	0x45, 0x52, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4c,
	0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x1f,
	0x12, 0x41, 0x0a, 0x3d, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f,
	0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54,
	0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0x20, 0x12, 0x44, 0x0a, 0x40, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f,
	0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4d, 0x49, 0x52, 0x41, 0x54, 0x45, 0x53, 0x5f, 0x49, 0x44,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x21, 0x12, 0x3a, 0x0a, 0x36, 0x46, 0x45, 0x45,
	0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x48,
	0x41, 0x50, 0x50, 0x59, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52,
	0x41, 0x4c, 0x53, 0x10, 0x22, 0x12, 0x3c, 0x0a, 0x38, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43,
	0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0x23, 0x12, 0x37, 0x0a, 0x33, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f,
	0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x50,
	0x41, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x24, 0x12, 0x39, 0x0a, 0x35,
	0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46,
	0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45,
	0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x25, 0x12, 0x3b, 0x0a, 0x37, 0x46, 0x45, 0x45, 0x44, 0x42,
	0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4c, 0x4f, 0x41,
	0x4e, 0x53, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49,
	0x4c, 0x53, 0x10, 0x26, 0x12, 0x44, 0x0a, 0x40, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b,
	0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x27, 0x12, 0x3e, 0x0a, 0x3a, 0x46, 0x45,
	0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f,
	0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x28, 0x12, 0x3b, 0x0a, 0x37, 0x46, 0x45,
	0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f,
	0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x43, 0x4f,
	0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x29, 0x12, 0x4f, 0x0a, 0x4b, 0x46, 0x45, 0x45, 0x44, 0x42,
	0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4c, 0x4f, 0x41,
	0x4e, 0x53, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f,
	0x56, 0x32, 0x5f, 0x4c, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x2a, 0x12, 0x3e, 0x0a, 0x3a, 0x46, 0x45, 0x45, 0x44,
	0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c,
	0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4c, 0x4f,
	0x41, 0x4e, 0x53, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x2b, 0x12, 0x3c, 0x0a, 0x38, 0x46, 0x45, 0x45, 0x44,
	0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c,
	0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x50, 0x57,
	0x41, 0x5f, 0x52, 0x45, 0x44, 0x49, 0x52, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x2c, 0x12, 0x4b, 0x0a, 0x47, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41,
	0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x4e,
	0x53, 0x5f, 0x57, 0x45, 0x42, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0x2d, 0x12, 0x3d, 0x0a, 0x39, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f,
	0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x43,
	0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x56, 0x32, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x2e, 0x12, 0x38, 0x0a, 0x34, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44,
	0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45,
	0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x43, 0x43, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0x2f, 0x12, 0x35, 0x0a, 0x31,
	0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46,
	0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45,
	0x52, 0x5f, 0x4c, 0x4c, 0x4d, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x43, 0x53, 0x41,
	0x54, 0x10, 0x30, 0x12, 0x33, 0x0a, 0x2f, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f,
	0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x53, 0x45, 0x41,
	0x52, 0x43, 0x48, 0x5f, 0x56, 0x32, 0x10, 0x31, 0x12, 0x3e, 0x0a, 0x3a, 0x46, 0x45, 0x45, 0x44,
	0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c,
	0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x54, 0x49,
	0x45, 0x52, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x53, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0x32, 0x12, 0x43, 0x0a, 0x3f, 0x46, 0x45, 0x45, 0x44,
	0x42, 0x41, 0x43, 0x4b, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x46, 0x4c,
	0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x54, 0x49,
	0x45, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x41, 0x52, 0x4e, 0x45, 0x44, 0x5f, 0x42, 0x45, 0x4e,
	0x45, 0x46, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x33, 0x2a, 0xc4, 0x03,
	0x0a, 0x19, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x54, 0x41, 0x46, 0x6c, 0x6f,
	0x77, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x28, 0x46,
	0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x43, 0x54, 0x41, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x3c, 0x0a, 0x38, 0x46, 0x45, 0x45,
	0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x43, 0x54, 0x41, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49,
	0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x45, 0x4c, 0x49,
	0x47, 0x49, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x2d, 0x0a, 0x29, 0x46, 0x45, 0x45, 0x44, 0x42,
	0x41, 0x43, 0x4b, 0x5f, 0x43, 0x54, 0x41, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45,
	0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f,
	0x50, 0x4f, 0x4c, 0x4c, 0x10, 0x02, 0x12, 0x2d, 0x0a, 0x29, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41,
	0x43, 0x4b, 0x5f, 0x43, 0x54, 0x41, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e,
	0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x45, 0x58, 0x50, 0x4c,
	0x4f, 0x52, 0x45, 0x10, 0x03, 0x12, 0x38, 0x0a, 0x34, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43,
	0x4b, 0x5f, 0x43, 0x54, 0x41, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54,
	0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x5f, 0x43, 0x58, 0x5f, 0x51,
	0x55, 0x45, 0x52, 0x59, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x10, 0x04, 0x12,
	0x2e, 0x0a, 0x2a, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x43, 0x54, 0x41, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f,
	0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x53, 0x10, 0x05, 0x12,
	0x3f, 0x0a, 0x3b, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x43, 0x54, 0x41, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f,
	0x53, 0x48, 0x41, 0x52, 0x45, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x55, 0x4e, 0x4c, 0x4f, 0x43, 0x4b,
	0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x53, 0x10, 0x06,
	0x12, 0x32, 0x0a, 0x2e, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x43, 0x54, 0x41,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52,
	0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b,
	0x45, 0x52, 0x10, 0x07, 0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_inapphelp_feedback_engine_proto_rawDescOnce sync.Once
	file_api_typesv2_inapphelp_feedback_engine_proto_rawDescData = file_api_typesv2_inapphelp_feedback_engine_proto_rawDesc
)

func file_api_typesv2_inapphelp_feedback_engine_proto_rawDescGZIP() []byte {
	file_api_typesv2_inapphelp_feedback_engine_proto_rawDescOnce.Do(func() {
		file_api_typesv2_inapphelp_feedback_engine_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_inapphelp_feedback_engine_proto_rawDescData)
	})
	return file_api_typesv2_inapphelp_feedback_engine_proto_rawDescData
}

var file_api_typesv2_inapphelp_feedback_engine_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_typesv2_inapphelp_feedback_engine_proto_goTypes = []interface{}{
	(FeedbackFlowIdentifierType)(0),    // 0: api.typesv2.FeedbackFlowIdentifierType
	(FeedbackAppFlowIdentifier)(0),     // 1: api.typesv2.FeedbackAppFlowIdentifier
	(FeedbackDropOffFlowIdentifier)(0), // 2: api.typesv2.FeedbackDropOffFlowIdentifier
	(FeedbackCTAFlowIdentifier)(0),     // 3: api.typesv2.FeedbackCTAFlowIdentifier
}
var file_api_typesv2_inapphelp_feedback_engine_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_typesv2_inapphelp_feedback_engine_proto_init() }
func file_api_typesv2_inapphelp_feedback_engine_proto_init() {
	if File_api_typesv2_inapphelp_feedback_engine_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_inapphelp_feedback_engine_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_inapphelp_feedback_engine_proto_goTypes,
		DependencyIndexes: file_api_typesv2_inapphelp_feedback_engine_proto_depIdxs,
		EnumInfos:         file_api_typesv2_inapphelp_feedback_engine_proto_enumTypes,
	}.Build()
	File_api_typesv2_inapphelp_feedback_engine_proto = out.File
	file_api_typesv2_inapphelp_feedback_engine_proto_rawDesc = nil
	file_api_typesv2_inapphelp_feedback_engine_proto_goTypes = nil
	file_api_typesv2_inapphelp_feedback_engine_proto_depIdxs = nil
}
