//go:generate gen_sql -types=Language

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/user.proto

package typesv2

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Property of a user created with a vendor bank.
// It controls various legal/regulation requirements
// from onboarding to payments to deposits.
type KYCLevel int32

const (
	KYCLevel_KYC_LEVEL_UNSPECIFIED KYCLevel = 0
	// A user with limited access to the banking features.
	// e.g. limit on amount that can be added to the account is 1L for such users.
	KYCLevel_MIN_KYC KYCLevel = 1
	// A user with full access to the banking features.
	KYCLevel_FULL_KYC KYCLevel = 2
)

// Enum value maps for KYCLevel.
var (
	KYCLevel_name = map[int32]string{
		0: "KYC_LEVEL_UNSPECIFIED",
		1: "MIN_KYC",
		2: "FULL_KYC",
	}
	KYCLevel_value = map[string]int32{
		"KYC_LEVEL_UNSPECIFIED": 0,
		"MIN_KYC":               1,
		"FULL_KYC":              2,
	}
)

func (x KYCLevel) Enum() *KYCLevel {
	p := new(KYCLevel)
	*p = x
	return p
}

func (x KYCLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KYCLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_user_proto_enumTypes[0].Descriptor()
}

func (KYCLevel) Type() protoreflect.EnumType {
	return &file_api_typesv2_user_proto_enumTypes[0]
}

func (x KYCLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KYCLevel.Descriptor instead.
func (KYCLevel) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_user_proto_rawDescGZIP(), []int{0}
}

// PaymentParty enum is to define type of party involve in a payment.
type PaymentParty int32

const (
	PaymentParty_PAYMENT_PARTY_UNSPECIFIED PaymentParty = 0
	// a person who pays
	PaymentParty_PAYER PaymentParty = 1
	// a person who get paid
	PaymentParty_PAYEE PaymentParty = 2
)

// Enum value maps for PaymentParty.
var (
	PaymentParty_name = map[int32]string{
		0: "PAYMENT_PARTY_UNSPECIFIED",
		1: "PAYER",
		2: "PAYEE",
	}
	PaymentParty_value = map[string]int32{
		"PAYMENT_PARTY_UNSPECIFIED": 0,
		"PAYER":                     1,
		"PAYEE":                     2,
	}
)

func (x PaymentParty) Enum() *PaymentParty {
	p := new(PaymentParty)
	*p = x
	return p
}

func (x PaymentParty) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentParty) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_user_proto_enumTypes[1].Descriptor()
}

func (PaymentParty) Type() protoreflect.EnumType {
	return &file_api_typesv2_user_proto_enumTypes[1]
}

func (x PaymentParty) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentParty.Descriptor instead.
func (PaymentParty) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_user_proto_rawDescGZIP(), []int{1}
}

// enum for defining device language in UserDeviceProperty message, can be used in other places as well in the future
type Language int32

const (
	Language_LANGUAGE_UNSPECIFIED Language = 0
	Language_LANGUAGE_ENGLISH     Language = 1
	Language_LANGUAGE_HINDI       Language = 2
	Language_LANGUAGE_TELUGU      Language = 3
	Language_LANGUAGE_TAMIL       Language = 4
	Language_LANGUAGE_KANNADA     Language = 5
	Language_LANGUAGE_MALAYALAM   Language = 6
	Language_LANGUAGE_BENGALI     Language = 7
	Language_LANGUAGE_MARATHI     Language = 8
	Language_LANGUAGE_PUNJABI     Language = 9
	Language_LANGUAGE_ASSAMESE    Language = 10
	Language_LANGUAGE_GUJARATI    Language = 11
	Language_LANGUAGE_ODIA        Language = 12
	Language_LANGUAGE_URDU        Language = 13
	Language_LANGUAGE_NEPALI      Language = 14
)

// Enum value maps for Language.
var (
	Language_name = map[int32]string{
		0:  "LANGUAGE_UNSPECIFIED",
		1:  "LANGUAGE_ENGLISH",
		2:  "LANGUAGE_HINDI",
		3:  "LANGUAGE_TELUGU",
		4:  "LANGUAGE_TAMIL",
		5:  "LANGUAGE_KANNADA",
		6:  "LANGUAGE_MALAYALAM",
		7:  "LANGUAGE_BENGALI",
		8:  "LANGUAGE_MARATHI",
		9:  "LANGUAGE_PUNJABI",
		10: "LANGUAGE_ASSAMESE",
		11: "LANGUAGE_GUJARATI",
		12: "LANGUAGE_ODIA",
		13: "LANGUAGE_URDU",
		14: "LANGUAGE_NEPALI",
	}
	Language_value = map[string]int32{
		"LANGUAGE_UNSPECIFIED": 0,
		"LANGUAGE_ENGLISH":     1,
		"LANGUAGE_HINDI":       2,
		"LANGUAGE_TELUGU":      3,
		"LANGUAGE_TAMIL":       4,
		"LANGUAGE_KANNADA":     5,
		"LANGUAGE_MALAYALAM":   6,
		"LANGUAGE_BENGALI":     7,
		"LANGUAGE_MARATHI":     8,
		"LANGUAGE_PUNJABI":     9,
		"LANGUAGE_ASSAMESE":    10,
		"LANGUAGE_GUJARATI":    11,
		"LANGUAGE_ODIA":        12,
		"LANGUAGE_URDU":        13,
		"LANGUAGE_NEPALI":      14,
	}
)

func (x Language) Enum() *Language {
	p := new(Language)
	*p = x
	return p
}

func (x Language) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Language) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_user_proto_enumTypes[2].Descriptor()
}

func (Language) Type() protoreflect.EnumType {
	return &file_api_typesv2_user_proto_enumTypes[2]
}

func (x Language) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Language.Descriptor instead.
func (Language) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_user_proto_rawDescGZIP(), []int{2}
}

// enum used for defining the type of property to be stored in UserDeviceProperty message
type DeviceProperty int32

const (
	DeviceProperty_DEVICE_PROP_UNSPECIFIED DeviceProperty = 0
	// Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_DEVICE_LANGUAGE).GetLanguage()
	DeviceProperty_DEVICE_PROP_DEVICE_LANGUAGE DeviceProperty = 1
	// Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN).GetLocationToken()
	DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN DeviceProperty = 2
	// Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO).GetDeviceModelInfo()
	DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO DeviceProperty = 4
	// Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_MALICIOUS_APP_INFO).GetMaliciousAppInfo()
	DeviceProperty_DEVICE_PROP_MALICIOUS_APP_INFO DeviceProperty = 5
	// Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO).GetAppVersionInfo()
	DeviceProperty_DEVICE_PROP_APP_VERSION_INFO DeviceProperty = 6
	// String. Device identifier property for Android device. More details:
	// The advertising ID is a unique, user-resettable ID for advertising, provided by Google Play services.
	// Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_GOOGLE_ADVERTISING_ID).GetGoogleAdvertisingId()
	DeviceProperty_DEVICE_PROP_GOOGLE_ADVERTISING_ID DeviceProperty = 7
	// String. Device identifier property for Android device. More details:
	// On Android 8.0 (API level 26) and higher versions of the platform, a 64-bit number (expressed as a hexadecimal
	// string), unique to each combination of app-signing key, user, and device. Values of ANDROID_ID are scoped by
	// signing key and user. The value may change if a factory reset is performed on the device or if an APK signing key changes
	// Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_ANDROID_ID).GetAndroidId()
	DeviceProperty_DEVICE_PROP_ANDROID_ID DeviceProperty = 8
	// IP Address Token represents an IP Address in obfuscator service.
	// The IP is the originating IP address or X-Forwarded-For of the user's device.
	// Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_IP_ADDRESS_TOKEN).GetIpAddressToken()
	DeviceProperty_DEVICE_PROP_IP_ADDRESS_TOKEN DeviceProperty = 9
	// Info related to all the apps installed in a user's device
	// Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_ALL_APPS_INFO).GetAllAppsInfo()
	DeviceProperty_DEVICE_PROP_ALL_APPS_INFO DeviceProperty = 10
	// Identifier property for app install
	// Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_APP_INSTALL_ID).GetAppInstallId()
	DeviceProperty_DEVICE_PROP_APP_INSTALL_ID DeviceProperty = 11
	// Device's fixed RAM and internal storage
	// Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_MEMORY_INFO).GetDeviceMemoryInfo()
	DeviceProperty_DEVICE_PROP_MEMORY_INFO DeviceProperty = 12
	// Unique identifier of the mobile device. For Android it's Android_ID and for iOS it's Advertising ID.
	// Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_MEMORY_INFO).GetDeviceId()
	DeviceProperty_DEVICE_PROP_DEVICE_ID DeviceProperty = 13
	// Maps to typesv2.AppName
	// Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_APP_NAME).GetAppName()
	DeviceProperty_DEVICE_PROP_APP_NAME DeviceProperty = 14
)

// Enum value maps for DeviceProperty.
var (
	DeviceProperty_name = map[int32]string{
		0:  "DEVICE_PROP_UNSPECIFIED",
		1:  "DEVICE_PROP_DEVICE_LANGUAGE",
		2:  "DEVICE_PROP_DEVICE_LOCATION_TOKEN",
		4:  "DEVICE_PROP_DEVICE_MODEL_INFO",
		5:  "DEVICE_PROP_MALICIOUS_APP_INFO",
		6:  "DEVICE_PROP_APP_VERSION_INFO",
		7:  "DEVICE_PROP_GOOGLE_ADVERTISING_ID",
		8:  "DEVICE_PROP_ANDROID_ID",
		9:  "DEVICE_PROP_IP_ADDRESS_TOKEN",
		10: "DEVICE_PROP_ALL_APPS_INFO",
		11: "DEVICE_PROP_APP_INSTALL_ID",
		12: "DEVICE_PROP_MEMORY_INFO",
		13: "DEVICE_PROP_DEVICE_ID",
		14: "DEVICE_PROP_APP_NAME",
	}
	DeviceProperty_value = map[string]int32{
		"DEVICE_PROP_UNSPECIFIED":           0,
		"DEVICE_PROP_DEVICE_LANGUAGE":       1,
		"DEVICE_PROP_DEVICE_LOCATION_TOKEN": 2,
		"DEVICE_PROP_DEVICE_MODEL_INFO":     4,
		"DEVICE_PROP_MALICIOUS_APP_INFO":    5,
		"DEVICE_PROP_APP_VERSION_INFO":      6,
		"DEVICE_PROP_GOOGLE_ADVERTISING_ID": 7,
		"DEVICE_PROP_ANDROID_ID":            8,
		"DEVICE_PROP_IP_ADDRESS_TOKEN":      9,
		"DEVICE_PROP_ALL_APPS_INFO":         10,
		"DEVICE_PROP_APP_INSTALL_ID":        11,
		"DEVICE_PROP_MEMORY_INFO":           12,
		"DEVICE_PROP_DEVICE_ID":             13,
		"DEVICE_PROP_APP_NAME":              14,
	}
)

func (x DeviceProperty) Enum() *DeviceProperty {
	p := new(DeviceProperty)
	*p = x
	return p
}

func (x DeviceProperty) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceProperty) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_user_proto_enumTypes[3].Descriptor()
}

func (DeviceProperty) Type() protoreflect.EnumType {
	return &file_api_typesv2_user_proto_enumTypes[3]
}

func (x DeviceProperty) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceProperty.Descriptor instead.
func (DeviceProperty) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_user_proto_rawDescGZIP(), []int{3}
}

// Language info message for device language etc.
type LanguageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Language Language `protobuf:"varint,1,opt,name=language,proto3,enum=api.typesv2.Language" json:"language,omitempty"`
	// the raw language string; can be used when we can't derive enum type from string.
	Raw string `protobuf:"bytes,2,opt,name=raw,proto3" json:"raw,omitempty"`
}

func (x *LanguageInfo) Reset() {
	*x = LanguageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_user_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LanguageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LanguageInfo) ProtoMessage() {}

func (x *LanguageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_user_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LanguageInfo.ProtoReflect.Descriptor instead.
func (*LanguageInfo) Descriptor() ([]byte, []int) {
	return file_api_typesv2_user_proto_rawDescGZIP(), []int{0}
}

func (x *LanguageInfo) GetLanguage() Language {
	if x != nil {
		return x.Language
	}
	return Language_LANGUAGE_UNSPECIFIED
}

func (x *LanguageInfo) GetRaw() string {
	if x != nil {
		return x.Raw
	}
	return ""
}

// this stores the value of property that we are storing in user_device_property table, messages for storing
// other properties like location token, device model etc. can be added later in oneof value.
type PropertyValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to PropValue:
	//
	//	*PropertyValue_DeviceLanguage
	//	*PropertyValue_LocationToken
	//	*PropertyValue_MaliciousAppInfo
	//	*PropertyValue_DeviceModelInfo
	//	*PropertyValue_AppVersionInfo
	//	*PropertyValue_GoogleAdvertisingId
	//	*PropertyValue_AndroidId
	//	*PropertyValue_IpAddressToken
	//	*PropertyValue_AllAppsInfo
	//	*PropertyValue_AppInstallId
	//	*PropertyValue_DeviceMemoryInfo
	//	*PropertyValue_DeviceId
	//	*PropertyValue_AppName
	PropValue isPropertyValue_PropValue `protobuf_oneof:"prop_value"`
}

func (x *PropertyValue) Reset() {
	*x = PropertyValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_user_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PropertyValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropertyValue) ProtoMessage() {}

func (x *PropertyValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_user_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropertyValue.ProtoReflect.Descriptor instead.
func (*PropertyValue) Descriptor() ([]byte, []int) {
	return file_api_typesv2_user_proto_rawDescGZIP(), []int{1}
}

func (m *PropertyValue) GetPropValue() isPropertyValue_PropValue {
	if m != nil {
		return m.PropValue
	}
	return nil
}

func (x *PropertyValue) GetDeviceLanguage() *LanguageInfo {
	if x, ok := x.GetPropValue().(*PropertyValue_DeviceLanguage); ok {
		return x.DeviceLanguage
	}
	return nil
}

func (x *PropertyValue) GetLocationToken() string {
	if x, ok := x.GetPropValue().(*PropertyValue_LocationToken); ok {
		return x.LocationToken
	}
	return ""
}

func (x *PropertyValue) GetMaliciousAppInfo() *UserDeviceMaliciousAppsInfo {
	if x, ok := x.GetPropValue().(*PropertyValue_MaliciousAppInfo); ok {
		return x.MaliciousAppInfo
	}
	return nil
}

func (x *PropertyValue) GetDeviceModelInfo() *common.DeviceModelInfo {
	if x, ok := x.GetPropValue().(*PropertyValue_DeviceModelInfo); ok {
		return x.DeviceModelInfo
	}
	return nil
}

func (x *PropertyValue) GetAppVersionInfo() *AppVersionInfo {
	if x, ok := x.GetPropValue().(*PropertyValue_AppVersionInfo); ok {
		return x.AppVersionInfo
	}
	return nil
}

func (x *PropertyValue) GetGoogleAdvertisingId() string {
	if x, ok := x.GetPropValue().(*PropertyValue_GoogleAdvertisingId); ok {
		return x.GoogleAdvertisingId
	}
	return ""
}

func (x *PropertyValue) GetAndroidId() string {
	if x, ok := x.GetPropValue().(*PropertyValue_AndroidId); ok {
		return x.AndroidId
	}
	return ""
}

func (x *PropertyValue) GetIpAddressToken() string {
	if x, ok := x.GetPropValue().(*PropertyValue_IpAddressToken); ok {
		return x.IpAddressToken
	}
	return ""
}

func (x *PropertyValue) GetAllAppsInfo() *UserDeviceAppsInfo {
	if x, ok := x.GetPropValue().(*PropertyValue_AllAppsInfo); ok {
		return x.AllAppsInfo
	}
	return nil
}

func (x *PropertyValue) GetAppInstallId() string {
	if x, ok := x.GetPropValue().(*PropertyValue_AppInstallId); ok {
		return x.AppInstallId
	}
	return ""
}

func (x *PropertyValue) GetDeviceMemoryInfo() *DeviceMemoryInfo {
	if x, ok := x.GetPropValue().(*PropertyValue_DeviceMemoryInfo); ok {
		return x.DeviceMemoryInfo
	}
	return nil
}

func (x *PropertyValue) GetDeviceId() string {
	if x, ok := x.GetPropValue().(*PropertyValue_DeviceId); ok {
		return x.DeviceId
	}
	return ""
}

func (x *PropertyValue) GetAppName() common.AppName {
	if x, ok := x.GetPropValue().(*PropertyValue_AppName); ok {
		return x.AppName
	}
	return common.AppName(0)
}

type isPropertyValue_PropValue interface {
	isPropertyValue_PropValue()
}

type PropertyValue_DeviceLanguage struct {
	// value for DEVICE_PROP_DEVICE_LANGUAGE
	DeviceLanguage *LanguageInfo `protobuf:"bytes,1,opt,name=device_language,json=deviceLanguage,proto3,oneof"`
}

type PropertyValue_LocationToken struct {
	// value for DEVICE_PROP_DEVICE_LOCATION_TOKEN
	LocationToken string `protobuf:"bytes,2,opt,name=location_token,json=locationToken,proto3,oneof"`
}

type PropertyValue_MaliciousAppInfo struct {
	// value for DEVICE_PROP_MALICIOUS_APP_INFO
	MaliciousAppInfo *UserDeviceMaliciousAppsInfo `protobuf:"bytes,3,opt,name=malicious_app_info,json=maliciousAppInfo,proto3,oneof"`
}

type PropertyValue_DeviceModelInfo struct {
	// value for DEVICE_PROP_DEVICE_MODEL_INFO
	DeviceModelInfo *common.DeviceModelInfo `protobuf:"bytes,4,opt,name=device_model_info,json=deviceModelInfo,proto3,oneof"`
}

type PropertyValue_AppVersionInfo struct {
	// value for DEVICE_PROP_APP_VERSION_INFO
	AppVersionInfo *AppVersionInfo `protobuf:"bytes,5,opt,name=app_version_info,json=appVersionInfo,proto3,oneof"`
}

type PropertyValue_GoogleAdvertisingId struct {
	// value for DEVICE_PROP_GOOGLE_ADVERTISING_ID
	GoogleAdvertisingId string `protobuf:"bytes,6,opt,name=google_advertising_id,json=googleAdvertisingId,proto3,oneof"`
}

type PropertyValue_AndroidId struct {
	// value for DEVICE_PROP_ANDROID_ID
	AndroidId string `protobuf:"bytes,7,opt,name=android_id,json=androidId,proto3,oneof"`
}

type PropertyValue_IpAddressToken struct {
	// value for DEVICE_PROP_IP_ADDRESS_TOKEN
	IpAddressToken string `protobuf:"bytes,8,opt,name=ip_address_token,json=ipAddressToken,proto3,oneof"`
}

type PropertyValue_AllAppsInfo struct {
	// value for DEVICE_PROP_ALL_APPS_INFO
	AllAppsInfo *UserDeviceAppsInfo `protobuf:"bytes,9,opt,name=all_apps_info,json=allAppsInfo,proto3,oneof"`
}

type PropertyValue_AppInstallId struct {
	// value for DEVICE_PROP_APP_INSTALL_ID
	AppInstallId string `protobuf:"bytes,10,opt,name=app_install_id,json=appInstallId,proto3,oneof"`
}

type PropertyValue_DeviceMemoryInfo struct {
	// value for DEVICE_PROP_MEMORY_INFO
	DeviceMemoryInfo *DeviceMemoryInfo `protobuf:"bytes,12,opt,name=device_memory_info,json=deviceMemoryInfo,proto3,oneof"`
}

type PropertyValue_DeviceId struct {
	// value for DEVICE_PROP_DEVICE_ID
	DeviceId string `protobuf:"bytes,13,opt,name=device_id,json=deviceId,proto3,oneof"`
}

type PropertyValue_AppName struct {
	// value for DEVICE_PROP_APP_NAME
	AppName common.AppName `protobuf:"varint,14,opt,name=app_name,json=appName,proto3,enum=api.typesv2.common.AppName,oneof"`
}

func (*PropertyValue_DeviceLanguage) isPropertyValue_PropValue() {}

func (*PropertyValue_LocationToken) isPropertyValue_PropValue() {}

func (*PropertyValue_MaliciousAppInfo) isPropertyValue_PropValue() {}

func (*PropertyValue_DeviceModelInfo) isPropertyValue_PropValue() {}

func (*PropertyValue_AppVersionInfo) isPropertyValue_PropValue() {}

func (*PropertyValue_GoogleAdvertisingId) isPropertyValue_PropValue() {}

func (*PropertyValue_AndroidId) isPropertyValue_PropValue() {}

func (*PropertyValue_IpAddressToken) isPropertyValue_PropValue() {}

func (*PropertyValue_AllAppsInfo) isPropertyValue_PropValue() {}

func (*PropertyValue_AppInstallId) isPropertyValue_PropValue() {}

func (*PropertyValue_DeviceMemoryInfo) isPropertyValue_PropValue() {}

func (*PropertyValue_DeviceId) isPropertyValue_PropValue() {}

func (*PropertyValue_AppName) isPropertyValue_PropValue() {}

// list of this message is sent in UpsertUserDevicePropertyRequest for information regarding which properties to update
type DevicePropertyKeyValuePair struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enum for type of property being stored
	DeviceProperty DeviceProperty `protobuf:"varint,1,opt,name=device_property,json=deviceProperty,proto3,enum=api.typesv2.DeviceProperty" json:"device_property,omitempty"`
	// value for the property being stored
	PropertyValue *PropertyValue `protobuf:"bytes,2,opt,name=property_value,json=propertyValue,proto3" json:"property_value,omitempty"`
}

func (x *DevicePropertyKeyValuePair) Reset() {
	*x = DevicePropertyKeyValuePair{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_user_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevicePropertyKeyValuePair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevicePropertyKeyValuePair) ProtoMessage() {}

func (x *DevicePropertyKeyValuePair) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_user_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevicePropertyKeyValuePair.ProtoReflect.Descriptor instead.
func (*DevicePropertyKeyValuePair) Descriptor() ([]byte, []int) {
	return file_api_typesv2_user_proto_rawDescGZIP(), []int{2}
}

func (x *DevicePropertyKeyValuePair) GetDeviceProperty() DeviceProperty {
	if x != nil {
		return x.DeviceProperty
	}
	return DeviceProperty_DEVICE_PROP_UNSPECIFIED
}

func (x *DevicePropertyKeyValuePair) GetPropertyValue() *PropertyValue {
	if x != nil {
		return x.PropertyValue
	}
	return nil
}

var File_api_typesv2_user_proto protoreflect.FileDescriptor

var file_api_typesv2_user_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x53, 0x0a, 0x0c, 0x4c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x0a, 0x08, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x72, 0x61, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x61, 0x77,
	0x22, 0x9c, 0x06, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x44, 0x0a, 0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x27, 0x0a, 0x0e, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x58, 0x0a, 0x12, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x61,
	0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x41,
	0x70, 0x70, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x10, 0x6d, 0x61, 0x6c, 0x69, 0x63,
	0x69, 0x6f, 0x75, 0x73, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x51, 0x0a, 0x11, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x47,
	0x0a, 0x10, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x34, 0x0a, 0x15, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x13, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0a, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x09, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x10, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0e, 0x69, 0x70, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x45, 0x0a, 0x0d, 0x61, 0x6c,
	0x6c, 0x5f, 0x61, 0x70, 0x70, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x41, 0x70, 0x70, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x48, 0x00, 0x52, 0x0b, 0x61, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x61, 0x70, 0x70,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x4d, 0x0a, 0x12, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x10, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x65,
	0x6d, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41,
	0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x48, 0x00, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0xa5, 0x01, 0x0a, 0x1a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x12, 0x44,
	0x0a, 0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x12, 0x41, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0x40, 0x0a, 0x08, 0x4b, 0x59, 0x43, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x12, 0x19, 0x0a, 0x15, 0x4b, 0x59, 0x43, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x4d, 0x49, 0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x46,
	0x55, 0x4c, 0x4c, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x02, 0x2a, 0x43, 0x0a, 0x0c, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x41, 0x59, 0x45,
	0x52, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x41, 0x59, 0x45, 0x45, 0x10, 0x02, 0x2a, 0xd0,
	0x02, 0x0a, 0x08, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x4c,
	0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47,
	0x45, 0x5f, 0x45, 0x4e, 0x47, 0x4c, 0x49, 0x53, 0x48, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x4c,
	0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45, 0x5f, 0x48, 0x49, 0x4e, 0x44, 0x49, 0x10, 0x02, 0x12,
	0x13, 0x0a, 0x0f, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x45, 0x4c, 0x55,
	0x47, 0x55, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45,
	0x5f, 0x54, 0x41, 0x4d, 0x49, 0x4c, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x41, 0x4e, 0x47,
	0x55, 0x41, 0x47, 0x45, 0x5f, 0x4b, 0x41, 0x4e, 0x4e, 0x41, 0x44, 0x41, 0x10, 0x05, 0x12, 0x16,
	0x0a, 0x12, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45, 0x5f, 0x4d, 0x41, 0x4c, 0x41, 0x59,
	0x41, 0x4c, 0x41, 0x4d, 0x10, 0x06, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41,
	0x47, 0x45, 0x5f, 0x42, 0x45, 0x4e, 0x47, 0x41, 0x4c, 0x49, 0x10, 0x07, 0x12, 0x14, 0x0a, 0x10,
	0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45, 0x5f, 0x4d, 0x41, 0x52, 0x41, 0x54, 0x48, 0x49,
	0x10, 0x08, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45, 0x5f, 0x50,
	0x55, 0x4e, 0x4a, 0x41, 0x42, 0x49, 0x10, 0x09, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x41, 0x4e, 0x47,
	0x55, 0x41, 0x47, 0x45, 0x5f, 0x41, 0x53, 0x53, 0x41, 0x4d, 0x45, 0x53, 0x45, 0x10, 0x0a, 0x12,
	0x15, 0x0a, 0x11, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45, 0x5f, 0x47, 0x55, 0x4a, 0x41,
	0x52, 0x41, 0x54, 0x49, 0x10, 0x0b, 0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41,
	0x47, 0x45, 0x5f, 0x4f, 0x44, 0x49, 0x41, 0x10, 0x0c, 0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x41, 0x4e,
	0x47, 0x55, 0x41, 0x47, 0x45, 0x5f, 0x55, 0x52, 0x44, 0x55, 0x10, 0x0d, 0x12, 0x13, 0x0a, 0x0f,
	0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x45, 0x50, 0x41, 0x4c, 0x49, 0x10,
	0x0e, 0x2a, 0xd4, 0x03, 0x0a, 0x0e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x12, 0x1b, 0x0a, 0x17, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50,
	0x52, 0x4f, 0x50, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x50,
	0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4c, 0x41, 0x4e, 0x47, 0x55, 0x41, 0x47, 0x45,
	0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f,
	0x50, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x44, 0x45, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f,
	0x4d, 0x4f, 0x44, 0x45, 0x4c, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e,
	0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x5f, 0x4d, 0x41, 0x4c, 0x49,
	0x43, 0x49, 0x4f, 0x55, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x05,
	0x12, 0x20, 0x0a, 0x1c, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x5f,
	0x41, 0x50, 0x50, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
	0x10, 0x06, 0x12, 0x25, 0x0a, 0x21, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f,
	0x50, 0x5f, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f, 0x41, 0x44, 0x56, 0x45, 0x52, 0x54, 0x49,
	0x53, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x44, 0x10, 0x07, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x45, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x5f, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44,
	0x5f, 0x49, 0x44, 0x10, 0x08, 0x12, 0x20, 0x0a, 0x1c, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f,
	0x50, 0x52, 0x4f, 0x50, 0x5f, 0x49, 0x50, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f,
	0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x09, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x45, 0x56, 0x49, 0x43,
	0x45, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x41, 0x50, 0x50, 0x53, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x10, 0x0a, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45,
	0x5f, 0x50, 0x52, 0x4f, 0x50, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c,
	0x4c, 0x5f, 0x49, 0x44, 0x10, 0x0b, 0x12, 0x1b, 0x0a, 0x17, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45,
	0x5f, 0x50, 0x52, 0x4f, 0x50, 0x5f, 0x4d, 0x45, 0x4d, 0x4f, 0x52, 0x59, 0x5f, 0x49, 0x4e, 0x46,
	0x4f, 0x10, 0x0c, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50, 0x52,
	0x4f, 0x50, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x44, 0x10, 0x0d, 0x12, 0x18,
	0x0a, 0x14, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x50, 0x5f, 0x41, 0x50,
	0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x0e, 0x42, 0x48, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x5a, 0x22,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_user_proto_rawDescOnce sync.Once
	file_api_typesv2_user_proto_rawDescData = file_api_typesv2_user_proto_rawDesc
)

func file_api_typesv2_user_proto_rawDescGZIP() []byte {
	file_api_typesv2_user_proto_rawDescOnce.Do(func() {
		file_api_typesv2_user_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_user_proto_rawDescData)
	})
	return file_api_typesv2_user_proto_rawDescData
}

var file_api_typesv2_user_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_typesv2_user_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_typesv2_user_proto_goTypes = []interface{}{
	(KYCLevel)(0),                       // 0: api.typesv2.KYCLevel
	(PaymentParty)(0),                   // 1: api.typesv2.PaymentParty
	(Language)(0),                       // 2: api.typesv2.Language
	(DeviceProperty)(0),                 // 3: api.typesv2.DeviceProperty
	(*LanguageInfo)(nil),                // 4: api.typesv2.LanguageInfo
	(*PropertyValue)(nil),               // 5: api.typesv2.PropertyValue
	(*DevicePropertyKeyValuePair)(nil),  // 6: api.typesv2.DevicePropertyKeyValuePair
	(*UserDeviceMaliciousAppsInfo)(nil), // 7: api.typesv2.UserDeviceMaliciousAppsInfo
	(*common.DeviceModelInfo)(nil),      // 8: api.typesv2.common.DeviceModelInfo
	(*AppVersionInfo)(nil),              // 9: api.typesv2.AppVersionInfo
	(*UserDeviceAppsInfo)(nil),          // 10: api.typesv2.UserDeviceAppsInfo
	(*DeviceMemoryInfo)(nil),            // 11: api.typesv2.DeviceMemoryInfo
	(common.AppName)(0),                 // 12: api.typesv2.common.AppName
}
var file_api_typesv2_user_proto_depIdxs = []int32{
	2,  // 0: api.typesv2.LanguageInfo.language:type_name -> api.typesv2.Language
	4,  // 1: api.typesv2.PropertyValue.device_language:type_name -> api.typesv2.LanguageInfo
	7,  // 2: api.typesv2.PropertyValue.malicious_app_info:type_name -> api.typesv2.UserDeviceMaliciousAppsInfo
	8,  // 3: api.typesv2.PropertyValue.device_model_info:type_name -> api.typesv2.common.DeviceModelInfo
	9,  // 4: api.typesv2.PropertyValue.app_version_info:type_name -> api.typesv2.AppVersionInfo
	10, // 5: api.typesv2.PropertyValue.all_apps_info:type_name -> api.typesv2.UserDeviceAppsInfo
	11, // 6: api.typesv2.PropertyValue.device_memory_info:type_name -> api.typesv2.DeviceMemoryInfo
	12, // 7: api.typesv2.PropertyValue.app_name:type_name -> api.typesv2.common.AppName
	3,  // 8: api.typesv2.DevicePropertyKeyValuePair.device_property:type_name -> api.typesv2.DeviceProperty
	5,  // 9: api.typesv2.DevicePropertyKeyValuePair.property_value:type_name -> api.typesv2.PropertyValue
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_api_typesv2_user_proto_init() }
func file_api_typesv2_user_proto_init() {
	if File_api_typesv2_user_proto != nil {
		return
	}
	file_api_typesv2_device_properties_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_user_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LanguageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_user_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PropertyValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_user_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevicePropertyKeyValuePair); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_typesv2_user_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*PropertyValue_DeviceLanguage)(nil),
		(*PropertyValue_LocationToken)(nil),
		(*PropertyValue_MaliciousAppInfo)(nil),
		(*PropertyValue_DeviceModelInfo)(nil),
		(*PropertyValue_AppVersionInfo)(nil),
		(*PropertyValue_GoogleAdvertisingId)(nil),
		(*PropertyValue_AndroidId)(nil),
		(*PropertyValue_IpAddressToken)(nil),
		(*PropertyValue_AllAppsInfo)(nil),
		(*PropertyValue_AppInstallId)(nil),
		(*PropertyValue_DeviceMemoryInfo)(nil),
		(*PropertyValue_DeviceId)(nil),
		(*PropertyValue_AppName)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_user_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_user_proto_goTypes,
		DependencyIndexes: file_api_typesv2_user_proto_depIdxs,
		EnumInfos:         file_api_typesv2_user_proto_enumTypes,
		MessageInfos:      file_api_typesv2_user_proto_msgTypes,
	}.Build()
	File_api_typesv2_user_proto = out.File
	file_api_typesv2_user_proto_rawDesc = nil
	file_api_typesv2_user_proto_goTypes = nil
	file_api_typesv2_user_proto_depIdxs = nil
}
