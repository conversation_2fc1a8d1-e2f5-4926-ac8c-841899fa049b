// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/ui/top_section_components.proto

package ui

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProgressVisualisationType int32

const (
	ProgressVisualisationType_PROGRESS_VISUALISATION_TYPE_UNSPECIFIED ProgressVisualisationType = 0
	ProgressVisualisationType_PROGRESS_VISUALISATION_TYPE_SEMI_CIRCLE ProgressVisualisationType = 1
	ProgressVisualisationType_PROGRESS_VISUALISATION_TYPE_CIRCLE      ProgressVisualisationType = 2
	ProgressVisualisationType_PROGRESS_VISUALISATION_TYPE_LINE        ProgressVisualisationType = 3
)

// Enum value maps for ProgressVisualisationType.
var (
	ProgressVisualisationType_name = map[int32]string{
		0: "PROGRESS_VISUALISATION_TYPE_UNSPECIFIED",
		1: "PROGRESS_VISUALISATION_TYPE_SEMI_CIRCLE",
		2: "PROGRESS_VISUALISATION_TYPE_CIRCLE",
		3: "PROGRESS_VISUALISATION_TYPE_LINE",
	}
	ProgressVisualisationType_value = map[string]int32{
		"PROGRESS_VISUALISATION_TYPE_UNSPECIFIED": 0,
		"PROGRESS_VISUALISATION_TYPE_SEMI_CIRCLE": 1,
		"PROGRESS_VISUALISATION_TYPE_CIRCLE":      2,
		"PROGRESS_VISUALISATION_TYPE_LINE":        3,
	}
)

func (x ProgressVisualisationType) Enum() *ProgressVisualisationType {
	p := new(ProgressVisualisationType)
	*p = x
	return p
}

func (x ProgressVisualisationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProgressVisualisationType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_ui_top_section_components_proto_enumTypes[0].Descriptor()
}

func (ProgressVisualisationType) Type() protoreflect.EnumType {
	return &file_api_typesv2_ui_top_section_components_proto_enumTypes[0]
}

func (x ProgressVisualisationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProgressVisualisationType.Descriptor instead.
func (ProgressVisualisationType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_ui_top_section_components_proto_rawDescGZIP(), []int{0}
}

// UI element that contains a visual Element, title, and subtitle fields in vertical order
// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68443-64946&t=IGmJANLtILqYrUaN-4
type TopSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title                    *common.Text             `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Subtitle                 *common.Text             `protobuf:"bytes,2,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	Image                    *common.VisualElement    `protobuf:"bytes,3,opt,name=image,proto3" json:"image,omitempty"`
	BgColor                  *widget.BackgroundColour `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	ProgressBarVisualisation []*ProgressVisualisation `protobuf:"bytes,5,rep,name=progress_bar_visualisation,json=progressBarVisualisation,proto3" json:"progress_bar_visualisation,omitempty"`
}

func (x *TopSection) Reset() {
	*x = TopSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_top_section_components_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TopSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopSection) ProtoMessage() {}

func (x *TopSection) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_top_section_components_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopSection.ProtoReflect.Descriptor instead.
func (*TopSection) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_top_section_components_proto_rawDescGZIP(), []int{0}
}

func (x *TopSection) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *TopSection) GetSubtitle() *common.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *TopSection) GetImage() *common.VisualElement {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *TopSection) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *TopSection) GetProgressBarVisualisation() []*ProgressVisualisation {
	if x != nil {
		return x.ProgressBarVisualisation
	}
	return nil
}

type ProgressVisualisation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentProgress   int64                     `protobuf:"varint,1,opt,name=current_progress,json=currentProgress,proto3" json:"current_progress,omitempty"`
	MaxProgress       int64                     `protobuf:"varint,2,opt,name=max_progress,json=maxProgress,proto3" json:"max_progress,omitempty"`
	TrackWidth        int64                     `protobuf:"varint,3,opt,name=track_width,json=trackWidth,proto3" json:"track_width,omitempty"`
	ProgressBarWidth  int64                     `protobuf:"varint,4,opt,name=progress_bar_width,json=progressBarWidth,proto3" json:"progress_bar_width,omitempty"`
	TrackColor        *widget.BackgroundColour  `protobuf:"bytes,5,opt,name=track_color,json=trackColor,proto3" json:"track_color,omitempty"`
	ProgressBarColor  *widget.BackgroundColour  `protobuf:"bytes,6,opt,name=progress_bar_color,json=progressBarColor,proto3" json:"progress_bar_color,omitempty"`
	VisualisationType ProgressVisualisationType `protobuf:"varint,7,opt,name=visualisation_type,json=visualisationType,proto3,enum=api.typesv2.ui.ProgressVisualisationType" json:"visualisation_type,omitempty"`
	// Types that are assignable to Visualisation:
	//
	//	*ProgressVisualisation_SemiCircleProgressVisualisation
	//	*ProgressVisualisation_CircleProgressVisualisation
	//	*ProgressVisualisation_LineProgressVisualisation
	Visualisation isProgressVisualisation_Visualisation `protobuf_oneof:"visualisation"`
}

func (x *ProgressVisualisation) Reset() {
	*x = ProgressVisualisation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_top_section_components_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProgressVisualisation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProgressVisualisation) ProtoMessage() {}

func (x *ProgressVisualisation) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_top_section_components_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProgressVisualisation.ProtoReflect.Descriptor instead.
func (*ProgressVisualisation) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_top_section_components_proto_rawDescGZIP(), []int{1}
}

func (x *ProgressVisualisation) GetCurrentProgress() int64 {
	if x != nil {
		return x.CurrentProgress
	}
	return 0
}

func (x *ProgressVisualisation) GetMaxProgress() int64 {
	if x != nil {
		return x.MaxProgress
	}
	return 0
}

func (x *ProgressVisualisation) GetTrackWidth() int64 {
	if x != nil {
		return x.TrackWidth
	}
	return 0
}

func (x *ProgressVisualisation) GetProgressBarWidth() int64 {
	if x != nil {
		return x.ProgressBarWidth
	}
	return 0
}

func (x *ProgressVisualisation) GetTrackColor() *widget.BackgroundColour {
	if x != nil {
		return x.TrackColor
	}
	return nil
}

func (x *ProgressVisualisation) GetProgressBarColor() *widget.BackgroundColour {
	if x != nil {
		return x.ProgressBarColor
	}
	return nil
}

func (x *ProgressVisualisation) GetVisualisationType() ProgressVisualisationType {
	if x != nil {
		return x.VisualisationType
	}
	return ProgressVisualisationType_PROGRESS_VISUALISATION_TYPE_UNSPECIFIED
}

func (m *ProgressVisualisation) GetVisualisation() isProgressVisualisation_Visualisation {
	if m != nil {
		return m.Visualisation
	}
	return nil
}

func (x *ProgressVisualisation) GetSemiCircleProgressVisualisation() *SemiCircleProgressVisualisation {
	if x, ok := x.GetVisualisation().(*ProgressVisualisation_SemiCircleProgressVisualisation); ok {
		return x.SemiCircleProgressVisualisation
	}
	return nil
}

func (x *ProgressVisualisation) GetCircleProgressVisualisation() *CircleProgressVisualisation {
	if x, ok := x.GetVisualisation().(*ProgressVisualisation_CircleProgressVisualisation); ok {
		return x.CircleProgressVisualisation
	}
	return nil
}

func (x *ProgressVisualisation) GetLineProgressVisualisation() *LineProgressVisualisation {
	if x, ok := x.GetVisualisation().(*ProgressVisualisation_LineProgressVisualisation); ok {
		return x.LineProgressVisualisation
	}
	return nil
}

type isProgressVisualisation_Visualisation interface {
	isProgressVisualisation_Visualisation()
}

type ProgressVisualisation_SemiCircleProgressVisualisation struct {
	SemiCircleProgressVisualisation *SemiCircleProgressVisualisation `protobuf:"bytes,8,opt,name=semi_circle_progress_visualisation,json=semiCircleProgressVisualisation,proto3,oneof"`
}

type ProgressVisualisation_CircleProgressVisualisation struct {
	CircleProgressVisualisation *CircleProgressVisualisation `protobuf:"bytes,9,opt,name=circle_progress_visualisation,json=circleProgressVisualisation,proto3,oneof"`
}

type ProgressVisualisation_LineProgressVisualisation struct {
	LineProgressVisualisation *LineProgressVisualisation `protobuf:"bytes,10,opt,name=line_progress_visualisation,json=lineProgressVisualisation,proto3,oneof"`
}

func (*ProgressVisualisation_SemiCircleProgressVisualisation) isProgressVisualisation_Visualisation() {
}

func (*ProgressVisualisation_CircleProgressVisualisation) isProgressVisualisation_Visualisation() {}

func (*ProgressVisualisation_LineProgressVisualisation) isProgressVisualisation_Visualisation() {}

// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=447-10393&mode=design&t=hUQ1W9x7CV6BJTYF-0
type SemiCircleProgressVisualisation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SemiCircleProgressVisualisation) Reset() {
	*x = SemiCircleProgressVisualisation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_top_section_components_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SemiCircleProgressVisualisation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SemiCircleProgressVisualisation) ProtoMessage() {}

func (x *SemiCircleProgressVisualisation) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_top_section_components_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SemiCircleProgressVisualisation.ProtoReflect.Descriptor instead.
func (*SemiCircleProgressVisualisation) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_top_section_components_proto_rawDescGZIP(), []int{2}
}

// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-16392&mode=design&t=hUQ1W9x7CV6BJTYF-0
type CircleProgressVisualisation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CircleProgressVisualisation) Reset() {
	*x = CircleProgressVisualisation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_top_section_components_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CircleProgressVisualisation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircleProgressVisualisation) ProtoMessage() {}

func (x *CircleProgressVisualisation) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_top_section_components_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircleProgressVisualisation.ProtoReflect.Descriptor instead.
func (*CircleProgressVisualisation) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_top_section_components_proto_rawDescGZIP(), []int{3}
}

// LineProgressVisualisation is a visualisation which can be used to show progress on a line. The line has a gradient color.
// There is also label which is shown below the progress.
// figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-16458&mode=design&t=7vaja8dLnmbHqoOM-0
type LineProgressVisualisation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProgressValue     *IconTextComponent `protobuf:"bytes,1,opt,name=progress_value,json=progressValue,proto3" json:"progress_value,omitempty"`
	ProgressBarHeight int64              `protobuf:"varint,2,opt,name=progress_bar_height,json=progressBarHeight,proto3" json:"progress_bar_height,omitempty"`
}

func (x *LineProgressVisualisation) Reset() {
	*x = LineProgressVisualisation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_top_section_components_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LineProgressVisualisation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LineProgressVisualisation) ProtoMessage() {}

func (x *LineProgressVisualisation) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_top_section_components_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LineProgressVisualisation.ProtoReflect.Descriptor instead.
func (*LineProgressVisualisation) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_top_section_components_proto_rawDescGZIP(), []int{4}
}

func (x *LineProgressVisualisation) GetProgressValue() *IconTextComponent {
	if x != nil {
		return x.ProgressValue
	}
	return nil
}

func (x *LineProgressVisualisation) GetProgressBarHeight() int64 {
	if x != nil {
		return x.ProgressBarHeight
	}
	return 0
}

// UI element that contains a ITC and scrolls vertically
// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68443-64954&t=IGmJANLtILqYrUaN-4
type USPCarouselComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BgColor        *widget.BackgroundColour `protobuf:"bytes,1,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	UspMessages    []*IconTextComponent     `protobuf:"bytes,2,rep,name=usp_messages,json=uspMessages,proto3" json:"usp_messages,omitempty"`
	HideIndicators bool                     `protobuf:"varint,3,opt,name=hide_indicators,json=hideIndicators,proto3" json:"hide_indicators,omitempty"`
}

func (x *USPCarouselComponent) Reset() {
	*x = USPCarouselComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_top_section_components_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *USPCarouselComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*USPCarouselComponent) ProtoMessage() {}

func (x *USPCarouselComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_top_section_components_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use USPCarouselComponent.ProtoReflect.Descriptor instead.
func (*USPCarouselComponent) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_top_section_components_proto_rawDescGZIP(), []int{5}
}

func (x *USPCarouselComponent) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *USPCarouselComponent) GetUspMessages() []*IconTextComponent {
	if x != nil {
		return x.UspMessages
	}
	return nil
}

func (x *USPCarouselComponent) GetHideIndicators() bool {
	if x != nil {
		return x.HideIndicators
	}
	return false
}

var File_api_typesv2_ui_top_section_components_proto protoreflect.FileDescriptor

var file_api_typesv2_ui_top_section_components_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69,
	0x2f, 0x74, 0x6f, 0x70, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x1a, 0x1d, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xdb, 0x02, 0x0a, 0x0a, 0x54, 0x6f, 0x70, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x34, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75,
	0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x63, 0x0a, 0x1a, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x18, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42,
	0x61, 0x72, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0xae, 0x06, 0x0a, 0x15, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42, 0x61,
	0x72, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x4f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0a, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x5c, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67,
	0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c,
	0x6f, 0x75, 0x72, 0x52, 0x10, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42, 0x61, 0x72,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x58, 0x0a, 0x12, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x76, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x7e, 0x0a, 0x22, 0x73, 0x65, 0x6d, 0x69, 0x5f, 0x63, 0x69, 0x72, 0x63, 0x6c, 0x65, 0x5f, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x53, 0x65, 0x6d,
	0x69, 0x43, 0x69, 0x72, 0x63, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x1f,
	0x73, 0x65, 0x6d, 0x69, 0x43, 0x69, 0x72, 0x63, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x71, 0x0a, 0x1d, 0x63, 0x69, 0x72, 0x63, 0x6c, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x6c, 0x65, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x1b, 0x63, 0x69, 0x72, 0x63, 0x6c, 0x65, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x6b, 0x0a, 0x1b, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x00, 0x52, 0x19, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x0f, 0x0a, 0x0d, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x21, 0x0a, 0x1f, 0x53, 0x65, 0x6d, 0x69, 0x43, 0x69, 0x72, 0x63, 0x6c, 0x65, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x1d, 0x0a, 0x1b, 0x43, 0x69, 0x72, 0x63, 0x6c, 0x65, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x95, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x6e, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x48, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x42, 0x61, 0x72, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0xd0, 0x01, 0x0a, 0x14, 0x55,
	0x53, 0x50, 0x43, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43,
	0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x44,
	0x0a, 0x0c, 0x75, 0x73, 0x70, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x70, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x68, 0x69, 0x64, 0x65, 0x5f, 0x69, 0x6e, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x68,
	0x69, 0x64, 0x65, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x2a, 0xc3, 0x01,
	0x0a, 0x19, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x27, 0x50,
	0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x56, 0x49, 0x53, 0x55, 0x41, 0x4c, 0x49, 0x53,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2b, 0x0a, 0x27, 0x50, 0x52, 0x4f, 0x47,
	0x52, 0x45, 0x53, 0x53, 0x5f, 0x56, 0x49, 0x53, 0x55, 0x41, 0x4c, 0x49, 0x53, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4d, 0x49, 0x5f, 0x43, 0x49, 0x52,
	0x43, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53,
	0x53, 0x5f, 0x56, 0x49, 0x53, 0x55, 0x41, 0x4c, 0x49, 0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x49, 0x52, 0x43, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x24, 0x0a,
	0x20, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x56, 0x49, 0x53, 0x55, 0x41, 0x4c,
	0x49, 0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49, 0x4e,
	0x45, 0x10, 0x03, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x5a, 0x25, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x75, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_ui_top_section_components_proto_rawDescOnce sync.Once
	file_api_typesv2_ui_top_section_components_proto_rawDescData = file_api_typesv2_ui_top_section_components_proto_rawDesc
)

func file_api_typesv2_ui_top_section_components_proto_rawDescGZIP() []byte {
	file_api_typesv2_ui_top_section_components_proto_rawDescOnce.Do(func() {
		file_api_typesv2_ui_top_section_components_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_ui_top_section_components_proto_rawDescData)
	})
	return file_api_typesv2_ui_top_section_components_proto_rawDescData
}

var file_api_typesv2_ui_top_section_components_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_ui_top_section_components_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_typesv2_ui_top_section_components_proto_goTypes = []interface{}{
	(ProgressVisualisationType)(0),          // 0: api.typesv2.ui.ProgressVisualisationType
	(*TopSection)(nil),                      // 1: api.typesv2.ui.TopSection
	(*ProgressVisualisation)(nil),           // 2: api.typesv2.ui.ProgressVisualisation
	(*SemiCircleProgressVisualisation)(nil), // 3: api.typesv2.ui.SemiCircleProgressVisualisation
	(*CircleProgressVisualisation)(nil),     // 4: api.typesv2.ui.CircleProgressVisualisation
	(*LineProgressVisualisation)(nil),       // 5: api.typesv2.ui.LineProgressVisualisation
	(*USPCarouselComponent)(nil),            // 6: api.typesv2.ui.USPCarouselComponent
	(*common.Text)(nil),                     // 7: api.typesv2.common.Text
	(*common.VisualElement)(nil),            // 8: api.typesv2.common.VisualElement
	(*widget.BackgroundColour)(nil),         // 9: api.typesv2.common.ui.widget.BackgroundColour
	(*IconTextComponent)(nil),               // 10: api.typesv2.ui.IconTextComponent
}
var file_api_typesv2_ui_top_section_components_proto_depIdxs = []int32{
	7,  // 0: api.typesv2.ui.TopSection.title:type_name -> api.typesv2.common.Text
	7,  // 1: api.typesv2.ui.TopSection.subtitle:type_name -> api.typesv2.common.Text
	8,  // 2: api.typesv2.ui.TopSection.image:type_name -> api.typesv2.common.VisualElement
	9,  // 3: api.typesv2.ui.TopSection.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	2,  // 4: api.typesv2.ui.TopSection.progress_bar_visualisation:type_name -> api.typesv2.ui.ProgressVisualisation
	9,  // 5: api.typesv2.ui.ProgressVisualisation.track_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	9,  // 6: api.typesv2.ui.ProgressVisualisation.progress_bar_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	0,  // 7: api.typesv2.ui.ProgressVisualisation.visualisation_type:type_name -> api.typesv2.ui.ProgressVisualisationType
	3,  // 8: api.typesv2.ui.ProgressVisualisation.semi_circle_progress_visualisation:type_name -> api.typesv2.ui.SemiCircleProgressVisualisation
	4,  // 9: api.typesv2.ui.ProgressVisualisation.circle_progress_visualisation:type_name -> api.typesv2.ui.CircleProgressVisualisation
	5,  // 10: api.typesv2.ui.ProgressVisualisation.line_progress_visualisation:type_name -> api.typesv2.ui.LineProgressVisualisation
	10, // 11: api.typesv2.ui.LineProgressVisualisation.progress_value:type_name -> api.typesv2.ui.IconTextComponent
	9,  // 12: api.typesv2.ui.USPCarouselComponent.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	10, // 13: api.typesv2.ui.USPCarouselComponent.usp_messages:type_name -> api.typesv2.ui.IconTextComponent
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_api_typesv2_ui_top_section_components_proto_init() }
func file_api_typesv2_ui_top_section_components_proto_init() {
	if File_api_typesv2_ui_top_section_components_proto != nil {
		return
	}
	file_api_typesv2_ui_icon_text_component_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_ui_top_section_components_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TopSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_top_section_components_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProgressVisualisation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_top_section_components_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SemiCircleProgressVisualisation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_top_section_components_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CircleProgressVisualisation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_top_section_components_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LineProgressVisualisation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_top_section_components_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*USPCarouselComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_typesv2_ui_top_section_components_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*ProgressVisualisation_SemiCircleProgressVisualisation)(nil),
		(*ProgressVisualisation_CircleProgressVisualisation)(nil),
		(*ProgressVisualisation_LineProgressVisualisation)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_ui_top_section_components_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_ui_top_section_components_proto_goTypes,
		DependencyIndexes: file_api_typesv2_ui_top_section_components_proto_depIdxs,
		EnumInfos:         file_api_typesv2_ui_top_section_components_proto_enumTypes,
		MessageInfos:      file_api_typesv2_ui_top_section_components_proto_msgTypes,
	}.Build()
	File_api_typesv2_ui_top_section_components_proto = out.File
	file_api_typesv2_ui_top_section_components_proto_rawDesc = nil
	file_api_typesv2_ui_top_section_components_proto_goTypes = nil
	file_api_typesv2_ui_top_section_components_proto_depIdxs = nil
}
