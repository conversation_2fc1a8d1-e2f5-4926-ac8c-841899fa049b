// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/ui/sdui/sections/expandable_section.proto

package sections

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ExpandableSection with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ExpandableSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExpandableSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExpandableSectionMultiError, or nil if none found.
func (m *ExpandableSection) ValidateAll() error {
	return m.validate(true)
}

func (m *ExpandableSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExpandableSectionValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExpandableSectionValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExpandableSectionValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpandableContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExpandableSectionValidationError{
					field:  "ExpandableContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExpandableSectionValidationError{
					field:  "ExpandableContent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpandableContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExpandableSectionValidationError{
				field:  "ExpandableContent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVisualProperties() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExpandableSectionValidationError{
						field:  fmt.Sprintf("VisualProperties[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExpandableSectionValidationError{
						field:  fmt.Sprintf("VisualProperties[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExpandableSectionValidationError{
					field:  fmt.Sprintf("VisualProperties[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsExpanded

	if all {
		switch v := interface{}(m.GetLoadBehavior()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExpandableSectionValidationError{
					field:  "LoadBehavior",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExpandableSectionValidationError{
					field:  "LoadBehavior",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoadBehavior()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExpandableSectionValidationError{
				field:  "LoadBehavior",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVisibleBehavior()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExpandableSectionValidationError{
					field:  "VisibleBehavior",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExpandableSectionValidationError{
					field:  "VisibleBehavior",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVisibleBehavior()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExpandableSectionValidationError{
				field:  "VisibleBehavior",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExpandableSectionMultiError(errors)
	}

	return nil
}

// ExpandableSectionMultiError is an error wrapping multiple validation errors
// returned by ExpandableSection.ValidateAll() if the designated constraints
// aren't met.
type ExpandableSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExpandableSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExpandableSectionMultiError) AllErrors() []error { return m }

// ExpandableSectionValidationError is the validation error returned by
// ExpandableSection.Validate if the designated constraints aren't met.
type ExpandableSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExpandableSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExpandableSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExpandableSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExpandableSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExpandableSectionValidationError) ErrorName() string {
	return "ExpandableSectionValidationError"
}

// Error satisfies the builtin error interface
func (e ExpandableSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExpandableSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExpandableSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExpandableSectionValidationError{}

// Validate checks the field values on ExpandableSection_Header with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExpandableSection_Header) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExpandableSection_Header with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExpandableSection_HeaderMultiError, or nil if none found.
func (m *ExpandableSection_Header) ValidateAll() error {
	return m.validate(true)
}

func (m *ExpandableSection_Header) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExpandableSection_HeaderValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExpandableSection_HeaderValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExpandableSection_HeaderValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLeftIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExpandableSection_HeaderValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExpandableSection_HeaderValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExpandableSection_HeaderValidationError{
				field:  "LeftIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightIconExpanded()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExpandableSection_HeaderValidationError{
					field:  "RightIconExpanded",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExpandableSection_HeaderValidationError{
					field:  "RightIconExpanded",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightIconExpanded()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExpandableSection_HeaderValidationError{
				field:  "RightIconExpanded",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightIconCollapsed()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExpandableSection_HeaderValidationError{
					field:  "RightIconCollapsed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExpandableSection_HeaderValidationError{
					field:  "RightIconCollapsed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightIconCollapsed()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExpandableSection_HeaderValidationError{
				field:  "RightIconCollapsed",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExpandableSection_HeaderMultiError(errors)
	}

	return nil
}

// ExpandableSection_HeaderMultiError is an error wrapping multiple validation
// errors returned by ExpandableSection_Header.ValidateAll() if the designated
// constraints aren't met.
type ExpandableSection_HeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExpandableSection_HeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExpandableSection_HeaderMultiError) AllErrors() []error { return m }

// ExpandableSection_HeaderValidationError is the validation error returned by
// ExpandableSection_Header.Validate if the designated constraints aren't met.
type ExpandableSection_HeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExpandableSection_HeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExpandableSection_HeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExpandableSection_HeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExpandableSection_HeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExpandableSection_HeaderValidationError) ErrorName() string {
	return "ExpandableSection_HeaderValidationError"
}

// Error satisfies the builtin error interface
func (e ExpandableSection_HeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExpandableSection_Header.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExpandableSection_HeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExpandableSection_HeaderValidationError{}
