// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/ui/top_section_components.proto

package ui

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TopSection with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TopSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TopSection with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TopSectionMultiError, or
// nil if none found.
func (m *TopSection) ValidateAll() error {
	return m.validate(true)
}

func (m *TopSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TopSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TopSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TopSectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TopSectionValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TopSectionValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TopSectionValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TopSectionValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TopSectionValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TopSectionValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TopSectionValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TopSectionValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TopSectionValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetProgressBarVisualisation() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TopSectionValidationError{
						field:  fmt.Sprintf("ProgressBarVisualisation[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TopSectionValidationError{
						field:  fmt.Sprintf("ProgressBarVisualisation[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TopSectionValidationError{
					field:  fmt.Sprintf("ProgressBarVisualisation[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TopSectionMultiError(errors)
	}

	return nil
}

// TopSectionMultiError is an error wrapping multiple validation errors
// returned by TopSection.ValidateAll() if the designated constraints aren't met.
type TopSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TopSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TopSectionMultiError) AllErrors() []error { return m }

// TopSectionValidationError is the validation error returned by
// TopSection.Validate if the designated constraints aren't met.
type TopSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TopSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TopSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TopSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TopSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TopSectionValidationError) ErrorName() string { return "TopSectionValidationError" }

// Error satisfies the builtin error interface
func (e TopSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTopSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TopSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TopSectionValidationError{}

// Validate checks the field values on ProgressVisualisation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProgressVisualisation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProgressVisualisation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProgressVisualisationMultiError, or nil if none found.
func (m *ProgressVisualisation) ValidateAll() error {
	return m.validate(true)
}

func (m *ProgressVisualisation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrentProgress

	// no validation rules for MaxProgress

	// no validation rules for TrackWidth

	// no validation rules for ProgressBarWidth

	if all {
		switch v := interface{}(m.GetTrackColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProgressVisualisationValidationError{
					field:  "TrackColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProgressVisualisationValidationError{
					field:  "TrackColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTrackColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProgressVisualisationValidationError{
				field:  "TrackColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProgressBarColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProgressVisualisationValidationError{
					field:  "ProgressBarColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProgressVisualisationValidationError{
					field:  "ProgressBarColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProgressBarColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProgressVisualisationValidationError{
				field:  "ProgressBarColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VisualisationType

	switch v := m.Visualisation.(type) {
	case *ProgressVisualisation_SemiCircleProgressVisualisation:
		if v == nil {
			err := ProgressVisualisationValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSemiCircleProgressVisualisation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProgressVisualisationValidationError{
						field:  "SemiCircleProgressVisualisation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProgressVisualisationValidationError{
						field:  "SemiCircleProgressVisualisation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSemiCircleProgressVisualisation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProgressVisualisationValidationError{
					field:  "SemiCircleProgressVisualisation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProgressVisualisation_CircleProgressVisualisation:
		if v == nil {
			err := ProgressVisualisationValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCircleProgressVisualisation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProgressVisualisationValidationError{
						field:  "CircleProgressVisualisation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProgressVisualisationValidationError{
						field:  "CircleProgressVisualisation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCircleProgressVisualisation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProgressVisualisationValidationError{
					field:  "CircleProgressVisualisation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProgressVisualisation_LineProgressVisualisation:
		if v == nil {
			err := ProgressVisualisationValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLineProgressVisualisation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProgressVisualisationValidationError{
						field:  "LineProgressVisualisation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProgressVisualisationValidationError{
						field:  "LineProgressVisualisation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLineProgressVisualisation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProgressVisualisationValidationError{
					field:  "LineProgressVisualisation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ProgressVisualisationMultiError(errors)
	}

	return nil
}

// ProgressVisualisationMultiError is an error wrapping multiple validation
// errors returned by ProgressVisualisation.ValidateAll() if the designated
// constraints aren't met.
type ProgressVisualisationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProgressVisualisationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProgressVisualisationMultiError) AllErrors() []error { return m }

// ProgressVisualisationValidationError is the validation error returned by
// ProgressVisualisation.Validate if the designated constraints aren't met.
type ProgressVisualisationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProgressVisualisationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProgressVisualisationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProgressVisualisationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProgressVisualisationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProgressVisualisationValidationError) ErrorName() string {
	return "ProgressVisualisationValidationError"
}

// Error satisfies the builtin error interface
func (e ProgressVisualisationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProgressVisualisation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProgressVisualisationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProgressVisualisationValidationError{}

// Validate checks the field values on SemiCircleProgressVisualisation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SemiCircleProgressVisualisation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SemiCircleProgressVisualisation with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SemiCircleProgressVisualisationMultiError, or nil if none found.
func (m *SemiCircleProgressVisualisation) ValidateAll() error {
	return m.validate(true)
}

func (m *SemiCircleProgressVisualisation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SemiCircleProgressVisualisationMultiError(errors)
	}

	return nil
}

// SemiCircleProgressVisualisationMultiError is an error wrapping multiple
// validation errors returned by SemiCircleProgressVisualisation.ValidateAll()
// if the designated constraints aren't met.
type SemiCircleProgressVisualisationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SemiCircleProgressVisualisationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SemiCircleProgressVisualisationMultiError) AllErrors() []error { return m }

// SemiCircleProgressVisualisationValidationError is the validation error
// returned by SemiCircleProgressVisualisation.Validate if the designated
// constraints aren't met.
type SemiCircleProgressVisualisationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SemiCircleProgressVisualisationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SemiCircleProgressVisualisationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SemiCircleProgressVisualisationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SemiCircleProgressVisualisationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SemiCircleProgressVisualisationValidationError) ErrorName() string {
	return "SemiCircleProgressVisualisationValidationError"
}

// Error satisfies the builtin error interface
func (e SemiCircleProgressVisualisationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSemiCircleProgressVisualisation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SemiCircleProgressVisualisationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SemiCircleProgressVisualisationValidationError{}

// Validate checks the field values on CircleProgressVisualisation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CircleProgressVisualisation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CircleProgressVisualisation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CircleProgressVisualisationMultiError, or nil if none found.
func (m *CircleProgressVisualisation) ValidateAll() error {
	return m.validate(true)
}

func (m *CircleProgressVisualisation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CircleProgressVisualisationMultiError(errors)
	}

	return nil
}

// CircleProgressVisualisationMultiError is an error wrapping multiple
// validation errors returned by CircleProgressVisualisation.ValidateAll() if
// the designated constraints aren't met.
type CircleProgressVisualisationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CircleProgressVisualisationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CircleProgressVisualisationMultiError) AllErrors() []error { return m }

// CircleProgressVisualisationValidationError is the validation error returned
// by CircleProgressVisualisation.Validate if the designated constraints
// aren't met.
type CircleProgressVisualisationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CircleProgressVisualisationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CircleProgressVisualisationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CircleProgressVisualisationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CircleProgressVisualisationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CircleProgressVisualisationValidationError) ErrorName() string {
	return "CircleProgressVisualisationValidationError"
}

// Error satisfies the builtin error interface
func (e CircleProgressVisualisationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCircleProgressVisualisation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CircleProgressVisualisationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CircleProgressVisualisationValidationError{}

// Validate checks the field values on LineProgressVisualisation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LineProgressVisualisation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LineProgressVisualisation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LineProgressVisualisationMultiError, or nil if none found.
func (m *LineProgressVisualisation) ValidateAll() error {
	return m.validate(true)
}

func (m *LineProgressVisualisation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetProgressValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineProgressVisualisationValidationError{
					field:  "ProgressValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineProgressVisualisationValidationError{
					field:  "ProgressValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProgressValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineProgressVisualisationValidationError{
				field:  "ProgressValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProgressBarHeight

	if len(errors) > 0 {
		return LineProgressVisualisationMultiError(errors)
	}

	return nil
}

// LineProgressVisualisationMultiError is an error wrapping multiple validation
// errors returned by LineProgressVisualisation.ValidateAll() if the
// designated constraints aren't met.
type LineProgressVisualisationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LineProgressVisualisationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LineProgressVisualisationMultiError) AllErrors() []error { return m }

// LineProgressVisualisationValidationError is the validation error returned by
// LineProgressVisualisation.Validate if the designated constraints aren't met.
type LineProgressVisualisationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LineProgressVisualisationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LineProgressVisualisationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LineProgressVisualisationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LineProgressVisualisationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LineProgressVisualisationValidationError) ErrorName() string {
	return "LineProgressVisualisationValidationError"
}

// Error satisfies the builtin error interface
func (e LineProgressVisualisationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLineProgressVisualisation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LineProgressVisualisationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LineProgressVisualisationValidationError{}

// Validate checks the field values on USPCarouselComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *USPCarouselComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on USPCarouselComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// USPCarouselComponentMultiError, or nil if none found.
func (m *USPCarouselComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *USPCarouselComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, USPCarouselComponentValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, USPCarouselComponentValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return USPCarouselComponentValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetUspMessages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, USPCarouselComponentValidationError{
						field:  fmt.Sprintf("UspMessages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, USPCarouselComponentValidationError{
						field:  fmt.Sprintf("UspMessages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return USPCarouselComponentValidationError{
					field:  fmt.Sprintf("UspMessages[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for HideIndicators

	if len(errors) > 0 {
		return USPCarouselComponentMultiError(errors)
	}

	return nil
}

// USPCarouselComponentMultiError is an error wrapping multiple validation
// errors returned by USPCarouselComponent.ValidateAll() if the designated
// constraints aren't met.
type USPCarouselComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m USPCarouselComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m USPCarouselComponentMultiError) AllErrors() []error { return m }

// USPCarouselComponentValidationError is the validation error returned by
// USPCarouselComponent.Validate if the designated constraints aren't met.
type USPCarouselComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e USPCarouselComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e USPCarouselComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e USPCarouselComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e USPCarouselComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e USPCarouselComponentValidationError) ErrorName() string {
	return "USPCarouselComponentValidationError"
}

// Error satisfies the builtin error interface
func (e USPCarouselComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUSPCarouselComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = USPCarouselComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = USPCarouselComponentValidationError{}
