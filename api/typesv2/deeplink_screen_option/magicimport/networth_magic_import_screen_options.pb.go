// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/magicimport/networth_magic_import_screen_options.proto

package magicimport

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	properties "github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Deeplink : NETWORTH_MAGIC_IMPORT_SCREEN
type NetworthMagicImportScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,7,opt,name=header,proto3" json:"header,omitempty"`
	// Toolbar
	HeaderBar          *ui.HeaderBar       `protobuf:"bytes,1,opt,name=header_bar,json=headerBar,proto3" json:"header_bar,omitempty"`
	ScanScreen         *ScanStateScreen    `protobuf:"bytes,2,opt,name=scan_screen,json=scanScreen,proto3" json:"scan_screen,omitempty"`
	PreviewScreen      *PreviewStateScreen `protobuf:"bytes,3,opt,name=preview_screen,json=previewScreen,proto3" json:"preview_screen,omitempty"`
	LoadingScreen      *LoadingStateScreen `protobuf:"bytes,4,opt,name=loading_screen,json=loadingScreen,proto3" json:"loading_screen,omitempty"`
	NetworkErrorScreen *ErrorStateScreen   `protobuf:"bytes,6,opt,name=network_error_screen,json=networkErrorScreen,proto3" json:"network_error_screen,omitempty"`
}

func (x *NetworthMagicImportScreenOptions) Reset() {
	*x = NetworthMagicImportScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworthMagicImportScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworthMagicImportScreenOptions) ProtoMessage() {}

func (x *NetworthMagicImportScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworthMagicImportScreenOptions.ProtoReflect.Descriptor instead.
func (*NetworthMagicImportScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *NetworthMagicImportScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *NetworthMagicImportScreenOptions) GetHeaderBar() *ui.HeaderBar {
	if x != nil {
		return x.HeaderBar
	}
	return nil
}

func (x *NetworthMagicImportScreenOptions) GetScanScreen() *ScanStateScreen {
	if x != nil {
		return x.ScanScreen
	}
	return nil
}

func (x *NetworthMagicImportScreenOptions) GetPreviewScreen() *PreviewStateScreen {
	if x != nil {
		return x.PreviewScreen
	}
	return nil
}

func (x *NetworthMagicImportScreenOptions) GetLoadingScreen() *LoadingStateScreen {
	if x != nil {
		return x.LoadingScreen
	}
	return nil
}

func (x *NetworthMagicImportScreenOptions) GetNetworkErrorScreen() *ErrorStateScreen {
	if x != nil {
		return x.NetworkErrorScreen
	}
	return nil
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15699&t=Iy3TPAyrAFPn1jPi-4
type ScanStateScreen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Snap & add anything to your networth
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Scan to know the value of any asset
	SubTitle *common.Text `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	// For best results, scan one clear object at a time.
	Message *common.Text `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	// Not an optional field
	PickerComponent *PickerComponent `protobuf:"bytes,5,opt,name=picker_component,json=pickerComponent,proto3" json:"picker_component,omitempty"`
	// no of assets supported
	NoOfAssetsSupported int32 `protobuf:"varint,6,opt,name=no_of_assets_supported,json=noOfAssetsSupported,proto3" json:"no_of_assets_supported,omitempty"`
	// Supported file types
	SupportedFiles []*SupportedFile `protobuf:"bytes,7,rep,name=supported_files,json=supportedFiles,proto3" json:"supported_files,omitempty"`
	// Max file size in kb for each file and if crosses we are not allowing to add that.
	// For images client will reduce to below this size
	// Total Max allowed size will be max_file_size_kb * no_of_assets_supported
	MaxFileSizeKb int32 `protobuf:"varint,8,opt,name=max_file_size_kb,json=maxFileSizeKb,proto3" json:"max_file_size_kb,omitempty"`
	// this will be shown to the user when permission for camera access is denied
	PermissionDenialPlaceholder *common.VisualElement `protobuf:"bytes,9,opt,name=permission_denial_placeholder,json=permissionDenialPlaceholder,proto3" json:"permission_denial_placeholder,omitempty"`
}

func (x *ScanStateScreen) Reset() {
	*x = ScanStateScreen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanStateScreen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanStateScreen) ProtoMessage() {}

func (x *ScanStateScreen) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanStateScreen.ProtoReflect.Descriptor instead.
func (*ScanStateScreen) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{1}
}

func (x *ScanStateScreen) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *ScanStateScreen) GetSubTitle() *common.Text {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

func (x *ScanStateScreen) GetMessage() *common.Text {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *ScanStateScreen) GetPickerComponent() *PickerComponent {
	if x != nil {
		return x.PickerComponent
	}
	return nil
}

func (x *ScanStateScreen) GetNoOfAssetsSupported() int32 {
	if x != nil {
		return x.NoOfAssetsSupported
	}
	return 0
}

func (x *ScanStateScreen) GetSupportedFiles() []*SupportedFile {
	if x != nil {
		return x.SupportedFiles
	}
	return nil
}

func (x *ScanStateScreen) GetMaxFileSizeKb() int32 {
	if x != nil {
		return x.MaxFileSizeKb
	}
	return 0
}

func (x *ScanStateScreen) GetPermissionDenialPlaceholder() *common.VisualElement {
	if x != nil {
		return x.PermissionDenialPlaceholder
	}
	return nil
}

type PickerComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Background   *widget.BackgroundColour `protobuf:"bytes,1,opt,name=background,proto3" json:"background,omitempty"`
	Border       *widget.BackgroundColour `protobuf:"bytes,2,opt,name=border,proto3" json:"border,omitempty"`
	CornerRadius int32                    `protobuf:"varint,3,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
	GalleryItc   *ui.IconTextComponent    `protobuf:"bytes,4,opt,name=gallery_itc,json=galleryItc,proto3" json:"gallery_itc,omitempty"`
	DocumentItc  *ui.IconTextComponent    `protobuf:"bytes,5,opt,name=document_itc,json=documentItc,proto3" json:"document_itc,omitempty"`
}

func (x *PickerComponent) Reset() {
	*x = PickerComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PickerComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PickerComponent) ProtoMessage() {}

func (x *PickerComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PickerComponent.ProtoReflect.Descriptor instead.
func (*PickerComponent) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{2}
}

func (x *PickerComponent) GetBackground() *widget.BackgroundColour {
	if x != nil {
		return x.Background
	}
	return nil
}

func (x *PickerComponent) GetBorder() *widget.BackgroundColour {
	if x != nil {
		return x.Border
	}
	return nil
}

func (x *PickerComponent) GetCornerRadius() int32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

func (x *PickerComponent) GetGalleryItc() *ui.IconTextComponent {
	if x != nil {
		return x.GalleryItc
	}
	return nil
}

func (x *PickerComponent) GetDocumentItc() *ui.IconTextComponent {
	if x != nil {
		return x.DocumentItc
	}
	return nil
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15728&t=Iy3TPAyrAFPn1jPi-4
type PreviewStateScreen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Ready to analyse its value?
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Scan more
	ScanMore *ScanMoreComponent `protobuf:"bytes,2,opt,name=scan_more,json=scanMore,proto3" json:"scan_more,omitempty"`
	// Analyse CTA
	AnalyseCta *ui.IconTextComponent `protobuf:"bytes,3,opt,name=analyse_cta,json=analyseCta,proto3" json:"analyse_cta,omitempty"`
}

func (x *PreviewStateScreen) Reset() {
	*x = PreviewStateScreen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewStateScreen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewStateScreen) ProtoMessage() {}

func (x *PreviewStateScreen) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewStateScreen.ProtoReflect.Descriptor instead.
func (*PreviewStateScreen) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{3}
}

func (x *PreviewStateScreen) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *PreviewStateScreen) GetScanMore() *ScanMoreComponent {
	if x != nil {
		return x.ScanMore
	}
	return nil
}

func (x *PreviewStateScreen) GetAnalyseCta() *ui.IconTextComponent {
	if x != nil {
		return x.AnalyseCta
	}
	return nil
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15732&t=Iy3TPAyrAFPn1jPi-4
type LoadingStateScreen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Ready to analyse its value?
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Loader animation
	// Using string as we want to use rive which visual element doesn't have support.
	LoaderAnimationUrl string `protobuf:"bytes,2,opt,name=loader_animation_url,json=loaderAnimationUrl,proto3" json:"loader_animation_url,omitempty"`
	// Data is safe and secure
	ConfidentialMessage *ui.IconTextComponent `protobuf:"bytes,4,opt,name=confidential_message,json=confidentialMessage,proto3" json:"confidential_message,omitempty"`
	// Frame on the picture when loading
	// This before image uploading
	Frame *common.VisualElement `protobuf:"bytes,5,opt,name=frame,proto3" json:"frame,omitempty"`
}

func (x *LoadingStateScreen) Reset() {
	*x = LoadingStateScreen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadingStateScreen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadingStateScreen) ProtoMessage() {}

func (x *LoadingStateScreen) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadingStateScreen.ProtoReflect.Descriptor instead.
func (*LoadingStateScreen) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{4}
}

func (x *LoadingStateScreen) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *LoadingStateScreen) GetLoaderAnimationUrl() string {
	if x != nil {
		return x.LoaderAnimationUrl
	}
	return ""
}

func (x *LoadingStateScreen) GetConfidentialMessage() *ui.IconTextComponent {
	if x != nil {
		return x.ConfidentialMessage
	}
	return nil
}

func (x *LoadingStateScreen) GetFrame() *common.VisualElement {
	if x != nil {
		return x.Frame
	}
	return nil
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12546-20031&t=snNA1uq3sIlGvlwB-4
// This is for no internet use case
type ErrorStateScreen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title    *common.Text          `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Message  *common.Text          `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Icon     *common.VisualElement `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	RetryCta *ui.IconTextComponent `protobuf:"bytes,4,opt,name=retry_cta,json=retryCta,proto3" json:"retry_cta,omitempty"`
}

func (x *ErrorStateScreen) Reset() {
	*x = ErrorStateScreen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErrorStateScreen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorStateScreen) ProtoMessage() {}

func (x *ErrorStateScreen) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorStateScreen.ProtoReflect.Descriptor instead.
func (*ErrorStateScreen) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{5}
}

func (x *ErrorStateScreen) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *ErrorStateScreen) GetMessage() *common.Text {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *ErrorStateScreen) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *ErrorStateScreen) GetRetryCta() *ui.IconTextComponent {
	if x != nil {
		return x.RetryCta
	}
	return nil
}

type SupportedFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// e.f, pdf, doc, jpg, png
	FileExtension string `protobuf:"bytes,1,opt,name=file_extension,json=fileExtension,proto3" json:"file_extension,omitempty"`
	// PDF only icon
	IconUrl *common.VisualElement `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	// Preview image url
	PreviewImg *common.VisualElement `protobuf:"bytes,3,opt,name=preview_img,json=previewImg,proto3" json:"preview_img,omitempty"`
}

func (x *SupportedFile) Reset() {
	*x = SupportedFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SupportedFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupportedFile) ProtoMessage() {}

func (x *SupportedFile) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupportedFile.ProtoReflect.Descriptor instead.
func (*SupportedFile) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{6}
}

func (x *SupportedFile) GetFileExtension() string {
	if x != nil {
		return x.FileExtension
	}
	return ""
}

func (x *SupportedFile) GetIconUrl() *common.VisualElement {
	if x != nil {
		return x.IconUrl
	}
	return nil
}

func (x *SupportedFile) GetPreviewImg() *common.VisualElement {
	if x != nil {
		return x.PreviewImg
	}
	return nil
}

type ScanMoreComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Border *properties.BorderProperty `protobuf:"bytes,1,opt,name=border,proto3" json:"border,omitempty"`
	Icon   *common.VisualElement      `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	Text   *common.Text               `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
}

func (x *ScanMoreComponent) Reset() {
	*x = ScanMoreComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanMoreComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanMoreComponent) ProtoMessage() {}

func (x *ScanMoreComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanMoreComponent.ProtoReflect.Descriptor instead.
func (*ScanMoreComponent) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{7}
}

func (x *ScanMoreComponent) GetBorder() *properties.BorderProperty {
	if x != nil {
		return x.Border
	}
	return nil
}

func (x *ScanMoreComponent) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *ScanMoreComponent) GetText() *common.Text {
	if x != nil {
		return x.Text
	}
	return nil
}

var File_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDesc = []byte{
	0x0a, 0x59, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x5f,
	0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x1a, 0x1d, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75,
	0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f,
	0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x61,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75,
	0x69, 0x2f, 0x73, 0x64, 0x75, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd5, 0x04, 0x0a, 0x20, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x35,
	0x0a, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x72, 0x52, 0x09, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x42, 0x61, 0x72, 0x12, 0x60, 0x0a, 0x0b, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x53, 0x63, 0x61, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x0a, 0x73, 0x63, 0x61,
	0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x69, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x42, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x12, 0x69, 0x0a, 0x0e, 0x6c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x4c, 0x6f, 0x61, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x0d,
	0x6c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x72, 0x0a,
	0x14, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x12, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x22, 0xc5, 0x04, 0x0a, 0x0f, 0x53, 0x63, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x32, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x6a, 0x0a, 0x10, 0x70, 0x69, 0x63, 0x6b, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x50, 0x69, 0x63, 0x6b,
	0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x70, 0x69, 0x63,
	0x6b, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x16,
	0x6e, 0x6f, 0x5f, 0x6f, 0x66, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x5f, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x6e, 0x6f,
	0x4f, 0x66, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65,
	0x64, 0x12, 0x66, 0x0a, 0x0f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x53, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x0e, 0x73, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x10, 0x6d, 0x61, 0x78,
	0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6b, 0x62, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x4b, 0x62, 0x12, 0x65, 0x0a, 0x1d, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x64, 0x65, 0x6e, 0x69, 0x61, 0x6c, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x1b, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x6e, 0x69, 0x61, 0x6c, 0x50, 0x6c,
	0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x22, 0xd8, 0x02, 0x0a, 0x0f, 0x50, 0x69,
	0x63, 0x6b, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x4e, 0x0a,
	0x0a, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x52, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x46, 0x0a,
	0x06, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x06, 0x62,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f,
	0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f,
	0x72, 0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x42, 0x0a, 0x0b, 0x67, 0x61,
	0x6c, 0x6c, 0x65, 0x72, 0x79, 0x5f, 0x69, 0x74, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x0a, 0x67, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x49, 0x74, 0x63, 0x12, 0x44,
	0x0a, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x74, 0x63, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x74, 0x63, 0x22, 0xe8, 0x01, 0x0a, 0x12, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x5e, 0x0a, 0x09, 0x73,
	0x63, 0x61, 0x6e, 0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e,
	0x53, 0x63, 0x61, 0x6e, 0x4d, 0x6f, 0x72, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x08, 0x73, 0x63, 0x61, 0x6e, 0x4d, 0x6f, 0x72, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x43, 0x74, 0x61, 0x22,
	0x85, 0x02, 0x0a, 0x12, 0x4c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72,
	0x5f, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x41, 0x6e, 0x69, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x54, 0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x13, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x37,
	0x0a, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x22, 0xed, 0x01, 0x0a, 0x10, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x2e, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x32, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x35, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x09, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x5f, 0x63, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e,
	0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x72,
	0x65, 0x74, 0x72, 0x79, 0x43, 0x74, 0x61, 0x22, 0xb8, 0x01, 0x0a, 0x0d, 0x53, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x3c, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x42,
	0x0a, 0x0b, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x6d, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49,
	0x6d, 0x67, 0x22, 0xc0, 0x01, 0x0a, 0x11, 0x53, 0x63, 0x61, 0x6e, 0x4d, 0x6f, 0x72, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x62, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x73, 0x64, 0x75, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x42, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x06, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x35, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x04, 0x74, 0x65, 0x78, 0x74, 0x42, 0x90, 0x01, 0x0a, 0x45, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50,
	0x01, 0x5a, 0x45, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6d, 0x61, 0x67,
	0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescData = file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_goTypes = []interface{}{
	(*NetworthMagicImportScreenOptions)(nil),          // 0: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions
	(*ScanStateScreen)(nil),                           // 1: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen
	(*PickerComponent)(nil),                           // 2: api.typesv2.deeplink_screen_option.magicimport.PickerComponent
	(*PreviewStateScreen)(nil),                        // 3: api.typesv2.deeplink_screen_option.magicimport.PreviewStateScreen
	(*LoadingStateScreen)(nil),                        // 4: api.typesv2.deeplink_screen_option.magicimport.LoadingStateScreen
	(*ErrorStateScreen)(nil),                          // 5: api.typesv2.deeplink_screen_option.magicimport.ErrorStateScreen
	(*SupportedFile)(nil),                             // 6: api.typesv2.deeplink_screen_option.magicimport.SupportedFile
	(*ScanMoreComponent)(nil),                         // 7: api.typesv2.deeplink_screen_option.magicimport.ScanMoreComponent
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 8: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*ui.HeaderBar)(nil),                              // 9: api.typesv2.HeaderBar
	(*common.Text)(nil),                               // 10: api.typesv2.common.Text
	(*common.VisualElement)(nil),                      // 11: api.typesv2.common.VisualElement
	(*widget.BackgroundColour)(nil),                   // 12: api.typesv2.common.ui.widget.BackgroundColour
	(*ui.IconTextComponent)(nil),                      // 13: api.typesv2.ui.IconTextComponent
	(*properties.BorderProperty)(nil),                 // 14: api.typesv2.ui.sdui.properties.BorderProperty
}
var file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_depIdxs = []int32{
	8,  // 0: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	9,  // 1: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.header_bar:type_name -> api.typesv2.HeaderBar
	1,  // 2: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.scan_screen:type_name -> api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen
	3,  // 3: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.preview_screen:type_name -> api.typesv2.deeplink_screen_option.magicimport.PreviewStateScreen
	4,  // 4: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.loading_screen:type_name -> api.typesv2.deeplink_screen_option.magicimport.LoadingStateScreen
	5,  // 5: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.network_error_screen:type_name -> api.typesv2.deeplink_screen_option.magicimport.ErrorStateScreen
	10, // 6: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.title:type_name -> api.typesv2.common.Text
	10, // 7: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.sub_title:type_name -> api.typesv2.common.Text
	10, // 8: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.message:type_name -> api.typesv2.common.Text
	2,  // 9: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.picker_component:type_name -> api.typesv2.deeplink_screen_option.magicimport.PickerComponent
	6,  // 10: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.supported_files:type_name -> api.typesv2.deeplink_screen_option.magicimport.SupportedFile
	11, // 11: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.permission_denial_placeholder:type_name -> api.typesv2.common.VisualElement
	12, // 12: api.typesv2.deeplink_screen_option.magicimport.PickerComponent.background:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	12, // 13: api.typesv2.deeplink_screen_option.magicimport.PickerComponent.border:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	13, // 14: api.typesv2.deeplink_screen_option.magicimport.PickerComponent.gallery_itc:type_name -> api.typesv2.ui.IconTextComponent
	13, // 15: api.typesv2.deeplink_screen_option.magicimport.PickerComponent.document_itc:type_name -> api.typesv2.ui.IconTextComponent
	10, // 16: api.typesv2.deeplink_screen_option.magicimport.PreviewStateScreen.title:type_name -> api.typesv2.common.Text
	7,  // 17: api.typesv2.deeplink_screen_option.magicimport.PreviewStateScreen.scan_more:type_name -> api.typesv2.deeplink_screen_option.magicimport.ScanMoreComponent
	13, // 18: api.typesv2.deeplink_screen_option.magicimport.PreviewStateScreen.analyse_cta:type_name -> api.typesv2.ui.IconTextComponent
	10, // 19: api.typesv2.deeplink_screen_option.magicimport.LoadingStateScreen.title:type_name -> api.typesv2.common.Text
	13, // 20: api.typesv2.deeplink_screen_option.magicimport.LoadingStateScreen.confidential_message:type_name -> api.typesv2.ui.IconTextComponent
	11, // 21: api.typesv2.deeplink_screen_option.magicimport.LoadingStateScreen.frame:type_name -> api.typesv2.common.VisualElement
	10, // 22: api.typesv2.deeplink_screen_option.magicimport.ErrorStateScreen.title:type_name -> api.typesv2.common.Text
	10, // 23: api.typesv2.deeplink_screen_option.magicimport.ErrorStateScreen.message:type_name -> api.typesv2.common.Text
	11, // 24: api.typesv2.deeplink_screen_option.magicimport.ErrorStateScreen.icon:type_name -> api.typesv2.common.VisualElement
	13, // 25: api.typesv2.deeplink_screen_option.magicimport.ErrorStateScreen.retry_cta:type_name -> api.typesv2.ui.IconTextComponent
	11, // 26: api.typesv2.deeplink_screen_option.magicimport.SupportedFile.icon_url:type_name -> api.typesv2.common.VisualElement
	11, // 27: api.typesv2.deeplink_screen_option.magicimport.SupportedFile.preview_img:type_name -> api.typesv2.common.VisualElement
	14, // 28: api.typesv2.deeplink_screen_option.magicimport.ScanMoreComponent.border:type_name -> api.typesv2.ui.sdui.properties.BorderProperty
	11, // 29: api.typesv2.deeplink_screen_option.magicimport.ScanMoreComponent.icon:type_name -> api.typesv2.common.VisualElement
	10, // 30: api.typesv2.deeplink_screen_option.magicimport.ScanMoreComponent.text:type_name -> api.typesv2.common.Text
	31, // [31:31] is the sub-list for method output_type
	31, // [31:31] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() {
	file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_init()
}
func file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_init() {
	if File_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworthMagicImportScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanStateScreen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PickerComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewStateScreen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadingStateScreen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ErrorStateScreen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SupportedFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanMoreComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_depIdxs,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto = out.File
	file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_depIdxs = nil
}
