// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/chat.proto

package typesv2

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)
)

// Validate checks the field values on ChatbotInitInformation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChatbotInitInformation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChatbotInitInformation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChatbotInitInformationMultiError, or nil if none found.
func (m *ChatbotInitInformation) ValidateAll() error {
	return m.validate(true)
}

func (m *ChatbotInitInformation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.ChatbotSpecificInitInformation.(type) {
	case *ChatbotInitInformation_NuggetChatbotInitInformation:
		if v == nil {
			err := ChatbotInitInformationValidationError{
				field:  "ChatbotSpecificInitInformation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNuggetChatbotInitInformation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ChatbotInitInformationValidationError{
						field:  "NuggetChatbotInitInformation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ChatbotInitInformationValidationError{
						field:  "NuggetChatbotInitInformation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNuggetChatbotInitInformation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ChatbotInitInformationValidationError{
					field:  "NuggetChatbotInitInformation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ChatbotInitInformation_FreshchatChatbotInitInformation:
		if v == nil {
			err := ChatbotInitInformationValidationError{
				field:  "ChatbotSpecificInitInformation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFreshchatChatbotInitInformation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ChatbotInitInformationValidationError{
						field:  "FreshchatChatbotInitInformation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ChatbotInitInformationValidationError{
						field:  "FreshchatChatbotInitInformation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFreshchatChatbotInitInformation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ChatbotInitInformationValidationError{
					field:  "FreshchatChatbotInitInformation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ChatbotInitInformation_SenseforthChatbotInitInformation:
		if v == nil {
			err := ChatbotInitInformationValidationError{
				field:  "ChatbotSpecificInitInformation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSenseforthChatbotInitInformation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ChatbotInitInformationValidationError{
						field:  "SenseforthChatbotInitInformation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ChatbotInitInformationValidationError{
						field:  "SenseforthChatbotInitInformation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSenseforthChatbotInitInformation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ChatbotInitInformationValidationError{
					field:  "SenseforthChatbotInitInformation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ChatbotInitInformationMultiError(errors)
	}

	return nil
}

// ChatbotInitInformationMultiError is an error wrapping multiple validation
// errors returned by ChatbotInitInformation.ValidateAll() if the designated
// constraints aren't met.
type ChatbotInitInformationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChatbotInitInformationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChatbotInitInformationMultiError) AllErrors() []error { return m }

// ChatbotInitInformationValidationError is the validation error returned by
// ChatbotInitInformation.Validate if the designated constraints aren't met.
type ChatbotInitInformationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChatbotInitInformationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChatbotInitInformationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChatbotInitInformationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChatbotInitInformationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChatbotInitInformationValidationError) ErrorName() string {
	return "ChatbotInitInformationValidationError"
}

// Error satisfies the builtin error interface
func (e ChatbotInitInformationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChatbotInitInformation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChatbotInitInformationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChatbotInitInformationValidationError{}

// Validate checks the field values on SenseforthChatbotInitInformation with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SenseforthChatbotInitInformation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SenseforthChatbotInitInformation with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SenseforthChatbotInitInformationMultiError, or nil if none found.
func (m *SenseforthChatbotInitInformation) ValidateAll() error {
	return m.validate(true)
}

func (m *SenseforthChatbotInitInformation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WebViewUrl

	// no validation rules for ShortToken

	// no validation rules for ReuseCacheData

	// no validation rules for BotContextCode

	if len(errors) > 0 {
		return SenseforthChatbotInitInformationMultiError(errors)
	}

	return nil
}

// SenseforthChatbotInitInformationMultiError is an error wrapping multiple
// validation errors returned by
// SenseforthChatbotInitInformation.ValidateAll() if the designated
// constraints aren't met.
type SenseforthChatbotInitInformationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SenseforthChatbotInitInformationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SenseforthChatbotInitInformationMultiError) AllErrors() []error { return m }

// SenseforthChatbotInitInformationValidationError is the validation error
// returned by SenseforthChatbotInitInformation.Validate if the designated
// constraints aren't met.
type SenseforthChatbotInitInformationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SenseforthChatbotInitInformationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SenseforthChatbotInitInformationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SenseforthChatbotInitInformationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SenseforthChatbotInitInformationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SenseforthChatbotInitInformationValidationError) ErrorName() string {
	return "SenseforthChatbotInitInformationValidationError"
}

// Error satisfies the builtin error interface
func (e SenseforthChatbotInitInformationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSenseforthChatbotInitInformation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SenseforthChatbotInitInformationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SenseforthChatbotInitInformationValidationError{}

// Validate checks the field values on FreschatChatbotInitInformation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FreschatChatbotInitInformation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FreschatChatbotInitInformation with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FreschatChatbotInitInformationMultiError, or nil if none found.
func (m *FreschatChatbotInitInformation) ValidateAll() error {
	return m.validate(true)
}

func (m *FreschatChatbotInitInformation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReferenceId

	// no validation rules for AppId

	// no validation rules for AppKey

	// no validation rules for Domain

	// no validation rules for CustomUserProperties

	// no validation rules for Email

	if len(errors) > 0 {
		return FreschatChatbotInitInformationMultiError(errors)
	}

	return nil
}

// FreschatChatbotInitInformationMultiError is an error wrapping multiple
// validation errors returned by FreschatChatbotInitInformation.ValidateAll()
// if the designated constraints aren't met.
type FreschatChatbotInitInformationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FreschatChatbotInitInformationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FreschatChatbotInitInformationMultiError) AllErrors() []error { return m }

// FreschatChatbotInitInformationValidationError is the validation error
// returned by FreschatChatbotInitInformation.Validate if the designated
// constraints aren't met.
type FreschatChatbotInitInformationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FreschatChatbotInitInformationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FreschatChatbotInitInformationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FreschatChatbotInitInformationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FreschatChatbotInitInformationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FreschatChatbotInitInformationValidationError) ErrorName() string {
	return "FreschatChatbotInitInformationValidationError"
}

// Error satisfies the builtin error interface
func (e FreschatChatbotInitInformationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFreschatChatbotInitInformation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FreschatChatbotInitInformationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FreschatChatbotInitInformationValidationError{}

// Validate checks the field values on NuggetChatbotInitInformation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NuggetChatbotInitInformation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NuggetChatbotInitInformation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NuggetChatbotInitInformationMultiError, or nil if none found.
func (m *NuggetChatbotInitInformation) ValidateAll() error {
	return m.validate(true)
}

func (m *NuggetChatbotInitInformation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccessToken

	// no validation rules for CustomUserProperties

	// no validation rules for Namespace

	// no validation rules for DeeplinkUri

	// no validation rules for HttpCode

	if all {
		switch v := interface{}(m.GetBusinessContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NuggetChatbotInitInformationValidationError{
					field:  "BusinessContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NuggetChatbotInitInformationValidationError{
					field:  "BusinessContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBusinessContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NuggetChatbotInitInformationValidationError{
				field:  "BusinessContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NuggetChatbotInitInformationMultiError(errors)
	}

	return nil
}

// NuggetChatbotInitInformationMultiError is an error wrapping multiple
// validation errors returned by NuggetChatbotInitInformation.ValidateAll() if
// the designated constraints aren't met.
type NuggetChatbotInitInformationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NuggetChatbotInitInformationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NuggetChatbotInitInformationMultiError) AllErrors() []error { return m }

// NuggetChatbotInitInformationValidationError is the validation error returned
// by NuggetChatbotInitInformation.Validate if the designated constraints
// aren't met.
type NuggetChatbotInitInformationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NuggetChatbotInitInformationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NuggetChatbotInitInformationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NuggetChatbotInitInformationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NuggetChatbotInitInformationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NuggetChatbotInitInformationValidationError) ErrorName() string {
	return "NuggetChatbotInitInformationValidationError"
}

// Error satisfies the builtin error interface
func (e NuggetChatbotInitInformationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNuggetChatbotInitInformation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NuggetChatbotInitInformationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NuggetChatbotInitInformationValidationError{}

// Validate checks the field values on
// NuggetChatbotInitInformation_BusinessContext with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NuggetChatbotInitInformation_BusinessContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// NuggetChatbotInitInformation_BusinessContext with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// NuggetChatbotInitInformation_BusinessContextMultiError, or nil if none found.
func (m *NuggetChatbotInitInformation_BusinessContext) ValidateAll() error {
	return m.validate(true)
}

func (m *NuggetChatbotInitInformation_BusinessContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChannelHandle

	// no validation rules for TicketGroupingId

	{
		sorted_keys := make([]string, len(m.GetTicketProperties()))
		i := 0
		for key := range m.GetTicketProperties() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetTicketProperties()[key]
			_ = val

			// no validation rules for TicketProperties[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, NuggetChatbotInitInformation_BusinessContextValidationError{
							field:  fmt.Sprintf("TicketProperties[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, NuggetChatbotInitInformation_BusinessContextValidationError{
							field:  fmt.Sprintf("TicketProperties[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return NuggetChatbotInitInformation_BusinessContextValidationError{
						field:  fmt.Sprintf("TicketProperties[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	{
		sorted_keys := make([]string, len(m.GetBotProperties()))
		i := 0
		for key := range m.GetBotProperties() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetBotProperties()[key]
			_ = val

			// no validation rules for BotProperties[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, NuggetChatbotInitInformation_BusinessContextValidationError{
							field:  fmt.Sprintf("BotProperties[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, NuggetChatbotInitInformation_BusinessContextValidationError{
							field:  fmt.Sprintf("BotProperties[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return NuggetChatbotInitInformation_BusinessContextValidationError{
						field:  fmt.Sprintf("BotProperties[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return NuggetChatbotInitInformation_BusinessContextMultiError(errors)
	}

	return nil
}

// NuggetChatbotInitInformation_BusinessContextMultiError is an error wrapping
// multiple validation errors returned by
// NuggetChatbotInitInformation_BusinessContext.ValidateAll() if the
// designated constraints aren't met.
type NuggetChatbotInitInformation_BusinessContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NuggetChatbotInitInformation_BusinessContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NuggetChatbotInitInformation_BusinessContextMultiError) AllErrors() []error { return m }

// NuggetChatbotInitInformation_BusinessContextValidationError is the
// validation error returned by
// NuggetChatbotInitInformation_BusinessContext.Validate if the designated
// constraints aren't met.
type NuggetChatbotInitInformation_BusinessContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NuggetChatbotInitInformation_BusinessContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NuggetChatbotInitInformation_BusinessContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NuggetChatbotInitInformation_BusinessContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NuggetChatbotInitInformation_BusinessContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NuggetChatbotInitInformation_BusinessContextValidationError) ErrorName() string {
	return "NuggetChatbotInitInformation_BusinessContextValidationError"
}

// Error satisfies the builtin error interface
func (e NuggetChatbotInitInformation_BusinessContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNuggetChatbotInitInformation_BusinessContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NuggetChatbotInitInformation_BusinessContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NuggetChatbotInitInformation_BusinessContextValidationError{}

// Validate checks the field values on
// NuggetChatbotInitInformation_BusinessContext_StringList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NuggetChatbotInitInformation_BusinessContext_StringList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// NuggetChatbotInitInformation_BusinessContext_StringList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NuggetChatbotInitInformation_BusinessContext_StringListMultiError, or nil
// if none found.
func (m *NuggetChatbotInitInformation_BusinessContext_StringList) ValidateAll() error {
	return m.validate(true)
}

func (m *NuggetChatbotInitInformation_BusinessContext_StringList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return NuggetChatbotInitInformation_BusinessContext_StringListMultiError(errors)
	}

	return nil
}

// NuggetChatbotInitInformation_BusinessContext_StringListMultiError is an
// error wrapping multiple validation errors returned by
// NuggetChatbotInitInformation_BusinessContext_StringList.ValidateAll() if
// the designated constraints aren't met.
type NuggetChatbotInitInformation_BusinessContext_StringListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NuggetChatbotInitInformation_BusinessContext_StringListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NuggetChatbotInitInformation_BusinessContext_StringListMultiError) AllErrors() []error {
	return m
}

// NuggetChatbotInitInformation_BusinessContext_StringListValidationError is
// the validation error returned by
// NuggetChatbotInitInformation_BusinessContext_StringList.Validate if the
// designated constraints aren't met.
type NuggetChatbotInitInformation_BusinessContext_StringListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NuggetChatbotInitInformation_BusinessContext_StringListValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e NuggetChatbotInitInformation_BusinessContext_StringListValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e NuggetChatbotInitInformation_BusinessContext_StringListValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e NuggetChatbotInitInformation_BusinessContext_StringListValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e NuggetChatbotInitInformation_BusinessContext_StringListValidationError) ErrorName() string {
	return "NuggetChatbotInitInformation_BusinessContext_StringListValidationError"
}

// Error satisfies the builtin error interface
func (e NuggetChatbotInitInformation_BusinessContext_StringListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNuggetChatbotInitInformation_BusinessContext_StringList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NuggetChatbotInitInformation_BusinessContext_StringListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NuggetChatbotInitInformation_BusinessContext_StringListValidationError{}
