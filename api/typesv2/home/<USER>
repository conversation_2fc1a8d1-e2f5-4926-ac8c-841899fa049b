// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/home/<USER>

package home

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IconType int32

const (
	IconType_ICON_TYPE_UNSPECIFIED IconType = 0
	// Nav bar icons
	IconType_HOME     IconType = 1
	IconType_PAY      IconType = 2
	IconType_INVEST   IconType = 3
	IconType_BORROW   IconType = 4
	IconType_DISCOVER IconType = 5
	// Dashboard Icons
	IconType_DASHBOARD_SETTING IconType = 6
	IconType_DASHBOARD_ACTION  IconType = 7
	// Appbar icon for notifications
	IconType_NOTIFICATION IconType = 8
	// Appbar icon for Analyser
	IconType_ANALYSER IconType = 9
	// Appbar icon for rewards
	IconType_REWARDS IconType = 10
	// Appbar icon for profile
	IconType_PROFILE IconType = 11
	// App bar icon for Segmented card entry for both DC & CC
	IconType_CARD IconType = 14
	// App bar icon for Credit card (standalone)
	IconType_CREDIT_CARD IconType = 58
	// App bar icon for Debit card (standalone)
	IconType_DEBIT_CARD IconType = 59
	// Appbar icon for referral
	IconType_REFER IconType = 16
	// Sticky Icon typesv2
	// Sticky Icon on home page - QR code icon
	IconType_QR_CODE IconType = 12
	// Sticky icon on primary savings summary page
	IconType_SAVINGS_SUMMARY IconType = 13
	// Connect bank account(for connected accounts use case)
	IconType_CONNECT_ACCOUNT IconType = 15
	// shortcut icon typesv2
	IconType_SHORTCUT_SALARY_PROGRAM        IconType = 17
	IconType_SHORTCUT_SALARY_BASIC_PLAN     IconType = 87
	IconType_SHORTCUT_JUMP                  IconType = 18
	IconType_SHORTCUT_PRE_APPROVED_LOAN     IconType = 19
	IconType_SHORTCUT_PRE_PAY_LOAN          IconType = 73
	IconType_SHORTCUT_MF                    IconType = 20
	IconType_SHORTCUT_FD                    IconType = 21
	IconType_SHORTCUT_SD                    IconType = 22
	IconType_SHORTCUT_FITTT                 IconType = 23
	IconType_SHORTCUT_PAY_TO_CONTACT        IconType = 24
	IconType_SHORTCUT_PAY_TO_UPI            IconType = 25
	IconType_SHORTCUT_PAY_TO_PHONE          IconType = 26
	IconType_SHORTCUT_BANK_TRANSFER         IconType = 27
	IconType_SHORTCUT_DEBIT_CARD            IconType = 28
	IconType_SHORTCUT_CREDIT_CARD           IconType = 29
	IconType_SHORTCUT_CARD_OFFERS           IconType = 30
	IconType_SHORTCUT_WAYS_TO_EARN          IconType = 31
	IconType_SHORTCUT_OFFERS_CATALOG        IconType = 32
	IconType_SHORTCUT_OFFERS_ORDERS         IconType = 33
	IconType_SHORTCUT_REFERRALS             IconType = 34
	IconType_SHORTCUT_SPEND_ANALYSER        IconType = 35
	IconType_SHORTCUT_CREDIT_SCORE_ANALYSER IconType = 36
	IconType_SHORTCUT_MF_ANALYSER           IconType = 37
	IconType_SHORTCUT_FI_MINUTES            IconType = 38
	IconType_SHORTCUT_KYC_INFO              IconType = 39
	IconType_SHORTCUT_AUTO_INVEST           IconType = 40
	IconType_SHORTCUT_AUTO_PAY              IconType = 41
	IconType_SHORTCUT_AUTO_SAVE             IconType = 42
	IconType_SHORTCUT_MANAGE_RULES          IconType = 43
	IconType_SHORTCUT_SET_REMINDER          IconType = 44
	IconType_SHORTCUT_EARLY_SALARY          IconType = 45
	IconType_SHORTCUT_CONNECTED_ACCOUNT     IconType = 46
	IconType_SHORTCUT_ADD_MONEY             IconType = 47
	IconType_SHORTCUT_SCAN_AND_PAY          IconType = 48
	IconType_SHORTCUT_PLUS_PLAN             IconType = 49
	IconType_SHORTCUT_INFINITE_PLAN         IconType = 50
	IconType_SHORTCUT_NETWORTH              IconType = 51
	IconType_SHORTCUT_CHEQUEBOOK_REQUEST    IconType = 52
	IconType_SHORTCUT_INTEREST_CERTIFICATE  IconType = 53
	IconType_SHORTCUT_ALL_REQUESTS          IconType = 54
	IconType_SHORTCUT_SECURED_LOANS         IconType = 55
	IconType_SHORTCUT_US_STOCKS             IconType = 56
	// Shortcuts for fi store icons
	IconType_SHORTCUT_FI_STORE                IconType = 57
	IconType_SHORTCUT_FI_STORE_GIFT_CARDS     IconType = 61
	IconType_SHORTCUT_FI_STORE_MILES_EXCHANGE IconType = 72
	IconType_SHORTCUT_DEBIT_CARD_CONTROLS     IconType = 60
	IconType_SHORTCUT_EXPLORE_FI              IconType = 62
	IconType_SHORTCUT_BANK_STATEMENT          IconType = 63
	// Savings account benefits tab which will get displayed to fi lite users
	IconType_SAVINGS_ACCOUNT_BENEFITS      IconType = 64
	IconType_SHORTCUT_CREDIT_CARD_CONTROLS IconType = 65
	IconType_SHORTCUT_CREDIT_CARD_OFFERS   IconType = 66
	IconType_SHORTCUT_DEBIT_CARD_OFFERS    IconType = 67
	IconType_SHORTCUT_SAVINGS_ACCOUNT      IconType = 68
	IconType_SHORTCUT_ZERO_STATE_REWARD    IconType = 69
	IconType_SHORTCUT_AA_SALARY_PLAN       IconType = 70
	// Networth icon in home bottom nav bar
	IconType_BOTTOM_NAV_BAR_NETWORTH               IconType = 71
	IconType_SHORTCUT_TOP_SPEND_MERCHANTS_ANALYSER IconType = 74
	IconType_SHORTCUT_SPENDS_BY_TIME_ANALYSER      IconType = 75
	IconType_SHORTCUT_SELF_TRANSFER                IconType = 76
	// Travel section icons
	IconType_SHORTCUT_TRAVEL_BUDGETING                          IconType = 77
	IconType_SHORTCUT_TRAVEL_MODE_SWITCH                        IconType = 78
	IconType_SHORTCUT_ATM_LOCATOR                               IconType = 79
	IconType_SHORTCUT_MODIFY_DC_INTERNATIONAL_TRANSACTION_LIMIT IconType = 80
	IconType_SHORTCUT_CURRENCY_EXCHANGE_CALCULATOR              IconType = 81
	// wealth builder landing page icon in home bottom nav bar
	IconType_BOTTOM_NAV_WEALTH_BUILDER          IconType = 82
	IconType_SHORTCUT_ORDER_PHYSICAL_DEBIT_CARD IconType = 83
	IconType_SHORTCUT_DEBIT_CARD_SETTINGS       IconType = 84
	// wealth builder landing page icon in home top nav bar
	IconType_TOP_NAV_WEALTH_BUILDER IconType = 85
	// US Stocks icon in home bottom nav bar
	IconType_NAV_BAR_US_STOCKS IconType = 86
	// WEALTH BUILDER SHORCUT FOR Explore
	// deprecated: use SHORTCUT_NETWORTH
	//
	// Deprecated: Marked as deprecated in api/typesv2/home/<USER>
	IconType_SHORTCUT_WEALTH_BUILDER IconType = 88
	// Redirects to AMB details screen
	IconType_SHORTCUT_AMB IconType = 89
	// Redirects user to fi calculator web page
	IconType_SHORTCUT_CALCULATOR IconType = 90
)

// Enum value maps for IconType.
var (
	IconType_name = map[int32]string{
		0:  "ICON_TYPE_UNSPECIFIED",
		1:  "HOME",
		2:  "PAY",
		3:  "INVEST",
		4:  "BORROW",
		5:  "DISCOVER",
		6:  "DASHBOARD_SETTING",
		7:  "DASHBOARD_ACTION",
		8:  "NOTIFICATION",
		9:  "ANALYSER",
		10: "REWARDS",
		11: "PROFILE",
		14: "CARD",
		58: "CREDIT_CARD",
		59: "DEBIT_CARD",
		16: "REFER",
		12: "QR_CODE",
		13: "SAVINGS_SUMMARY",
		15: "CONNECT_ACCOUNT",
		17: "SHORTCUT_SALARY_PROGRAM",
		87: "SHORTCUT_SALARY_BASIC_PLAN",
		18: "SHORTCUT_JUMP",
		19: "SHORTCUT_PRE_APPROVED_LOAN",
		73: "SHORTCUT_PRE_PAY_LOAN",
		20: "SHORTCUT_MF",
		21: "SHORTCUT_FD",
		22: "SHORTCUT_SD",
		23: "SHORTCUT_FITTT",
		24: "SHORTCUT_PAY_TO_CONTACT",
		25: "SHORTCUT_PAY_TO_UPI",
		26: "SHORTCUT_PAY_TO_PHONE",
		27: "SHORTCUT_BANK_TRANSFER",
		28: "SHORTCUT_DEBIT_CARD",
		29: "SHORTCUT_CREDIT_CARD",
		30: "SHORTCUT_CARD_OFFERS",
		31: "SHORTCUT_WAYS_TO_EARN",
		32: "SHORTCUT_OFFERS_CATALOG",
		33: "SHORTCUT_OFFERS_ORDERS",
		34: "SHORTCUT_REFERRALS",
		35: "SHORTCUT_SPEND_ANALYSER",
		36: "SHORTCUT_CREDIT_SCORE_ANALYSER",
		37: "SHORTCUT_MF_ANALYSER",
		38: "SHORTCUT_FI_MINUTES",
		39: "SHORTCUT_KYC_INFO",
		40: "SHORTCUT_AUTO_INVEST",
		41: "SHORTCUT_AUTO_PAY",
		42: "SHORTCUT_AUTO_SAVE",
		43: "SHORTCUT_MANAGE_RULES",
		44: "SHORTCUT_SET_REMINDER",
		45: "SHORTCUT_EARLY_SALARY",
		46: "SHORTCUT_CONNECTED_ACCOUNT",
		47: "SHORTCUT_ADD_MONEY",
		48: "SHORTCUT_SCAN_AND_PAY",
		49: "SHORTCUT_PLUS_PLAN",
		50: "SHORTCUT_INFINITE_PLAN",
		51: "SHORTCUT_NETWORTH",
		52: "SHORTCUT_CHEQUEBOOK_REQUEST",
		53: "SHORTCUT_INTEREST_CERTIFICATE",
		54: "SHORTCUT_ALL_REQUESTS",
		55: "SHORTCUT_SECURED_LOANS",
		56: "SHORTCUT_US_STOCKS",
		57: "SHORTCUT_FI_STORE",
		61: "SHORTCUT_FI_STORE_GIFT_CARDS",
		72: "SHORTCUT_FI_STORE_MILES_EXCHANGE",
		60: "SHORTCUT_DEBIT_CARD_CONTROLS",
		62: "SHORTCUT_EXPLORE_FI",
		63: "SHORTCUT_BANK_STATEMENT",
		64: "SAVINGS_ACCOUNT_BENEFITS",
		65: "SHORTCUT_CREDIT_CARD_CONTROLS",
		66: "SHORTCUT_CREDIT_CARD_OFFERS",
		67: "SHORTCUT_DEBIT_CARD_OFFERS",
		68: "SHORTCUT_SAVINGS_ACCOUNT",
		69: "SHORTCUT_ZERO_STATE_REWARD",
		70: "SHORTCUT_AA_SALARY_PLAN",
		71: "BOTTOM_NAV_BAR_NETWORTH",
		74: "SHORTCUT_TOP_SPEND_MERCHANTS_ANALYSER",
		75: "SHORTCUT_SPENDS_BY_TIME_ANALYSER",
		76: "SHORTCUT_SELF_TRANSFER",
		77: "SHORTCUT_TRAVEL_BUDGETING",
		78: "SHORTCUT_TRAVEL_MODE_SWITCH",
		79: "SHORTCUT_ATM_LOCATOR",
		80: "SHORTCUT_MODIFY_DC_INTERNATIONAL_TRANSACTION_LIMIT",
		81: "SHORTCUT_CURRENCY_EXCHANGE_CALCULATOR",
		82: "BOTTOM_NAV_WEALTH_BUILDER",
		83: "SHORTCUT_ORDER_PHYSICAL_DEBIT_CARD",
		84: "SHORTCUT_DEBIT_CARD_SETTINGS",
		85: "TOP_NAV_WEALTH_BUILDER",
		86: "NAV_BAR_US_STOCKS",
		88: "SHORTCUT_WEALTH_BUILDER",
		89: "SHORTCUT_AMB",
		90: "SHORTCUT_CALCULATOR",
	}
	IconType_value = map[string]int32{
		"ICON_TYPE_UNSPECIFIED":                 0,
		"HOME":                                  1,
		"PAY":                                   2,
		"INVEST":                                3,
		"BORROW":                                4,
		"DISCOVER":                              5,
		"DASHBOARD_SETTING":                     6,
		"DASHBOARD_ACTION":                      7,
		"NOTIFICATION":                          8,
		"ANALYSER":                              9,
		"REWARDS":                               10,
		"PROFILE":                               11,
		"CARD":                                  14,
		"CREDIT_CARD":                           58,
		"DEBIT_CARD":                            59,
		"REFER":                                 16,
		"QR_CODE":                               12,
		"SAVINGS_SUMMARY":                       13,
		"CONNECT_ACCOUNT":                       15,
		"SHORTCUT_SALARY_PROGRAM":               17,
		"SHORTCUT_SALARY_BASIC_PLAN":            87,
		"SHORTCUT_JUMP":                         18,
		"SHORTCUT_PRE_APPROVED_LOAN":            19,
		"SHORTCUT_PRE_PAY_LOAN":                 73,
		"SHORTCUT_MF":                           20,
		"SHORTCUT_FD":                           21,
		"SHORTCUT_SD":                           22,
		"SHORTCUT_FITTT":                        23,
		"SHORTCUT_PAY_TO_CONTACT":               24,
		"SHORTCUT_PAY_TO_UPI":                   25,
		"SHORTCUT_PAY_TO_PHONE":                 26,
		"SHORTCUT_BANK_TRANSFER":                27,
		"SHORTCUT_DEBIT_CARD":                   28,
		"SHORTCUT_CREDIT_CARD":                  29,
		"SHORTCUT_CARD_OFFERS":                  30,
		"SHORTCUT_WAYS_TO_EARN":                 31,
		"SHORTCUT_OFFERS_CATALOG":               32,
		"SHORTCUT_OFFERS_ORDERS":                33,
		"SHORTCUT_REFERRALS":                    34,
		"SHORTCUT_SPEND_ANALYSER":               35,
		"SHORTCUT_CREDIT_SCORE_ANALYSER":        36,
		"SHORTCUT_MF_ANALYSER":                  37,
		"SHORTCUT_FI_MINUTES":                   38,
		"SHORTCUT_KYC_INFO":                     39,
		"SHORTCUT_AUTO_INVEST":                  40,
		"SHORTCUT_AUTO_PAY":                     41,
		"SHORTCUT_AUTO_SAVE":                    42,
		"SHORTCUT_MANAGE_RULES":                 43,
		"SHORTCUT_SET_REMINDER":                 44,
		"SHORTCUT_EARLY_SALARY":                 45,
		"SHORTCUT_CONNECTED_ACCOUNT":            46,
		"SHORTCUT_ADD_MONEY":                    47,
		"SHORTCUT_SCAN_AND_PAY":                 48,
		"SHORTCUT_PLUS_PLAN":                    49,
		"SHORTCUT_INFINITE_PLAN":                50,
		"SHORTCUT_NETWORTH":                     51,
		"SHORTCUT_CHEQUEBOOK_REQUEST":           52,
		"SHORTCUT_INTEREST_CERTIFICATE":         53,
		"SHORTCUT_ALL_REQUESTS":                 54,
		"SHORTCUT_SECURED_LOANS":                55,
		"SHORTCUT_US_STOCKS":                    56,
		"SHORTCUT_FI_STORE":                     57,
		"SHORTCUT_FI_STORE_GIFT_CARDS":          61,
		"SHORTCUT_FI_STORE_MILES_EXCHANGE":      72,
		"SHORTCUT_DEBIT_CARD_CONTROLS":          60,
		"SHORTCUT_EXPLORE_FI":                   62,
		"SHORTCUT_BANK_STATEMENT":               63,
		"SAVINGS_ACCOUNT_BENEFITS":              64,
		"SHORTCUT_CREDIT_CARD_CONTROLS":         65,
		"SHORTCUT_CREDIT_CARD_OFFERS":           66,
		"SHORTCUT_DEBIT_CARD_OFFERS":            67,
		"SHORTCUT_SAVINGS_ACCOUNT":              68,
		"SHORTCUT_ZERO_STATE_REWARD":            69,
		"SHORTCUT_AA_SALARY_PLAN":               70,
		"BOTTOM_NAV_BAR_NETWORTH":               71,
		"SHORTCUT_TOP_SPEND_MERCHANTS_ANALYSER": 74,
		"SHORTCUT_SPENDS_BY_TIME_ANALYSER":      75,
		"SHORTCUT_SELF_TRANSFER":                76,
		"SHORTCUT_TRAVEL_BUDGETING":             77,
		"SHORTCUT_TRAVEL_MODE_SWITCH":           78,
		"SHORTCUT_ATM_LOCATOR":                  79,
		"SHORTCUT_MODIFY_DC_INTERNATIONAL_TRANSACTION_LIMIT": 80,
		"SHORTCUT_CURRENCY_EXCHANGE_CALCULATOR":              81,
		"BOTTOM_NAV_WEALTH_BUILDER":                          82,
		"SHORTCUT_ORDER_PHYSICAL_DEBIT_CARD":                 83,
		"SHORTCUT_DEBIT_CARD_SETTINGS":                       84,
		"TOP_NAV_WEALTH_BUILDER":                             85,
		"NAV_BAR_US_STOCKS":                                  86,
		"SHORTCUT_WEALTH_BUILDER":                            88,
		"SHORTCUT_AMB":                                       89,
		"SHORTCUT_CALCULATOR":                                90,
	}
)

func (x IconType) Enum() *IconType {
	p := new(IconType)
	*p = x
	return p
}

func (x IconType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IconType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_home_icon_type_proto_enumTypes[0].Descriptor()
}

func (IconType) Type() protoreflect.EnumType {
	return &file_api_typesv2_home_icon_type_proto_enumTypes[0]
}

func (x IconType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IconType.Descriptor instead.
func (IconType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_home_icon_type_proto_rawDescGZIP(), []int{0}
}

type ExploreSectionType int32

const (
	ExploreSectionType_EXPLORE_SECTION_TYPE_UNSPECIFIED           ExploreSectionType = 0
	ExploreSectionType_EXPLORE_SECTION_TYPE_MUST_TRY              ExploreSectionType = 1
	ExploreSectionType_EXPLORE_SECTION_TYPE_CARDS                 ExploreSectionType = 2
	ExploreSectionType_EXPLORE_SECTION_TYPE_LOANS                 ExploreSectionType = 3
	ExploreSectionType_EXPLORE_SECTION_TYPE_ACCOUNT_PLANS         ExploreSectionType = 4
	ExploreSectionType_EXPLORE_SECTION_TYPE_YOUR_FINANCES         ExploreSectionType = 5
	ExploreSectionType_EXPLORE_SECTION_TYPE_INVEST_MONEY          ExploreSectionType = 6
	ExploreSectionType_EXPLORE_SECTION_TYPE_AUTOMATE_FINANCES     ExploreSectionType = 7
	ExploreSectionType_EXPLORE_SECTION_TYPE_REWARDS_AND_REFERRALS ExploreSectionType = 8
	ExploreSectionType_EXPLORE_SECTION_TYPE_DOCUMENT_REQUESTS     ExploreSectionType = 9
	ExploreSectionType_EXPLORE_SECTION_TYPE_PAYMENTS              ExploreSectionType = 10
	ExploreSectionType_EXPLORE_SECTION_TYPE_CREDIT_CARDS          ExploreSectionType = 11
	ExploreSectionType_EXPLORE_SECTION_TYPE_TRAVEL                ExploreSectionType = 12
	ExploreSectionType_EXPLORE_SECTION_TYPE_INSIGHTS              ExploreSectionType = 13
)

// Enum value maps for ExploreSectionType.
var (
	ExploreSectionType_name = map[int32]string{
		0:  "EXPLORE_SECTION_TYPE_UNSPECIFIED",
		1:  "EXPLORE_SECTION_TYPE_MUST_TRY",
		2:  "EXPLORE_SECTION_TYPE_CARDS",
		3:  "EXPLORE_SECTION_TYPE_LOANS",
		4:  "EXPLORE_SECTION_TYPE_ACCOUNT_PLANS",
		5:  "EXPLORE_SECTION_TYPE_YOUR_FINANCES",
		6:  "EXPLORE_SECTION_TYPE_INVEST_MONEY",
		7:  "EXPLORE_SECTION_TYPE_AUTOMATE_FINANCES",
		8:  "EXPLORE_SECTION_TYPE_REWARDS_AND_REFERRALS",
		9:  "EXPLORE_SECTION_TYPE_DOCUMENT_REQUESTS",
		10: "EXPLORE_SECTION_TYPE_PAYMENTS",
		11: "EXPLORE_SECTION_TYPE_CREDIT_CARDS",
		12: "EXPLORE_SECTION_TYPE_TRAVEL",
		13: "EXPLORE_SECTION_TYPE_INSIGHTS",
	}
	ExploreSectionType_value = map[string]int32{
		"EXPLORE_SECTION_TYPE_UNSPECIFIED":           0,
		"EXPLORE_SECTION_TYPE_MUST_TRY":              1,
		"EXPLORE_SECTION_TYPE_CARDS":                 2,
		"EXPLORE_SECTION_TYPE_LOANS":                 3,
		"EXPLORE_SECTION_TYPE_ACCOUNT_PLANS":         4,
		"EXPLORE_SECTION_TYPE_YOUR_FINANCES":         5,
		"EXPLORE_SECTION_TYPE_INVEST_MONEY":          6,
		"EXPLORE_SECTION_TYPE_AUTOMATE_FINANCES":     7,
		"EXPLORE_SECTION_TYPE_REWARDS_AND_REFERRALS": 8,
		"EXPLORE_SECTION_TYPE_DOCUMENT_REQUESTS":     9,
		"EXPLORE_SECTION_TYPE_PAYMENTS":              10,
		"EXPLORE_SECTION_TYPE_CREDIT_CARDS":          11,
		"EXPLORE_SECTION_TYPE_TRAVEL":                12,
		"EXPLORE_SECTION_TYPE_INSIGHTS":              13,
	}
)

func (x ExploreSectionType) Enum() *ExploreSectionType {
	p := new(ExploreSectionType)
	*p = x
	return p
}

func (x ExploreSectionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExploreSectionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_home_icon_type_proto_enumTypes[1].Descriptor()
}

func (ExploreSectionType) Type() protoreflect.EnumType {
	return &file_api_typesv2_home_icon_type_proto_enumTypes[1]
}

func (x ExploreSectionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExploreSectionType.Descriptor instead.
func (ExploreSectionType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_home_icon_type_proto_rawDescGZIP(), []int{1}
}

var File_api_typesv2_home_icon_type_proto protoreflect.FileDescriptor

var file_api_typesv2_home_icon_type_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x68, 0x6f,
	0x6d, 0x65, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x10, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x68, 0x6f, 0x6d, 0x65, 0x2a, 0xc3, 0x12, 0x0a, 0x08, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04,
	0x48, 0x4f, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x41, 0x59, 0x10, 0x02, 0x12,
	0x0a, 0x0a, 0x06, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x42,
	0x4f, 0x52, 0x52, 0x4f, 0x57, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x49, 0x53, 0x43, 0x4f,
	0x56, 0x45, 0x52, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x06, 0x12, 0x14, 0x0a, 0x10,
	0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x07, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x08, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52,
	0x10, 0x09, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x10, 0x0a, 0x12,
	0x0b, 0x0a, 0x07, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x0b, 0x12, 0x08, 0x0a, 0x04,
	0x43, 0x41, 0x52, 0x44, 0x10, 0x0e, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x3a, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x45, 0x42, 0x49, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x3b, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45, 0x46, 0x45, 0x52,
	0x10, 0x10, 0x12, 0x0b, 0x0a, 0x07, 0x51, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x0c, 0x12,
	0x13, 0x0a, 0x0f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41,
	0x52, 0x59, 0x10, 0x0d, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x0f, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x48, 0x4f,
	0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x41, 0x4d, 0x10, 0x11, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43,
	0x55, 0x54, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x42, 0x41, 0x53, 0x49, 0x43, 0x5f,
	0x50, 0x4c, 0x41, 0x4e, 0x10, 0x57, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43,
	0x55, 0x54, 0x5f, 0x4a, 0x55, 0x4d, 0x50, 0x10, 0x12, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x48, 0x4f,
	0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56,
	0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x13, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x48, 0x4f,
	0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x4c, 0x4f,
	0x41, 0x4e, 0x10, 0x49, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54,
	0x5f, 0x4d, 0x46, 0x10, 0x14, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55,
	0x54, 0x5f, 0x46, 0x44, 0x10, 0x15, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43,
	0x55, 0x54, 0x5f, 0x53, 0x44, 0x10, 0x16, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x48, 0x4f, 0x52, 0x54,
	0x43, 0x55, 0x54, 0x5f, 0x46, 0x49, 0x54, 0x54, 0x54, 0x10, 0x17, 0x12, 0x1b, 0x0a, 0x17, 0x53,
	0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x54, 0x4f, 0x5f, 0x43,
	0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54, 0x10, 0x18, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x48, 0x4f, 0x52,
	0x54, 0x43, 0x55, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x54, 0x4f, 0x5f, 0x55, 0x50, 0x49, 0x10,
	0x19, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x50, 0x41,
	0x59, 0x5f, 0x54, 0x4f, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x1a, 0x12, 0x1a, 0x0a, 0x16,
	0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x10, 0x1b, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x48, 0x4f, 0x52,
	0x54, 0x43, 0x55, 0x54, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10,
	0x1c, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x1d, 0x12, 0x18, 0x0a, 0x14, 0x53,
	0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x53, 0x10, 0x1e, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55,
	0x54, 0x5f, 0x57, 0x41, 0x59, 0x53, 0x5f, 0x54, 0x4f, 0x5f, 0x45, 0x41, 0x52, 0x4e, 0x10, 0x1f,
	0x12, 0x1b, 0x0a, 0x17, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x53, 0x5f, 0x43, 0x41, 0x54, 0x41, 0x4c, 0x4f, 0x47, 0x10, 0x20, 0x12, 0x1a, 0x0a,
	0x16, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53,
	0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x53, 0x10, 0x21, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x48, 0x4f,
	0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x10,
	0x22, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x53, 0x50,
	0x45, 0x4e, 0x44, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x10, 0x23, 0x12, 0x22,
	0x0a, 0x1e, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52,
	0x10, 0x24, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x4d,
	0x46, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x10, 0x25, 0x12, 0x17, 0x0a, 0x13,
	0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x46, 0x49, 0x5f, 0x4d, 0x49, 0x4e, 0x55,
	0x54, 0x45, 0x53, 0x10, 0x26, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55,
	0x54, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x27, 0x12, 0x18, 0x0a, 0x14,
	0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x49, 0x4e,
	0x56, 0x45, 0x53, 0x54, 0x10, 0x28, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43,
	0x55, 0x54, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50, 0x41, 0x59, 0x10, 0x29, 0x12, 0x16, 0x0a,
	0x12, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x53,
	0x41, 0x56, 0x45, 0x10, 0x2a, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55,
	0x54, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x53, 0x10, 0x2b,
	0x12, 0x19, 0x0a, 0x15, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x53, 0x45, 0x54,
	0x5f, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x10, 0x2c, 0x12, 0x19, 0x0a, 0x15, 0x53,
	0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x5f, 0x53, 0x41,
	0x4c, 0x41, 0x52, 0x59, 0x10, 0x2d, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43,
	0x55, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x10, 0x2e, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43,
	0x55, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x10, 0x2f, 0x12, 0x19,
	0x0a, 0x15, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x53, 0x43, 0x41, 0x4e, 0x5f,
	0x41, 0x4e, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x10, 0x30, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x48, 0x4f,
	0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x50, 0x4c, 0x55, 0x53, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10,
	0x31, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x49, 0x4e,
	0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10, 0x32, 0x12, 0x15, 0x0a,
	0x11, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52,
	0x54, 0x48, 0x10, 0x33, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54,
	0x5f, 0x43, 0x48, 0x45, 0x51, 0x55, 0x45, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x10, 0x34, 0x12, 0x21, 0x0a, 0x1d, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55,
	0x54, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x45, 0x52, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x45, 0x10, 0x35, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x48, 0x4f, 0x52,
	0x54, 0x43, 0x55, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x53, 0x10, 0x36, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f,
	0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x10, 0x37, 0x12,
	0x16, 0x0a, 0x12, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x55, 0x53, 0x5f, 0x53,
	0x54, 0x4f, 0x43, 0x4b, 0x53, 0x10, 0x38, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x48, 0x4f, 0x52, 0x54,
	0x43, 0x55, 0x54, 0x5f, 0x46, 0x49, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x10, 0x39, 0x12, 0x20,
	0x0a, 0x1c, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x46, 0x49, 0x5f, 0x53, 0x54,
	0x4f, 0x52, 0x45, 0x5f, 0x47, 0x49, 0x46, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x53, 0x10, 0x3d,
	0x12, 0x24, 0x0a, 0x20, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x46, 0x49, 0x5f,
	0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x4d, 0x49, 0x4c, 0x45, 0x53, 0x5f, 0x45, 0x58, 0x43, 0x48,
	0x41, 0x4e, 0x47, 0x45, 0x10, 0x48, 0x12, 0x20, 0x0a, 0x1c, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43,
	0x55, 0x54, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4f,
	0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x53, 0x10, 0x3c, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x48, 0x4f, 0x52,
	0x54, 0x43, 0x55, 0x54, 0x5f, 0x45, 0x58, 0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x46, 0x49, 0x10,
	0x3e, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x42, 0x41,
	0x4e, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x3f, 0x12, 0x1c,
	0x0a, 0x18, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x10, 0x40, 0x12, 0x21, 0x0a, 0x1d,
	0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x53, 0x10, 0x41, 0x12,
	0x1f, 0x0a, 0x1b, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x10, 0x42,
	0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x44, 0x45, 0x42,
	0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x10, 0x43,
	0x12, 0x1c, 0x0a, 0x18, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x53, 0x41, 0x56,
	0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x44, 0x12, 0x1e,
	0x0a, 0x1a, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x5a, 0x45, 0x52, 0x4f, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x10, 0x45, 0x12, 0x1b,
	0x0a, 0x17, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x41, 0x41, 0x5f, 0x53, 0x41,
	0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10, 0x46, 0x12, 0x1b, 0x0a, 0x17, 0x42,
	0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x4e, 0x41, 0x56, 0x5f, 0x42, 0x41, 0x52, 0x5f, 0x4e, 0x45,
	0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x10, 0x47, 0x12, 0x29, 0x0a, 0x25, 0x53, 0x48, 0x4f, 0x52,
	0x54, 0x43, 0x55, 0x54, 0x5f, 0x54, 0x4f, 0x50, 0x5f, 0x53, 0x50, 0x45, 0x4e, 0x44, 0x5f, 0x4d,
	0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x53, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45,
	0x52, 0x10, 0x4a, 0x12, 0x24, 0x0a, 0x20, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f,
	0x53, 0x50, 0x45, 0x4e, 0x44, 0x53, 0x5f, 0x42, 0x59, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x41,
	0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x10, 0x4b, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x48, 0x4f,
	0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x46, 0x45, 0x52, 0x10, 0x4c, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55,
	0x54, 0x5f, 0x54, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x5f, 0x42, 0x55, 0x44, 0x47, 0x45, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0x4d, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54,
	0x5f, 0x54, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x57, 0x49,
	0x54, 0x43, 0x48, 0x10, 0x4e, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55,
	0x54, 0x5f, 0x41, 0x54, 0x4d, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x4f, 0x52, 0x10, 0x4f, 0x12,
	0x36, 0x0a, 0x32, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x4d, 0x4f, 0x44, 0x49,
	0x46, 0x59, 0x5f, 0x44, 0x43, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x41, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x50, 0x12, 0x29, 0x0a, 0x25, 0x53, 0x48, 0x4f, 0x52, 0x54,
	0x43, 0x55, 0x54, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x45, 0x58, 0x43,
	0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x43, 0x41, 0x4c, 0x43, 0x55, 0x4c, 0x41, 0x54, 0x4f, 0x52,
	0x10, 0x51, 0x12, 0x1d, 0x0a, 0x19, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x4e, 0x41, 0x56,
	0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x42, 0x55, 0x49, 0x4c, 0x44, 0x45, 0x52, 0x10,
	0x52, 0x12, 0x26, 0x0a, 0x22, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x44, 0x45, 0x42,
	0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x53, 0x12, 0x20, 0x0a, 0x1c, 0x53, 0x48, 0x4f,
	0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x53, 0x10, 0x54, 0x12, 0x1a, 0x0a, 0x16, 0x54,
	0x4f, 0x50, 0x5f, 0x4e, 0x41, 0x56, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x42, 0x55,
	0x49, 0x4c, 0x44, 0x45, 0x52, 0x10, 0x55, 0x12, 0x15, 0x0a, 0x11, 0x4e, 0x41, 0x56, 0x5f, 0x42,
	0x41, 0x52, 0x5f, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x10, 0x56, 0x12, 0x1f,
	0x0a, 0x17, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54,
	0x48, 0x5f, 0x42, 0x55, 0x49, 0x4c, 0x44, 0x45, 0x52, 0x10, 0x58, 0x1a, 0x02, 0x08, 0x01, 0x12,
	0x10, 0x0a, 0x0c, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x41, 0x4d, 0x42, 0x10,
	0x59, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x43, 0x55, 0x54, 0x5f, 0x43, 0x41,
	0x4c, 0x43, 0x55, 0x4c, 0x41, 0x54, 0x4f, 0x52, 0x10, 0x5a, 0x2a, 0xaa, 0x04, 0x0a, 0x12, 0x45,
	0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x24, 0x0a, 0x20, 0x45, 0x58, 0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x58, 0x50, 0x4c, 0x4f,
	0x52, 0x45, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x4d, 0x55, 0x53, 0x54, 0x5f, 0x54, 0x52, 0x59, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x58,
	0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x53, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x58,
	0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x10, 0x03, 0x12, 0x26, 0x0a, 0x22, 0x45, 0x58,
	0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x53,
	0x10, 0x04, 0x12, 0x26, 0x0a, 0x22, 0x45, 0x58, 0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x59, 0x4f, 0x55, 0x52, 0x5f,
	0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x53, 0x10, 0x05, 0x12, 0x25, 0x0a, 0x21, 0x45, 0x58,
	0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x10,
	0x06, 0x12, 0x2a, 0x0a, 0x26, 0x45, 0x58, 0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x4d, 0x41,
	0x54, 0x45, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x53, 0x10, 0x07, 0x12, 0x2e, 0x0a,
	0x2a, 0x45, 0x58, 0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x41, 0x4e,
	0x44, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x10, 0x08, 0x12, 0x2a, 0x0a,
	0x26, 0x45, 0x58, 0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x53, 0x10, 0x09, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x58, 0x50,
	0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x0a, 0x12, 0x25, 0x0a, 0x21,
	0x45, 0x58, 0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x53, 0x10, 0x0b, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x58, 0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f, 0x53,
	0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x56,
	0x45, 0x4c, 0x10, 0x0c, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x58, 0x50, 0x4c, 0x4f, 0x52, 0x45, 0x5f,
	0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x53,
	0x49, 0x47, 0x48, 0x54, 0x53, 0x10, 0x0d, 0x42, 0x52, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x68, 0x6f,
	0x6d, 0x65, 0x5a, 0x27, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x68, 0x6f, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_home_icon_type_proto_rawDescOnce sync.Once
	file_api_typesv2_home_icon_type_proto_rawDescData = file_api_typesv2_home_icon_type_proto_rawDesc
)

func file_api_typesv2_home_icon_type_proto_rawDescGZIP() []byte {
	file_api_typesv2_home_icon_type_proto_rawDescOnce.Do(func() {
		file_api_typesv2_home_icon_type_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_home_icon_type_proto_rawDescData)
	})
	return file_api_typesv2_home_icon_type_proto_rawDescData
}

var file_api_typesv2_home_icon_type_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_typesv2_home_icon_type_proto_goTypes = []interface{}{
	(IconType)(0),           // 0: api.typesv2.home.IconType
	(ExploreSectionType)(0), // 1: api.typesv2.home.ExploreSectionType
}
var file_api_typesv2_home_icon_type_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_typesv2_home_icon_type_proto_init() }
func file_api_typesv2_home_icon_type_proto_init() {
	if File_api_typesv2_home_icon_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_home_icon_type_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_home_icon_type_proto_goTypes,
		DependencyIndexes: file_api_typesv2_home_icon_type_proto_depIdxs,
		EnumInfos:         file_api_typesv2_home_icon_type_proto_enumTypes,
	}.Build()
	File_api_typesv2_home_icon_type_proto = out.File
	file_api_typesv2_home_icon_type_proto_rawDesc = nil
	file_api_typesv2_home_icon_type_proto_goTypes = nil
	file_api_typesv2_home_icon_type_proto_depIdxs = nil
}
