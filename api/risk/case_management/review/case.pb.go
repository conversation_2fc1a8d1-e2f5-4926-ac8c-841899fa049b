// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/case_management/review/case.proto

package review

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	common "github.com/epifi/be-common/api/typesv2/common"
	enums "github.com/epifi/gamma/api/risk/case_management/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Rule: Rules allow you to define alert conditions based on signals a.k.a user behavioral data and trigger an alert.
//
// Alert: For any Rule trip an alert will be generated, Alert typically have a score(1-100) or priorities(Low/medium/high) attached to them.
//
// Case:
// - A case represents an entity that requires a risk assessment.
// - Based on thresholds on alert scores or priorities, a case may or may not be created.
// - Although the risk profile of any actor can be viewed, a formal risk assessment should be tied with a case for tracking and audit purposes.
//
// For more details on how an alert is converted to a case, refer case creation workflow section in the design doc:
// https://docs.google.com/document/d/1J58p4C3QEFZCw6fBXJ1_c-vmsium6lotg7BVX2YNODs/edit#bookmark=id.1pfihdcv71el
type Case struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique ID of the case, this will be generated in CRM tool
	Id       string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Priority Priority `protobuf:"varint,2,opt,name=priority,proto3,enum=risk.case_management.review.Priority" json:"priority,omitempty"`
	Status   Status   `protobuf:"varint,3,opt,name=status,proto3,enum=risk.case_management.review.Status" json:"status,omitempty"`
	// email of the analyst to whom the case has been assigned
	AssignedTo string `protobuf:"bytes,4,opt,name=assigned_to,json=assignedTo,proto3" json:"assigned_to,omitempty"`
	// will be set to true if the case was created as part of sampling in rules
	IsSample   common.BooleanEnum     `protobuf:"varint,5,opt,name=is_sample,json=isSample,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_sample,omitempty"`
	ActorId    string                 `protobuf:"bytes,6,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ReviewType ReviewType             `protobuf:"varint,7,opt,name=review_type,json=reviewType,proto3,enum=risk.case_management.review.ReviewType" json:"review_type,omitempty"`
	CreatedAt  *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt  *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// field to indicate what was the final resolution for the case
	Verdict enums.Verdict `protobuf:"varint,10,opt,name=verdict,proto3,enum=risk.case_management.enums.Verdict" json:"verdict,omitempty"`
	// We will be using it to add tags required for assignment like
	// Rule names, Batch names, Queue types etc
	Tags []string `protobuf:"bytes,11,rep,name=tags,proto3" json:"tags,omitempty"`
	// Ticket will be marked on sleep/hold till expiry and will have limited visibility
	// Snooze will also be removed if case status or assigned agent changes
	SnoozedTill *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=snoozed_till,json=snoozedTill,proto3" json:"snoozed_till,omitempty"`
	// Confidence score is a mix of score calculated from rules triggered and user risk score.
	// It can be used to prioritize the case.
	ConfidenceScore float32 `protobuf:"fixed32,13,opt,name=confidence_score,json=confidenceScore,proto3" json:"confidence_score,omitempty"`
	// it defines which user is worked on the ticket previously. It will help in determining who we should assign the
	// ticket once it moved back from out-call or other flow. It typically happens when case is moved around to
	// different agents in its lifecycle.
	LastAssignedAnalystEmail string `protobuf:"bytes,14,opt,name=last_assigned_analyst_email,json=lastAssignedAnalystEmail,proto3" json:"last_assigned_analyst_email,omitempty"`
	// Analyst group to which case is assigned.
	// Group can be used for access control of the case.
	// Case can be assigned to a group where all analysts in the group will have access.
	AnalystGroup AnalystGroup `protobuf:"varint,15,opt,name=analyst_group,json=analystGroup,proto3,enum=risk.case_management.review.AnalystGroup" json:"analyst_group,omitempty"`
}

func (x *Case) Reset() {
	*x = Case{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_case_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Case) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Case) ProtoMessage() {}

func (x *Case) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_case_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Case.ProtoReflect.Descriptor instead.
func (*Case) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_case_proto_rawDescGZIP(), []int{0}
}

func (x *Case) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Case) GetPriority() Priority {
	if x != nil {
		return x.Priority
	}
	return Priority_PRIORITY_UNSPECIFIED
}

func (x *Case) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_STATUS_UNSPECIFIED
}

func (x *Case) GetAssignedTo() string {
	if x != nil {
		return x.AssignedTo
	}
	return ""
}

func (x *Case) GetIsSample() common.BooleanEnum {
	if x != nil {
		return x.IsSample
	}
	return common.BooleanEnum(0)
}

func (x *Case) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *Case) GetReviewType() ReviewType {
	if x != nil {
		return x.ReviewType
	}
	return ReviewType_REVIEW_TYPE_UNSPECIFIED
}

func (x *Case) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Case) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Case) GetVerdict() enums.Verdict {
	if x != nil {
		return x.Verdict
	}
	return enums.Verdict(0)
}

func (x *Case) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Case) GetSnoozedTill() *timestamppb.Timestamp {
	if x != nil {
		return x.SnoozedTill
	}
	return nil
}

func (x *Case) GetConfidenceScore() float32 {
	if x != nil {
		return x.ConfidenceScore
	}
	return 0
}

func (x *Case) GetLastAssignedAnalystEmail() string {
	if x != nil {
		return x.LastAssignedAnalystEmail
	}
	return ""
}

func (x *Case) GetAnalystGroup() AnalystGroup {
	if x != nil {
		return x.AnalystGroup
	}
	return AnalystGroup_ANALYST_GROUP_UNSPECIFIED
}

// CaseFilter will wrap all the filters available while fetching the list of cases
// For repeated field we will apply "OR" conditions between values of same field
// Between different fileds "AND" condition will be applied
// EX: if statuses value is [ASSIGNED, IN_REVIEW] and assigned_to_email is set to "<EMAIL>" the filter condition will be
// (status == ASSIGNED || status == IN_REVIEW) && assigned_to_email = <EMAIL>
type CaseFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter to be used for fetching cases assigned to a particular analyst
	AssignedToEmail string `protobuf:"bytes,1,opt,name=assigned_to_email,json=assignedToEmail,proto3" json:"assigned_to_email,omitempty"`
	// filter to be used to only fetch cases with given list of statuses
	Statuses []Status `protobuf:"varint,2,rep,packed,name=statuses,proto3,enum=risk.case_management.review.Status" json:"statuses,omitempty"`
	// filter to be used to only fetch cases with given list of priorities
	Priorities []Priority `protobuf:"varint,3,rep,packed,name=priorities,proto3,enum=risk.case_management.review.Priority" json:"priorities,omitempty"`
	// filter to be used to only fetch cases with the given list of review types
	ReviewTypes []ReviewType `protobuf:"varint,4,rep,packed,name=review_types,json=reviewTypes,proto3,enum=risk.case_management.review.ReviewType" json:"review_types,omitempty"`
	// True - if response should have only snoozed tickets
	// False - If response should not have snoozed tickets
	// Unspecified - If response should include both
	Snoozed common.BooleanEnum `protobuf:"varint,5,opt,name=snoozed,proto3,enum=api.typesv2.common.BooleanEnum" json:"snoozed,omitempty"`
	// filter to be used to only fetch cases with given list of tags
	Tags []string `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`
}

func (x *CaseFilters) Reset() {
	*x = CaseFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_case_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaseFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseFilters) ProtoMessage() {}

func (x *CaseFilters) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_case_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseFilters.ProtoReflect.Descriptor instead.
func (*CaseFilters) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_case_proto_rawDescGZIP(), []int{1}
}

func (x *CaseFilters) GetAssignedToEmail() string {
	if x != nil {
		return x.AssignedToEmail
	}
	return ""
}

func (x *CaseFilters) GetStatuses() []Status {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *CaseFilters) GetPriorities() []Priority {
	if x != nil {
		return x.Priorities
	}
	return nil
}

func (x *CaseFilters) GetReviewTypes() []ReviewType {
	if x != nil {
		return x.ReviewTypes
	}
	return nil
}

func (x *CaseFilters) GetSnoozed() common.BooleanEnum {
	if x != nil {
		return x.Snoozed
	}
	return common.BooleanEnum(0)
}

func (x *CaseFilters) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

// sort by can be used to sort cases by a field with specific order.
type CaseSortBy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FieldMask CaseFieldMask `protobuf:"varint,1,opt,name=field_mask,json=fieldMask,proto3,enum=risk.case_management.review.CaseFieldMask" json:"field_mask,omitempty"`
	// Deprecated: Marked as deprecated in api/risk/case_management/review/case.proto.
	Order     SortOrder        `protobuf:"varint,2,opt,name=order,proto3,enum=risk.case_management.review.SortOrder" json:"order,omitempty"`
	SortOrder common.SortOrder `protobuf:"varint,3,opt,name=sort_order,json=sortOrder,proto3,enum=api.typesv2.common.SortOrder" json:"sort_order,omitempty"`
}

func (x *CaseSortBy) Reset() {
	*x = CaseSortBy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_case_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaseSortBy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseSortBy) ProtoMessage() {}

func (x *CaseSortBy) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_case_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseSortBy.ProtoReflect.Descriptor instead.
func (*CaseSortBy) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_case_proto_rawDescGZIP(), []int{2}
}

func (x *CaseSortBy) GetFieldMask() CaseFieldMask {
	if x != nil {
		return x.FieldMask
	}
	return CaseFieldMask_CASE_FIELD_MASK_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/risk/case_management/review/case.proto.
func (x *CaseSortBy) GetOrder() SortOrder {
	if x != nil {
		return x.Order
	}
	return SortOrder_SORT_ORDER_UNSPECIFIED
}

func (x *CaseSortBy) GetSortOrder() common.SortOrder {
	if x != nil {
		return x.SortOrder
	}
	return common.SortOrder(0)
}

// We can have dedupe logic on different fields for case and alert based on the strategy used
// EX: We can choose to dedupe all alert at an actor level(or account level) and create a single case for all the alerts against a user
// Or we can group the alerts by user and type of review required etc
// DedupeParams message will wrap different possible group of dedupe fields to be used in different fields
type DedupeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to DedupeIdentifier:
	//
	//	*DedupeParams_ActorIdAccountIdReviewType_
	//	*DedupeParams_ActorIdReviewType_
	DedupeIdentifier isDedupeParams_DedupeIdentifier `protobuf_oneof:"dedupe_identifier"`
}

func (x *DedupeParams) Reset() {
	*x = DedupeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_case_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DedupeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DedupeParams) ProtoMessage() {}

func (x *DedupeParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_case_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DedupeParams.ProtoReflect.Descriptor instead.
func (*DedupeParams) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_case_proto_rawDescGZIP(), []int{3}
}

func (m *DedupeParams) GetDedupeIdentifier() isDedupeParams_DedupeIdentifier {
	if m != nil {
		return m.DedupeIdentifier
	}
	return nil
}

func (x *DedupeParams) GetActorIdAccountIdReviewType() *DedupeParams_ActorIdAccountIdReviewType {
	if x, ok := x.GetDedupeIdentifier().(*DedupeParams_ActorIdAccountIdReviewType_); ok {
		return x.ActorIdAccountIdReviewType
	}
	return nil
}

func (x *DedupeParams) GetActorIdReviewType() *DedupeParams_ActorIdReviewType {
	if x, ok := x.GetDedupeIdentifier().(*DedupeParams_ActorIdReviewType_); ok {
		return x.ActorIdReviewType
	}
	return nil
}

type isDedupeParams_DedupeIdentifier interface {
	isDedupeParams_DedupeIdentifier()
}

type DedupeParams_ActorIdAccountIdReviewType_ struct {
	// Dedupe will be done at actor, account and review type level
	ActorIdAccountIdReviewType *DedupeParams_ActorIdAccountIdReviewType `protobuf:"bytes,1,opt,name=actor_id_account_id_review_type,json=actorIdAccountIdReviewType,proto3,oneof"`
}

type DedupeParams_ActorIdReviewType_ struct {
	// Dedupe will be done at actor and review type level
	ActorIdReviewType *DedupeParams_ActorIdReviewType `protobuf:"bytes,2,opt,name=actor_id_review_type,json=actorIdReviewType,proto3,oneof"`
}

func (*DedupeParams_ActorIdAccountIdReviewType_) isDedupeParams_DedupeIdentifier() {}

func (*DedupeParams_ActorIdReviewType_) isDedupeParams_DedupeIdentifier() {}

type SortableCaseFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *SortableCaseFilters) Reset() {
	*x = SortableCaseFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_case_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortableCaseFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortableCaseFilters) ProtoMessage() {}

func (x *SortableCaseFilters) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_case_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortableCaseFilters.ProtoReflect.Descriptor instead.
func (*SortableCaseFilters) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_case_proto_rawDescGZIP(), []int{4}
}

func (x *SortableCaseFilters) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type CaseReviewLevel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsActive bool `protobuf:"varint,1,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	// indicates what review type should be taken against a case
	CaseReviewLevel enums.CaseReviewLevel `protobuf:"varint,2,opt,name=case_review_level,json=caseReviewLevel,proto3,enum=risk.case_management.enums.CaseReviewLevel" json:"case_review_level,omitempty"`
}

func (x *CaseReviewLevel) Reset() {
	*x = CaseReviewLevel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_case_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaseReviewLevel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseReviewLevel) ProtoMessage() {}

func (x *CaseReviewLevel) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_case_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseReviewLevel.ProtoReflect.Descriptor instead.
func (*CaseReviewLevel) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_case_proto_rawDescGZIP(), []int{5}
}

func (x *CaseReviewLevel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *CaseReviewLevel) GetCaseReviewLevel() enums.CaseReviewLevel {
	if x != nil {
		return x.CaseReviewLevel
	}
	return enums.CaseReviewLevel(0)
}

// Optional fields to create case
type CaseBuilderOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// will be set to true if the case was created as part of sampling in rules
	IsSample bool `protobuf:"varint,1,opt,name=is_sample,json=isSample,proto3" json:"is_sample,omitempty"`
	// field indicating whether auto action will be done for the given case
	IsAutoAction bool `protobuf:"varint,2,opt,name=is_auto_action,json=isAutoAction,proto3" json:"is_auto_action,omitempty"`
	// review level for case
	CaseReviewLevel *CaseReviewLevel `protobuf:"bytes,3,opt,name=case_review_level,json=caseReviewLevel,proto3" json:"case_review_level,omitempty"`
}

func (x *CaseBuilderOptions) Reset() {
	*x = CaseBuilderOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_case_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaseBuilderOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseBuilderOptions) ProtoMessage() {}

func (x *CaseBuilderOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_case_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseBuilderOptions.ProtoReflect.Descriptor instead.
func (*CaseBuilderOptions) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_case_proto_rawDescGZIP(), []int{6}
}

func (x *CaseBuilderOptions) GetIsSample() bool {
	if x != nil {
		return x.IsSample
	}
	return false
}

func (x *CaseBuilderOptions) GetIsAutoAction() bool {
	if x != nil {
		return x.IsAutoAction
	}
	return false
}

func (x *CaseBuilderOptions) GetCaseReviewLevel() *CaseReviewLevel {
	if x != nil {
		return x.CaseReviewLevel
	}
	return nil
}

// optional fields to get case
type GetCaseOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Option:
	//
	//	*GetCaseOption_DataFreshness
	Option isGetCaseOption_Option `protobuf_oneof:"option"`
}

func (x *GetCaseOption) Reset() {
	*x = GetCaseOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_case_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCaseOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCaseOption) ProtoMessage() {}

func (x *GetCaseOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_case_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCaseOption.ProtoReflect.Descriptor instead.
func (*GetCaseOption) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_case_proto_rawDescGZIP(), []int{7}
}

func (m *GetCaseOption) GetOption() isGetCaseOption_Option {
	if m != nil {
		return m.Option
	}
	return nil
}

func (x *GetCaseOption) GetDataFreshness() enums.DataFreshness {
	if x, ok := x.GetOption().(*GetCaseOption_DataFreshness); ok {
		return x.DataFreshness
	}
	return enums.DataFreshness(0)
}

type isGetCaseOption_Option interface {
	isGetCaseOption_Option()
}

type GetCaseOption_DataFreshness struct {
	// acceptable freshness of the case in response
	DataFreshness enums.DataFreshness `protobuf:"varint,1,opt,name=data_freshness,json=dataFreshness,proto3,enum=risk.case_management.enums.DataFreshness,oneof"`
}

func (*GetCaseOption_DataFreshness) isGetCaseOption_Option() {}

type DedupeParams_ActorIdAccountIdReviewType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId    string     `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	AccountId  string     `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	ReviewType ReviewType `protobuf:"varint,3,opt,name=review_type,json=reviewType,proto3,enum=risk.case_management.review.ReviewType" json:"review_type,omitempty"`
}

func (x *DedupeParams_ActorIdAccountIdReviewType) Reset() {
	*x = DedupeParams_ActorIdAccountIdReviewType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_case_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DedupeParams_ActorIdAccountIdReviewType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DedupeParams_ActorIdAccountIdReviewType) ProtoMessage() {}

func (x *DedupeParams_ActorIdAccountIdReviewType) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_case_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DedupeParams_ActorIdAccountIdReviewType.ProtoReflect.Descriptor instead.
func (*DedupeParams_ActorIdAccountIdReviewType) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_case_proto_rawDescGZIP(), []int{3, 0}
}

func (x *DedupeParams_ActorIdAccountIdReviewType) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DedupeParams_ActorIdAccountIdReviewType) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *DedupeParams_ActorIdAccountIdReviewType) GetReviewType() ReviewType {
	if x != nil {
		return x.ReviewType
	}
	return ReviewType_REVIEW_TYPE_UNSPECIFIED
}

type DedupeParams_ActorIdReviewType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId    string     `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ReviewType ReviewType `protobuf:"varint,2,opt,name=review_type,json=reviewType,proto3,enum=risk.case_management.review.ReviewType" json:"review_type,omitempty"`
}

func (x *DedupeParams_ActorIdReviewType) Reset() {
	*x = DedupeParams_ActorIdReviewType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_review_case_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DedupeParams_ActorIdReviewType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DedupeParams_ActorIdReviewType) ProtoMessage() {}

func (x *DedupeParams_ActorIdReviewType) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_review_case_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DedupeParams_ActorIdReviewType.ProtoReflect.Descriptor instead.
func (*DedupeParams_ActorIdReviewType) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_review_case_proto_rawDescGZIP(), []int{3, 1}
}

func (x *DedupeParams_ActorIdReviewType) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DedupeParams_ActorIdReviewType) GetReviewType() ReviewType {
	if x != nil {
		return x.ReviewType
	}
	return ReviewType_REVIEW_TYPE_UNSPECIFIED
}

var File_api_risk_case_management_review_case_proto protoreflect.FileDescriptor

var file_api_risk_case_management_review_case_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x73, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9c, 0x06,
	0x0a, 0x04, 0x43, 0x61, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x41, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x52,
	0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x3b, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x5f, 0x74, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x6f, 0x12, 0x3c, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x73, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x69, 0x73, 0x53,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x48, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x3d, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x23, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x56,
	0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x52, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x61, 0x67, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6c, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x64, 0x54, 0x69,
	0x6c, 0x6c, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x3d, 0x0a,
	0x1b, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x18, 0x6c, 0x61, 0x73, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x4e, 0x0a, 0x0d,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0c,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0xdc, 0x02, 0x0a,
	0x0b, 0x43, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x2a, 0x0a, 0x11,
	0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x54, 0x6f, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x3f, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12, 0x45, 0x0a, 0x0a, 0x70, 0x72, 0x69,
	0x6f, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x25, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x50, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x79, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x12, 0x4a, 0x0a, 0x0c, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x07,
	0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x07,
	0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x22, 0xeb, 0x01, 0x0a, 0x0a,
	0x43, 0x61, 0x73, 0x65, 0x53, 0x6f, 0x72, 0x74, 0x42, 0x79, 0x12, 0x53, 0x0a, 0x0a, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x61, 0x73,
	0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x20, 0x00, 0x52, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12,
	0x40, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x53, 0x6f, 0x72,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x46, 0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x09,
	0x73, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22, 0xbe, 0x04, 0x0a, 0x0c, 0x44, 0x65,
	0x64, 0x75, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x8b, 0x01, 0x0a, 0x1f, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x2e, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x1a, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x6e, 0x0a, 0x14, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54,
	0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x11, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x1a, 0xa0, 0x01, 0x0a, 0x1a, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x48, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x78, 0x0a, 0x11, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x0b, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x54, 0x79, 0x70, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x5f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x30, 0x0a, 0x13, 0x53, 0x6f,
	0x72, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x87, 0x01, 0x0a,
	0x0f, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x57, 0x0a,
	0x11, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x0f, 0x63, 0x61, 0x73, 0x65, 0x52, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0xb1, 0x01, 0x0a, 0x12, 0x43, 0x61, 0x73, 0x65, 0x42,
	0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x73, 0x5f, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73,
	0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x58, 0x0a, 0x11, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x0f, 0x63, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x6d, 0x0a, 0x0d, 0x47, 0x65,
	0x74, 0x43, 0x61, 0x73, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x52, 0x0a, 0x0e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x66, 0x72, 0x65, 0x73, 0x68, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x72, 0x65, 0x73, 0x68, 0x6e, 0x65, 0x73, 0x73, 0x48, 0x00,
	0x52, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x46, 0x72, 0x65, 0x73, 0x68, 0x6e, 0x65, 0x73, 0x73, 0x42,
	0x08, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x70, 0x0a, 0x36, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_risk_case_management_review_case_proto_rawDescOnce sync.Once
	file_api_risk_case_management_review_case_proto_rawDescData = file_api_risk_case_management_review_case_proto_rawDesc
)

func file_api_risk_case_management_review_case_proto_rawDescGZIP() []byte {
	file_api_risk_case_management_review_case_proto_rawDescOnce.Do(func() {
		file_api_risk_case_management_review_case_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_case_management_review_case_proto_rawDescData)
	})
	return file_api_risk_case_management_review_case_proto_rawDescData
}

var file_api_risk_case_management_review_case_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_risk_case_management_review_case_proto_goTypes = []interface{}{
	(*Case)(nil),                                    // 0: risk.case_management.review.Case
	(*CaseFilters)(nil),                             // 1: risk.case_management.review.CaseFilters
	(*CaseSortBy)(nil),                              // 2: risk.case_management.review.CaseSortBy
	(*DedupeParams)(nil),                            // 3: risk.case_management.review.DedupeParams
	(*SortableCaseFilters)(nil),                     // 4: risk.case_management.review.SortableCaseFilters
	(*CaseReviewLevel)(nil),                         // 5: risk.case_management.review.CaseReviewLevel
	(*CaseBuilderOptions)(nil),                      // 6: risk.case_management.review.CaseBuilderOptions
	(*GetCaseOption)(nil),                           // 7: risk.case_management.review.GetCaseOption
	(*DedupeParams_ActorIdAccountIdReviewType)(nil), // 8: risk.case_management.review.DedupeParams.ActorIdAccountIdReviewType
	(*DedupeParams_ActorIdReviewType)(nil),          // 9: risk.case_management.review.DedupeParams.ActorIdReviewType
	(Priority)(0),                                   // 10: risk.case_management.review.Priority
	(Status)(0),                                     // 11: risk.case_management.review.Status
	(common.BooleanEnum)(0),                         // 12: api.typesv2.common.BooleanEnum
	(ReviewType)(0),                                 // 13: risk.case_management.review.ReviewType
	(*timestamppb.Timestamp)(nil),                   // 14: google.protobuf.Timestamp
	(enums.Verdict)(0),                              // 15: risk.case_management.enums.Verdict
	(AnalystGroup)(0),                               // 16: risk.case_management.review.AnalystGroup
	(CaseFieldMask)(0),                              // 17: risk.case_management.review.CaseFieldMask
	(SortOrder)(0),                                  // 18: risk.case_management.review.SortOrder
	(common.SortOrder)(0),                           // 19: api.typesv2.common.SortOrder
	(enums.CaseReviewLevel)(0),                      // 20: risk.case_management.enums.CaseReviewLevel
	(enums.DataFreshness)(0),                        // 21: risk.case_management.enums.DataFreshness
}
var file_api_risk_case_management_review_case_proto_depIdxs = []int32{
	10, // 0: risk.case_management.review.Case.priority:type_name -> risk.case_management.review.Priority
	11, // 1: risk.case_management.review.Case.status:type_name -> risk.case_management.review.Status
	12, // 2: risk.case_management.review.Case.is_sample:type_name -> api.typesv2.common.BooleanEnum
	13, // 3: risk.case_management.review.Case.review_type:type_name -> risk.case_management.review.ReviewType
	14, // 4: risk.case_management.review.Case.created_at:type_name -> google.protobuf.Timestamp
	14, // 5: risk.case_management.review.Case.updated_at:type_name -> google.protobuf.Timestamp
	15, // 6: risk.case_management.review.Case.verdict:type_name -> risk.case_management.enums.Verdict
	14, // 7: risk.case_management.review.Case.snoozed_till:type_name -> google.protobuf.Timestamp
	16, // 8: risk.case_management.review.Case.analyst_group:type_name -> risk.case_management.review.AnalystGroup
	11, // 9: risk.case_management.review.CaseFilters.statuses:type_name -> risk.case_management.review.Status
	10, // 10: risk.case_management.review.CaseFilters.priorities:type_name -> risk.case_management.review.Priority
	13, // 11: risk.case_management.review.CaseFilters.review_types:type_name -> risk.case_management.review.ReviewType
	12, // 12: risk.case_management.review.CaseFilters.snoozed:type_name -> api.typesv2.common.BooleanEnum
	17, // 13: risk.case_management.review.CaseSortBy.field_mask:type_name -> risk.case_management.review.CaseFieldMask
	18, // 14: risk.case_management.review.CaseSortBy.order:type_name -> risk.case_management.review.SortOrder
	19, // 15: risk.case_management.review.CaseSortBy.sort_order:type_name -> api.typesv2.common.SortOrder
	8,  // 16: risk.case_management.review.DedupeParams.actor_id_account_id_review_type:type_name -> risk.case_management.review.DedupeParams.ActorIdAccountIdReviewType
	9,  // 17: risk.case_management.review.DedupeParams.actor_id_review_type:type_name -> risk.case_management.review.DedupeParams.ActorIdReviewType
	20, // 18: risk.case_management.review.CaseReviewLevel.case_review_level:type_name -> risk.case_management.enums.CaseReviewLevel
	5,  // 19: risk.case_management.review.CaseBuilderOptions.case_review_level:type_name -> risk.case_management.review.CaseReviewLevel
	21, // 20: risk.case_management.review.GetCaseOption.data_freshness:type_name -> risk.case_management.enums.DataFreshness
	13, // 21: risk.case_management.review.DedupeParams.ActorIdAccountIdReviewType.review_type:type_name -> risk.case_management.review.ReviewType
	13, // 22: risk.case_management.review.DedupeParams.ActorIdReviewType.review_type:type_name -> risk.case_management.review.ReviewType
	23, // [23:23] is the sub-list for method output_type
	23, // [23:23] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_api_risk_case_management_review_case_proto_init() }
func file_api_risk_case_management_review_case_proto_init() {
	if File_api_risk_case_management_review_case_proto != nil {
		return
	}
	file_api_risk_case_management_review_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_case_management_review_case_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Case); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_case_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaseFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_case_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaseSortBy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_case_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DedupeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_case_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortableCaseFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_case_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaseReviewLevel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_case_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaseBuilderOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_case_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCaseOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_case_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DedupeParams_ActorIdAccountIdReviewType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_review_case_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DedupeParams_ActorIdReviewType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_case_management_review_case_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*DedupeParams_ActorIdAccountIdReviewType_)(nil),
		(*DedupeParams_ActorIdReviewType_)(nil),
	}
	file_api_risk_case_management_review_case_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*GetCaseOption_DataFreshness)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_case_management_review_case_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_case_management_review_case_proto_goTypes,
		DependencyIndexes: file_api_risk_case_management_review_case_proto_depIdxs,
		MessageInfos:      file_api_risk_case_management_review_case_proto_msgTypes,
	}.Build()
	File_api_risk_case_management_review_case_proto = out.File
	file_api_risk_case_management_review_case_proto_rawDesc = nil
	file_api_risk_case_management_review_case_proto_goTypes = nil
	file_api_risk_case_management_review_case_proto_depIdxs = nil
}
