// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/case_management/review/case.proto

package review

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	enums "github.com/epifi/gamma/api/risk/case_management/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = enums.Verdict(0)
)

// Validate checks the field values on Case with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Case) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Case with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CaseMultiError, or nil if none found.
func (m *Case) ValidateAll() error {
	return m.validate(true)
}

func (m *Case) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Priority

	// no validation rules for Status

	// no validation rules for AssignedTo

	// no validation rules for IsSample

	// no validation rules for ActorId

	// no validation rules for ReviewType

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaseValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaseValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaseValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaseValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Verdict

	if all {
		switch v := interface{}(m.GetSnoozedTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaseValidationError{
					field:  "SnoozedTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaseValidationError{
					field:  "SnoozedTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSnoozedTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaseValidationError{
				field:  "SnoozedTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ConfidenceScore

	// no validation rules for LastAssignedAnalystEmail

	// no validation rules for AnalystGroup

	if len(errors) > 0 {
		return CaseMultiError(errors)
	}

	return nil
}

// CaseMultiError is an error wrapping multiple validation errors returned by
// Case.ValidateAll() if the designated constraints aren't met.
type CaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseMultiError) AllErrors() []error { return m }

// CaseValidationError is the validation error returned by Case.Validate if the
// designated constraints aren't met.
type CaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseValidationError) ErrorName() string { return "CaseValidationError" }

// Error satisfies the builtin error interface
func (e CaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCase.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseValidationError{}

// Validate checks the field values on CaseFilters with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CaseFilters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaseFilters with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CaseFiltersMultiError, or
// nil if none found.
func (m *CaseFilters) ValidateAll() error {
	return m.validate(true)
}

func (m *CaseFilters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AssignedToEmail

	// no validation rules for Snoozed

	if len(errors) > 0 {
		return CaseFiltersMultiError(errors)
	}

	return nil
}

// CaseFiltersMultiError is an error wrapping multiple validation errors
// returned by CaseFilters.ValidateAll() if the designated constraints aren't met.
type CaseFiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseFiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseFiltersMultiError) AllErrors() []error { return m }

// CaseFiltersValidationError is the validation error returned by
// CaseFilters.Validate if the designated constraints aren't met.
type CaseFiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseFiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseFiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseFiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseFiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseFiltersValidationError) ErrorName() string { return "CaseFiltersValidationError" }

// Error satisfies the builtin error interface
func (e CaseFiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaseFilters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseFiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseFiltersValidationError{}

// Validate checks the field values on CaseSortBy with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CaseSortBy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaseSortBy with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CaseSortByMultiError, or
// nil if none found.
func (m *CaseSortBy) ValidateAll() error {
	return m.validate(true)
}

func (m *CaseSortBy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _CaseSortBy_FieldMask_NotInLookup[m.GetFieldMask()]; ok {
		err := CaseSortByValidationError{
			field:  "FieldMask",
			reason: "value must not be in list [CASE_FIELD_MASK_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Order

	if _, ok := _CaseSortBy_SortOrder_NotInLookup[m.GetSortOrder()]; ok {
		err := CaseSortByValidationError{
			field:  "SortOrder",
			reason: "value must not be in list [SORT_ORDER_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CaseSortByMultiError(errors)
	}

	return nil
}

// CaseSortByMultiError is an error wrapping multiple validation errors
// returned by CaseSortBy.ValidateAll() if the designated constraints aren't met.
type CaseSortByMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseSortByMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseSortByMultiError) AllErrors() []error { return m }

// CaseSortByValidationError is the validation error returned by
// CaseSortBy.Validate if the designated constraints aren't met.
type CaseSortByValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseSortByValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseSortByValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseSortByValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseSortByValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseSortByValidationError) ErrorName() string { return "CaseSortByValidationError" }

// Error satisfies the builtin error interface
func (e CaseSortByValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaseSortBy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseSortByValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseSortByValidationError{}

var _CaseSortBy_FieldMask_NotInLookup = map[CaseFieldMask]struct{}{
	0: {},
}

var _CaseSortBy_SortOrder_NotInLookup = map[common.SortOrder]struct{}{
	0: {},
}

// Validate checks the field values on DedupeParams with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DedupeParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DedupeParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DedupeParamsMultiError, or
// nil if none found.
func (m *DedupeParams) ValidateAll() error {
	return m.validate(true)
}

func (m *DedupeParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.DedupeIdentifier.(type) {
	case *DedupeParams_ActorIdAccountIdReviewType_:
		if v == nil {
			err := DedupeParamsValidationError{
				field:  "DedupeIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetActorIdAccountIdReviewType()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DedupeParamsValidationError{
						field:  "ActorIdAccountIdReviewType",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DedupeParamsValidationError{
						field:  "ActorIdAccountIdReviewType",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetActorIdAccountIdReviewType()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DedupeParamsValidationError{
					field:  "ActorIdAccountIdReviewType",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *DedupeParams_ActorIdReviewType_:
		if v == nil {
			err := DedupeParamsValidationError{
				field:  "DedupeIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetActorIdReviewType()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DedupeParamsValidationError{
						field:  "ActorIdReviewType",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DedupeParamsValidationError{
						field:  "ActorIdReviewType",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetActorIdReviewType()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DedupeParamsValidationError{
					field:  "ActorIdReviewType",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return DedupeParamsMultiError(errors)
	}

	return nil
}

// DedupeParamsMultiError is an error wrapping multiple validation errors
// returned by DedupeParams.ValidateAll() if the designated constraints aren't met.
type DedupeParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DedupeParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DedupeParamsMultiError) AllErrors() []error { return m }

// DedupeParamsValidationError is the validation error returned by
// DedupeParams.Validate if the designated constraints aren't met.
type DedupeParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DedupeParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DedupeParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DedupeParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DedupeParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DedupeParamsValidationError) ErrorName() string { return "DedupeParamsValidationError" }

// Error satisfies the builtin error interface
func (e DedupeParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDedupeParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DedupeParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DedupeParamsValidationError{}

// Validate checks the field values on SortableCaseFilters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SortableCaseFilters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SortableCaseFilters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SortableCaseFiltersMultiError, or nil if none found.
func (m *SortableCaseFilters) ValidateAll() error {
	return m.validate(true)
}

func (m *SortableCaseFilters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return SortableCaseFiltersMultiError(errors)
	}

	return nil
}

// SortableCaseFiltersMultiError is an error wrapping multiple validation
// errors returned by SortableCaseFilters.ValidateAll() if the designated
// constraints aren't met.
type SortableCaseFiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SortableCaseFiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SortableCaseFiltersMultiError) AllErrors() []error { return m }

// SortableCaseFiltersValidationError is the validation error returned by
// SortableCaseFilters.Validate if the designated constraints aren't met.
type SortableCaseFiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SortableCaseFiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SortableCaseFiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SortableCaseFiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SortableCaseFiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SortableCaseFiltersValidationError) ErrorName() string {
	return "SortableCaseFiltersValidationError"
}

// Error satisfies the builtin error interface
func (e SortableCaseFiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSortableCaseFilters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SortableCaseFiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SortableCaseFiltersValidationError{}

// Validate checks the field values on CaseReviewLevel with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CaseReviewLevel) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaseReviewLevel with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CaseReviewLevelMultiError, or nil if none found.
func (m *CaseReviewLevel) ValidateAll() error {
	return m.validate(true)
}

func (m *CaseReviewLevel) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsActive

	// no validation rules for CaseReviewLevel

	if len(errors) > 0 {
		return CaseReviewLevelMultiError(errors)
	}

	return nil
}

// CaseReviewLevelMultiError is an error wrapping multiple validation errors
// returned by CaseReviewLevel.ValidateAll() if the designated constraints
// aren't met.
type CaseReviewLevelMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseReviewLevelMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseReviewLevelMultiError) AllErrors() []error { return m }

// CaseReviewLevelValidationError is the validation error returned by
// CaseReviewLevel.Validate if the designated constraints aren't met.
type CaseReviewLevelValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseReviewLevelValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseReviewLevelValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseReviewLevelValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseReviewLevelValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseReviewLevelValidationError) ErrorName() string { return "CaseReviewLevelValidationError" }

// Error satisfies the builtin error interface
func (e CaseReviewLevelValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaseReviewLevel.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseReviewLevelValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseReviewLevelValidationError{}

// Validate checks the field values on CaseBuilderOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CaseBuilderOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaseBuilderOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CaseBuilderOptionsMultiError, or nil if none found.
func (m *CaseBuilderOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CaseBuilderOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsSample

	// no validation rules for IsAutoAction

	if all {
		switch v := interface{}(m.GetCaseReviewLevel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaseBuilderOptionsValidationError{
					field:  "CaseReviewLevel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaseBuilderOptionsValidationError{
					field:  "CaseReviewLevel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCaseReviewLevel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaseBuilderOptionsValidationError{
				field:  "CaseReviewLevel",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CaseBuilderOptionsMultiError(errors)
	}

	return nil
}

// CaseBuilderOptionsMultiError is an error wrapping multiple validation errors
// returned by CaseBuilderOptions.ValidateAll() if the designated constraints
// aren't met.
type CaseBuilderOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaseBuilderOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaseBuilderOptionsMultiError) AllErrors() []error { return m }

// CaseBuilderOptionsValidationError is the validation error returned by
// CaseBuilderOptions.Validate if the designated constraints aren't met.
type CaseBuilderOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaseBuilderOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaseBuilderOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaseBuilderOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaseBuilderOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaseBuilderOptionsValidationError) ErrorName() string {
	return "CaseBuilderOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CaseBuilderOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaseBuilderOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaseBuilderOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaseBuilderOptionsValidationError{}

// Validate checks the field values on GetCaseOption with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetCaseOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCaseOption with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetCaseOptionMultiError, or
// nil if none found.
func (m *GetCaseOption) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCaseOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Option.(type) {
	case *GetCaseOption_DataFreshness:
		if v == nil {
			err := GetCaseOptionValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for DataFreshness
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetCaseOptionMultiError(errors)
	}

	return nil
}

// GetCaseOptionMultiError is an error wrapping multiple validation errors
// returned by GetCaseOption.ValidateAll() if the designated constraints
// aren't met.
type GetCaseOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCaseOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCaseOptionMultiError) AllErrors() []error { return m }

// GetCaseOptionValidationError is the validation error returned by
// GetCaseOption.Validate if the designated constraints aren't met.
type GetCaseOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCaseOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCaseOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCaseOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCaseOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCaseOptionValidationError) ErrorName() string { return "GetCaseOptionValidationError" }

// Error satisfies the builtin error interface
func (e GetCaseOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCaseOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCaseOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCaseOptionValidationError{}

// Validate checks the field values on DedupeParams_ActorIdAccountIdReviewType
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DedupeParams_ActorIdAccountIdReviewType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DedupeParams_ActorIdAccountIdReviewType with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// DedupeParams_ActorIdAccountIdReviewTypeMultiError, or nil if none found.
func (m *DedupeParams_ActorIdAccountIdReviewType) ValidateAll() error {
	return m.validate(true)
}

func (m *DedupeParams_ActorIdAccountIdReviewType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AccountId

	// no validation rules for ReviewType

	if len(errors) > 0 {
		return DedupeParams_ActorIdAccountIdReviewTypeMultiError(errors)
	}

	return nil
}

// DedupeParams_ActorIdAccountIdReviewTypeMultiError is an error wrapping
// multiple validation errors returned by
// DedupeParams_ActorIdAccountIdReviewType.ValidateAll() if the designated
// constraints aren't met.
type DedupeParams_ActorIdAccountIdReviewTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DedupeParams_ActorIdAccountIdReviewTypeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DedupeParams_ActorIdAccountIdReviewTypeMultiError) AllErrors() []error { return m }

// DedupeParams_ActorIdAccountIdReviewTypeValidationError is the validation
// error returned by DedupeParams_ActorIdAccountIdReviewType.Validate if the
// designated constraints aren't met.
type DedupeParams_ActorIdAccountIdReviewTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DedupeParams_ActorIdAccountIdReviewTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DedupeParams_ActorIdAccountIdReviewTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DedupeParams_ActorIdAccountIdReviewTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DedupeParams_ActorIdAccountIdReviewTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DedupeParams_ActorIdAccountIdReviewTypeValidationError) ErrorName() string {
	return "DedupeParams_ActorIdAccountIdReviewTypeValidationError"
}

// Error satisfies the builtin error interface
func (e DedupeParams_ActorIdAccountIdReviewTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDedupeParams_ActorIdAccountIdReviewType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DedupeParams_ActorIdAccountIdReviewTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DedupeParams_ActorIdAccountIdReviewTypeValidationError{}

// Validate checks the field values on DedupeParams_ActorIdReviewType with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DedupeParams_ActorIdReviewType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DedupeParams_ActorIdReviewType with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DedupeParams_ActorIdReviewTypeMultiError, or nil if none found.
func (m *DedupeParams_ActorIdReviewType) ValidateAll() error {
	return m.validate(true)
}

func (m *DedupeParams_ActorIdReviewType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ReviewType

	if len(errors) > 0 {
		return DedupeParams_ActorIdReviewTypeMultiError(errors)
	}

	return nil
}

// DedupeParams_ActorIdReviewTypeMultiError is an error wrapping multiple
// validation errors returned by DedupeParams_ActorIdReviewType.ValidateAll()
// if the designated constraints aren't met.
type DedupeParams_ActorIdReviewTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DedupeParams_ActorIdReviewTypeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DedupeParams_ActorIdReviewTypeMultiError) AllErrors() []error { return m }

// DedupeParams_ActorIdReviewTypeValidationError is the validation error
// returned by DedupeParams_ActorIdReviewType.Validate if the designated
// constraints aren't met.
type DedupeParams_ActorIdReviewTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DedupeParams_ActorIdReviewTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DedupeParams_ActorIdReviewTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DedupeParams_ActorIdReviewTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DedupeParams_ActorIdReviewTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DedupeParams_ActorIdReviewTypeValidationError) ErrorName() string {
	return "DedupeParams_ActorIdReviewTypeValidationError"
}

// Error satisfies the builtin error interface
func (e DedupeParams_ActorIdReviewTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDedupeParams_ActorIdReviewType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DedupeParams_ActorIdReviewTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DedupeParams_ActorIdReviewTypeValidationError{}
