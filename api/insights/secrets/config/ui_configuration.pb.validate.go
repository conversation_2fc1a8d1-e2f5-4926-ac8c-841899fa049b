// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/insights/secrets/config/ui_configuration.proto

package config

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.FontStyle(0)
)

// Validate checks the field values on SecretSummaryCardConfiguration with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecretSummaryCardConfiguration) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecretSummaryCardConfiguration with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SecretSummaryCardConfigurationMultiError, or nil if none found.
func (m *SecretSummaryCardConfiguration) ValidateAll() error {
	return m.validate(true)
}

func (m *SecretSummaryCardConfiguration) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretSummaryCardConfigurationValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretSummaryCardConfigurationValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretSummaryCardConfigurationValidationError{
				field:  "TitleConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValueConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretSummaryCardConfigurationValidationError{
					field:  "ValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretSummaryCardConfigurationValidationError{
					field:  "ValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValueConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretSummaryCardConfigurationValidationError{
				field:  "ValueConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVisualisationConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretSummaryCardConfigurationValidationError{
					field:  "VisualisationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretSummaryCardConfigurationValidationError{
					field:  "VisualisationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVisualisationConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretSummaryCardConfigurationValidationError{
				field:  "VisualisationConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	// no validation rules for BorderColor

	// no validation rules for ZeroStateSecretValue

	// no validation rules for SecretSummaryDisabledState

	if len(errors) > 0 {
		return SecretSummaryCardConfigurationMultiError(errors)
	}

	return nil
}

// SecretSummaryCardConfigurationMultiError is an error wrapping multiple
// validation errors returned by SecretSummaryCardConfiguration.ValidateAll()
// if the designated constraints aren't met.
type SecretSummaryCardConfigurationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecretSummaryCardConfigurationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecretSummaryCardConfigurationMultiError) AllErrors() []error { return m }

// SecretSummaryCardConfigurationValidationError is the validation error
// returned by SecretSummaryCardConfiguration.Validate if the designated
// constraints aren't met.
type SecretSummaryCardConfigurationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecretSummaryCardConfigurationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecretSummaryCardConfigurationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecretSummaryCardConfigurationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecretSummaryCardConfigurationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecretSummaryCardConfigurationValidationError) ErrorName() string {
	return "SecretSummaryCardConfigurationValidationError"
}

// Error satisfies the builtin error interface
func (e SecretSummaryCardConfigurationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecretSummaryCardConfiguration.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecretSummaryCardConfigurationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecretSummaryCardConfigurationValidationError{}

// Validate checks the field values on ITCConfiguration with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ITCConfiguration) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ITCConfiguration with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ITCConfigurationMultiError, or nil if none found.
func (m *ITCConfiguration) ValidateAll() error {
	return m.validate(true)
}

func (m *ITCConfiguration) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLeftVisualisationConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ITCConfigurationValidationError{
					field:  "LeftVisualisationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ITCConfigurationValidationError{
					field:  "LeftVisualisationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftVisualisationConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ITCConfigurationValidationError{
				field:  "LeftVisualisationConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTextConfigs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ITCConfigurationValidationError{
						field:  fmt.Sprintf("TextConfigs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ITCConfigurationValidationError{
						field:  fmt.Sprintf("TextConfigs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ITCConfigurationValidationError{
					field:  fmt.Sprintf("TextConfigs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetRightVisualisationConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ITCConfigurationValidationError{
					field:  "RightVisualisationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ITCConfigurationValidationError{
					field:  "RightVisualisationConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightVisualisationConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ITCConfigurationValidationError{
				field:  "RightVisualisationConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBorderConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ITCConfigurationValidationError{
					field:  "BorderConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ITCConfigurationValidationError{
					field:  "BorderConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorderConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ITCConfigurationValidationError{
				field:  "BorderConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetContainerProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ITCConfigurationValidationError{
					field:  "ContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ITCConfigurationValidationError{
					field:  "ContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContainerProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ITCConfigurationValidationError{
				field:  "ContainerProperties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ITCConfigurationValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ITCConfigurationValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ITCConfigurationValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ITCConfigurationMultiError(errors)
	}

	return nil
}

// ITCConfigurationMultiError is an error wrapping multiple validation errors
// returned by ITCConfiguration.ValidateAll() if the designated constraints
// aren't met.
type ITCConfigurationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ITCConfigurationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ITCConfigurationMultiError) AllErrors() []error { return m }

// ITCConfigurationValidationError is the validation error returned by
// ITCConfiguration.Validate if the designated constraints aren't met.
type ITCConfigurationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ITCConfigurationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ITCConfigurationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ITCConfigurationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ITCConfigurationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ITCConfigurationValidationError) ErrorName() string { return "ITCConfigurationValidationError" }

// Error satisfies the builtin error interface
func (e ITCConfigurationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sITCConfiguration.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ITCConfigurationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ITCConfigurationValidationError{}

// Validate checks the field values on VerticalITCConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerticalITCConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerticalITCConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerticalITCConfigMultiError, or nil if none found.
func (m *VerticalITCConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *VerticalITCConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerticalITCConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerticalITCConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerticalITCConfigValidationError{
				field:  "TitleConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValueConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerticalITCConfigValidationError{
					field:  "ValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerticalITCConfigValidationError{
					field:  "ValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValueConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerticalITCConfigValidationError{
				field:  "ValueConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerticalITCConfigMultiError(errors)
	}

	return nil
}

// VerticalITCConfigMultiError is an error wrapping multiple validation errors
// returned by VerticalITCConfig.ValidateAll() if the designated constraints
// aren't met.
type VerticalITCConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerticalITCConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerticalITCConfigMultiError) AllErrors() []error { return m }

// VerticalITCConfigValidationError is the validation error returned by
// VerticalITCConfig.Validate if the designated constraints aren't met.
type VerticalITCConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerticalITCConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerticalITCConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerticalITCConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerticalITCConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerticalITCConfigValidationError) ErrorName() string {
	return "VerticalITCConfigValidationError"
}

// Error satisfies the builtin error interface
func (e VerticalITCConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerticalITCConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerticalITCConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerticalITCConfigValidationError{}

// Validate checks the field values on TextConfiguration with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TextConfiguration) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TextConfiguration with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TextConfigurationMultiError, or nil if none found.
func (m *TextConfiguration) ValidateAll() error {
	return m.validate(true)
}

func (m *TextConfiguration) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Color

	// no validation rules for FontStyle

	// no validation rules for Text

	if all {
		switch v := interface{}(m.GetDisplayTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TextConfigurationValidationError{
					field:  "DisplayTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TextConfigurationValidationError{
					field:  "DisplayTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TextConfigurationValidationError{
				field:  "DisplayTemplate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	// no validation rules for TextAlignment

	if len(errors) > 0 {
		return TextConfigurationMultiError(errors)
	}

	return nil
}

// TextConfigurationMultiError is an error wrapping multiple validation errors
// returned by TextConfiguration.ValidateAll() if the designated constraints
// aren't met.
type TextConfigurationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TextConfigurationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TextConfigurationMultiError) AllErrors() []error { return m }

// TextConfigurationValidationError is the validation error returned by
// TextConfiguration.Validate if the designated constraints aren't met.
type TextConfigurationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TextConfigurationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TextConfigurationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TextConfigurationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TextConfigurationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TextConfigurationValidationError) ErrorName() string {
	return "TextConfigurationValidationError"
}

// Error satisfies the builtin error interface
func (e TextConfigurationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTextConfiguration.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TextConfigurationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TextConfigurationValidationError{}

// Validate checks the field values on DisplayTemplate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DisplayTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DisplayTemplate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DisplayTemplateMultiError, or nil if none found.
func (m *DisplayTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *DisplayTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	if len(errors) > 0 {
		return DisplayTemplateMultiError(errors)
	}

	return nil
}

// DisplayTemplateMultiError is an error wrapping multiple validation errors
// returned by DisplayTemplate.ValidateAll() if the designated constraints
// aren't met.
type DisplayTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DisplayTemplateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DisplayTemplateMultiError) AllErrors() []error { return m }

// DisplayTemplateValidationError is the validation error returned by
// DisplayTemplate.Validate if the designated constraints aren't met.
type DisplayTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DisplayTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DisplayTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DisplayTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DisplayTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DisplayTemplateValidationError) ErrorName() string { return "DisplayTemplateValidationError" }

// Error satisfies the builtin error interface
func (e DisplayTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDisplayTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DisplayTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DisplayTemplateValidationError{}

// Validate checks the field values on VisualisationConfiguration with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VisualisationConfiguration) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VisualisationConfiguration with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VisualisationConfigurationMultiError, or nil if none found.
func (m *VisualisationConfiguration) ValidateAll() error {
	return m.validate(true)
}

func (m *VisualisationConfiguration) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ImageUrl

	// no validation rules for Height

	// no validation rules for Width

	if len(errors) > 0 {
		return VisualisationConfigurationMultiError(errors)
	}

	return nil
}

// VisualisationConfigurationMultiError is an error wrapping multiple
// validation errors returned by VisualisationConfiguration.ValidateAll() if
// the designated constraints aren't met.
type VisualisationConfigurationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VisualisationConfigurationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VisualisationConfigurationMultiError) AllErrors() []error { return m }

// VisualisationConfigurationValidationError is the validation error returned
// by VisualisationConfiguration.Validate if the designated constraints aren't met.
type VisualisationConfigurationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VisualisationConfigurationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VisualisationConfigurationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VisualisationConfigurationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VisualisationConfigurationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VisualisationConfigurationValidationError) ErrorName() string {
	return "VisualisationConfigurationValidationError"
}

// Error satisfies the builtin error interface
func (e VisualisationConfigurationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVisualisationConfiguration.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VisualisationConfigurationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VisualisationConfigurationValidationError{}

// Validate checks the field values on VisualisationCardConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VisualisationCardConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VisualisationCardConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VisualisationCardConfigMultiError, or nil if none found.
func (m *VisualisationCardConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *VisualisationCardConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetProTipConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VisualisationCardConfigValidationError{
					field:  "ProTipConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VisualisationCardConfigValidationError{
					field:  "ProTipConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProTipConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VisualisationCardConfigValidationError{
				field:  "ProTipConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Visualisation.(type) {
	case *VisualisationCardConfig_NumberCardConfig:
		if v == nil {
			err := VisualisationCardConfigValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNumberCardConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VisualisationCardConfigValidationError{
						field:  "NumberCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VisualisationCardConfigValidationError{
						field:  "NumberCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNumberCardConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VisualisationCardConfigValidationError{
					field:  "NumberCardConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *VisualisationCardConfig_BarChartCardConfig:
		if v == nil {
			err := VisualisationCardConfigValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBarChartCardConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VisualisationCardConfigValidationError{
						field:  "BarChartCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VisualisationCardConfigValidationError{
						field:  "BarChartCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBarChartCardConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VisualisationCardConfigValidationError{
					field:  "BarChartCardConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *VisualisationCardConfig_ImageCardConfig:
		if v == nil {
			err := VisualisationCardConfigValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetImageCardConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VisualisationCardConfigValidationError{
						field:  "ImageCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VisualisationCardConfigValidationError{
						field:  "ImageCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetImageCardConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VisualisationCardConfigValidationError{
					field:  "ImageCardConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *VisualisationCardConfig_AreaChartCardConfig:
		if v == nil {
			err := VisualisationCardConfigValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAreaChartCardConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VisualisationCardConfigValidationError{
						field:  "AreaChartCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VisualisationCardConfigValidationError{
						field:  "AreaChartCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAreaChartCardConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VisualisationCardConfigValidationError{
					field:  "AreaChartCardConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *VisualisationCardConfig_GridVisualisationCardConfig:
		if v == nil {
			err := VisualisationCardConfigValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGridVisualisationCardConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VisualisationCardConfigValidationError{
						field:  "GridVisualisationCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VisualisationCardConfigValidationError{
						field:  "GridVisualisationCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGridVisualisationCardConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VisualisationCardConfigValidationError{
					field:  "GridVisualisationCardConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *VisualisationCardConfig_DonutCardConfig:
		if v == nil {
			err := VisualisationCardConfigValidationError{
				field:  "Visualisation",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDonutCardConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VisualisationCardConfigValidationError{
						field:  "DonutCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VisualisationCardConfigValidationError{
						field:  "DonutCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDonutCardConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VisualisationCardConfigValidationError{
					field:  "DonutCardConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return VisualisationCardConfigMultiError(errors)
	}

	return nil
}

// VisualisationCardConfigMultiError is an error wrapping multiple validation
// errors returned by VisualisationCardConfig.ValidateAll() if the designated
// constraints aren't met.
type VisualisationCardConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VisualisationCardConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VisualisationCardConfigMultiError) AllErrors() []error { return m }

// VisualisationCardConfigValidationError is the validation error returned by
// VisualisationCardConfig.Validate if the designated constraints aren't met.
type VisualisationCardConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VisualisationCardConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VisualisationCardConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VisualisationCardConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VisualisationCardConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VisualisationCardConfigValidationError) ErrorName() string {
	return "VisualisationCardConfigValidationError"
}

// Error satisfies the builtin error interface
func (e VisualisationCardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVisualisationCardConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VisualisationCardConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VisualisationCardConfigValidationError{}

// Validate checks the field values on NumberCardConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NumberCardConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NumberCardConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NumberCardConfigMultiError, or nil if none found.
func (m *NumberCardConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *NumberCardConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleValueConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NumberCardConfigValidationError{
					field:  "TitleValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NumberCardConfigValidationError{
					field:  "TitleValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleValueConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NumberCardConfigValidationError{
				field:  "TitleValueConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImageConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NumberCardConfigValidationError{
					field:  "ImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NumberCardConfigValidationError{
					field:  "ImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImageConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NumberCardConfigValidationError{
				field:  "ImageConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if len(errors) > 0 {
		return NumberCardConfigMultiError(errors)
	}

	return nil
}

// NumberCardConfigMultiError is an error wrapping multiple validation errors
// returned by NumberCardConfig.ValidateAll() if the designated constraints
// aren't met.
type NumberCardConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NumberCardConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NumberCardConfigMultiError) AllErrors() []error { return m }

// NumberCardConfigValidationError is the validation error returned by
// NumberCardConfig.Validate if the designated constraints aren't met.
type NumberCardConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NumberCardConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NumberCardConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NumberCardConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NumberCardConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NumberCardConfigValidationError) ErrorName() string { return "NumberCardConfigValidationError" }

// Error satisfies the builtin error interface
func (e NumberCardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNumberCardConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NumberCardConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NumberCardConfigValidationError{}

// Validate checks the field values on BarChartCardConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BarChartCardConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BarChartCardConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BarChartCardConfigMultiError, or nil if none found.
func (m *BarChartCardConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *BarChartCardConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleValueConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BarChartCardConfigValidationError{
					field:  "TitleValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BarChartCardConfigValidationError{
					field:  "TitleValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleValueConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BarChartCardConfigValidationError{
				field:  "TitleValueConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScoreInsightConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BarChartCardConfigValidationError{
					field:  "ScoreInsightConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BarChartCardConfigValidationError{
					field:  "ScoreInsightConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScoreInsightConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BarChartCardConfigValidationError{
				field:  "ScoreInsightConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBarChartConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BarChartCardConfigValidationError{
					field:  "BarChartConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BarChartCardConfigValidationError{
					field:  "BarChartConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBarChartConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BarChartCardConfigValidationError{
				field:  "BarChartConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetReferenceLine()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BarChartCardConfigValidationError{
					field:  "ReferenceLine",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BarChartCardConfigValidationError{
					field:  "ReferenceLine",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReferenceLine()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BarChartCardConfigValidationError{
				field:  "ReferenceLine",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BarChartCardConfigMultiError(errors)
	}

	return nil
}

// BarChartCardConfigMultiError is an error wrapping multiple validation errors
// returned by BarChartCardConfig.ValidateAll() if the designated constraints
// aren't met.
type BarChartCardConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BarChartCardConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BarChartCardConfigMultiError) AllErrors() []error { return m }

// BarChartCardConfigValidationError is the validation error returned by
// BarChartCardConfig.Validate if the designated constraints aren't met.
type BarChartCardConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BarChartCardConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BarChartCardConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BarChartCardConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BarChartCardConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BarChartCardConfigValidationError) ErrorName() string {
	return "BarChartCardConfigValidationError"
}

// Error satisfies the builtin error interface
func (e BarChartCardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBarChartCardConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BarChartCardConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BarChartCardConfigValidationError{}

// Validate checks the field values on ReferenceLine with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReferenceLine) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReferenceLine with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReferenceLineMultiError, or
// nil if none found.
func (m *ReferenceLine) ValidateAll() error {
	return m.validate(true)
}

func (m *ReferenceLine) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReferenceLineValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReferenceLineValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReferenceLineValidationError{
				field:  "TitleConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LineColor

	if len(errors) > 0 {
		return ReferenceLineMultiError(errors)
	}

	return nil
}

// ReferenceLineMultiError is an error wrapping multiple validation errors
// returned by ReferenceLine.ValidateAll() if the designated constraints
// aren't met.
type ReferenceLineMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReferenceLineMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReferenceLineMultiError) AllErrors() []error { return m }

// ReferenceLineValidationError is the validation error returned by
// ReferenceLine.Validate if the designated constraints aren't met.
type ReferenceLineValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReferenceLineValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReferenceLineValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReferenceLineValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReferenceLineValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReferenceLineValidationError) ErrorName() string { return "ReferenceLineValidationError" }

// Error satisfies the builtin error interface
func (e ReferenceLineValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReferenceLine.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReferenceLineValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReferenceLineValidationError{}

// Validate checks the field values on BarChartConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BarChartConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BarChartConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BarChartConfigMultiError,
// or nil if none found.
func (m *BarChartConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *BarChartConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDefaultBarConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BarChartConfigValidationError{
					field:  "DefaultBarConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BarChartConfigValidationError{
					field:  "DefaultBarConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDefaultBarConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BarChartConfigValidationError{
				field:  "DefaultBarConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BarChartConfigMultiError(errors)
	}

	return nil
}

// BarChartConfigMultiError is an error wrapping multiple validation errors
// returned by BarChartConfig.ValidateAll() if the designated constraints
// aren't met.
type BarChartConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BarChartConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BarChartConfigMultiError) AllErrors() []error { return m }

// BarChartConfigValidationError is the validation error returned by
// BarChartConfig.Validate if the designated constraints aren't met.
type BarChartConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BarChartConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BarChartConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BarChartConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BarChartConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BarChartConfigValidationError) ErrorName() string { return "BarChartConfigValidationError" }

// Error satisfies the builtin error interface
func (e BarChartConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBarChartConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BarChartConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BarChartConfigValidationError{}

// Validate checks the field values on BarConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BarConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BarConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BarConfigMultiError, or nil
// if none found.
func (m *BarConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *BarConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTopDisplayComponentConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BarConfigValidationError{
					field:  "TopDisplayComponentConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BarConfigValidationError{
					field:  "TopDisplayComponentConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopDisplayComponentConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BarConfigValidationError{
				field:  "TopDisplayComponentConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomDisplayComponentConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BarConfigValidationError{
					field:  "BottomDisplayComponentConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BarConfigValidationError{
					field:  "BottomDisplayComponentConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomDisplayComponentConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BarConfigValidationError{
				field:  "BottomDisplayComponentConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BarColor

	if len(errors) > 0 {
		return BarConfigMultiError(errors)
	}

	return nil
}

// BarConfigMultiError is an error wrapping multiple validation errors returned
// by BarConfig.ValidateAll() if the designated constraints aren't met.
type BarConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BarConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BarConfigMultiError) AllErrors() []error { return m }

// BarConfigValidationError is the validation error returned by
// BarConfig.Validate if the designated constraints aren't met.
type BarConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BarConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BarConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BarConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BarConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BarConfigValidationError) ErrorName() string { return "BarConfigValidationError" }

// Error satisfies the builtin error interface
func (e BarConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBarConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BarConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BarConfigValidationError{}

// Validate checks the field values on BarDisplayComponentConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BarDisplayComponentConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BarDisplayComponentConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BarDisplayComponentConfigMultiError, or nil if none found.
func (m *BarDisplayComponentConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *BarDisplayComponentConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Config.(type) {
	case *BarDisplayComponentConfig_ImageConfig:
		if v == nil {
			err := BarDisplayComponentConfigValidationError{
				field:  "Config",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetImageConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BarDisplayComponentConfigValidationError{
						field:  "ImageConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BarDisplayComponentConfigValidationError{
						field:  "ImageConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetImageConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BarDisplayComponentConfigValidationError{
					field:  "ImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *BarDisplayComponentConfig_DisplayTextConfig:
		if v == nil {
			err := BarDisplayComponentConfigValidationError{
				field:  "Config",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDisplayTextConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BarDisplayComponentConfigValidationError{
						field:  "DisplayTextConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BarDisplayComponentConfigValidationError{
						field:  "DisplayTextConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDisplayTextConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BarDisplayComponentConfigValidationError{
					field:  "DisplayTextConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return BarDisplayComponentConfigMultiError(errors)
	}

	return nil
}

// BarDisplayComponentConfigMultiError is an error wrapping multiple validation
// errors returned by BarDisplayComponentConfig.ValidateAll() if the
// designated constraints aren't met.
type BarDisplayComponentConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BarDisplayComponentConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BarDisplayComponentConfigMultiError) AllErrors() []error { return m }

// BarDisplayComponentConfigValidationError is the validation error returned by
// BarDisplayComponentConfig.Validate if the designated constraints aren't met.
type BarDisplayComponentConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BarDisplayComponentConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BarDisplayComponentConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BarDisplayComponentConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BarDisplayComponentConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BarDisplayComponentConfigValidationError) ErrorName() string {
	return "BarDisplayComponentConfigValidationError"
}

// Error satisfies the builtin error interface
func (e BarDisplayComponentConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBarDisplayComponentConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BarDisplayComponentConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BarDisplayComponentConfigValidationError{}

// Validate checks the field values on ImageCardConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ImageCardConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImageCardConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ImageCardConfigMultiError, or nil if none found.
func (m *ImageCardConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ImageCardConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImageCardConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImageCardConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImageCardConfigValidationError{
				field:  "TitleConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundImageConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImageCardConfigValidationError{
					field:  "BackgroundImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImageCardConfigValidationError{
					field:  "BackgroundImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundImageConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImageCardConfigValidationError{
				field:  "BackgroundImageConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BackgroundColor

	if all {
		switch v := interface{}(m.GetValueComponentConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImageCardConfigValidationError{
					field:  "ValueComponentConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImageCardConfigValidationError{
					field:  "ValueComponentConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValueComponentConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImageCardConfigValidationError{
				field:  "ValueComponentConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ImageCardConfigMultiError(errors)
	}

	return nil
}

// ImageCardConfigMultiError is an error wrapping multiple validation errors
// returned by ImageCardConfig.ValidateAll() if the designated constraints
// aren't met.
type ImageCardConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageCardConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageCardConfigMultiError) AllErrors() []error { return m }

// ImageCardConfigValidationError is the validation error returned by
// ImageCardConfig.Validate if the designated constraints aren't met.
type ImageCardConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageCardConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageCardConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageCardConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageCardConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageCardConfigValidationError) ErrorName() string { return "ImageCardConfigValidationError" }

// Error satisfies the builtin error interface
func (e ImageCardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImageCardConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageCardConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageCardConfigValidationError{}

// Validate checks the field values on LineItemsConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LineItemsConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LineItemsConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LineItemsConfigMultiError, or nil if none found.
func (m *LineItemsConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *LineItemsConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLineItemConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemsConfigValidationError{
					field:  "LineItemConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemsConfigValidationError{
					field:  "LineItemConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLineItemConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemsConfigValidationError{
				field:  "LineItemConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLeftAlignedHeadingConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemsConfigValidationError{
					field:  "LeftAlignedHeadingConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemsConfigValidationError{
					field:  "LeftAlignedHeadingConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftAlignedHeadingConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemsConfigValidationError{
				field:  "LeftAlignedHeadingConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightAlignedHeadingConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemsConfigValidationError{
					field:  "RightAlignedHeadingConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemsConfigValidationError{
					field:  "RightAlignedHeadingConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightAlignedHeadingConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemsConfigValidationError{
				field:  "RightAlignedHeadingConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LineItemsConfigMultiError(errors)
	}

	return nil
}

// LineItemsConfigMultiError is an error wrapping multiple validation errors
// returned by LineItemsConfig.ValidateAll() if the designated constraints
// aren't met.
type LineItemsConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LineItemsConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LineItemsConfigMultiError) AllErrors() []error { return m }

// LineItemsConfigValidationError is the validation error returned by
// LineItemsConfig.Validate if the designated constraints aren't met.
type LineItemsConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LineItemsConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LineItemsConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LineItemsConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LineItemsConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LineItemsConfigValidationError) ErrorName() string { return "LineItemsConfigValidationError" }

// Error satisfies the builtin error interface
func (e LineItemsConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLineItemsConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LineItemsConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LineItemsConfigValidationError{}

// Validate checks the field values on LineItemConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LineItemConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LineItemConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LineItemConfigMultiError,
// or nil if none found.
func (m *LineItemConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *LineItemConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLeftHeadingConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemConfigValidationError{
					field:  "LeftHeadingConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemConfigValidationError{
					field:  "LeftHeadingConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftHeadingConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemConfigValidationError{
				field:  "LeftHeadingConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLeftTagsConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemConfigValidationError{
					field:  "LeftTagsConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemConfigValidationError{
					field:  "LeftTagsConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftTagsConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemConfigValidationError{
				field:  "LeftTagsConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightHeadingConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemConfigValidationError{
					field:  "RightHeadingConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemConfigValidationError{
					field:  "RightHeadingConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightHeadingConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemConfigValidationError{
				field:  "RightHeadingConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightTagsConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemConfigValidationError{
					field:  "RightTagsConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemConfigValidationError{
					field:  "RightTagsConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightTagsConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemConfigValidationError{
				field:  "RightTagsConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLeftImageConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LineItemConfigValidationError{
					field:  "LeftImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LineItemConfigValidationError{
					field:  "LeftImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftImageConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LineItemConfigValidationError{
				field:  "LeftImageConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LineItemConfigMultiError(errors)
	}

	return nil
}

// LineItemConfigMultiError is an error wrapping multiple validation errors
// returned by LineItemConfig.ValidateAll() if the designated constraints
// aren't met.
type LineItemConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LineItemConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LineItemConfigMultiError) AllErrors() []error { return m }

// LineItemConfigValidationError is the validation error returned by
// LineItemConfig.Validate if the designated constraints aren't met.
type LineItemConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LineItemConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LineItemConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LineItemConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LineItemConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LineItemConfigValidationError) ErrorName() string { return "LineItemConfigValidationError" }

// Error satisfies the builtin error interface
func (e LineItemConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLineItemConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LineItemConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LineItemConfigValidationError{}

// Validate checks the field values on LearnOnTheGoSectionConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LearnOnTheGoSectionConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LearnOnTheGoSectionConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LearnOnTheGoSectionConfigMultiError, or nil if none found.
func (m *LearnOnTheGoSectionConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *LearnOnTheGoSectionConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LearnOnTheGoSectionConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LearnOnTheGoSectionConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LearnOnTheGoSectionConfigValidationError{
				field:  "TitleConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSectionItemsConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LearnOnTheGoSectionConfigValidationError{
						field:  fmt.Sprintf("SectionItemsConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LearnOnTheGoSectionConfigValidationError{
						field:  fmt.Sprintf("SectionItemsConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LearnOnTheGoSectionConfigValidationError{
					field:  fmt.Sprintf("SectionItemsConfig[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return LearnOnTheGoSectionConfigMultiError(errors)
	}

	return nil
}

// LearnOnTheGoSectionConfigMultiError is an error wrapping multiple validation
// errors returned by LearnOnTheGoSectionConfig.ValidateAll() if the
// designated constraints aren't met.
type LearnOnTheGoSectionConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LearnOnTheGoSectionConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LearnOnTheGoSectionConfigMultiError) AllErrors() []error { return m }

// LearnOnTheGoSectionConfigValidationError is the validation error returned by
// LearnOnTheGoSectionConfig.Validate if the designated constraints aren't met.
type LearnOnTheGoSectionConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LearnOnTheGoSectionConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LearnOnTheGoSectionConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LearnOnTheGoSectionConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LearnOnTheGoSectionConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LearnOnTheGoSectionConfigValidationError) ErrorName() string {
	return "LearnOnTheGoSectionConfigValidationError"
}

// Error satisfies the builtin error interface
func (e LearnOnTheGoSectionConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLearnOnTheGoSectionConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LearnOnTheGoSectionConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LearnOnTheGoSectionConfigValidationError{}

// Validate checks the field values on LearnOnTheGoSectionItemConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LearnOnTheGoSectionItemConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LearnOnTheGoSectionItemConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LearnOnTheGoSectionItemConfigMultiError, or nil if none found.
func (m *LearnOnTheGoSectionItemConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *LearnOnTheGoSectionItemConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetImageConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemConfigValidationError{
					field:  "ImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemConfigValidationError{
					field:  "ImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImageConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LearnOnTheGoSectionItemConfigValidationError{
				field:  "ImageConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInfoTagConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemConfigValidationError{
					field:  "InfoTagConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemConfigValidationError{
					field:  "InfoTagConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoTagConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LearnOnTheGoSectionItemConfigValidationError{
				field:  "InfoTagConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OverlayColor

	if all {
		switch v := interface{}(m.GetDescriptionConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemConfigValidationError{
					field:  "DescriptionConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemConfigValidationError{
					field:  "DescriptionConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescriptionConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LearnOnTheGoSectionItemConfigValidationError{
				field:  "DescriptionConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRedirectionConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemConfigValidationError{
					field:  "RedirectionConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LearnOnTheGoSectionItemConfigValidationError{
					field:  "RedirectionConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedirectionConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LearnOnTheGoSectionItemConfigValidationError{
				field:  "RedirectionConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LearnOnTheGoSectionItemConfigMultiError(errors)
	}

	return nil
}

// LearnOnTheGoSectionItemConfigMultiError is an error wrapping multiple
// validation errors returned by LearnOnTheGoSectionItemConfig.ValidateAll()
// if the designated constraints aren't met.
type LearnOnTheGoSectionItemConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LearnOnTheGoSectionItemConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LearnOnTheGoSectionItemConfigMultiError) AllErrors() []error { return m }

// LearnOnTheGoSectionItemConfigValidationError is the validation error
// returned by LearnOnTheGoSectionItemConfig.Validate if the designated
// constraints aren't met.
type LearnOnTheGoSectionItemConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LearnOnTheGoSectionItemConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LearnOnTheGoSectionItemConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LearnOnTheGoSectionItemConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LearnOnTheGoSectionItemConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LearnOnTheGoSectionItemConfigValidationError) ErrorName() string {
	return "LearnOnTheGoSectionItemConfigValidationError"
}

// Error satisfies the builtin error interface
func (e LearnOnTheGoSectionItemConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLearnOnTheGoSectionItemConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LearnOnTheGoSectionItemConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LearnOnTheGoSectionItemConfigValidationError{}

// Validate checks the field values on TextExplainerSectionConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TextExplainerSectionConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TextExplainerSectionConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TextExplainerSectionConfigMultiError, or nil if none found.
func (m *TextExplainerSectionConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *TextExplainerSectionConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TextExplainerSectionConfigValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TextExplainerSectionConfigValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TextExplainerSectionConfigValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDescriptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TextExplainerSectionConfigValidationError{
						field:  fmt.Sprintf("Descriptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TextExplainerSectionConfigValidationError{
						field:  fmt.Sprintf("Descriptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TextExplainerSectionConfigValidationError{
					field:  fmt.Sprintf("Descriptions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BgColor

	// no validation rules for BorderColor

	if all {
		switch v := interface{}(m.GetExpandableControlConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TextExplainerSectionConfigValidationError{
					field:  "ExpandableControlConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TextExplainerSectionConfigValidationError{
					field:  "ExpandableControlConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpandableControlConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TextExplainerSectionConfigValidationError{
				field:  "ExpandableControlConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCtaConfigs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TextExplainerSectionConfigValidationError{
						field:  fmt.Sprintf("CtaConfigs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TextExplainerSectionConfigValidationError{
						field:  fmt.Sprintf("CtaConfigs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TextExplainerSectionConfigValidationError{
					field:  fmt.Sprintf("CtaConfigs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TextExplainerSectionConfigMultiError(errors)
	}

	return nil
}

// TextExplainerSectionConfigMultiError is an error wrapping multiple
// validation errors returned by TextExplainerSectionConfig.ValidateAll() if
// the designated constraints aren't met.
type TextExplainerSectionConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TextExplainerSectionConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TextExplainerSectionConfigMultiError) AllErrors() []error { return m }

// TextExplainerSectionConfigValidationError is the validation error returned
// by TextExplainerSectionConfig.Validate if the designated constraints aren't met.
type TextExplainerSectionConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TextExplainerSectionConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TextExplainerSectionConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TextExplainerSectionConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TextExplainerSectionConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TextExplainerSectionConfigValidationError) ErrorName() string {
	return "TextExplainerSectionConfigValidationError"
}

// Error satisfies the builtin error interface
func (e TextExplainerSectionConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTextExplainerSectionConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TextExplainerSectionConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TextExplainerSectionConfigValidationError{}

// Validate checks the field values on CardInfoComponentConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardInfoComponentConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardInfoComponentConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardInfoComponentConfigMultiError, or nil if none found.
func (m *CardInfoComponentConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *CardInfoComponentConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleValueConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardInfoComponentConfigValidationError{
					field:  "TitleValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardInfoComponentConfigValidationError{
					field:  "TitleValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleValueConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardInfoComponentConfigValidationError{
				field:  "TitleValueConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInsightConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardInfoComponentConfigValidationError{
					field:  "InsightConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardInfoComponentConfigValidationError{
					field:  "InsightConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInsightConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardInfoComponentConfigValidationError{
				field:  "InsightConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardInfoComponentConfigMultiError(errors)
	}

	return nil
}

// CardInfoComponentConfigMultiError is an error wrapping multiple validation
// errors returned by CardInfoComponentConfig.ValidateAll() if the designated
// constraints aren't met.
type CardInfoComponentConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardInfoComponentConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardInfoComponentConfigMultiError) AllErrors() []error { return m }

// CardInfoComponentConfigValidationError is the validation error returned by
// CardInfoComponentConfig.Validate if the designated constraints aren't met.
type CardInfoComponentConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardInfoComponentConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardInfoComponentConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardInfoComponentConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardInfoComponentConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardInfoComponentConfigValidationError) ErrorName() string {
	return "CardInfoComponentConfigValidationError"
}

// Error satisfies the builtin error interface
func (e CardInfoComponentConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardInfoComponentConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardInfoComponentConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardInfoComponentConfigValidationError{}

// Validate checks the field values on ChartDragIndicatorConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChartDragIndicatorConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChartDragIndicatorConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChartDragIndicatorConfigMultiError, or nil if none found.
func (m *ChartDragIndicatorConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ChartDragIndicatorConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LocatorLineColor

	if all {
		switch v := interface{}(m.GetIndicatorLabelConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChartDragIndicatorConfigValidationError{
					field:  "IndicatorLabelConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChartDragIndicatorConfigValidationError{
					field:  "IndicatorLabelConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIndicatorLabelConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChartDragIndicatorConfigValidationError{
				field:  "IndicatorLabelConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ChartDragIndicatorConfigMultiError(errors)
	}

	return nil
}

// ChartDragIndicatorConfigMultiError is an error wrapping multiple validation
// errors returned by ChartDragIndicatorConfig.ValidateAll() if the designated
// constraints aren't met.
type ChartDragIndicatorConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChartDragIndicatorConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChartDragIndicatorConfigMultiError) AllErrors() []error { return m }

// ChartDragIndicatorConfigValidationError is the validation error returned by
// ChartDragIndicatorConfig.Validate if the designated constraints aren't met.
type ChartDragIndicatorConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChartDragIndicatorConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChartDragIndicatorConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChartDragIndicatorConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChartDragIndicatorConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChartDragIndicatorConfigValidationError) ErrorName() string {
	return "ChartDragIndicatorConfigValidationError"
}

// Error satisfies the builtin error interface
func (e ChartDragIndicatorConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChartDragIndicatorConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChartDragIndicatorConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChartDragIndicatorConfigValidationError{}

// Validate checks the field values on ChartLineConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ChartLineConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChartLineConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChartLineConfigMultiError, or nil if none found.
func (m *ChartLineConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ChartLineConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LineColor

	// no validation rules for LineWidth

	if len(errors) > 0 {
		return ChartLineConfigMultiError(errors)
	}

	return nil
}

// ChartLineConfigMultiError is an error wrapping multiple validation errors
// returned by ChartLineConfig.ValidateAll() if the designated constraints
// aren't met.
type ChartLineConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChartLineConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChartLineConfigMultiError) AllErrors() []error { return m }

// ChartLineConfigValidationError is the validation error returned by
// ChartLineConfig.Validate if the designated constraints aren't met.
type ChartLineConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChartLineConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChartLineConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChartLineConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChartLineConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChartLineConfigValidationError) ErrorName() string { return "ChartLineConfigValidationError" }

// Error satisfies the builtin error interface
func (e ChartLineConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChartLineConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChartLineConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChartLineConfigValidationError{}

// Validate checks the field values on AreaChartCardConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AreaChartCardConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AreaChartCardConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AreaChartCardConfigMultiError, or nil if none found.
func (m *AreaChartCardConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *AreaChartCardConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCardInfoComponentConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AreaChartCardConfigValidationError{
					field:  "CardInfoComponentConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AreaChartCardConfigValidationError{
					field:  "CardInfoComponentConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardInfoComponentConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AreaChartCardConfigValidationError{
				field:  "CardInfoComponentConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetAreaFillColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AreaChartCardConfigValidationError{
					field:  "AreaFillColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AreaChartCardConfigValidationError{
					field:  "AreaFillColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAreaFillColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AreaChartCardConfigValidationError{
				field:  "AreaFillColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetChartDragIndicatorConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AreaChartCardConfigValidationError{
					field:  "ChartDragIndicatorConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AreaChartCardConfigValidationError{
					field:  "ChartDragIndicatorConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChartDragIndicatorConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AreaChartCardConfigValidationError{
				field:  "ChartDragIndicatorConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetChartLineConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AreaChartCardConfigValidationError{
					field:  "ChartLineConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AreaChartCardConfigValidationError{
					field:  "ChartLineConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChartLineConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AreaChartCardConfigValidationError{
				field:  "ChartLineConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHorizontalAxisLabelConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AreaChartCardConfigValidationError{
					field:  "HorizontalAxisLabelConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AreaChartCardConfigValidationError{
					field:  "HorizontalAxisLabelConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHorizontalAxisLabelConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AreaChartCardConfigValidationError{
				field:  "HorizontalAxisLabelConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AreaChartCardConfigMultiError(errors)
	}

	return nil
}

// AreaChartCardConfigMultiError is an error wrapping multiple validation
// errors returned by AreaChartCardConfig.ValidateAll() if the designated
// constraints aren't met.
type AreaChartCardConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AreaChartCardConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AreaChartCardConfigMultiError) AllErrors() []error { return m }

// AreaChartCardConfigValidationError is the validation error returned by
// AreaChartCardConfig.Validate if the designated constraints aren't met.
type AreaChartCardConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AreaChartCardConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AreaChartCardConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AreaChartCardConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AreaChartCardConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AreaChartCardConfigValidationError) ErrorName() string {
	return "AreaChartCardConfigValidationError"
}

// Error satisfies the builtin error interface
func (e AreaChartCardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAreaChartCardConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AreaChartCardConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AreaChartCardConfigValidationError{}

// Validate checks the field values on ActionableBannerConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActionableBannerConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActionableBannerConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActionableBannerConfigMultiError, or nil if none found.
func (m *ActionableBannerConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ActionableBannerConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLeftImageConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerConfigValidationError{
					field:  "LeftImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerConfigValidationError{
					field:  "LeftImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftImageConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerConfigValidationError{
				field:  "LeftImageConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescriptionConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerConfigValidationError{
					field:  "DescriptionConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerConfigValidationError{
					field:  "DescriptionConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescriptionConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerConfigValidationError{
				field:  "DescriptionConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightComponentConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerConfigValidationError{
					field:  "RightComponentConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerConfigValidationError{
					field:  "RightComponentConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightComponentConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerConfigValidationError{
				field:  "RightComponentConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerConfigValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerConfigValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerConfigValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBorderProperty()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerConfigValidationError{
					field:  "BorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerConfigValidationError{
					field:  "BorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorderProperty()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerConfigValidationError{
				field:  "BorderProperty",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ActionableBannerConfigMultiError(errors)
	}

	return nil
}

// ActionableBannerConfigMultiError is an error wrapping multiple validation
// errors returned by ActionableBannerConfig.ValidateAll() if the designated
// constraints aren't met.
type ActionableBannerConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionableBannerConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionableBannerConfigMultiError) AllErrors() []error { return m }

// ActionableBannerConfigValidationError is the validation error returned by
// ActionableBannerConfig.Validate if the designated constraints aren't met.
type ActionableBannerConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionableBannerConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionableBannerConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionableBannerConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionableBannerConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionableBannerConfigValidationError) ErrorName() string {
	return "ActionableBannerConfigValidationError"
}

// Error satisfies the builtin error interface
func (e ActionableBannerConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActionableBannerConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionableBannerConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionableBannerConfigValidationError{}

// Validate checks the field values on GridVisualisationCardConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GridVisualisationCardConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GridVisualisationCardConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GridVisualisationCardConfigMultiError, or nil if none found.
func (m *GridVisualisationCardConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *GridVisualisationCardConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleValueConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GridVisualisationCardConfigValidationError{
					field:  "TitleValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GridVisualisationCardConfigValidationError{
					field:  "TitleValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleValueConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GridVisualisationCardConfigValidationError{
				field:  "TitleValueConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGridComponentConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GridVisualisationCardConfigValidationError{
					field:  "GridComponentConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GridVisualisationCardConfigValidationError{
					field:  "GridComponentConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGridComponentConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GridVisualisationCardConfigValidationError{
				field:  "GridComponentConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GridVisualisationCardConfigMultiError(errors)
	}

	return nil
}

// GridVisualisationCardConfigMultiError is an error wrapping multiple
// validation errors returned by GridVisualisationCardConfig.ValidateAll() if
// the designated constraints aren't met.
type GridVisualisationCardConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GridVisualisationCardConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GridVisualisationCardConfigMultiError) AllErrors() []error { return m }

// GridVisualisationCardConfigValidationError is the validation error returned
// by GridVisualisationCardConfig.Validate if the designated constraints
// aren't met.
type GridVisualisationCardConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GridVisualisationCardConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GridVisualisationCardConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GridVisualisationCardConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GridVisualisationCardConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GridVisualisationCardConfigValidationError) ErrorName() string {
	return "GridVisualisationCardConfigValidationError"
}

// Error satisfies the builtin error interface
func (e GridVisualisationCardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGridVisualisationCardConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GridVisualisationCardConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GridVisualisationCardConfigValidationError{}

// Validate checks the field values on GridComponentConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GridComponentConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GridComponentConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GridComponentConfigMultiError, or nil if none found.
func (m *GridComponentConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *GridComponentConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GridComponentConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GridComponentConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GridComponentConfigValidationError{
				field:  "TitleConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetGridCardConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GridComponentConfigValidationError{
					field:  "GridCardConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GridComponentConfigValidationError{
					field:  "GridCardConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGridCardConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GridComponentConfigValidationError{
				field:  "GridCardConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GridComponentConfigMultiError(errors)
	}

	return nil
}

// GridComponentConfigMultiError is an error wrapping multiple validation
// errors returned by GridComponentConfig.ValidateAll() if the designated
// constraints aren't met.
type GridComponentConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GridComponentConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GridComponentConfigMultiError) AllErrors() []error { return m }

// GridComponentConfigValidationError is the validation error returned by
// GridComponentConfig.Validate if the designated constraints aren't met.
type GridComponentConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GridComponentConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GridComponentConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GridComponentConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GridComponentConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GridComponentConfigValidationError) ErrorName() string {
	return "GridComponentConfigValidationError"
}

// Error satisfies the builtin error interface
func (e GridComponentConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGridComponentConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GridComponentConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GridComponentConfigValidationError{}

// Validate checks the field values on GridCardConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GridCardConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GridCardConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GridCardConfigMultiError,
// or nil if none found.
func (m *GridCardConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *GridCardConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ColumnCount

	if all {
		switch v := interface{}(m.GetGridTileConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GridCardConfigValidationError{
					field:  "GridTileConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GridCardConfigValidationError{
					field:  "GridTileConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGridTileConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GridCardConfigValidationError{
				field:  "GridTileConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GridCardConfigMultiError(errors)
	}

	return nil
}

// GridCardConfigMultiError is an error wrapping multiple validation errors
// returned by GridCardConfig.ValidateAll() if the designated constraints
// aren't met.
type GridCardConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GridCardConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GridCardConfigMultiError) AllErrors() []error { return m }

// GridCardConfigValidationError is the validation error returned by
// GridCardConfig.Validate if the designated constraints aren't met.
type GridCardConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GridCardConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GridCardConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GridCardConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GridCardConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GridCardConfigValidationError) ErrorName() string { return "GridCardConfigValidationError" }

// Error satisfies the builtin error interface
func (e GridCardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGridCardConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GridCardConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GridCardConfigValidationError{}

// Validate checks the field values on GridTileConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GridTileConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GridTileConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GridTileConfigMultiError,
// or nil if none found.
func (m *GridTileConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *GridTileConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GridTileConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GridTileConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GridTileConfigValidationError{
				field:  "TitleConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValueConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GridTileConfigValidationError{
					field:  "ValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GridTileConfigValidationError{
					field:  "ValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValueConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GridTileConfigValidationError{
				field:  "ValueConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetIconConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GridTileConfigValidationError{
					field:  "IconConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GridTileConfigValidationError{
					field:  "IconConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIconConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GridTileConfigValidationError{
				field:  "IconConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomCardConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GridTileConfigValidationError{
					field:  "BottomCardConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GridTileConfigValidationError{
					field:  "BottomCardConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomCardConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GridTileConfigValidationError{
				field:  "BottomCardConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GridTileConfigMultiError(errors)
	}

	return nil
}

// GridTileConfigMultiError is an error wrapping multiple validation errors
// returned by GridTileConfig.ValidateAll() if the designated constraints
// aren't met.
type GridTileConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GridTileConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GridTileConfigMultiError) AllErrors() []error { return m }

// GridTileConfigValidationError is the validation error returned by
// GridTileConfig.Validate if the designated constraints aren't met.
type GridTileConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GridTileConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GridTileConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GridTileConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GridTileConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GridTileConfigValidationError) ErrorName() string { return "GridTileConfigValidationError" }

// Error satisfies the builtin error interface
func (e GridTileConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGridTileConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GridTileConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GridTileConfigValidationError{}

// Validate checks the field values on GridComponentBottomCardConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GridComponentBottomCardConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GridComponentBottomCardConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GridComponentBottomCardConfigMultiError, or nil if none found.
func (m *GridComponentBottomCardConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *GridComponentBottomCardConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GridComponentBottomCardConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GridComponentBottomCardConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GridComponentBottomCardConfigValidationError{
				field:  "TitleConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValueConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GridComponentBottomCardConfigValidationError{
					field:  "ValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GridComponentBottomCardConfigValidationError{
					field:  "ValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValueConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GridComponentBottomCardConfigValidationError{
				field:  "ValueConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GridComponentBottomCardConfigMultiError(errors)
	}

	return nil
}

// GridComponentBottomCardConfigMultiError is an error wrapping multiple
// validation errors returned by GridComponentBottomCardConfig.ValidateAll()
// if the designated constraints aren't met.
type GridComponentBottomCardConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GridComponentBottomCardConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GridComponentBottomCardConfigMultiError) AllErrors() []error { return m }

// GridComponentBottomCardConfigValidationError is the validation error
// returned by GridComponentBottomCardConfig.Validate if the designated
// constraints aren't met.
type GridComponentBottomCardConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GridComponentBottomCardConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GridComponentBottomCardConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GridComponentBottomCardConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GridComponentBottomCardConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GridComponentBottomCardConfigValidationError) ErrorName() string {
	return "GridComponentBottomCardConfigValidationError"
}

// Error satisfies the builtin error interface
func (e GridComponentBottomCardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGridComponentBottomCardConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GridComponentBottomCardConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GridComponentBottomCardConfigValidationError{}

// Validate checks the field values on GridComponentBottomCardValueConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GridComponentBottomCardValueConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GridComponentBottomCardValueConfig
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GridComponentBottomCardValueConfigMultiError, or nil if none found.
func (m *GridComponentBottomCardValueConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *GridComponentBottomCardValueConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetKeyConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GridComponentBottomCardValueConfigValidationError{
					field:  "KeyConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GridComponentBottomCardValueConfigValidationError{
					field:  "KeyConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKeyConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GridComponentBottomCardValueConfigValidationError{
				field:  "KeyConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValueConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GridComponentBottomCardValueConfigValidationError{
					field:  "ValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GridComponentBottomCardValueConfigValidationError{
					field:  "ValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValueConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GridComponentBottomCardValueConfigValidationError{
				field:  "ValueConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GridComponentBottomCardValueConfigMultiError(errors)
	}

	return nil
}

// GridComponentBottomCardValueConfigMultiError is an error wrapping multiple
// validation errors returned by
// GridComponentBottomCardValueConfig.ValidateAll() if the designated
// constraints aren't met.
type GridComponentBottomCardValueConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GridComponentBottomCardValueConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GridComponentBottomCardValueConfigMultiError) AllErrors() []error { return m }

// GridComponentBottomCardValueConfigValidationError is the validation error
// returned by GridComponentBottomCardValueConfig.Validate if the designated
// constraints aren't met.
type GridComponentBottomCardValueConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GridComponentBottomCardValueConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GridComponentBottomCardValueConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GridComponentBottomCardValueConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GridComponentBottomCardValueConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GridComponentBottomCardValueConfigValidationError) ErrorName() string {
	return "GridComponentBottomCardValueConfigValidationError"
}

// Error satisfies the builtin error interface
func (e GridComponentBottomCardValueConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGridComponentBottomCardValueConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GridComponentBottomCardValueConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GridComponentBottomCardValueConfigValidationError{}

// Validate checks the field values on DonutCardConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DonutCardConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DonutCardConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DonutCardConfigMultiError, or nil if none found.
func (m *DonutCardConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *DonutCardConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleValueConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DonutCardConfigValidationError{
					field:  "TitleValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DonutCardConfigValidationError{
					field:  "TitleValueConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleValueConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DonutCardConfigValidationError{
				field:  "TitleValueConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDonutSliceConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DonutCardConfigValidationError{
					field:  "DonutSliceConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DonutCardConfigValidationError{
					field:  "DonutSliceConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDonutSliceConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DonutCardConfigValidationError{
				field:  "DonutSliceConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if len(errors) > 0 {
		return DonutCardConfigMultiError(errors)
	}

	return nil
}

// DonutCardConfigMultiError is an error wrapping multiple validation errors
// returned by DonutCardConfig.ValidateAll() if the designated constraints
// aren't met.
type DonutCardConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DonutCardConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DonutCardConfigMultiError) AllErrors() []error { return m }

// DonutCardConfigValidationError is the validation error returned by
// DonutCardConfig.Validate if the designated constraints aren't met.
type DonutCardConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DonutCardConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DonutCardConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DonutCardConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DonutCardConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DonutCardConfigValidationError) ErrorName() string { return "DonutCardConfigValidationError" }

// Error satisfies the builtin error interface
func (e DonutCardConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDonutCardConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DonutCardConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DonutCardConfigValidationError{}

// Validate checks the field values on DonutSliceConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DonutSliceConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DonutSliceConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DonutSliceConfigMultiError, or nil if none found.
func (m *DonutSliceConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *DonutSliceConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DonutColor

	if len(errors) > 0 {
		return DonutSliceConfigMultiError(errors)
	}

	return nil
}

// DonutSliceConfigMultiError is an error wrapping multiple validation errors
// returned by DonutSliceConfig.ValidateAll() if the designated constraints
// aren't met.
type DonutSliceConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DonutSliceConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DonutSliceConfigMultiError) AllErrors() []error { return m }

// DonutSliceConfigValidationError is the validation error returned by
// DonutSliceConfig.Validate if the designated constraints aren't met.
type DonutSliceConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DonutSliceConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DonutSliceConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DonutSliceConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DonutSliceConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DonutSliceConfigValidationError) ErrorName() string { return "DonutSliceConfigValidationError" }

// Error satisfies the builtin error interface
func (e DonutSliceConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDonutSliceConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DonutSliceConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DonutSliceConfigValidationError{}

// Validate checks the field values on ActionableBannerV2Config with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActionableBannerV2Config) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActionableBannerV2Config with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActionableBannerV2ConfigMultiError, or nil if none found.
func (m *ActionableBannerV2Config) ValidateAll() error {
	return m.validate(true)
}

func (m *ActionableBannerV2Config) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRightImageConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerV2ConfigValidationError{
					field:  "RightImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerV2ConfigValidationError{
					field:  "RightImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightImageConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerV2ConfigValidationError{
				field:  "RightImageConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitleConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerV2ConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerV2ConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerV2ConfigValidationError{
				field:  "TitleConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCtaComponentConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerV2ConfigValidationError{
					field:  "CtaComponentConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerV2ConfigValidationError{
					field:  "CtaComponentConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCtaComponentConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerV2ConfigValidationError{
				field:  "CtaComponentConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerV2ConfigValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerV2ConfigValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerV2ConfigValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActionableBannerV2ConfigValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActionableBannerV2ConfigValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActionableBannerV2ConfigValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ActionableBannerV2ConfigMultiError(errors)
	}

	return nil
}

// ActionableBannerV2ConfigMultiError is an error wrapping multiple validation
// errors returned by ActionableBannerV2Config.ValidateAll() if the designated
// constraints aren't met.
type ActionableBannerV2ConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionableBannerV2ConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionableBannerV2ConfigMultiError) AllErrors() []error { return m }

// ActionableBannerV2ConfigValidationError is the validation error returned by
// ActionableBannerV2Config.Validate if the designated constraints aren't met.
type ActionableBannerV2ConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionableBannerV2ConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionableBannerV2ConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionableBannerV2ConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionableBannerV2ConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionableBannerV2ConfigValidationError) ErrorName() string {
	return "ActionableBannerV2ConfigValidationError"
}

// Error satisfies the builtin error interface
func (e ActionableBannerV2ConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActionableBannerV2Config.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionableBannerV2ConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionableBannerV2ConfigValidationError{}

// Validate checks the field values on SecretSummaryCardUiConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecretSummaryCardUiConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecretSummaryCardUiConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecretSummaryCardUiConfigMultiError, or nil if none found.
func (m *SecretSummaryCardUiConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *SecretSummaryCardUiConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitleConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecretSummaryCardUiConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecretSummaryCardUiConfigValidationError{
					field:  "TitleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecretSummaryCardUiConfigValidationError{
				field:  "TitleConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSecretSummaryCardConfig() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretSummaryCardUiConfigValidationError{
						field:  fmt.Sprintf("SecretSummaryCardConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretSummaryCardUiConfigValidationError{
						field:  fmt.Sprintf("SecretSummaryCardConfig[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretSummaryCardUiConfigValidationError{
					field:  fmt.Sprintf("SecretSummaryCardConfig[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SecretSummaryCardUiConfigMultiError(errors)
	}

	return nil
}

// SecretSummaryCardUiConfigMultiError is an error wrapping multiple validation
// errors returned by SecretSummaryCardUiConfig.ValidateAll() if the
// designated constraints aren't met.
type SecretSummaryCardUiConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecretSummaryCardUiConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecretSummaryCardUiConfigMultiError) AllErrors() []error { return m }

// SecretSummaryCardUiConfigValidationError is the validation error returned by
// SecretSummaryCardUiConfig.Validate if the designated constraints aren't met.
type SecretSummaryCardUiConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecretSummaryCardUiConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecretSummaryCardUiConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecretSummaryCardUiConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecretSummaryCardUiConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecretSummaryCardUiConfigValidationError) ErrorName() string {
	return "SecretSummaryCardUiConfigValidationError"
}

// Error satisfies the builtin error interface
func (e SecretSummaryCardUiConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecretSummaryCardUiConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecretSummaryCardUiConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecretSummaryCardUiConfigValidationError{}

// Validate checks the field values on ITCConfiguration_BorderConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ITCConfiguration_BorderConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ITCConfiguration_BorderConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ITCConfiguration_BorderConfigMultiError, or nil if none found.
func (m *ITCConfiguration_BorderConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ITCConfiguration_BorderConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Color

	if len(errors) > 0 {
		return ITCConfiguration_BorderConfigMultiError(errors)
	}

	return nil
}

// ITCConfiguration_BorderConfigMultiError is an error wrapping multiple
// validation errors returned by ITCConfiguration_BorderConfig.ValidateAll()
// if the designated constraints aren't met.
type ITCConfiguration_BorderConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ITCConfiguration_BorderConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ITCConfiguration_BorderConfigMultiError) AllErrors() []error { return m }

// ITCConfiguration_BorderConfigValidationError is the validation error
// returned by ITCConfiguration_BorderConfig.Validate if the designated
// constraints aren't met.
type ITCConfiguration_BorderConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ITCConfiguration_BorderConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ITCConfiguration_BorderConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ITCConfiguration_BorderConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ITCConfiguration_BorderConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ITCConfiguration_BorderConfigValidationError) ErrorName() string {
	return "ITCConfiguration_BorderConfigValidationError"
}

// Error satisfies the builtin error interface
func (e ITCConfiguration_BorderConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sITCConfiguration_BorderConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ITCConfiguration_BorderConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ITCConfiguration_BorderConfigValidationError{}

// Validate checks the field values on ImageCardConfig_ValueComponentConfig
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ImageCardConfig_ValueComponentConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImageCardConfig_ValueComponentConfig
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ImageCardConfig_ValueComponentConfigMultiError, or nil if none found.
func (m *ImageCardConfig_ValueComponentConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ImageCardConfig_ValueComponentConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetImageConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImageCardConfig_ValueComponentConfigValidationError{
					field:  "ImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImageCardConfig_ValueComponentConfigValidationError{
					field:  "ImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImageConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImageCardConfig_ValueComponentConfigValidationError{
				field:  "ImageConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSecretValuePairConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ImageCardConfig_ValueComponentConfigValidationError{
					field:  "SecretValuePairConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ImageCardConfig_ValueComponentConfigValidationError{
					field:  "SecretValuePairConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSecretValuePairConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ImageCardConfig_ValueComponentConfigValidationError{
				field:  "SecretValuePairConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ImageCardConfig_ValueComponentConfigMultiError(errors)
	}

	return nil
}

// ImageCardConfig_ValueComponentConfigMultiError is an error wrapping multiple
// validation errors returned by
// ImageCardConfig_ValueComponentConfig.ValidateAll() if the designated
// constraints aren't met.
type ImageCardConfig_ValueComponentConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageCardConfig_ValueComponentConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageCardConfig_ValueComponentConfigMultiError) AllErrors() []error { return m }

// ImageCardConfig_ValueComponentConfigValidationError is the validation error
// returned by ImageCardConfig_ValueComponentConfig.Validate if the designated
// constraints aren't met.
type ImageCardConfig_ValueComponentConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageCardConfig_ValueComponentConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageCardConfig_ValueComponentConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageCardConfig_ValueComponentConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageCardConfig_ValueComponentConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageCardConfig_ValueComponentConfigValidationError) ErrorName() string {
	return "ImageCardConfig_ValueComponentConfigValidationError"
}

// Error satisfies the builtin error interface
func (e ImageCardConfig_ValueComponentConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImageCardConfig_ValueComponentConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageCardConfig_ValueComponentConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageCardConfig_ValueComponentConfigValidationError{}

// Validate checks the field values on
// LearnOnTheGoSectionItemConfig_RedirectionConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LearnOnTheGoSectionItemConfig_RedirectionConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LearnOnTheGoSectionItemConfig_RedirectionConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// LearnOnTheGoSectionItemConfig_RedirectionConfigMultiError, or nil if none found.
func (m *LearnOnTheGoSectionItemConfig_RedirectionConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *LearnOnTheGoSectionItemConfig_RedirectionConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Redirection.(type) {
	case *LearnOnTheGoSectionItemConfig_RedirectionConfig_Deeplink:
		if v == nil {
			err := LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError{
				field:  "Redirection",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDeeplink()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError{
						field:  "Deeplink",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError{
						field:  "Deeplink",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LearnOnTheGoSectionItemConfig_RedirectionConfig_StorifymeUrl:
		if v == nil {
			err := LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError{
				field:  "Redirection",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for StorifymeUrl
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return LearnOnTheGoSectionItemConfig_RedirectionConfigMultiError(errors)
	}

	return nil
}

// LearnOnTheGoSectionItemConfig_RedirectionConfigMultiError is an error
// wrapping multiple validation errors returned by
// LearnOnTheGoSectionItemConfig_RedirectionConfig.ValidateAll() if the
// designated constraints aren't met.
type LearnOnTheGoSectionItemConfig_RedirectionConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LearnOnTheGoSectionItemConfig_RedirectionConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LearnOnTheGoSectionItemConfig_RedirectionConfigMultiError) AllErrors() []error { return m }

// LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError is the
// validation error returned by
// LearnOnTheGoSectionItemConfig_RedirectionConfig.Validate if the designated
// constraints aren't met.
type LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError) ErrorName() string {
	return "LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError"
}

// Error satisfies the builtin error interface
func (e LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLearnOnTheGoSectionItemConfig_RedirectionConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LearnOnTheGoSectionItemConfig_RedirectionConfigValidationError{}

// Validate checks the field values on
// TextExplainerSectionConfig_ExpandableControlConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TextExplainerSectionConfig_ExpandableControlConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// TextExplainerSectionConfig_ExpandableControlConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// TextExplainerSectionConfig_ExpandableControlConfigMultiError, or nil if
// none found.
func (m *TextExplainerSectionConfig_ExpandableControlConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *TextExplainerSectionConfig_ExpandableControlConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsExpanded

	if all {
		switch v := interface{}(m.GetToggleImageConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TextExplainerSectionConfig_ExpandableControlConfigValidationError{
					field:  "ToggleImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TextExplainerSectionConfig_ExpandableControlConfigValidationError{
					field:  "ToggleImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToggleImageConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TextExplainerSectionConfig_ExpandableControlConfigValidationError{
				field:  "ToggleImageConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TextExplainerSectionConfig_ExpandableControlConfigMultiError(errors)
	}

	return nil
}

// TextExplainerSectionConfig_ExpandableControlConfigMultiError is an error
// wrapping multiple validation errors returned by
// TextExplainerSectionConfig_ExpandableControlConfig.ValidateAll() if the
// designated constraints aren't met.
type TextExplainerSectionConfig_ExpandableControlConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TextExplainerSectionConfig_ExpandableControlConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TextExplainerSectionConfig_ExpandableControlConfigMultiError) AllErrors() []error { return m }

// TextExplainerSectionConfig_ExpandableControlConfigValidationError is the
// validation error returned by
// TextExplainerSectionConfig_ExpandableControlConfig.Validate if the
// designated constraints aren't met.
type TextExplainerSectionConfig_ExpandableControlConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TextExplainerSectionConfig_ExpandableControlConfigValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e TextExplainerSectionConfig_ExpandableControlConfigValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e TextExplainerSectionConfig_ExpandableControlConfigValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e TextExplainerSectionConfig_ExpandableControlConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TextExplainerSectionConfig_ExpandableControlConfigValidationError) ErrorName() string {
	return "TextExplainerSectionConfig_ExpandableControlConfigValidationError"
}

// Error satisfies the builtin error interface
func (e TextExplainerSectionConfig_ExpandableControlConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTextExplainerSectionConfig_ExpandableControlConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TextExplainerSectionConfig_ExpandableControlConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TextExplainerSectionConfig_ExpandableControlConfigValidationError{}
