// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/insights/secrets/config/secret_builder_config.proto

package config

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	categorizer "github.com/epifi/gamma/api/categorizer"

	mutualfund "github.com/epifi/gamma/api/investment/mutualfund"

	variables "github.com/epifi/gamma/api/analyser/variables"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = categorizer.DisplayCategory(0)

	_ = mutualfund.MutualFundCategoryName(0)

	_ = variables.AnalysisVariableName(0)
)

// Validate checks the field values on SecretBuilderDataConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecretBuilderDataConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecretBuilderDataConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecretBuilderDataConfigMultiError, or nil if none found.
func (m *SecretBuilderDataConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *SecretBuilderDataConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.DataConfig.(type) {
	case *SecretBuilderDataConfig_CategoryAggregateConfig:
		if v == nil {
			err := SecretBuilderDataConfigValidationError{
				field:  "DataConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCategoryAggregateConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretBuilderDataConfigValidationError{
						field:  "CategoryAggregateConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretBuilderDataConfigValidationError{
						field:  "CategoryAggregateConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCategoryAggregateConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretBuilderDataConfigValidationError{
					field:  "CategoryAggregateConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretBuilderDataConfig_CreditReportSecretDataConfig:
		if v == nil {
			err := SecretBuilderDataConfigValidationError{
				field:  "DataConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCreditReportSecretDataConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretBuilderDataConfigValidationError{
						field:  "CreditReportSecretDataConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretBuilderDataConfigValidationError{
						field:  "CreditReportSecretDataConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreditReportSecretDataConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretBuilderDataConfigValidationError{
					field:  "CreditReportSecretDataConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretBuilderDataConfig_MfPortfolioSecretDataConfig:
		if v == nil {
			err := SecretBuilderDataConfigValidationError{
				field:  "DataConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMfPortfolioSecretDataConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretBuilderDataConfigValidationError{
						field:  "MfPortfolioSecretDataConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretBuilderDataConfigValidationError{
						field:  "MfPortfolioSecretDataConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMfPortfolioSecretDataConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretBuilderDataConfigValidationError{
					field:  "MfPortfolioSecretDataConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretBuilderDataConfig_EpfSecretDataConfig:
		if v == nil {
			err := SecretBuilderDataConfigValidationError{
				field:  "DataConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEpfSecretDataConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretBuilderDataConfigValidationError{
						field:  "EpfSecretDataConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretBuilderDataConfigValidationError{
						field:  "EpfSecretDataConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEpfSecretDataConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretBuilderDataConfigValidationError{
					field:  "EpfSecretDataConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretBuilderDataConfig_AssetsSecretDataConfig:
		if v == nil {
			err := SecretBuilderDataConfigValidationError{
				field:  "DataConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAssetsSecretDataConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretBuilderDataConfigValidationError{
						field:  "AssetsSecretDataConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretBuilderDataConfigValidationError{
						field:  "AssetsSecretDataConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAssetsSecretDataConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretBuilderDataConfigValidationError{
					field:  "AssetsSecretDataConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretBuilderDataConfig_CreditScoreSecretDataConfig:
		if v == nil {
			err := SecretBuilderDataConfigValidationError{
				field:  "DataConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCreditScoreSecretDataConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretBuilderDataConfigValidationError{
						field:  "CreditScoreSecretDataConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretBuilderDataConfigValidationError{
						field:  "CreditScoreSecretDataConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreditScoreSecretDataConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretBuilderDataConfigValidationError{
					field:  "CreditScoreSecretDataConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SecretBuilderDataConfigMultiError(errors)
	}

	return nil
}

// SecretBuilderDataConfigMultiError is an error wrapping multiple validation
// errors returned by SecretBuilderDataConfig.ValidateAll() if the designated
// constraints aren't met.
type SecretBuilderDataConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecretBuilderDataConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecretBuilderDataConfigMultiError) AllErrors() []error { return m }

// SecretBuilderDataConfigValidationError is the validation error returned by
// SecretBuilderDataConfig.Validate if the designated constraints aren't met.
type SecretBuilderDataConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecretBuilderDataConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecretBuilderDataConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecretBuilderDataConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecretBuilderDataConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecretBuilderDataConfigValidationError) ErrorName() string {
	return "SecretBuilderDataConfigValidationError"
}

// Error satisfies the builtin error interface
func (e SecretBuilderDataConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecretBuilderDataConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecretBuilderDataConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecretBuilderDataConfigValidationError{}

// Validate checks the field values on CategoryAggregateDataConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CategoryAggregateDataConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CategoryAggregateDataConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CategoryAggregateDataConfigMultiError, or nil if none found.
func (m *CategoryAggregateDataConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *CategoryAggregateDataConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DisplayCategory

	if len(errors) > 0 {
		return CategoryAggregateDataConfigMultiError(errors)
	}

	return nil
}

// CategoryAggregateDataConfigMultiError is an error wrapping multiple
// validation errors returned by CategoryAggregateDataConfig.ValidateAll() if
// the designated constraints aren't met.
type CategoryAggregateDataConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CategoryAggregateDataConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CategoryAggregateDataConfigMultiError) AllErrors() []error { return m }

// CategoryAggregateDataConfigValidationError is the validation error returned
// by CategoryAggregateDataConfig.Validate if the designated constraints
// aren't met.
type CategoryAggregateDataConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CategoryAggregateDataConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CategoryAggregateDataConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CategoryAggregateDataConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CategoryAggregateDataConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CategoryAggregateDataConfigValidationError) ErrorName() string {
	return "CategoryAggregateDataConfigValidationError"
}

// Error satisfies the builtin error interface
func (e CategoryAggregateDataConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCategoryAggregateDataConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CategoryAggregateDataConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CategoryAggregateDataConfigValidationError{}

// Validate checks the field values on CreditReportSecretDataConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreditReportSecretDataConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreditReportSecretDataConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreditReportSecretDataConfigMultiError, or nil if none found.
func (m *CreditReportSecretDataConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditReportSecretDataConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CreditReportSecretDimension

	if len(errors) > 0 {
		return CreditReportSecretDataConfigMultiError(errors)
	}

	return nil
}

// CreditReportSecretDataConfigMultiError is an error wrapping multiple
// validation errors returned by CreditReportSecretDataConfig.ValidateAll() if
// the designated constraints aren't met.
type CreditReportSecretDataConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditReportSecretDataConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditReportSecretDataConfigMultiError) AllErrors() []error { return m }

// CreditReportSecretDataConfigValidationError is the validation error returned
// by CreditReportSecretDataConfig.Validate if the designated constraints
// aren't met.
type CreditReportSecretDataConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditReportSecretDataConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditReportSecretDataConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditReportSecretDataConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditReportSecretDataConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditReportSecretDataConfigValidationError) ErrorName() string {
	return "CreditReportSecretDataConfigValidationError"
}

// Error satisfies the builtin error interface
func (e CreditReportSecretDataConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditReportSecretDataConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditReportSecretDataConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditReportSecretDataConfigValidationError{}

// Validate checks the field values on MfPortfolioSecretDataConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MfPortfolioSecretDataConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MfPortfolioSecretDataConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MfPortfolioSecretDataConfigMultiError, or nil if none found.
func (m *MfPortfolioSecretDataConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *MfPortfolioSecretDataConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MutualFundCategoryName

	// no validation rules for AssetClass

	if all {
		switch v := interface{}(m.GetAumRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MfPortfolioSecretDataConfigValidationError{
					field:  "AumRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MfPortfolioSecretDataConfigValidationError{
					field:  "AumRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAumRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MfPortfolioSecretDataConfigValidationError{
				field:  "AumRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Amc

	// no validation rules for SecretMetric

	if all {
		switch v := interface{}(m.GetSchemesWithMetricCount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MfPortfolioSecretDataConfigValidationError{
					field:  "SchemesWithMetricCount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MfPortfolioSecretDataConfigValidationError{
					field:  "SchemesWithMetricCount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSchemesWithMetricCount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MfPortfolioSecretDataConfigValidationError{
				field:  "SchemesWithMetricCount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsAscendingOrder

	// no validation rules for PlanType

	if all {
		switch v := interface{}(m.GetPortfolioPerformanceData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MfPortfolioSecretDataConfigValidationError{
					field:  "PortfolioPerformanceData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MfPortfolioSecretDataConfigValidationError{
					field:  "PortfolioPerformanceData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPortfolioPerformanceData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MfPortfolioSecretDataConfigValidationError{
				field:  "PortfolioPerformanceData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MfPortfolioSecretDataConfigMultiError(errors)
	}

	return nil
}

// MfPortfolioSecretDataConfigMultiError is an error wrapping multiple
// validation errors returned by MfPortfolioSecretDataConfig.ValidateAll() if
// the designated constraints aren't met.
type MfPortfolioSecretDataConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MfPortfolioSecretDataConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MfPortfolioSecretDataConfigMultiError) AllErrors() []error { return m }

// MfPortfolioSecretDataConfigValidationError is the validation error returned
// by MfPortfolioSecretDataConfig.Validate if the designated constraints
// aren't met.
type MfPortfolioSecretDataConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MfPortfolioSecretDataConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MfPortfolioSecretDataConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MfPortfolioSecretDataConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MfPortfolioSecretDataConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MfPortfolioSecretDataConfigValidationError) ErrorName() string {
	return "MfPortfolioSecretDataConfigValidationError"
}

// Error satisfies the builtin error interface
func (e MfPortfolioSecretDataConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMfPortfolioSecretDataConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MfPortfolioSecretDataConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MfPortfolioSecretDataConfigValidationError{}

// Validate checks the field values on EpfSecretDataConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EpfSecretDataConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EpfSecretDataConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EpfSecretDataConfigMultiError, or nil if none found.
func (m *EpfSecretDataConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *EpfSecretDataConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EpfSecretMetric

	// no validation rules for EpfSecretDimension

	if len(errors) > 0 {
		return EpfSecretDataConfigMultiError(errors)
	}

	return nil
}

// EpfSecretDataConfigMultiError is an error wrapping multiple validation
// errors returned by EpfSecretDataConfig.ValidateAll() if the designated
// constraints aren't met.
type EpfSecretDataConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EpfSecretDataConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EpfSecretDataConfigMultiError) AllErrors() []error { return m }

// EpfSecretDataConfigValidationError is the validation error returned by
// EpfSecretDataConfig.Validate if the designated constraints aren't met.
type EpfSecretDataConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EpfSecretDataConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EpfSecretDataConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EpfSecretDataConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EpfSecretDataConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EpfSecretDataConfigValidationError) ErrorName() string {
	return "EpfSecretDataConfigValidationError"
}

// Error satisfies the builtin error interface
func (e EpfSecretDataConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEpfSecretDataConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EpfSecretDataConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EpfSecretDataConfigValidationError{}

// Validate checks the field values on AssetsSecretDataConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AssetsSecretDataConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssetsSecretDataConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssetsSecretDataConfigMultiError, or nil if none found.
func (m *AssetsSecretDataConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *AssetsSecretDataConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AssetsSecretMetric

	if len(errors) > 0 {
		return AssetsSecretDataConfigMultiError(errors)
	}

	return nil
}

// AssetsSecretDataConfigMultiError is an error wrapping multiple validation
// errors returned by AssetsSecretDataConfig.ValidateAll() if the designated
// constraints aren't met.
type AssetsSecretDataConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssetsSecretDataConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssetsSecretDataConfigMultiError) AllErrors() []error { return m }

// AssetsSecretDataConfigValidationError is the validation error returned by
// AssetsSecretDataConfig.Validate if the designated constraints aren't met.
type AssetsSecretDataConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssetsSecretDataConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssetsSecretDataConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssetsSecretDataConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssetsSecretDataConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssetsSecretDataConfigValidationError) ErrorName() string {
	return "AssetsSecretDataConfigValidationError"
}

// Error satisfies the builtin error interface
func (e AssetsSecretDataConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssetsSecretDataConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssetsSecretDataConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssetsSecretDataConfigValidationError{}

// Validate checks the field values on CreditScoreSecretDataConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreditScoreSecretDataConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreditScoreSecretDataConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreditScoreSecretDataConfigMultiError, or nil if none found.
func (m *CreditScoreSecretDataConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditScoreSecretDataConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CreditScoreSecretDataConfigMultiError(errors)
	}

	return nil
}

// CreditScoreSecretDataConfigMultiError is an error wrapping multiple
// validation errors returned by CreditScoreSecretDataConfig.ValidateAll() if
// the designated constraints aren't met.
type CreditScoreSecretDataConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditScoreSecretDataConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditScoreSecretDataConfigMultiError) AllErrors() []error { return m }

// CreditScoreSecretDataConfigValidationError is the validation error returned
// by CreditScoreSecretDataConfig.Validate if the designated constraints
// aren't met.
type CreditScoreSecretDataConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditScoreSecretDataConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditScoreSecretDataConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditScoreSecretDataConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditScoreSecretDataConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditScoreSecretDataConfigValidationError) ErrorName() string {
	return "CreditScoreSecretDataConfigValidationError"
}

// Error satisfies the builtin error interface
func (e CreditScoreSecretDataConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditScoreSecretDataConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditScoreSecretDataConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditScoreSecretDataConfigValidationError{}

// Validate checks the field values on SecretAnalyserUIConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecretAnalyserUIConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecretAnalyserUIConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecretAnalyserUIConfigMultiError, or nil if none found.
func (m *SecretAnalyserUIConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *SecretAnalyserUIConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComponentName

	// no validation rules for Theme

	// no validation rules for IsOptional

	switch v := m.Config.(type) {
	case *SecretAnalyserUIConfig_LineItemsConfig:
		if v == nil {
			err := SecretAnalyserUIConfigValidationError{
				field:  "Config",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLineItemsConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "LineItemsConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "LineItemsConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLineItemsConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserUIConfigValidationError{
					field:  "LineItemsConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretAnalyserUIConfig_VisualisationCardConfig:
		if v == nil {
			err := SecretAnalyserUIConfigValidationError{
				field:  "Config",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetVisualisationCardConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "VisualisationCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "VisualisationCardConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetVisualisationCardConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserUIConfigValidationError{
					field:  "VisualisationCardConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretAnalyserUIConfig_LearnOnTheGoSectionConfig:
		if v == nil {
			err := SecretAnalyserUIConfigValidationError{
				field:  "Config",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLearnOnTheGoSectionConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "LearnOnTheGoSectionConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "LearnOnTheGoSectionConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLearnOnTheGoSectionConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserUIConfigValidationError{
					field:  "LearnOnTheGoSectionConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretAnalyserUIConfig_TextExplainerSectionConfig:
		if v == nil {
			err := SecretAnalyserUIConfigValidationError{
				field:  "Config",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTextExplainerSectionConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "TextExplainerSectionConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "TextExplainerSectionConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTextExplainerSectionConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserUIConfigValidationError{
					field:  "TextExplainerSectionConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretAnalyserUIConfig_ActionableBannerConfig:
		if v == nil {
			err := SecretAnalyserUIConfigValidationError{
				field:  "Config",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetActionableBannerConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "ActionableBannerConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "ActionableBannerConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetActionableBannerConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserUIConfigValidationError{
					field:  "ActionableBannerConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretAnalyserUIConfig_GlobalAnalyserUiComponentConfig:
		if v == nil {
			err := SecretAnalyserUIConfigValidationError{
				field:  "Config",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGlobalAnalyserUiComponentConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "GlobalAnalyserUiComponentConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "GlobalAnalyserUiComponentConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGlobalAnalyserUiComponentConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserUIConfigValidationError{
					field:  "GlobalAnalyserUiComponentConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretAnalyserUIConfig_ActionableBannerV2Config:
		if v == nil {
			err := SecretAnalyserUIConfigValidationError{
				field:  "Config",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetActionableBannerV2Config()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "ActionableBannerV2Config",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "ActionableBannerV2Config",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetActionableBannerV2Config()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserUIConfigValidationError{
					field:  "ActionableBannerV2Config",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecretAnalyserUIConfig_SecretSummaryCardUiConfig:
		if v == nil {
			err := SecretAnalyserUIConfigValidationError{
				field:  "Config",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSecretSummaryCardUiConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "SecretSummaryCardUiConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretAnalyserUIConfigValidationError{
						field:  "SecretSummaryCardUiConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSecretSummaryCardUiConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretAnalyserUIConfigValidationError{
					field:  "SecretSummaryCardUiConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SecretAnalyserUIConfigMultiError(errors)
	}

	return nil
}

// SecretAnalyserUIConfigMultiError is an error wrapping multiple validation
// errors returned by SecretAnalyserUIConfig.ValidateAll() if the designated
// constraints aren't met.
type SecretAnalyserUIConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecretAnalyserUIConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecretAnalyserUIConfigMultiError) AllErrors() []error { return m }

// SecretAnalyserUIConfigValidationError is the validation error returned by
// SecretAnalyserUIConfig.Validate if the designated constraints aren't met.
type SecretAnalyserUIConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecretAnalyserUIConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecretAnalyserUIConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecretAnalyserUIConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecretAnalyserUIConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecretAnalyserUIConfigValidationError) ErrorName() string {
	return "SecretAnalyserUIConfigValidationError"
}

// Error satisfies the builtin error interface
func (e SecretAnalyserUIConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecretAnalyserUIConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecretAnalyserUIConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecretAnalyserUIConfigValidationError{}

// Validate checks the field values on GlobalAnalyserUIComponentConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GlobalAnalyserUIComponentConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GlobalAnalyserUIComponentConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GlobalAnalyserUIComponentConfigMultiError, or nil if none found.
func (m *GlobalAnalyserUIComponentConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *GlobalAnalyserUIComponentConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GlobalConfigId

	if len(errors) > 0 {
		return GlobalAnalyserUIComponentConfigMultiError(errors)
	}

	return nil
}

// GlobalAnalyserUIComponentConfigMultiError is an error wrapping multiple
// validation errors returned by GlobalAnalyserUIComponentConfig.ValidateAll()
// if the designated constraints aren't met.
type GlobalAnalyserUIComponentConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GlobalAnalyserUIComponentConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GlobalAnalyserUIComponentConfigMultiError) AllErrors() []error { return m }

// GlobalAnalyserUIComponentConfigValidationError is the validation error
// returned by GlobalAnalyserUIComponentConfig.Validate if the designated
// constraints aren't met.
type GlobalAnalyserUIComponentConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GlobalAnalyserUIComponentConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GlobalAnalyserUIComponentConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GlobalAnalyserUIComponentConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GlobalAnalyserUIComponentConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GlobalAnalyserUIComponentConfigValidationError) ErrorName() string {
	return "GlobalAnalyserUIComponentConfigValidationError"
}

// Error satisfies the builtin error interface
func (e GlobalAnalyserUIComponentConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGlobalAnalyserUIComponentConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GlobalAnalyserUIComponentConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GlobalAnalyserUIComponentConfigValidationError{}

// Validate checks the field values on SecretSummaryUIConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecretSummaryUIConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecretSummaryUIConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecretSummaryUIConfigMultiError, or nil if none found.
func (m *SecretSummaryUIConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *SecretSummaryUIConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Theme

	switch v := m.Config.(type) {
	case *SecretSummaryUIConfig_SecretSummaryCardConfiguration:
		if v == nil {
			err := SecretSummaryUIConfigValidationError{
				field:  "Config",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSecretSummaryCardConfiguration()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecretSummaryUIConfigValidationError{
						field:  "SecretSummaryCardConfiguration",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecretSummaryUIConfigValidationError{
						field:  "SecretSummaryCardConfiguration",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSecretSummaryCardConfiguration()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecretSummaryUIConfigValidationError{
					field:  "SecretSummaryCardConfiguration",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SecretSummaryUIConfigMultiError(errors)
	}

	return nil
}

// SecretSummaryUIConfigMultiError is an error wrapping multiple validation
// errors returned by SecretSummaryUIConfig.ValidateAll() if the designated
// constraints aren't met.
type SecretSummaryUIConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecretSummaryUIConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecretSummaryUIConfigMultiError) AllErrors() []error { return m }

// SecretSummaryUIConfigValidationError is the validation error returned by
// SecretSummaryUIConfig.Validate if the designated constraints aren't met.
type SecretSummaryUIConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecretSummaryUIConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecretSummaryUIConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecretSummaryUIConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecretSummaryUIConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecretSummaryUIConfigValidationError) ErrorName() string {
	return "SecretSummaryUIConfigValidationError"
}

// Error satisfies the builtin error interface
func (e SecretSummaryUIConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecretSummaryUIConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecretSummaryUIConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecretSummaryUIConfigValidationError{}

// Validate checks the field values on MfPortfolioSecretDataConfig_AumRange
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *MfPortfolioSecretDataConfig_AumRange) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MfPortfolioSecretDataConfig_AumRange
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// MfPortfolioSecretDataConfig_AumRangeMultiError, or nil if none found.
func (m *MfPortfolioSecretDataConfig_AumRange) ValidateAll() error {
	return m.validate(true)
}

func (m *MfPortfolioSecretDataConfig_AumRange) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Min

	// no validation rules for Max

	if len(errors) > 0 {
		return MfPortfolioSecretDataConfig_AumRangeMultiError(errors)
	}

	return nil
}

// MfPortfolioSecretDataConfig_AumRangeMultiError is an error wrapping multiple
// validation errors returned by
// MfPortfolioSecretDataConfig_AumRange.ValidateAll() if the designated
// constraints aren't met.
type MfPortfolioSecretDataConfig_AumRangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MfPortfolioSecretDataConfig_AumRangeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MfPortfolioSecretDataConfig_AumRangeMultiError) AllErrors() []error { return m }

// MfPortfolioSecretDataConfig_AumRangeValidationError is the validation error
// returned by MfPortfolioSecretDataConfig_AumRange.Validate if the designated
// constraints aren't met.
type MfPortfolioSecretDataConfig_AumRangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MfPortfolioSecretDataConfig_AumRangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MfPortfolioSecretDataConfig_AumRangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MfPortfolioSecretDataConfig_AumRangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MfPortfolioSecretDataConfig_AumRangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MfPortfolioSecretDataConfig_AumRangeValidationError) ErrorName() string {
	return "MfPortfolioSecretDataConfig_AumRangeValidationError"
}

// Error satisfies the builtin error interface
func (e MfPortfolioSecretDataConfig_AumRangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMfPortfolioSecretDataConfig_AumRange.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MfPortfolioSecretDataConfig_AumRangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MfPortfolioSecretDataConfig_AumRangeValidationError{}

// Validate checks the field values on MfPortfolioSecretDataConfig_SchemeCount
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *MfPortfolioSecretDataConfig_SchemeCount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// MfPortfolioSecretDataConfig_SchemeCount with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// MfPortfolioSecretDataConfig_SchemeCountMultiError, or nil if none found.
func (m *MfPortfolioSecretDataConfig_SchemeCount) ValidateAll() error {
	return m.validate(true)
}

func (m *MfPortfolioSecretDataConfig_SchemeCount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Min

	// no validation rules for Max

	if len(errors) > 0 {
		return MfPortfolioSecretDataConfig_SchemeCountMultiError(errors)
	}

	return nil
}

// MfPortfolioSecretDataConfig_SchemeCountMultiError is an error wrapping
// multiple validation errors returned by
// MfPortfolioSecretDataConfig_SchemeCount.ValidateAll() if the designated
// constraints aren't met.
type MfPortfolioSecretDataConfig_SchemeCountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MfPortfolioSecretDataConfig_SchemeCountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MfPortfolioSecretDataConfig_SchemeCountMultiError) AllErrors() []error { return m }

// MfPortfolioSecretDataConfig_SchemeCountValidationError is the validation
// error returned by MfPortfolioSecretDataConfig_SchemeCount.Validate if the
// designated constraints aren't met.
type MfPortfolioSecretDataConfig_SchemeCountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MfPortfolioSecretDataConfig_SchemeCountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MfPortfolioSecretDataConfig_SchemeCountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MfPortfolioSecretDataConfig_SchemeCountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MfPortfolioSecretDataConfig_SchemeCountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MfPortfolioSecretDataConfig_SchemeCountValidationError) ErrorName() string {
	return "MfPortfolioSecretDataConfig_SchemeCountValidationError"
}

// Error satisfies the builtin error interface
func (e MfPortfolioSecretDataConfig_SchemeCountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMfPortfolioSecretDataConfig_SchemeCount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MfPortfolioSecretDataConfig_SchemeCountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MfPortfolioSecretDataConfig_SchemeCountValidationError{}

// Validate checks the field values on
// MfPortfolioSecretDataConfig_PortfolioPerformanceData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MfPortfolioSecretDataConfig_PortfolioPerformanceData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// MfPortfolioSecretDataConfig_PortfolioPerformanceData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// MfPortfolioSecretDataConfig_PortfolioPerformanceDataMultiError, or nil if
// none found.
func (m *MfPortfolioSecretDataConfig_PortfolioPerformanceData) ValidateAll() error {
	return m.validate(true)
}

func (m *MfPortfolioSecretDataConfig_PortfolioPerformanceData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FixedDeposit

	// no validation rules for Nifty50

	// no validation rules for Peers

	if len(errors) > 0 {
		return MfPortfolioSecretDataConfig_PortfolioPerformanceDataMultiError(errors)
	}

	return nil
}

// MfPortfolioSecretDataConfig_PortfolioPerformanceDataMultiError is an error
// wrapping multiple validation errors returned by
// MfPortfolioSecretDataConfig_PortfolioPerformanceData.ValidateAll() if the
// designated constraints aren't met.
type MfPortfolioSecretDataConfig_PortfolioPerformanceDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MfPortfolioSecretDataConfig_PortfolioPerformanceDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MfPortfolioSecretDataConfig_PortfolioPerformanceDataMultiError) AllErrors() []error { return m }

// MfPortfolioSecretDataConfig_PortfolioPerformanceDataValidationError is the
// validation error returned by
// MfPortfolioSecretDataConfig_PortfolioPerformanceData.Validate if the
// designated constraints aren't met.
type MfPortfolioSecretDataConfig_PortfolioPerformanceDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MfPortfolioSecretDataConfig_PortfolioPerformanceDataValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e MfPortfolioSecretDataConfig_PortfolioPerformanceDataValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e MfPortfolioSecretDataConfig_PortfolioPerformanceDataValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e MfPortfolioSecretDataConfig_PortfolioPerformanceDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MfPortfolioSecretDataConfig_PortfolioPerformanceDataValidationError) ErrorName() string {
	return "MfPortfolioSecretDataConfig_PortfolioPerformanceDataValidationError"
}

// Error satisfies the builtin error interface
func (e MfPortfolioSecretDataConfig_PortfolioPerformanceDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMfPortfolioSecretDataConfig_PortfolioPerformanceData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MfPortfolioSecretDataConfig_PortfolioPerformanceDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MfPortfolioSecretDataConfig_PortfolioPerformanceDataValidationError{}
