// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/insights/secrets/config/secret_builder_config.proto

package config

import (
	variables "github.com/epifi/gamma/api/analyser/variables"
	categorizer "github.com/epifi/gamma/api/categorizer"
	mutualfund "github.com/epifi/gamma/api/investment/mutualfund"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// this use case help in rendering secret data for SECRET_BUILDER_NAME_CREDIT_REPORT_SECRET secret
type CreditReportSecretDimension int32

const (
	CreditReportSecretDimension_CREDIT_REPORT_SECRET_DIMENSION_UNSPECIFIED  CreditReportSecretDimension = 0
	CreditReportSecretDimension_CREDIT_REPORT_SECRET_DIMENSION_HARD_ENQUIRY CreditReportSecretDimension = 1
)

// Enum value maps for CreditReportSecretDimension.
var (
	CreditReportSecretDimension_name = map[int32]string{
		0: "CREDIT_REPORT_SECRET_DIMENSION_UNSPECIFIED",
		1: "CREDIT_REPORT_SECRET_DIMENSION_HARD_ENQUIRY",
	}
	CreditReportSecretDimension_value = map[string]int32{
		"CREDIT_REPORT_SECRET_DIMENSION_UNSPECIFIED":  0,
		"CREDIT_REPORT_SECRET_DIMENSION_HARD_ENQUIRY": 1,
	}
)

func (x CreditReportSecretDimension) Enum() *CreditReportSecretDimension {
	p := new(CreditReportSecretDimension)
	*p = x
	return p
}

func (x CreditReportSecretDimension) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreditReportSecretDimension) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[0].Descriptor()
}

func (CreditReportSecretDimension) Type() protoreflect.EnumType {
	return &file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[0]
}

func (x CreditReportSecretDimension) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreditReportSecretDimension.Descriptor instead.
func (CreditReportSecretDimension) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{0}
}

type EpfSecretMetric int32

const (
	EpfSecretMetric_EPF_SECRET_METRIC_UNSPECIFIED                               EpfSecretMetric = 0
	EpfSecretMetric_EPF_SECRET_METRIC_MEDICAL_WITHDRAWABLE_BALANCE              EpfSecretMetric = 1
	EpfSecretMetric_EPF_SECRET_METRIC_MARRIAGE_WITHDRAWABLE_BALANCE             EpfSecretMetric = 2
	EpfSecretMetric_EPF_SECRET_METRIC_EDUCATION_WITHDRAWABLE_BALANCE            EpfSecretMetric = 3
	EpfSecretMetric_EPF_SECRET_METRIC_HOUSE_CONSTRUCTION_WITHDRAWABLE_BALANCE   EpfSecretMetric = 4
	EpfSecretMetric_EPF_SECRET_METRIC_HOUSE_LOAN_REPAYMENT_WITHDRAWABLE_BALANCE EpfSecretMetric = 5
	EpfSecretMetric_EPF_SECRET_METRIC_HOUSE_RENOVATION_WITHDRAWABLE_BALANCE     EpfSecretMetric = 6
	EpfSecretMetric_EPF_SECRET_METRIC_BALANCE                                   EpfSecretMetric = 7
	EpfSecretMetric_EPF_SECRET_METRIC_LAND_PURCHASE_WITHDRAWABLE_BALANCE        EpfSecretMetric = 8
)

// Enum value maps for EpfSecretMetric.
var (
	EpfSecretMetric_name = map[int32]string{
		0: "EPF_SECRET_METRIC_UNSPECIFIED",
		1: "EPF_SECRET_METRIC_MEDICAL_WITHDRAWABLE_BALANCE",
		2: "EPF_SECRET_METRIC_MARRIAGE_WITHDRAWABLE_BALANCE",
		3: "EPF_SECRET_METRIC_EDUCATION_WITHDRAWABLE_BALANCE",
		4: "EPF_SECRET_METRIC_HOUSE_CONSTRUCTION_WITHDRAWABLE_BALANCE",
		5: "EPF_SECRET_METRIC_HOUSE_LOAN_REPAYMENT_WITHDRAWABLE_BALANCE",
		6: "EPF_SECRET_METRIC_HOUSE_RENOVATION_WITHDRAWABLE_BALANCE",
		7: "EPF_SECRET_METRIC_BALANCE",
		8: "EPF_SECRET_METRIC_LAND_PURCHASE_WITHDRAWABLE_BALANCE",
	}
	EpfSecretMetric_value = map[string]int32{
		"EPF_SECRET_METRIC_UNSPECIFIED":                               0,
		"EPF_SECRET_METRIC_MEDICAL_WITHDRAWABLE_BALANCE":              1,
		"EPF_SECRET_METRIC_MARRIAGE_WITHDRAWABLE_BALANCE":             2,
		"EPF_SECRET_METRIC_EDUCATION_WITHDRAWABLE_BALANCE":            3,
		"EPF_SECRET_METRIC_HOUSE_CONSTRUCTION_WITHDRAWABLE_BALANCE":   4,
		"EPF_SECRET_METRIC_HOUSE_LOAN_REPAYMENT_WITHDRAWABLE_BALANCE": 5,
		"EPF_SECRET_METRIC_HOUSE_RENOVATION_WITHDRAWABLE_BALANCE":     6,
		"EPF_SECRET_METRIC_BALANCE":                                   7,
		"EPF_SECRET_METRIC_LAND_PURCHASE_WITHDRAWABLE_BALANCE":        8,
	}
)

func (x EpfSecretMetric) Enum() *EpfSecretMetric {
	p := new(EpfSecretMetric)
	*p = x
	return p
}

func (x EpfSecretMetric) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EpfSecretMetric) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[1].Descriptor()
}

func (EpfSecretMetric) Type() protoreflect.EnumType {
	return &file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[1]
}

func (x EpfSecretMetric) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EpfSecretMetric.Descriptor instead.
func (EpfSecretMetric) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{1}
}

// EpfSecretDimension defines different dimensions over which epf data can be shown
type EpfSecretDimension int32

const (
	EpfSecretDimension_EPF_SECRET_DIMENSION_UNSPECIFIED              EpfSecretDimension = 0
	EpfSecretDimension_EPF_SECRET_DIMENSION_CURRENT_PASSBOOK_BALANCE EpfSecretDimension = 1
	EpfSecretDimension_EPF_SECRET_DIMENSION_WITHDRAWABLE_BALANCE     EpfSecretDimension = 2
	EpfSecretDimension_EPF_SECRET_DIMENSION_CONTRIBUTORS             EpfSecretDimension = 3
	EpfSecretDimension_EPF_SECRET_DIMENSION_TRANSFERABLE_BALANCE     EpfSecretDimension = 4
)

// Enum value maps for EpfSecretDimension.
var (
	EpfSecretDimension_name = map[int32]string{
		0: "EPF_SECRET_DIMENSION_UNSPECIFIED",
		1: "EPF_SECRET_DIMENSION_CURRENT_PASSBOOK_BALANCE",
		2: "EPF_SECRET_DIMENSION_WITHDRAWABLE_BALANCE",
		3: "EPF_SECRET_DIMENSION_CONTRIBUTORS",
		4: "EPF_SECRET_DIMENSION_TRANSFERABLE_BALANCE",
	}
	EpfSecretDimension_value = map[string]int32{
		"EPF_SECRET_DIMENSION_UNSPECIFIED":              0,
		"EPF_SECRET_DIMENSION_CURRENT_PASSBOOK_BALANCE": 1,
		"EPF_SECRET_DIMENSION_WITHDRAWABLE_BALANCE":     2,
		"EPF_SECRET_DIMENSION_CONTRIBUTORS":             3,
		"EPF_SECRET_DIMENSION_TRANSFERABLE_BALANCE":     4,
	}
)

func (x EpfSecretDimension) Enum() *EpfSecretDimension {
	p := new(EpfSecretDimension)
	*p = x
	return p
}

func (x EpfSecretDimension) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EpfSecretDimension) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[2].Descriptor()
}

func (EpfSecretDimension) Type() protoreflect.EnumType {
	return &file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[2]
}

func (x EpfSecretDimension) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EpfSecretDimension.Descriptor instead.
func (EpfSecretDimension) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{2}
}

type AssetsSecretMetric int32

const (
	AssetsSecretMetric_ASSETS_SECRET_METRIC_UNSPECIFIED        AssetsSecretMetric = 0
	AssetsSecretMetric_ASSETS_SECRET_METRIC_ACTIVE_ASSET_RATIO AssetsSecretMetric = 1
)

// Enum value maps for AssetsSecretMetric.
var (
	AssetsSecretMetric_name = map[int32]string{
		0: "ASSETS_SECRET_METRIC_UNSPECIFIED",
		1: "ASSETS_SECRET_METRIC_ACTIVE_ASSET_RATIO",
	}
	AssetsSecretMetric_value = map[string]int32{
		"ASSETS_SECRET_METRIC_UNSPECIFIED":        0,
		"ASSETS_SECRET_METRIC_ACTIVE_ASSET_RATIO": 1,
	}
)

func (x AssetsSecretMetric) Enum() *AssetsSecretMetric {
	p := new(AssetsSecretMetric)
	*p = x
	return p
}

func (x AssetsSecretMetric) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetsSecretMetric) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[3].Descriptor()
}

func (AssetsSecretMetric) Type() protoreflect.EnumType {
	return &file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[3]
}

func (x AssetsSecretMetric) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetsSecretMetric.Descriptor instead.
func (AssetsSecretMetric) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{3}
}

// SecretAnalyserUiTheme is used to identify a predefined SecretAnalyserUIConfig
// Themes are used to define common configurations for a ui component that can be shared across multiple secrets
// A secret can override any field specified in a theme by defining a non zero value of the field in the secret config
type SecretAnalyserUiTheme int32

const (
	SecretAnalyserUiTheme_SECRET_ANALYSER_UI_THEME_UNSPECIFIED SecretAnalyserUiTheme = 0
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-10471&t=P4Bovzg8WuzAHcFq-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_BAR_CHART_CARD_THEME_BERRY SecretAnalyserUiTheme = 1
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-10821&t=P4Bovzg8WuzAHcFq-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_BAR_CHART_CARD_THEME_INDIGO SecretAnalyserUiTheme = 2
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-11207&t=P4Bovzg8WuzAHcFq-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_BAR_CHART_CARD_THEME_AMBER SecretAnalyserUiTheme = 3
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-12178&t=P4Bovzg8WuzAHcFq-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_BAR_CHART_CARD_THEME_OCEAN SecretAnalyserUiTheme = 4
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-12891&t=P4Bovzg8WuzAHcFq-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_BAR_CHART_CARD_THEME_MOSS SecretAnalyserUiTheme = 5
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-13246&t=P4Bovzg8WuzAHcFq-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_BAR_CHART_CARD_THEME_CHERRY SecretAnalyserUiTheme = 6
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16176-31409&t=mfJ7JsMFE4wchGAY-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_NUMBER_CARD_THEME_OCEAN SecretAnalyserUiTheme = 7
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16171-23658&t=mfJ7JsMFE4wchGAY-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_NUMBER_CARD_THEME_AMBER SecretAnalyserUiTheme = 8
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16176-31970&t=mfJ7JsMFE4wchGAY-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_NUMBER_CARD_THEME_HONEY SecretAnalyserUiTheme = 9
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16176-31973&t=mfJ7JsMFE4wchGAY-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_LINE_ITEMS_THEME_DEFAULT SecretAnalyserUiTheme = 10
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18789-15157&t=u97bzoDwUVWKxH1T-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_NUMBER_CARD_THEME_MOSS SecretAnalyserUiTheme = 11
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18789-16293&t=u97bzoDwUVWKxH1T-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_NUMBER_CARD_THEME_CHERRY SecretAnalyserUiTheme = 12
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18789-16589&t=u97bzoDwUVWKxH1T-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_NUMBER_CARD_THEME_BERRY SecretAnalyserUiTheme = 13
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20249-35436&t=zebR3cdcBSHAN1qr-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_TEXT_EXPLAINER_SECTION_THEME_DEFAULT SecretAnalyserUiTheme = 14
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=21325-39061&t=IHYiPFIJ2rbOWhgd-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_ACTIONABLE_REFRESH_BANNER_THEME_DEFAULT SecretAnalyserUiTheme = 15
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20582-35088&t=nHS0eS3IVpAsh1TZ-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_TEXT_EXPLAINER_SECTION_WITH_TOGGLE_THEME_DEFAULT SecretAnalyserUiTheme = 16
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=584-34006&t=ELxnMCaKE0UkQULY-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_DONUT_CARD_THEME_OCEAN SecretAnalyserUiTheme = 17
	// Default theme defining fonts and colors of text in actionable banner which allows user to edit some value
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=355-27824&t=CSA4n2mM2davu1kY-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_EDITABLE_VALUE_ACTIONABLE_BANNER_THEME_DEFAULT SecretAnalyserUiTheme = 18
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=584-34649&t=bVnPAh3ePhKtbrPK-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_DONUT_CARD_THEME_MOSS SecretAnalyserUiTheme = 19
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=584-34513&t=hFSN60FxT2pCVd6p-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_AREA_CHART_CARD_THEME_MOSS SecretAnalyserUiTheme = 20
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=409-8961&t=ULWGJvPOCEZUMFgN-4
	SecretAnalyserUiTheme_SECRET_ANALYSER_DONUT_CARD_THEME_BERRY SecretAnalyserUiTheme = 21
)

// Enum value maps for SecretAnalyserUiTheme.
var (
	SecretAnalyserUiTheme_name = map[int32]string{
		0:  "SECRET_ANALYSER_UI_THEME_UNSPECIFIED",
		1:  "SECRET_ANALYSER_BAR_CHART_CARD_THEME_BERRY",
		2:  "SECRET_ANALYSER_BAR_CHART_CARD_THEME_INDIGO",
		3:  "SECRET_ANALYSER_BAR_CHART_CARD_THEME_AMBER",
		4:  "SECRET_ANALYSER_BAR_CHART_CARD_THEME_OCEAN",
		5:  "SECRET_ANALYSER_BAR_CHART_CARD_THEME_MOSS",
		6:  "SECRET_ANALYSER_BAR_CHART_CARD_THEME_CHERRY",
		7:  "SECRET_ANALYSER_NUMBER_CARD_THEME_OCEAN",
		8:  "SECRET_ANALYSER_NUMBER_CARD_THEME_AMBER",
		9:  "SECRET_ANALYSER_NUMBER_CARD_THEME_HONEY",
		10: "SECRET_ANALYSER_LINE_ITEMS_THEME_DEFAULT",
		11: "SECRET_ANALYSER_NUMBER_CARD_THEME_MOSS",
		12: "SECRET_ANALYSER_NUMBER_CARD_THEME_CHERRY",
		13: "SECRET_ANALYSER_NUMBER_CARD_THEME_BERRY",
		14: "SECRET_ANALYSER_TEXT_EXPLAINER_SECTION_THEME_DEFAULT",
		15: "SECRET_ANALYSER_ACTIONABLE_REFRESH_BANNER_THEME_DEFAULT",
		16: "SECRET_ANALYSER_TEXT_EXPLAINER_SECTION_WITH_TOGGLE_THEME_DEFAULT",
		17: "SECRET_ANALYSER_DONUT_CARD_THEME_OCEAN",
		18: "SECRET_ANALYSER_EDITABLE_VALUE_ACTIONABLE_BANNER_THEME_DEFAULT",
		19: "SECRET_ANALYSER_DONUT_CARD_THEME_MOSS",
		20: "SECRET_ANALYSER_AREA_CHART_CARD_THEME_MOSS",
		21: "SECRET_ANALYSER_DONUT_CARD_THEME_BERRY",
	}
	SecretAnalyserUiTheme_value = map[string]int32{
		"SECRET_ANALYSER_UI_THEME_UNSPECIFIED":                             0,
		"SECRET_ANALYSER_BAR_CHART_CARD_THEME_BERRY":                       1,
		"SECRET_ANALYSER_BAR_CHART_CARD_THEME_INDIGO":                      2,
		"SECRET_ANALYSER_BAR_CHART_CARD_THEME_AMBER":                       3,
		"SECRET_ANALYSER_BAR_CHART_CARD_THEME_OCEAN":                       4,
		"SECRET_ANALYSER_BAR_CHART_CARD_THEME_MOSS":                        5,
		"SECRET_ANALYSER_BAR_CHART_CARD_THEME_CHERRY":                      6,
		"SECRET_ANALYSER_NUMBER_CARD_THEME_OCEAN":                          7,
		"SECRET_ANALYSER_NUMBER_CARD_THEME_AMBER":                          8,
		"SECRET_ANALYSER_NUMBER_CARD_THEME_HONEY":                          9,
		"SECRET_ANALYSER_LINE_ITEMS_THEME_DEFAULT":                         10,
		"SECRET_ANALYSER_NUMBER_CARD_THEME_MOSS":                           11,
		"SECRET_ANALYSER_NUMBER_CARD_THEME_CHERRY":                         12,
		"SECRET_ANALYSER_NUMBER_CARD_THEME_BERRY":                          13,
		"SECRET_ANALYSER_TEXT_EXPLAINER_SECTION_THEME_DEFAULT":             14,
		"SECRET_ANALYSER_ACTIONABLE_REFRESH_BANNER_THEME_DEFAULT":          15,
		"SECRET_ANALYSER_TEXT_EXPLAINER_SECTION_WITH_TOGGLE_THEME_DEFAULT": 16,
		"SECRET_ANALYSER_DONUT_CARD_THEME_OCEAN":                           17,
		"SECRET_ANALYSER_EDITABLE_VALUE_ACTIONABLE_BANNER_THEME_DEFAULT":   18,
		"SECRET_ANALYSER_DONUT_CARD_THEME_MOSS":                            19,
		"SECRET_ANALYSER_AREA_CHART_CARD_THEME_MOSS":                       20,
		"SECRET_ANALYSER_DONUT_CARD_THEME_BERRY":                           21,
	}
)

func (x SecretAnalyserUiTheme) Enum() *SecretAnalyserUiTheme {
	p := new(SecretAnalyserUiTheme)
	*p = x
	return p
}

func (x SecretAnalyserUiTheme) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SecretAnalyserUiTheme) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[4].Descriptor()
}

func (SecretAnalyserUiTheme) Type() protoreflect.EnumType {
	return &file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[4]
}

func (x SecretAnalyserUiTheme) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SecretAnalyserUiTheme.Descriptor instead.
func (SecretAnalyserUiTheme) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{4}
}

// SecretSummaryUiTheme is used to identify a predefined SecretSummaryUIConfig
// Themes are used to define common configurations for secret summary that can be shared across multiple secrets
// A secret can override any field specified in a theme by defining a non zero value of the field in the secret config
type SecretSummaryUiTheme int32

const (
	SecretSummaryUiTheme_SECRET_SUMMARY_UI_THEME_UNSPECIFIED SecretSummaryUiTheme = 0
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-13705&t=szhPPN3FsJXgv3kH-4
	SecretSummaryUiTheme_SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_BERRY SecretSummaryUiTheme = 1
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-13637&t=szhPPN3FsJXgv3kH-4
	SecretSummaryUiTheme_SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_INDIGO SecretSummaryUiTheme = 2
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-13610&t=szhPPN3FsJXgv3kH-4
	SecretSummaryUiTheme_SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_AMBER SecretSummaryUiTheme = 3
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-13743&t=szhPPN3FsJXgv3kH-4
	SecretSummaryUiTheme_SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_OCEAN SecretSummaryUiTheme = 4
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-13781&t=szhPPN3FsJXgv3kH-4
	SecretSummaryUiTheme_SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_MOSS SecretSummaryUiTheme = 5
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18485-13819&t=szhPPN3FsJXgv3kH-4
	SecretSummaryUiTheme_SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_CHERRY SecretSummaryUiTheme = 6
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16276-15128&t=mfJ7JsMFE4wchGAY-4
	SecretSummaryUiTheme_SECRET_SUMMARY_CARD_THEME_OCEAN SecretSummaryUiTheme = 7
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16276-15777&t=mfJ7JsMFE4wchGAY-4
	SecretSummaryUiTheme_SECRET_SUMMARY_CARD_THEME_AMBER SecretSummaryUiTheme = 8
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16276-15840&t=mfJ7JsMFE4wchGAY-4
	SecretSummaryUiTheme_SECRET_SUMMARY_CARD_THEME_HONEY SecretSummaryUiTheme = 9
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18789-16270&t=u97bzoDwUVWKxH1T-4
	SecretSummaryUiTheme_SECRET_SUMMARY_CARD_THEME_MOSS SecretSummaryUiTheme = 10
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18789-16567&t=u97bzoDwUVWKxH1T-4
	SecretSummaryUiTheme_SECRET_SUMMARY_CARD_THEME_CHERRY SecretSummaryUiTheme = 11
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18789-16863&t=u97bzoDwUVWKxH1T-4
	SecretSummaryUiTheme_SECRET_SUMMARY_CARD_THEME_BERRY SecretSummaryUiTheme = 12
)

// Enum value maps for SecretSummaryUiTheme.
var (
	SecretSummaryUiTheme_name = map[int32]string{
		0:  "SECRET_SUMMARY_UI_THEME_UNSPECIFIED",
		1:  "SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_BERRY",
		2:  "SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_INDIGO",
		3:  "SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_AMBER",
		4:  "SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_OCEAN",
		5:  "SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_MOSS",
		6:  "SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_CHERRY",
		7:  "SECRET_SUMMARY_CARD_THEME_OCEAN",
		8:  "SECRET_SUMMARY_CARD_THEME_AMBER",
		9:  "SECRET_SUMMARY_CARD_THEME_HONEY",
		10: "SECRET_SUMMARY_CARD_THEME_MOSS",
		11: "SECRET_SUMMARY_CARD_THEME_CHERRY",
		12: "SECRET_SUMMARY_CARD_THEME_BERRY",
	}
	SecretSummaryUiTheme_value = map[string]int32{
		"SECRET_SUMMARY_UI_THEME_UNSPECIFIED":            0,
		"SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_BERRY":  1,
		"SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_INDIGO": 2,
		"SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_AMBER":  3,
		"SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_OCEAN":  4,
		"SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_MOSS":   5,
		"SECRET_SUMMARY_CARD_THEME_VERTICAL_BARS_CHERRY": 6,
		"SECRET_SUMMARY_CARD_THEME_OCEAN":                7,
		"SECRET_SUMMARY_CARD_THEME_AMBER":                8,
		"SECRET_SUMMARY_CARD_THEME_HONEY":                9,
		"SECRET_SUMMARY_CARD_THEME_MOSS":                 10,
		"SECRET_SUMMARY_CARD_THEME_CHERRY":               11,
		"SECRET_SUMMARY_CARD_THEME_BERRY":                12,
	}
)

func (x SecretSummaryUiTheme) Enum() *SecretSummaryUiTheme {
	p := new(SecretSummaryUiTheme)
	*p = x
	return p
}

func (x SecretSummaryUiTheme) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SecretSummaryUiTheme) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[5].Descriptor()
}

func (SecretSummaryUiTheme) Type() protoreflect.EnumType {
	return &file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[5]
}

func (x SecretSummaryUiTheme) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SecretSummaryUiTheme.Descriptor instead.
func (SecretSummaryUiTheme) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{5}
}

type SecretAnalyserState int32

const (
	SecretAnalyserState_SECRET_ANALYSER_STATE_UNSPECIFIED SecretAnalyserState = 0
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=19267-19488&node-type=frame&t=ix0xvJKV5jSYJlt2-0
	SecretAnalyserState_SECRET_ANALYSER_STATE_BASE SecretAnalyserState = 1
	// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20535-35288&node-type=frame&t=ix0xvJKV5jSYJlt2-0
	// state where insufficient data is available to generate a secret
	SecretAnalyserState_SECRET_ANALYSER_STATE_ZERO_VALUE SecretAnalyserState = 2
)

// Enum value maps for SecretAnalyserState.
var (
	SecretAnalyserState_name = map[int32]string{
		0: "SECRET_ANALYSER_STATE_UNSPECIFIED",
		1: "SECRET_ANALYSER_STATE_BASE",
		2: "SECRET_ANALYSER_STATE_ZERO_VALUE",
	}
	SecretAnalyserState_value = map[string]int32{
		"SECRET_ANALYSER_STATE_UNSPECIFIED": 0,
		"SECRET_ANALYSER_STATE_BASE":        1,
		"SECRET_ANALYSER_STATE_ZERO_VALUE":  2,
	}
)

func (x SecretAnalyserState) Enum() *SecretAnalyserState {
	p := new(SecretAnalyserState)
	*p = x
	return p
}

func (x SecretAnalyserState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SecretAnalyserState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[6].Descriptor()
}

func (SecretAnalyserState) Type() protoreflect.EnumType {
	return &file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[6]
}

func (x SecretAnalyserState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SecretAnalyserState.Descriptor instead.
func (SecretAnalyserState) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{6}
}

type MfPortfolioSecretDataConfig_SecretMetric int32

const (
	MfPortfolioSecretDataConfig_SECRET_METRIC_UNSPECIFIED             MfPortfolioSecretDataConfig_SecretMetric = 0
	MfPortfolioSecretDataConfig_SECRET_METRIC_XIRR                    MfPortfolioSecretDataConfig_SecretMetric = 1
	MfPortfolioSecretDataConfig_SECRET_METRIC_SHARPE_RATIO            MfPortfolioSecretDataConfig_SecretMetric = 2
	MfPortfolioSecretDataConfig_SECRET_METRIC_ALPHA                   MfPortfolioSecretDataConfig_SecretMetric = 3
	MfPortfolioSecretDataConfig_SECRET_METRIC_HIT_RATE                MfPortfolioSecretDataConfig_SecretMetric = 4
	MfPortfolioSecretDataConfig_SECRET_METRIC_EXPENSE_RATIO           MfPortfolioSecretDataConfig_SecretMetric = 5
	MfPortfolioSecretDataConfig_SECRET_METRIC_BATTING_AVG             MfPortfolioSecretDataConfig_SecretMetric = 6
	MfPortfolioSecretDataConfig_SECRET_METRIC_UPSIDE_CAPTURE_RATIO    MfPortfolioSecretDataConfig_SecretMetric = 7
	MfPortfolioSecretDataConfig_SECRET_METRIC_DOWN_SIDE_CAPTURE_RATIO MfPortfolioSecretDataConfig_SecretMetric = 8
)

// Enum value maps for MfPortfolioSecretDataConfig_SecretMetric.
var (
	MfPortfolioSecretDataConfig_SecretMetric_name = map[int32]string{
		0: "SECRET_METRIC_UNSPECIFIED",
		1: "SECRET_METRIC_XIRR",
		2: "SECRET_METRIC_SHARPE_RATIO",
		3: "SECRET_METRIC_ALPHA",
		4: "SECRET_METRIC_HIT_RATE",
		5: "SECRET_METRIC_EXPENSE_RATIO",
		6: "SECRET_METRIC_BATTING_AVG",
		7: "SECRET_METRIC_UPSIDE_CAPTURE_RATIO",
		8: "SECRET_METRIC_DOWN_SIDE_CAPTURE_RATIO",
	}
	MfPortfolioSecretDataConfig_SecretMetric_value = map[string]int32{
		"SECRET_METRIC_UNSPECIFIED":             0,
		"SECRET_METRIC_XIRR":                    1,
		"SECRET_METRIC_SHARPE_RATIO":            2,
		"SECRET_METRIC_ALPHA":                   3,
		"SECRET_METRIC_HIT_RATE":                4,
		"SECRET_METRIC_EXPENSE_RATIO":           5,
		"SECRET_METRIC_BATTING_AVG":             6,
		"SECRET_METRIC_UPSIDE_CAPTURE_RATIO":    7,
		"SECRET_METRIC_DOWN_SIDE_CAPTURE_RATIO": 8,
	}
)

func (x MfPortfolioSecretDataConfig_SecretMetric) Enum() *MfPortfolioSecretDataConfig_SecretMetric {
	p := new(MfPortfolioSecretDataConfig_SecretMetric)
	*p = x
	return p
}

func (x MfPortfolioSecretDataConfig_SecretMetric) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MfPortfolioSecretDataConfig_SecretMetric) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[7].Descriptor()
}

func (MfPortfolioSecretDataConfig_SecretMetric) Type() protoreflect.EnumType {
	return &file_api_insights_secrets_config_secret_builder_config_proto_enumTypes[7]
}

func (x MfPortfolioSecretDataConfig_SecretMetric) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MfPortfolioSecretDataConfig_SecretMetric.Descriptor instead.
func (MfPortfolioSecretDataConfig_SecretMetric) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{3, 0}
}

// SecretBuilderDataConfig defines configurations required to run a secret builder
// This includes inherent filters applicable while preparing a secret such as category name
type SecretBuilderDataConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to DataConfig:
	//
	//	*SecretBuilderDataConfig_CategoryAggregateConfig
	//	*SecretBuilderDataConfig_CreditReportSecretDataConfig
	//	*SecretBuilderDataConfig_MfPortfolioSecretDataConfig
	//	*SecretBuilderDataConfig_EpfSecretDataConfig
	//	*SecretBuilderDataConfig_AssetsSecretDataConfig
	//	*SecretBuilderDataConfig_CreditScoreSecretDataConfig
	DataConfig isSecretBuilderDataConfig_DataConfig `protobuf_oneof:"data_config"`
}

func (x *SecretBuilderDataConfig) Reset() {
	*x = SecretBuilderDataConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecretBuilderDataConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretBuilderDataConfig) ProtoMessage() {}

func (x *SecretBuilderDataConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretBuilderDataConfig.ProtoReflect.Descriptor instead.
func (*SecretBuilderDataConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{0}
}

func (m *SecretBuilderDataConfig) GetDataConfig() isSecretBuilderDataConfig_DataConfig {
	if m != nil {
		return m.DataConfig
	}
	return nil
}

func (x *SecretBuilderDataConfig) GetCategoryAggregateConfig() *CategoryAggregateDataConfig {
	if x, ok := x.GetDataConfig().(*SecretBuilderDataConfig_CategoryAggregateConfig); ok {
		return x.CategoryAggregateConfig
	}
	return nil
}

func (x *SecretBuilderDataConfig) GetCreditReportSecretDataConfig() *CreditReportSecretDataConfig {
	if x, ok := x.GetDataConfig().(*SecretBuilderDataConfig_CreditReportSecretDataConfig); ok {
		return x.CreditReportSecretDataConfig
	}
	return nil
}

func (x *SecretBuilderDataConfig) GetMfPortfolioSecretDataConfig() *MfPortfolioSecretDataConfig {
	if x, ok := x.GetDataConfig().(*SecretBuilderDataConfig_MfPortfolioSecretDataConfig); ok {
		return x.MfPortfolioSecretDataConfig
	}
	return nil
}

func (x *SecretBuilderDataConfig) GetEpfSecretDataConfig() *EpfSecretDataConfig {
	if x, ok := x.GetDataConfig().(*SecretBuilderDataConfig_EpfSecretDataConfig); ok {
		return x.EpfSecretDataConfig
	}
	return nil
}

func (x *SecretBuilderDataConfig) GetAssetsSecretDataConfig() *AssetsSecretDataConfig {
	if x, ok := x.GetDataConfig().(*SecretBuilderDataConfig_AssetsSecretDataConfig); ok {
		return x.AssetsSecretDataConfig
	}
	return nil
}

func (x *SecretBuilderDataConfig) GetCreditScoreSecretDataConfig() *CreditScoreSecretDataConfig {
	if x, ok := x.GetDataConfig().(*SecretBuilderDataConfig_CreditScoreSecretDataConfig); ok {
		return x.CreditScoreSecretDataConfig
	}
	return nil
}

type isSecretBuilderDataConfig_DataConfig interface {
	isSecretBuilderDataConfig_DataConfig()
}

type SecretBuilderDataConfig_CategoryAggregateConfig struct {
	CategoryAggregateConfig *CategoryAggregateDataConfig `protobuf:"bytes,1,opt,name=category_aggregate_config,json=categoryAggregateConfig,proto3,oneof"`
}

type SecretBuilderDataConfig_CreditReportSecretDataConfig struct {
	CreditReportSecretDataConfig *CreditReportSecretDataConfig `protobuf:"bytes,2,opt,name=credit_report_secret_data_config,json=creditReportSecretDataConfig,proto3,oneof"`
}

type SecretBuilderDataConfig_MfPortfolioSecretDataConfig struct {
	MfPortfolioSecretDataConfig *MfPortfolioSecretDataConfig `protobuf:"bytes,3,opt,name=mf_portfolio_secret_data_config,json=mfPortfolioSecretDataConfig,proto3,oneof"`
}

type SecretBuilderDataConfig_EpfSecretDataConfig struct {
	EpfSecretDataConfig *EpfSecretDataConfig `protobuf:"bytes,4,opt,name=epf_secret_data_config,json=epfSecretDataConfig,proto3,oneof"`
}

type SecretBuilderDataConfig_AssetsSecretDataConfig struct {
	AssetsSecretDataConfig *AssetsSecretDataConfig `protobuf:"bytes,5,opt,name=assets_secret_data_config,json=assetsSecretDataConfig,proto3,oneof"`
}

type SecretBuilderDataConfig_CreditScoreSecretDataConfig struct {
	CreditScoreSecretDataConfig *CreditScoreSecretDataConfig `protobuf:"bytes,6,opt,name=credit_score_secret_data_config,json=creditScoreSecretDataConfig,proto3,oneof"`
}

func (*SecretBuilderDataConfig_CategoryAggregateConfig) isSecretBuilderDataConfig_DataConfig() {}

func (*SecretBuilderDataConfig_CreditReportSecretDataConfig) isSecretBuilderDataConfig_DataConfig() {}

func (*SecretBuilderDataConfig_MfPortfolioSecretDataConfig) isSecretBuilderDataConfig_DataConfig() {}

func (*SecretBuilderDataConfig_EpfSecretDataConfig) isSecretBuilderDataConfig_DataConfig() {}

func (*SecretBuilderDataConfig_AssetsSecretDataConfig) isSecretBuilderDataConfig_DataConfig() {}

func (*SecretBuilderDataConfig_CreditScoreSecretDataConfig) isSecretBuilderDataConfig_DataConfig() {}

// CategoryAggregateDataConfig defines the config for SECRET_BUILDER_NAME_TRANSACTION_CATEGORY
type CategoryAggregateDataConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DisplayCategory categorizer.DisplayCategory `protobuf:"varint,1,opt,name=display_category,json=displayCategory,proto3,enum=categorizer.DisplayCategory" json:"display_category,omitempty"`
}

func (x *CategoryAggregateDataConfig) Reset() {
	*x = CategoryAggregateDataConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CategoryAggregateDataConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryAggregateDataConfig) ProtoMessage() {}

func (x *CategoryAggregateDataConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryAggregateDataConfig.ProtoReflect.Descriptor instead.
func (*CategoryAggregateDataConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{1}
}

func (x *CategoryAggregateDataConfig) GetDisplayCategory() categorizer.DisplayCategory {
	if x != nil {
		return x.DisplayCategory
	}
	return categorizer.DisplayCategory(0)
}

// CreditReportSecretDataConfig defines the config for dimension to show credit report secretY
type CreditReportSecretDataConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreditReportSecretDimension CreditReportSecretDimension `protobuf:"varint,1,opt,name=credit_report_secret_dimension,json=creditReportSecretDimension,proto3,enum=insights.secrets.config.CreditReportSecretDimension" json:"credit_report_secret_dimension,omitempty"`
}

func (x *CreditReportSecretDataConfig) Reset() {
	*x = CreditReportSecretDataConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditReportSecretDataConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditReportSecretDataConfig) ProtoMessage() {}

func (x *CreditReportSecretDataConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditReportSecretDataConfig.ProtoReflect.Descriptor instead.
func (*CreditReportSecretDataConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{2}
}

func (x *CreditReportSecretDataConfig) GetCreditReportSecretDimension() CreditReportSecretDimension {
	if x != nil {
		return x.CreditReportSecretDimension
	}
	return CreditReportSecretDimension_CREDIT_REPORT_SECRET_DIMENSION_UNSPECIFIED
}

// MfPortfolioSecretDataConfig defines filters that can be applied while filtering the relevant mutual funds for a secret
type MfPortfolioSecretDataConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MutualFundCategoryName mutualfund.MutualFundCategoryName     `protobuf:"varint,1,opt,name=mutual_fund_category_name,json=mutualFundCategoryName,proto3,enum=api.investment.mutualfund.MutualFundCategoryName" json:"mutual_fund_category_name,omitempty"`
	AssetClass             mutualfund.AssetClass                 `protobuf:"varint,2,opt,name=asset_class,json=assetClass,proto3,enum=api.investment.mutualfund.AssetClass" json:"asset_class,omitempty"`
	AumRange               *MfPortfolioSecretDataConfig_AumRange `protobuf:"bytes,3,opt,name=aum_range,json=aumRange,proto3" json:"aum_range,omitempty"`
	Amc                    mutualfund.Amc                        `protobuf:"varint,4,opt,name=amc,proto3,enum=api.investment.mutualfund.Amc" json:"amc,omitempty"`
	// Represents the value of the mutual fund, which will be used for sorting and calculating metrics for secrets.
	SecretMetric MfPortfolioSecretDataConfig_SecretMetric `protobuf:"varint,5,opt,name=secret_metric,json=secretMetric,proto3,enum=insights.secrets.config.MfPortfolioSecretDataConfig_SecretMetric" json:"secret_metric,omitempty"`
	// Expected number of schemes with the secret metric available
	SchemesWithMetricCount *MfPortfolioSecretDataConfig_SchemeCount `protobuf:"bytes,6,opt,name=schemes_with_metric_count,json=schemesWithMetricCount,proto3" json:"schemes_with_metric_count,omitempty"`
	// collection names to be used for fetching the mutual funds in given collection and filtering on that basis
	CollectionName []string `protobuf:"bytes,7,rep,name=collection_name,json=collectionName,proto3" json:"collection_name,omitempty"`
	// The flag to be passed as true when the mf analytics data to be sorted in ascending order
	IsAscendingOrder bool `protobuf:"varint,8,opt,name=is_ascending_order,json=isAscendingOrder,proto3" json:"is_ascending_order,omitempty"`
	// The type of plan offer for the mutual fund
	PlanType                 mutualfund.PlanType                                   `protobuf:"varint,9,opt,name=plan_type,json=planType,proto3,enum=api.investment.mutualfund.PlanType" json:"plan_type,omitempty"`
	PortfolioPerformanceData *MfPortfolioSecretDataConfig_PortfolioPerformanceData `protobuf:"bytes,10,opt,name=portfolio_performance_data,json=portfolioPerformanceData,proto3" json:"portfolio_performance_data,omitempty"`
}

func (x *MfPortfolioSecretDataConfig) Reset() {
	*x = MfPortfolioSecretDataConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MfPortfolioSecretDataConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MfPortfolioSecretDataConfig) ProtoMessage() {}

func (x *MfPortfolioSecretDataConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MfPortfolioSecretDataConfig.ProtoReflect.Descriptor instead.
func (*MfPortfolioSecretDataConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{3}
}

func (x *MfPortfolioSecretDataConfig) GetMutualFundCategoryName() mutualfund.MutualFundCategoryName {
	if x != nil {
		return x.MutualFundCategoryName
	}
	return mutualfund.MutualFundCategoryName(0)
}

func (x *MfPortfolioSecretDataConfig) GetAssetClass() mutualfund.AssetClass {
	if x != nil {
		return x.AssetClass
	}
	return mutualfund.AssetClass(0)
}

func (x *MfPortfolioSecretDataConfig) GetAumRange() *MfPortfolioSecretDataConfig_AumRange {
	if x != nil {
		return x.AumRange
	}
	return nil
}

func (x *MfPortfolioSecretDataConfig) GetAmc() mutualfund.Amc {
	if x != nil {
		return x.Amc
	}
	return mutualfund.Amc(0)
}

func (x *MfPortfolioSecretDataConfig) GetSecretMetric() MfPortfolioSecretDataConfig_SecretMetric {
	if x != nil {
		return x.SecretMetric
	}
	return MfPortfolioSecretDataConfig_SECRET_METRIC_UNSPECIFIED
}

func (x *MfPortfolioSecretDataConfig) GetSchemesWithMetricCount() *MfPortfolioSecretDataConfig_SchemeCount {
	if x != nil {
		return x.SchemesWithMetricCount
	}
	return nil
}

func (x *MfPortfolioSecretDataConfig) GetCollectionName() []string {
	if x != nil {
		return x.CollectionName
	}
	return nil
}

func (x *MfPortfolioSecretDataConfig) GetIsAscendingOrder() bool {
	if x != nil {
		return x.IsAscendingOrder
	}
	return false
}

func (x *MfPortfolioSecretDataConfig) GetPlanType() mutualfund.PlanType {
	if x != nil {
		return x.PlanType
	}
	return mutualfund.PlanType(0)
}

func (x *MfPortfolioSecretDataConfig) GetPortfolioPerformanceData() *MfPortfolioSecretDataConfig_PortfolioPerformanceData {
	if x != nil {
		return x.PortfolioPerformanceData
	}
	return nil
}

type EpfSecretDataConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EpfSecretMetric    EpfSecretMetric    `protobuf:"varint,1,opt,name=epf_secret_metric,json=epfSecretMetric,proto3,enum=insights.secrets.config.EpfSecretMetric" json:"epf_secret_metric,omitempty"`
	EpfSecretDimension EpfSecretDimension `protobuf:"varint,2,opt,name=epf_secret_dimension,json=epfSecretDimension,proto3,enum=insights.secrets.config.EpfSecretDimension" json:"epf_secret_dimension,omitempty"`
}

func (x *EpfSecretDataConfig) Reset() {
	*x = EpfSecretDataConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EpfSecretDataConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EpfSecretDataConfig) ProtoMessage() {}

func (x *EpfSecretDataConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EpfSecretDataConfig.ProtoReflect.Descriptor instead.
func (*EpfSecretDataConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{4}
}

func (x *EpfSecretDataConfig) GetEpfSecretMetric() EpfSecretMetric {
	if x != nil {
		return x.EpfSecretMetric
	}
	return EpfSecretMetric_EPF_SECRET_METRIC_UNSPECIFIED
}

func (x *EpfSecretDataConfig) GetEpfSecretDimension() EpfSecretDimension {
	if x != nil {
		return x.EpfSecretDimension
	}
	return EpfSecretDimension_EPF_SECRET_DIMENSION_UNSPECIFIED
}

type AssetsSecretDataConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetsSecretMetric AssetsSecretMetric `protobuf:"varint,1,opt,name=assets_secret_metric,json=assetsSecretMetric,proto3,enum=insights.secrets.config.AssetsSecretMetric" json:"assets_secret_metric,omitempty"`
}

func (x *AssetsSecretDataConfig) Reset() {
	*x = AssetsSecretDataConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetsSecretDataConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetsSecretDataConfig) ProtoMessage() {}

func (x *AssetsSecretDataConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetsSecretDataConfig.ProtoReflect.Descriptor instead.
func (*AssetsSecretDataConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{5}
}

func (x *AssetsSecretDataConfig) GetAssetsSecretMetric() AssetsSecretMetric {
	if x != nil {
		return x.AssetsSecretMetric
	}
	return AssetsSecretMetric_ASSETS_SECRET_METRIC_UNSPECIFIED
}

type CreditScoreSecretDataConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreditReportSecrets []string `protobuf:"bytes,1,rep,name=credit_report_secrets,json=creditReportSecrets,proto3" json:"credit_report_secrets,omitempty"`
}

func (x *CreditScoreSecretDataConfig) Reset() {
	*x = CreditScoreSecretDataConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditScoreSecretDataConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditScoreSecretDataConfig) ProtoMessage() {}

func (x *CreditScoreSecretDataConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditScoreSecretDataConfig.ProtoReflect.Descriptor instead.
func (*CreditScoreSecretDataConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{6}
}

func (x *CreditScoreSecretDataConfig) GetCreditReportSecrets() []string {
	if x != nil {
		return x.CreditReportSecrets
	}
	return nil
}

type SecretAnalyserUIConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComponentName string `protobuf:"bytes,1,opt,name=component_name,json=componentName,proto3" json:"component_name,omitempty"`
	// Types that are assignable to Config:
	//
	//	*SecretAnalyserUIConfig_LineItemsConfig
	//	*SecretAnalyserUIConfig_VisualisationCardConfig
	//	*SecretAnalyserUIConfig_LearnOnTheGoSectionConfig
	//	*SecretAnalyserUIConfig_TextExplainerSectionConfig
	//	*SecretAnalyserUIConfig_ActionableBannerConfig
	//	*SecretAnalyserUIConfig_GlobalAnalyserUiComponentConfig
	//	*SecretAnalyserUIConfig_ActionableBannerV2Config
	//	*SecretAnalyserUIConfig_SecretSummaryCardUiConfig
	Config isSecretAnalyserUIConfig_Config `protobuf_oneof:"config"`
	// Optional theme to be applied to the secret analyser ui config. Theme is used as to set zero value fields with default values.
	Theme SecretAnalyserUiTheme `protobuf:"varint,4,opt,name=theme,proto3,enum=insights.secrets.config.SecretAnalyserUiTheme" json:"theme,omitempty"`
	// The flag to be passed as true when the component can expect a null value as well
	IsOptional bool `protobuf:"varint,7,opt,name=is_optional,json=isOptional,proto3" json:"is_optional,omitempty"`
	// represent eligible state for the secret analyser ui component
	EligibleStates            []SecretAnalyserState            `protobuf:"varint,10,rep,packed,name=eligible_states,json=eligibleStates,proto3,enum=insights.secrets.config.SecretAnalyserState" json:"eligible_states,omitempty"`
	RequiredAnalysisVariables []variables.AnalysisVariableName `protobuf:"varint,11,rep,packed,name=required_analysis_variables,json=requiredAnalysisVariables,proto3,enum=api.analyser.variables.AnalysisVariableName" json:"required_analysis_variables,omitempty"`
}

func (x *SecretAnalyserUIConfig) Reset() {
	*x = SecretAnalyserUIConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecretAnalyserUIConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretAnalyserUIConfig) ProtoMessage() {}

func (x *SecretAnalyserUIConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretAnalyserUIConfig.ProtoReflect.Descriptor instead.
func (*SecretAnalyserUIConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{7}
}

func (x *SecretAnalyserUIConfig) GetComponentName() string {
	if x != nil {
		return x.ComponentName
	}
	return ""
}

func (m *SecretAnalyserUIConfig) GetConfig() isSecretAnalyserUIConfig_Config {
	if m != nil {
		return m.Config
	}
	return nil
}

func (x *SecretAnalyserUIConfig) GetLineItemsConfig() *LineItemsConfig {
	if x, ok := x.GetConfig().(*SecretAnalyserUIConfig_LineItemsConfig); ok {
		return x.LineItemsConfig
	}
	return nil
}

func (x *SecretAnalyserUIConfig) GetVisualisationCardConfig() *VisualisationCardConfig {
	if x, ok := x.GetConfig().(*SecretAnalyserUIConfig_VisualisationCardConfig); ok {
		return x.VisualisationCardConfig
	}
	return nil
}

func (x *SecretAnalyserUIConfig) GetLearnOnTheGoSectionConfig() *LearnOnTheGoSectionConfig {
	if x, ok := x.GetConfig().(*SecretAnalyserUIConfig_LearnOnTheGoSectionConfig); ok {
		return x.LearnOnTheGoSectionConfig
	}
	return nil
}

func (x *SecretAnalyserUIConfig) GetTextExplainerSectionConfig() *TextExplainerSectionConfig {
	if x, ok := x.GetConfig().(*SecretAnalyserUIConfig_TextExplainerSectionConfig); ok {
		return x.TextExplainerSectionConfig
	}
	return nil
}

func (x *SecretAnalyserUIConfig) GetActionableBannerConfig() *ActionableBannerConfig {
	if x, ok := x.GetConfig().(*SecretAnalyserUIConfig_ActionableBannerConfig); ok {
		return x.ActionableBannerConfig
	}
	return nil
}

func (x *SecretAnalyserUIConfig) GetGlobalAnalyserUiComponentConfig() *GlobalAnalyserUIComponentConfig {
	if x, ok := x.GetConfig().(*SecretAnalyserUIConfig_GlobalAnalyserUiComponentConfig); ok {
		return x.GlobalAnalyserUiComponentConfig
	}
	return nil
}

func (x *SecretAnalyserUIConfig) GetActionableBannerV2Config() *ActionableBannerV2Config {
	if x, ok := x.GetConfig().(*SecretAnalyserUIConfig_ActionableBannerV2Config); ok {
		return x.ActionableBannerV2Config
	}
	return nil
}

func (x *SecretAnalyserUIConfig) GetSecretSummaryCardUiConfig() *SecretSummaryCardUiConfig {
	if x, ok := x.GetConfig().(*SecretAnalyserUIConfig_SecretSummaryCardUiConfig); ok {
		return x.SecretSummaryCardUiConfig
	}
	return nil
}

func (x *SecretAnalyserUIConfig) GetTheme() SecretAnalyserUiTheme {
	if x != nil {
		return x.Theme
	}
	return SecretAnalyserUiTheme_SECRET_ANALYSER_UI_THEME_UNSPECIFIED
}

func (x *SecretAnalyserUIConfig) GetIsOptional() bool {
	if x != nil {
		return x.IsOptional
	}
	return false
}

func (x *SecretAnalyserUIConfig) GetEligibleStates() []SecretAnalyserState {
	if x != nil {
		return x.EligibleStates
	}
	return nil
}

func (x *SecretAnalyserUIConfig) GetRequiredAnalysisVariables() []variables.AnalysisVariableName {
	if x != nil {
		return x.RequiredAnalysisVariables
	}
	return nil
}

type isSecretAnalyserUIConfig_Config interface {
	isSecretAnalyserUIConfig_Config()
}

type SecretAnalyserUIConfig_LineItemsConfig struct {
	LineItemsConfig *LineItemsConfig `protobuf:"bytes,2,opt,name=line_items_config,json=lineItemsConfig,proto3,oneof"`
}

type SecretAnalyserUIConfig_VisualisationCardConfig struct {
	// Configuration for the visualisation (number card, bar, line chart etc) shown in the secret
	VisualisationCardConfig *VisualisationCardConfig `protobuf:"bytes,3,opt,name=visualisation_card_config,json=visualisationCardConfig,proto3,oneof"`
}

type SecretAnalyserUIConfig_LearnOnTheGoSectionConfig struct {
	LearnOnTheGoSectionConfig *LearnOnTheGoSectionConfig `protobuf:"bytes,5,opt,name=learn_on_the_go_section_config,json=learnOnTheGoSectionConfig,proto3,oneof"`
}

type SecretAnalyserUIConfig_TextExplainerSectionConfig struct {
	TextExplainerSectionConfig *TextExplainerSectionConfig `protobuf:"bytes,6,opt,name=text_explainer_section_config,json=textExplainerSectionConfig,proto3,oneof"`
}

type SecretAnalyserUIConfig_ActionableBannerConfig struct {
	ActionableBannerConfig *ActionableBannerConfig `protobuf:"bytes,8,opt,name=actionable_banner_config,json=actionableBannerConfig,proto3,oneof"`
}

type SecretAnalyserUIConfig_GlobalAnalyserUiComponentConfig struct {
	// Added to avoid redundant UI configuration duplication for trivial elements,
	// e.g., during "learn-on-the-go" UI configuration population used in multiple places
	GlobalAnalyserUiComponentConfig *GlobalAnalyserUIComponentConfig `protobuf:"bytes,9,opt,name=global_analyser_ui_component_config,json=globalAnalyserUiComponentConfig,proto3,oneof"`
}

type SecretAnalyserUIConfig_ActionableBannerV2Config struct {
	// Configuration for action banner
	ActionableBannerV2Config *ActionableBannerV2Config `protobuf:"bytes,12,opt,name=actionable_banner_v2_config,json=actionableBannerV2Config,proto3,oneof"`
}

type SecretAnalyserUIConfig_SecretSummaryCardUiConfig struct {
	// ui config for list secret summary horizontal scrollable component
	SecretSummaryCardUiConfig *SecretSummaryCardUiConfig `protobuf:"bytes,13,opt,name=secret_summary_card_ui_config,json=secretSummaryCardUiConfig,proto3,oneof"`
}

func (*SecretAnalyserUIConfig_LineItemsConfig) isSecretAnalyserUIConfig_Config() {}

func (*SecretAnalyserUIConfig_VisualisationCardConfig) isSecretAnalyserUIConfig_Config() {}

func (*SecretAnalyserUIConfig_LearnOnTheGoSectionConfig) isSecretAnalyserUIConfig_Config() {}

func (*SecretAnalyserUIConfig_TextExplainerSectionConfig) isSecretAnalyserUIConfig_Config() {}

func (*SecretAnalyserUIConfig_ActionableBannerConfig) isSecretAnalyserUIConfig_Config() {}

func (*SecretAnalyserUIConfig_GlobalAnalyserUiComponentConfig) isSecretAnalyserUIConfig_Config() {}

func (*SecretAnalyserUIConfig_ActionableBannerV2Config) isSecretAnalyserUIConfig_Config() {}

func (*SecretAnalyserUIConfig_SecretSummaryCardUiConfig) isSecretAnalyserUIConfig_Config() {}

type GlobalAnalyserUIComponentConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GlobalConfigId string `protobuf:"bytes,1,opt,name=global_config_id,json=globalConfigId,proto3" json:"global_config_id,omitempty"`
}

func (x *GlobalAnalyserUIComponentConfig) Reset() {
	*x = GlobalAnalyserUIComponentConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GlobalAnalyserUIComponentConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GlobalAnalyserUIComponentConfig) ProtoMessage() {}

func (x *GlobalAnalyserUIComponentConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GlobalAnalyserUIComponentConfig.ProtoReflect.Descriptor instead.
func (*GlobalAnalyserUIComponentConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{8}
}

func (x *GlobalAnalyserUIComponentConfig) GetGlobalConfigId() string {
	if x != nil {
		return x.GlobalConfigId
	}
	return ""
}

type SecretSummaryUIConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Config:
	//
	//	*SecretSummaryUIConfig_SecretSummaryCardConfiguration
	Config isSecretSummaryUIConfig_Config `protobuf_oneof:"config"`
	// Optional theme to be applied to the secret summary ui config. Theme is used as to set zero value fields with default values.
	Theme SecretSummaryUiTheme `protobuf:"varint,2,opt,name=theme,proto3,enum=insights.secrets.config.SecretSummaryUiTheme" json:"theme,omitempty"`
}

func (x *SecretSummaryUIConfig) Reset() {
	*x = SecretSummaryUIConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecretSummaryUIConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretSummaryUIConfig) ProtoMessage() {}

func (x *SecretSummaryUIConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretSummaryUIConfig.ProtoReflect.Descriptor instead.
func (*SecretSummaryUIConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{9}
}

func (m *SecretSummaryUIConfig) GetConfig() isSecretSummaryUIConfig_Config {
	if m != nil {
		return m.Config
	}
	return nil
}

func (x *SecretSummaryUIConfig) GetSecretSummaryCardConfiguration() *SecretSummaryCardConfiguration {
	if x, ok := x.GetConfig().(*SecretSummaryUIConfig_SecretSummaryCardConfiguration); ok {
		return x.SecretSummaryCardConfiguration
	}
	return nil
}

func (x *SecretSummaryUIConfig) GetTheme() SecretSummaryUiTheme {
	if x != nil {
		return x.Theme
	}
	return SecretSummaryUiTheme_SECRET_SUMMARY_UI_THEME_UNSPECIFIED
}

type isSecretSummaryUIConfig_Config interface {
	isSecretSummaryUIConfig_Config()
}

type SecretSummaryUIConfig_SecretSummaryCardConfiguration struct {
	SecretSummaryCardConfiguration *SecretSummaryCardConfiguration `protobuf:"bytes,1,opt,name=secret_summary_card_configuration,json=secretSummaryCardConfiguration,proto3,oneof"`
}

func (*SecretSummaryUIConfig_SecretSummaryCardConfiguration) isSecretSummaryUIConfig_Config() {}

type MfPortfolioSecretDataConfig_AumRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Min float64 `protobuf:"fixed64,1,opt,name=min,proto3" json:"min,omitempty"`
	Max float64 `protobuf:"fixed64,2,opt,name=max,proto3" json:"max,omitempty"`
}

func (x *MfPortfolioSecretDataConfig_AumRange) Reset() {
	*x = MfPortfolioSecretDataConfig_AumRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MfPortfolioSecretDataConfig_AumRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MfPortfolioSecretDataConfig_AumRange) ProtoMessage() {}

func (x *MfPortfolioSecretDataConfig_AumRange) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MfPortfolioSecretDataConfig_AumRange.ProtoReflect.Descriptor instead.
func (*MfPortfolioSecretDataConfig_AumRange) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{3, 0}
}

func (x *MfPortfolioSecretDataConfig_AumRange) GetMin() float64 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *MfPortfolioSecretDataConfig_AumRange) GetMax() float64 {
	if x != nil {
		return x.Max
	}
	return 0
}

// Expected number of schemes in the secret. If the count of schemes donot fall in the range the secret will not be generated.
type MfPortfolioSecretDataConfig_SchemeCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Min int32 `protobuf:"varint,1,opt,name=min,proto3" json:"min,omitempty"`
	Max int32 `protobuf:"varint,2,opt,name=max,proto3" json:"max,omitempty"`
}

func (x *MfPortfolioSecretDataConfig_SchemeCount) Reset() {
	*x = MfPortfolioSecretDataConfig_SchemeCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MfPortfolioSecretDataConfig_SchemeCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MfPortfolioSecretDataConfig_SchemeCount) ProtoMessage() {}

func (x *MfPortfolioSecretDataConfig_SchemeCount) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MfPortfolioSecretDataConfig_SchemeCount.ProtoReflect.Descriptor instead.
func (*MfPortfolioSecretDataConfig_SchemeCount) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{3, 1}
}

func (x *MfPortfolioSecretDataConfig_SchemeCount) GetMin() int32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *MfPortfolioSecretDataConfig_SchemeCount) GetMax() int32 {
	if x != nil {
		return x.Max
	}
	return 0
}

// data required to show the performance of the portfolio
type MfPortfolioSecretDataConfig_PortfolioPerformanceData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FixedDeposit float64 `protobuf:"fixed64,1,opt,name=fixed_deposit,json=fixedDeposit,proto3" json:"fixed_deposit,omitempty"`
	Nifty50      float64 `protobuf:"fixed64,2,opt,name=nifty50,proto3" json:"nifty50,omitempty"`
	Peers        float64 `protobuf:"fixed64,3,opt,name=peers,proto3" json:"peers,omitempty"`
}

func (x *MfPortfolioSecretDataConfig_PortfolioPerformanceData) Reset() {
	*x = MfPortfolioSecretDataConfig_PortfolioPerformanceData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MfPortfolioSecretDataConfig_PortfolioPerformanceData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MfPortfolioSecretDataConfig_PortfolioPerformanceData) ProtoMessage() {}

func (x *MfPortfolioSecretDataConfig_PortfolioPerformanceData) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MfPortfolioSecretDataConfig_PortfolioPerformanceData.ProtoReflect.Descriptor instead.
func (*MfPortfolioSecretDataConfig_PortfolioPerformanceData) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP(), []int{3, 2}
}

func (x *MfPortfolioSecretDataConfig_PortfolioPerformanceData) GetFixedDeposit() float64 {
	if x != nil {
		return x.FixedDeposit
	}
	return 0
}

func (x *MfPortfolioSecretDataConfig_PortfolioPerformanceData) GetNifty50() float64 {
	if x != nil {
		return x.Nifty50
	}
	return 0
}

func (x *MfPortfolioSecretDataConfig_PortfolioPerformanceData) GetPeers() float64 {
	if x != nil {
		return x.Peers
	}
	return 0
}

var File_api_insights_secrets_config_secret_builder_config_proto protoreflect.FileDescriptor

var file_api_insights_secrets_config_secret_builder_config_proto_rawDesc = []byte{
	0x0a, 0x37, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x5f, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72,
	0x2f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x69, 0x73, 0x5f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x7a, 0x65, 0x72, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x75, 0x69,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f,
	0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xec, 0x05, 0x0a, 0x17, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x42, 0x75, 0x69, 0x6c,
	0x64, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x72, 0x0a,
	0x19, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x34, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x17, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x7f, 0x0a, 0x20, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x48, 0x00, 0x52, 0x1c, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x7c, 0x0a, 0x1f, 0x6d, 0x66, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c,
	0x69, 0x6f, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69,
	0x6f, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x48, 0x00, 0x52, 0x1b, 0x6d, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x63, 0x0a, 0x16, 0x65, 0x70, 0x66, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x45, 0x70, 0x66, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00,
	0x52, 0x13, 0x65, 0x70, 0x66, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x6c, 0x0a, 0x19, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x5f,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x16, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x7c, 0x0a, 0x1f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x48, 0x00, 0x52, 0x1b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x42, 0x0d, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x22, 0x66, 0x0a, 0x1b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x41, 0x67, 0x67, 0x72,
	0x65, 0x67, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x47, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x99, 0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x79, 0x0a, 0x1e, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x5f, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x34, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x69,
	0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x1b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x69, 0x6d, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0xf1, 0x0a, 0x0a, 0x1b, 0x4d, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x6c, 0x0a, 0x19, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x66,
	0x75, 0x6e, 0x64, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x16, 0x6d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x46, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x0a,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x5a, 0x0a, 0x09, 0x61, 0x75,
	0x6d, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x41, 0x75, 0x6d, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x08, 0x61, 0x75,
	0x6d, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x30, 0x0a, 0x03, 0x61, 0x6d, 0x63, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e,
	0x41, 0x6d, 0x63, 0x52, 0x03, 0x61, 0x6d, 0x63, 0x12, 0x66, 0x0a, 0x0d, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x41, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d, 0x66, 0x50, 0x6f, 0x72, 0x74,
	0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x52, 0x0c, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x12, 0x7b, 0x0a, 0x19, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x5f, 0x77, 0x69, 0x74, 0x68,
	0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d, 0x66,
	0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x16, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x57, 0x69,
	0x74, 0x68, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a,
	0x0f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x61, 0x73, 0x63,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x41, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x8b, 0x01, 0x0a, 0x1a, 0x70, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69,
	0x6f, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x50, 0x65, 0x72, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x18, 0x70, 0x6f, 0x72, 0x74,
	0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x1a, 0x2e, 0x0a, 0x08, 0x41, 0x75, 0x6d, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6d,
	0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x03, 0x6d, 0x61, 0x78, 0x1a, 0x31, 0x0a, 0x0b, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x1a, 0x6f, 0x0a, 0x18, 0x50, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x69, 0x66, 0x74,
	0x79, 0x35, 0x30, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x6e, 0x69, 0x66, 0x74, 0x79,
	0x35, 0x30, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x65, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x05, 0x70, 0x65, 0x65, 0x72, 0x73, 0x22, 0xad, 0x02, 0x0a, 0x0c, 0x53, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x45, 0x43,
	0x52, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x45, 0x43, 0x52,
	0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x58, 0x49, 0x52, 0x52, 0x10, 0x01,
	0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49,
	0x43, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x50, 0x45, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x10, 0x02,
	0x12, 0x17, 0x0a, 0x13, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49,
	0x43, 0x5f, 0x41, 0x4c, 0x50, 0x48, 0x41, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x45, 0x43,
	0x52, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x48, 0x49, 0x54, 0x5f, 0x52,
	0x41, 0x54, 0x45, 0x10, 0x04, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f,
	0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x45, 0x58, 0x50, 0x45, 0x4e, 0x53, 0x45, 0x5f, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54,
	0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x42, 0x41, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x5f,
	0x41, 0x56, 0x47, 0x10, 0x06, 0x12, 0x26, 0x0a, 0x22, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f,
	0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x55, 0x50, 0x53, 0x49, 0x44, 0x45, 0x5f, 0x43, 0x41,
	0x50, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x10, 0x07, 0x12, 0x29, 0x0a,
	0x25, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x44,
	0x4f, 0x57, 0x4e, 0x5f, 0x53, 0x49, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45,
	0x5f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x10, 0x08, 0x22, 0xca, 0x01, 0x0a, 0x13, 0x45, 0x70, 0x66,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x54, 0x0a, 0x11, 0x65, 0x70, 0x66, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x45, 0x70, 0x66, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x0f, 0x65, 0x70, 0x66, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x5d, 0x0a, 0x14, 0x65, 0x70, 0x66, 0x5f, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x5f, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x45,
	0x70, 0x66, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x12, 0x65, 0x70, 0x66, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x69, 0x6d, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x77, 0x0a, 0x16, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x53,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x5d, 0x0a, 0x14, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x12, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x22, 0x51,
	0x0a, 0x1b, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x53, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x32, 0x0a,
	0x15, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x22, 0x94, 0x0a, 0x0a, 0x16, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x65, 0x72, 0x55, 0x49, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x25, 0x0a, 0x0e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x56, 0x0a, 0x11, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x0f, 0x6c, 0x69, 0x6e, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x6e, 0x0a, 0x19, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x48, 0x00, 0x52, 0x17, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x77, 0x0a, 0x1e, 0x6c,
	0x65, 0x61, 0x72, 0x6e, 0x5f, 0x6f, 0x6e, 0x5f, 0x74, 0x68, 0x65, 0x5f, 0x67, 0x6f, 0x5f, 0x73,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4c, 0x65,
	0x61, 0x72, 0x6e, 0x4f, 0x6e, 0x54, 0x68, 0x65, 0x47, 0x6f, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x19, 0x6c, 0x65, 0x61, 0x72, 0x6e,
	0x4f, 0x6e, 0x54, 0x68, 0x65, 0x47, 0x6f, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x78, 0x0a, 0x1d, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x65, 0x78, 0x70,
	0x6c, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x48, 0x00, 0x52, 0x1a, 0x74, 0x65, 0x78, 0x74, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x6b,
	0x0a, 0x18, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x48, 0x00, 0x52, 0x16, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x42,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x88, 0x01, 0x0a, 0x23,
	0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x5f,
	0x75, 0x69, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x65, 0x72, 0x55, 0x49, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x1f, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x65, 0x72, 0x55, 0x69, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x72, 0x0a, 0x1b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x76, 0x32, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x56, 0x32, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00,
	0x52, 0x18, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x56, 0x32, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x76, 0x0a, 0x1d, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x75, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x61, 0x72, 0x64, 0x55, 0x69, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x19, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x61, 0x72, 0x64, 0x55, 0x69, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x44, 0x0a, 0x05, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x55, 0x69, 0x54, 0x68, 0x65, 0x6d,
	0x65, 0x52, 0x05, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69,
	0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x12, 0x55, 0x0a, 0x0f, 0x65, 0x6c, 0x69,
	0x67, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x0e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x73,
	0x12, 0x6c, 0x0a, 0x1b, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x69, 0x73, 0x5f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18,
	0x0b, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x41,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x19, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x69, 0x73, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x42, 0x08,
	0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x4b, 0x0a, 0x1f, 0x47, 0x6c, 0x6f, 0x62,
	0x61, 0x6c, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x55, 0x49, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x28, 0x0a, 0x10, 0x67,
	0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x49, 0x64, 0x22, 0xed, 0x01, 0x0a, 0x15, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x55, 0x49, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x84, 0x01, 0x0a, 0x21, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x1e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x05, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x55, 0x69, 0x54,
	0x68, 0x65, 0x6d, 0x65, 0x52, 0x05, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2a, 0x7e, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44, 0x69, 0x6d, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x2a, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52,
	0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x44, 0x49, 0x4d,
	0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x2f, 0x0a, 0x2b, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52,
	0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x44, 0x49, 0x4d,
	0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x5f, 0x45, 0x4e, 0x51, 0x55,
	0x49, 0x52, 0x59, 0x10, 0x01, 0x2a, 0xe9, 0x03, 0x0a, 0x0f, 0x45, 0x70, 0x66, 0x53, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x50, 0x46,
	0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x32, 0x0a, 0x2e,
	0x45, 0x50, 0x46, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49,
	0x43, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52,
	0x41, 0x57, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x01,
	0x12, 0x33, 0x0a, 0x2f, 0x45, 0x50, 0x46, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x4d,
	0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x4d, 0x41, 0x52, 0x52, 0x49, 0x41, 0x47, 0x45, 0x5f, 0x57,
	0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x42, 0x41, 0x4c, 0x41,
	0x4e, 0x43, 0x45, 0x10, 0x02, 0x12, 0x34, 0x0a, 0x30, 0x45, 0x50, 0x46, 0x5f, 0x53, 0x45, 0x43,
	0x52, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x45, 0x44, 0x55, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41, 0x42, 0x4c,
	0x45, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x03, 0x12, 0x3d, 0x0a, 0x39, 0x45,
	0x50, 0x46, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43,
	0x5f, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41, 0x42, 0x4c, 0x45,
	0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x04, 0x12, 0x3f, 0x0a, 0x3b, 0x45, 0x50,
	0x46, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f,
	0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41, 0x42, 0x4c,
	0x45, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x05, 0x12, 0x3b, 0x0a, 0x37, 0x45,
	0x50, 0x46, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43,
	0x5f, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x52, 0x45, 0x4e, 0x4f, 0x56, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x42,
	0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x06, 0x12, 0x1d, 0x0a, 0x19, 0x45, 0x50, 0x46, 0x5f,
	0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x42, 0x41,
	0x4c, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x07, 0x12, 0x38, 0x0a, 0x34, 0x45, 0x50, 0x46, 0x5f, 0x53,
	0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x4c, 0x41, 0x4e,
	0x44, 0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44,
	0x52, 0x41, 0x57, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x10,
	0x08, 0x2a, 0xf2, 0x01, 0x0a, 0x12, 0x45, 0x70, 0x66, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x44,
	0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x20, 0x45, 0x50, 0x46, 0x5f,
	0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x44, 0x49, 0x4d, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x31,
	0x0a, 0x2d, 0x45, 0x50, 0x46, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x44, 0x49, 0x4d,
	0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x50,
	0x41, 0x53, 0x53, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x10,
	0x01, 0x12, 0x2d, 0x0a, 0x29, 0x45, 0x50, 0x46, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f,
	0x44, 0x49, 0x4d, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52,
	0x41, 0x57, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x02,
	0x12, 0x25, 0x0a, 0x21, 0x45, 0x50, 0x46, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x44,
	0x49, 0x4d, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x49, 0x42,
	0x55, 0x54, 0x4f, 0x52, 0x53, 0x10, 0x03, 0x12, 0x2d, 0x0a, 0x29, 0x45, 0x50, 0x46, 0x5f, 0x53,
	0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x44, 0x49, 0x4d, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x42, 0x41, 0x4c,
	0x41, 0x4e, 0x43, 0x45, 0x10, 0x04, 0x2a, 0x67, 0x0a, 0x12, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x24, 0x0a, 0x20,
	0x41, 0x53, 0x53, 0x45, 0x54, 0x53, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x4d, 0x45,
	0x54, 0x52, 0x49, 0x43, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x2b, 0x0a, 0x27, 0x41, 0x53, 0x53, 0x45, 0x54, 0x53, 0x5f, 0x53, 0x45, 0x43,
	0x52, 0x45, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56,
	0x45, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x10, 0x01, 0x2a,
	0xd2, 0x08, 0x0a, 0x15, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x65, 0x72, 0x55, 0x69, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x24, 0x53, 0x45, 0x43,
	0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x55, 0x49, 0x5f,
	0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x2e, 0x0a, 0x2a, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e,
	0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x52, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x42, 0x45, 0x52, 0x52,
	0x59, 0x10, 0x01, 0x12, 0x2f, 0x0a, 0x2b, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e,
	0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x52, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x44, 0x49,
	0x47, 0x4f, 0x10, 0x02, 0x12, 0x2e, 0x0a, 0x2a, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41,
	0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x52, 0x5f, 0x43, 0x48, 0x41, 0x52,
	0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x41, 0x4d, 0x42,
	0x45, 0x52, 0x10, 0x03, 0x12, 0x2e, 0x0a, 0x2a, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41,
	0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x52, 0x5f, 0x43, 0x48, 0x41, 0x52,
	0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x4f, 0x43, 0x45,
	0x41, 0x4e, 0x10, 0x04, 0x12, 0x2d, 0x0a, 0x29, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41,
	0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x52, 0x5f, 0x43, 0x48, 0x41, 0x52,
	0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x4d, 0x4f, 0x53,
	0x53, 0x10, 0x05, 0x12, 0x2f, 0x0a, 0x2b, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e,
	0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x52, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x52,
	0x52, 0x59, 0x10, 0x06, 0x12, 0x2b, 0x0a, 0x27, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41,
	0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x4f, 0x43, 0x45, 0x41, 0x4e, 0x10,
	0x07, 0x12, 0x2b, 0x0a, 0x27, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c,
	0x59, 0x53, 0x45, 0x52, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x41, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x08, 0x12, 0x2b,
	0x0a, 0x27, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45,
	0x52, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48,
	0x45, 0x4d, 0x45, 0x5f, 0x48, 0x4f, 0x4e, 0x45, 0x59, 0x10, 0x09, 0x12, 0x2c, 0x0a, 0x28, 0x53,
	0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x4c,
	0x49, 0x4e, 0x45, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x53, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f,
	0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x0a, 0x12, 0x2a, 0x0a, 0x26, 0x53, 0x45, 0x43,
	0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x4e, 0x55, 0x4d,
	0x42, 0x45, 0x52, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x4d,
	0x4f, 0x53, 0x53, 0x10, 0x0b, 0x12, 0x2c, 0x0a, 0x28, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f,
	0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x52, 0x52,
	0x59, 0x10, 0x0c, 0x12, 0x2b, 0x0a, 0x27, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e,
	0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x42, 0x45, 0x52, 0x52, 0x59, 0x10, 0x0d,
	0x12, 0x38, 0x0a, 0x34, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59,
	0x53, 0x45, 0x52, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x45, 0x58, 0x50, 0x4c, 0x41, 0x49, 0x4e,
	0x45, 0x52, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45,
	0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x0e, 0x12, 0x3b, 0x0a, 0x37, 0x53, 0x45,
	0x43, 0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48,
	0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x44, 0x45,
	0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x0f, 0x12, 0x44, 0x0a, 0x40, 0x53, 0x45, 0x43, 0x52, 0x45,
	0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x5f,
	0x45, 0x58, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x54, 0x4f, 0x47, 0x47, 0x4c, 0x45, 0x5f, 0x54, 0x48,
	0x45, 0x4d, 0x45, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x10, 0x12, 0x2a, 0x0a,
	0x26, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52,
	0x5f, 0x44, 0x4f, 0x4e, 0x55, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d,
	0x45, 0x5f, 0x4f, 0x43, 0x45, 0x41, 0x4e, 0x10, 0x11, 0x12, 0x42, 0x0a, 0x3e, 0x53, 0x45, 0x43,
	0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x45, 0x44, 0x49,
	0x54, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x54, 0x48,
	0x45, 0x4d, 0x45, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x12, 0x12, 0x29, 0x0a,
	0x25, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52,
	0x5f, 0x44, 0x4f, 0x4e, 0x55, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d,
	0x45, 0x5f, 0x4d, 0x4f, 0x53, 0x53, 0x10, 0x13, 0x12, 0x2e, 0x0a, 0x2a, 0x53, 0x45, 0x43, 0x52,
	0x45, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x52, 0x45, 0x41,
	0x5f, 0x43, 0x48, 0x41, 0x52, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d,
	0x45, 0x5f, 0x4d, 0x4f, 0x53, 0x53, 0x10, 0x14, 0x12, 0x2a, 0x0a, 0x26, 0x53, 0x45, 0x43, 0x52,
	0x45, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x4f, 0x4e, 0x55,
	0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x42, 0x45, 0x52,
	0x52, 0x59, 0x10, 0x15, 0x2a, 0xd0, 0x04, 0x0a, 0x14, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x55, 0x69, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x27, 0x0a,
	0x23, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f,
	0x55, 0x49, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x31, 0x0a, 0x2d, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54,
	0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48,
	0x45, 0x4d, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x42, 0x41, 0x52,
	0x53, 0x5f, 0x42, 0x45, 0x52, 0x52, 0x59, 0x10, 0x01, 0x12, 0x32, 0x0a, 0x2e, 0x53, 0x45, 0x43,
	0x52, 0x45, 0x54, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f,
	0x42, 0x41, 0x52, 0x53, 0x5f, 0x49, 0x4e, 0x44, 0x49, 0x47, 0x4f, 0x10, 0x02, 0x12, 0x31, 0x0a,
	0x2d, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x54, 0x49,
	0x43, 0x41, 0x4c, 0x5f, 0x42, 0x41, 0x52, 0x53, 0x5f, 0x41, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x03,
	0x12, 0x31, 0x0a, 0x2d, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41,
	0x52, 0x59, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x56, 0x45,
	0x52, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x42, 0x41, 0x52, 0x53, 0x5f, 0x4f, 0x43, 0x45, 0x41,
	0x4e, 0x10, 0x04, 0x12, 0x30, 0x0a, 0x2c, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x53, 0x55,
	0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45,
	0x5f, 0x56, 0x45, 0x52, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x42, 0x41, 0x52, 0x53, 0x5f, 0x4d,
	0x4f, 0x53, 0x53, 0x10, 0x05, 0x12, 0x32, 0x0a, 0x2e, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f,
	0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45,
	0x4d, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x42, 0x41, 0x52, 0x53,
	0x5f, 0x43, 0x48, 0x45, 0x52, 0x52, 0x59, 0x10, 0x06, 0x12, 0x23, 0x0a, 0x1f, 0x53, 0x45, 0x43,
	0x52, 0x45, 0x54, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x4f, 0x43, 0x45, 0x41, 0x4e, 0x10, 0x07, 0x12, 0x23,
	0x0a, 0x1f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x41, 0x4d, 0x42, 0x45,
	0x52, 0x10, 0x08, 0x12, 0x23, 0x0a, 0x1f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x53, 0x55,
	0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45,
	0x5f, 0x48, 0x4f, 0x4e, 0x45, 0x59, 0x10, 0x09, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x45, 0x43, 0x52,
	0x45, 0x54, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x4d, 0x4f, 0x53, 0x53, 0x10, 0x0a, 0x12, 0x24, 0x0a, 0x20,
	0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x52, 0x52, 0x59,
	0x10, 0x0b, 0x12, 0x23, 0x0a, 0x1f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x53, 0x55, 0x4d,
	0x4d, 0x41, 0x52, 0x59, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x48, 0x45, 0x4d, 0x45, 0x5f,
	0x42, 0x45, 0x52, 0x52, 0x59, 0x10, 0x0c, 0x2a, 0x82, 0x01, 0x0a, 0x13, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x25, 0x0a, 0x21, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53,
	0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54,
	0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f,
	0x42, 0x41, 0x53, 0x45, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54,
	0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f,
	0x5a, 0x45, 0x52, 0x4f, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0x02, 0x42, 0x68, 0x0a, 0x32,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5a, 0x32, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_insights_secrets_config_secret_builder_config_proto_rawDescOnce sync.Once
	file_api_insights_secrets_config_secret_builder_config_proto_rawDescData = file_api_insights_secrets_config_secret_builder_config_proto_rawDesc
)

func file_api_insights_secrets_config_secret_builder_config_proto_rawDescGZIP() []byte {
	file_api_insights_secrets_config_secret_builder_config_proto_rawDescOnce.Do(func() {
		file_api_insights_secrets_config_secret_builder_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_insights_secrets_config_secret_builder_config_proto_rawDescData)
	})
	return file_api_insights_secrets_config_secret_builder_config_proto_rawDescData
}

var file_api_insights_secrets_config_secret_builder_config_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_api_insights_secrets_config_secret_builder_config_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_api_insights_secrets_config_secret_builder_config_proto_goTypes = []interface{}{
	(CreditReportSecretDimension)(0),                             // 0: insights.secrets.config.CreditReportSecretDimension
	(EpfSecretMetric)(0),                                         // 1: insights.secrets.config.EpfSecretMetric
	(EpfSecretDimension)(0),                                      // 2: insights.secrets.config.EpfSecretDimension
	(AssetsSecretMetric)(0),                                      // 3: insights.secrets.config.AssetsSecretMetric
	(SecretAnalyserUiTheme)(0),                                   // 4: insights.secrets.config.SecretAnalyserUiTheme
	(SecretSummaryUiTheme)(0),                                    // 5: insights.secrets.config.SecretSummaryUiTheme
	(SecretAnalyserState)(0),                                     // 6: insights.secrets.config.SecretAnalyserState
	(MfPortfolioSecretDataConfig_SecretMetric)(0),                // 7: insights.secrets.config.MfPortfolioSecretDataConfig.SecretMetric
	(*SecretBuilderDataConfig)(nil),                              // 8: insights.secrets.config.SecretBuilderDataConfig
	(*CategoryAggregateDataConfig)(nil),                          // 9: insights.secrets.config.CategoryAggregateDataConfig
	(*CreditReportSecretDataConfig)(nil),                         // 10: insights.secrets.config.CreditReportSecretDataConfig
	(*MfPortfolioSecretDataConfig)(nil),                          // 11: insights.secrets.config.MfPortfolioSecretDataConfig
	(*EpfSecretDataConfig)(nil),                                  // 12: insights.secrets.config.EpfSecretDataConfig
	(*AssetsSecretDataConfig)(nil),                               // 13: insights.secrets.config.AssetsSecretDataConfig
	(*CreditScoreSecretDataConfig)(nil),                          // 14: insights.secrets.config.CreditScoreSecretDataConfig
	(*SecretAnalyserUIConfig)(nil),                               // 15: insights.secrets.config.SecretAnalyserUIConfig
	(*GlobalAnalyserUIComponentConfig)(nil),                      // 16: insights.secrets.config.GlobalAnalyserUIComponentConfig
	(*SecretSummaryUIConfig)(nil),                                // 17: insights.secrets.config.SecretSummaryUIConfig
	(*MfPortfolioSecretDataConfig_AumRange)(nil),                 // 18: insights.secrets.config.MfPortfolioSecretDataConfig.AumRange
	(*MfPortfolioSecretDataConfig_SchemeCount)(nil),              // 19: insights.secrets.config.MfPortfolioSecretDataConfig.SchemeCount
	(*MfPortfolioSecretDataConfig_PortfolioPerformanceData)(nil), // 20: insights.secrets.config.MfPortfolioSecretDataConfig.PortfolioPerformanceData
	(categorizer.DisplayCategory)(0),                             // 21: categorizer.DisplayCategory
	(mutualfund.MutualFundCategoryName)(0),                       // 22: api.investment.mutualfund.MutualFundCategoryName
	(mutualfund.AssetClass)(0),                                   // 23: api.investment.mutualfund.AssetClass
	(mutualfund.Amc)(0),                                          // 24: api.investment.mutualfund.Amc
	(mutualfund.PlanType)(0),                                     // 25: api.investment.mutualfund.PlanType
	(*LineItemsConfig)(nil),                                      // 26: insights.secrets.config.LineItemsConfig
	(*VisualisationCardConfig)(nil),                              // 27: insights.secrets.config.VisualisationCardConfig
	(*LearnOnTheGoSectionConfig)(nil),                            // 28: insights.secrets.config.LearnOnTheGoSectionConfig
	(*TextExplainerSectionConfig)(nil),                           // 29: insights.secrets.config.TextExplainerSectionConfig
	(*ActionableBannerConfig)(nil),                               // 30: insights.secrets.config.ActionableBannerConfig
	(*ActionableBannerV2Config)(nil),                             // 31: insights.secrets.config.ActionableBannerV2Config
	(*SecretSummaryCardUiConfig)(nil),                            // 32: insights.secrets.config.SecretSummaryCardUiConfig
	(variables.AnalysisVariableName)(0),                          // 33: api.analyser.variables.AnalysisVariableName
	(*SecretSummaryCardConfiguration)(nil),                       // 34: insights.secrets.config.SecretSummaryCardConfiguration
}
var file_api_insights_secrets_config_secret_builder_config_proto_depIdxs = []int32{
	9,  // 0: insights.secrets.config.SecretBuilderDataConfig.category_aggregate_config:type_name -> insights.secrets.config.CategoryAggregateDataConfig
	10, // 1: insights.secrets.config.SecretBuilderDataConfig.credit_report_secret_data_config:type_name -> insights.secrets.config.CreditReportSecretDataConfig
	11, // 2: insights.secrets.config.SecretBuilderDataConfig.mf_portfolio_secret_data_config:type_name -> insights.secrets.config.MfPortfolioSecretDataConfig
	12, // 3: insights.secrets.config.SecretBuilderDataConfig.epf_secret_data_config:type_name -> insights.secrets.config.EpfSecretDataConfig
	13, // 4: insights.secrets.config.SecretBuilderDataConfig.assets_secret_data_config:type_name -> insights.secrets.config.AssetsSecretDataConfig
	14, // 5: insights.secrets.config.SecretBuilderDataConfig.credit_score_secret_data_config:type_name -> insights.secrets.config.CreditScoreSecretDataConfig
	21, // 6: insights.secrets.config.CategoryAggregateDataConfig.display_category:type_name -> categorizer.DisplayCategory
	0,  // 7: insights.secrets.config.CreditReportSecretDataConfig.credit_report_secret_dimension:type_name -> insights.secrets.config.CreditReportSecretDimension
	22, // 8: insights.secrets.config.MfPortfolioSecretDataConfig.mutual_fund_category_name:type_name -> api.investment.mutualfund.MutualFundCategoryName
	23, // 9: insights.secrets.config.MfPortfolioSecretDataConfig.asset_class:type_name -> api.investment.mutualfund.AssetClass
	18, // 10: insights.secrets.config.MfPortfolioSecretDataConfig.aum_range:type_name -> insights.secrets.config.MfPortfolioSecretDataConfig.AumRange
	24, // 11: insights.secrets.config.MfPortfolioSecretDataConfig.amc:type_name -> api.investment.mutualfund.Amc
	7,  // 12: insights.secrets.config.MfPortfolioSecretDataConfig.secret_metric:type_name -> insights.secrets.config.MfPortfolioSecretDataConfig.SecretMetric
	19, // 13: insights.secrets.config.MfPortfolioSecretDataConfig.schemes_with_metric_count:type_name -> insights.secrets.config.MfPortfolioSecretDataConfig.SchemeCount
	25, // 14: insights.secrets.config.MfPortfolioSecretDataConfig.plan_type:type_name -> api.investment.mutualfund.PlanType
	20, // 15: insights.secrets.config.MfPortfolioSecretDataConfig.portfolio_performance_data:type_name -> insights.secrets.config.MfPortfolioSecretDataConfig.PortfolioPerformanceData
	1,  // 16: insights.secrets.config.EpfSecretDataConfig.epf_secret_metric:type_name -> insights.secrets.config.EpfSecretMetric
	2,  // 17: insights.secrets.config.EpfSecretDataConfig.epf_secret_dimension:type_name -> insights.secrets.config.EpfSecretDimension
	3,  // 18: insights.secrets.config.AssetsSecretDataConfig.assets_secret_metric:type_name -> insights.secrets.config.AssetsSecretMetric
	26, // 19: insights.secrets.config.SecretAnalyserUIConfig.line_items_config:type_name -> insights.secrets.config.LineItemsConfig
	27, // 20: insights.secrets.config.SecretAnalyserUIConfig.visualisation_card_config:type_name -> insights.secrets.config.VisualisationCardConfig
	28, // 21: insights.secrets.config.SecretAnalyserUIConfig.learn_on_the_go_section_config:type_name -> insights.secrets.config.LearnOnTheGoSectionConfig
	29, // 22: insights.secrets.config.SecretAnalyserUIConfig.text_explainer_section_config:type_name -> insights.secrets.config.TextExplainerSectionConfig
	30, // 23: insights.secrets.config.SecretAnalyserUIConfig.actionable_banner_config:type_name -> insights.secrets.config.ActionableBannerConfig
	16, // 24: insights.secrets.config.SecretAnalyserUIConfig.global_analyser_ui_component_config:type_name -> insights.secrets.config.GlobalAnalyserUIComponentConfig
	31, // 25: insights.secrets.config.SecretAnalyserUIConfig.actionable_banner_v2_config:type_name -> insights.secrets.config.ActionableBannerV2Config
	32, // 26: insights.secrets.config.SecretAnalyserUIConfig.secret_summary_card_ui_config:type_name -> insights.secrets.config.SecretSummaryCardUiConfig
	4,  // 27: insights.secrets.config.SecretAnalyserUIConfig.theme:type_name -> insights.secrets.config.SecretAnalyserUiTheme
	6,  // 28: insights.secrets.config.SecretAnalyserUIConfig.eligible_states:type_name -> insights.secrets.config.SecretAnalyserState
	33, // 29: insights.secrets.config.SecretAnalyserUIConfig.required_analysis_variables:type_name -> api.analyser.variables.AnalysisVariableName
	34, // 30: insights.secrets.config.SecretSummaryUIConfig.secret_summary_card_configuration:type_name -> insights.secrets.config.SecretSummaryCardConfiguration
	5,  // 31: insights.secrets.config.SecretSummaryUIConfig.theme:type_name -> insights.secrets.config.SecretSummaryUiTheme
	32, // [32:32] is the sub-list for method output_type
	32, // [32:32] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_api_insights_secrets_config_secret_builder_config_proto_init() }
func file_api_insights_secrets_config_secret_builder_config_proto_init() {
	if File_api_insights_secrets_config_secret_builder_config_proto != nil {
		return
	}
	file_api_insights_secrets_config_ui_configuration_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecretBuilderDataConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CategoryAggregateDataConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditReportSecretDataConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MfPortfolioSecretDataConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EpfSecretDataConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetsSecretDataConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditScoreSecretDataConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecretAnalyserUIConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GlobalAnalyserUIComponentConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecretSummaryUIConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MfPortfolioSecretDataConfig_AumRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MfPortfolioSecretDataConfig_SchemeCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MfPortfolioSecretDataConfig_PortfolioPerformanceData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*SecretBuilderDataConfig_CategoryAggregateConfig)(nil),
		(*SecretBuilderDataConfig_CreditReportSecretDataConfig)(nil),
		(*SecretBuilderDataConfig_MfPortfolioSecretDataConfig)(nil),
		(*SecretBuilderDataConfig_EpfSecretDataConfig)(nil),
		(*SecretBuilderDataConfig_AssetsSecretDataConfig)(nil),
		(*SecretBuilderDataConfig_CreditScoreSecretDataConfig)(nil),
	}
	file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*SecretAnalyserUIConfig_LineItemsConfig)(nil),
		(*SecretAnalyserUIConfig_VisualisationCardConfig)(nil),
		(*SecretAnalyserUIConfig_LearnOnTheGoSectionConfig)(nil),
		(*SecretAnalyserUIConfig_TextExplainerSectionConfig)(nil),
		(*SecretAnalyserUIConfig_ActionableBannerConfig)(nil),
		(*SecretAnalyserUIConfig_GlobalAnalyserUiComponentConfig)(nil),
		(*SecretAnalyserUIConfig_ActionableBannerV2Config)(nil),
		(*SecretAnalyserUIConfig_SecretSummaryCardUiConfig)(nil),
	}
	file_api_insights_secrets_config_secret_builder_config_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*SecretSummaryUIConfig_SecretSummaryCardConfiguration)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_insights_secrets_config_secret_builder_config_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_insights_secrets_config_secret_builder_config_proto_goTypes,
		DependencyIndexes: file_api_insights_secrets_config_secret_builder_config_proto_depIdxs,
		EnumInfos:         file_api_insights_secrets_config_secret_builder_config_proto_enumTypes,
		MessageInfos:      file_api_insights_secrets_config_secret_builder_config_proto_msgTypes,
	}.Build()
	File_api_insights_secrets_config_secret_builder_config_proto = out.File
	file_api_insights_secrets_config_secret_builder_config_proto_rawDesc = nil
	file_api_insights_secrets_config_secret_builder_config_proto_goTypes = nil
	file_api_insights_secrets_config_secret_builder_config_proto_depIdxs = nil
}
