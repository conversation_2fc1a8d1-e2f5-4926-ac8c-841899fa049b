// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/insights/secrets/config/ui_configuration.proto

package config

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SecretSummaryDisabledState int32

const (
	SecretSummaryDisabledState_SECRET_SUMMARY_DISABLED_STATE_UNSPECIFIED SecretSummaryDisabledState = 0
	// Shows the secret with coming soon tag
	SecretSummaryDisabledState_SSDS_COMING_SOON SecretSummaryDisabledState = 1
	// Shows the locked secret with unlock secret tag
	SecretSummaryDisabledState_SSDS_UNLOCK_SECRET SecretSummaryDisabledState = 2
)

// Enum value maps for SecretSummaryDisabledState.
var (
	SecretSummaryDisabledState_name = map[int32]string{
		0: "SECRET_SUMMARY_DISABLED_STATE_UNSPECIFIED",
		1: "SSDS_COMING_SOON",
		2: "SSDS_UNLOCK_SECRET",
	}
	SecretSummaryDisabledState_value = map[string]int32{
		"SECRET_SUMMARY_DISABLED_STATE_UNSPECIFIED": 0,
		"SSDS_COMING_SOON":                          1,
		"SSDS_UNLOCK_SECRET":                        2,
	}
)

func (x SecretSummaryDisabledState) Enum() *SecretSummaryDisabledState {
	p := new(SecretSummaryDisabledState)
	*p = x
	return p
}

func (x SecretSummaryDisabledState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SecretSummaryDisabledState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_secrets_config_ui_configuration_proto_enumTypes[0].Descriptor()
}

func (SecretSummaryDisabledState) Type() protoreflect.EnumType {
	return &file_api_insights_secrets_config_ui_configuration_proto_enumTypes[0]
}

func (x SecretSummaryDisabledState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SecretSummaryDisabledState.Descriptor instead.
func (SecretSummaryDisabledState) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{0}
}

// Configuration powering api.typesv2.ui.assetsandanalysis.SecretSummaryCard.
type SecretSummaryCardConfiguration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleConfig *ITCConfiguration `protobuf:"bytes,1,opt,name=title_config,json=titleConfig,proto3" json:"title_config,omitempty"`
	// Only configures the text styling of the value, we would not be configuring the complete ITC
	ValueConfig         *TextConfiguration          `protobuf:"bytes,2,opt,name=value_config,json=valueConfig,proto3" json:"value_config,omitempty"`
	VisualisationConfig *VisualisationConfiguration `protobuf:"bytes,3,opt,name=visualisation_config,json=visualisationConfig,proto3" json:"visualisation_config,omitempty"`
	BgColor             string                      `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	BorderColor         string                      `protobuf:"bytes,5,opt,name=border_color,json=borderColor,proto3" json:"border_color,omitempty"`
	// If the user does not provide data for the formation of the secret, this value will be used to display a zero state text, which will be shaded on the client side.
	ZeroStateSecretValue string `protobuf:"bytes,6,opt,name=zero_state_secret_value,json=zeroStateSecretValue,proto3" json:"zero_state_secret_value,omitempty"`
	// Used for configuring how we wish to show the user the secret summary if the secret is disabled or unavailable.
	SecretSummaryDisabledState SecretSummaryDisabledState `protobuf:"varint,7,opt,name=secret_summary_disabled_state,json=secretSummaryDisabledState,proto3,enum=insights.secrets.config.SecretSummaryDisabledState" json:"secret_summary_disabled_state,omitempty"`
}

func (x *SecretSummaryCardConfiguration) Reset() {
	*x = SecretSummaryCardConfiguration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecretSummaryCardConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretSummaryCardConfiguration) ProtoMessage() {}

func (x *SecretSummaryCardConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretSummaryCardConfiguration.ProtoReflect.Descriptor instead.
func (*SecretSummaryCardConfiguration) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{0}
}

func (x *SecretSummaryCardConfiguration) GetTitleConfig() *ITCConfiguration {
	if x != nil {
		return x.TitleConfig
	}
	return nil
}

func (x *SecretSummaryCardConfiguration) GetValueConfig() *TextConfiguration {
	if x != nil {
		return x.ValueConfig
	}
	return nil
}

func (x *SecretSummaryCardConfiguration) GetVisualisationConfig() *VisualisationConfiguration {
	if x != nil {
		return x.VisualisationConfig
	}
	return nil
}

func (x *SecretSummaryCardConfiguration) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *SecretSummaryCardConfiguration) GetBorderColor() string {
	if x != nil {
		return x.BorderColor
	}
	return ""
}

func (x *SecretSummaryCardConfiguration) GetZeroStateSecretValue() string {
	if x != nil {
		return x.ZeroStateSecretValue
	}
	return ""
}

func (x *SecretSummaryCardConfiguration) GetSecretSummaryDisabledState() SecretSummaryDisabledState {
	if x != nil {
		return x.SecretSummaryDisabledState
	}
	return SecretSummaryDisabledState_SECRET_SUMMARY_DISABLED_STATE_UNSPECIFIED
}

type ITCConfiguration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeftVisualisationConfig  *VisualisationConfiguration               `protobuf:"bytes,1,opt,name=left_visualisation_config,json=leftVisualisationConfig,proto3" json:"left_visualisation_config,omitempty"`
	TextConfigs              []*TextConfiguration                      `protobuf:"bytes,2,rep,name=text_configs,json=textConfigs,proto3" json:"text_configs,omitempty"`
	RightVisualisationConfig *VisualisationConfiguration               `protobuf:"bytes,3,opt,name=right_visualisation_config,json=rightVisualisationConfig,proto3" json:"right_visualisation_config,omitempty"`
	BorderConfig             *ITCConfiguration_BorderConfig            `protobuf:"bytes,4,opt,name=border_config,json=borderConfig,proto3" json:"border_config,omitempty"`
	BgColor                  string                                    `protobuf:"bytes,5,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	ContainerProperties      *ui.IconTextComponent_ContainerProperties `protobuf:"bytes,6,opt,name=container_properties,json=containerProperties,proto3" json:"container_properties,omitempty"`
	Deeplink                 *deeplink.Deeplink                        `protobuf:"bytes,7,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *ITCConfiguration) Reset() {
	*x = ITCConfiguration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ITCConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ITCConfiguration) ProtoMessage() {}

func (x *ITCConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ITCConfiguration.ProtoReflect.Descriptor instead.
func (*ITCConfiguration) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{1}
}

func (x *ITCConfiguration) GetLeftVisualisationConfig() *VisualisationConfiguration {
	if x != nil {
		return x.LeftVisualisationConfig
	}
	return nil
}

func (x *ITCConfiguration) GetTextConfigs() []*TextConfiguration {
	if x != nil {
		return x.TextConfigs
	}
	return nil
}

func (x *ITCConfiguration) GetRightVisualisationConfig() *VisualisationConfiguration {
	if x != nil {
		return x.RightVisualisationConfig
	}
	return nil
}

func (x *ITCConfiguration) GetBorderConfig() *ITCConfiguration_BorderConfig {
	if x != nil {
		return x.BorderConfig
	}
	return nil
}

func (x *ITCConfiguration) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *ITCConfiguration) GetContainerProperties() *ui.IconTextComponent_ContainerProperties {
	if x != nil {
		return x.ContainerProperties
	}
	return nil
}

func (x *ITCConfiguration) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

type VerticalITCConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleConfig *ITCConfiguration `protobuf:"bytes,1,opt,name=title_config,json=titleConfig,proto3" json:"title_config,omitempty"`
	ValueConfig *ITCConfiguration `protobuf:"bytes,2,opt,name=value_config,json=valueConfig,proto3" json:"value_config,omitempty"`
}

func (x *VerticalITCConfig) Reset() {
	*x = VerticalITCConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerticalITCConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerticalITCConfig) ProtoMessage() {}

func (x *VerticalITCConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerticalITCConfig.ProtoReflect.Descriptor instead.
func (*VerticalITCConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{2}
}

func (x *VerticalITCConfig) GetTitleConfig() *ITCConfiguration {
	if x != nil {
		return x.TitleConfig
	}
	return nil
}

func (x *VerticalITCConfig) GetValueConfig() *ITCConfiguration {
	if x != nil {
		return x.ValueConfig
	}
	return nil
}

type TextConfiguration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Color of text font
	Color     string           `protobuf:"bytes,1,opt,name=color,proto3" json:"color,omitempty"`
	FontStyle common.FontStyle `protobuf:"varint,2,opt,name=font_style,json=fontStyle,proto3,enum=api.typesv2.common.FontStyle" json:"font_style,omitempty"`
	// optional text
	Text            string           `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	DisplayTemplate *DisplayTemplate `protobuf:"bytes,4,opt,name=display_template,json=displayTemplate,proto3" json:"display_template,omitempty"`
	// Background color of text
	BgColor       string                    `protobuf:"bytes,5,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	TextAlignment common.Text_TextAlignment `protobuf:"varint,6,opt,name=text_alignment,json=textAlignment,proto3,enum=api.typesv2.common.Text_TextAlignment" json:"text_alignment,omitempty"`
}

func (x *TextConfiguration) Reset() {
	*x = TextConfiguration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextConfiguration) ProtoMessage() {}

func (x *TextConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextConfiguration.ProtoReflect.Descriptor instead.
func (*TextConfiguration) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{3}
}

func (x *TextConfiguration) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *TextConfiguration) GetFontStyle() common.FontStyle {
	if x != nil {
		return x.FontStyle
	}
	return common.FontStyle(0)
}

func (x *TextConfiguration) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *TextConfiguration) GetDisplayTemplate() *DisplayTemplate {
	if x != nil {
		return x.DisplayTemplate
	}
	return nil
}

func (x *TextConfiguration) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *TextConfiguration) GetTextAlignment() common.Text_TextAlignment {
	if x != nil {
		return x.TextAlignment
	}
	return common.Text_TextAlignment(0)
}

// This object helps replace dynamic content with the corresponding computed secret text.
type DisplayTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This text is used to replace a placeholder with a specific input value.
	// For example:
	//
	//	expression_text := "your {input} is the fee"
	//
	// It will be rendered in the UI as "your 0.5$ is the fee" if the input is 0.5$.
	// The placeholder {input} is replaced with the user-provided value.
	// note: currently replacing of text is done for {input} value only
	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
}

func (x *DisplayTemplate) Reset() {
	*x = DisplayTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisplayTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayTemplate) ProtoMessage() {}

func (x *DisplayTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayTemplate.ProtoReflect.Descriptor instead.
func (*DisplayTemplate) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{4}
}

func (x *DisplayTemplate) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type VisualisationConfiguration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageUrl string `protobuf:"bytes,1,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	// Height of image
	Height int32 `protobuf:"varint,2,opt,name=height,proto3" json:"height,omitempty"`
	// Width of image
	Width int32 `protobuf:"varint,3,opt,name=width,proto3" json:"width,omitempty"`
}

func (x *VisualisationConfiguration) Reset() {
	*x = VisualisationConfiguration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VisualisationConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VisualisationConfiguration) ProtoMessage() {}

func (x *VisualisationConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VisualisationConfiguration.ProtoReflect.Descriptor instead.
func (*VisualisationConfiguration) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{5}
}

func (x *VisualisationConfiguration) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *VisualisationConfiguration) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *VisualisationConfiguration) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

type VisualisationCardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProTipConfig *ITCConfiguration `protobuf:"bytes,1,opt,name=pro_tip_config,json=proTipConfig,proto3" json:"pro_tip_config,omitempty"`
	// Types that are assignable to Visualisation:
	//
	//	*VisualisationCardConfig_NumberCardConfig
	//	*VisualisationCardConfig_BarChartCardConfig
	//	*VisualisationCardConfig_ImageCardConfig
	//	*VisualisationCardConfig_AreaChartCardConfig
	//	*VisualisationCardConfig_GridVisualisationCardConfig
	//	*VisualisationCardConfig_DonutCardConfig
	Visualisation isVisualisationCardConfig_Visualisation `protobuf_oneof:"visualisation"`
}

func (x *VisualisationCardConfig) Reset() {
	*x = VisualisationCardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VisualisationCardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VisualisationCardConfig) ProtoMessage() {}

func (x *VisualisationCardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VisualisationCardConfig.ProtoReflect.Descriptor instead.
func (*VisualisationCardConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{6}
}

func (x *VisualisationCardConfig) GetProTipConfig() *ITCConfiguration {
	if x != nil {
		return x.ProTipConfig
	}
	return nil
}

func (m *VisualisationCardConfig) GetVisualisation() isVisualisationCardConfig_Visualisation {
	if m != nil {
		return m.Visualisation
	}
	return nil
}

func (x *VisualisationCardConfig) GetNumberCardConfig() *NumberCardConfig {
	if x, ok := x.GetVisualisation().(*VisualisationCardConfig_NumberCardConfig); ok {
		return x.NumberCardConfig
	}
	return nil
}

func (x *VisualisationCardConfig) GetBarChartCardConfig() *BarChartCardConfig {
	if x, ok := x.GetVisualisation().(*VisualisationCardConfig_BarChartCardConfig); ok {
		return x.BarChartCardConfig
	}
	return nil
}

func (x *VisualisationCardConfig) GetImageCardConfig() *ImageCardConfig {
	if x, ok := x.GetVisualisation().(*VisualisationCardConfig_ImageCardConfig); ok {
		return x.ImageCardConfig
	}
	return nil
}

func (x *VisualisationCardConfig) GetAreaChartCardConfig() *AreaChartCardConfig {
	if x, ok := x.GetVisualisation().(*VisualisationCardConfig_AreaChartCardConfig); ok {
		return x.AreaChartCardConfig
	}
	return nil
}

func (x *VisualisationCardConfig) GetGridVisualisationCardConfig() *GridVisualisationCardConfig {
	if x, ok := x.GetVisualisation().(*VisualisationCardConfig_GridVisualisationCardConfig); ok {
		return x.GridVisualisationCardConfig
	}
	return nil
}

func (x *VisualisationCardConfig) GetDonutCardConfig() *DonutCardConfig {
	if x, ok := x.GetVisualisation().(*VisualisationCardConfig_DonutCardConfig); ok {
		return x.DonutCardConfig
	}
	return nil
}

type isVisualisationCardConfig_Visualisation interface {
	isVisualisationCardConfig_Visualisation()
}

type VisualisationCardConfig_NumberCardConfig struct {
	NumberCardConfig *NumberCardConfig `protobuf:"bytes,2,opt,name=number_card_config,json=numberCardConfig,proto3,oneof"`
}

type VisualisationCardConfig_BarChartCardConfig struct {
	BarChartCardConfig *BarChartCardConfig `protobuf:"bytes,3,opt,name=bar_chart_card_config,json=barChartCardConfig,proto3,oneof"`
}

type VisualisationCardConfig_ImageCardConfig struct {
	ImageCardConfig *ImageCardConfig `protobuf:"bytes,4,opt,name=image_card_config,json=imageCardConfig,proto3,oneof"`
}

type VisualisationCardConfig_AreaChartCardConfig struct {
	AreaChartCardConfig *AreaChartCardConfig `protobuf:"bytes,5,opt,name=area_chart_card_config,json=areaChartCardConfig,proto3,oneof"`
}

type VisualisationCardConfig_GridVisualisationCardConfig struct {
	GridVisualisationCardConfig *GridVisualisationCardConfig `protobuf:"bytes,6,opt,name=grid_visualisation_card_config,json=gridVisualisationCardConfig,proto3,oneof"`
}

type VisualisationCardConfig_DonutCardConfig struct {
	DonutCardConfig *DonutCardConfig `protobuf:"bytes,7,opt,name=donut_card_config,json=donutCardConfig,proto3,oneof"`
}

func (*VisualisationCardConfig_NumberCardConfig) isVisualisationCardConfig_Visualisation() {}

func (*VisualisationCardConfig_BarChartCardConfig) isVisualisationCardConfig_Visualisation() {}

func (*VisualisationCardConfig_ImageCardConfig) isVisualisationCardConfig_Visualisation() {}

func (*VisualisationCardConfig_AreaChartCardConfig) isVisualisationCardConfig_Visualisation() {}

func (*VisualisationCardConfig_GridVisualisationCardConfig) isVisualisationCardConfig_Visualisation() {
}

func (*VisualisationCardConfig_DonutCardConfig) isVisualisationCardConfig_Visualisation() {}

// Config powering Number Card
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16176-31409&t=aSPJa0l9AITF7Sr1-4
type NumberCardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleValueConfig *VerticalITCConfig          `protobuf:"bytes,1,opt,name=title_value_config,json=titleValueConfig,proto3" json:"title_value_config,omitempty"`
	ImageConfig      *VisualisationConfiguration `protobuf:"bytes,2,opt,name=image_config,json=imageConfig,proto3" json:"image_config,omitempty"`
	BgColor          string                      `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *NumberCardConfig) Reset() {
	*x = NumberCardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NumberCardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NumberCardConfig) ProtoMessage() {}

func (x *NumberCardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NumberCardConfig.ProtoReflect.Descriptor instead.
func (*NumberCardConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{7}
}

func (x *NumberCardConfig) GetTitleValueConfig() *VerticalITCConfig {
	if x != nil {
		return x.TitleValueConfig
	}
	return nil
}

func (x *NumberCardConfig) GetImageConfig() *VisualisationConfiguration {
	if x != nil {
		return x.ImageConfig
	}
	return nil
}

func (x *NumberCardConfig) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

type BarChartCardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Config for a title and its value, e.g. 'Your top-performing Mutual Fund' and 'ICICI Pru Dividend fund'
	TitleValueConfig *VerticalITCConfig `protobuf:"bytes,1,opt,name=title_value_config,json=titleValueConfig,proto3" json:"title_value_config,omitempty"`
	// Config for a score or insight value, e.g. XIRR of the top performing MF '+24.5%'
	ScoreInsightConfig *ITCConfiguration `protobuf:"bytes,2,opt,name=score_insight_config,json=scoreInsightConfig,proto3" json:"score_insight_config,omitempty"`
	// Config for a bar chart
	BarChartConfig *BarChartConfig `protobuf:"bytes,3,opt,name=bar_chart_config,json=barChartConfig,proto3" json:"bar_chart_config,omitempty"`
	// Background color of card
	BgColor string `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// refrence line
	ReferenceLine *ReferenceLine `protobuf:"bytes,5,opt,name=reference_line,json=referenceLine,proto3" json:"reference_line,omitempty"`
}

func (x *BarChartCardConfig) Reset() {
	*x = BarChartCardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BarChartCardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BarChartCardConfig) ProtoMessage() {}

func (x *BarChartCardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BarChartCardConfig.ProtoReflect.Descriptor instead.
func (*BarChartCardConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{8}
}

func (x *BarChartCardConfig) GetTitleValueConfig() *VerticalITCConfig {
	if x != nil {
		return x.TitleValueConfig
	}
	return nil
}

func (x *BarChartCardConfig) GetScoreInsightConfig() *ITCConfiguration {
	if x != nil {
		return x.ScoreInsightConfig
	}
	return nil
}

func (x *BarChartCardConfig) GetBarChartConfig() *BarChartConfig {
	if x != nil {
		return x.BarChartConfig
	}
	return nil
}

func (x *BarChartCardConfig) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *BarChartCardConfig) GetReferenceLine() *ReferenceLine {
	if x != nil {
		return x.ReferenceLine
	}
	return nil
}

type ReferenceLine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleConfig *ITCConfiguration `protobuf:"bytes,1,opt,name=title_config,json=titleConfig,proto3" json:"title_config,omitempty"`
	LineColor   string            `protobuf:"bytes,2,opt,name=line_color,json=lineColor,proto3" json:"line_color,omitempty"`
}

func (x *ReferenceLine) Reset() {
	*x = ReferenceLine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReferenceLine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReferenceLine) ProtoMessage() {}

func (x *ReferenceLine) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReferenceLine.ProtoReflect.Descriptor instead.
func (*ReferenceLine) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{9}
}

func (x *ReferenceLine) GetTitleConfig() *ITCConfiguration {
	if x != nil {
		return x.TitleConfig
	}
	return nil
}

func (x *ReferenceLine) GetLineColor() string {
	if x != nil {
		return x.LineColor
	}
	return ""
}

// Config for a bar chart
type BarChartConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DefaultBarConfig *BarConfig `protobuf:"bytes,1,opt,name=default_bar_config,json=defaultBarConfig,proto3" json:"default_bar_config,omitempty"` // ToDo: add config for individual bars if required (unlikely)
}

func (x *BarChartConfig) Reset() {
	*x = BarChartConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BarChartConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BarChartConfig) ProtoMessage() {}

func (x *BarChartConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BarChartConfig.ProtoReflect.Descriptor instead.
func (*BarChartConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{10}
}

func (x *BarChartConfig) GetDefaultBarConfig() *BarConfig {
	if x != nil {
		return x.DefaultBarConfig
	}
	return nil
}

// Config for a single bar of the bar chart
type BarConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Config for the component on top of the bar for a positive value
	// and below the bar for a negative value
	TopDisplayComponentConfig *BarDisplayComponentConfig `protobuf:"bytes,1,opt,name=top_display_component_config,json=topDisplayComponentConfig,proto3" json:"top_display_component_config,omitempty"`
	// Config for the component below the bar axis for a positive value
	// and above the bar axis for a negative value
	BottomDisplayComponentConfig *BarDisplayComponentConfig `protobuf:"bytes,2,opt,name=bottom_display_component_config,json=bottomDisplayComponentConfig,proto3" json:"bottom_display_component_config,omitempty"`
	// Color of a bar
	BarColor string `protobuf:"bytes,3,opt,name=bar_color,json=barColor,proto3" json:"bar_color,omitempty"`
}

func (x *BarConfig) Reset() {
	*x = BarConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BarConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BarConfig) ProtoMessage() {}

func (x *BarConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BarConfig.ProtoReflect.Descriptor instead.
func (*BarConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{11}
}

func (x *BarConfig) GetTopDisplayComponentConfig() *BarDisplayComponentConfig {
	if x != nil {
		return x.TopDisplayComponentConfig
	}
	return nil
}

func (x *BarConfig) GetBottomDisplayComponentConfig() *BarDisplayComponentConfig {
	if x != nil {
		return x.BottomDisplayComponentConfig
	}
	return nil
}

func (x *BarConfig) GetBarColor() string {
	if x != nil {
		return x.BarColor
	}
	return ""
}

type BarDisplayComponentConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Config:
	//
	//	*BarDisplayComponentConfig_ImageConfig
	//	*BarDisplayComponentConfig_DisplayTextConfig
	Config isBarDisplayComponentConfig_Config `protobuf_oneof:"config"`
}

func (x *BarDisplayComponentConfig) Reset() {
	*x = BarDisplayComponentConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BarDisplayComponentConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BarDisplayComponentConfig) ProtoMessage() {}

func (x *BarDisplayComponentConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BarDisplayComponentConfig.ProtoReflect.Descriptor instead.
func (*BarDisplayComponentConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{12}
}

func (m *BarDisplayComponentConfig) GetConfig() isBarDisplayComponentConfig_Config {
	if m != nil {
		return m.Config
	}
	return nil
}

func (x *BarDisplayComponentConfig) GetImageConfig() *VisualisationConfiguration {
	if x, ok := x.GetConfig().(*BarDisplayComponentConfig_ImageConfig); ok {
		return x.ImageConfig
	}
	return nil
}

func (x *BarDisplayComponentConfig) GetDisplayTextConfig() *ITCConfiguration {
	if x, ok := x.GetConfig().(*BarDisplayComponentConfig_DisplayTextConfig); ok {
		return x.DisplayTextConfig
	}
	return nil
}

type isBarDisplayComponentConfig_Config interface {
	isBarDisplayComponentConfig_Config()
}

type BarDisplayComponentConfig_ImageConfig struct {
	ImageConfig *VisualisationConfiguration `protobuf:"bytes,1,opt,name=image_config,json=imageConfig,proto3,oneof"`
}

type BarDisplayComponentConfig_DisplayTextConfig struct {
	DisplayTextConfig *ITCConfiguration `protobuf:"bytes,2,opt,name=display_text_config,json=displayTextConfig,proto3,oneof"`
}

func (*BarDisplayComponentConfig_ImageConfig) isBarDisplayComponentConfig_Config() {}

func (*BarDisplayComponentConfig_DisplayTextConfig) isBarDisplayComponentConfig_Config() {}

// Config to control styling and content of an image card
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16171-24623&t=x8vnu5NlxmgXddE9-4
type ImageCardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleConfig           *ITCConfiguration                     `protobuf:"bytes,1,opt,name=title_config,json=titleConfig,proto3" json:"title_config,omitempty"`
	BackgroundImageConfig *VisualisationConfiguration           `protobuf:"bytes,2,opt,name=background_image_config,json=backgroundImageConfig,proto3" json:"background_image_config,omitempty"`
	BackgroundColor       string                                `protobuf:"bytes,3,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	ValueComponentConfig  *ImageCardConfig_ValueComponentConfig `protobuf:"bytes,4,opt,name=value_component_config,json=valueComponentConfig,proto3" json:"value_component_config,omitempty"`
}

func (x *ImageCardConfig) Reset() {
	*x = ImageCardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageCardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageCardConfig) ProtoMessage() {}

func (x *ImageCardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageCardConfig.ProtoReflect.Descriptor instead.
func (*ImageCardConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{13}
}

func (x *ImageCardConfig) GetTitleConfig() *ITCConfiguration {
	if x != nil {
		return x.TitleConfig
	}
	return nil
}

func (x *ImageCardConfig) GetBackgroundImageConfig() *VisualisationConfiguration {
	if x != nil {
		return x.BackgroundImageConfig
	}
	return nil
}

func (x *ImageCardConfig) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

func (x *ImageCardConfig) GetValueComponentConfig() *ImageCardConfig_ValueComponentConfig {
	if x != nil {
		return x.ValueComponentConfig
	}
	return nil
}

// LineItemsConfig defines ui configuration of a group line items
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16175-29794&t=58SDplXF2rqHeMK6-4
type LineItemsConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LineItemConfig *LineItemConfig `protobuf:"bytes,1,opt,name=line_item_config,json=lineItemConfig,proto3" json:"line_item_config,omitempty"`
	// Config for left-side header row for line items
	LeftAlignedHeadingConfig *TextConfiguration `protobuf:"bytes,2,opt,name=left_aligned_heading_config,json=leftAlignedHeadingConfig,proto3" json:"left_aligned_heading_config,omitempty"`
	// Config for right-side header row for line items
	RightAlignedHeadingConfig *TextConfiguration `protobuf:"bytes,3,opt,name=right_aligned_heading_config,json=rightAlignedHeadingConfig,proto3" json:"right_aligned_heading_config,omitempty"`
}

func (x *LineItemsConfig) Reset() {
	*x = LineItemsConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LineItemsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LineItemsConfig) ProtoMessage() {}

func (x *LineItemsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LineItemsConfig.ProtoReflect.Descriptor instead.
func (*LineItemsConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{14}
}

func (x *LineItemsConfig) GetLineItemConfig() *LineItemConfig {
	if x != nil {
		return x.LineItemConfig
	}
	return nil
}

func (x *LineItemsConfig) GetLeftAlignedHeadingConfig() *TextConfiguration {
	if x != nil {
		return x.LeftAlignedHeadingConfig
	}
	return nil
}

func (x *LineItemsConfig) GetRightAlignedHeadingConfig() *TextConfiguration {
	if x != nil {
		return x.RightAlignedHeadingConfig
	}
	return nil
}

// LineItemConfig defines configuration of individual line item
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16175-29798&t=58SDplXF2rqHeMK6-4
type LineItemConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeftHeadingConfig  *ITCConfiguration           `protobuf:"bytes,1,opt,name=left_heading_config,json=leftHeadingConfig,proto3" json:"left_heading_config,omitempty"`
	LeftTagsConfig     *ITCConfiguration           `protobuf:"bytes,2,opt,name=left_tags_config,json=leftTagsConfig,proto3" json:"left_tags_config,omitempty"`
	RightHeadingConfig *ITCConfiguration           `protobuf:"bytes,3,opt,name=right_heading_config,json=rightHeadingConfig,proto3" json:"right_heading_config,omitempty"`
	RightTagsConfig    *ITCConfiguration           `protobuf:"bytes,4,opt,name=right_tags_config,json=rightTagsConfig,proto3" json:"right_tags_config,omitempty"`
	LeftImageConfig    *VisualisationConfiguration `protobuf:"bytes,5,opt,name=left_image_config,json=leftImageConfig,proto3" json:"left_image_config,omitempty"`
}

func (x *LineItemConfig) Reset() {
	*x = LineItemConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LineItemConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LineItemConfig) ProtoMessage() {}

func (x *LineItemConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LineItemConfig.ProtoReflect.Descriptor instead.
func (*LineItemConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{15}
}

func (x *LineItemConfig) GetLeftHeadingConfig() *ITCConfiguration {
	if x != nil {
		return x.LeftHeadingConfig
	}
	return nil
}

func (x *LineItemConfig) GetLeftTagsConfig() *ITCConfiguration {
	if x != nil {
		return x.LeftTagsConfig
	}
	return nil
}

func (x *LineItemConfig) GetRightHeadingConfig() *ITCConfiguration {
	if x != nil {
		return x.RightHeadingConfig
	}
	return nil
}

func (x *LineItemConfig) GetRightTagsConfig() *ITCConfiguration {
	if x != nil {
		return x.RightTagsConfig
	}
	return nil
}

func (x *LineItemConfig) GetLeftImageConfig() *VisualisationConfiguration {
	if x != nil {
		return x.LeftImageConfig
	}
	return nil
}

type LearnOnTheGoSectionConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleConfig        *TextConfiguration               `protobuf:"bytes,1,opt,name=title_config,json=titleConfig,proto3" json:"title_config,omitempty"`
	SectionItemsConfig []*LearnOnTheGoSectionItemConfig `protobuf:"bytes,2,rep,name=section_items_config,json=sectionItemsConfig,proto3" json:"section_items_config,omitempty"`
}

func (x *LearnOnTheGoSectionConfig) Reset() {
	*x = LearnOnTheGoSectionConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LearnOnTheGoSectionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LearnOnTheGoSectionConfig) ProtoMessage() {}

func (x *LearnOnTheGoSectionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LearnOnTheGoSectionConfig.ProtoReflect.Descriptor instead.
func (*LearnOnTheGoSectionConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{16}
}

func (x *LearnOnTheGoSectionConfig) GetTitleConfig() *TextConfiguration {
	if x != nil {
		return x.TitleConfig
	}
	return nil
}

func (x *LearnOnTheGoSectionConfig) GetSectionItemsConfig() []*LearnOnTheGoSectionItemConfig {
	if x != nil {
		return x.SectionItemsConfig
	}
	return nil
}

type LearnOnTheGoSectionItemConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageConfig   *VisualisationConfiguration `protobuf:"bytes,1,opt,name=image_config,json=imageConfig,proto3" json:"image_config,omitempty"`
	InfoTagConfig *ITCConfiguration           `protobuf:"bytes,2,opt,name=info_tag_config,json=infoTagConfig,proto3" json:"info_tag_config,omitempty"`
	// Color of the overlay around the image
	OverlayColor      string                                           `protobuf:"bytes,3,opt,name=overlay_color,json=overlayColor,proto3" json:"overlay_color,omitempty"`
	DescriptionConfig *TextConfiguration                               `protobuf:"bytes,4,opt,name=description_config,json=descriptionConfig,proto3" json:"description_config,omitempty"`
	RedirectionConfig *LearnOnTheGoSectionItemConfig_RedirectionConfig `protobuf:"bytes,5,opt,name=redirection_config,json=redirectionConfig,proto3" json:"redirection_config,omitempty"`
}

func (x *LearnOnTheGoSectionItemConfig) Reset() {
	*x = LearnOnTheGoSectionItemConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LearnOnTheGoSectionItemConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LearnOnTheGoSectionItemConfig) ProtoMessage() {}

func (x *LearnOnTheGoSectionItemConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LearnOnTheGoSectionItemConfig.ProtoReflect.Descriptor instead.
func (*LearnOnTheGoSectionItemConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{17}
}

func (x *LearnOnTheGoSectionItemConfig) GetImageConfig() *VisualisationConfiguration {
	if x != nil {
		return x.ImageConfig
	}
	return nil
}

func (x *LearnOnTheGoSectionItemConfig) GetInfoTagConfig() *ITCConfiguration {
	if x != nil {
		return x.InfoTagConfig
	}
	return nil
}

func (x *LearnOnTheGoSectionItemConfig) GetOverlayColor() string {
	if x != nil {
		return x.OverlayColor
	}
	return ""
}

func (x *LearnOnTheGoSectionItemConfig) GetDescriptionConfig() *TextConfiguration {
	if x != nil {
		return x.DescriptionConfig
	}
	return nil
}

func (x *LearnOnTheGoSectionItemConfig) GetRedirectionConfig() *LearnOnTheGoSectionItemConfig_RedirectionConfig {
	if x != nil {
		return x.RedirectionConfig
	}
	return nil
}

type TextExplainerSectionConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title                   *ITCConfiguration                                   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Descriptions            []*TextConfiguration                                `protobuf:"bytes,2,rep,name=descriptions,proto3" json:"descriptions,omitempty"`
	BgColor                 string                                              `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	BorderColor             string                                              `protobuf:"bytes,4,opt,name=border_color,json=borderColor,proto3" json:"border_color,omitempty"`
	ExpandableControlConfig *TextExplainerSectionConfig_ExpandableControlConfig `protobuf:"bytes,5,opt,name=expandable_control_config,json=expandableControlConfig,proto3" json:"expandable_control_config,omitempty"`
	CtaConfigs              []*ITCConfiguration                                 `protobuf:"bytes,6,rep,name=cta_configs,json=ctaConfigs,proto3" json:"cta_configs,omitempty"`
}

func (x *TextExplainerSectionConfig) Reset() {
	*x = TextExplainerSectionConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextExplainerSectionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextExplainerSectionConfig) ProtoMessage() {}

func (x *TextExplainerSectionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextExplainerSectionConfig.ProtoReflect.Descriptor instead.
func (*TextExplainerSectionConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{18}
}

func (x *TextExplainerSectionConfig) GetTitle() *ITCConfiguration {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *TextExplainerSectionConfig) GetDescriptions() []*TextConfiguration {
	if x != nil {
		return x.Descriptions
	}
	return nil
}

func (x *TextExplainerSectionConfig) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *TextExplainerSectionConfig) GetBorderColor() string {
	if x != nil {
		return x.BorderColor
	}
	return ""
}

func (x *TextExplainerSectionConfig) GetExpandableControlConfig() *TextExplainerSectionConfig_ExpandableControlConfig {
	if x != nil {
		return x.ExpandableControlConfig
	}
	return nil
}

func (x *TextExplainerSectionConfig) GetCtaConfigs() []*ITCConfiguration {
	if x != nil {
		return x.CtaConfigs
	}
	return nil
}

type CardInfoComponentConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleValueConfig *VerticalITCConfig `protobuf:"bytes,1,opt,name=title_value_config,json=titleValueConfig,proto3" json:"title_value_config,omitempty"`
	InsightConfig    *ITCConfiguration  `protobuf:"bytes,2,opt,name=insight_config,json=insightConfig,proto3" json:"insight_config,omitempty"`
}

func (x *CardInfoComponentConfig) Reset() {
	*x = CardInfoComponentConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardInfoComponentConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardInfoComponentConfig) ProtoMessage() {}

func (x *CardInfoComponentConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardInfoComponentConfig.ProtoReflect.Descriptor instead.
func (*CardInfoComponentConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{19}
}

func (x *CardInfoComponentConfig) GetTitleValueConfig() *VerticalITCConfig {
	if x != nil {
		return x.TitleValueConfig
	}
	return nil
}

func (x *CardInfoComponentConfig) GetInsightConfig() *ITCConfiguration {
	if x != nil {
		return x.InsightConfig
	}
	return nil
}

type ChartDragIndicatorConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LocatorLineColor     string            `protobuf:"bytes,1,opt,name=locator_line_color,json=locatorLineColor,proto3" json:"locator_line_color,omitempty"`
	IndicatorLabelConfig *ITCConfiguration `protobuf:"bytes,2,opt,name=indicatorLabelConfig,proto3" json:"indicatorLabelConfig,omitempty"`
}

func (x *ChartDragIndicatorConfig) Reset() {
	*x = ChartDragIndicatorConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChartDragIndicatorConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChartDragIndicatorConfig) ProtoMessage() {}

func (x *ChartDragIndicatorConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChartDragIndicatorConfig.ProtoReflect.Descriptor instead.
func (*ChartDragIndicatorConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{20}
}

func (x *ChartDragIndicatorConfig) GetLocatorLineColor() string {
	if x != nil {
		return x.LocatorLineColor
	}
	return ""
}

func (x *ChartDragIndicatorConfig) GetIndicatorLabelConfig() *ITCConfiguration {
	if x != nil {
		return x.IndicatorLabelConfig
	}
	return nil
}

type ChartLineConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LineColor string `protobuf:"bytes,1,opt,name=line_color,json=lineColor,proto3" json:"line_color,omitempty"`
	LineWidth int32  `protobuf:"varint,2,opt,name=line_width,json=lineWidth,proto3" json:"line_width,omitempty"`
}

func (x *ChartLineConfig) Reset() {
	*x = ChartLineConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChartLineConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChartLineConfig) ProtoMessage() {}

func (x *ChartLineConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChartLineConfig.ProtoReflect.Descriptor instead.
func (*ChartLineConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{21}
}

func (x *ChartLineConfig) GetLineColor() string {
	if x != nil {
		return x.LineColor
	}
	return ""
}

func (x *ChartLineConfig) GetLineWidth() int32 {
	if x != nil {
		return x.LineWidth
	}
	return 0
}

type AreaChartCardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardInfoComponentConfig   *CardInfoComponentConfig  `protobuf:"bytes,1,opt,name=card_info_component_config,json=cardInfoComponentConfig,proto3" json:"card_info_component_config,omitempty"`
	BgColor                   string                    `protobuf:"bytes,2,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	AreaFillColor             *widget.BackgroundColour  `protobuf:"bytes,3,opt,name=area_fill_color,json=areaFillColor,proto3" json:"area_fill_color,omitempty"`
	ChartDragIndicatorConfig  *ChartDragIndicatorConfig `protobuf:"bytes,4,opt,name=chart_drag_indicator_config,json=chartDragIndicatorConfig,proto3" json:"chart_drag_indicator_config,omitempty"`
	ChartLineConfig           *ChartLineConfig          `protobuf:"bytes,5,opt,name=chart_line_config,json=chartLineConfig,proto3" json:"chart_line_config,omitempty"`
	HorizontalAxisLabelConfig *TextConfiguration        `protobuf:"bytes,6,opt,name=horizontal_axis_label_config,json=horizontalAxisLabelConfig,proto3" json:"horizontal_axis_label_config,omitempty"`
}

func (x *AreaChartCardConfig) Reset() {
	*x = AreaChartCardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AreaChartCardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AreaChartCardConfig) ProtoMessage() {}

func (x *AreaChartCardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AreaChartCardConfig.ProtoReflect.Descriptor instead.
func (*AreaChartCardConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{22}
}

func (x *AreaChartCardConfig) GetCardInfoComponentConfig() *CardInfoComponentConfig {
	if x != nil {
		return x.CardInfoComponentConfig
	}
	return nil
}

func (x *AreaChartCardConfig) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *AreaChartCardConfig) GetAreaFillColor() *widget.BackgroundColour {
	if x != nil {
		return x.AreaFillColor
	}
	return nil
}

func (x *AreaChartCardConfig) GetChartDragIndicatorConfig() *ChartDragIndicatorConfig {
	if x != nil {
		return x.ChartDragIndicatorConfig
	}
	return nil
}

func (x *AreaChartCardConfig) GetChartLineConfig() *ChartLineConfig {
	if x != nil {
		return x.ChartLineConfig
	}
	return nil
}

func (x *AreaChartCardConfig) GetHorizontalAxisLabelConfig() *TextConfiguration {
	if x != nil {
		return x.HorizontalAxisLabelConfig
	}
	return nil
}

// ActionableBannerConfig configures the ui for actionable banner component
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20349-35035&t=nVMbBK1YpBhtAc09-4
type ActionableBannerConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LeftImageConfig      *VisualisationConfiguration `protobuf:"bytes,1,opt,name=left_image_config,json=leftImageConfig,proto3" json:"left_image_config,omitempty"`
	DescriptionConfig    *TextConfiguration          `protobuf:"bytes,2,opt,name=description_config,json=descriptionConfig,proto3" json:"description_config,omitempty"`
	RightComponentConfig *ITCConfiguration           `protobuf:"bytes,3,opt,name=right_component_config,json=rightComponentConfig,proto3" json:"right_component_config,omitempty"`
	BgColor              string                      `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	Deeplink             *deeplink.Deeplink          `protobuf:"bytes,5,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	BorderProperty       *ui.BorderProperty          `protobuf:"bytes,7,opt,name=border_property,json=borderProperty,proto3" json:"border_property,omitempty"`
}

func (x *ActionableBannerConfig) Reset() {
	*x = ActionableBannerConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActionableBannerConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionableBannerConfig) ProtoMessage() {}

func (x *ActionableBannerConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionableBannerConfig.ProtoReflect.Descriptor instead.
func (*ActionableBannerConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{23}
}

func (x *ActionableBannerConfig) GetLeftImageConfig() *VisualisationConfiguration {
	if x != nil {
		return x.LeftImageConfig
	}
	return nil
}

func (x *ActionableBannerConfig) GetDescriptionConfig() *TextConfiguration {
	if x != nil {
		return x.DescriptionConfig
	}
	return nil
}

func (x *ActionableBannerConfig) GetRightComponentConfig() *ITCConfiguration {
	if x != nil {
		return x.RightComponentConfig
	}
	return nil
}

func (x *ActionableBannerConfig) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *ActionableBannerConfig) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *ActionableBannerConfig) GetBorderProperty() *ui.BorderProperty {
	if x != nil {
		return x.BorderProperty
	}
	return nil
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/Crore-Club?node-id=355-28110&t=9PLMrfTzO3vjKyor-4
type GridVisualisationCardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleValueConfig    *VerticalITCConfig   `protobuf:"bytes,1,opt,name=title_value_config,json=titleValueConfig,proto3" json:"title_value_config,omitempty"`
	GridComponentConfig *GridComponentConfig `protobuf:"bytes,2,opt,name=grid_component_config,json=gridComponentConfig,proto3" json:"grid_component_config,omitempty"`
}

func (x *GridVisualisationCardConfig) Reset() {
	*x = GridVisualisationCardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GridVisualisationCardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GridVisualisationCardConfig) ProtoMessage() {}

func (x *GridVisualisationCardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GridVisualisationCardConfig.ProtoReflect.Descriptor instead.
func (*GridVisualisationCardConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{24}
}

func (x *GridVisualisationCardConfig) GetTitleValueConfig() *VerticalITCConfig {
	if x != nil {
		return x.TitleValueConfig
	}
	return nil
}

func (x *GridVisualisationCardConfig) GetGridComponentConfig() *GridComponentConfig {
	if x != nil {
		return x.GridComponentConfig
	}
	return nil
}

// GridComponentConfig configures the ui for grid component
// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/Crore-Club?node-id=355-28140&t=9PLMrfTzO3vjKyor-4
type GridComponentConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleConfig    *TextConfiguration `protobuf:"bytes,1,opt,name=title_config,json=titleConfig,proto3" json:"title_config,omitempty"`
	BgColor        string             `protobuf:"bytes,2,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	GridCardConfig *GridCardConfig    `protobuf:"bytes,3,opt,name=grid_card_config,json=gridCardConfig,proto3" json:"grid_card_config,omitempty"`
}

func (x *GridComponentConfig) Reset() {
	*x = GridComponentConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GridComponentConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GridComponentConfig) ProtoMessage() {}

func (x *GridComponentConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GridComponentConfig.ProtoReflect.Descriptor instead.
func (*GridComponentConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{25}
}

func (x *GridComponentConfig) GetTitleConfig() *TextConfiguration {
	if x != nil {
		return x.TitleConfig
	}
	return nil
}

func (x *GridComponentConfig) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *GridComponentConfig) GetGridCardConfig() *GridCardConfig {
	if x != nil {
		return x.GridCardConfig
	}
	return nil
}

// GridCardConfig configures the ui for grid card component
// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/Crore-Club?node-id=355-28142&t=9PLMrfTzO3vjKyor-4
type GridCardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ColumnCount    int32           `protobuf:"varint,1,opt,name=column_count,json=columnCount,proto3" json:"column_count,omitempty"`
	GridTileConfig *GridTileConfig `protobuf:"bytes,2,opt,name=grid_tile_config,json=gridTileConfig,proto3" json:"grid_tile_config,omitempty"`
}

func (x *GridCardConfig) Reset() {
	*x = GridCardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GridCardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GridCardConfig) ProtoMessage() {}

func (x *GridCardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GridCardConfig.ProtoReflect.Descriptor instead.
func (*GridCardConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{26}
}

func (x *GridCardConfig) GetColumnCount() int32 {
	if x != nil {
		return x.ColumnCount
	}
	return 0
}

func (x *GridCardConfig) GetGridTileConfig() *GridTileConfig {
	if x != nil {
		return x.GridTileConfig
	}
	return nil
}

// GridTileConfig configures the ui for individual grid tile
// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/Crore-Club?node-id=355-28226&t=9PLMrfTzO3vjKyor-4
type GridTileConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleConfig      *ITCConfiguration              `protobuf:"bytes,1,opt,name=title_config,json=titleConfig,proto3" json:"title_config,omitempty"`
	ValueConfig      *ITCConfiguration              `protobuf:"bytes,2,opt,name=value_config,json=valueConfig,proto3" json:"value_config,omitempty"`
	BgColor          string                         `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	IconConfig       *VisualisationConfiguration    `protobuf:"bytes,4,opt,name=icon_config,json=iconConfig,proto3" json:"icon_config,omitempty"`
	BottomCardConfig *GridComponentBottomCardConfig `protobuf:"bytes,5,opt,name=bottom_card_config,json=bottomCardConfig,proto3" json:"bottom_card_config,omitempty"`
}

func (x *GridTileConfig) Reset() {
	*x = GridTileConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GridTileConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GridTileConfig) ProtoMessage() {}

func (x *GridTileConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GridTileConfig.ProtoReflect.Descriptor instead.
func (*GridTileConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{27}
}

func (x *GridTileConfig) GetTitleConfig() *ITCConfiguration {
	if x != nil {
		return x.TitleConfig
	}
	return nil
}

func (x *GridTileConfig) GetValueConfig() *ITCConfiguration {
	if x != nil {
		return x.ValueConfig
	}
	return nil
}

func (x *GridTileConfig) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *GridTileConfig) GetIconConfig() *VisualisationConfiguration {
	if x != nil {
		return x.IconConfig
	}
	return nil
}

func (x *GridTileConfig) GetBottomCardConfig() *GridComponentBottomCardConfig {
	if x != nil {
		return x.BottomCardConfig
	}
	return nil
}

// GridComponentBottomCardConfig configures the ui for bottom card component in grid tile which is shown on tap of grid tile
// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/Crore-Club?node-id=355-28241&t=9PLMrfTzO3vjKyor-4
type GridComponentBottomCardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleConfig *ITCConfiguration                   `protobuf:"bytes,1,opt,name=title_config,json=titleConfig,proto3" json:"title_config,omitempty"`
	ValueConfig *GridComponentBottomCardValueConfig `protobuf:"bytes,2,opt,name=value_config,json=valueConfig,proto3" json:"value_config,omitempty"`
}

func (x *GridComponentBottomCardConfig) Reset() {
	*x = GridComponentBottomCardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GridComponentBottomCardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GridComponentBottomCardConfig) ProtoMessage() {}

func (x *GridComponentBottomCardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GridComponentBottomCardConfig.ProtoReflect.Descriptor instead.
func (*GridComponentBottomCardConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{28}
}

func (x *GridComponentBottomCardConfig) GetTitleConfig() *ITCConfiguration {
	if x != nil {
		return x.TitleConfig
	}
	return nil
}

func (x *GridComponentBottomCardConfig) GetValueConfig() *GridComponentBottomCardValueConfig {
	if x != nil {
		return x.ValueConfig
	}
	return nil
}

type GridComponentBottomCardValueConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KeyConfig   *ITCConfiguration `protobuf:"bytes,1,opt,name=key_config,json=keyConfig,proto3" json:"key_config,omitempty"`
	ValueConfig *ITCConfiguration `protobuf:"bytes,2,opt,name=value_config,json=valueConfig,proto3" json:"value_config,omitempty"`
}

func (x *GridComponentBottomCardValueConfig) Reset() {
	*x = GridComponentBottomCardValueConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GridComponentBottomCardValueConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GridComponentBottomCardValueConfig) ProtoMessage() {}

func (x *GridComponentBottomCardValueConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GridComponentBottomCardValueConfig.ProtoReflect.Descriptor instead.
func (*GridComponentBottomCardValueConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{29}
}

func (x *GridComponentBottomCardValueConfig) GetKeyConfig() *ITCConfiguration {
	if x != nil {
		return x.KeyConfig
	}
	return nil
}

func (x *GridComponentBottomCardValueConfig) GetValueConfig() *ITCConfiguration {
	if x != nil {
		return x.ValueConfig
	}
	return nil
}

// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=18363-11178&t=oa57kBmUiIJoXyzi-1
type DonutCardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Config for a title and its value, e.g. 'Your most invested fund' and 'HDFC'
	TitleValueConfig *VerticalITCConfig `protobuf:"bytes,1,opt,name=title_value_config,json=titleValueConfig,proto3" json:"title_value_config,omitempty"`
	// config for donut slice
	DonutSliceConfig *DonutSliceConfig `protobuf:"bytes,2,opt,name=donut_slice_config,json=donutSliceConfig,proto3" json:"donut_slice_config,omitempty"`
	// Background color of card
	BgColor string `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *DonutCardConfig) Reset() {
	*x = DonutCardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DonutCardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DonutCardConfig) ProtoMessage() {}

func (x *DonutCardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DonutCardConfig.ProtoReflect.Descriptor instead.
func (*DonutCardConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{30}
}

func (x *DonutCardConfig) GetTitleValueConfig() *VerticalITCConfig {
	if x != nil {
		return x.TitleValueConfig
	}
	return nil
}

func (x *DonutCardConfig) GetDonutSliceConfig() *DonutSliceConfig {
	if x != nil {
		return x.DonutSliceConfig
	}
	return nil
}

func (x *DonutCardConfig) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

type DonutSliceConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// default color of a donut slice, if not provided in code then this color will be used
	DonutColor string `protobuf:"bytes,1,opt,name=donut_color,json=donutColor,proto3" json:"donut_color,omitempty"`
}

func (x *DonutSliceConfig) Reset() {
	*x = DonutSliceConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DonutSliceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DonutSliceConfig) ProtoMessage() {}

func (x *DonutSliceConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DonutSliceConfig.ProtoReflect.Descriptor instead.
func (*DonutSliceConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{31}
}

func (x *DonutSliceConfig) GetDonutColor() string {
	if x != nil {
		return x.DonutColor
	}
	return ""
}

// ActionableV2BannerConfig configures the ui for actionable banner component
// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/Lending-FFF?node-id=68069-73027&t=3WXp9aB2SYPlGJWW-4 (Loan banner in the figma)
type ActionableBannerV2Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RightImageConfig   *VisualisationConfiguration `protobuf:"bytes,1,opt,name=right_image_config,json=rightImageConfig,proto3" json:"right_image_config,omitempty"`
	TitleConfig        *TextConfiguration          `protobuf:"bytes,2,opt,name=title_config,json=titleConfig,proto3" json:"title_config,omitempty"`
	CtaComponentConfig *ITCConfiguration           `protobuf:"bytes,3,opt,name=cta_component_config,json=ctaComponentConfig,proto3" json:"cta_component_config,omitempty"`
	BgColor            *widget.BackgroundColour    `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	Deeplink           *deeplink.Deeplink          `protobuf:"bytes,5,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *ActionableBannerV2Config) Reset() {
	*x = ActionableBannerV2Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActionableBannerV2Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionableBannerV2Config) ProtoMessage() {}

func (x *ActionableBannerV2Config) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionableBannerV2Config.ProtoReflect.Descriptor instead.
func (*ActionableBannerV2Config) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{32}
}

func (x *ActionableBannerV2Config) GetRightImageConfig() *VisualisationConfiguration {
	if x != nil {
		return x.RightImageConfig
	}
	return nil
}

func (x *ActionableBannerV2Config) GetTitleConfig() *TextConfiguration {
	if x != nil {
		return x.TitleConfig
	}
	return nil
}

func (x *ActionableBannerV2Config) GetCtaComponentConfig() *ITCConfiguration {
	if x != nil {
		return x.CtaComponentConfig
	}
	return nil
}

func (x *ActionableBannerV2Config) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *ActionableBannerV2Config) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

type SecretSummaryCardUiConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TitleConfig             *TextConfiguration                `protobuf:"bytes,1,opt,name=title_config,json=titleConfig,proto3" json:"title_config,omitempty"`
	SecretSummaryCardConfig []*SecretSummaryCardConfiguration `protobuf:"bytes,2,rep,name=secret_summary_card_config,json=secretSummaryCardConfig,proto3" json:"secret_summary_card_config,omitempty"`
}

func (x *SecretSummaryCardUiConfig) Reset() {
	*x = SecretSummaryCardUiConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecretSummaryCardUiConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretSummaryCardUiConfig) ProtoMessage() {}

func (x *SecretSummaryCardUiConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretSummaryCardUiConfig.ProtoReflect.Descriptor instead.
func (*SecretSummaryCardUiConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{33}
}

func (x *SecretSummaryCardUiConfig) GetTitleConfig() *TextConfiguration {
	if x != nil {
		return x.TitleConfig
	}
	return nil
}

func (x *SecretSummaryCardUiConfig) GetSecretSummaryCardConfig() []*SecretSummaryCardConfiguration {
	if x != nil {
		return x.SecretSummaryCardConfig
	}
	return nil
}

type ITCConfiguration_BorderConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Color string `protobuf:"bytes,1,opt,name=color,proto3" json:"color,omitempty"`
}

func (x *ITCConfiguration_BorderConfig) Reset() {
	*x = ITCConfiguration_BorderConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ITCConfiguration_BorderConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ITCConfiguration_BorderConfig) ProtoMessage() {}

func (x *ITCConfiguration_BorderConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ITCConfiguration_BorderConfig.ProtoReflect.Descriptor instead.
func (*ITCConfiguration_BorderConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ITCConfiguration_BorderConfig) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

type ImageCardConfig_ValueComponentConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageConfig           *VisualisationConfiguration `protobuf:"bytes,1,opt,name=image_config,json=imageConfig,proto3" json:"image_config,omitempty"`
	SecretValuePairConfig *VerticalITCConfig          `protobuf:"bytes,2,opt,name=secret_value_pair_config,json=secretValuePairConfig,proto3" json:"secret_value_pair_config,omitempty"`
}

func (x *ImageCardConfig_ValueComponentConfig) Reset() {
	*x = ImageCardConfig_ValueComponentConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageCardConfig_ValueComponentConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageCardConfig_ValueComponentConfig) ProtoMessage() {}

func (x *ImageCardConfig_ValueComponentConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageCardConfig_ValueComponentConfig.ProtoReflect.Descriptor instead.
func (*ImageCardConfig_ValueComponentConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{13, 0}
}

func (x *ImageCardConfig_ValueComponentConfig) GetImageConfig() *VisualisationConfiguration {
	if x != nil {
		return x.ImageConfig
	}
	return nil
}

func (x *ImageCardConfig_ValueComponentConfig) GetSecretValuePairConfig() *VerticalITCConfig {
	if x != nil {
		return x.SecretValuePairConfig
	}
	return nil
}

type LearnOnTheGoSectionItemConfig_RedirectionConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Redirection:
	//
	//	*LearnOnTheGoSectionItemConfig_RedirectionConfig_Deeplink
	//	*LearnOnTheGoSectionItemConfig_RedirectionConfig_StorifymeUrl
	Redirection isLearnOnTheGoSectionItemConfig_RedirectionConfig_Redirection `protobuf_oneof:"redirection"`
}

func (x *LearnOnTheGoSectionItemConfig_RedirectionConfig) Reset() {
	*x = LearnOnTheGoSectionItemConfig_RedirectionConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LearnOnTheGoSectionItemConfig_RedirectionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LearnOnTheGoSectionItemConfig_RedirectionConfig) ProtoMessage() {}

func (x *LearnOnTheGoSectionItemConfig_RedirectionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LearnOnTheGoSectionItemConfig_RedirectionConfig.ProtoReflect.Descriptor instead.
func (*LearnOnTheGoSectionItemConfig_RedirectionConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{17, 0}
}

func (m *LearnOnTheGoSectionItemConfig_RedirectionConfig) GetRedirection() isLearnOnTheGoSectionItemConfig_RedirectionConfig_Redirection {
	if m != nil {
		return m.Redirection
	}
	return nil
}

func (x *LearnOnTheGoSectionItemConfig_RedirectionConfig) GetDeeplink() *deeplink.Deeplink {
	if x, ok := x.GetRedirection().(*LearnOnTheGoSectionItemConfig_RedirectionConfig_Deeplink); ok {
		return x.Deeplink
	}
	return nil
}

func (x *LearnOnTheGoSectionItemConfig_RedirectionConfig) GetStorifymeUrl() string {
	if x, ok := x.GetRedirection().(*LearnOnTheGoSectionItemConfig_RedirectionConfig_StorifymeUrl); ok {
		return x.StorifymeUrl
	}
	return ""
}

type isLearnOnTheGoSectionItemConfig_RedirectionConfig_Redirection interface {
	isLearnOnTheGoSectionItemConfig_RedirectionConfig_Redirection()
}

type LearnOnTheGoSectionItemConfig_RedirectionConfig_Deeplink struct {
	Deeplink *deeplink.Deeplink `protobuf:"bytes,1,opt,name=deeplink,proto3,oneof"`
}

type LearnOnTheGoSectionItemConfig_RedirectionConfig_StorifymeUrl struct {
	StorifymeUrl string `protobuf:"bytes,2,opt,name=storifyme_url,json=storifymeUrl,proto3,oneof"`
}

func (*LearnOnTheGoSectionItemConfig_RedirectionConfig_Deeplink) isLearnOnTheGoSectionItemConfig_RedirectionConfig_Redirection() {
}

func (*LearnOnTheGoSectionItemConfig_RedirectionConfig_StorifymeUrl) isLearnOnTheGoSectionItemConfig_RedirectionConfig_Redirection() {
}

type TextExplainerSectionConfig_ExpandableControlConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsExpanded        bool                        `protobuf:"varint,1,opt,name=is_expanded,json=isExpanded,proto3" json:"is_expanded,omitempty"`
	ToggleImageConfig *VisualisationConfiguration `protobuf:"bytes,2,opt,name=toggle_image_config,json=toggleImageConfig,proto3" json:"toggle_image_config,omitempty"`
}

func (x *TextExplainerSectionConfig_ExpandableControlConfig) Reset() {
	*x = TextExplainerSectionConfig_ExpandableControlConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextExplainerSectionConfig_ExpandableControlConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextExplainerSectionConfig_ExpandableControlConfig) ProtoMessage() {}

func (x *TextExplainerSectionConfig_ExpandableControlConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_secrets_config_ui_configuration_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextExplainerSectionConfig_ExpandableControlConfig.ProtoReflect.Descriptor instead.
func (*TextExplainerSectionConfig_ExpandableControlConfig) Descriptor() ([]byte, []int) {
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP(), []int{18, 0}
}

func (x *TextExplainerSectionConfig_ExpandableControlConfig) GetIsExpanded() bool {
	if x != nil {
		return x.IsExpanded
	}
	return false
}

func (x *TextExplainerSectionConfig_ExpandableControlConfig) GetToggleImageConfig() *VisualisationConfiguration {
	if x != nil {
		return x.ToggleImageConfig
	}
	return nil
}

var File_api_insights_secrets_config_ui_configuration_proto protoreflect.FileDescriptor

var file_api_insights_secrets_config_ui_configuration_proto_rawDesc = []byte{
	0x0a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x75, 0x69,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x24, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x75, 0x69, 0x2f, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x92, 0x04, 0x0a, 0x1e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4c, 0x0a, 0x0c, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4d, 0x0a, 0x0c, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x66, 0x0a, 0x14, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08,
	0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x35, 0x0a, 0x17, 0x7a, 0x65,
	0x72, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x7a, 0x65, 0x72,
	0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x76, 0x0a, 0x1d, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x73, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x1a, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x44, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x86, 0x05, 0x0a, 0x10, 0x49, 0x54,
	0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6f,
	0x0a, 0x19, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x17, 0x6c, 0x65, 0x66, 0x74, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x4d, 0x0a, 0x0c, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0b, 0x74, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x71,
	0x0a, 0x1a, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x18, 0x72, 0x69, 0x67, 0x68, 0x74, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x5b, 0x0a, 0x0d, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x0c, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19,
	0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x68, 0x0a, 0x14, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x13,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x1a, 0x24, 0x0a, 0x0c,
	0x42, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x22, 0xaf, 0x01, 0x0a, 0x11, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x49,
	0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4c, 0x0a, 0x0c, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4c, 0x0a, 0x0c, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x22, 0xba, 0x02, 0x0a, 0x11, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x3c, 0x0a, 0x0a, 0x66, 0x6f, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x6f, 0x6e, 0x74, 0x53, 0x74,
	0x79, 0x6c, 0x65, 0x52, 0x09, 0x66, 0x6f, 0x6e, 0x74, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65,
	0x78, 0x74, 0x12, 0x53, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x12, 0x4d, 0x0a, 0x0e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x6c, 0x69, 0x67, 0x6e,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x41, 0x6c, 0x69, 0x67, 0x6e, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x0d, 0x74, 0x65, 0x78, 0x74, 0x41, 0x6c, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x22, 0x25, 0x0a, 0x0f, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x22, 0x67, 0x0a, 0x1a, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74,
	0x68, 0x22, 0xca, 0x05, 0x0a, 0x17, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4f, 0x0a,
	0x0e, 0x70, 0x72, 0x6f, 0x5f, 0x74, 0x69, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0c, 0x70, 0x72, 0x6f, 0x54, 0x69, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x59,
	0x0a, 0x12, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x43, 0x61, 0x72, 0x64, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x10, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x43,
	0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x60, 0x0a, 0x15, 0x62, 0x61, 0x72,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x42, 0x61, 0x72, 0x43, 0x68, 0x61, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x12, 0x62, 0x61, 0x72, 0x43, 0x68, 0x61, 0x72,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x56, 0x0a, 0x11, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x48, 0x00, 0x52, 0x0f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x63, 0x0a, 0x16, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x63, 0x68, 0x61, 0x72,
	0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x41, 0x72,
	0x65, 0x61, 0x43, 0x68, 0x61, 0x72, 0x74, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x48, 0x00, 0x52, 0x13, 0x61, 0x72, 0x65, 0x61, 0x43, 0x68, 0x61, 0x72, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x7b, 0x0a, 0x1e, 0x67, 0x72, 0x69, 0x64,
	0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x34, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x47, 0x72, 0x69, 0x64, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x1b, 0x67, 0x72, 0x69, 0x64, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x56, 0x0a, 0x11, 0x64, 0x6f, 0x6e, 0x75, 0x74, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x6f, 0x6e, 0x75, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x0f, 0x64, 0x6f,
	0x6e, 0x75, 0x74, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x0f, 0x0a,
	0x0d, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xdf,
	0x01, 0x0a, 0x10, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x58, 0x0a, 0x12, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63,
	0x61, 0x6c, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x56, 0x0a,
	0x0c, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x22, 0x88, 0x03, 0x0a, 0x12, 0x42, 0x61, 0x72, 0x43, 0x68, 0x61, 0x72, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x58, 0x0a, 0x12, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x65,
	0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x10, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x5b, 0x0a, 0x14, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x51,
	0x0a, 0x10, 0x62, 0x61, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x42, 0x61, 0x72, 0x43, 0x68, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0e, 0x62, 0x61, 0x72, 0x43, 0x68, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x4d, 0x0a, 0x0e,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x52, 0x0d, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x22, 0x7c, 0x0a, 0x0d, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x4c, 0x0a, 0x0c,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54, 0x43,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x62, 0x0a, 0x0e, 0x42, 0x61, 0x72,
	0x43, 0x68, 0x61, 0x72, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x50, 0x0a, 0x12, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x61, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x42, 0x61, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x42, 0x61, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x98, 0x02,
	0x0a, 0x09, 0x42, 0x61, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x73, 0x0a, 0x1c, 0x74,
	0x6f, 0x70, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x42, 0x61, 0x72, 0x44,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x19, 0x74, 0x6f, 0x70, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x79, 0x0a, 0x1f, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x42, 0x61, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x1c, 0x62,
	0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x62,
	0x61, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x62, 0x61, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xdc, 0x01, 0x0a, 0x19, 0x42, 0x61, 0x72,
	0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x58, 0x0a, 0x0c, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x00, 0x52, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x5b, 0x0a, 0x13, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x74, 0x65, 0x78, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x11, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x08, 0x0a,
	0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xc2, 0x04, 0x0a, 0x0f, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4c, 0x0a, 0x0c, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54, 0x43, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x6b, 0x0a, 0x17, 0x62, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x15, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x73, 0x0a, 0x16, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3d, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x14, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0xd3, 0x01, 0x0a, 0x14, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x56, 0x0a, 0x0c, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x63, 0x0a, 0x18, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x49, 0x54, 0x43, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x15, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xbc, 0x02, 0x0a,
	0x0f, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x51, 0x0a, 0x10, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0e, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x69, 0x0a, 0x1b, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x61, 0x6c, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x18, 0x6c, 0x65, 0x66, 0x74, 0x41, 0x6c, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x6b,
	0x0a, 0x1c, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x61, 0x6c, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x19, 0x72, 0x69, 0x67, 0x68, 0x74, 0x41, 0x6c, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x48, 0x65,
	0x61, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xd5, 0x03, 0x0a, 0x0e,
	0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x59,
	0x0a, 0x13, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x6c, 0x65, 0x66, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x53, 0x0a, 0x10, 0x6c, 0x65, 0x66,
	0x74, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54,
	0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e,
	0x6c, 0x65, 0x66, 0x74, 0x54, 0x61, 0x67, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5b,
	0x0a, 0x14, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x72, 0x69, 0x67, 0x68, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x55, 0x0a, 0x11, 0x72,
	0x69, 0x67, 0x68, 0x74, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x54, 0x61, 0x67, 0x73, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x5f, 0x0a, 0x11, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0f, 0x6c, 0x65, 0x66, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x22, 0xd4, 0x01, 0x0a, 0x19, 0x4c, 0x65, 0x61, 0x72, 0x6e, 0x4f, 0x6e, 0x54,
	0x68, 0x65, 0x47, 0x6f, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x4d, 0x0a, 0x0c, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x68, 0x0a, 0x14, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4c, 0x65, 0x61, 0x72, 0x6e, 0x4f, 0x6e,
	0x54, 0x68, 0x65, 0x47, 0x6f, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x12, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xca, 0x04, 0x0a, 0x1d, 0x4c,
	0x65, 0x61, 0x72, 0x6e, 0x4f, 0x6e, 0x54, 0x68, 0x65, 0x47, 0x6f, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x56, 0x0a, 0x0c,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x51, 0x0a, 0x0f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x74, 0x61, 0x67,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x69, 0x6e, 0x66, 0x6f, 0x54, 0x61,
	0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x76, 0x65, 0x72, 0x6c,
	0x61, 0x79, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x6f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x59, 0x0a, 0x12,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x77, 0x0a, 0x12, 0x72, 0x65, 0x64, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4c, 0x65,
	0x61, 0x72, 0x6e, 0x4f, 0x6e, 0x54, 0x68, 0x65, 0x47, 0x6f, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x65, 0x64, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x11, 0x72,
	0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x1a, 0x84, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x39, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x48, 0x00, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x12, 0x25, 0x0a, 0x0d, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x66, 0x79, 0x6d, 0x65, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x74, 0x6f, 0x72,
	0x69, 0x66, 0x79, 0x6d, 0x65, 0x55, 0x72, 0x6c, 0x42, 0x0d, 0x0a, 0x0b, 0x72, 0x65, 0x64, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe3, 0x04, 0x0a, 0x1a, 0x54, 0x65, 0x78, 0x74,
	0x45, 0x78, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3f, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x4e, 0x0a, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x87, 0x01, 0x0a, 0x19, 0x65, 0x78, 0x70, 0x61, 0x6e, 0x64,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x45, 0x78, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x45,
	0x78, 0x70, 0x61, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x17, 0x65, 0x78, 0x70, 0x61, 0x6e, 0x64, 0x61, 0x62,
	0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x4a, 0x0a, 0x0b, 0x63, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49,
	0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0a, 0x63, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x1a, 0x9f, 0x01, 0x0a, 0x17,
	0x45, 0x78, 0x70, 0x61, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x65, 0x78,
	0x70, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73,
	0x45, 0x78, 0x70, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x12, 0x63, 0x0a, 0x13, 0x74, 0x6f, 0x67, 0x67,
	0x6c, 0x65, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x74, 0x6f, 0x67, 0x67,
	0x6c, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xc5, 0x01,
	0x0a, 0x17, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x58, 0x0a, 0x12, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x10, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x50, 0x0a, 0x0e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xa7, 0x01, 0x0a, 0x18, 0x43, 0x68, 0x61, 0x72, 0x74, 0x44,
	0x72, 0x61, 0x67, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x2c, 0x0a, 0x12, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x5d, 0x0a, 0x14, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x69, 0x6e, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x6f, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22,
	0x4f, 0x0a, 0x0f, 0x43, 0x68, 0x61, 0x72, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x65, 0x57, 0x69, 0x64, 0x74, 0x68,
	0x22, 0xac, 0x04, 0x0a, 0x13, 0x41, 0x72, 0x65, 0x61, 0x43, 0x68, 0x61, 0x72, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x6d, 0x0a, 0x1a, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x17,
	0x63, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x12, 0x56, 0x0a, 0x0f, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x66, 0x69, 0x6c, 0x6c, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0d, 0x61, 0x72, 0x65,
	0x61, 0x46, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x70, 0x0a, 0x1b, 0x63, 0x68,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x72, 0x61, 0x67, 0x5f, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x44,
	0x72, 0x61, 0x67, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x18, 0x63, 0x68, 0x61, 0x72, 0x74, 0x44, 0x72, 0x61, 0x67, 0x49, 0x6e, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x54, 0x0a, 0x11,
	0x63, 0x68, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x0f, 0x63, 0x68, 0x61, 0x72, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x6b, 0x0a, 0x1c, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c,
	0x5f, 0x61, 0x78, 0x69, 0x73, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x19, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c,
	0x41, 0x78, 0x69, 0x73, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22,
	0xd2, 0x03, 0x0a, 0x16, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5f, 0x0a, 0x11, 0x6c, 0x65,
	0x66, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x6c, 0x65, 0x66, 0x74,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x59, 0x0a, 0x12, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x11, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5f, 0x0a, 0x16, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x14, 0x72, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x47, 0x0a, 0x0f, 0x62,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x42, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x52, 0x0e, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x79, 0x22, 0xd9, 0x01, 0x0a, 0x1b, 0x47, 0x72, 0x69, 0x64, 0x56, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x58, 0x0a, 0x12, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69,
	0x63, 0x61, 0x6c, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x60,
	0x0a, 0x15, 0x67, 0x72, 0x69, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x47, 0x72, 0x69, 0x64, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x13, 0x67, 0x72, 0x69,
	0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x22, 0xd2, 0x01, 0x0a, 0x13, 0x47, 0x72, 0x69, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4d, 0x0a, 0x0c, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x12, 0x51, 0x0a, 0x10, 0x67, 0x72, 0x69, 0x64, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x47, 0x72, 0x69, 0x64, 0x43, 0x61, 0x72, 0x64, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e, 0x67, 0x72, 0x69, 0x64, 0x43, 0x61, 0x72, 0x64, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x86, 0x01, 0x0a, 0x0e, 0x47, 0x72, 0x69, 0x64, 0x43, 0x61,
	0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6c, 0x75,
	0x6d, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x51, 0x0a, 0x10, 0x67,
	0x72, 0x69, 0x64, 0x5f, 0x74, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x47, 0x72, 0x69, 0x64, 0x54, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0e,
	0x67, 0x72, 0x69, 0x64, 0x54, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x83,
	0x03, 0x0a, 0x0e, 0x47, 0x72, 0x69, 0x64, 0x54, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x4c, 0x0a, 0x0c, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x4c, 0x0a, 0x0c, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0b, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a,
	0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x54, 0x0a, 0x0b, 0x69, 0x63, 0x6f, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0a, 0x69, 0x63, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x64,
	0x0a, 0x12, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x47, 0x72, 0x69, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x10, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x22, 0xcd, 0x01, 0x0a, 0x1d, 0x47, 0x72, 0x69, 0x64, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x43, 0x61, 0x72, 0x64,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4c, 0x0a, 0x0c, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x5e, 0x0a, 0x0c, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x47, 0x72, 0x69, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x43, 0x61, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0b, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x22, 0xbc, 0x01, 0x0a, 0x22, 0x47, 0x72, 0x69, 0x64, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x43, 0x61, 0x72, 0x64,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x48, 0x0a, 0x0a, 0x6b,
	0x65, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x6b, 0x65, 0x79, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4c, 0x0a, 0x0c, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x22, 0xdf, 0x01, 0x0a, 0x0f, 0x44, 0x6f, 0x6e, 0x75, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x58, 0x0a, 0x12, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x65,
	0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x10, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x57, 0x0a, 0x12, 0x64, 0x6f, 0x6e, 0x75, 0x74, 0x5f, 0x73, 0x6c, 0x69, 0x63, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x6f, 0x6e, 0x75, 0x74, 0x53, 0x6c, 0x69,
	0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x64, 0x6f, 0x6e, 0x75, 0x74, 0x53,
	0x6c, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x33, 0x0a, 0x10, 0x44, 0x6f, 0x6e, 0x75, 0x74, 0x53, 0x6c,
	0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x6e,
	0x75, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x64, 0x6f, 0x6e, 0x75, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xad, 0x03, 0x0a, 0x18, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x56,
	0x32, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x61, 0x0a, 0x12, 0x72, 0x69, 0x67, 0x68, 0x74,
	0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x72, 0x69, 0x67, 0x68, 0x74, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4d, 0x0a, 0x0c, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5b, 0x0a, 0x14, 0x63, 0x74, 0x61,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x49, 0x54, 0x43, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x12, 0x63, 0x74, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69,
	0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0xe0, 0x01, 0x0a, 0x19, 0x53,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x61, 0x72, 0x64,
	0x55, 0x69, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4d, 0x0a, 0x0c, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x74, 0x0a, 0x1a, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x17, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2a, 0x79, 0x0a,
	0x1a, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x0a, 0x29, 0x53,
	0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x44, 0x49,
	0x53, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x53,
	0x44, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x4f, 0x4f, 0x4e, 0x10, 0x01,
	0x12, 0x16, 0x0a, 0x12, 0x53, 0x53, 0x44, 0x53, 0x5f, 0x55, 0x4e, 0x4c, 0x4f, 0x43, 0x4b, 0x5f,
	0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x10, 0x02, 0x42, 0x68, 0x0a, 0x32, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5a, 0x32,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_insights_secrets_config_ui_configuration_proto_rawDescOnce sync.Once
	file_api_insights_secrets_config_ui_configuration_proto_rawDescData = file_api_insights_secrets_config_ui_configuration_proto_rawDesc
)

func file_api_insights_secrets_config_ui_configuration_proto_rawDescGZIP() []byte {
	file_api_insights_secrets_config_ui_configuration_proto_rawDescOnce.Do(func() {
		file_api_insights_secrets_config_ui_configuration_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_insights_secrets_config_ui_configuration_proto_rawDescData)
	})
	return file_api_insights_secrets_config_ui_configuration_proto_rawDescData
}

var file_api_insights_secrets_config_ui_configuration_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_insights_secrets_config_ui_configuration_proto_msgTypes = make([]protoimpl.MessageInfo, 38)
var file_api_insights_secrets_config_ui_configuration_proto_goTypes = []interface{}{
	(SecretSummaryDisabledState)(0),                            // 0: insights.secrets.config.SecretSummaryDisabledState
	(*SecretSummaryCardConfiguration)(nil),                     // 1: insights.secrets.config.SecretSummaryCardConfiguration
	(*ITCConfiguration)(nil),                                   // 2: insights.secrets.config.ITCConfiguration
	(*VerticalITCConfig)(nil),                                  // 3: insights.secrets.config.VerticalITCConfig
	(*TextConfiguration)(nil),                                  // 4: insights.secrets.config.TextConfiguration
	(*DisplayTemplate)(nil),                                    // 5: insights.secrets.config.DisplayTemplate
	(*VisualisationConfiguration)(nil),                         // 6: insights.secrets.config.VisualisationConfiguration
	(*VisualisationCardConfig)(nil),                            // 7: insights.secrets.config.VisualisationCardConfig
	(*NumberCardConfig)(nil),                                   // 8: insights.secrets.config.NumberCardConfig
	(*BarChartCardConfig)(nil),                                 // 9: insights.secrets.config.BarChartCardConfig
	(*ReferenceLine)(nil),                                      // 10: insights.secrets.config.ReferenceLine
	(*BarChartConfig)(nil),                                     // 11: insights.secrets.config.BarChartConfig
	(*BarConfig)(nil),                                          // 12: insights.secrets.config.BarConfig
	(*BarDisplayComponentConfig)(nil),                          // 13: insights.secrets.config.BarDisplayComponentConfig
	(*ImageCardConfig)(nil),                                    // 14: insights.secrets.config.ImageCardConfig
	(*LineItemsConfig)(nil),                                    // 15: insights.secrets.config.LineItemsConfig
	(*LineItemConfig)(nil),                                     // 16: insights.secrets.config.LineItemConfig
	(*LearnOnTheGoSectionConfig)(nil),                          // 17: insights.secrets.config.LearnOnTheGoSectionConfig
	(*LearnOnTheGoSectionItemConfig)(nil),                      // 18: insights.secrets.config.LearnOnTheGoSectionItemConfig
	(*TextExplainerSectionConfig)(nil),                         // 19: insights.secrets.config.TextExplainerSectionConfig
	(*CardInfoComponentConfig)(nil),                            // 20: insights.secrets.config.CardInfoComponentConfig
	(*ChartDragIndicatorConfig)(nil),                           // 21: insights.secrets.config.ChartDragIndicatorConfig
	(*ChartLineConfig)(nil),                                    // 22: insights.secrets.config.ChartLineConfig
	(*AreaChartCardConfig)(nil),                                // 23: insights.secrets.config.AreaChartCardConfig
	(*ActionableBannerConfig)(nil),                             // 24: insights.secrets.config.ActionableBannerConfig
	(*GridVisualisationCardConfig)(nil),                        // 25: insights.secrets.config.GridVisualisationCardConfig
	(*GridComponentConfig)(nil),                                // 26: insights.secrets.config.GridComponentConfig
	(*GridCardConfig)(nil),                                     // 27: insights.secrets.config.GridCardConfig
	(*GridTileConfig)(nil),                                     // 28: insights.secrets.config.GridTileConfig
	(*GridComponentBottomCardConfig)(nil),                      // 29: insights.secrets.config.GridComponentBottomCardConfig
	(*GridComponentBottomCardValueConfig)(nil),                 // 30: insights.secrets.config.GridComponentBottomCardValueConfig
	(*DonutCardConfig)(nil),                                    // 31: insights.secrets.config.DonutCardConfig
	(*DonutSliceConfig)(nil),                                   // 32: insights.secrets.config.DonutSliceConfig
	(*ActionableBannerV2Config)(nil),                           // 33: insights.secrets.config.ActionableBannerV2Config
	(*SecretSummaryCardUiConfig)(nil),                          // 34: insights.secrets.config.SecretSummaryCardUiConfig
	(*ITCConfiguration_BorderConfig)(nil),                      // 35: insights.secrets.config.ITCConfiguration.BorderConfig
	(*ImageCardConfig_ValueComponentConfig)(nil),               // 36: insights.secrets.config.ImageCardConfig.ValueComponentConfig
	(*LearnOnTheGoSectionItemConfig_RedirectionConfig)(nil),    // 37: insights.secrets.config.LearnOnTheGoSectionItemConfig.RedirectionConfig
	(*TextExplainerSectionConfig_ExpandableControlConfig)(nil), // 38: insights.secrets.config.TextExplainerSectionConfig.ExpandableControlConfig
	(*ui.IconTextComponent_ContainerProperties)(nil),           // 39: api.typesv2.ui.IconTextComponent.ContainerProperties
	(*deeplink.Deeplink)(nil),                                  // 40: frontend.deeplink.Deeplink
	(common.FontStyle)(0),                                      // 41: api.typesv2.common.FontStyle
	(common.Text_TextAlignment)(0),                             // 42: api.typesv2.common.Text.TextAlignment
	(*widget.BackgroundColour)(nil),                            // 43: api.typesv2.common.ui.widget.BackgroundColour
	(*ui.BorderProperty)(nil),                                  // 44: api.typesv2.ui.BorderProperty
}
var file_api_insights_secrets_config_ui_configuration_proto_depIdxs = []int32{
	2,  // 0: insights.secrets.config.SecretSummaryCardConfiguration.title_config:type_name -> insights.secrets.config.ITCConfiguration
	4,  // 1: insights.secrets.config.SecretSummaryCardConfiguration.value_config:type_name -> insights.secrets.config.TextConfiguration
	6,  // 2: insights.secrets.config.SecretSummaryCardConfiguration.visualisation_config:type_name -> insights.secrets.config.VisualisationConfiguration
	0,  // 3: insights.secrets.config.SecretSummaryCardConfiguration.secret_summary_disabled_state:type_name -> insights.secrets.config.SecretSummaryDisabledState
	6,  // 4: insights.secrets.config.ITCConfiguration.left_visualisation_config:type_name -> insights.secrets.config.VisualisationConfiguration
	4,  // 5: insights.secrets.config.ITCConfiguration.text_configs:type_name -> insights.secrets.config.TextConfiguration
	6,  // 6: insights.secrets.config.ITCConfiguration.right_visualisation_config:type_name -> insights.secrets.config.VisualisationConfiguration
	35, // 7: insights.secrets.config.ITCConfiguration.border_config:type_name -> insights.secrets.config.ITCConfiguration.BorderConfig
	39, // 8: insights.secrets.config.ITCConfiguration.container_properties:type_name -> api.typesv2.ui.IconTextComponent.ContainerProperties
	40, // 9: insights.secrets.config.ITCConfiguration.deeplink:type_name -> frontend.deeplink.Deeplink
	2,  // 10: insights.secrets.config.VerticalITCConfig.title_config:type_name -> insights.secrets.config.ITCConfiguration
	2,  // 11: insights.secrets.config.VerticalITCConfig.value_config:type_name -> insights.secrets.config.ITCConfiguration
	41, // 12: insights.secrets.config.TextConfiguration.font_style:type_name -> api.typesv2.common.FontStyle
	5,  // 13: insights.secrets.config.TextConfiguration.display_template:type_name -> insights.secrets.config.DisplayTemplate
	42, // 14: insights.secrets.config.TextConfiguration.text_alignment:type_name -> api.typesv2.common.Text.TextAlignment
	2,  // 15: insights.secrets.config.VisualisationCardConfig.pro_tip_config:type_name -> insights.secrets.config.ITCConfiguration
	8,  // 16: insights.secrets.config.VisualisationCardConfig.number_card_config:type_name -> insights.secrets.config.NumberCardConfig
	9,  // 17: insights.secrets.config.VisualisationCardConfig.bar_chart_card_config:type_name -> insights.secrets.config.BarChartCardConfig
	14, // 18: insights.secrets.config.VisualisationCardConfig.image_card_config:type_name -> insights.secrets.config.ImageCardConfig
	23, // 19: insights.secrets.config.VisualisationCardConfig.area_chart_card_config:type_name -> insights.secrets.config.AreaChartCardConfig
	25, // 20: insights.secrets.config.VisualisationCardConfig.grid_visualisation_card_config:type_name -> insights.secrets.config.GridVisualisationCardConfig
	31, // 21: insights.secrets.config.VisualisationCardConfig.donut_card_config:type_name -> insights.secrets.config.DonutCardConfig
	3,  // 22: insights.secrets.config.NumberCardConfig.title_value_config:type_name -> insights.secrets.config.VerticalITCConfig
	6,  // 23: insights.secrets.config.NumberCardConfig.image_config:type_name -> insights.secrets.config.VisualisationConfiguration
	3,  // 24: insights.secrets.config.BarChartCardConfig.title_value_config:type_name -> insights.secrets.config.VerticalITCConfig
	2,  // 25: insights.secrets.config.BarChartCardConfig.score_insight_config:type_name -> insights.secrets.config.ITCConfiguration
	11, // 26: insights.secrets.config.BarChartCardConfig.bar_chart_config:type_name -> insights.secrets.config.BarChartConfig
	10, // 27: insights.secrets.config.BarChartCardConfig.reference_line:type_name -> insights.secrets.config.ReferenceLine
	2,  // 28: insights.secrets.config.ReferenceLine.title_config:type_name -> insights.secrets.config.ITCConfiguration
	12, // 29: insights.secrets.config.BarChartConfig.default_bar_config:type_name -> insights.secrets.config.BarConfig
	13, // 30: insights.secrets.config.BarConfig.top_display_component_config:type_name -> insights.secrets.config.BarDisplayComponentConfig
	13, // 31: insights.secrets.config.BarConfig.bottom_display_component_config:type_name -> insights.secrets.config.BarDisplayComponentConfig
	6,  // 32: insights.secrets.config.BarDisplayComponentConfig.image_config:type_name -> insights.secrets.config.VisualisationConfiguration
	2,  // 33: insights.secrets.config.BarDisplayComponentConfig.display_text_config:type_name -> insights.secrets.config.ITCConfiguration
	2,  // 34: insights.secrets.config.ImageCardConfig.title_config:type_name -> insights.secrets.config.ITCConfiguration
	6,  // 35: insights.secrets.config.ImageCardConfig.background_image_config:type_name -> insights.secrets.config.VisualisationConfiguration
	36, // 36: insights.secrets.config.ImageCardConfig.value_component_config:type_name -> insights.secrets.config.ImageCardConfig.ValueComponentConfig
	16, // 37: insights.secrets.config.LineItemsConfig.line_item_config:type_name -> insights.secrets.config.LineItemConfig
	4,  // 38: insights.secrets.config.LineItemsConfig.left_aligned_heading_config:type_name -> insights.secrets.config.TextConfiguration
	4,  // 39: insights.secrets.config.LineItemsConfig.right_aligned_heading_config:type_name -> insights.secrets.config.TextConfiguration
	2,  // 40: insights.secrets.config.LineItemConfig.left_heading_config:type_name -> insights.secrets.config.ITCConfiguration
	2,  // 41: insights.secrets.config.LineItemConfig.left_tags_config:type_name -> insights.secrets.config.ITCConfiguration
	2,  // 42: insights.secrets.config.LineItemConfig.right_heading_config:type_name -> insights.secrets.config.ITCConfiguration
	2,  // 43: insights.secrets.config.LineItemConfig.right_tags_config:type_name -> insights.secrets.config.ITCConfiguration
	6,  // 44: insights.secrets.config.LineItemConfig.left_image_config:type_name -> insights.secrets.config.VisualisationConfiguration
	4,  // 45: insights.secrets.config.LearnOnTheGoSectionConfig.title_config:type_name -> insights.secrets.config.TextConfiguration
	18, // 46: insights.secrets.config.LearnOnTheGoSectionConfig.section_items_config:type_name -> insights.secrets.config.LearnOnTheGoSectionItemConfig
	6,  // 47: insights.secrets.config.LearnOnTheGoSectionItemConfig.image_config:type_name -> insights.secrets.config.VisualisationConfiguration
	2,  // 48: insights.secrets.config.LearnOnTheGoSectionItemConfig.info_tag_config:type_name -> insights.secrets.config.ITCConfiguration
	4,  // 49: insights.secrets.config.LearnOnTheGoSectionItemConfig.description_config:type_name -> insights.secrets.config.TextConfiguration
	37, // 50: insights.secrets.config.LearnOnTheGoSectionItemConfig.redirection_config:type_name -> insights.secrets.config.LearnOnTheGoSectionItemConfig.RedirectionConfig
	2,  // 51: insights.secrets.config.TextExplainerSectionConfig.title:type_name -> insights.secrets.config.ITCConfiguration
	4,  // 52: insights.secrets.config.TextExplainerSectionConfig.descriptions:type_name -> insights.secrets.config.TextConfiguration
	38, // 53: insights.secrets.config.TextExplainerSectionConfig.expandable_control_config:type_name -> insights.secrets.config.TextExplainerSectionConfig.ExpandableControlConfig
	2,  // 54: insights.secrets.config.TextExplainerSectionConfig.cta_configs:type_name -> insights.secrets.config.ITCConfiguration
	3,  // 55: insights.secrets.config.CardInfoComponentConfig.title_value_config:type_name -> insights.secrets.config.VerticalITCConfig
	2,  // 56: insights.secrets.config.CardInfoComponentConfig.insight_config:type_name -> insights.secrets.config.ITCConfiguration
	2,  // 57: insights.secrets.config.ChartDragIndicatorConfig.indicatorLabelConfig:type_name -> insights.secrets.config.ITCConfiguration
	20, // 58: insights.secrets.config.AreaChartCardConfig.card_info_component_config:type_name -> insights.secrets.config.CardInfoComponentConfig
	43, // 59: insights.secrets.config.AreaChartCardConfig.area_fill_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	21, // 60: insights.secrets.config.AreaChartCardConfig.chart_drag_indicator_config:type_name -> insights.secrets.config.ChartDragIndicatorConfig
	22, // 61: insights.secrets.config.AreaChartCardConfig.chart_line_config:type_name -> insights.secrets.config.ChartLineConfig
	4,  // 62: insights.secrets.config.AreaChartCardConfig.horizontal_axis_label_config:type_name -> insights.secrets.config.TextConfiguration
	6,  // 63: insights.secrets.config.ActionableBannerConfig.left_image_config:type_name -> insights.secrets.config.VisualisationConfiguration
	4,  // 64: insights.secrets.config.ActionableBannerConfig.description_config:type_name -> insights.secrets.config.TextConfiguration
	2,  // 65: insights.secrets.config.ActionableBannerConfig.right_component_config:type_name -> insights.secrets.config.ITCConfiguration
	40, // 66: insights.secrets.config.ActionableBannerConfig.deeplink:type_name -> frontend.deeplink.Deeplink
	44, // 67: insights.secrets.config.ActionableBannerConfig.border_property:type_name -> api.typesv2.ui.BorderProperty
	3,  // 68: insights.secrets.config.GridVisualisationCardConfig.title_value_config:type_name -> insights.secrets.config.VerticalITCConfig
	26, // 69: insights.secrets.config.GridVisualisationCardConfig.grid_component_config:type_name -> insights.secrets.config.GridComponentConfig
	4,  // 70: insights.secrets.config.GridComponentConfig.title_config:type_name -> insights.secrets.config.TextConfiguration
	27, // 71: insights.secrets.config.GridComponentConfig.grid_card_config:type_name -> insights.secrets.config.GridCardConfig
	28, // 72: insights.secrets.config.GridCardConfig.grid_tile_config:type_name -> insights.secrets.config.GridTileConfig
	2,  // 73: insights.secrets.config.GridTileConfig.title_config:type_name -> insights.secrets.config.ITCConfiguration
	2,  // 74: insights.secrets.config.GridTileConfig.value_config:type_name -> insights.secrets.config.ITCConfiguration
	6,  // 75: insights.secrets.config.GridTileConfig.icon_config:type_name -> insights.secrets.config.VisualisationConfiguration
	29, // 76: insights.secrets.config.GridTileConfig.bottom_card_config:type_name -> insights.secrets.config.GridComponentBottomCardConfig
	2,  // 77: insights.secrets.config.GridComponentBottomCardConfig.title_config:type_name -> insights.secrets.config.ITCConfiguration
	30, // 78: insights.secrets.config.GridComponentBottomCardConfig.value_config:type_name -> insights.secrets.config.GridComponentBottomCardValueConfig
	2,  // 79: insights.secrets.config.GridComponentBottomCardValueConfig.key_config:type_name -> insights.secrets.config.ITCConfiguration
	2,  // 80: insights.secrets.config.GridComponentBottomCardValueConfig.value_config:type_name -> insights.secrets.config.ITCConfiguration
	3,  // 81: insights.secrets.config.DonutCardConfig.title_value_config:type_name -> insights.secrets.config.VerticalITCConfig
	32, // 82: insights.secrets.config.DonutCardConfig.donut_slice_config:type_name -> insights.secrets.config.DonutSliceConfig
	6,  // 83: insights.secrets.config.ActionableBannerV2Config.right_image_config:type_name -> insights.secrets.config.VisualisationConfiguration
	4,  // 84: insights.secrets.config.ActionableBannerV2Config.title_config:type_name -> insights.secrets.config.TextConfiguration
	2,  // 85: insights.secrets.config.ActionableBannerV2Config.cta_component_config:type_name -> insights.secrets.config.ITCConfiguration
	43, // 86: insights.secrets.config.ActionableBannerV2Config.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	40, // 87: insights.secrets.config.ActionableBannerV2Config.deeplink:type_name -> frontend.deeplink.Deeplink
	4,  // 88: insights.secrets.config.SecretSummaryCardUiConfig.title_config:type_name -> insights.secrets.config.TextConfiguration
	1,  // 89: insights.secrets.config.SecretSummaryCardUiConfig.secret_summary_card_config:type_name -> insights.secrets.config.SecretSummaryCardConfiguration
	6,  // 90: insights.secrets.config.ImageCardConfig.ValueComponentConfig.image_config:type_name -> insights.secrets.config.VisualisationConfiguration
	3,  // 91: insights.secrets.config.ImageCardConfig.ValueComponentConfig.secret_value_pair_config:type_name -> insights.secrets.config.VerticalITCConfig
	40, // 92: insights.secrets.config.LearnOnTheGoSectionItemConfig.RedirectionConfig.deeplink:type_name -> frontend.deeplink.Deeplink
	6,  // 93: insights.secrets.config.TextExplainerSectionConfig.ExpandableControlConfig.toggle_image_config:type_name -> insights.secrets.config.VisualisationConfiguration
	94, // [94:94] is the sub-list for method output_type
	94, // [94:94] is the sub-list for method input_type
	94, // [94:94] is the sub-list for extension type_name
	94, // [94:94] is the sub-list for extension extendee
	0,  // [0:94] is the sub-list for field type_name
}

func init() { file_api_insights_secrets_config_ui_configuration_proto_init() }
func file_api_insights_secrets_config_ui_configuration_proto_init() {
	if File_api_insights_secrets_config_ui_configuration_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecretSummaryCardConfiguration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ITCConfiguration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerticalITCConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextConfiguration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisplayTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VisualisationConfiguration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VisualisationCardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NumberCardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BarChartCardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReferenceLine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BarChartConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BarConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BarDisplayComponentConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageCardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LineItemsConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LineItemConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LearnOnTheGoSectionConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LearnOnTheGoSectionItemConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextExplainerSectionConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardInfoComponentConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChartDragIndicatorConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChartLineConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AreaChartCardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActionableBannerConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GridVisualisationCardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GridComponentConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GridCardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GridTileConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GridComponentBottomCardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GridComponentBottomCardValueConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DonutCardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DonutSliceConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActionableBannerV2Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecretSummaryCardUiConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ITCConfiguration_BorderConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageCardConfig_ValueComponentConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LearnOnTheGoSectionItemConfig_RedirectionConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_secrets_config_ui_configuration_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextExplainerSectionConfig_ExpandableControlConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_insights_secrets_config_ui_configuration_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*VisualisationCardConfig_NumberCardConfig)(nil),
		(*VisualisationCardConfig_BarChartCardConfig)(nil),
		(*VisualisationCardConfig_ImageCardConfig)(nil),
		(*VisualisationCardConfig_AreaChartCardConfig)(nil),
		(*VisualisationCardConfig_GridVisualisationCardConfig)(nil),
		(*VisualisationCardConfig_DonutCardConfig)(nil),
	}
	file_api_insights_secrets_config_ui_configuration_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*BarDisplayComponentConfig_ImageConfig)(nil),
		(*BarDisplayComponentConfig_DisplayTextConfig)(nil),
	}
	file_api_insights_secrets_config_ui_configuration_proto_msgTypes[36].OneofWrappers = []interface{}{
		(*LearnOnTheGoSectionItemConfig_RedirectionConfig_Deeplink)(nil),
		(*LearnOnTheGoSectionItemConfig_RedirectionConfig_StorifymeUrl)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_insights_secrets_config_ui_configuration_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   38,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_insights_secrets_config_ui_configuration_proto_goTypes,
		DependencyIndexes: file_api_insights_secrets_config_ui_configuration_proto_depIdxs,
		EnumInfos:         file_api_insights_secrets_config_ui_configuration_proto_enumTypes,
		MessageInfos:      file_api_insights_secrets_config_ui_configuration_proto_msgTypes,
	}.Build()
	File_api_insights_secrets_config_ui_configuration_proto = out.File
	file_api_insights_secrets_config_ui_configuration_proto_rawDesc = nil
	file_api_insights_secrets_config_ui_configuration_proto_goTypes = nil
	file_api_insights_secrets_config_ui_configuration_proto_depIdxs = nil
}
