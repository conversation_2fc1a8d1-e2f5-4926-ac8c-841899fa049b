package networth

import moneyPb "google.golang.org/genproto/googleapis/type/money"

// MinimalRespForLLM returns response with only important fields needed for LLM(to MCP etc)
// It will filter out all the Fi internal details
func (r *GetNetWorthValueResponse) MinimalRespForLLM() *GetNetWorthValueResponse {
	newAssets := make([]*AssetValue, 0)
	var totalValueUnits int64
	for _, asset := range r.GetAssetValues() {
		if asset.GetValue().GetUnits() != 0 {
			totalValueUnits += asset.GetValue().GetUnits()
			newAssets = append(newAssets, &AssetValue{
				NetWorthAttribute: asset.GetNetWorthAttribute(),
				Value: &moneyPb.Money{
					CurrencyCode: asset.GetValue().GetCurrencyCode(),
					Units:        asset.GetValue().GetUnits(),
				},
			})
		}
	}
	newLiabilities := make([]*LiabilityValue, 0)
	for _, asset := range r.GetLiabilityValues() {
		if asset.GetValue().GetUnits() != 0 {
			totalValueUnits -= asset.GetValue().GetUnits()
			newLiabilities = append(newLiabilities, &LiabilityValue{
				NetWorthAttribute: asset.GetNetWorthAttribute(),
				Value: &moneyPb.Money{
					CurrencyCode: asset.GetValue().GetCurrencyCode(),
					Units:        asset.GetValue().GetUnits(),
				},
			})
		}
	}
	res := &GetNetWorthValueResponse{
		AssetValues:     newAssets,
		LiabilityValues: newLiabilities,
		// TODO(sainath): do proper sum with currency code checks
		TotalNetWorthValue: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        totalValueUnits,
		},
	}
	return res
}
