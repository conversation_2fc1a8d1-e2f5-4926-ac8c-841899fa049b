package networth

import (
	"fmt"
	"sync"

	enumsPb "github.com/epifi/gamma/api/insights/networth/enums"
)

// Define the mapping from internal AssetType to proto AssetType
var (
	internalToPbMap = map[AssetType]enumsPb.AssetType{
		AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS:             enumsPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
		AssetType_ASSET_TYPE_FIXED_DEPOSITS:               enumsPb.AssetType_ASSET_TYPE_FIXED_DEPOSITS,
		AssetType_ASSET_TYPE_EPF:                          enumsPb.AssetType_ASSET_TYPE_EPF,
		AssetType_ASSET_TYPE_MUTUAL_FUND:                  enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND,
		AssetType_ASSET_TYPE_INDIAN_SECURITIES:            enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES,
		AssetType_ASSET_TYPE_US_SECURITIES:                enumsPb.AssetType_ASSET_TYPE_US_SECURITIES,
		AssetType_ASSET_TYPE_P2P_LENDING:                  enumsPb.AssetType_ASSET_TYPE_P2P_LENDING,
		AssetType_ASSET_TYPE_AIF:                          enumsPb.AssetType_ASSET_TYPE_AIF,
		AssetType_ASSET_TYPE_PRIVATE_EQUITY:               enumsPb.AssetType_ASSET_TYPE_PRIVATE_EQUITY,
		AssetType_ASSET_TYPE_REAL_ESTATE:                  enumsPb.AssetType_ASSET_TYPE_REAL_ESTATE,
		AssetType_ASSET_TYPE_ART_ARTEFACTS:                enumsPb.AssetType_ASSET_TYPE_ART_ARTEFACTS,
		AssetType_ASSET_TYPE_BONDS:                        enumsPb.AssetType_ASSET_TYPE_BONDS,
		AssetType_ASSET_TYPE_CASH:                         enumsPb.AssetType_ASSET_TYPE_CASH,
		AssetType_ASSET_TYPE_DIGITAL_GOLD:                 enumsPb.AssetType_ASSET_TYPE_DIGITAL_GOLD,
		AssetType_ASSET_TYPE_DIGITAL_SILVER:               enumsPb.AssetType_ASSET_TYPE_DIGITAL_SILVER,
		AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE: enumsPb.AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE,
		AssetType_ASSET_TYPE_PUBLIC_PROVIDENT_FUND:        enumsPb.AssetType_ASSET_TYPE_PUBLIC_PROVIDENT_FUND,
		AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION:        enumsPb.AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION,
		AssetType_ASSET_TYPE_NPS:                          enumsPb.AssetType_ASSET_TYPE_NPS,
		AssetType_ASSET_TYPE_GADGETS:                      enumsPb.AssetType_ASSET_TYPE_GADGETS,
		AssetType_ASSET_TYPE_VEHICLES:                     enumsPb.AssetType_ASSET_TYPE_VEHICLES,
		AssetType_ASSET_TYPE_CRYPTO:                       enumsPb.AssetType_ASSET_TYPE_CRYPTO,
		AssetType_ASSET_TYPE_FURNITURE:                    enumsPb.AssetType_ASSET_TYPE_FURNITURE,
		AssetType_ASSET_TYPE_COLLECTIBLES:                 enumsPb.AssetType_ASSET_TYPE_COLLECTIBLES,
		AssetType_ASSET_TYPE_JEWELLERY:                    enumsPb.AssetType_ASSET_TYPE_JEWELLERY,
		AssetType_ASSET_TYPE_OTHERS:                       enumsPb.AssetType_ASSET_TYPE_OTHERS,
	}
	pbToInternalMap    = map[enumsPb.AssetType]AssetType{}
	reverseMapInitOnce sync.Once
)

// ensureReverseMapInitialized ensures the reverse mapping is initialized
func ensureReverseMapInitialized() {
	reverseMapInitOnce.Do(func() {
		for internal, pb := range internalToPbMap {
			pbToInternalMap[pb] = internal
		}
	})
}

func (a AssetType) ToEnumAssetTypeWithoutError() enumsPb.AssetType {
	assetType, _ := a.ToEnumAssetType()
	return assetType
}

func (a AssetType) ToEnumAssetType() (enumsPb.AssetType, error) {
	if pbType, ok := internalToPbMap[a]; ok {
		return pbType, nil
	}
	return enumsPb.AssetType_ASSET_TYPE_UNSPECIFIED, fmt.Errorf("unknown asset type %v", a)
}

func FromEnumAssetTypeWithoutError(assetType enumsPb.AssetType) AssetType {
	result, _ := FromEnumAssetType(assetType)
	return result
}

func FromEnumAssetType(assetType enumsPb.AssetType) (AssetType, error) {
	ensureReverseMapInitialized()
	if internalType, ok := pbToInternalMap[assetType]; ok {
		return internalType, nil
	}
	return AssetType_ASSET_TYPE_UNSPECIFIED, fmt.Errorf("unknown enum asset type %v", assetType)
}

func GetZeroAssetTypeAssetValueMap(assetTypes []enumsPb.AssetType) map[enumsPb.AssetType]*AssetValue {
	res := make(map[enumsPb.AssetType]*AssetValue)
	for _, assetType := range assetTypes {
		res[assetType] = &AssetValue{
			NetWorthAttribute: FromEnumAssetTypeWithoutError(assetType),
			AssetType:         assetType,
			ComputationStatus: ComputationStatus_COMPUTATION_STATUS_NOT_FOUND,
		}
	}
	return res
}
