// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/insights/networth/service.proto

package networth

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	file "github.com/epifi/be-common/api/typesv2/common/file"
	dynamic_elements "github.com/epifi/gamma/api/dynamic_elements"
	employment "github.com/epifi/gamma/api/employment"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	enums "github.com/epifi/gamma/api/insights/networth/enums"
	frontend "github.com/epifi/gamma/api/insights/networth/frontend"
	magicimport "github.com/epifi/gamma/api/insights/networth/magicimport"
	model "github.com/epifi/gamma/api/insights/networth/model"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AssetType defines broad assets contributing to net worth
type AssetType int32

const (
	AssetType_ASSET_TYPE_UNSPECIFIED AssetType = 0
	// saving account managed by Fi and connected via Account Aggregator
	AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS AssetType = 1
	// fixed/smart/recurring deposits managed by Fi and connected via Account Aggregator
	AssetType_ASSET_TYPE_FIXED_DEPOSITS AssetType = 2
	// EPF accounts connected by user
	AssetType_ASSET_TYPE_EPF AssetType = 3
	// Indian mutual fund investments
	AssetType_ASSET_TYPE_MUTUAL_FUND AssetType = 4
	// India listed securities. Stocks, Bonds, REITs, InvITs, Options, Futures etc
	AssetType_ASSET_TYPE_INDIAN_SECURITIES AssetType = 5
	// US listed securities
	AssetType_ASSET_TYPE_US_SECURITIES AssetType = 6
	// investments in p2p lending products
	AssetType_ASSET_TYPE_P2P_LENDING AssetType = 7
	// Alternate Investment Fund
	AssetType_ASSET_TYPE_AIF AssetType = 8
	// Private equity
	AssetType_ASSET_TYPE_PRIVATE_EQUITY AssetType = 9
	// Real Estate
	AssetType_ASSET_TYPE_REAL_ESTATE AssetType = 10
	// Art & Artefacts
	AssetType_ASSET_TYPE_ART_ARTEFACTS AssetType = 11
	// bonds
	AssetType_ASSET_TYPE_BONDS AssetType = 12
	// cash
	AssetType_ASSET_TYPE_CASH AssetType = 13
	// digital gold
	AssetType_ASSET_TYPE_DIGITAL_GOLD AssetType = 14
	// digital silver
	AssetType_ASSET_TYPE_DIGITAL_SILVER AssetType = 15
	// Portfolio Management Service (PMS)
	AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE AssetType = 16
	// Public Provident Fund (PPF)
	AssetType_ASSET_TYPE_PUBLIC_PROVIDENT_FUND AssetType = 17
	// Employee Stock Options (ESOP)
	AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION AssetType = 18
	// National Pension Scheme
	AssetType_ASSET_TYPE_NPS AssetType = 19
	// Gadgets
	AssetType_ASSET_TYPE_GADGETS AssetType = 20
	// Vehicles
	AssetType_ASSET_TYPE_VEHICLES AssetType = 21
	// Crypto
	AssetType_ASSET_TYPE_CRYPTO AssetType = 22
	// Furniture
	AssetType_ASSET_TYPE_FURNITURE AssetType = 23
	// Collectibles
	AssetType_ASSET_TYPE_COLLECTIBLES AssetType = 24
	// Jewellery
	AssetType_ASSET_TYPE_JEWELLERY AssetType = 25
	// Others
	AssetType_ASSET_TYPE_OTHERS AssetType = 26
)

// Enum value maps for AssetType.
var (
	AssetType_name = map[int32]string{
		0:  "ASSET_TYPE_UNSPECIFIED",
		1:  "ASSET_TYPE_SAVINGS_ACCOUNTS",
		2:  "ASSET_TYPE_FIXED_DEPOSITS",
		3:  "ASSET_TYPE_EPF",
		4:  "ASSET_TYPE_MUTUAL_FUND",
		5:  "ASSET_TYPE_INDIAN_SECURITIES",
		6:  "ASSET_TYPE_US_SECURITIES",
		7:  "ASSET_TYPE_P2P_LENDING",
		8:  "ASSET_TYPE_AIF",
		9:  "ASSET_TYPE_PRIVATE_EQUITY",
		10: "ASSET_TYPE_REAL_ESTATE",
		11: "ASSET_TYPE_ART_ARTEFACTS",
		12: "ASSET_TYPE_BONDS",
		13: "ASSET_TYPE_CASH",
		14: "ASSET_TYPE_DIGITAL_GOLD",
		15: "ASSET_TYPE_DIGITAL_SILVER",
		16: "ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE",
		17: "ASSET_TYPE_PUBLIC_PROVIDENT_FUND",
		18: "ASSET_TYPE_EMPLOYEE_STOCK_OPTION",
		19: "ASSET_TYPE_NPS",
		20: "ASSET_TYPE_GADGETS",
		21: "ASSET_TYPE_VEHICLES",
		22: "ASSET_TYPE_CRYPTO",
		23: "ASSET_TYPE_FURNITURE",
		24: "ASSET_TYPE_COLLECTIBLES",
		25: "ASSET_TYPE_JEWELLERY",
		26: "ASSET_TYPE_OTHERS",
	}
	AssetType_value = map[string]int32{
		"ASSET_TYPE_UNSPECIFIED":                  0,
		"ASSET_TYPE_SAVINGS_ACCOUNTS":             1,
		"ASSET_TYPE_FIXED_DEPOSITS":               2,
		"ASSET_TYPE_EPF":                          3,
		"ASSET_TYPE_MUTUAL_FUND":                  4,
		"ASSET_TYPE_INDIAN_SECURITIES":            5,
		"ASSET_TYPE_US_SECURITIES":                6,
		"ASSET_TYPE_P2P_LENDING":                  7,
		"ASSET_TYPE_AIF":                          8,
		"ASSET_TYPE_PRIVATE_EQUITY":               9,
		"ASSET_TYPE_REAL_ESTATE":                  10,
		"ASSET_TYPE_ART_ARTEFACTS":                11,
		"ASSET_TYPE_BONDS":                        12,
		"ASSET_TYPE_CASH":                         13,
		"ASSET_TYPE_DIGITAL_GOLD":                 14,
		"ASSET_TYPE_DIGITAL_SILVER":               15,
		"ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE": 16,
		"ASSET_TYPE_PUBLIC_PROVIDENT_FUND":        17,
		"ASSET_TYPE_EMPLOYEE_STOCK_OPTION":        18,
		"ASSET_TYPE_NPS":                          19,
		"ASSET_TYPE_GADGETS":                      20,
		"ASSET_TYPE_VEHICLES":                     21,
		"ASSET_TYPE_CRYPTO":                       22,
		"ASSET_TYPE_FURNITURE":                    23,
		"ASSET_TYPE_COLLECTIBLES":                 24,
		"ASSET_TYPE_JEWELLERY":                    25,
		"ASSET_TYPE_OTHERS":                       26,
	}
)

func (x AssetType) Enum() *AssetType {
	p := new(AssetType)
	*p = x
	return p
}

func (x AssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[0].Descriptor()
}

func (AssetType) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[0]
}

func (x AssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetType.Descriptor instead.
func (AssetType) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{0}
}

// LiabilityType defines broad liabilities contributing to net worth
type LiabilityType int32

const (
	LiabilityType_LIABILITY_TYPE_UNSPECIFIED LiabilityType = 0
	// outstanding balance across all credit cards
	LiabilityType_LIABILITY_TYPE_CREDIT_CARD_OUTSTANDING LiabilityType = 1
	// outstanding home loan amount
	LiabilityType_LIABILITY_TYPE_HOME_LOAN LiabilityType = 2
	// outstanding personal loan amount
	LiabilityType_LIABILITY_TYPE_PERSONAL_LOAN LiabilityType = 3
	// outstanding vehicle loan amount
	LiabilityType_LIABILITY_TYPE_VEHICLE_LOAN LiabilityType = 4
	// outstanding education loan amount
	LiabilityType_LIABILITY_TYPE_EDUCATION_LOAN LiabilityType = 5
	// other loan types not covered as any specific liability type
	LiabilityType_LIABILITY_TYPE_OTHER_LOAN LiabilityType = 6
)

// Enum value maps for LiabilityType.
var (
	LiabilityType_name = map[int32]string{
		0: "LIABILITY_TYPE_UNSPECIFIED",
		1: "LIABILITY_TYPE_CREDIT_CARD_OUTSTANDING",
		2: "LIABILITY_TYPE_HOME_LOAN",
		3: "LIABILITY_TYPE_PERSONAL_LOAN",
		4: "LIABILITY_TYPE_VEHICLE_LOAN",
		5: "LIABILITY_TYPE_EDUCATION_LOAN",
		6: "LIABILITY_TYPE_OTHER_LOAN",
	}
	LiabilityType_value = map[string]int32{
		"LIABILITY_TYPE_UNSPECIFIED":             0,
		"LIABILITY_TYPE_CREDIT_CARD_OUTSTANDING": 1,
		"LIABILITY_TYPE_HOME_LOAN":               2,
		"LIABILITY_TYPE_PERSONAL_LOAN":           3,
		"LIABILITY_TYPE_VEHICLE_LOAN":            4,
		"LIABILITY_TYPE_EDUCATION_LOAN":          5,
		"LIABILITY_TYPE_OTHER_LOAN":              6,
	}
)

func (x LiabilityType) Enum() *LiabilityType {
	p := new(LiabilityType)
	*p = x
	return p
}

func (x LiabilityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LiabilityType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[1].Descriptor()
}

func (LiabilityType) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[1]
}

func (x LiabilityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LiabilityType.Descriptor instead.
func (LiabilityType) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{1}
}

type ComputationStatus int32

const (
	ComputationStatus_COMPUTATION_STATUS_UNSPECIFIED ComputationStatus = 0
	ComputationStatus_COMPUTATION_STATUS_SUCCESS     ComputationStatus = 1
	// no data found for net worth attribute
	ComputationStatus_COMPUTATION_STATUS_NOT_FOUND ComputationStatus = 2
	// failed to compute net worth attribute balance
	ComputationStatus_COMPUTATION_STATUS_FAILED ComputationStatus = 3
)

// Enum value maps for ComputationStatus.
var (
	ComputationStatus_name = map[int32]string{
		0: "COMPUTATION_STATUS_UNSPECIFIED",
		1: "COMPUTATION_STATUS_SUCCESS",
		2: "COMPUTATION_STATUS_NOT_FOUND",
		3: "COMPUTATION_STATUS_FAILED",
	}
	ComputationStatus_value = map[string]int32{
		"COMPUTATION_STATUS_UNSPECIFIED": 0,
		"COMPUTATION_STATUS_SUCCESS":     1,
		"COMPUTATION_STATUS_NOT_FOUND":   2,
		"COMPUTATION_STATUS_FAILED":      3,
	}
)

func (x ComputationStatus) Enum() *ComputationStatus {
	p := new(ComputationStatus)
	*p = x
	return p
}

func (x ComputationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ComputationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[2].Descriptor()
}

func (ComputationStatus) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[2]
}

func (x ComputationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ComputationStatus.Descriptor instead.
func (ComputationStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{2}
}

type RefreshStatus int32

const (
	RefreshStatus_REFRESH_STATUS_UNSPECIFIED RefreshStatus = 0
	// last successful refresh is before instrument refresh threshold duration, so refresh is required
	RefreshStatus_REFRESH_STATUS_REQUIRED RefreshStatus = 1
	// last successful refresh is not before instrument refresh threshold duration, so refresh is not required
	RefreshStatus_REFRESH_STATUS_NOT_REQUIRED RefreshStatus = 2
	// last refresh request is in process for instrument process refresh threshold duration, so refresh is not required for now
	// if instrument refresh do not get successfully processed within process refresh threshold then refresh status would change to REFRESH_STATUS_REQUIRED
	// else as instrument is refreshed successfully refresh status would be REFRESH_STATUS_NOT_REQUIRED
	RefreshStatus_REFRESH_STATUS_IN_PROCESS RefreshStatus = 3
)

// Enum value maps for RefreshStatus.
var (
	RefreshStatus_name = map[int32]string{
		0: "REFRESH_STATUS_UNSPECIFIED",
		1: "REFRESH_STATUS_REQUIRED",
		2: "REFRESH_STATUS_NOT_REQUIRED",
		3: "REFRESH_STATUS_IN_PROCESS",
	}
	RefreshStatus_value = map[string]int32{
		"REFRESH_STATUS_UNSPECIFIED":  0,
		"REFRESH_STATUS_REQUIRED":     1,
		"REFRESH_STATUS_NOT_REQUIRED": 2,
		"REFRESH_STATUS_IN_PROCESS":   3,
	}
)

func (x RefreshStatus) Enum() *RefreshStatus {
	p := new(RefreshStatus)
	*p = x
	return p
}

func (x RefreshStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RefreshStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[3].Descriptor()
}

func (RefreshStatus) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[3]
}

func (x RefreshStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RefreshStatus.Descriptor instead.
func (RefreshStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{3}
}

type DeleteAllInvestmentDeclarationResponse_Status int32

const (
	DeleteAllInvestmentDeclarationResponse_OK       DeleteAllInvestmentDeclarationResponse_Status = 0
	DeleteAllInvestmentDeclarationResponse_INTERNAL DeleteAllInvestmentDeclarationResponse_Status = 13
)

// Enum value maps for DeleteAllInvestmentDeclarationResponse_Status.
var (
	DeleteAllInvestmentDeclarationResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	DeleteAllInvestmentDeclarationResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x DeleteAllInvestmentDeclarationResponse_Status) Enum() *DeleteAllInvestmentDeclarationResponse_Status {
	p := new(DeleteAllInvestmentDeclarationResponse_Status)
	*p = x
	return p
}

func (x DeleteAllInvestmentDeclarationResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeleteAllInvestmentDeclarationResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[4].Descriptor()
}

func (DeleteAllInvestmentDeclarationResponse_Status) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[4]
}

func (x DeleteAllInvestmentDeclarationResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeleteAllInvestmentDeclarationResponse_Status.Descriptor instead.
func (DeleteAllInvestmentDeclarationResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{7, 0}
}

type GetInvestmentDeclarationsResponse_Status int32

const (
	GetInvestmentDeclarationsResponse_OK       GetInvestmentDeclarationsResponse_Status = 0
	GetInvestmentDeclarationsResponse_INTERNAL GetInvestmentDeclarationsResponse_Status = 13
)

// Enum value maps for GetInvestmentDeclarationsResponse_Status.
var (
	GetInvestmentDeclarationsResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetInvestmentDeclarationsResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetInvestmentDeclarationsResponse_Status) Enum() *GetInvestmentDeclarationsResponse_Status {
	p := new(GetInvestmentDeclarationsResponse_Status)
	*p = x
	return p
}

func (x GetInvestmentDeclarationsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetInvestmentDeclarationsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[5].Descriptor()
}

func (GetInvestmentDeclarationsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[5]
}

func (x GetInvestmentDeclarationsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetInvestmentDeclarationsResponse_Status.Descriptor instead.
func (GetInvestmentDeclarationsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{9, 0}
}

type DeclareInvestmentResponse_Status int32

const (
	DeclareInvestmentResponse_OK       DeclareInvestmentResponse_Status = 0
	DeclareInvestmentResponse_INTERNAL DeclareInvestmentResponse_Status = 13
)

// Enum value maps for DeclareInvestmentResponse_Status.
var (
	DeclareInvestmentResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	DeclareInvestmentResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x DeclareInvestmentResponse_Status) Enum() *DeclareInvestmentResponse_Status {
	p := new(DeclareInvestmentResponse_Status)
	*p = x
	return p
}

func (x DeclareInvestmentResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeclareInvestmentResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[6].Descriptor()
}

func (DeclareInvestmentResponse_Status) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[6]
}

func (x DeclareInvestmentResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeclareInvestmentResponse_Status.Descriptor instead.
func (DeclareInvestmentResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{13, 0}
}

type UpdateInvestmentDeclarationResponse_Status int32

const (
	UpdateInvestmentDeclarationResponse_OK       UpdateInvestmentDeclarationResponse_Status = 0
	UpdateInvestmentDeclarationResponse_INTERNAL UpdateInvestmentDeclarationResponse_Status = 13
)

// Enum value maps for UpdateInvestmentDeclarationResponse_Status.
var (
	UpdateInvestmentDeclarationResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	UpdateInvestmentDeclarationResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x UpdateInvestmentDeclarationResponse_Status) Enum() *UpdateInvestmentDeclarationResponse_Status {
	p := new(UpdateInvestmentDeclarationResponse_Status)
	*p = x
	return p
}

func (x UpdateInvestmentDeclarationResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateInvestmentDeclarationResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[7].Descriptor()
}

func (UpdateInvestmentDeclarationResponse_Status) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[7]
}

func (x UpdateInvestmentDeclarationResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateInvestmentDeclarationResponse_Status.Descriptor instead.
func (UpdateInvestmentDeclarationResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{15, 0}
}

type GetInvestmentDeclarationResponse_Status int32

const (
	GetInvestmentDeclarationResponse_OK       GetInvestmentDeclarationResponse_Status = 0
	GetInvestmentDeclarationResponse_INTERNAL GetInvestmentDeclarationResponse_Status = 13
)

// Enum value maps for GetInvestmentDeclarationResponse_Status.
var (
	GetInvestmentDeclarationResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetInvestmentDeclarationResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetInvestmentDeclarationResponse_Status) Enum() *GetInvestmentDeclarationResponse_Status {
	p := new(GetInvestmentDeclarationResponse_Status)
	*p = x
	return p
}

func (x GetInvestmentDeclarationResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetInvestmentDeclarationResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[8].Descriptor()
}

func (GetInvestmentDeclarationResponse_Status) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[8]
}

func (x GetInvestmentDeclarationResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetInvestmentDeclarationResponse_Status.Descriptor instead.
func (GetInvestmentDeclarationResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{17, 0}
}

type DeleteInvestmentDeclarationResponse_Status int32

const (
	DeleteInvestmentDeclarationResponse_OK       DeleteInvestmentDeclarationResponse_Status = 0
	DeleteInvestmentDeclarationResponse_INTERNAL DeleteInvestmentDeclarationResponse_Status = 13
)

// Enum value maps for DeleteInvestmentDeclarationResponse_Status.
var (
	DeleteInvestmentDeclarationResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	DeleteInvestmentDeclarationResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x DeleteInvestmentDeclarationResponse_Status) Enum() *DeleteInvestmentDeclarationResponse_Status {
	p := new(DeleteInvestmentDeclarationResponse_Status)
	*p = x
	return p
}

func (x DeleteInvestmentDeclarationResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeleteInvestmentDeclarationResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[9].Descriptor()
}

func (DeleteInvestmentDeclarationResponse_Status) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[9]
}

func (x DeleteInvestmentDeclarationResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeleteInvestmentDeclarationResponse_Status.Descriptor instead.
func (DeleteInvestmentDeclarationResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{19, 0}
}

type GetNetWorthValueResponse_Status int32

const (
	GetNetWorthValueResponse_OK       GetNetWorthValueResponse_Status = 0
	GetNetWorthValueResponse_INTERNAL GetNetWorthValueResponse_Status = 13
)

// Enum value maps for GetNetWorthValueResponse_Status.
var (
	GetNetWorthValueResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetNetWorthValueResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetNetWorthValueResponse_Status) Enum() *GetNetWorthValueResponse_Status {
	p := new(GetNetWorthValueResponse_Status)
	*p = x
	return p
}

func (x GetNetWorthValueResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetNetWorthValueResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[10].Descriptor()
}

func (GetNetWorthValueResponse_Status) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[10]
}

func (x GetNetWorthValueResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetNetWorthValueResponse_Status.Descriptor instead.
func (GetNetWorthValueResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{24, 0}
}

type UpdateBulkManualAssetsCurrentValueResponse_Status int32

const (
	// Request has been processed successfully
	UpdateBulkManualAssetsCurrentValueResponse_OK UpdateBulkManualAssetsCurrentValueResponse_Status = 0
	// Invalid argument passed in the request
	UpdateBulkManualAssetsCurrentValueResponse_INVALID_ARGUMENT UpdateBulkManualAssetsCurrentValueResponse_Status = 3
	// internal server error
	UpdateBulkManualAssetsCurrentValueResponse_INTERNAL UpdateBulkManualAssetsCurrentValueResponse_Status = 13
	// not all the investment declarations updated successfully, some of them failed
	UpdateBulkManualAssetsCurrentValueResponse_PARTIAL_UPDATED UpdateBulkManualAssetsCurrentValueResponse_Status = 101
)

// Enum value maps for UpdateBulkManualAssetsCurrentValueResponse_Status.
var (
	UpdateBulkManualAssetsCurrentValueResponse_Status_name = map[int32]string{
		0:   "OK",
		3:   "INVALID_ARGUMENT",
		13:  "INTERNAL",
		101: "PARTIAL_UPDATED",
	}
	UpdateBulkManualAssetsCurrentValueResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
		"PARTIAL_UPDATED":  101,
	}
)

func (x UpdateBulkManualAssetsCurrentValueResponse_Status) Enum() *UpdateBulkManualAssetsCurrentValueResponse_Status {
	p := new(UpdateBulkManualAssetsCurrentValueResponse_Status)
	*p = x
	return p
}

func (x UpdateBulkManualAssetsCurrentValueResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateBulkManualAssetsCurrentValueResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[11].Descriptor()
}

func (UpdateBulkManualAssetsCurrentValueResponse_Status) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[11]
}

func (x UpdateBulkManualAssetsCurrentValueResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateBulkManualAssetsCurrentValueResponse_Status.Descriptor instead.
func (UpdateBulkManualAssetsCurrentValueResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{28, 0}
}

type CreateNetWorthRefreshSessionResponse_Status int32

const (
	CreateNetWorthRefreshSessionResponse_OK CreateNetWorthRefreshSessionResponse_Status = 0
	// internal server error
	CreateNetWorthRefreshSessionResponse_INTERNAL CreateNetWorthRefreshSessionResponse_Status = 13
)

// Enum value maps for CreateNetWorthRefreshSessionResponse_Status.
var (
	CreateNetWorthRefreshSessionResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	CreateNetWorthRefreshSessionResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x CreateNetWorthRefreshSessionResponse_Status) Enum() *CreateNetWorthRefreshSessionResponse_Status {
	p := new(CreateNetWorthRefreshSessionResponse_Status)
	*p = x
	return p
}

func (x CreateNetWorthRefreshSessionResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateNetWorthRefreshSessionResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[12].Descriptor()
}

func (CreateNetWorthRefreshSessionResponse_Status) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[12]
}

func (x CreateNetWorthRefreshSessionResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateNetWorthRefreshSessionResponse_Status.Descriptor instead.
func (CreateNetWorthRefreshSessionResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{30, 0}
}

type UpdateNetWorthRefreshSessionResponse_Status int32

const (
	UpdateNetWorthRefreshSessionResponse_OK UpdateNetWorthRefreshSessionResponse_Status = 0
	// netWorth refresh session not found for given id
	UpdateNetWorthRefreshSessionResponse_NOT_FOUND UpdateNetWorthRefreshSessionResponse_Status = 5
	// internal server error
	UpdateNetWorthRefreshSessionResponse_INTERNAL UpdateNetWorthRefreshSessionResponse_Status = 13
)

// Enum value maps for UpdateNetWorthRefreshSessionResponse_Status.
var (
	UpdateNetWorthRefreshSessionResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	UpdateNetWorthRefreshSessionResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x UpdateNetWorthRefreshSessionResponse_Status) Enum() *UpdateNetWorthRefreshSessionResponse_Status {
	p := new(UpdateNetWorthRefreshSessionResponse_Status)
	*p = x
	return p
}

func (x UpdateNetWorthRefreshSessionResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateNetWorthRefreshSessionResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[13].Descriptor()
}

func (UpdateNetWorthRefreshSessionResponse_Status) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[13]
}

func (x UpdateNetWorthRefreshSessionResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateNetWorthRefreshSessionResponse_Status.Descriptor instead.
func (UpdateNetWorthRefreshSessionResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{32, 0}
}

type GetNetWorthRefreshSessionResponse_Status int32

const (
	GetNetWorthRefreshSessionResponse_OK GetNetWorthRefreshSessionResponse_Status = 0
	// netWorth refresh session not found for given id
	GetNetWorthRefreshSessionResponse_NOT_FOUND GetNetWorthRefreshSessionResponse_Status = 5
	// internal server error
	GetNetWorthRefreshSessionResponse_INTERNAL GetNetWorthRefreshSessionResponse_Status = 13
)

// Enum value maps for GetNetWorthRefreshSessionResponse_Status.
var (
	GetNetWorthRefreshSessionResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetNetWorthRefreshSessionResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetNetWorthRefreshSessionResponse_Status) Enum() *GetNetWorthRefreshSessionResponse_Status {
	p := new(GetNetWorthRefreshSessionResponse_Status)
	*p = x
	return p
}

func (x GetNetWorthRefreshSessionResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetNetWorthRefreshSessionResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[14].Descriptor()
}

func (GetNetWorthRefreshSessionResponse_Status) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[14]
}

func (x GetNetWorthRefreshSessionResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetNetWorthRefreshSessionResponse_Status.Descriptor instead.
func (GetNetWorthRefreshSessionResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{34, 0}
}

type GetNetWorthInstrumentsRefreshDetailsResponse_Status int32

const (
	// Request has been processed successfully
	GetNetWorthInstrumentsRefreshDetailsResponse_OK GetNetWorthInstrumentsRefreshDetailsResponse_Status = 0
	// internal server error
	GetNetWorthInstrumentsRefreshDetailsResponse_INTERNAL GetNetWorthInstrumentsRefreshDetailsResponse_Status = 13
	// unable to fetch InstrumentRefreshDetails for all the instruments, some of them failed
	GetNetWorthInstrumentsRefreshDetailsResponse_PARTIAL GetNetWorthInstrumentsRefreshDetailsResponse_Status = 101
)

// Enum value maps for GetNetWorthInstrumentsRefreshDetailsResponse_Status.
var (
	GetNetWorthInstrumentsRefreshDetailsResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		101: "PARTIAL",
	}
	GetNetWorthInstrumentsRefreshDetailsResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
		"PARTIAL":  101,
	}
)

func (x GetNetWorthInstrumentsRefreshDetailsResponse_Status) Enum() *GetNetWorthInstrumentsRefreshDetailsResponse_Status {
	p := new(GetNetWorthInstrumentsRefreshDetailsResponse_Status)
	*p = x
	return p
}

func (x GetNetWorthInstrumentsRefreshDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetNetWorthInstrumentsRefreshDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_service_proto_enumTypes[15].Descriptor()
}

func (GetNetWorthInstrumentsRefreshDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_insights_networth_service_proto_enumTypes[15]
}

func (x GetNetWorthInstrumentsRefreshDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetNetWorthInstrumentsRefreshDetailsResponse_Status.Descriptor instead.
func (GetNetWorthInstrumentsRefreshDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{36, 0}
}

type MagicImportFilesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string       `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Files   []*file.File `protobuf:"bytes,2,rep,name=files,proto3" json:"files,omitempty"`
}

func (x *MagicImportFilesRequest) Reset() {
	*x = MagicImportFilesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MagicImportFilesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MagicImportFilesRequest) ProtoMessage() {}

func (x *MagicImportFilesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MagicImportFilesRequest.ProtoReflect.Descriptor instead.
func (*MagicImportFilesRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{0}
}

func (x *MagicImportFilesRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *MagicImportFilesRequest) GetFiles() []*file.File {
	if x != nil {
		return x.Files
	}
	return nil
}

type MagicImportFilesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status             *rpc.Status                     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NextAction         *deeplink.Deeplink              `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	MagicImportDetails *magicimport.MagicImportDetails `protobuf:"bytes,3,opt,name=magic_import_details,json=magicImportDetails,proto3" json:"magic_import_details,omitempty"`
}

func (x *MagicImportFilesResponse) Reset() {
	*x = MagicImportFilesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MagicImportFilesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MagicImportFilesResponse) ProtoMessage() {}

func (x *MagicImportFilesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MagicImportFilesResponse.ProtoReflect.Descriptor instead.
func (*MagicImportFilesResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{1}
}

func (x *MagicImportFilesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *MagicImportFilesResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *MagicImportFilesResponse) GetMagicImportDetails() *magicimport.MagicImportDetails {
	if x != nil {
		return x.MagicImportDetails
	}
	return nil
}

type GetAssetsDayChangeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor_id is mandatory
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Initial/Starting date
	// (not naming them as start and end date because these are not range queries but point in time queries)
	InitialDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=initial_date,json=initialDate,proto3" json:"initial_date,omitempty"`
	// Final/Ending date
	FinalDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=final_date,json=finalDate,proto3" json:"final_date,omitempty"`
	// AssetTypes to query changes for
	AssetTypes []enums.AssetType `protobuf:"varint,4,rep,packed,name=asset_types,json=assetTypes,proto3,enum=insights.networth.enums.AssetType" json:"asset_types,omitempty"`
}

func (x *GetAssetsDayChangeRequest) Reset() {
	*x = GetAssetsDayChangeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetsDayChangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetsDayChangeRequest) ProtoMessage() {}

func (x *GetAssetsDayChangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetsDayChangeRequest.ProtoReflect.Descriptor instead.
func (*GetAssetsDayChangeRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetAssetsDayChangeRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAssetsDayChangeRequest) GetInitialDate() *timestamppb.Timestamp {
	if x != nil {
		return x.InitialDate
	}
	return nil
}

func (x *GetAssetsDayChangeRequest) GetFinalDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FinalDate
	}
	return nil
}

func (x *GetAssetsDayChangeRequest) GetAssetTypes() []enums.AssetType {
	if x != nil {
		return x.AssetTypes
	}
	return nil
}

type GetAssetsDayChangeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Asset response map with insights.networth.enums.AssetType.String() as key
	AssetTypeToDayChangeResponseMap map[string]*AssetTypeDayChangeResponse `protobuf:"bytes,2,rep,name=asset_type_to_day_change_response_map,json=assetTypeToDayChangeResponseMap,proto3" json:"asset_type_to_day_change_response_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetAssetsDayChangeResponse) Reset() {
	*x = GetAssetsDayChangeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetsDayChangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetsDayChangeResponse) ProtoMessage() {}

func (x *GetAssetsDayChangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetsDayChangeResponse.ProtoReflect.Descriptor instead.
func (*GetAssetsDayChangeResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetAssetsDayChangeResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAssetsDayChangeResponse) GetAssetTypeToDayChangeResponseMap() map[string]*AssetTypeDayChangeResponse {
	if x != nil {
		return x.AssetTypeToDayChangeResponseMap
	}
	return nil
}

type AssetTypeDayChangeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Total value of asset at initial date
	InitialDateTotalValue *money.Money `protobuf:"bytes,1,opt,name=initial_date_total_value,json=initialDateTotalValue,proto3" json:"initial_date_total_value,omitempty"`
	// Total value of asset at final date
	FinalDateTotalValue *money.Money `protobuf:"bytes,2,opt,name=final_date_total_value,json=finalDateTotalValue,proto3" json:"final_date_total_value,omitempty"`
	// total change , combination of all instruments diff
	TotalChange float64 `protobuf:"fixed64,3,opt,name=total_change,json=totalChange,proto3" json:"total_change,omitempty"`
	// For each asset there can be multiple instruments and this field represent distributions for each instrument
	// For example, Multiple schemes for MF data, Multiple stocks for Stocks data etc
	AssetsValueChange []*AssetValueChange `protobuf:"bytes,4,rep,name=assets_value_change,json=assetsValueChange,proto3" json:"assets_value_change,omitempty"`
}

func (x *AssetTypeDayChangeResponse) Reset() {
	*x = AssetTypeDayChangeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetTypeDayChangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetTypeDayChangeResponse) ProtoMessage() {}

func (x *AssetTypeDayChangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetTypeDayChangeResponse.ProtoReflect.Descriptor instead.
func (*AssetTypeDayChangeResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{4}
}

func (x *AssetTypeDayChangeResponse) GetInitialDateTotalValue() *money.Money {
	if x != nil {
		return x.InitialDateTotalValue
	}
	return nil
}

func (x *AssetTypeDayChangeResponse) GetFinalDateTotalValue() *money.Money {
	if x != nil {
		return x.FinalDateTotalValue
	}
	return nil
}

func (x *AssetTypeDayChangeResponse) GetTotalChange() float64 {
	if x != nil {
		return x.TotalChange
	}
	return 0
}

func (x *AssetTypeDayChangeResponse) GetAssetsValueChange() []*AssetValueChange {
	if x != nil {
		return x.AssetsValueChange
	}
	return nil
}

type AssetValueChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// asset_id represents unique identifier for each instrument
	// For example, security_id for Stocks, mf_id for MF etc
	AssetId string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	// Value at initial_date
	InitialDateValue *money.Money `protobuf:"bytes,2,opt,name=initial_date_value,json=initialDateValue,proto3" json:"initial_date_value,omitempty"`
	// Value at final_date
	FinalDateValue *money.Money `protobuf:"bytes,3,opt,name=final_date_value,json=finalDateValue,proto3" json:"final_date_value,omitempty"`
	// difference is always final_date - initial_date
	// can be negative if value at initial_date is higher than value at final_date
	Change float64 `protobuf:"fixed64,4,opt,name=change,proto3" json:"change,omitempty"`
}

func (x *AssetValueChange) Reset() {
	*x = AssetValueChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetValueChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetValueChange) ProtoMessage() {}

func (x *AssetValueChange) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetValueChange.ProtoReflect.Descriptor instead.
func (*AssetValueChange) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{5}
}

func (x *AssetValueChange) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *AssetValueChange) GetInitialDateValue() *money.Money {
	if x != nil {
		return x.InitialDateValue
	}
	return nil
}

func (x *AssetValueChange) GetFinalDateValue() *money.Money {
	if x != nil {
		return x.FinalDateValue
	}
	return nil
}

func (x *AssetValueChange) GetChange() float64 {
	if x != nil {
		return x.Change
	}
	return 0
}

type DeleteAllInvestmentDeclarationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *DeleteAllInvestmentDeclarationRequest) Reset() {
	*x = DeleteAllInvestmentDeclarationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAllInvestmentDeclarationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAllInvestmentDeclarationRequest) ProtoMessage() {}

func (x *DeleteAllInvestmentDeclarationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAllInvestmentDeclarationRequest.ProtoReflect.Descriptor instead.
func (*DeleteAllInvestmentDeclarationRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteAllInvestmentDeclarationRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type DeleteAllInvestmentDeclarationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DeleteAllInvestmentDeclarationResponse) Reset() {
	*x = DeleteAllInvestmentDeclarationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAllInvestmentDeclarationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAllInvestmentDeclarationResponse) ProtoMessage() {}

func (x *DeleteAllInvestmentDeclarationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAllInvestmentDeclarationResponse.ProtoReflect.Descriptor instead.
func (*DeleteAllInvestmentDeclarationResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteAllInvestmentDeclarationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetInvestmentDeclarationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageContext *rpc.PageContextRequest `protobuf:"bytes,1,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	ActorId     string                  `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// declarations will be filtered based on instrument types
	InstrumentTypes []typesv2.InvestmentInstrumentType `protobuf:"varint,3,rep,packed,name=instrument_types,json=instrumentTypes,proto3,enum=api.typesv2.InvestmentInstrumentType" json:"instrument_types,omitempty"`
}

func (x *GetInvestmentDeclarationsRequest) Reset() {
	*x = GetInvestmentDeclarationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvestmentDeclarationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvestmentDeclarationsRequest) ProtoMessage() {}

func (x *GetInvestmentDeclarationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvestmentDeclarationsRequest.ProtoReflect.Descriptor instead.
func (*GetInvestmentDeclarationsRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetInvestmentDeclarationsRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *GetInvestmentDeclarationsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetInvestmentDeclarationsRequest) GetInstrumentTypes() []typesv2.InvestmentInstrumentType {
	if x != nil {
		return x.InstrumentTypes
	}
	return nil
}

type GetInvestmentDeclarationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PageContext *rpc.PageContextResponse `protobuf:"bytes,2,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	// Deprecated: Marked as deprecated in api/insights/networth/service.proto.
	InvestmentDeclaration []*model.InvestmentDeclaration `protobuf:"bytes,3,rep,name=investment_declaration,json=investmentDeclaration,proto3" json:"investment_declaration,omitempty"`
	InvestmentDetails     []*InvestmentDetails           `protobuf:"bytes,4,rep,name=investment_details,json=investmentDetails,proto3" json:"investment_details,omitempty"`
}

func (x *GetInvestmentDeclarationsResponse) Reset() {
	*x = GetInvestmentDeclarationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvestmentDeclarationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvestmentDeclarationsResponse) ProtoMessage() {}

func (x *GetInvestmentDeclarationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvestmentDeclarationsResponse.ProtoReflect.Descriptor instead.
func (*GetInvestmentDeclarationsResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetInvestmentDeclarationsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetInvestmentDeclarationsResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

// Deprecated: Marked as deprecated in api/insights/networth/service.proto.
func (x *GetInvestmentDeclarationsResponse) GetInvestmentDeclaration() []*model.InvestmentDeclaration {
	if x != nil {
		return x.InvestmentDeclaration
	}
	return nil
}

func (x *GetInvestmentDeclarationsResponse) GetInvestmentDetails() []*InvestmentDetails {
	if x != nil {
		return x.InvestmentDetails
	}
	return nil
}

type StoreSnapshotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Actor id to store snapshot for
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *StoreSnapshotRequest) Reset() {
	*x = StoreSnapshotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreSnapshotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreSnapshotRequest) ProtoMessage() {}

func (x *StoreSnapshotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreSnapshotRequest.ProtoReflect.Descriptor instead.
func (*StoreSnapshotRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{10}
}

func (x *StoreSnapshotRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type StoreSnapshotResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *StoreSnapshotResponse) Reset() {
	*x = StoreSnapshotResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreSnapshotResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreSnapshotResponse) ProtoMessage() {}

func (x *StoreSnapshotResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreSnapshotResponse.ProtoReflect.Descriptor instead.
func (*StoreSnapshotResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{11}
}

func (x *StoreSnapshotResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeclareInvestmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// instrument in which money was invested
	InstrumentType typesv2.InvestmentInstrumentType `protobuf:"varint,2,opt,name=instrument_type,json=instrumentType,proto3,enum=api.typesv2.InvestmentInstrumentType" json:"instrument_type,omitempty"`
	// amount invested in instrument
	InvestedAmount *money.Money `protobuf:"bytes,3,opt,name=invested_amount,json=investedAmount,proto3" json:"invested_amount,omitempty"`
	// time at which money was invested in the instrument
	InvestedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=invested_at,json=investedAt,proto3" json:"invested_at,omitempty"`
	// time at which invested amount will mature
	MaturityTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=maturity_time,json=maturityTime,proto3" json:"maturity_time,omitempty"`
	// the rate at which invested amount is growing
	InterestRate float64 `protobuf:"fixed64,6,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
	// metadata for instrument specific details
	DeclarationDetails *model.OtherDeclarationDetails `protobuf:"bytes,7,opt,name=declaration_details,json=declarationDetails,proto3" json:"declaration_details,omitempty"`
	// reference to consent recorded when user submitted this form
	ConsentId string `protobuf:"bytes,12,opt,name=consent_id,json=consentId,proto3" json:"consent_id,omitempty"`
}

func (x *DeclareInvestmentRequest) Reset() {
	*x = DeclareInvestmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeclareInvestmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeclareInvestmentRequest) ProtoMessage() {}

func (x *DeclareInvestmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeclareInvestmentRequest.ProtoReflect.Descriptor instead.
func (*DeclareInvestmentRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{12}
}

func (x *DeclareInvestmentRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DeclareInvestmentRequest) GetInstrumentType() typesv2.InvestmentInstrumentType {
	if x != nil {
		return x.InstrumentType
	}
	return typesv2.InvestmentInstrumentType(0)
}

func (x *DeclareInvestmentRequest) GetInvestedAmount() *money.Money {
	if x != nil {
		return x.InvestedAmount
	}
	return nil
}

func (x *DeclareInvestmentRequest) GetInvestedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.InvestedAt
	}
	return nil
}

func (x *DeclareInvestmentRequest) GetMaturityTime() *timestamppb.Timestamp {
	if x != nil {
		return x.MaturityTime
	}
	return nil
}

func (x *DeclareInvestmentRequest) GetInterestRate() float64 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

func (x *DeclareInvestmentRequest) GetDeclarationDetails() *model.OtherDeclarationDetails {
	if x != nil {
		return x.DeclarationDetails
	}
	return nil
}

func (x *DeclareInvestmentRequest) GetConsentId() string {
	if x != nil {
		return x.ConsentId
	}
	return ""
}

type DeclareInvestmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                *rpc.Status                  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	InvestmentDeclaration *model.InvestmentDeclaration `protobuf:"bytes,2,opt,name=investment_declaration,json=investmentDeclaration,proto3" json:"investment_declaration,omitempty"`
}

func (x *DeclareInvestmentResponse) Reset() {
	*x = DeclareInvestmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeclareInvestmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeclareInvestmentResponse) ProtoMessage() {}

func (x *DeclareInvestmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeclareInvestmentResponse.ProtoReflect.Descriptor instead.
func (*DeclareInvestmentResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{13}
}

func (x *DeclareInvestmentResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *DeclareInvestmentResponse) GetInvestmentDeclaration() *model.InvestmentDeclaration {
	if x != nil {
		return x.InvestmentDeclaration
	}
	return nil
}

type UpdateInvestmentDeclarationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UpdatedDeclaration *model.InvestmentDeclaration `protobuf:"bytes,1,opt,name=updated_declaration,json=updatedDeclaration,proto3" json:"updated_declaration,omitempty"`
	// fields to update
	FieldMasks []model.InvestmentDeclarationFieldMask `protobuf:"varint,2,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=insights.networth.model.InvestmentDeclarationFieldMask" json:"field_masks,omitempty"`
}

func (x *UpdateInvestmentDeclarationRequest) Reset() {
	*x = UpdateInvestmentDeclarationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateInvestmentDeclarationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateInvestmentDeclarationRequest) ProtoMessage() {}

func (x *UpdateInvestmentDeclarationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateInvestmentDeclarationRequest.ProtoReflect.Descriptor instead.
func (*UpdateInvestmentDeclarationRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateInvestmentDeclarationRequest) GetUpdatedDeclaration() *model.InvestmentDeclaration {
	if x != nil {
		return x.UpdatedDeclaration
	}
	return nil
}

func (x *UpdateInvestmentDeclarationRequest) GetFieldMasks() []model.InvestmentDeclarationFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

type UpdateInvestmentDeclarationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                *rpc.Status                  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	InvestmentDeclaration *model.InvestmentDeclaration `protobuf:"bytes,2,opt,name=investment_declaration,json=investmentDeclaration,proto3" json:"investment_declaration,omitempty"`
}

func (x *UpdateInvestmentDeclarationResponse) Reset() {
	*x = UpdateInvestmentDeclarationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateInvestmentDeclarationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateInvestmentDeclarationResponse) ProtoMessage() {}

func (x *UpdateInvestmentDeclarationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateInvestmentDeclarationResponse.ProtoReflect.Descriptor instead.
func (*UpdateInvestmentDeclarationResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateInvestmentDeclarationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateInvestmentDeclarationResponse) GetInvestmentDeclaration() *model.InvestmentDeclaration {
	if x != nil {
		return x.InvestmentDeclaration
	}
	return nil
}

type GetInvestmentDeclarationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/insights/networth/service.proto.
	Id         string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ActorId    string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ExternalId string `protobuf:"bytes,3,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
}

func (x *GetInvestmentDeclarationRequest) Reset() {
	*x = GetInvestmentDeclarationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvestmentDeclarationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvestmentDeclarationRequest) ProtoMessage() {}

func (x *GetInvestmentDeclarationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvestmentDeclarationRequest.ProtoReflect.Descriptor instead.
func (*GetInvestmentDeclarationRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{16}
}

// Deprecated: Marked as deprecated in api/insights/networth/service.proto.
func (x *GetInvestmentDeclarationRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetInvestmentDeclarationRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetInvestmentDeclarationRequest) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

type GetInvestmentDeclarationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Deprecated: Marked as deprecated in api/insights/networth/service.proto.
	InvestmentDeclaration *model.InvestmentDeclaration `protobuf:"bytes,2,opt,name=investment_declaration,json=investmentDeclaration,proto3" json:"investment_declaration,omitempty"`
	InvestmentDetails     *InvestmentDetails           `protobuf:"bytes,3,opt,name=investment_details,json=investmentDetails,proto3" json:"investment_details,omitempty"`
}

func (x *GetInvestmentDeclarationResponse) Reset() {
	*x = GetInvestmentDeclarationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvestmentDeclarationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvestmentDeclarationResponse) ProtoMessage() {}

func (x *GetInvestmentDeclarationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvestmentDeclarationResponse.ProtoReflect.Descriptor instead.
func (*GetInvestmentDeclarationResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetInvestmentDeclarationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Deprecated: Marked as deprecated in api/insights/networth/service.proto.
func (x *GetInvestmentDeclarationResponse) GetInvestmentDeclaration() *model.InvestmentDeclaration {
	if x != nil {
		return x.InvestmentDeclaration
	}
	return nil
}

func (x *GetInvestmentDeclarationResponse) GetInvestmentDetails() *InvestmentDetails {
	if x != nil {
		return x.InvestmentDetails
	}
	return nil
}

type DeleteInvestmentDeclarationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId    string `protobuf:"bytes,1,opt,name=actorId,proto3" json:"actorId,omitempty"`
	ExternalId string `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
}

func (x *DeleteInvestmentDeclarationRequest) Reset() {
	*x = DeleteInvestmentDeclarationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteInvestmentDeclarationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteInvestmentDeclarationRequest) ProtoMessage() {}

func (x *DeleteInvestmentDeclarationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteInvestmentDeclarationRequest.ProtoReflect.Descriptor instead.
func (*DeleteInvestmentDeclarationRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{18}
}

func (x *DeleteInvestmentDeclarationRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DeleteInvestmentDeclarationRequest) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

type DeleteInvestmentDeclarationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DeleteInvestmentDeclarationResponse) Reset() {
	*x = DeleteInvestmentDeclarationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteInvestmentDeclarationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteInvestmentDeclarationResponse) ProtoMessage() {}

func (x *DeleteInvestmentDeclarationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteInvestmentDeclarationResponse.ProtoReflect.Descriptor instead.
func (*DeleteInvestmentDeclarationResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteInvestmentDeclarationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// InvestmentDetails contains user declared investment attributes and enriched investment details
type InvestmentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvestmentDeclaration     *model.InvestmentDeclaration `protobuf:"bytes,1,opt,name=investment_declaration,json=investmentDeclaration,proto3" json:"investment_declaration,omitempty"`
	CurrentValue              *money.Money                 `protobuf:"bytes,2,opt,name=current_value,json=currentValue,proto3" json:"current_value,omitempty"`
	ComputedInvestmentDetails *ComputedInvestmentDetails   `protobuf:"bytes,3,opt,name=computed_investment_details,json=computedInvestmentDetails,proto3" json:"computed_investment_details,omitempty"`
}

func (x *InvestmentDetails) Reset() {
	*x = InvestmentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvestmentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvestmentDetails) ProtoMessage() {}

func (x *InvestmentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvestmentDetails.ProtoReflect.Descriptor instead.
func (*InvestmentDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{20}
}

func (x *InvestmentDetails) GetInvestmentDeclaration() *model.InvestmentDeclaration {
	if x != nil {
		return x.InvestmentDeclaration
	}
	return nil
}

func (x *InvestmentDetails) GetCurrentValue() *money.Money {
	if x != nil {
		return x.CurrentValue
	}
	return nil
}

func (x *InvestmentDetails) GetComputedInvestmentDetails() *ComputedInvestmentDetails {
	if x != nil {
		return x.ComputedInvestmentDetails
	}
	return nil
}

type ComputedInvestmentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to ComputedDetails:
	//
	//	*ComputedInvestmentDetails_EsopDetails
	ComputedDetails isComputedInvestmentDetails_ComputedDetails `protobuf_oneof:"computed_details"`
}

func (x *ComputedInvestmentDetails) Reset() {
	*x = ComputedInvestmentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComputedInvestmentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComputedInvestmentDetails) ProtoMessage() {}

func (x *ComputedInvestmentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComputedInvestmentDetails.ProtoReflect.Descriptor instead.
func (*ComputedInvestmentDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{21}
}

func (m *ComputedInvestmentDetails) GetComputedDetails() isComputedInvestmentDetails_ComputedDetails {
	if m != nil {
		return m.ComputedDetails
	}
	return nil
}

func (x *ComputedInvestmentDetails) GetEsopDetails() *EsopDetails {
	if x, ok := x.GetComputedDetails().(*ComputedInvestmentDetails_EsopDetails); ok {
		return x.EsopDetails
	}
	return nil
}

type isComputedInvestmentDetails_ComputedDetails interface {
	isComputedInvestmentDetails_ComputedDetails()
}

type ComputedInvestmentDetails_EsopDetails struct {
	EsopDetails *EsopDetails `protobuf:"bytes,1,opt,name=esop_details,json=esopDetails,proto3,oneof"`
}

func (*ComputedInvestmentDetails_EsopDetails) isComputedInvestmentDetails_ComputedDetails() {}

type EsopDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalVestedEsops float64 `protobuf:"fixed64,1,opt,name=total_vested_esops,json=totalVestedEsops,proto3" json:"total_vested_esops,omitempty"`
}

func (x *EsopDetails) Reset() {
	*x = EsopDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EsopDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EsopDetails) ProtoMessage() {}

func (x *EsopDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EsopDetails.ProtoReflect.Descriptor instead.
func (*EsopDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{22}
}

func (x *EsopDetails) GetTotalVestedEsops() float64 {
	if x != nil {
		return x.TotalVestedEsops
	}
	return 0
}

type GetNetWorthValueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId        string          `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	AssetTypes     []AssetType     `protobuf:"varint,2,rep,packed,name=asset_types,json=assetTypes,proto3,enum=insights.networth.AssetType" json:"asset_types,omitempty"`
	LiabilityTypes []LiabilityType `protobuf:"varint,3,rep,packed,name=liability_types,json=liabilityTypes,proto3,enum=insights.networth.LiabilityType" json:"liability_types,omitempty"`
}

func (x *GetNetWorthValueRequest) Reset() {
	*x = GetNetWorthValueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNetWorthValueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNetWorthValueRequest) ProtoMessage() {}

func (x *GetNetWorthValueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNetWorthValueRequest.ProtoReflect.Descriptor instead.
func (*GetNetWorthValueRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetNetWorthValueRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetNetWorthValueRequest) GetAssetTypes() []AssetType {
	if x != nil {
		return x.AssetTypes
	}
	return nil
}

func (x *GetNetWorthValueRequest) GetLiabilityTypes() []LiabilityType {
	if x != nil {
		return x.LiabilityTypes
	}
	return nil
}

type GetNetWorthValueResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status          *rpc.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	AssetValues     []*AssetValue     `protobuf:"bytes,2,rep,name=asset_values,json=assetValues,proto3" json:"asset_values,omitempty"`
	LiabilityValues []*LiabilityValue `protobuf:"bytes,3,rep,name=liability_values,json=liabilityValues,proto3" json:"liability_values,omitempty"`
	// total net worth value(total_assets_value - total_liabilities_value)
	// for llm only for now, not to use in other places
	TotalNetWorthValue *money.Money `protobuf:"bytes,4,opt,name=total_net_worth_value,json=totalNetWorthValue,proto3" json:"total_net_worth_value,omitempty"`
}

func (x *GetNetWorthValueResponse) Reset() {
	*x = GetNetWorthValueResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNetWorthValueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNetWorthValueResponse) ProtoMessage() {}

func (x *GetNetWorthValueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNetWorthValueResponse.ProtoReflect.Descriptor instead.
func (*GetNetWorthValueResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetNetWorthValueResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetNetWorthValueResponse) GetAssetValues() []*AssetValue {
	if x != nil {
		return x.AssetValues
	}
	return nil
}

func (x *GetNetWorthValueResponse) GetLiabilityValues() []*LiabilityValue {
	if x != nil {
		return x.LiabilityValues
	}
	return nil
}

func (x *GetNetWorthValueResponse) GetTotalNetWorthValue() *money.Money {
	if x != nil {
		return x.TotalNetWorthValue
	}
	return nil
}

type AssetValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: use asset_type instead
	//
	// Deprecated: Marked as deprecated in api/insights/networth/service.proto.
	NetWorthAttribute AssetType       `protobuf:"varint,1,opt,name=net_worth_attribute,json=netWorthAttribute,proto3,enum=insights.networth.AssetType" json:"net_worth_attribute,omitempty"`
	AssetType         enums.AssetType `protobuf:"varint,5,opt,name=asset_type,json=assetType,proto3,enum=insights.networth.enums.AssetType" json:"asset_type,omitempty"`
	// total balance under all instruments classified as the asset type
	Value *money.Money `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// status of balance computation. non success status indicate the balance is not available
	ComputationStatus ComputationStatus `protobuf:"varint,3,opt,name=computation_status,json=computationStatus,proto3,enum=insights.networth.ComputationStatus" json:"computation_status,omitempty"`
	// error message in case the balance computation failed
	ErrorMessage string `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

func (x *AssetValue) Reset() {
	*x = AssetValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetValue) ProtoMessage() {}

func (x *AssetValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetValue.ProtoReflect.Descriptor instead.
func (*AssetValue) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{25}
}

// Deprecated: Marked as deprecated in api/insights/networth/service.proto.
func (x *AssetValue) GetNetWorthAttribute() AssetType {
	if x != nil {
		return x.NetWorthAttribute
	}
	return AssetType_ASSET_TYPE_UNSPECIFIED
}

func (x *AssetValue) GetAssetType() enums.AssetType {
	if x != nil {
		return x.AssetType
	}
	return enums.AssetType(0)
}

func (x *AssetValue) GetValue() *money.Money {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *AssetValue) GetComputationStatus() ComputationStatus {
	if x != nil {
		return x.ComputationStatus
	}
	return ComputationStatus_COMPUTATION_STATUS_UNSPECIFIED
}

func (x *AssetValue) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

type LiabilityValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NetWorthAttribute LiabilityType `protobuf:"varint,1,opt,name=net_worth_attribute,json=netWorthAttribute,proto3,enum=insights.networth.LiabilityType" json:"net_worth_attribute,omitempty"`
	// total balance under all instruments classified as the liability type
	Value *money.Money `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// status of balance computation. non success status indicate the balance is not available
	ComputationStatus ComputationStatus `protobuf:"varint,3,opt,name=computation_status,json=computationStatus,proto3,enum=insights.networth.ComputationStatus" json:"computation_status,omitempty"`
	// error message in case the balance computation failed
	ErrorMessage string `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

func (x *LiabilityValue) Reset() {
	*x = LiabilityValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LiabilityValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LiabilityValue) ProtoMessage() {}

func (x *LiabilityValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LiabilityValue.ProtoReflect.Descriptor instead.
func (*LiabilityValue) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{26}
}

func (x *LiabilityValue) GetNetWorthAttribute() LiabilityType {
	if x != nil {
		return x.NetWorthAttribute
	}
	return LiabilityType_LIABILITY_TYPE_UNSPECIFIED
}

func (x *LiabilityValue) GetValue() *money.Money {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *LiabilityValue) GetComputationStatus() ComputationStatus {
	if x != nil {
		return x.ComputationStatus
	}
	return ComputationStatus_COMPUTATION_STATUS_UNSPECIFIED
}

func (x *LiabilityValue) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

type UpdateBulkManualAssetsCurrentValueRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Map of 'external_id -> current_value'
	// external_id would be unique id corresponding to manual asset
	// current_value would be of type money whatever user inputs
	UpdatedAssetCurrentValues map[string]*money.Money `protobuf:"bytes,2,rep,name=updated_asset_current_values,json=updatedAssetCurrentValues,proto3" json:"updated_asset_current_values,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdateBulkManualAssetsCurrentValueRequest) Reset() {
	*x = UpdateBulkManualAssetsCurrentValueRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBulkManualAssetsCurrentValueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBulkManualAssetsCurrentValueRequest) ProtoMessage() {}

func (x *UpdateBulkManualAssetsCurrentValueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBulkManualAssetsCurrentValueRequest.ProtoReflect.Descriptor instead.
func (*UpdateBulkManualAssetsCurrentValueRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{27}
}

func (x *UpdateBulkManualAssetsCurrentValueRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UpdateBulkManualAssetsCurrentValueRequest) GetUpdatedAssetCurrentValues() map[string]*money.Money {
	if x != nil {
		return x.UpdatedAssetCurrentValues
	}
	return nil
}

type UpdateBulkManualAssetsCurrentValueResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of updated external id
	UpdatedExternalId []string `protobuf:"bytes,2,rep,name=updated_external_id,json=updatedExternalId,proto3" json:"updated_external_id,omitempty"`
	// list of failed external id
	FailedExternalId []string `protobuf:"bytes,3,rep,name=failed_external_id,json=failedExternalId,proto3" json:"failed_external_id,omitempty"`
}

func (x *UpdateBulkManualAssetsCurrentValueResponse) Reset() {
	*x = UpdateBulkManualAssetsCurrentValueResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBulkManualAssetsCurrentValueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBulkManualAssetsCurrentValueResponse) ProtoMessage() {}

func (x *UpdateBulkManualAssetsCurrentValueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBulkManualAssetsCurrentValueResponse.ProtoReflect.Descriptor instead.
func (*UpdateBulkManualAssetsCurrentValueResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{28}
}

func (x *UpdateBulkManualAssetsCurrentValueResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateBulkManualAssetsCurrentValueResponse) GetUpdatedExternalId() []string {
	if x != nil {
		return x.UpdatedExternalId
	}
	return nil
}

func (x *UpdateBulkManualAssetsCurrentValueResponse) GetFailedExternalId() []string {
	if x != nil {
		return x.FailedExternalId
	}
	return nil
}

type CreateNetWorthRefreshSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// ordered list of assets to be refreshed with their refresh info
	AssetRefreshDetails []*model.AssetRefreshDetail `protobuf:"bytes,2,rep,name=asset_refresh_details,json=assetRefreshDetails,proto3" json:"asset_refresh_details,omitempty"`
}

func (x *CreateNetWorthRefreshSessionRequest) Reset() {
	*x = CreateNetWorthRefreshSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateNetWorthRefreshSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNetWorthRefreshSessionRequest) ProtoMessage() {}

func (x *CreateNetWorthRefreshSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNetWorthRefreshSessionRequest.ProtoReflect.Descriptor instead.
func (*CreateNetWorthRefreshSessionRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{29}
}

func (x *CreateNetWorthRefreshSessionRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CreateNetWorthRefreshSessionRequest) GetAssetRefreshDetails() []*model.AssetRefreshDetail {
	if x != nil {
		return x.AssetRefreshDetails
	}
	return nil
}

type CreateNetWorthRefreshSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                 *rpc.Status                   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NetWorthRefreshSession *model.NetWorthRefreshSession `protobuf:"bytes,2,opt,name=net_worth_refresh_session,json=netWorthRefreshSession,proto3" json:"net_worth_refresh_session,omitempty"`
}

func (x *CreateNetWorthRefreshSessionResponse) Reset() {
	*x = CreateNetWorthRefreshSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateNetWorthRefreshSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNetWorthRefreshSessionResponse) ProtoMessage() {}

func (x *CreateNetWorthRefreshSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNetWorthRefreshSessionResponse.ProtoReflect.Descriptor instead.
func (*CreateNetWorthRefreshSessionResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{30}
}

func (x *CreateNetWorthRefreshSessionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateNetWorthRefreshSessionResponse) GetNetWorthRefreshSession() *model.NetWorthRefreshSession {
	if x != nil {
		return x.NetWorthRefreshSession
	}
	return nil
}

type UpdateNetWorthRefreshSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UpdatedNetWorthRefreshSession *model.NetWorthRefreshSession `protobuf:"bytes,1,opt,name=updated_net_worth_refresh_session,json=updatedNetWorthRefreshSession,proto3" json:"updated_net_worth_refresh_session,omitempty"`
	// fields to update
	FieldMasks []model.NetWorthRefreshSessionFieldMask `protobuf:"varint,2,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=insights.networth.model.NetWorthRefreshSessionFieldMask" json:"field_masks,omitempty"`
}

func (x *UpdateNetWorthRefreshSessionRequest) Reset() {
	*x = UpdateNetWorthRefreshSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateNetWorthRefreshSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNetWorthRefreshSessionRequest) ProtoMessage() {}

func (x *UpdateNetWorthRefreshSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNetWorthRefreshSessionRequest.ProtoReflect.Descriptor instead.
func (*UpdateNetWorthRefreshSessionRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{31}
}

func (x *UpdateNetWorthRefreshSessionRequest) GetUpdatedNetWorthRefreshSession() *model.NetWorthRefreshSession {
	if x != nil {
		return x.UpdatedNetWorthRefreshSession
	}
	return nil
}

func (x *UpdateNetWorthRefreshSessionRequest) GetFieldMasks() []model.NetWorthRefreshSessionFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

type UpdateNetWorthRefreshSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                 *rpc.Status                   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NetWorthRefreshSession *model.NetWorthRefreshSession `protobuf:"bytes,2,opt,name=net_worth_refresh_session,json=netWorthRefreshSession,proto3" json:"net_worth_refresh_session,omitempty"`
}

func (x *UpdateNetWorthRefreshSessionResponse) Reset() {
	*x = UpdateNetWorthRefreshSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateNetWorthRefreshSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNetWorthRefreshSessionResponse) ProtoMessage() {}

func (x *UpdateNetWorthRefreshSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNetWorthRefreshSessionResponse.ProtoReflect.Descriptor instead.
func (*UpdateNetWorthRefreshSessionResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{32}
}

func (x *UpdateNetWorthRefreshSessionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateNetWorthRefreshSessionResponse) GetNetWorthRefreshSession() *model.NetWorthRefreshSession {
	if x != nil {
		return x.NetWorthRefreshSession
	}
	return nil
}

type GetNetWorthRefreshSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NetWorthRefreshId string `protobuf:"bytes,1,opt,name=net_worth_refresh_id,json=netWorthRefreshId,proto3" json:"net_worth_refresh_id,omitempty"`
}

func (x *GetNetWorthRefreshSessionRequest) Reset() {
	*x = GetNetWorthRefreshSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNetWorthRefreshSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNetWorthRefreshSessionRequest) ProtoMessage() {}

func (x *GetNetWorthRefreshSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNetWorthRefreshSessionRequest.ProtoReflect.Descriptor instead.
func (*GetNetWorthRefreshSessionRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{33}
}

func (x *GetNetWorthRefreshSessionRequest) GetNetWorthRefreshId() string {
	if x != nil {
		return x.NetWorthRefreshId
	}
	return ""
}

type GetNetWorthRefreshSessionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                 *rpc.Status                   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NetWorthRefreshSession *model.NetWorthRefreshSession `protobuf:"bytes,2,opt,name=net_worth_refresh_session,json=netWorthRefreshSession,proto3" json:"net_worth_refresh_session,omitempty"`
}

func (x *GetNetWorthRefreshSessionResponse) Reset() {
	*x = GetNetWorthRefreshSessionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNetWorthRefreshSessionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNetWorthRefreshSessionResponse) ProtoMessage() {}

func (x *GetNetWorthRefreshSessionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNetWorthRefreshSessionResponse.ProtoReflect.Descriptor instead.
func (*GetNetWorthRefreshSessionResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetNetWorthRefreshSessionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetNetWorthRefreshSessionResponse) GetNetWorthRefreshSession() *model.NetWorthRefreshSession {
	if x != nil {
		return x.NetWorthRefreshSession
	}
	return nil
}

type GetNetWorthInstrumentsRefreshDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// optional: only provide in case of refresh details required for particular asset
	NetworthRefreshAssets []model.NetWorthRefreshAsset `protobuf:"varint,2,rep,packed,name=networth_refresh_assets,json=networthRefreshAssets,proto3,enum=insights.networth.model.NetWorthRefreshAsset" json:"networth_refresh_assets,omitempty"`
}

func (x *GetNetWorthInstrumentsRefreshDetailsRequest) Reset() {
	*x = GetNetWorthInstrumentsRefreshDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNetWorthInstrumentsRefreshDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNetWorthInstrumentsRefreshDetailsRequest) ProtoMessage() {}

func (x *GetNetWorthInstrumentsRefreshDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNetWorthInstrumentsRefreshDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetNetWorthInstrumentsRefreshDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{35}
}

func (x *GetNetWorthInstrumentsRefreshDetailsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetNetWorthInstrumentsRefreshDetailsRequest) GetNetworthRefreshAssets() []model.NetWorthRefreshAsset {
	if x != nil {
		return x.NetworthRefreshAssets
	}
	return nil
}

type GetNetWorthInstrumentsRefreshDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of InstrumentRefreshDetails
	// contains refresh details for each supported instrument for Net Worth Refresh.
	InstrumentRefreshSummary []*InstrumentRefreshDetails `protobuf:"bytes,2,rep,name=instrument_refresh_summary,json=instrumentRefreshSummary,proto3" json:"instrument_refresh_summary,omitempty"`
}

func (x *GetNetWorthInstrumentsRefreshDetailsResponse) Reset() {
	*x = GetNetWorthInstrumentsRefreshDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNetWorthInstrumentsRefreshDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNetWorthInstrumentsRefreshDetailsResponse) ProtoMessage() {}

func (x *GetNetWorthInstrumentsRefreshDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNetWorthInstrumentsRefreshDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetNetWorthInstrumentsRefreshDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetNetWorthInstrumentsRefreshDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetNetWorthInstrumentsRefreshDetailsResponse) GetInstrumentRefreshSummary() []*InstrumentRefreshDetails {
	if x != nil {
		return x.InstrumentRefreshSummary
	}
	return nil
}

type InstrumentRefreshDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// asset name corresponding to particular net worth refresh asset
	AssetName model.NetWorthRefreshAsset `protobuf:"varint,1,opt,name=asset_name,json=assetName,proto3,enum=insights.networth.model.NetWorthRefreshAsset" json:"asset_name,omitempty"`
	// last refreshed time for the instrument
	// not applicable for manual assets
	LastRefreshedTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=last_refreshed_time,json=lastRefreshedTime,proto3" json:"last_refreshed_time,omitempty"`
	// last refreshed duration for the instrument
	// not applicable for manual assets
	LastRefreshedDuration *durationpb.Duration `protobuf:"bytes,3,opt,name=last_refreshed_duration,json=lastRefreshedDuration,proto3" json:"last_refreshed_duration,omitempty"`
	// instrument refresh is required or not
	//
	// Deprecated: Marked as deprecated in api/insights/networth/service.proto.
	IsRefreshRequired bool `protobuf:"varint,4,opt,name=is_refresh_required,json=isRefreshRequired,proto3" json:"is_refresh_required,omitempty"`
	// return true if there is an error while getting refresh details
	// not applicable for manual assets
	HasError bool `protobuf:"varint,5,opt,name=has_error,json=hasError,proto3" json:"has_error,omitempty"`
	// applicable only for epf
	// list of uan ids to refresh
	Uan []string `protobuf:"bytes,6,rep,name=uan,proto3" json:"uan,omitempty"`
	// applicable only for manual assets
	// InvestmentInstrumentType -> ManualAssetRefreshDetails map
	// only manual assets which needs refresh would be present
	ManualAssetRefreshDetails map[string]*ManualAssetRefreshDetails `protobuf:"bytes,7,rep,name=manual_asset_refresh_details,json=manualAssetRefreshDetails,proto3" json:"manual_asset_refresh_details,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// instrument refresh status e.g. required, in process, not required
	RefreshStatus RefreshStatus `protobuf:"varint,8,opt,name=refresh_status,json=refreshStatus,proto3,enum=insights.networth.RefreshStatus" json:"refresh_status,omitempty"`
}

func (x *InstrumentRefreshDetails) Reset() {
	*x = InstrumentRefreshDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstrumentRefreshDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstrumentRefreshDetails) ProtoMessage() {}

func (x *InstrumentRefreshDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstrumentRefreshDetails.ProtoReflect.Descriptor instead.
func (*InstrumentRefreshDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{37}
}

func (x *InstrumentRefreshDetails) GetAssetName() model.NetWorthRefreshAsset {
	if x != nil {
		return x.AssetName
	}
	return model.NetWorthRefreshAsset(0)
}

func (x *InstrumentRefreshDetails) GetLastRefreshedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastRefreshedTime
	}
	return nil
}

func (x *InstrumentRefreshDetails) GetLastRefreshedDuration() *durationpb.Duration {
	if x != nil {
		return x.LastRefreshedDuration
	}
	return nil
}

// Deprecated: Marked as deprecated in api/insights/networth/service.proto.
func (x *InstrumentRefreshDetails) GetIsRefreshRequired() bool {
	if x != nil {
		return x.IsRefreshRequired
	}
	return false
}

func (x *InstrumentRefreshDetails) GetHasError() bool {
	if x != nil {
		return x.HasError
	}
	return false
}

func (x *InstrumentRefreshDetails) GetUan() []string {
	if x != nil {
		return x.Uan
	}
	return nil
}

func (x *InstrumentRefreshDetails) GetManualAssetRefreshDetails() map[string]*ManualAssetRefreshDetails {
	if x != nil {
		return x.ManualAssetRefreshDetails
	}
	return nil
}

func (x *InstrumentRefreshDetails) GetRefreshStatus() RefreshStatus {
	if x != nil {
		return x.RefreshStatus
	}
	return RefreshStatus_REFRESH_STATUS_UNSPECIFIED
}

type ManualAssetRefreshDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// latest refreshed time among all the declarations for the manual asset
	LastRefreshedTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=last_refreshed_time,json=lastRefreshedTime,proto3" json:"last_refreshed_time,omitempty"`
	// latest refreshed duration among all the declarations for the manual asset
	LastRefreshedDuration *durationpb.Duration `protobuf:"bytes,2,opt,name=last_refreshed_duration,json=lastRefreshedDuration,proto3" json:"last_refreshed_duration,omitempty"`
	// instrument refresh is required or not
	//
	// Deprecated: Marked as deprecated in api/insights/networth/service.proto.
	IsRefreshRequired bool `protobuf:"varint,3,opt,name=is_refresh_required,json=isRefreshRequired,proto3" json:"is_refresh_required,omitempty"`
	// list of investment declared by the user for the manual asset which needs to be refreshed
	InvestmentDeclarations []*model.InvestmentDeclaration `protobuf:"bytes,4,rep,name=investment_declarations,json=investmentDeclarations,proto3" json:"investment_declarations,omitempty"`
	// return true if there is an error while getting refresh details
	HasError bool `protobuf:"varint,5,opt,name=has_error,json=hasError,proto3" json:"has_error,omitempty"`
	// manual asset refresh status e.g. required, not required
	RefreshStatus RefreshStatus `protobuf:"varint,8,opt,name=refresh_status,json=refreshStatus,proto3,enum=insights.networth.RefreshStatus" json:"refresh_status,omitempty"`
}

func (x *ManualAssetRefreshDetails) Reset() {
	*x = ManualAssetRefreshDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualAssetRefreshDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualAssetRefreshDetails) ProtoMessage() {}

func (x *ManualAssetRefreshDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualAssetRefreshDetails.ProtoReflect.Descriptor instead.
func (*ManualAssetRefreshDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{38}
}

func (x *ManualAssetRefreshDetails) GetLastRefreshedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastRefreshedTime
	}
	return nil
}

func (x *ManualAssetRefreshDetails) GetLastRefreshedDuration() *durationpb.Duration {
	if x != nil {
		return x.LastRefreshedDuration
	}
	return nil
}

// Deprecated: Marked as deprecated in api/insights/networth/service.proto.
func (x *ManualAssetRefreshDetails) GetIsRefreshRequired() bool {
	if x != nil {
		return x.IsRefreshRequired
	}
	return false
}

func (x *ManualAssetRefreshDetails) GetInvestmentDeclarations() []*model.InvestmentDeclaration {
	if x != nil {
		return x.InvestmentDeclarations
	}
	return nil
}

func (x *ManualAssetRefreshDetails) GetHasError() bool {
	if x != nil {
		return x.HasError
	}
	return false
}

func (x *ManualAssetRefreshDetails) GetRefreshStatus() RefreshStatus {
	if x != nil {
		return x.RefreshStatus
	}
	return RefreshStatus_REFRESH_STATUS_UNSPECIFIED
}

type SearchAssetFormFieldOptionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SearchIdentifier frontend.AssetFormFieldSearchIdentifier `protobuf:"varint,1,opt,name=search_identifier,json=searchIdentifier,proto3,enum=insights.networth.frontend.AssetFormFieldSearchIdentifier" json:"search_identifier,omitempty"`
	SearchText       string                                  `protobuf:"bytes,2,opt,name=search_text,json=searchText,proto3" json:"search_text,omitempty"`
}

func (x *SearchAssetFormFieldOptionsRequest) Reset() {
	*x = SearchAssetFormFieldOptionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchAssetFormFieldOptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAssetFormFieldOptionsRequest) ProtoMessage() {}

func (x *SearchAssetFormFieldOptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAssetFormFieldOptionsRequest.ProtoReflect.Descriptor instead.
func (*SearchAssetFormFieldOptionsRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{39}
}

func (x *SearchAssetFormFieldOptionsRequest) GetSearchIdentifier() frontend.AssetFormFieldSearchIdentifier {
	if x != nil {
		return x.SearchIdentifier
	}
	return frontend.AssetFormFieldSearchIdentifier(0)
}

func (x *SearchAssetFormFieldOptionsRequest) GetSearchText() string {
	if x != nil {
		return x.SearchText
	}
	return ""
}

type SearchAssetFormFieldOptionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Types that are assignable to SearchResults:
	//
	//	*SearchAssetFormFieldOptionsResponse_PmsProviderList
	//	*SearchAssetFormFieldOptionsResponse_AifList
	//	*SearchAssetFormFieldOptionsResponse_CompanyList
	SearchResults isSearchAssetFormFieldOptionsResponse_SearchResults `protobuf_oneof:"search_results"`
}

func (x *SearchAssetFormFieldOptionsResponse) Reset() {
	*x = SearchAssetFormFieldOptionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchAssetFormFieldOptionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAssetFormFieldOptionsResponse) ProtoMessage() {}

func (x *SearchAssetFormFieldOptionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAssetFormFieldOptionsResponse.ProtoReflect.Descriptor instead.
func (*SearchAssetFormFieldOptionsResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{40}
}

func (x *SearchAssetFormFieldOptionsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (m *SearchAssetFormFieldOptionsResponse) GetSearchResults() isSearchAssetFormFieldOptionsResponse_SearchResults {
	if m != nil {
		return m.SearchResults
	}
	return nil
}

func (x *SearchAssetFormFieldOptionsResponse) GetPmsProviderList() *PmsProviderList {
	if x, ok := x.GetSearchResults().(*SearchAssetFormFieldOptionsResponse_PmsProviderList); ok {
		return x.PmsProviderList
	}
	return nil
}

func (x *SearchAssetFormFieldOptionsResponse) GetAifList() *AifList {
	if x, ok := x.GetSearchResults().(*SearchAssetFormFieldOptionsResponse_AifList); ok {
		return x.AifList
	}
	return nil
}

func (x *SearchAssetFormFieldOptionsResponse) GetCompanyList() *CompanyList {
	if x, ok := x.GetSearchResults().(*SearchAssetFormFieldOptionsResponse_CompanyList); ok {
		return x.CompanyList
	}
	return nil
}

type isSearchAssetFormFieldOptionsResponse_SearchResults interface {
	isSearchAssetFormFieldOptionsResponse_SearchResults()
}

type SearchAssetFormFieldOptionsResponse_PmsProviderList struct {
	PmsProviderList *PmsProviderList `protobuf:"bytes,2,opt,name=pms_provider_list,json=pmsProviderList,proto3,oneof"`
}

type SearchAssetFormFieldOptionsResponse_AifList struct {
	AifList *AifList `protobuf:"bytes,3,opt,name=aif_list,json=aifList,proto3,oneof"`
}

type SearchAssetFormFieldOptionsResponse_CompanyList struct {
	CompanyList *CompanyList `protobuf:"bytes,4,opt,name=company_list,json=companyList,proto3,oneof"`
}

func (*SearchAssetFormFieldOptionsResponse_PmsProviderList) isSearchAssetFormFieldOptionsResponse_SearchResults() {
}

func (*SearchAssetFormFieldOptionsResponse_AifList) isSearchAssetFormFieldOptionsResponse_SearchResults() {
}

func (*SearchAssetFormFieldOptionsResponse_CompanyList) isSearchAssetFormFieldOptionsResponse_SearchResults() {
}

// For use inside one-ofs only
type PmsProviderList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PmsProviders []*model.PmsProvider `protobuf:"bytes,1,rep,name=pms_providers,json=pmsProviders,proto3" json:"pms_providers,omitempty"`
}

func (x *PmsProviderList) Reset() {
	*x = PmsProviderList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PmsProviderList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PmsProviderList) ProtoMessage() {}

func (x *PmsProviderList) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PmsProviderList.ProtoReflect.Descriptor instead.
func (*PmsProviderList) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{41}
}

func (x *PmsProviderList) GetPmsProviders() []*model.PmsProvider {
	if x != nil {
		return x.PmsProviders
	}
	return nil
}

// For use inside one-ofs only
type AifList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AlternativeInvestmentFunds []*model.AlternativeInvestmentFund `protobuf:"bytes,1,rep,name=alternative_investment_funds,json=alternativeInvestmentFunds,proto3" json:"alternative_investment_funds,omitempty"`
}

func (x *AifList) Reset() {
	*x = AifList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AifList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AifList) ProtoMessage() {}

func (x *AifList) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AifList.ProtoReflect.Descriptor instead.
func (*AifList) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{42}
}

func (x *AifList) GetAlternativeInvestmentFunds() []*model.AlternativeInvestmentFund {
	if x != nil {
		return x.AlternativeInvestmentFunds
	}
	return nil
}

type CompanyList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmployerInfo []*employment.EmployerInfo `protobuf:"bytes,1,rep,name=employer_info,json=employerInfo,proto3" json:"employer_info,omitempty"`
}

func (x *CompanyList) Reset() {
	*x = CompanyList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyList) ProtoMessage() {}

func (x *CompanyList) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyList.ProtoReflect.Descriptor instead.
func (*CompanyList) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{43}
}

func (x *CompanyList) GetEmployerInfo() []*employment.EmployerInfo {
	if x != nil {
		return x.EmployerInfo
	}
	return nil
}

type GetPortfolioChangeSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId     string                 `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	HistoryDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=history_date,json=historyDate,proto3" json:"history_date,omitempty"`
}

func (x *GetPortfolioChangeSummaryRequest) Reset() {
	*x = GetPortfolioChangeSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPortfolioChangeSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPortfolioChangeSummaryRequest) ProtoMessage() {}

func (x *GetPortfolioChangeSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPortfolioChangeSummaryRequest.ProtoReflect.Descriptor instead.
func (*GetPortfolioChangeSummaryRequest) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{44}
}

func (x *GetPortfolioChangeSummaryRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetPortfolioChangeSummaryRequest) GetHistoryDate() *timestamppb.Timestamp {
	if x != nil {
		return x.HistoryDate
	}
	return nil
}

type GetPortfolioChangeSummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                   *rpc.Status  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	AggregatedNetworthAtDate *money.Money `protobuf:"bytes,2,opt,name=aggregated_networth_at_date,json=aggregatedNetworthAtDate,proto3" json:"aggregated_networth_at_date,omitempty"`
	CurrentNetworthValue     *money.Money `protobuf:"bytes,3,opt,name=current_networth_value,json=currentNetworthValue,proto3" json:"current_networth_value,omitempty"`
	AmountChange             *money.Money `protobuf:"bytes,4,opt,name=amount_change,json=amountChange,proto3" json:"amount_change,omitempty"`
	PercentageChange         float32      `protobuf:"fixed32,5,opt,name=percentage_change,json=percentageChange,proto3" json:"percentage_change,omitempty"`
}

func (x *GetPortfolioChangeSummaryResponse) Reset() {
	*x = GetPortfolioChangeSummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPortfolioChangeSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPortfolioChangeSummaryResponse) ProtoMessage() {}

func (x *GetPortfolioChangeSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPortfolioChangeSummaryResponse.ProtoReflect.Descriptor instead.
func (*GetPortfolioChangeSummaryResponse) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_service_proto_rawDescGZIP(), []int{45}
}

func (x *GetPortfolioChangeSummaryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPortfolioChangeSummaryResponse) GetAggregatedNetworthAtDate() *money.Money {
	if x != nil {
		return x.AggregatedNetworthAtDate
	}
	return nil
}

func (x *GetPortfolioChangeSummaryResponse) GetCurrentNetworthValue() *money.Money {
	if x != nil {
		return x.CurrentNetworthValue
	}
	return nil
}

func (x *GetPortfolioChangeSummaryResponse) GetAmountChange() *money.Money {
	if x != nil {
		return x.AmountChange
	}
	return nil
}

func (x *GetPortfolioChangeSummaryResponse) GetPercentageChange() float32 {
	if x != nil {
		return x.PercentageChange
	}
	return 0
}

var File_api_insights_networth_service_proto protoreflect.FileDescriptor

var file_api_insights_networth_service_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3c,
	0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x2f, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70,
	0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37,
	0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x72, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x61, 0x67,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63,
	0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x2f,
	0x66, 0x69, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x69, 0x0a, 0x17, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x05,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65,
	0x73, 0x22, 0xe2, 0x01, 0x0a, 0x18, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x63, 0x0a, 0x14, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e,
	0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x12, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xfe, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x66, 0x69, 0x6e, 0x61, 0x6c,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x43, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0xea, 0x02, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x73, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0xa2, 0x01, 0x0a, 0x25,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x64, 0x61,
	0x79, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x1f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x44, 0x61, 0x79, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x61, 0x70,
	0x1a, 0x81, 0x01, 0x0a, 0x24, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x6f,
	0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x43, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xaa, 0x02, 0x0a, 0x1a, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x18, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x15, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x61, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x47, 0x0a, 0x16, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x13, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x53, 0x0a, 0x13,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x11,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x22, 0xc5, 0x01, 0x0a, 0x10, 0x41, 0x73, 0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x40, 0x0a, 0x12, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x10, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x3c, 0x0a, 0x10, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0e, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x06, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x4b, 0x0a, 0x25, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x6d, 0x0a, 0x26, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x41, 0x6c, 0x6c, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63,
	0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xd4, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x50, 0x0a, 0x10, 0x69, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73,
	0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x69, 0x6e, 0x73,
	0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0xe5, 0x02, 0x0a,
	0x21, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x69, 0x0a, 0x16, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x02, 0x18, 0x01, 0x52, 0x15, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x53, 0x0a, 0x12, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e,
	0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x11, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x0d, 0x22, 0x3a, 0x0a, 0x14, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x53, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x22, 0x3c, 0x0a, 0x15, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xf0,
	0x03, 0x0a, 0x18, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x4e, 0x0a, 0x0f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0e, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x3b, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0b,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3f, 0x0a, 0x0d, 0x6d, 0x61, 0x74,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6d, 0x61,
	0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x12,
	0x61, 0x0a, 0x13, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x44, 0x65, 0x63, 0x6c,
	0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x12,
	0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0xc7, 0x01, 0x0a, 0x19, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x49, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x65, 0x0a, 0x16, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x15, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x1e, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xdf, 0x01, 0x0a, 0x22,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x5f, 0x0a, 0x13, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x65,
	0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x12, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x58, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73,
	0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63,
	0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x73, 0x22, 0xd1, 0x01,
	0x0a, 0x23, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x65, 0x0a, 0x16, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x15, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x0d, 0x22, 0x83, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a,
	0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x22, 0xa7, 0x02, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x49,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x69, 0x0a, 0x16, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x02, 0x18, 0x01, 0x52, 0x15, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x53, 0x0a, 0x12,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x49, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x0d, 0x22, 0x71, 0x0a, 0x22, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x49, 0x64, 0x22, 0x6a, 0x0a, 0x23, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d,
	0x22, 0xa1, 0x02, 0x0a, 0x11, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x65, 0x0a, 0x16, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x15, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a,
	0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x6c, 0x0a, 0x1b, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74,
	0x65, 0x64, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e,
	0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x19, 0x63, 0x6f, 0x6d, 0x70, 0x75,
	0x74, 0x65, 0x64, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x22, 0x74, 0x0a, 0x19, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x64,
	0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x43, 0x0a, 0x0c, 0x65, 0x73, 0x6f, 0x70, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x45, 0x73, 0x6f, 0x70,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0b, 0x65, 0x73, 0x6f, 0x70, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x12, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74,
	0x65, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x3b, 0x0a, 0x0b, 0x45, 0x73,
	0x6f, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x73, 0x6f, 0x70, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x56, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x45, 0x73, 0x6f, 0x70, 0x73, 0x22, 0xc7, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x49, 0x0a, 0x0f, 0x6c, 0x69, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x20, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x4c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0e, 0x6c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x22, 0xb6, 0x02, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74,
	0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x4c, 0x0a, 0x10, 0x6c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x4c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x0f, 0x6c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x12, 0x45, 0x0a, 0x15, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x74,
	0x5f, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4e, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x74, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xc5, 0x02, 0x0a, 0x0a, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x50, 0x0a, 0x13, 0x6e, 0x65, 0x74,
	0x5f, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x6e, 0x65, 0x74, 0x57, 0x6f, 0x72,
	0x74, 0x68, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x41, 0x0a, 0x0a, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x22, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x53, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x70,
	0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x63, 0x6f, 0x6d, 0x70,
	0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x22, 0x86, 0x02, 0x0a, 0x0e, 0x4c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x50, 0x0a, 0x13, 0x6e, 0x65, 0x74, 0x5f, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x4c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x6e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x53, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x11, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xd0, 0x02, 0x0a, 0x29,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x75, 0x6c, 0x6b, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x9c, 0x01,
	0x0a, 0x1c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x5b, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42,
	0x75, 0x6c, 0x6b, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x19, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x1a, 0x60, 0x0a, 0x1e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x28, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xfa,
	0x01, 0x0a, 0x2a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x75, 0x6c, 0x6b, 0x4d, 0x61, 0x6e,
	0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x11, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64,
	0x22, 0x49, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52,
	0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41,
	0x4c, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x10, 0x65, 0x22, 0xaa, 0x01, 0x0a, 0x23,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x5f, 0x0a, 0x15, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x13, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73,
	0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xd7, 0x01, 0x0a, 0x24, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x6a, 0x0a, 0x19, 0x6e, 0x65, 0x74, 0x5f, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x16, 0x6e, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c,
	0x10, 0x0d, 0x22, 0xfb, 0x01, 0x0a, 0x23, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x79, 0x0a, 0x21, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f,
	0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x1d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x4e,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x59, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d,
	0x61, 0x73, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x38, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x73,
	0x22, 0xe6, 0x01, 0x0a, 0x24, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x57, 0x6f,
	0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x6a,
	0x0a, 0x19, 0x6e, 0x65, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x16, 0x6e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09,
	0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x53, 0x0a, 0x20, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a,
	0x14, 0x6e, 0x65, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6e, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x49, 0x64, 0x22, 0xe3,
	0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x6a, 0x0a, 0x19, 0x6e, 0x65, 0x74,
	0x5f, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52,
	0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x16, 0x6e,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x0d, 0x22, 0xb8, 0x01, 0x0a, 0x2b, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x74, 0x68, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52,
	0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x65, 0x0a, 0x17, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x15, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x22,
	0xeb, 0x01, 0x0a, 0x2c, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x49,
	0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73,
	0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x69, 0x0a, 0x1a, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x73, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x49, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x18, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x22, 0x2b, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d,
	0x12, 0x0b, 0x0a, 0x07, 0x50, 0x41, 0x52, 0x54, 0x49, 0x41, 0x4c, 0x10, 0x65, 0x22, 0xbd, 0x05,
	0x0a, 0x18, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4c, 0x0a, 0x0a, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74,
	0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x09, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4a, 0x0a, 0x13, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x11, 0x6c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x51, 0x0a, 0x17, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x15, 0x6c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x65, 0x64, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x72, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x69, 0x73, 0x52, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x68,
	0x61, 0x73, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x68, 0x61, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x61, 0x6e, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x75, 0x61, 0x6e, 0x12, 0x8b, 0x01, 0x0a, 0x1c, 0x6d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x4a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x4d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73,
	0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x19, 0x6d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73,
	0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x47, 0x0a, 0x0e, 0x72, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x20, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x1a, 0x7a, 0x0a, 0x1e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x42, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbd, 0x03,
	0x0a, 0x19, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4a, 0x0a, 0x13, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x11, 0x6c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73,
	0x68, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x51, 0x0a, 0x17, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x15, 0x6c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x65, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x13, 0x69, 0x73,
	0x5f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x69, 0x73, 0x52,
	0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x67,
	0x0a, 0x17, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x63,
	0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x16, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x73, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x68, 0x61, 0x73, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x47, 0x0a, 0x0e, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d,
	0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xae, 0x01,
	0x0a, 0x22, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x6f, 0x72,
	0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x67, 0x0a, 0x11, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x3a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x10, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x78, 0x74, 0x22, 0xac,
	0x02, 0x0a, 0x23, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x6f,
	0x72, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x50, 0x0a, 0x11, 0x70,
	0x6d, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x50, 0x6d, 0x73, 0x50, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0f, 0x70, 0x6d,
	0x73, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x37, 0x0a,
	0x08, 0x61, 0x69, 0x66, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x41, 0x69, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x07, 0x61,
	0x69, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0b,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x10, 0x0a, 0x0e, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x22, 0x5c, 0x0a,
	0x0f, 0x50, 0x6d, 0x73, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x49, 0x0a, 0x0d, 0x70, 0x6d, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x50, 0x6d, 0x73, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x52, 0x0c, 0x70,
	0x6d, 0x73, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x22, 0x7f, 0x0a, 0x07, 0x41,
	0x69, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x74, 0x0a, 0x1c, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x75, 0x6e, 0x64,
	0x52, 0x1a, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x22, 0x4c, 0x0a, 0x0b,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3d, 0x0a, 0x0d, 0x65,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x65, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x85, 0x01, 0x0a, 0x20, 0x47,
	0x65, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x61,
	0x74, 0x65, 0x22, 0xcb, 0x02, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x51, 0x0a,
	0x1b, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x5f, 0x61, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x18, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x64, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x41, 0x74, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x48, 0x0a, 0x16, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x37, 0x0a, 0x0d, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x2a, 0x84, 0x06, 0x0a, 0x09, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a,
	0x0a, 0x16, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x41,
	0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f,
	0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x53, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x50, 0x46, 0x10, 0x03, 0x12, 0x1a,
	0x0a, 0x16, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x55, 0x54,
	0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x04, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x44, 0x49, 0x41, 0x4e, 0x5f,
	0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18,
	0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x5f, 0x53, 0x45,
	0x43, 0x55, 0x52, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x06, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x32, 0x50, 0x5f, 0x4c, 0x45, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x07, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x49, 0x46, 0x10, 0x08, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45,
	0x5f, 0x45, 0x51, 0x55, 0x49, 0x54, 0x59, 0x10, 0x09, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x53, 0x53,
	0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x45, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x10, 0x0a, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x41, 0x52, 0x54, 0x5f, 0x41, 0x52, 0x54, 0x45, 0x46, 0x41, 0x43, 0x54,
	0x53, 0x10, 0x0b, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x42, 0x4f, 0x4e, 0x44, 0x53, 0x10, 0x0c, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x53, 0x53,
	0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x10, 0x0d, 0x12, 0x1b,
	0x0a, 0x17, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x49, 0x47,
	0x49, 0x54, 0x41, 0x4c, 0x5f, 0x47, 0x4f, 0x4c, 0x44, 0x10, 0x0e, 0x12, 0x1d, 0x0a, 0x19, 0x41,
	0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x49, 0x47, 0x49, 0x54, 0x41,
	0x4c, 0x5f, 0x53, 0x49, 0x4c, 0x56, 0x45, 0x52, 0x10, 0x0f, 0x12, 0x2b, 0x0a, 0x27, 0x41, 0x53,
	0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c,
	0x49, 0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45,
	0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x10, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x50, 0x52, 0x4f,
	0x56, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x11, 0x12, 0x24, 0x0a,
	0x20, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x4c,
	0x4f, 0x59, 0x45, 0x45, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x12, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4e, 0x50, 0x53, 0x10, 0x13, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x41, 0x44, 0x47, 0x45, 0x54, 0x53, 0x10, 0x14, 0x12,
	0x17, 0x0a, 0x13, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x45,
	0x48, 0x49, 0x43, 0x4c, 0x45, 0x53, 0x10, 0x15, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x53, 0x53, 0x45,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x59, 0x50, 0x54, 0x4f, 0x10, 0x16, 0x12,
	0x18, 0x0a, 0x14, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x55,
	0x52, 0x4e, 0x49, 0x54, 0x55, 0x52, 0x45, 0x10, 0x17, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x53, 0x53,
	0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49,
	0x42, 0x4c, 0x45, 0x53, 0x10, 0x18, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x45, 0x57, 0x45, 0x4c, 0x4c, 0x45, 0x52, 0x59, 0x10, 0x19,
	0x12, 0x15, 0x0a, 0x11, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f,
	0x54, 0x48, 0x45, 0x52, 0x53, 0x10, 0x1a, 0x2a, 0xfe, 0x01, 0x0a, 0x0d, 0x4c, 0x69, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x49, 0x41,
	0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x49, 0x41,
	0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x55, 0x54, 0x53, 0x54, 0x41, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49,
	0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x4c, 0x4f, 0x41,
	0x4e, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49,
	0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f,
	0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x04, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c,
	0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x44, 0x55, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x49, 0x41,
	0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45,
	0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x06, 0x2a, 0x98, 0x01, 0x0a, 0x11, 0x43, 0x6f, 0x6d,
	0x70, 0x75, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22,
	0x0a, 0x1e, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x03, 0x2a, 0x8c, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45,
	0x44, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53,
	0x10, 0x03, 0x32, 0x87, 0x14, 0x0a, 0x08, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x12,
	0x6d, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f,
	0x72, 0x74, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2b, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x70,
	0x0a, 0x11, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x2b, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x49,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2c, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x49, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x8e, 0x01, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x35, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c,
	0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x85, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x33, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x88, 0x01, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x33, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x49,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x84, 0x01, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e,
	0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xa3, 0x01, 0x0a, 0x22,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x75, 0x6c, 0x6b, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x3c, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x75, 0x6c,
	0x6b, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3d, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x75, 0x6c, 0x6b, 0x4d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x91, 0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x36, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x91, 0x01, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74,
	0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x88, 0x01, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72,
	0x65, 0x73, 0x68, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0xa7, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x74, 0x68, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52,
	0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3e, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x49, 0x6e, 0x73,
	0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x49, 0x6e, 0x73,
	0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c,
	0x01, 0x0a, 0x1b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x6f,
	0x72, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x35,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x6f,
	0x72, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a,
	0x0d, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x27,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x53, 0x74, 0x6f, 0x72,
	0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x77, 0x0a, 0x14, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2d, 0x2e, 0x64, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7d, 0x0a, 0x16, 0x44, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x12, 0x2f, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x88, 0x01, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x33, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x95, 0x01, 0x0a, 0x1e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41,
	0x6c, 0x6c, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c,
	0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x41, 0x6c, 0x6c, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x39, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x49,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x71, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x2c, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x44, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2d, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x44, 0x61,
	0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x6b, 0x0a, 0x10, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69,
	0x6c, 0x65, 0x73, 0x12, 0x2a, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2b, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x5c, 0x0a, 0x2c,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5a, 0x2c, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_insights_networth_service_proto_rawDescOnce sync.Once
	file_api_insights_networth_service_proto_rawDescData = file_api_insights_networth_service_proto_rawDesc
)

func file_api_insights_networth_service_proto_rawDescGZIP() []byte {
	file_api_insights_networth_service_proto_rawDescOnce.Do(func() {
		file_api_insights_networth_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_insights_networth_service_proto_rawDescData)
	})
	return file_api_insights_networth_service_proto_rawDescData
}

var file_api_insights_networth_service_proto_enumTypes = make([]protoimpl.EnumInfo, 16)
var file_api_insights_networth_service_proto_msgTypes = make([]protoimpl.MessageInfo, 49)
var file_api_insights_networth_service_proto_goTypes = []interface{}{
	(AssetType)(0),         // 0: insights.networth.AssetType
	(LiabilityType)(0),     // 1: insights.networth.LiabilityType
	(ComputationStatus)(0), // 2: insights.networth.ComputationStatus
	(RefreshStatus)(0),     // 3: insights.networth.RefreshStatus
	(DeleteAllInvestmentDeclarationResponse_Status)(0),       // 4: insights.networth.DeleteAllInvestmentDeclarationResponse.Status
	(GetInvestmentDeclarationsResponse_Status)(0),            // 5: insights.networth.GetInvestmentDeclarationsResponse.Status
	(DeclareInvestmentResponse_Status)(0),                    // 6: insights.networth.DeclareInvestmentResponse.Status
	(UpdateInvestmentDeclarationResponse_Status)(0),          // 7: insights.networth.UpdateInvestmentDeclarationResponse.Status
	(GetInvestmentDeclarationResponse_Status)(0),             // 8: insights.networth.GetInvestmentDeclarationResponse.Status
	(DeleteInvestmentDeclarationResponse_Status)(0),          // 9: insights.networth.DeleteInvestmentDeclarationResponse.Status
	(GetNetWorthValueResponse_Status)(0),                     // 10: insights.networth.GetNetWorthValueResponse.Status
	(UpdateBulkManualAssetsCurrentValueResponse_Status)(0),   // 11: insights.networth.UpdateBulkManualAssetsCurrentValueResponse.Status
	(CreateNetWorthRefreshSessionResponse_Status)(0),         // 12: insights.networth.CreateNetWorthRefreshSessionResponse.Status
	(UpdateNetWorthRefreshSessionResponse_Status)(0),         // 13: insights.networth.UpdateNetWorthRefreshSessionResponse.Status
	(GetNetWorthRefreshSessionResponse_Status)(0),            // 14: insights.networth.GetNetWorthRefreshSessionResponse.Status
	(GetNetWorthInstrumentsRefreshDetailsResponse_Status)(0), // 15: insights.networth.GetNetWorthInstrumentsRefreshDetailsResponse.Status
	(*MagicImportFilesRequest)(nil),                          // 16: insights.networth.MagicImportFilesRequest
	(*MagicImportFilesResponse)(nil),                         // 17: insights.networth.MagicImportFilesResponse
	(*GetAssetsDayChangeRequest)(nil),                        // 18: insights.networth.GetAssetsDayChangeRequest
	(*GetAssetsDayChangeResponse)(nil),                       // 19: insights.networth.GetAssetsDayChangeResponse
	(*AssetTypeDayChangeResponse)(nil),                       // 20: insights.networth.AssetTypeDayChangeResponse
	(*AssetValueChange)(nil),                                 // 21: insights.networth.AssetValueChange
	(*DeleteAllInvestmentDeclarationRequest)(nil),            // 22: insights.networth.DeleteAllInvestmentDeclarationRequest
	(*DeleteAllInvestmentDeclarationResponse)(nil),           // 23: insights.networth.DeleteAllInvestmentDeclarationResponse
	(*GetInvestmentDeclarationsRequest)(nil),                 // 24: insights.networth.GetInvestmentDeclarationsRequest
	(*GetInvestmentDeclarationsResponse)(nil),                // 25: insights.networth.GetInvestmentDeclarationsResponse
	(*StoreSnapshotRequest)(nil),                             // 26: insights.networth.StoreSnapshotRequest
	(*StoreSnapshotResponse)(nil),                            // 27: insights.networth.StoreSnapshotResponse
	(*DeclareInvestmentRequest)(nil),                         // 28: insights.networth.DeclareInvestmentRequest
	(*DeclareInvestmentResponse)(nil),                        // 29: insights.networth.DeclareInvestmentResponse
	(*UpdateInvestmentDeclarationRequest)(nil),               // 30: insights.networth.UpdateInvestmentDeclarationRequest
	(*UpdateInvestmentDeclarationResponse)(nil),              // 31: insights.networth.UpdateInvestmentDeclarationResponse
	(*GetInvestmentDeclarationRequest)(nil),                  // 32: insights.networth.GetInvestmentDeclarationRequest
	(*GetInvestmentDeclarationResponse)(nil),                 // 33: insights.networth.GetInvestmentDeclarationResponse
	(*DeleteInvestmentDeclarationRequest)(nil),               // 34: insights.networth.DeleteInvestmentDeclarationRequest
	(*DeleteInvestmentDeclarationResponse)(nil),              // 35: insights.networth.DeleteInvestmentDeclarationResponse
	(*InvestmentDetails)(nil),                                // 36: insights.networth.InvestmentDetails
	(*ComputedInvestmentDetails)(nil),                        // 37: insights.networth.ComputedInvestmentDetails
	(*EsopDetails)(nil),                                      // 38: insights.networth.EsopDetails
	(*GetNetWorthValueRequest)(nil),                          // 39: insights.networth.GetNetWorthValueRequest
	(*GetNetWorthValueResponse)(nil),                         // 40: insights.networth.GetNetWorthValueResponse
	(*AssetValue)(nil),                                       // 41: insights.networth.AssetValue
	(*LiabilityValue)(nil),                                   // 42: insights.networth.LiabilityValue
	(*UpdateBulkManualAssetsCurrentValueRequest)(nil),        // 43: insights.networth.UpdateBulkManualAssetsCurrentValueRequest
	(*UpdateBulkManualAssetsCurrentValueResponse)(nil),       // 44: insights.networth.UpdateBulkManualAssetsCurrentValueResponse
	(*CreateNetWorthRefreshSessionRequest)(nil),              // 45: insights.networth.CreateNetWorthRefreshSessionRequest
	(*CreateNetWorthRefreshSessionResponse)(nil),             // 46: insights.networth.CreateNetWorthRefreshSessionResponse
	(*UpdateNetWorthRefreshSessionRequest)(nil),              // 47: insights.networth.UpdateNetWorthRefreshSessionRequest
	(*UpdateNetWorthRefreshSessionResponse)(nil),             // 48: insights.networth.UpdateNetWorthRefreshSessionResponse
	(*GetNetWorthRefreshSessionRequest)(nil),                 // 49: insights.networth.GetNetWorthRefreshSessionRequest
	(*GetNetWorthRefreshSessionResponse)(nil),                // 50: insights.networth.GetNetWorthRefreshSessionResponse
	(*GetNetWorthInstrumentsRefreshDetailsRequest)(nil),      // 51: insights.networth.GetNetWorthInstrumentsRefreshDetailsRequest
	(*GetNetWorthInstrumentsRefreshDetailsResponse)(nil),     // 52: insights.networth.GetNetWorthInstrumentsRefreshDetailsResponse
	(*InstrumentRefreshDetails)(nil),                         // 53: insights.networth.InstrumentRefreshDetails
	(*ManualAssetRefreshDetails)(nil),                        // 54: insights.networth.ManualAssetRefreshDetails
	(*SearchAssetFormFieldOptionsRequest)(nil),               // 55: insights.networth.SearchAssetFormFieldOptionsRequest
	(*SearchAssetFormFieldOptionsResponse)(nil),              // 56: insights.networth.SearchAssetFormFieldOptionsResponse
	(*PmsProviderList)(nil),                                  // 57: insights.networth.PmsProviderList
	(*AifList)(nil),                                          // 58: insights.networth.AifList
	(*CompanyList)(nil),                                      // 59: insights.networth.CompanyList
	(*GetPortfolioChangeSummaryRequest)(nil),                 // 60: insights.networth.GetPortfolioChangeSummaryRequest
	(*GetPortfolioChangeSummaryResponse)(nil),                // 61: insights.networth.GetPortfolioChangeSummaryResponse
	nil,                                                     // 62: insights.networth.GetAssetsDayChangeResponse.AssetTypeToDayChangeResponseMapEntry
	nil,                                                     // 63: insights.networth.UpdateBulkManualAssetsCurrentValueRequest.UpdatedAssetCurrentValuesEntry
	nil,                                                     // 64: insights.networth.InstrumentRefreshDetails.ManualAssetRefreshDetailsEntry
	(*file.File)(nil),                                       // 65: api.typesv2.common.file.File
	(*rpc.Status)(nil),                                      // 66: rpc.Status
	(*deeplink.Deeplink)(nil),                               // 67: frontend.deeplink.Deeplink
	(*magicimport.MagicImportDetails)(nil),                  // 68: insights.networth.magicimport.MagicImportDetails
	(*timestamppb.Timestamp)(nil),                           // 69: google.protobuf.Timestamp
	(enums.AssetType)(0),                                    // 70: insights.networth.enums.AssetType
	(*money.Money)(nil),                                     // 71: google.type.Money
	(*rpc.PageContextRequest)(nil),                          // 72: rpc.PageContextRequest
	(typesv2.InvestmentInstrumentType)(0),                   // 73: api.typesv2.InvestmentInstrumentType
	(*rpc.PageContextResponse)(nil),                         // 74: rpc.PageContextResponse
	(*model.InvestmentDeclaration)(nil),                     // 75: insights.networth.model.InvestmentDeclaration
	(*model.OtherDeclarationDetails)(nil),                   // 76: insights.networth.model.OtherDeclarationDetails
	(model.InvestmentDeclarationFieldMask)(0),               // 77: insights.networth.model.InvestmentDeclarationFieldMask
	(*model.AssetRefreshDetail)(nil),                        // 78: insights.networth.model.AssetRefreshDetail
	(*model.NetWorthRefreshSession)(nil),                    // 79: insights.networth.model.NetWorthRefreshSession
	(model.NetWorthRefreshSessionFieldMask)(0),              // 80: insights.networth.model.NetWorthRefreshSessionFieldMask
	(model.NetWorthRefreshAsset)(0),                         // 81: insights.networth.model.NetWorthRefreshAsset
	(*durationpb.Duration)(nil),                             // 82: google.protobuf.Duration
	(frontend.AssetFormFieldSearchIdentifier)(0),            // 83: insights.networth.frontend.AssetFormFieldSearchIdentifier
	(*model.PmsProvider)(nil),                               // 84: insights.networth.model.PmsProvider
	(*model.AlternativeInvestmentFund)(nil),                 // 85: insights.networth.model.AlternativeInvestmentFund
	(*employment.EmployerInfo)(nil),                         // 86: employment.EmployerInfo
	(*dynamic_elements.FetchDynamicElementsRequest)(nil),    // 87: dynamic_elements.FetchDynamicElementsRequest
	(*dynamic_elements.DynamicElementCallbackRequest)(nil),  // 88: dynamic_elements.DynamicElementCallbackRequest
	(*dynamic_elements.FetchDynamicElementsResponse)(nil),   // 89: dynamic_elements.FetchDynamicElementsResponse
	(*dynamic_elements.DynamicElementCallbackResponse)(nil), // 90: dynamic_elements.DynamicElementCallbackResponse
}
var file_api_insights_networth_service_proto_depIdxs = []int32{
	65,  // 0: insights.networth.MagicImportFilesRequest.files:type_name -> api.typesv2.common.file.File
	66,  // 1: insights.networth.MagicImportFilesResponse.status:type_name -> rpc.Status
	67,  // 2: insights.networth.MagicImportFilesResponse.next_action:type_name -> frontend.deeplink.Deeplink
	68,  // 3: insights.networth.MagicImportFilesResponse.magic_import_details:type_name -> insights.networth.magicimport.MagicImportDetails
	69,  // 4: insights.networth.GetAssetsDayChangeRequest.initial_date:type_name -> google.protobuf.Timestamp
	69,  // 5: insights.networth.GetAssetsDayChangeRequest.final_date:type_name -> google.protobuf.Timestamp
	70,  // 6: insights.networth.GetAssetsDayChangeRequest.asset_types:type_name -> insights.networth.enums.AssetType
	66,  // 7: insights.networth.GetAssetsDayChangeResponse.status:type_name -> rpc.Status
	62,  // 8: insights.networth.GetAssetsDayChangeResponse.asset_type_to_day_change_response_map:type_name -> insights.networth.GetAssetsDayChangeResponse.AssetTypeToDayChangeResponseMapEntry
	71,  // 9: insights.networth.AssetTypeDayChangeResponse.initial_date_total_value:type_name -> google.type.Money
	71,  // 10: insights.networth.AssetTypeDayChangeResponse.final_date_total_value:type_name -> google.type.Money
	21,  // 11: insights.networth.AssetTypeDayChangeResponse.assets_value_change:type_name -> insights.networth.AssetValueChange
	71,  // 12: insights.networth.AssetValueChange.initial_date_value:type_name -> google.type.Money
	71,  // 13: insights.networth.AssetValueChange.final_date_value:type_name -> google.type.Money
	66,  // 14: insights.networth.DeleteAllInvestmentDeclarationResponse.status:type_name -> rpc.Status
	72,  // 15: insights.networth.GetInvestmentDeclarationsRequest.page_context:type_name -> rpc.PageContextRequest
	73,  // 16: insights.networth.GetInvestmentDeclarationsRequest.instrument_types:type_name -> api.typesv2.InvestmentInstrumentType
	66,  // 17: insights.networth.GetInvestmentDeclarationsResponse.status:type_name -> rpc.Status
	74,  // 18: insights.networth.GetInvestmentDeclarationsResponse.page_context:type_name -> rpc.PageContextResponse
	75,  // 19: insights.networth.GetInvestmentDeclarationsResponse.investment_declaration:type_name -> insights.networth.model.InvestmentDeclaration
	36,  // 20: insights.networth.GetInvestmentDeclarationsResponse.investment_details:type_name -> insights.networth.InvestmentDetails
	66,  // 21: insights.networth.StoreSnapshotResponse.status:type_name -> rpc.Status
	73,  // 22: insights.networth.DeclareInvestmentRequest.instrument_type:type_name -> api.typesv2.InvestmentInstrumentType
	71,  // 23: insights.networth.DeclareInvestmentRequest.invested_amount:type_name -> google.type.Money
	69,  // 24: insights.networth.DeclareInvestmentRequest.invested_at:type_name -> google.protobuf.Timestamp
	69,  // 25: insights.networth.DeclareInvestmentRequest.maturity_time:type_name -> google.protobuf.Timestamp
	76,  // 26: insights.networth.DeclareInvestmentRequest.declaration_details:type_name -> insights.networth.model.OtherDeclarationDetails
	66,  // 27: insights.networth.DeclareInvestmentResponse.status:type_name -> rpc.Status
	75,  // 28: insights.networth.DeclareInvestmentResponse.investment_declaration:type_name -> insights.networth.model.InvestmentDeclaration
	75,  // 29: insights.networth.UpdateInvestmentDeclarationRequest.updated_declaration:type_name -> insights.networth.model.InvestmentDeclaration
	77,  // 30: insights.networth.UpdateInvestmentDeclarationRequest.field_masks:type_name -> insights.networth.model.InvestmentDeclarationFieldMask
	66,  // 31: insights.networth.UpdateInvestmentDeclarationResponse.status:type_name -> rpc.Status
	75,  // 32: insights.networth.UpdateInvestmentDeclarationResponse.investment_declaration:type_name -> insights.networth.model.InvestmentDeclaration
	66,  // 33: insights.networth.GetInvestmentDeclarationResponse.status:type_name -> rpc.Status
	75,  // 34: insights.networth.GetInvestmentDeclarationResponse.investment_declaration:type_name -> insights.networth.model.InvestmentDeclaration
	36,  // 35: insights.networth.GetInvestmentDeclarationResponse.investment_details:type_name -> insights.networth.InvestmentDetails
	66,  // 36: insights.networth.DeleteInvestmentDeclarationResponse.status:type_name -> rpc.Status
	75,  // 37: insights.networth.InvestmentDetails.investment_declaration:type_name -> insights.networth.model.InvestmentDeclaration
	71,  // 38: insights.networth.InvestmentDetails.current_value:type_name -> google.type.Money
	37,  // 39: insights.networth.InvestmentDetails.computed_investment_details:type_name -> insights.networth.ComputedInvestmentDetails
	38,  // 40: insights.networth.ComputedInvestmentDetails.esop_details:type_name -> insights.networth.EsopDetails
	0,   // 41: insights.networth.GetNetWorthValueRequest.asset_types:type_name -> insights.networth.AssetType
	1,   // 42: insights.networth.GetNetWorthValueRequest.liability_types:type_name -> insights.networth.LiabilityType
	66,  // 43: insights.networth.GetNetWorthValueResponse.status:type_name -> rpc.Status
	41,  // 44: insights.networth.GetNetWorthValueResponse.asset_values:type_name -> insights.networth.AssetValue
	42,  // 45: insights.networth.GetNetWorthValueResponse.liability_values:type_name -> insights.networth.LiabilityValue
	71,  // 46: insights.networth.GetNetWorthValueResponse.total_net_worth_value:type_name -> google.type.Money
	0,   // 47: insights.networth.AssetValue.net_worth_attribute:type_name -> insights.networth.AssetType
	70,  // 48: insights.networth.AssetValue.asset_type:type_name -> insights.networth.enums.AssetType
	71,  // 49: insights.networth.AssetValue.value:type_name -> google.type.Money
	2,   // 50: insights.networth.AssetValue.computation_status:type_name -> insights.networth.ComputationStatus
	1,   // 51: insights.networth.LiabilityValue.net_worth_attribute:type_name -> insights.networth.LiabilityType
	71,  // 52: insights.networth.LiabilityValue.value:type_name -> google.type.Money
	2,   // 53: insights.networth.LiabilityValue.computation_status:type_name -> insights.networth.ComputationStatus
	63,  // 54: insights.networth.UpdateBulkManualAssetsCurrentValueRequest.updated_asset_current_values:type_name -> insights.networth.UpdateBulkManualAssetsCurrentValueRequest.UpdatedAssetCurrentValuesEntry
	66,  // 55: insights.networth.UpdateBulkManualAssetsCurrentValueResponse.status:type_name -> rpc.Status
	78,  // 56: insights.networth.CreateNetWorthRefreshSessionRequest.asset_refresh_details:type_name -> insights.networth.model.AssetRefreshDetail
	66,  // 57: insights.networth.CreateNetWorthRefreshSessionResponse.status:type_name -> rpc.Status
	79,  // 58: insights.networth.CreateNetWorthRefreshSessionResponse.net_worth_refresh_session:type_name -> insights.networth.model.NetWorthRefreshSession
	79,  // 59: insights.networth.UpdateNetWorthRefreshSessionRequest.updated_net_worth_refresh_session:type_name -> insights.networth.model.NetWorthRefreshSession
	80,  // 60: insights.networth.UpdateNetWorthRefreshSessionRequest.field_masks:type_name -> insights.networth.model.NetWorthRefreshSessionFieldMask
	66,  // 61: insights.networth.UpdateNetWorthRefreshSessionResponse.status:type_name -> rpc.Status
	79,  // 62: insights.networth.UpdateNetWorthRefreshSessionResponse.net_worth_refresh_session:type_name -> insights.networth.model.NetWorthRefreshSession
	66,  // 63: insights.networth.GetNetWorthRefreshSessionResponse.status:type_name -> rpc.Status
	79,  // 64: insights.networth.GetNetWorthRefreshSessionResponse.net_worth_refresh_session:type_name -> insights.networth.model.NetWorthRefreshSession
	81,  // 65: insights.networth.GetNetWorthInstrumentsRefreshDetailsRequest.networth_refresh_assets:type_name -> insights.networth.model.NetWorthRefreshAsset
	66,  // 66: insights.networth.GetNetWorthInstrumentsRefreshDetailsResponse.status:type_name -> rpc.Status
	53,  // 67: insights.networth.GetNetWorthInstrumentsRefreshDetailsResponse.instrument_refresh_summary:type_name -> insights.networth.InstrumentRefreshDetails
	81,  // 68: insights.networth.InstrumentRefreshDetails.asset_name:type_name -> insights.networth.model.NetWorthRefreshAsset
	69,  // 69: insights.networth.InstrumentRefreshDetails.last_refreshed_time:type_name -> google.protobuf.Timestamp
	82,  // 70: insights.networth.InstrumentRefreshDetails.last_refreshed_duration:type_name -> google.protobuf.Duration
	64,  // 71: insights.networth.InstrumentRefreshDetails.manual_asset_refresh_details:type_name -> insights.networth.InstrumentRefreshDetails.ManualAssetRefreshDetailsEntry
	3,   // 72: insights.networth.InstrumentRefreshDetails.refresh_status:type_name -> insights.networth.RefreshStatus
	69,  // 73: insights.networth.ManualAssetRefreshDetails.last_refreshed_time:type_name -> google.protobuf.Timestamp
	82,  // 74: insights.networth.ManualAssetRefreshDetails.last_refreshed_duration:type_name -> google.protobuf.Duration
	75,  // 75: insights.networth.ManualAssetRefreshDetails.investment_declarations:type_name -> insights.networth.model.InvestmentDeclaration
	3,   // 76: insights.networth.ManualAssetRefreshDetails.refresh_status:type_name -> insights.networth.RefreshStatus
	83,  // 77: insights.networth.SearchAssetFormFieldOptionsRequest.search_identifier:type_name -> insights.networth.frontend.AssetFormFieldSearchIdentifier
	66,  // 78: insights.networth.SearchAssetFormFieldOptionsResponse.status:type_name -> rpc.Status
	57,  // 79: insights.networth.SearchAssetFormFieldOptionsResponse.pms_provider_list:type_name -> insights.networth.PmsProviderList
	58,  // 80: insights.networth.SearchAssetFormFieldOptionsResponse.aif_list:type_name -> insights.networth.AifList
	59,  // 81: insights.networth.SearchAssetFormFieldOptionsResponse.company_list:type_name -> insights.networth.CompanyList
	84,  // 82: insights.networth.PmsProviderList.pms_providers:type_name -> insights.networth.model.PmsProvider
	85,  // 83: insights.networth.AifList.alternative_investment_funds:type_name -> insights.networth.model.AlternativeInvestmentFund
	86,  // 84: insights.networth.CompanyList.employer_info:type_name -> employment.EmployerInfo
	69,  // 85: insights.networth.GetPortfolioChangeSummaryRequest.history_date:type_name -> google.protobuf.Timestamp
	66,  // 86: insights.networth.GetPortfolioChangeSummaryResponse.status:type_name -> rpc.Status
	71,  // 87: insights.networth.GetPortfolioChangeSummaryResponse.aggregated_networth_at_date:type_name -> google.type.Money
	71,  // 88: insights.networth.GetPortfolioChangeSummaryResponse.current_networth_value:type_name -> google.type.Money
	71,  // 89: insights.networth.GetPortfolioChangeSummaryResponse.amount_change:type_name -> google.type.Money
	20,  // 90: insights.networth.GetAssetsDayChangeResponse.AssetTypeToDayChangeResponseMapEntry.value:type_name -> insights.networth.AssetTypeDayChangeResponse
	71,  // 91: insights.networth.UpdateBulkManualAssetsCurrentValueRequest.UpdatedAssetCurrentValuesEntry.value:type_name -> google.type.Money
	54,  // 92: insights.networth.InstrumentRefreshDetails.ManualAssetRefreshDetailsEntry.value:type_name -> insights.networth.ManualAssetRefreshDetails
	39,  // 93: insights.networth.NetWorth.GetNetWorthValue:input_type -> insights.networth.GetNetWorthValueRequest
	28,  // 94: insights.networth.NetWorth.DeclareInvestment:input_type -> insights.networth.DeclareInvestmentRequest
	30,  // 95: insights.networth.NetWorth.UpdateInvestmentDeclaration:input_type -> insights.networth.UpdateInvestmentDeclarationRequest
	32,  // 96: insights.networth.NetWorth.GetInvestmentDeclaration:input_type -> insights.networth.GetInvestmentDeclarationRequest
	24,  // 97: insights.networth.NetWorth.GetInvestmentDeclarations:input_type -> insights.networth.GetInvestmentDeclarationsRequest
	34,  // 98: insights.networth.NetWorth.DeleteInvestmentDeclaration:input_type -> insights.networth.DeleteInvestmentDeclarationRequest
	43,  // 99: insights.networth.NetWorth.UpdateBulkManualAssetsCurrentValue:input_type -> insights.networth.UpdateBulkManualAssetsCurrentValueRequest
	45,  // 100: insights.networth.NetWorth.CreateNetWorthRefreshSession:input_type -> insights.networth.CreateNetWorthRefreshSessionRequest
	47,  // 101: insights.networth.NetWorth.UpdateNetWorthRefreshSession:input_type -> insights.networth.UpdateNetWorthRefreshSessionRequest
	49,  // 102: insights.networth.NetWorth.GetNetWorthRefreshSession:input_type -> insights.networth.GetNetWorthRefreshSessionRequest
	51,  // 103: insights.networth.NetWorth.GetNetWorthInstrumentsRefreshDetails:input_type -> insights.networth.GetNetWorthInstrumentsRefreshDetailsRequest
	55,  // 104: insights.networth.NetWorth.SearchAssetFormFieldOptions:input_type -> insights.networth.SearchAssetFormFieldOptionsRequest
	26,  // 105: insights.networth.NetWorth.StoreSnapshot:input_type -> insights.networth.StoreSnapshotRequest
	87,  // 106: insights.networth.NetWorth.FetchDynamicElements:input_type -> dynamic_elements.FetchDynamicElementsRequest
	88,  // 107: insights.networth.NetWorth.DynamicElementCallback:input_type -> dynamic_elements.DynamicElementCallbackRequest
	60,  // 108: insights.networth.NetWorth.GetPortfolioChangeSummary:input_type -> insights.networth.GetPortfolioChangeSummaryRequest
	22,  // 109: insights.networth.NetWorth.DeleteAllInvestmentDeclaration:input_type -> insights.networth.DeleteAllInvestmentDeclarationRequest
	18,  // 110: insights.networth.NetWorth.GetAssetsDayChange:input_type -> insights.networth.GetAssetsDayChangeRequest
	16,  // 111: insights.networth.NetWorth.MagicImportFiles:input_type -> insights.networth.MagicImportFilesRequest
	40,  // 112: insights.networth.NetWorth.GetNetWorthValue:output_type -> insights.networth.GetNetWorthValueResponse
	29,  // 113: insights.networth.NetWorth.DeclareInvestment:output_type -> insights.networth.DeclareInvestmentResponse
	31,  // 114: insights.networth.NetWorth.UpdateInvestmentDeclaration:output_type -> insights.networth.UpdateInvestmentDeclarationResponse
	33,  // 115: insights.networth.NetWorth.GetInvestmentDeclaration:output_type -> insights.networth.GetInvestmentDeclarationResponse
	25,  // 116: insights.networth.NetWorth.GetInvestmentDeclarations:output_type -> insights.networth.GetInvestmentDeclarationsResponse
	29,  // 117: insights.networth.NetWorth.DeleteInvestmentDeclaration:output_type -> insights.networth.DeclareInvestmentResponse
	44,  // 118: insights.networth.NetWorth.UpdateBulkManualAssetsCurrentValue:output_type -> insights.networth.UpdateBulkManualAssetsCurrentValueResponse
	46,  // 119: insights.networth.NetWorth.CreateNetWorthRefreshSession:output_type -> insights.networth.CreateNetWorthRefreshSessionResponse
	48,  // 120: insights.networth.NetWorth.UpdateNetWorthRefreshSession:output_type -> insights.networth.UpdateNetWorthRefreshSessionResponse
	50,  // 121: insights.networth.NetWorth.GetNetWorthRefreshSession:output_type -> insights.networth.GetNetWorthRefreshSessionResponse
	52,  // 122: insights.networth.NetWorth.GetNetWorthInstrumentsRefreshDetails:output_type -> insights.networth.GetNetWorthInstrumentsRefreshDetailsResponse
	56,  // 123: insights.networth.NetWorth.SearchAssetFormFieldOptions:output_type -> insights.networth.SearchAssetFormFieldOptionsResponse
	27,  // 124: insights.networth.NetWorth.StoreSnapshot:output_type -> insights.networth.StoreSnapshotResponse
	89,  // 125: insights.networth.NetWorth.FetchDynamicElements:output_type -> dynamic_elements.FetchDynamicElementsResponse
	90,  // 126: insights.networth.NetWorth.DynamicElementCallback:output_type -> dynamic_elements.DynamicElementCallbackResponse
	61,  // 127: insights.networth.NetWorth.GetPortfolioChangeSummary:output_type -> insights.networth.GetPortfolioChangeSummaryResponse
	23,  // 128: insights.networth.NetWorth.DeleteAllInvestmentDeclaration:output_type -> insights.networth.DeleteAllInvestmentDeclarationResponse
	19,  // 129: insights.networth.NetWorth.GetAssetsDayChange:output_type -> insights.networth.GetAssetsDayChangeResponse
	17,  // 130: insights.networth.NetWorth.MagicImportFiles:output_type -> insights.networth.MagicImportFilesResponse
	112, // [112:131] is the sub-list for method output_type
	93,  // [93:112] is the sub-list for method input_type
	93,  // [93:93] is the sub-list for extension type_name
	93,  // [93:93] is the sub-list for extension extendee
	0,   // [0:93] is the sub-list for field type_name
}

func init() { file_api_insights_networth_service_proto_init() }
func file_api_insights_networth_service_proto_init() {
	if File_api_insights_networth_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_insights_networth_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MagicImportFilesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MagicImportFilesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetsDayChangeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetsDayChangeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetTypeDayChangeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetValueChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAllInvestmentDeclarationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAllInvestmentDeclarationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvestmentDeclarationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvestmentDeclarationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreSnapshotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreSnapshotResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeclareInvestmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeclareInvestmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateInvestmentDeclarationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateInvestmentDeclarationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvestmentDeclarationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvestmentDeclarationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteInvestmentDeclarationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteInvestmentDeclarationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvestmentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComputedInvestmentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EsopDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNetWorthValueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNetWorthValueResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LiabilityValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBulkManualAssetsCurrentValueRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBulkManualAssetsCurrentValueResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateNetWorthRefreshSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateNetWorthRefreshSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateNetWorthRefreshSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateNetWorthRefreshSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNetWorthRefreshSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNetWorthRefreshSessionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNetWorthInstrumentsRefreshDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNetWorthInstrumentsRefreshDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstrumentRefreshDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualAssetRefreshDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchAssetFormFieldOptionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchAssetFormFieldOptionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PmsProviderList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AifList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPortfolioChangeSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPortfolioChangeSummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_insights_networth_service_proto_msgTypes[21].OneofWrappers = []interface{}{
		(*ComputedInvestmentDetails_EsopDetails)(nil),
	}
	file_api_insights_networth_service_proto_msgTypes[40].OneofWrappers = []interface{}{
		(*SearchAssetFormFieldOptionsResponse_PmsProviderList)(nil),
		(*SearchAssetFormFieldOptionsResponse_AifList)(nil),
		(*SearchAssetFormFieldOptionsResponse_CompanyList)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_insights_networth_service_proto_rawDesc,
			NumEnums:      16,
			NumMessages:   49,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_insights_networth_service_proto_goTypes,
		DependencyIndexes: file_api_insights_networth_service_proto_depIdxs,
		EnumInfos:         file_api_insights_networth_service_proto_enumTypes,
		MessageInfos:      file_api_insights_networth_service_proto_msgTypes,
	}.Build()
	File_api_insights_networth_service_proto = out.File
	file_api_insights_networth_service_proto_rawDesc = nil
	file_api_insights_networth_service_proto_goTypes = nil
	file_api_insights_networth_service_proto_depIdxs = nil
}
