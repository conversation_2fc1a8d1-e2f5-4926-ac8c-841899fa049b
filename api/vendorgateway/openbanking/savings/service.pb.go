// protolint:disable MAX_LINE_LENGTH

// Vendor gateway APIs for savings account management with a partner bank.
// This includes operations like create, update, and other operations.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/openbanking/savings/service.proto

package savings

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	user "github.com/epifi/gamma/api/user"
	header "github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// KYC done by user can either be min kyc or full kyc. This enum should be maintain in sync with kyc.KYCLevel
type KYCLevel int32

const (
	KYCLevel_UNSPECIFIED KYCLevel = 0
	// Various conditions lead to a customer becoming min kyc customer:
	// 1. Dedupe customer, with partial kyc and goes through ekyc
	// 2. CKYC fails due to some reason, and customer taken through ekyc
	KYCLevel_MIN_KYC KYCLevel = 1
	// Under the following conditions, a customer can become a full kyc customer:
	// 1. Dedupe customer with full kyc done
	// 2. Dedupe customer, with partial kyc and goes through ckyc successfully
	KYCLevel_FULL_KYC KYCLevel = 2
)

// Enum value maps for KYCLevel.
var (
	KYCLevel_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "MIN_KYC",
		2: "FULL_KYC",
	}
	KYCLevel_value = map[string]int32{
		"UNSPECIFIED": 0,
		"MIN_KYC":     1,
		"FULL_KYC":    2,
	}
)

func (x KYCLevel) Enum() *KYCLevel {
	p := new(KYCLevel)
	*p = x
	return p
}

func (x KYCLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KYCLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_savings_service_proto_enumTypes[0].Descriptor()
}

func (KYCLevel) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_savings_service_proto_enumTypes[0]
}

func (x KYCLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KYCLevel.Descriptor instead.
func (KYCLevel) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{0}
}

type CreateAccountResponse_Status int32

const (
	CreateAccountResponse_OK                     CreateAccountResponse_Status = 0
	CreateAccountResponse_STATUS_UNKNOWN         CreateAccountResponse_Status = 100
	CreateAccountResponse_INVALID_SENDER_DETAILS CreateAccountResponse_Status = 102
	CreateAccountResponse_SENDER_NOT_ENABLED     CreateAccountResponse_Status = 103
	CreateAccountResponse_DUPLICATE_REQUESTID    CreateAccountResponse_Status = 104
	CreateAccountResponse_NO_REQUESTID           CreateAccountResponse_Status = 105
	CreateAccountResponse_APPLICATION_ERROR      CreateAccountResponse_Status = 106
	CreateAccountResponse_INVALID_DEVICE         CreateAccountResponse_Status = 107
	CreateAccountResponse_INVALID_TRAN           CreateAccountResponse_Status = 108
	CreateAccountResponse_SB_ACCOUNT_IN_PROCESS  CreateAccountResponse_Status = 109
	CreateAccountResponse_DEVICE_NOT_LINKED      CreateAccountResponse_Status = 110
	CreateAccountResponse_INVALID_DATA           CreateAccountResponse_Status = 111
	CreateAccountResponse_INVALID_INPUT          CreateAccountResponse_Status = 112
)

// Enum value maps for CreateAccountResponse_Status.
var (
	CreateAccountResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "STATUS_UNKNOWN",
		102: "INVALID_SENDER_DETAILS",
		103: "SENDER_NOT_ENABLED",
		104: "DUPLICATE_REQUESTID",
		105: "NO_REQUESTID",
		106: "APPLICATION_ERROR",
		107: "INVALID_DEVICE",
		108: "INVALID_TRAN",
		109: "SB_ACCOUNT_IN_PROCESS",
		110: "DEVICE_NOT_LINKED",
		111: "INVALID_DATA",
		112: "INVALID_INPUT",
	}
	CreateAccountResponse_Status_value = map[string]int32{
		"OK":                     0,
		"STATUS_UNKNOWN":         100,
		"INVALID_SENDER_DETAILS": 102,
		"SENDER_NOT_ENABLED":     103,
		"DUPLICATE_REQUESTID":    104,
		"NO_REQUESTID":           105,
		"APPLICATION_ERROR":      106,
		"INVALID_DEVICE":         107,
		"INVALID_TRAN":           108,
		"SB_ACCOUNT_IN_PROCESS":  109,
		"DEVICE_NOT_LINKED":      110,
		"INVALID_DATA":           111,
		"INVALID_INPUT":          112,
	}
)

func (x CreateAccountResponse_Status) Enum() *CreateAccountResponse_Status {
	p := new(CreateAccountResponse_Status)
	*p = x
	return p
}

func (x CreateAccountResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateAccountResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_savings_service_proto_enumTypes[1].Descriptor()
}

func (CreateAccountResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_savings_service_proto_enumTypes[1]
}

func (x CreateAccountResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateAccountResponse_Status.Descriptor instead.
func (CreateAccountResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{1, 0}
}

type CheckAccountStatusResponse_Status int32

const (
	CheckAccountStatusResponse_OK                     CheckAccountStatusResponse_Status = 0
	CheckAccountStatusResponse_STATUS_UNKNOWN         CheckAccountStatusResponse_Status = 100
	CheckAccountStatusResponse_DUPLICATE_REQUESTID    CheckAccountStatusResponse_Status = 102
	CheckAccountStatusResponse_NO_REQUESTID           CheckAccountStatusResponse_Status = 103
	CheckAccountStatusResponse_INVALID_SENDER_DETAILS CheckAccountStatusResponse_Status = 104
	CheckAccountStatusResponse_SENDER_NOT_ENABLED     CheckAccountStatusResponse_Status = 105
	CheckAccountStatusResponse_INSERTION_EXCEPTION    CheckAccountStatusResponse_Status = 106
	CheckAccountStatusResponse_EXCEPTION              CheckAccountStatusResponse_Status = 107
	CheckAccountStatusResponse_FIELD_EXCEPTION        CheckAccountStatusResponse_Status = 108
	CheckAccountStatusResponse_SB_ACCOUNT_IN_PROCESS  CheckAccountStatusResponse_Status = 109
	CheckAccountStatusResponse_INVALID_DATA           CheckAccountStatusResponse_Status = 110
	CheckAccountStatusResponse_SOCKET_EXCEPTION       CheckAccountStatusResponse_Status = 111
	// If the initial customer creation request did not reach vendor we get details not found.
	// We can retry customer creation with the same requestId
	CheckAccountStatusResponse_DETAILS_NOT_FOUND CheckAccountStatusResponse_Status = 112
	// federal ResponseAction":"FAILURE","ResponseCode":"OBE0059"
	CheckAccountStatusResponse_INVALID_DEVICE_TOKEN CheckAccountStatusResponse_Status = 113
	// federal ResponseAction":"FAILURE","ResponseCode":"SB_012"
	CheckAccountStatusResponse_SAVINGS_ACCOUNT_FAILURE CheckAccountStatusResponse_Status = 114
)

// Enum value maps for CheckAccountStatusResponse_Status.
var (
	CheckAccountStatusResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "STATUS_UNKNOWN",
		102: "DUPLICATE_REQUESTID",
		103: "NO_REQUESTID",
		104: "INVALID_SENDER_DETAILS",
		105: "SENDER_NOT_ENABLED",
		106: "INSERTION_EXCEPTION",
		107: "EXCEPTION",
		108: "FIELD_EXCEPTION",
		109: "SB_ACCOUNT_IN_PROCESS",
		110: "INVALID_DATA",
		111: "SOCKET_EXCEPTION",
		112: "DETAILS_NOT_FOUND",
		113: "INVALID_DEVICE_TOKEN",
		114: "SAVINGS_ACCOUNT_FAILURE",
	}
	CheckAccountStatusResponse_Status_value = map[string]int32{
		"OK":                      0,
		"STATUS_UNKNOWN":          100,
		"DUPLICATE_REQUESTID":     102,
		"NO_REQUESTID":            103,
		"INVALID_SENDER_DETAILS":  104,
		"SENDER_NOT_ENABLED":      105,
		"INSERTION_EXCEPTION":     106,
		"EXCEPTION":               107,
		"FIELD_EXCEPTION":         108,
		"SB_ACCOUNT_IN_PROCESS":   109,
		"INVALID_DATA":            110,
		"SOCKET_EXCEPTION":        111,
		"DETAILS_NOT_FOUND":       112,
		"INVALID_DEVICE_TOKEN":    113,
		"SAVINGS_ACCOUNT_FAILURE": 114,
	}
)

func (x CheckAccountStatusResponse_Status) Enum() *CheckAccountStatusResponse_Status {
	p := new(CheckAccountStatusResponse_Status)
	*p = x
	return p
}

func (x CheckAccountStatusResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CheckAccountStatusResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_savings_service_proto_enumTypes[2].Descriptor()
}

func (CheckAccountStatusResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_savings_service_proto_enumTypes[2]
}

func (x CheckAccountStatusResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CheckAccountStatusResponse_Status.Descriptor instead.
func (CheckAccountStatusResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{3, 0}
}

type GetBalanceResponse_Status int32

const (
	GetBalanceResponse_OK GetBalanceResponse_Status = 0
	// Device Temporarily Deactivated by bank Code: OBE0170
	GetBalanceResponse_DEVICE_TEMPORARILY_DEACTIVATED GetBalanceResponse_Status = 100
)

// Enum value maps for GetBalanceResponse_Status.
var (
	GetBalanceResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "DEVICE_TEMPORARILY_DEACTIVATED",
	}
	GetBalanceResponse_Status_value = map[string]int32{
		"OK":                             0,
		"DEVICE_TEMPORARILY_DEACTIVATED": 100,
	}
)

func (x GetBalanceResponse_Status) Enum() *GetBalanceResponse_Status {
	p := new(GetBalanceResponse_Status)
	*p = x
	return p
}

func (x GetBalanceResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetBalanceResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_savings_service_proto_enumTypes[3].Descriptor()
}

func (GetBalanceResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_savings_service_proto_enumTypes[3]
}

func (x GetBalanceResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetBalanceResponse_Status.Descriptor instead.
func (GetBalanceResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{5, 0}
}

type GetOpeningBalanceResponse_Status int32

const (
	GetOpeningBalanceResponse_OK GetOpeningBalanceResponse_Status = 0
	// for response code "OBE0170" from vendor, "Device is deactivated temporary by bank"
	GetOpeningBalanceResponse_DEVICE_TEMPORARILY_DEACTIVATED GetOpeningBalanceResponse_Status = 101
)

// Enum value maps for GetOpeningBalanceResponse_Status.
var (
	GetOpeningBalanceResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "DEVICE_TEMPORARILY_DEACTIVATED",
	}
	GetOpeningBalanceResponse_Status_value = map[string]int32{
		"OK":                             0,
		"DEVICE_TEMPORARILY_DEACTIVATED": 101,
	}
)

func (x GetOpeningBalanceResponse_Status) Enum() *GetOpeningBalanceResponse_Status {
	p := new(GetOpeningBalanceResponse_Status)
	*p = x
	return p
}

func (x GetOpeningBalanceResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetOpeningBalanceResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_savings_service_proto_enumTypes[4].Descriptor()
}

func (GetOpeningBalanceResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_savings_service_proto_enumTypes[4]
}

func (x GetOpeningBalanceResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetOpeningBalanceResponse_Status.Descriptor instead.
func (GetOpeningBalanceResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{7, 0}
}

type GetBalanceV1Response_Status int32

const (
	GetBalanceV1Response_OK GetBalanceV1Response_Status = 0
)

// Enum value maps for GetBalanceV1Response_Status.
var (
	GetBalanceV1Response_Status_name = map[int32]string{
		0: "OK",
	}
	GetBalanceV1Response_Status_value = map[string]int32{
		"OK": 0,
	}
)

func (x GetBalanceV1Response_Status) Enum() *GetBalanceV1Response_Status {
	p := new(GetBalanceV1Response_Status)
	*p = x
	return p
}

func (x GetBalanceV1Response_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetBalanceV1Response_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_savings_service_proto_enumTypes[5].Descriptor()
}

func (GetBalanceV1Response_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_savings_service_proto_enumTypes[5]
}

func (x GetBalanceV1Response_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetBalanceV1Response_Status.Descriptor instead.
func (GetBalanceV1Response_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{9, 0}
}

// Request message for initiation of creation of a savings account.
type CreateAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendorgateway APIs.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Unique request ID generated by epiFi.
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Customer ID(s) of the operators of this bank account.
	BankCustomerId []string `protobuf:"bytes,3,rep,name=bank_customer_id,json=bankCustomerId,proto3" json:"bank_customer_id,omitempty"`
	// device details of the customer
	DeviceDetails *header.Auth     `protobuf:"bytes,4,opt,name=device_details,json=deviceDetails,proto3" json:"device_details,omitempty"`
	CustomerName  string           `protobuf:"bytes,5,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	AccountNo     string           `protobuf:"bytes,6,opt,name=account_no,json=accountNo,proto3" json:"account_no,omitempty"`
	MobileNo      string           `protobuf:"bytes,7,opt,name=mobile_no,json=mobileNo,proto3" json:"mobile_no,omitempty"`
	EmailId       string           `protobuf:"bytes,8,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	Nominee       *typesv2.Nominee `protobuf:"bytes,9,opt,name=nominee,proto3" json:"nominee,omitempty"`
	// account type - min account/ full account depends on kyc level
	// vendor_sku is directly used instead of evaluation through kyc level
	//
	// Deprecated: Marked as deprecated in api/vendorgateway/openbanking/savings/service.proto.
	KycLevel KYCLevel `protobuf:"varint,10,opt,name=kyc_level,json=kycLevel,proto3,enum=vendorgateway.openbanking.savings.KYCLevel" json:"kyc_level,omitempty"`
	// product type of savings account to be created.
	VendorSku string `protobuf:"bytes,11,opt,name=vendor_sku,json=vendorSku,proto3" json:"vendor_sku,omitempty"`
	// Sol ID with which the account is supposed to be created.
	SolId string `protobuf:"bytes,12,opt,name=sol_id,json=solId,proto3" json:"sol_id,omitempty"`
	// Source of funds for savings account
	SourceOfFunds typesv2.SourceOfFunds `protobuf:"varint,13,opt,name=source_of_funds,json=sourceOfFunds,proto3,enum=api.typesv2.SourceOfFunds" json:"source_of_funds,omitempty"`
	// if account will be used to receive any scholarship
	ScholarshipFlag bool `protobuf:"varint,14,opt,name=scholarship_flag,json=scholarshipFlag,proto3" json:"scholarship_flag,omitempty"`
	// if account will be used for Direct Benefit Transfer
	DbtFlag                 bool                            `protobuf:"varint,15,opt,name=dbt_flag,json=dbtFlag,proto3" json:"dbt_flag,omitempty"`
	AnnualTxnVolume         *user.SalaryRange               `protobuf:"bytes,16,opt,name=annual_txn_volume,json=annualTxnVolume,proto3" json:"annual_txn_volume,omitempty"`
	PurposeOfSavingsAccount typesv2.PurposeOfSavingsAccount `protobuf:"varint,17,opt,name=purpose_of_savings_account,json=purposeOfSavingsAccount,proto3,enum=api.typesv2.PurposeOfSavingsAccount" json:"purpose_of_savings_account,omitempty"`
}

func (x *CreateAccountRequest) Reset() {
	*x = CreateAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountRequest) ProtoMessage() {}

func (x *CreateAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountRequest.ProtoReflect.Descriptor instead.
func (*CreateAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateAccountRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreateAccountRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CreateAccountRequest) GetBankCustomerId() []string {
	if x != nil {
		return x.BankCustomerId
	}
	return nil
}

func (x *CreateAccountRequest) GetDeviceDetails() *header.Auth {
	if x != nil {
		return x.DeviceDetails
	}
	return nil
}

func (x *CreateAccountRequest) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *CreateAccountRequest) GetAccountNo() string {
	if x != nil {
		return x.AccountNo
	}
	return ""
}

func (x *CreateAccountRequest) GetMobileNo() string {
	if x != nil {
		return x.MobileNo
	}
	return ""
}

func (x *CreateAccountRequest) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *CreateAccountRequest) GetNominee() *typesv2.Nominee {
	if x != nil {
		return x.Nominee
	}
	return nil
}

// Deprecated: Marked as deprecated in api/vendorgateway/openbanking/savings/service.proto.
func (x *CreateAccountRequest) GetKycLevel() KYCLevel {
	if x != nil {
		return x.KycLevel
	}
	return KYCLevel_UNSPECIFIED
}

func (x *CreateAccountRequest) GetVendorSku() string {
	if x != nil {
		return x.VendorSku
	}
	return ""
}

func (x *CreateAccountRequest) GetSolId() string {
	if x != nil {
		return x.SolId
	}
	return ""
}

func (x *CreateAccountRequest) GetSourceOfFunds() typesv2.SourceOfFunds {
	if x != nil {
		return x.SourceOfFunds
	}
	return typesv2.SourceOfFunds(0)
}

func (x *CreateAccountRequest) GetScholarshipFlag() bool {
	if x != nil {
		return x.ScholarshipFlag
	}
	return false
}

func (x *CreateAccountRequest) GetDbtFlag() bool {
	if x != nil {
		return x.DbtFlag
	}
	return false
}

func (x *CreateAccountRequest) GetAnnualTxnVolume() *user.SalaryRange {
	if x != nil {
		return x.AnnualTxnVolume
	}
	return nil
}

func (x *CreateAccountRequest) GetPurposeOfSavingsAccount() typesv2.PurposeOfSavingsAccount {
	if x != nil {
		return x.PurposeOfSavingsAccount
	}
	return typesv2.PurposeOfSavingsAccount(0)
}

// Response message for CreateAccount. Returns either success or failure for
// initiation of creation of the account the bank's end. The
// actual account details need to be fetched in the check status call.
type CreateAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	VendorStatus *vendorgateway.VendorStatus `protobuf:"bytes,2,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
}

func (x *CreateAccountResponse) Reset() {
	*x = CreateAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountResponse) ProtoMessage() {}

func (x *CreateAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountResponse.ProtoReflect.Descriptor instead.
func (*CreateAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateAccountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateAccountResponse) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

// Request message for checking account creation status.
type CheckAccountStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendorgateway APIs.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// The same request ID which was passed in the CreateAccount API.
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// device params
	DeviceDetails *header.Auth `protobuf:"bytes,3,opt,name=device_details,json=deviceDetails,proto3" json:"device_details,omitempty"`
	// phone number
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *CheckAccountStatusRequest) Reset() {
	*x = CheckAccountStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAccountStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAccountStatusRequest) ProtoMessage() {}

func (x *CheckAccountStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAccountStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckAccountStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{2}
}

func (x *CheckAccountStatusRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CheckAccountStatusRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CheckAccountStatusRequest) GetDeviceDetails() *header.Auth {
	if x != nil {
		return x.DeviceDetails
	}
	return nil
}

func (x *CheckAccountStatusRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

// Response message for checking account status. Returns the
// account number of the created account.
type CheckAccountStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Account number of the savings account that was created. This is string to
	// accommodate leading zeros which sometimes exist in account numbers.
	AccountNumber string      `protobuf:"bytes,1,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	Status        *rpc.Status `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// time at which account creation was successful at vendor's end
	CreatedAt    *timestamppb.Timestamp      `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	VendorStatus *vendorgateway.VendorStatus `protobuf:"bytes,4,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
}

func (x *CheckAccountStatusResponse) Reset() {
	*x = CheckAccountStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAccountStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAccountStatusResponse) ProtoMessage() {}

func (x *CheckAccountStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAccountStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckAccountStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{3}
}

func (x *CheckAccountStatusResponse) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *CheckAccountStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckAccountStatusResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CheckAccountStatusResponse) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

// Request for checking balance of a savings account.
type GetBalanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendorgateway APIs.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// registered device info of user/customer
	Auth *header.Auth `protobuf:"bytes,2,opt,name=auth,proto3" json:"auth,omitempty"`
	// Unique request ID for this request generated by epiFi.
	RequestId string `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Account number of the bank account whose balance needs to
	// be fetched.
	AccountNumber string `protobuf:"bytes,4,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// phone number of the user
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,5,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *GetBalanceRequest) Reset() {
	*x = GetBalanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceRequest) ProtoMessage() {}

func (x *GetBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceRequest.ProtoReflect.Descriptor instead.
func (*GetBalanceRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetBalanceRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetBalanceRequest) GetAuth() *header.Auth {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *GetBalanceRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetBalanceRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *GetBalanceRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

// Response message for check balance. Returns the current balance amount, the ledger balance
// which is the opening balance available on the given day along with he currency code and account status
type GetBalanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Available Balance of the account as of balance_at timestamp
	AvailableBalance *money.Money `protobuf:"bytes,2,opt,name=available_balance,json=availableBalance,proto3" json:"available_balance,omitempty"`
	// Ledger balance of the account as of balance_at timestamp
	LedgerBalance *money.Money `protobuf:"bytes,3,opt,name=ledger_balance,json=ledgerBalance,proto3" json:"ledger_balance,omitempty"`
	// Currency of the balance amount
	Currency string `protobuf:"bytes,4,opt,name=currency,proto3" json:"currency,omitempty"`
	// Account status of the given account
	AccountStatus string `protobuf:"bytes,5,opt,name=account_status,json=accountStatus,proto3" json:"account_status,omitempty"`
	// timestamp corresponding to which balance is returned from the bank
	BalanceAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=balance_at,json=balanceAt,proto3" json:"balance_at,omitempty"`
}

func (x *GetBalanceResponse) Reset() {
	*x = GetBalanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceResponse) ProtoMessage() {}

func (x *GetBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceResponse.ProtoReflect.Descriptor instead.
func (*GetBalanceResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetBalanceResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetBalanceResponse) GetAvailableBalance() *money.Money {
	if x != nil {
		return x.AvailableBalance
	}
	return nil
}

func (x *GetBalanceResponse) GetLedgerBalance() *money.Money {
	if x != nil {
		return x.LedgerBalance
	}
	return nil
}

func (x *GetBalanceResponse) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *GetBalanceResponse) GetAccountStatus() string {
	if x != nil {
		return x.AccountStatus
	}
	return ""
}

func (x *GetBalanceResponse) GetBalanceAt() *timestamppb.Timestamp {
	if x != nil {
		return x.BalanceAt
	}
	return nil
}

type GetOpeningBalanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendorgateway APIs.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// account number to identify the account for which opening balance needs
	// to be fetched
	AccountNumber string `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// date on which opening balance need to be enquired
	//
	// Deprecated: Marked as deprecated in api/vendorgateway/openbanking/savings/service.proto.
	Date *date.Date `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"`
	// timestamp for which opening balance need to be enquired
	BalanceAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=balance_at,json=balanceAt,proto3" json:"balance_at,omitempty"`
	// id of registered device
	DeviceId string `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// token corresponding to the registered device
	DeviceToken string `protobuf:"bytes,5,opt,name=device_token,json=deviceToken,proto3" json:"device_token,omitempty"`
	// customer id of the actor to which the account belongs to
	CustomerId string `protobuf:"bytes,6,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// phone number of the account holder
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,7,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// User group flag for using new closing balance api
	IsClosingBalanceUserGroup bool `protobuf:"varint,9,opt,name=is_closing_balance_user_group,json=isClosingBalanceUserGroup,proto3" json:"is_closing_balance_user_group,omitempty"`
}

func (x *GetOpeningBalanceRequest) Reset() {
	*x = GetOpeningBalanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOpeningBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOpeningBalanceRequest) ProtoMessage() {}

func (x *GetOpeningBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOpeningBalanceRequest.ProtoReflect.Descriptor instead.
func (*GetOpeningBalanceRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetOpeningBalanceRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetOpeningBalanceRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

// Deprecated: Marked as deprecated in api/vendorgateway/openbanking/savings/service.proto.
func (x *GetOpeningBalanceRequest) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *GetOpeningBalanceRequest) GetBalanceAt() *timestamppb.Timestamp {
	if x != nil {
		return x.BalanceAt
	}
	return nil
}

func (x *GetOpeningBalanceRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *GetOpeningBalanceRequest) GetDeviceToken() string {
	if x != nil {
		return x.DeviceToken
	}
	return ""
}

func (x *GetOpeningBalanceRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *GetOpeningBalanceRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *GetOpeningBalanceRequest) GetIsClosingBalanceUserGroup() bool {
	if x != nil {
		return x.IsClosingBalanceUserGroup
	}
	return false
}

type GetOpeningBalanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	AccountNumber  string       `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	AccountName    string       `protobuf:"bytes,3,opt,name=account_name,json=accountName,proto3" json:"account_name,omitempty"`
	OpeningBalance *money.Money `protobuf:"bytes,5,opt,name=opening_balance,json=openingBalance,proto3" json:"opening_balance,omitempty"`
	// Represents the time when the last closing balance for the account was computed w.r.t to the enquired date.
	// Opening balance for an account for a given date d = closing balance on d-1.
	// So depending on the time when enquiry is triggered to banking servers, the date can help a client
	// understand if bank side EOD has finished by looking at `bank_eod_at` and if it's not finished then closing balance corresponding to what date is returned by the bank.
	// e.g. if we are enquiring about opening balance on a current date say `14-12-2021` the API returns closing balance of `13-12-2021`
	//
	//	but if we enquire opening balance at `14-12-2021T00:01:00+05:30` and bank EOD process has not finished then the api will return closing balance of `12-12-2021`. Since, `13-12-2021` closing isn't processed.
	//
	// In such cases client should re-enquire and wait until bank EOD is finished and closinf balance corresponding to `13-12-2021` is returned
	LastClosedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=last_closed_at,json=lastClosedAt,proto3" json:"last_closed_at,omitempty"`
	// This field refers to the last date when the last EOD was performed by the bank
	BankEodAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=bank_eod_at,json=bankEodAt,proto3" json:"bank_eod_at,omitempty"`
}

func (x *GetOpeningBalanceResponse) Reset() {
	*x = GetOpeningBalanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOpeningBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOpeningBalanceResponse) ProtoMessage() {}

func (x *GetOpeningBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOpeningBalanceResponse.ProtoReflect.Descriptor instead.
func (*GetOpeningBalanceResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetOpeningBalanceResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetOpeningBalanceResponse) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *GetOpeningBalanceResponse) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

func (x *GetOpeningBalanceResponse) GetOpeningBalance() *money.Money {
	if x != nil {
		return x.OpeningBalance
	}
	return nil
}

func (x *GetOpeningBalanceResponse) GetLastClosedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastClosedAt
	}
	return nil
}

func (x *GetOpeningBalanceResponse) GetBankEodAt() *timestamppb.Timestamp {
	if x != nil {
		return x.BankEodAt
	}
	return nil
}

type GetBalanceV1Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendorgateway APIs.
	Header        *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RequestId     string                       `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	AccountNumber string                       `protobuf:"bytes,3,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
}

func (x *GetBalanceV1Request) Reset() {
	*x = GetBalanceV1Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBalanceV1Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceV1Request) ProtoMessage() {}

func (x *GetBalanceV1Request) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceV1Request.ProtoReflect.Descriptor instead.
func (*GetBalanceV1Request) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetBalanceV1Request) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetBalanceV1Request) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetBalanceV1Request) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

type GetBalanceV1Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Name of the customer
	CustomerName *common.Name `protobuf:"bytes,2,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	// (Available Balance +  Lien Balance + amount in clearance) = Ledger Balance
	LedgerBalance    *money.Money `protobuf:"bytes,3,opt,name=ledger_balance,json=ledgerBalance,proto3" json:"ledger_balance,omitempty"`
	AvailableBalance *money.Money `protobuf:"bytes,4,opt,name=available_balance,json=availableBalance,proto3" json:"available_balance,omitempty"`
	ClearanceBalance *money.Money `protobuf:"bytes,5,opt,name=clearance_balance,json=clearanceBalance,proto3" json:"clearance_balance,omitempty"`
	LienBalance      *money.Money `protobuf:"bytes,6,opt,name=lien_balance,json=lienBalance,proto3" json:"lien_balance,omitempty"`
	// Timestamp for which the given balance was calculated
	BalanceAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=balance_at,json=balanceAt,proto3" json:"balance_at,omitempty"`
	// There can be freeze conditions with respect to an account, freeze status gives a standard code about it
	FreezeStatus string `protobuf:"bytes,8,opt,name=freeze_status,json=freezeStatus,proto3" json:"freeze_status,omitempty"`
	// Reason for the freeze status
	FreezeReason string `protobuf:"bytes,9,opt,name=freeze_reason,json=freezeReason,proto3" json:"freeze_reason,omitempty"`
}

func (x *GetBalanceV1Response) Reset() {
	*x = GetBalanceV1Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBalanceV1Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceV1Response) ProtoMessage() {}

func (x *GetBalanceV1Response) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceV1Response.ProtoReflect.Descriptor instead.
func (*GetBalanceV1Response) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetBalanceV1Response) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetBalanceV1Response) GetCustomerName() *common.Name {
	if x != nil {
		return x.CustomerName
	}
	return nil
}

func (x *GetBalanceV1Response) GetLedgerBalance() *money.Money {
	if x != nil {
		return x.LedgerBalance
	}
	return nil
}

func (x *GetBalanceV1Response) GetAvailableBalance() *money.Money {
	if x != nil {
		return x.AvailableBalance
	}
	return nil
}

func (x *GetBalanceV1Response) GetClearanceBalance() *money.Money {
	if x != nil {
		return x.ClearanceBalance
	}
	return nil
}

func (x *GetBalanceV1Response) GetLienBalance() *money.Money {
	if x != nil {
		return x.LienBalance
	}
	return nil
}

func (x *GetBalanceV1Response) GetBalanceAt() *timestamppb.Timestamp {
	if x != nil {
		return x.BalanceAt
	}
	return nil
}

func (x *GetBalanceV1Response) GetFreezeStatus() string {
	if x != nil {
		return x.FreezeStatus
	}
	return ""
}

func (x *GetBalanceV1Response) GetFreezeReason() string {
	if x != nil {
		return x.FreezeReason
	}
	return ""
}

type UpdateNomineeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendorgateway APIs.
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// ADD / ENQUIRY
	ReqType string `protobuf:"bytes,2,opt,name=req_type,json=reqType,proto3" json:"req_type,omitempty"`
	// unique request id for each request to the vendor,
	// will be used to enquire the status
	ReqId string `protobuf:"bytes,31,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	// Unique identifier (account numberwith sol id)
	Foracid string `protobuf:"bytes,3,opt,name=foracid,proto3" json:"foracid,omitempty"`
	// Unique request id for each requestapi/vendors/federal/update_nominee.proto
	ServiceReqId string `protobuf:"bytes,4,opt,name=service_req_id,json=serviceReqId,proto3" json:"service_req_id,omitempty"`
	// ekyc rrn number received from aadhaar ekyc
	EkycCrrn string `protobuf:"bytes,5,opt,name=ekyc_crrn,json=ekycCrrn,proto3" json:"ekyc_crrn,omitempty"`
	// Nominee name
	NomineeName string `protobuf:"bytes,6,opt,name=nominee_name,json=nomineeName,proto3" json:"nominee_name,omitempty"`
	// Nominee registration number
	NomineeRegNo string `protobuf:"bytes,7,opt,name=nominee_reg_no,json=nomineeRegNo,proto3" json:"nominee_reg_no,omitempty"`
	// Nominee relation to customer
	NomineeRelType string `protobuf:"bytes,8,opt,name=nominee_rel_type,json=nomineeRelType,proto3" json:"nominee_rel_type,omitempty"`
	// Y/N (whetherNominee is a Minor)
	NomineeMinorFlag string `protobuf:"bytes,9,opt,name=nominee_minor_flag,json=nomineeMinorFlag,proto3" json:"nominee_minor_flag,omitempty"`
	// Nominee date of birth
	NomineeDob string `protobuf:"bytes,10,opt,name=nominee_dob,json=nomineeDob,proto3" json:"nominee_dob,omitempty"`
	// Nominee Addr line 1
	NomineeAddrLine1 string `protobuf:"bytes,11,opt,name=nominee_addr_line1,json=nomineeAddrLine1,proto3" json:"nominee_addr_line1,omitempty"`
	// Nominee Addr line 2
	NomineeAddrLine2 string `protobuf:"bytes,12,opt,name=nominee_addr_line2,json=nomineeAddrLine2,proto3" json:"nominee_addr_line2,omitempty"`
	// Nominee Addr line 3
	NomineeAddrLine3 string `protobuf:"bytes,13,opt,name=nominee_addr_line3,json=nomineeAddrLine3,proto3" json:"nominee_addr_line3,omitempty"`
	// Nominee City Code
	NomineeCity string `protobuf:"bytes,14,opt,name=nominee_city,json=nomineeCity,proto3" json:"nominee_city,omitempty"`
	// Nominee state code
	NomineeState string `protobuf:"bytes,15,opt,name=nominee_state,json=nomineeState,proto3" json:"nominee_state,omitempty"`
	// Nominee country code
	NomineeCountry string `protobuf:"bytes,16,opt,name=nominee_country,json=nomineeCountry,proto3" json:"nominee_country,omitempty"`
	// Nominee Pin Code
	NomineePostalCode string `protobuf:"bytes,17,opt,name=nominee_postal_code,json=nomineePostalCode,proto3" json:"nominee_postal_code,omitempty"`
	// if Nominee is Minor
	GuardianCode string `protobuf:"bytes,18,opt,name=guardian_code,json=guardianCode,proto3" json:"guardian_code,omitempty"`
	// if Nominee is minor
	GuardianName string `protobuf:"bytes,19,opt,name=guardian_name,json=guardianName,proto3" json:"guardian_name,omitempty"`
	// to identify which channel
	Channel           string `protobuf:"bytes,20,opt,name=channel,proto3" json:"channel,omitempty"`
	ReserveFreeText1  string `protobuf:"bytes,21,opt,name=reserve_free_text1,json=reserveFreeText1,proto3" json:"reserve_free_text1,omitempty"`
	ReserveFreeText2  string `protobuf:"bytes,22,opt,name=reserve_free_text2,json=reserveFreeText2,proto3" json:"reserve_free_text2,omitempty"`
	ReserveFreeText3  string `protobuf:"bytes,23,opt,name=reserve_free_text3,json=reserveFreeText3,proto3" json:"reserve_free_text3,omitempty"`
	ReserveFreeText4  string `protobuf:"bytes,24,opt,name=reserve_free_text4,json=reserveFreeText4,proto3" json:"reserve_free_text4,omitempty"`
	ReserveFreeText5  string `protobuf:"bytes,25,opt,name=reserve_free_text5,json=reserveFreeText5,proto3" json:"reserve_free_text5,omitempty"`
	ReserveFreeText6  string `protobuf:"bytes,26,opt,name=reserve_free_text6,json=reserveFreeText6,proto3" json:"reserve_free_text6,omitempty"`
	ReserveFreeText7  string `protobuf:"bytes,27,opt,name=reserve_free_text7,json=reserveFreeText7,proto3" json:"reserve_free_text7,omitempty"`
	ReserveFreeTextt8 string `protobuf:"bytes,28,opt,name=reserve_free_textt8,json=reserveFreeTextt8,proto3" json:"reserve_free_textt8,omitempty"`
	ReserveFreeText9  string `protobuf:"bytes,29,opt,name=reserve_free_text9,json=reserveFreeText9,proto3" json:"reserve_free_text9,omitempty"`
	ReserveFreeText10 string `protobuf:"bytes,30,opt,name=reserve_free_text10,json=reserveFreeText10,proto3" json:"reserve_free_text10,omitempty"`
}

func (x *UpdateNomineeRequest) Reset() {
	*x = UpdateNomineeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateNomineeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNomineeRequest) ProtoMessage() {}

func (x *UpdateNomineeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNomineeRequest.ProtoReflect.Descriptor instead.
func (*UpdateNomineeRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateNomineeRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *UpdateNomineeRequest) GetReqType() string {
	if x != nil {
		return x.ReqType
	}
	return ""
}

func (x *UpdateNomineeRequest) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *UpdateNomineeRequest) GetForacid() string {
	if x != nil {
		return x.Foracid
	}
	return ""
}

func (x *UpdateNomineeRequest) GetServiceReqId() string {
	if x != nil {
		return x.ServiceReqId
	}
	return ""
}

func (x *UpdateNomineeRequest) GetEkycCrrn() string {
	if x != nil {
		return x.EkycCrrn
	}
	return ""
}

func (x *UpdateNomineeRequest) GetNomineeName() string {
	if x != nil {
		return x.NomineeName
	}
	return ""
}

func (x *UpdateNomineeRequest) GetNomineeRegNo() string {
	if x != nil {
		return x.NomineeRegNo
	}
	return ""
}

func (x *UpdateNomineeRequest) GetNomineeRelType() string {
	if x != nil {
		return x.NomineeRelType
	}
	return ""
}

func (x *UpdateNomineeRequest) GetNomineeMinorFlag() string {
	if x != nil {
		return x.NomineeMinorFlag
	}
	return ""
}

func (x *UpdateNomineeRequest) GetNomineeDob() string {
	if x != nil {
		return x.NomineeDob
	}
	return ""
}

func (x *UpdateNomineeRequest) GetNomineeAddrLine1() string {
	if x != nil {
		return x.NomineeAddrLine1
	}
	return ""
}

func (x *UpdateNomineeRequest) GetNomineeAddrLine2() string {
	if x != nil {
		return x.NomineeAddrLine2
	}
	return ""
}

func (x *UpdateNomineeRequest) GetNomineeAddrLine3() string {
	if x != nil {
		return x.NomineeAddrLine3
	}
	return ""
}

func (x *UpdateNomineeRequest) GetNomineeCity() string {
	if x != nil {
		return x.NomineeCity
	}
	return ""
}

func (x *UpdateNomineeRequest) GetNomineeState() string {
	if x != nil {
		return x.NomineeState
	}
	return ""
}

func (x *UpdateNomineeRequest) GetNomineeCountry() string {
	if x != nil {
		return x.NomineeCountry
	}
	return ""
}

func (x *UpdateNomineeRequest) GetNomineePostalCode() string {
	if x != nil {
		return x.NomineePostalCode
	}
	return ""
}

func (x *UpdateNomineeRequest) GetGuardianCode() string {
	if x != nil {
		return x.GuardianCode
	}
	return ""
}

func (x *UpdateNomineeRequest) GetGuardianName() string {
	if x != nil {
		return x.GuardianName
	}
	return ""
}

func (x *UpdateNomineeRequest) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *UpdateNomineeRequest) GetReserveFreeText1() string {
	if x != nil {
		return x.ReserveFreeText1
	}
	return ""
}

func (x *UpdateNomineeRequest) GetReserveFreeText2() string {
	if x != nil {
		return x.ReserveFreeText2
	}
	return ""
}

func (x *UpdateNomineeRequest) GetReserveFreeText3() string {
	if x != nil {
		return x.ReserveFreeText3
	}
	return ""
}

func (x *UpdateNomineeRequest) GetReserveFreeText4() string {
	if x != nil {
		return x.ReserveFreeText4
	}
	return ""
}

func (x *UpdateNomineeRequest) GetReserveFreeText5() string {
	if x != nil {
		return x.ReserveFreeText5
	}
	return ""
}

func (x *UpdateNomineeRequest) GetReserveFreeText6() string {
	if x != nil {
		return x.ReserveFreeText6
	}
	return ""
}

func (x *UpdateNomineeRequest) GetReserveFreeText7() string {
	if x != nil {
		return x.ReserveFreeText7
	}
	return ""
}

func (x *UpdateNomineeRequest) GetReserveFreeTextt8() string {
	if x != nil {
		return x.ReserveFreeTextt8
	}
	return ""
}

func (x *UpdateNomineeRequest) GetReserveFreeText9() string {
	if x != nil {
		return x.ReserveFreeText9
	}
	return ""
}

func (x *UpdateNomineeRequest) GetReserveFreeText10() string {
	if x != nil {
		return x.ReserveFreeText10
	}
	return ""
}

type UpdateNomineeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	// Request Id which was sent to vendor
	RequestId   string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	CbsResponse string `protobuf:"bytes,2,opt,name=cbs_response,json=cbsResponse,proto3" json:"cbs_response,omitempty"`
	CbsStatus   string `protobuf:"bytes,3,opt,name=cbs_status,json=cbsStatus,proto3" json:"cbs_status,omitempty"`
}

func (x *UpdateNomineeResponse) Reset() {
	*x = UpdateNomineeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateNomineeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNomineeResponse) ProtoMessage() {}

func (x *UpdateNomineeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNomineeResponse.ProtoReflect.Descriptor instead.
func (*UpdateNomineeResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateNomineeResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateNomineeResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *UpdateNomineeResponse) GetCbsResponse() string {
	if x != nil {
		return x.CbsResponse
	}
	return ""
}

func (x *UpdateNomineeResponse) GetCbsStatus() string {
	if x != nil {
		return x.CbsStatus
	}
	return ""
}

var File_api_vendorgateway_openbanking_savings_service_proto protoreflect.FileDescriptor

var file_api_vendorgateway_openbanking_savings_service_proto_rawDesc = []byte{
	0x0a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6e, 0x6f, 0x6d, 0x69, 0x6e,
	0x65, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6f, 0x66, 0x5f,
	0x66, 0x75, 0x6e, 0x64, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc0, 0x06, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x61,
	0x6e, 0x6b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x4d, 0x0a, 0x0e,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x0d, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x12,
	0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x6f, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x07, 0x6e, 0x6f, 0x6d, 0x69, 0x6e,
	0x65, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x52, 0x07,
	0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x12, 0x4c, 0x0a, 0x09, 0x6b, 0x79, 0x63, 0x5f, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62,
	0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x4b,
	0x59, 0x43, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x6b, 0x79, 0x63,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f,
	0x73, 0x6b, 0x75, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x53, 0x6b, 0x75, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x6f, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x73, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4f, 0x66, 0x46, 0x75, 0x6e, 0x64, 0x73,
	0x52, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4f, 0x66, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x12,
	0x29, 0x0a, 0x10, 0x73, 0x63, 0x68, 0x6f, 0x6c, 0x61, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x66,
	0x6c, 0x61, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x73, 0x63, 0x68, 0x6f, 0x6c,
	0x61, 0x72, 0x73, 0x68, 0x69, 0x70, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x62,
	0x74, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x62,
	0x74, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x3d, 0x0a, 0x11, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f,
	0x74, 0x78, 0x6e, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x0f, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x54, 0x78, 0x6e, 0x56, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x12, 0x61, 0x0a, 0x1a, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x5f,
	0x6f, 0x66, 0x5f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x4f, 0x66,
	0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x17,
	0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x4f, 0x66, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x98, 0x03, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x97, 0x02, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x64, 0x12,
	0x1a, 0x0a, 0x16, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x45,
	0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x66, 0x12, 0x16, 0x0a, 0x12, 0x53,
	0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45,
	0x44, 0x10, 0x67, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x44, 0x10, 0x68, 0x12, 0x10, 0x0a, 0x0c,
	0x4e, 0x4f, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x44, 0x10, 0x69, 0x12, 0x15,
	0x0a, 0x11, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x10, 0x6a, 0x12, 0x12, 0x0a, 0x0e, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x6b, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x10, 0x6c, 0x12, 0x19, 0x0a, 0x15, 0x53,
	0x42, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f,
	0x43, 0x45, 0x53, 0x53, 0x10, 0x6d, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x10, 0x6e, 0x12, 0x10, 0x0a,
	0x0c, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x6f, 0x12,
	0x11, 0x0a, 0x0d, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x49, 0x4e, 0x50, 0x55, 0x54,
	0x10, 0x70, 0x22, 0x83, 0x02, 0x0a, 0x19, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x4d, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x0d, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xb9, 0x04, 0x0a, 0x1a, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x40,
	0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0xd1, 0x02, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x64, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x55, 0x50, 0x4c, 0x49,
	0x43, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x44, 0x10, 0x66,
	0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x44,
	0x10, 0x67, 0x12, 0x1a, 0x0a, 0x16, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x45,
	0x4e, 0x44, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x68, 0x12, 0x16,
	0x0a, 0x12, 0x53, 0x45, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x41,
	0x42, 0x4c, 0x45, 0x44, 0x10, 0x69, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x4e, 0x53, 0x45, 0x52, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x6a, 0x12,
	0x0d, 0x0a, 0x09, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x6b, 0x12, 0x13,
	0x0a, 0x0f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x6c, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x42, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x10, 0x6d, 0x12, 0x10,
	0x0a, 0x0c, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x6e,
	0x12, 0x14, 0x0a, 0x10, 0x53, 0x4f, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x6f, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x70, 0x12, 0x18, 0x0a,
	0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f,
	0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x71, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x41, 0x56, 0x49, 0x4e,
	0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55,
	0x52, 0x45, 0x10, 0x72, 0x22, 0x8f, 0x02, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x3a, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xe9, 0x02, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x3f, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x5f, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0d, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x39, 0x0a, 0x0a, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x74, 0x22, 0x34, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x22,
	0x0a, 0x1e, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4f, 0x52, 0x41,
	0x52, 0x49, 0x4c, 0x59, 0x5f, 0x44, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x44,
	0x10, 0x64, 0x22, 0xc4, 0x03, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x6e,
	0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x41, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x1d, 0x69, 0x73, 0x5f, 0x63, 0x6c,
	0x6f, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19,
	0x69, 0x73, 0x43, 0x6c, 0x6f, 0x73, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0xfb, 0x02, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0f, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x6e,
	0x67, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0e, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x40, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x63, 0x6c, 0x6f, 0x73,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x43, 0x6c, 0x6f,
	0x73, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3a, 0x0a, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x65, 0x6f,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x45, 0x6f, 0x64, 0x41,
	0x74, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x45,
	0x4d, 0x50, 0x4f, 0x52, 0x41, 0x52, 0x49, 0x4c, 0x59, 0x5f, 0x44, 0x45, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x41, 0x54, 0x45, 0x44, 0x10, 0x65, 0x22, 0x91, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x85, 0x04, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x72, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x11, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x10, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x11, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x10, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x35, 0x0a, 0x0c, 0x6c, 0x69, 0x65, 0x6e, 0x5f, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0b, 0x6c, 0x69, 0x65, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x0a,
	0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x72, 0x65, 0x65, 0x7a,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x22, 0x10, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x22, 0xfc, 0x09, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f,
	0x6d, 0x69, 0x6e, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x71, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a,
	0x06, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72,
	0x65, 0x71, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x6f, 0x72, 0x61, 0x63, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x6f, 0x72, 0x61, 0x63, 0x69, 0x64, 0x12, 0x24,
	0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6b, 0x79, 0x63, 0x5f, 0x63, 0x72, 0x72,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6b, 0x79, 0x63, 0x43, 0x72, 0x72,
	0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f,
	0x72, 0x65, 0x67, 0x5f, 0x6e, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x6f,
	0x6d, 0x69, 0x6e, 0x65, 0x65, 0x52, 0x65, 0x67, 0x4e, 0x6f, 0x12, 0x28, 0x0a, 0x10, 0x6e, 0x6f,
	0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x52, 0x65, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f,
	0x6d, 0x69, 0x6e, 0x6f, 0x72, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x4d, 0x69, 0x6e, 0x6f, 0x72, 0x46, 0x6c,
	0x61, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x64, 0x6f,
	0x62, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65,
	0x44, 0x6f, 0x62, 0x12, 0x2c, 0x0a, 0x12, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x41, 0x64, 0x64, 0x72, 0x4c, 0x69, 0x6e, 0x65,
	0x31, 0x12, 0x2c, 0x0a, 0x12, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6e,
	0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x41, 0x64, 0x64, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x32, 0x12,
	0x2c, 0x0a, 0x12, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x5f,
	0x6c, 0x69, 0x6e, 0x65, 0x33, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6e, 0x6f, 0x6d,
	0x69, 0x6e, 0x65, 0x65, 0x41, 0x64, 0x64, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x33, 0x12, 0x21, 0x0a,
	0x0c, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x43, 0x69, 0x74, 0x79,
	0x12, 0x23, 0x0a, 0x0d, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x2e,
	0x0a, 0x13, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6e, 0x6f, 0x6d,
	0x69, 0x6e, 0x65, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x67, 0x75, 0x61, 0x72,
	0x64, 0x69, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x5f, 0x66, 0x72,
	0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x31, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x46, 0x72, 0x65, 0x65, 0x54, 0x65, 0x78, 0x74, 0x31,
	0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x5f, 0x66, 0x72, 0x65, 0x65,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x32, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x46, 0x72, 0x65, 0x65, 0x54, 0x65, 0x78, 0x74, 0x32, 0x12, 0x2c,
	0x0a, 0x12, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x33, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x46, 0x72, 0x65, 0x65, 0x54, 0x65, 0x78, 0x74, 0x33, 0x12, 0x2c, 0x0a, 0x12,
	0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x34, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x46, 0x72, 0x65, 0x65, 0x54, 0x65, 0x78, 0x74, 0x34, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x35,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x46,
	0x72, 0x65, 0x65, 0x54, 0x65, 0x78, 0x74, 0x35, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x36, 0x18, 0x1a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x46, 0x72, 0x65,
	0x65, 0x54, 0x65, 0x78, 0x74, 0x36, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x5f, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x37, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x46, 0x72, 0x65, 0x65, 0x54,
	0x65, 0x78, 0x74, 0x37, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x5f,
	0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x74, 0x38, 0x18, 0x1c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x46, 0x72, 0x65, 0x65, 0x54, 0x65,
	0x78, 0x74, 0x74, 0x38, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x5f,
	0x66, 0x72, 0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x39, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x46, 0x72, 0x65, 0x65, 0x54, 0x65, 0x78,
	0x74, 0x39, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x5f, 0x66, 0x72,
	0x65, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x31, 0x30, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x46, 0x72, 0x65, 0x65, 0x54, 0x65, 0x78, 0x74,
	0x31, 0x30, 0x22, 0x9d, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x6d,
	0x69, 0x6e, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x62, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x62, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x62, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x2a, 0x36, 0x0a, 0x08, 0x4b, 0x59, 0x43, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x0f,
	0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0b, 0x0a, 0x07, 0x4d, 0x49, 0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08,
	0x46, 0x55, 0x4c, 0x4c, 0x5f, 0x4b, 0x59, 0x43, 0x10, 0x02, 0x32, 0xb5, 0x06, 0x0a, 0x07, 0x53,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x82, 0x01, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x37, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x38, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x61,
	0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x91, 0x01, 0x0a, 0x12,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x3c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x73,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x61, 0x76,
	0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x79, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x34, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8e, 0x01, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x3b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x61, 0x76,
	0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x0c, 0x47,
	0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31, 0x12, 0x36, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x56, 0x31, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x56, 0x31, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x83, 0x01, 0x0a,
	0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x73, 0x12,
	0x37, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x61, 0x76, 0x69,
	0x6e, 0x67, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x7c, 0x0a, 0x3c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e,
	0x67, 0x73, 0x5a, 0x3c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65,
	0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_openbanking_savings_service_proto_rawDescOnce sync.Once
	file_api_vendorgateway_openbanking_savings_service_proto_rawDescData = file_api_vendorgateway_openbanking_savings_service_proto_rawDesc
)

func file_api_vendorgateway_openbanking_savings_service_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_openbanking_savings_service_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_openbanking_savings_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_openbanking_savings_service_proto_rawDescData)
	})
	return file_api_vendorgateway_openbanking_savings_service_proto_rawDescData
}

var file_api_vendorgateway_openbanking_savings_service_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_vendorgateway_openbanking_savings_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_api_vendorgateway_openbanking_savings_service_proto_goTypes = []interface{}{
	(KYCLevel)(0),                          // 0: vendorgateway.openbanking.savings.KYCLevel
	(CreateAccountResponse_Status)(0),      // 1: vendorgateway.openbanking.savings.CreateAccountResponse.Status
	(CheckAccountStatusResponse_Status)(0), // 2: vendorgateway.openbanking.savings.CheckAccountStatusResponse.Status
	(GetBalanceResponse_Status)(0),         // 3: vendorgateway.openbanking.savings.GetBalanceResponse.Status
	(GetOpeningBalanceResponse_Status)(0),  // 4: vendorgateway.openbanking.savings.GetOpeningBalanceResponse.Status
	(GetBalanceV1Response_Status)(0),       // 5: vendorgateway.openbanking.savings.GetBalanceV1Response.Status
	(*CreateAccountRequest)(nil),           // 6: vendorgateway.openbanking.savings.CreateAccountRequest
	(*CreateAccountResponse)(nil),          // 7: vendorgateway.openbanking.savings.CreateAccountResponse
	(*CheckAccountStatusRequest)(nil),      // 8: vendorgateway.openbanking.savings.CheckAccountStatusRequest
	(*CheckAccountStatusResponse)(nil),     // 9: vendorgateway.openbanking.savings.CheckAccountStatusResponse
	(*GetBalanceRequest)(nil),              // 10: vendorgateway.openbanking.savings.GetBalanceRequest
	(*GetBalanceResponse)(nil),             // 11: vendorgateway.openbanking.savings.GetBalanceResponse
	(*GetOpeningBalanceRequest)(nil),       // 12: vendorgateway.openbanking.savings.GetOpeningBalanceRequest
	(*GetOpeningBalanceResponse)(nil),      // 13: vendorgateway.openbanking.savings.GetOpeningBalanceResponse
	(*GetBalanceV1Request)(nil),            // 14: vendorgateway.openbanking.savings.GetBalanceV1Request
	(*GetBalanceV1Response)(nil),           // 15: vendorgateway.openbanking.savings.GetBalanceV1Response
	(*UpdateNomineeRequest)(nil),           // 16: vendorgateway.openbanking.savings.UpdateNomineeRequest
	(*UpdateNomineeResponse)(nil),          // 17: vendorgateway.openbanking.savings.UpdateNomineeResponse
	(*vendorgateway.RequestHeader)(nil),    // 18: vendorgateway.RequestHeader
	(*header.Auth)(nil),                    // 19: vendorgateway.openbanking.header.Auth
	(*typesv2.Nominee)(nil),                // 20: api.typesv2.Nominee
	(typesv2.SourceOfFunds)(0),             // 21: api.typesv2.SourceOfFunds
	(*user.SalaryRange)(nil),               // 22: user.SalaryRange
	(typesv2.PurposeOfSavingsAccount)(0),   // 23: api.typesv2.PurposeOfSavingsAccount
	(*rpc.Status)(nil),                     // 24: rpc.Status
	(*vendorgateway.VendorStatus)(nil),     // 25: vendorgateway.VendorStatus
	(*common.PhoneNumber)(nil),             // 26: api.typesv2.common.PhoneNumber
	(*timestamppb.Timestamp)(nil),          // 27: google.protobuf.Timestamp
	(*money.Money)(nil),                    // 28: google.type.Money
	(*date.Date)(nil),                      // 29: google.type.Date
	(*common.Name)(nil),                    // 30: api.typesv2.common.Name
}
var file_api_vendorgateway_openbanking_savings_service_proto_depIdxs = []int32{
	18, // 0: vendorgateway.openbanking.savings.CreateAccountRequest.header:type_name -> vendorgateway.RequestHeader
	19, // 1: vendorgateway.openbanking.savings.CreateAccountRequest.device_details:type_name -> vendorgateway.openbanking.header.Auth
	20, // 2: vendorgateway.openbanking.savings.CreateAccountRequest.nominee:type_name -> api.typesv2.Nominee
	0,  // 3: vendorgateway.openbanking.savings.CreateAccountRequest.kyc_level:type_name -> vendorgateway.openbanking.savings.KYCLevel
	21, // 4: vendorgateway.openbanking.savings.CreateAccountRequest.source_of_funds:type_name -> api.typesv2.SourceOfFunds
	22, // 5: vendorgateway.openbanking.savings.CreateAccountRequest.annual_txn_volume:type_name -> user.SalaryRange
	23, // 6: vendorgateway.openbanking.savings.CreateAccountRequest.purpose_of_savings_account:type_name -> api.typesv2.PurposeOfSavingsAccount
	24, // 7: vendorgateway.openbanking.savings.CreateAccountResponse.status:type_name -> rpc.Status
	25, // 8: vendorgateway.openbanking.savings.CreateAccountResponse.vendor_status:type_name -> vendorgateway.VendorStatus
	18, // 9: vendorgateway.openbanking.savings.CheckAccountStatusRequest.header:type_name -> vendorgateway.RequestHeader
	19, // 10: vendorgateway.openbanking.savings.CheckAccountStatusRequest.device_details:type_name -> vendorgateway.openbanking.header.Auth
	26, // 11: vendorgateway.openbanking.savings.CheckAccountStatusRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	24, // 12: vendorgateway.openbanking.savings.CheckAccountStatusResponse.status:type_name -> rpc.Status
	27, // 13: vendorgateway.openbanking.savings.CheckAccountStatusResponse.created_at:type_name -> google.protobuf.Timestamp
	25, // 14: vendorgateway.openbanking.savings.CheckAccountStatusResponse.vendor_status:type_name -> vendorgateway.VendorStatus
	18, // 15: vendorgateway.openbanking.savings.GetBalanceRequest.header:type_name -> vendorgateway.RequestHeader
	19, // 16: vendorgateway.openbanking.savings.GetBalanceRequest.auth:type_name -> vendorgateway.openbanking.header.Auth
	26, // 17: vendorgateway.openbanking.savings.GetBalanceRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	24, // 18: vendorgateway.openbanking.savings.GetBalanceResponse.status:type_name -> rpc.Status
	28, // 19: vendorgateway.openbanking.savings.GetBalanceResponse.available_balance:type_name -> google.type.Money
	28, // 20: vendorgateway.openbanking.savings.GetBalanceResponse.ledger_balance:type_name -> google.type.Money
	27, // 21: vendorgateway.openbanking.savings.GetBalanceResponse.balance_at:type_name -> google.protobuf.Timestamp
	18, // 22: vendorgateway.openbanking.savings.GetOpeningBalanceRequest.header:type_name -> vendorgateway.RequestHeader
	29, // 23: vendorgateway.openbanking.savings.GetOpeningBalanceRequest.date:type_name -> google.type.Date
	27, // 24: vendorgateway.openbanking.savings.GetOpeningBalanceRequest.balance_at:type_name -> google.protobuf.Timestamp
	26, // 25: vendorgateway.openbanking.savings.GetOpeningBalanceRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	24, // 26: vendorgateway.openbanking.savings.GetOpeningBalanceResponse.status:type_name -> rpc.Status
	28, // 27: vendorgateway.openbanking.savings.GetOpeningBalanceResponse.opening_balance:type_name -> google.type.Money
	27, // 28: vendorgateway.openbanking.savings.GetOpeningBalanceResponse.last_closed_at:type_name -> google.protobuf.Timestamp
	27, // 29: vendorgateway.openbanking.savings.GetOpeningBalanceResponse.bank_eod_at:type_name -> google.protobuf.Timestamp
	18, // 30: vendorgateway.openbanking.savings.GetBalanceV1Request.header:type_name -> vendorgateway.RequestHeader
	24, // 31: vendorgateway.openbanking.savings.GetBalanceV1Response.status:type_name -> rpc.Status
	30, // 32: vendorgateway.openbanking.savings.GetBalanceV1Response.customer_name:type_name -> api.typesv2.common.Name
	28, // 33: vendorgateway.openbanking.savings.GetBalanceV1Response.ledger_balance:type_name -> google.type.Money
	28, // 34: vendorgateway.openbanking.savings.GetBalanceV1Response.available_balance:type_name -> google.type.Money
	28, // 35: vendorgateway.openbanking.savings.GetBalanceV1Response.clearance_balance:type_name -> google.type.Money
	28, // 36: vendorgateway.openbanking.savings.GetBalanceV1Response.lien_balance:type_name -> google.type.Money
	27, // 37: vendorgateway.openbanking.savings.GetBalanceV1Response.balance_at:type_name -> google.protobuf.Timestamp
	18, // 38: vendorgateway.openbanking.savings.UpdateNomineeRequest.header:type_name -> vendorgateway.RequestHeader
	24, // 39: vendorgateway.openbanking.savings.UpdateNomineeResponse.status:type_name -> rpc.Status
	6,  // 40: vendorgateway.openbanking.savings.Savings.CreateAccount:input_type -> vendorgateway.openbanking.savings.CreateAccountRequest
	8,  // 41: vendorgateway.openbanking.savings.Savings.CheckAccountStatus:input_type -> vendorgateway.openbanking.savings.CheckAccountStatusRequest
	10, // 42: vendorgateway.openbanking.savings.Savings.GetBalance:input_type -> vendorgateway.openbanking.savings.GetBalanceRequest
	12, // 43: vendorgateway.openbanking.savings.Savings.GetOpeningBalance:input_type -> vendorgateway.openbanking.savings.GetOpeningBalanceRequest
	14, // 44: vendorgateway.openbanking.savings.Savings.GetBalanceV1:input_type -> vendorgateway.openbanking.savings.GetBalanceV1Request
	16, // 45: vendorgateway.openbanking.savings.Savings.UpdateNominees:input_type -> vendorgateway.openbanking.savings.UpdateNomineeRequest
	7,  // 46: vendorgateway.openbanking.savings.Savings.CreateAccount:output_type -> vendorgateway.openbanking.savings.CreateAccountResponse
	9,  // 47: vendorgateway.openbanking.savings.Savings.CheckAccountStatus:output_type -> vendorgateway.openbanking.savings.CheckAccountStatusResponse
	11, // 48: vendorgateway.openbanking.savings.Savings.GetBalance:output_type -> vendorgateway.openbanking.savings.GetBalanceResponse
	13, // 49: vendorgateway.openbanking.savings.Savings.GetOpeningBalance:output_type -> vendorgateway.openbanking.savings.GetOpeningBalanceResponse
	15, // 50: vendorgateway.openbanking.savings.Savings.GetBalanceV1:output_type -> vendorgateway.openbanking.savings.GetBalanceV1Response
	17, // 51: vendorgateway.openbanking.savings.Savings.UpdateNominees:output_type -> vendorgateway.openbanking.savings.UpdateNomineeResponse
	46, // [46:52] is the sub-list for method output_type
	40, // [40:46] is the sub-list for method input_type
	40, // [40:40] is the sub-list for extension type_name
	40, // [40:40] is the sub-list for extension extendee
	0,  // [0:40] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_openbanking_savings_service_proto_init() }
func file_api_vendorgateway_openbanking_savings_service_proto_init() {
	if File_api_vendorgateway_openbanking_savings_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAccountStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAccountStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBalanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBalanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOpeningBalanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOpeningBalanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBalanceV1Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBalanceV1Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateNomineeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_savings_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateNomineeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_openbanking_savings_service_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendorgateway_openbanking_savings_service_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_openbanking_savings_service_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_openbanking_savings_service_proto_enumTypes,
		MessageInfos:      file_api_vendorgateway_openbanking_savings_service_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_openbanking_savings_service_proto = out.File
	file_api_vendorgateway_openbanking_savings_service_proto_rawDesc = nil
	file_api_vendorgateway_openbanking_savings_service_proto_goTypes = nil
	file_api_vendorgateway_openbanking_savings_service_proto_depIdxs = nil
}
