// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/openbanking/dispute/enums.proto

package dispute

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// this indicates whether the dispute is normal or unauthorized
// we received these values from Federal DMP doc
type DisputeType int32

const (
	DisputeType_DISPUTE_TYPE_UNSPECIFIED   DisputeType = 0
	DisputeType_DISPUTE_TYPE_NORMAL        DisputeType = 1
	DisputeType_DISPUTE_TYPE_UN_AUTHORISED DisputeType = 2
)

// Enum value maps for DisputeType.
var (
	DisputeType_name = map[int32]string{
		0: "DISPUTE_TYPE_UNSPECIFIED",
		1: "DISPUTE_TYPE_NORMAL",
		2: "DISPUTE_TYPE_UN_AUTHORISED",
	}
	DisputeType_value = map[string]int32{
		"DISPUTE_TYPE_UNSPECIFIED":   0,
		"DISPUTE_TYPE_NORMAL":        1,
		"DISPUTE_TYPE_UN_AUTHORISED": 2,
	}
)

func (x DisputeType) Enum() *DisputeType {
	p := new(DisputeType)
	*p = x
	return p
}

func (x DisputeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisputeType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes[0].Descriptor()
}

func (DisputeType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes[0]
}

func (x DisputeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisputeType.Descriptor instead.
func (DisputeType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_dispute_enums_proto_rawDescGZIP(), []int{0}
}

// transaction channel
type Channel int32

const (
	Channel_CHANNEL_UNSPECIFIED     Channel = 0
	Channel_CHANNEL_UPI             Channel = 1
	Channel_CHANNEL_IMPS            Channel = 2
	Channel_CHANNEL_NEFT            Channel = 3
	Channel_CHANNEL_RTGS            Channel = 4
	Channel_CHANNEL_DEBIT_CARD_ATM  Channel = 5
	Channel_CHANNEL_DEBIT_CARD_POS  Channel = 6
	Channel_CHANNEL_DEBIT_CARD_ECOM Channel = 7
	Channel_CHANNEL_CHEQUE          Channel = 8
	Channel_CHANNEL_INTRA_BANK      Channel = 9
	Channel_CHANNEL_CARD            Channel = 10
)

// Enum value maps for Channel.
var (
	Channel_name = map[int32]string{
		0:  "CHANNEL_UNSPECIFIED",
		1:  "CHANNEL_UPI",
		2:  "CHANNEL_IMPS",
		3:  "CHANNEL_NEFT",
		4:  "CHANNEL_RTGS",
		5:  "CHANNEL_DEBIT_CARD_ATM",
		6:  "CHANNEL_DEBIT_CARD_POS",
		7:  "CHANNEL_DEBIT_CARD_ECOM",
		8:  "CHANNEL_CHEQUE",
		9:  "CHANNEL_INTRA_BANK",
		10: "CHANNEL_CARD",
	}
	Channel_value = map[string]int32{
		"CHANNEL_UNSPECIFIED":     0,
		"CHANNEL_UPI":             1,
		"CHANNEL_IMPS":            2,
		"CHANNEL_NEFT":            3,
		"CHANNEL_RTGS":            4,
		"CHANNEL_DEBIT_CARD_ATM":  5,
		"CHANNEL_DEBIT_CARD_POS":  6,
		"CHANNEL_DEBIT_CARD_ECOM": 7,
		"CHANNEL_CHEQUE":          8,
		"CHANNEL_INTRA_BANK":      9,
		"CHANNEL_CARD":            10,
	}
)

func (x Channel) Enum() *Channel {
	p := new(Channel)
	*p = x
	return p
}

func (x Channel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Channel) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes[1].Descriptor()
}

func (Channel) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes[1]
}

func (x Channel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Channel.Descriptor instead.
func (Channel) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_dispute_enums_proto_rawDescGZIP(), []int{1}
}

// dispute status describes current status of the dispute
type DisputeStatus int32

const (
	DisputeStatus_DISPUTE_STATUS_UNSPECIFIED DisputeStatus = 0
	DisputeStatus_DISPUTE_STATUS_OPEN        DisputeStatus = 1
	DisputeStatus_DISPUTE_STATUS_CLOSED      DisputeStatus = 2
)

// Enum value maps for DisputeStatus.
var (
	DisputeStatus_name = map[int32]string{
		0: "DISPUTE_STATUS_UNSPECIFIED",
		1: "DISPUTE_STATUS_OPEN",
		2: "DISPUTE_STATUS_CLOSED",
	}
	DisputeStatus_value = map[string]int32{
		"DISPUTE_STATUS_UNSPECIFIED": 0,
		"DISPUTE_STATUS_OPEN":        1,
		"DISPUTE_STATUS_CLOSED":      2,
	}
)

func (x DisputeStatus) Enum() *DisputeStatus {
	p := new(DisputeStatus)
	*p = x
	return p
}

func (x DisputeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisputeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes[2].Descriptor()
}

func (DisputeStatus) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes[2]
}

func (x DisputeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisputeStatus.Descriptor instead.
func (DisputeStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_dispute_enums_proto_rawDescGZIP(), []int{2}
}

// The workflow status of the transaction.
type WorkFlowStatus int32

const (
	WorkFlowStatus_WORK_FLOW_STATUS_UNSPECIFIED                   WorkFlowStatus = 0
	WorkFlowStatus_WORK_FLOW_STATUS_OPENED                        WorkFlowStatus = 1
	WorkFlowStatus_WORK_FLOW_STATUS_OPENED_FOR_CHARGEBACK         WorkFlowStatus = 2
	WorkFlowStatus_WORK_FLOW_STATUS_INITIATED_FOR_CHARGE_BACK     WorkFlowStatus = 3
	WorkFlowStatus_WORK_FLOW_STATUS_RAISED_FOR_CHARGE_BACK        WorkFlowStatus = 4
	WorkFlowStatus_WORK_FLOW_STATUS_CHARGEBACK_APPROVED           WorkFlowStatus = 5
	WorkFlowStatus_WORK_FLOW_STATUS_CHARGEBACK_REJECTED           WorkFlowStatus = 6
	WorkFlowStatus_WORK_FLOW_STATUS_OPENED_FOR_PRE_ARBITRATION    WorkFlowStatus = 7
	WorkFlowStatus_WORK_FLOW_STATUS_INITIATED_FOR_PRE_ARBITRATION WorkFlowStatus = 8
	WorkFlowStatus_WORK_FLOW_STATUS_RAISED_FOR_PRE_ARBITRATION    WorkFlowStatus = 9
	WorkFlowStatus_WORK_FLOW_STATUS_PRE_ARBITRATION_APPROVED      WorkFlowStatus = 10
	WorkFlowStatus_WORK_FLOW_STATUS_PRE_ARBITRATION_REJECTED      WorkFlowStatus = 11
	WorkFlowStatus_WORK_FLOW_STATUS_CLOSED                        WorkFlowStatus = 12
	WorkFlowStatus_WORK_FLOW_STATUS_AWAITING_RESPONSE_FROM_BRANCH WorkFlowStatus = 13
	WorkFlowStatus_WORK_FLOW_STATUS_DISPUTE_REGISTERED            WorkFlowStatus = 14
	WorkFlowStatus_WORK_FLOW_STATUS_LIABILITY_COMPUTED            WorkFlowStatus = 15
	WorkFlowStatus_WORK_FLOW_STATUS_LIABILITY_REJECTED            WorkFlowStatus = 16
	WorkFlowStatus_WORK_FLOW_STATUS_LIABILITY_APPROVED            WorkFlowStatus = 17
	WorkFlowStatus_WORK_FLOW_STATUS_RECOMMENDED_FOR_ACCEPTING     WorkFlowStatus = 18
	WorkFlowStatus_WORK_FLOW_STATUS_RECHECK_F                     WorkFlowStatus = 19
	WorkFlowStatus_WORK_FLOW_STATUS_ACCEPTED                      WorkFlowStatus = 20
	WorkFlowStatus_WORK_FLOW_STATUS_RECOMMENDED_FOR_REJECTING     WorkFlowStatus = 21
	WorkFlowStatus_WORK_FLOW_STATUS_RECHECK_NF                    WorkFlowStatus = 22
	WorkFlowStatus_WORK_FLOW_STATUS_REJECTED                      WorkFlowStatus = 23
	WorkFlowStatus_WORK_FLOW_STATUS_RAISE_CHARGE_BACK             WorkFlowStatus = 24
)

// Enum value maps for WorkFlowStatus.
var (
	WorkFlowStatus_name = map[int32]string{
		0:  "WORK_FLOW_STATUS_UNSPECIFIED",
		1:  "WORK_FLOW_STATUS_OPENED",
		2:  "WORK_FLOW_STATUS_OPENED_FOR_CHARGEBACK",
		3:  "WORK_FLOW_STATUS_INITIATED_FOR_CHARGE_BACK",
		4:  "WORK_FLOW_STATUS_RAISED_FOR_CHARGE_BACK",
		5:  "WORK_FLOW_STATUS_CHARGEBACK_APPROVED",
		6:  "WORK_FLOW_STATUS_CHARGEBACK_REJECTED",
		7:  "WORK_FLOW_STATUS_OPENED_FOR_PRE_ARBITRATION",
		8:  "WORK_FLOW_STATUS_INITIATED_FOR_PRE_ARBITRATION",
		9:  "WORK_FLOW_STATUS_RAISED_FOR_PRE_ARBITRATION",
		10: "WORK_FLOW_STATUS_PRE_ARBITRATION_APPROVED",
		11: "WORK_FLOW_STATUS_PRE_ARBITRATION_REJECTED",
		12: "WORK_FLOW_STATUS_CLOSED",
		13: "WORK_FLOW_STATUS_AWAITING_RESPONSE_FROM_BRANCH",
		14: "WORK_FLOW_STATUS_DISPUTE_REGISTERED",
		15: "WORK_FLOW_STATUS_LIABILITY_COMPUTED",
		16: "WORK_FLOW_STATUS_LIABILITY_REJECTED",
		17: "WORK_FLOW_STATUS_LIABILITY_APPROVED",
		18: "WORK_FLOW_STATUS_RECOMMENDED_FOR_ACCEPTING",
		19: "WORK_FLOW_STATUS_RECHECK_F",
		20: "WORK_FLOW_STATUS_ACCEPTED",
		21: "WORK_FLOW_STATUS_RECOMMENDED_FOR_REJECTING",
		22: "WORK_FLOW_STATUS_RECHECK_NF",
		23: "WORK_FLOW_STATUS_REJECTED",
		24: "WORK_FLOW_STATUS_RAISE_CHARGE_BACK",
	}
	WorkFlowStatus_value = map[string]int32{
		"WORK_FLOW_STATUS_UNSPECIFIED":                   0,
		"WORK_FLOW_STATUS_OPENED":                        1,
		"WORK_FLOW_STATUS_OPENED_FOR_CHARGEBACK":         2,
		"WORK_FLOW_STATUS_INITIATED_FOR_CHARGE_BACK":     3,
		"WORK_FLOW_STATUS_RAISED_FOR_CHARGE_BACK":        4,
		"WORK_FLOW_STATUS_CHARGEBACK_APPROVED":           5,
		"WORK_FLOW_STATUS_CHARGEBACK_REJECTED":           6,
		"WORK_FLOW_STATUS_OPENED_FOR_PRE_ARBITRATION":    7,
		"WORK_FLOW_STATUS_INITIATED_FOR_PRE_ARBITRATION": 8,
		"WORK_FLOW_STATUS_RAISED_FOR_PRE_ARBITRATION":    9,
		"WORK_FLOW_STATUS_PRE_ARBITRATION_APPROVED":      10,
		"WORK_FLOW_STATUS_PRE_ARBITRATION_REJECTED":      11,
		"WORK_FLOW_STATUS_CLOSED":                        12,
		"WORK_FLOW_STATUS_AWAITING_RESPONSE_FROM_BRANCH": 13,
		"WORK_FLOW_STATUS_DISPUTE_REGISTERED":            14,
		"WORK_FLOW_STATUS_LIABILITY_COMPUTED":            15,
		"WORK_FLOW_STATUS_LIABILITY_REJECTED":            16,
		"WORK_FLOW_STATUS_LIABILITY_APPROVED":            17,
		"WORK_FLOW_STATUS_RECOMMENDED_FOR_ACCEPTING":     18,
		"WORK_FLOW_STATUS_RECHECK_F":                     19,
		"WORK_FLOW_STATUS_ACCEPTED":                      20,
		"WORK_FLOW_STATUS_RECOMMENDED_FOR_REJECTING":     21,
		"WORK_FLOW_STATUS_RECHECK_NF":                    22,
		"WORK_FLOW_STATUS_REJECTED":                      23,
		"WORK_FLOW_STATUS_RAISE_CHARGE_BACK":             24,
	}
)

func (x WorkFlowStatus) Enum() *WorkFlowStatus {
	p := new(WorkFlowStatus)
	*p = x
	return p
}

func (x WorkFlowStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkFlowStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes[3].Descriptor()
}

func (WorkFlowStatus) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes[3]
}

func (x WorkFlowStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkFlowStatus.Descriptor instead.
func (WorkFlowStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_dispute_enums_proto_rawDescGZIP(), []int{3}
}

// describes data type of questionnare response asked while raising the dispute
// todo: ask federal for meaning/defination of each data type
type QuestionnaireResponseDataType int32

const (
	QuestionnaireResponseDataType_QUESTIONNAIRE_RESPONSE_DATA_TYPE_UNSPECIFIED QuestionnaireResponseDataType = 0
	QuestionnaireResponseDataType_QUESTIONNAIRE_RESPONSE_DATA_TYPE_YES_AND_NO  QuestionnaireResponseDataType = 1
	// format: DD/MM/YY
	QuestionnaireResponseDataType_QUESTIONNAIRE_RESPONSE_DATA_TYPE_DATE                    QuestionnaireResponseDataType = 2
	QuestionnaireResponseDataType_QUESTIONNAIRE_RESPONSE_DATA_TYPE_NOS                     QuestionnaireResponseDataType = 3
	QuestionnaireResponseDataType_QUESTIONNAIRE_RESPONSE_DATA_TYPE_AMOUNT                  QuestionnaireResponseDataType = 4
	QuestionnaireResponseDataType_QUESTIONNAIRE_RESPONSE_DATA_TYPE_SELECT_FROM_LIST        QuestionnaireResponseDataType = 5
	QuestionnaireResponseDataType_QUESTIONNAIRE_RESPONSE_DATA_TYPE_STRING_REFERENCE_NUMBER QuestionnaireResponseDataType = 6
	QuestionnaireResponseDataType_QUESTIONNAIRE_RESPONSE_DATA_TYPE_STRING_TO_DESCRIBE      QuestionnaireResponseDataType = 7
)

// Enum value maps for QuestionnaireResponseDataType.
var (
	QuestionnaireResponseDataType_name = map[int32]string{
		0: "QUESTIONNAIRE_RESPONSE_DATA_TYPE_UNSPECIFIED",
		1: "QUESTIONNAIRE_RESPONSE_DATA_TYPE_YES_AND_NO",
		2: "QUESTIONNAIRE_RESPONSE_DATA_TYPE_DATE",
		3: "QUESTIONNAIRE_RESPONSE_DATA_TYPE_NOS",
		4: "QUESTIONNAIRE_RESPONSE_DATA_TYPE_AMOUNT",
		5: "QUESTIONNAIRE_RESPONSE_DATA_TYPE_SELECT_FROM_LIST",
		6: "QUESTIONNAIRE_RESPONSE_DATA_TYPE_STRING_REFERENCE_NUMBER",
		7: "QUESTIONNAIRE_RESPONSE_DATA_TYPE_STRING_TO_DESCRIBE",
	}
	QuestionnaireResponseDataType_value = map[string]int32{
		"QUESTIONNAIRE_RESPONSE_DATA_TYPE_UNSPECIFIED":             0,
		"QUESTIONNAIRE_RESPONSE_DATA_TYPE_YES_AND_NO":              1,
		"QUESTIONNAIRE_RESPONSE_DATA_TYPE_DATE":                    2,
		"QUESTIONNAIRE_RESPONSE_DATA_TYPE_NOS":                     3,
		"QUESTIONNAIRE_RESPONSE_DATA_TYPE_AMOUNT":                  4,
		"QUESTIONNAIRE_RESPONSE_DATA_TYPE_SELECT_FROM_LIST":        5,
		"QUESTIONNAIRE_RESPONSE_DATA_TYPE_STRING_REFERENCE_NUMBER": 6,
		"QUESTIONNAIRE_RESPONSE_DATA_TYPE_STRING_TO_DESCRIBE":      7,
	}
)

func (x QuestionnaireResponseDataType) Enum() *QuestionnaireResponseDataType {
	p := new(QuestionnaireResponseDataType)
	*p = x
	return p
}

func (x QuestionnaireResponseDataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestionnaireResponseDataType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes[4].Descriptor()
}

func (QuestionnaireResponseDataType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes[4]
}

func (x QuestionnaireResponseDataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestionnaireResponseDataType.Descriptor instead.
func (QuestionnaireResponseDataType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_dispute_enums_proto_rawDescGZIP(), []int{4}
}

// file extensions
type FileExtension int32

const (
	FileExtension_FILE_EXTENSION_UNSPECIFIED FileExtension = 0
	FileExtension_FILE_EXTENSION_PDF         FileExtension = 1
	FileExtension_FILE_EXTENSION_JPG         FileExtension = 2
	FileExtension_FILE_EXTENSION_JPEG        FileExtension = 3
	FileExtension_FILE_EXTENSION_PNG         FileExtension = 4
	FileExtension_FILE_EXTENSION_DOC         FileExtension = 5
	FileExtension_FILE_EXTENSION_TXT         FileExtension = 6
	FileExtension_FILE_EXTENSION_XLS         FileExtension = 7
	FileExtension_FILE_EXTENSION_XLSX        FileExtension = 8
	FileExtension_FILE_EXTENSION_BMP         FileExtension = 9
)

// Enum value maps for FileExtension.
var (
	FileExtension_name = map[int32]string{
		0: "FILE_EXTENSION_UNSPECIFIED",
		1: "FILE_EXTENSION_PDF",
		2: "FILE_EXTENSION_JPG",
		3: "FILE_EXTENSION_JPEG",
		4: "FILE_EXTENSION_PNG",
		5: "FILE_EXTENSION_DOC",
		6: "FILE_EXTENSION_TXT",
		7: "FILE_EXTENSION_XLS",
		8: "FILE_EXTENSION_XLSX",
		9: "FILE_EXTENSION_BMP",
	}
	FileExtension_value = map[string]int32{
		"FILE_EXTENSION_UNSPECIFIED": 0,
		"FILE_EXTENSION_PDF":         1,
		"FILE_EXTENSION_JPG":         2,
		"FILE_EXTENSION_JPEG":        3,
		"FILE_EXTENSION_PNG":         4,
		"FILE_EXTENSION_DOC":         5,
		"FILE_EXTENSION_TXT":         6,
		"FILE_EXTENSION_XLS":         7,
		"FILE_EXTENSION_XLSX":        8,
		"FILE_EXTENSION_BMP":         9,
	}
)

func (x FileExtension) Enum() *FileExtension {
	p := new(FileExtension)
	*p = x
	return p
}

func (x FileExtension) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileExtension) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes[5].Descriptor()
}

func (FileExtension) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes[5]
}

func (x FileExtension) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileExtension.Descriptor instead.
func (FileExtension) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_dispute_enums_proto_rawDescGZIP(), []int{5}
}

// response codes
type ResponseCode int32

const (
	ResponseCode_RESPONSE_CODE_UNSPECIFIED                                                      ResponseCode = 0
	ResponseCode_RESPONSE_CODE_REQUESTER_ID_REQUIRED                                            ResponseCode = 1
	ResponseCode_RESPONSE_CODE_DISPUTE_TYPE_REQUIRED                                            ResponseCode = 2
	ResponseCode_RESPONSE_CODE_INVALID_DISPUTE_TYPE                                             ResponseCode = 3
	ResponseCode_RESPONSE_CODE_ACCOUNT_NUMBER_REQUIRED                                          ResponseCode = 4
	ResponseCode_RESPONSE_CODE_CHANNEL_REQUIRED                                                 ResponseCode = 5
	ResponseCode_RESPONSE_CODE_INVALID_CHANNEL                                                  ResponseCode = 6
	ResponseCode_RESPONSE_CODE_TRANSACTION_DATE_REQUIRED                                        ResponseCode = 7
	ResponseCode_RESPONSE_CODE_INVALID_TRANSACTION_FROM_DATE                                    ResponseCode = 8
	ResponseCode_RESPONSE_CODE_INVALID_TRANSACTION_TO_DATE                                      ResponseCode = 9
	ResponseCode_RESPONSE_CODE_TRANSACTION_FROM_DATE_SHOULD_BE_GREATER_THAN_TRANSACTION_TO_DATE ResponseCode = 10
	ResponseCode_RESPONSE_CODE_DISPUTE_CASE_NUMBERS_MISMATCH_WITH_DISPUTE_COUNT                 ResponseCode = 11
	ResponseCode_RESPONSE_CODE_SUCCESS                                                          ResponseCode = 12
	ResponseCode_RESPONSE_CODE_SUCCESS_WITH_NO_TRANSACTIONS                                     ResponseCode = 13
	ResponseCode_RESPONSE_CODE_INVALID_DISPUTE_CASE_NUMBER                                      ResponseCode = 14
	ResponseCode_RESPONSE_CODE_ADD_TRANSACTION_DETAILS                                          ResponseCode = 15
	ResponseCode_RESPONSE_CODE_ADD_QUESTIONNAIRE                                                ResponseCode = 16
	ResponseCode_RESPONSE_CODE_TRANSACTION_ID_REQUIRED                                          ResponseCode = 17
	ResponseCode_RESPONSE_CODE_VALUE_DATE_REQUIRED                                              ResponseCode = 19
	ResponseCode_RESPONSE_CODE_TRANSACTION_TIME_REQUIRED                                        ResponseCode = 20
	ResponseCode_RESPONSE_CODE_TRANSACTION_PARTICULAR_REQUIRED                                  ResponseCode = 21
	ResponseCode_RESPONSE_CODE_TRANSACTION_AMOUNT_REQUIRED                                      ResponseCode = 22
	ResponseCode_RESPONSE_CODE_INVALID_TRANSACTION_DETAILS_CHECK_TRANSACTION_AMOUNT             ResponseCode = 23
	ResponseCode_RESPONSE_CODE_INVALID_TRANSACTION_DETAILS_CHECK_VALUE_DATE                     ResponseCode = 24
	ResponseCode_RESPONSE_CODE_INVALID_TRANSACTION_DETAILS_CHECK_TRANSACTION_TIME               ResponseCode = 25
	ResponseCode_RESPONSE_CODE_INVALID_TRANSACTION_DETAILS_CHECK_TRANSACTION_PARTICULAR         ResponseCode = 26
	ResponseCode_RESPONSE_CODE_ONE_TRANSACTION_ALLOWED_AGAINST_NORMAL_DISPUTE                   ResponseCode = 27
	ResponseCode_RESPONSE_CODE_QUESTION_CODE_REQUIRED                                           ResponseCode = 28
	ResponseCode_RESPONSE_CODE_RESPONSE_DATA_TYPE_REQUIRED                                      ResponseCode = 29
	ResponseCode_RESPONSE_CODE_RESPONSE_REQUIRED                                                ResponseCode = 30
	ResponseCode_RESPONSE_CODE_DISPUTE_COUNT_REQUIRED                                           ResponseCode = 31
	ResponseCode_RESPONSE_CODE_AT_LEAST_ONE_DISPUTE_CASE_NUMBER_REQUIRED                        ResponseCode = 32
	ResponseCode_RESPONSE_CODE_INVALID_RESPONSE_DATA_TYPE                                       ResponseCode = 33
	ResponseCode_RESPONSE_CODE_DISPUTE_CASE_NUMBER_REQUIRED                                     ResponseCode = 34
	ResponseCode_RESPONSE_CODE_FILE_NAME_SHOULD_NOT_BE_EMPTY                                    ResponseCode = 35
	ResponseCode_RESPONSE_CODE_FILE_NAME_SHOULD_CONTAIN_EXTENSION                               ResponseCode = 36
	ResponseCode_RESPONSE_CODE_INVALID_FILE_TYPE                                                ResponseCode = 37
	ResponseCode_RESPONSE_CODE_FILE_NAME_EXTENSION_AND_FILE_EXTENSION_MUST_BE_EQUAL             ResponseCode = 38
	ResponseCode_RESPONSE_CODE_FILE_CONTENT_IS_NOT_VALID_BASE_64                                ResponseCode = 39
	ResponseCode_RESPONSE_CODE_FILE_SIZE_EXCEEDED_MAXIMUM_ALLOWED_LIMIT                         ResponseCode = 40
	ResponseCode_RESPONSE_CODE_BASE_64_FILE_CONTENT_TYPE_IS_NOT_MATCHING_WITH_FILE_EXTENSION    ResponseCode = 41
	ResponseCode_RESPONSE_CODE_FILE_EXTENSION_SHOULD_NOT_BE_EMPTY                               ResponseCode = 42
	ResponseCode_RESPONSE_CODE_CORRESPONDENCE_TEXT_SHOULD_NOT_BE_EMPTY                          ResponseCode = 43
	ResponseCode_RESPONSE_CODE_TRANSACTION_NUMBER_SHOULD_NOT_BE_EMPTY                           ResponseCode = 44
	ResponseCode_RESPONSE_CODE_INVALID_TRANSACTION_NUMBER                                       ResponseCode = 45
	ResponseCode_RESPONSE_CODE_BASE_64_FORMAT_NOT_SUPPORTED                                     ResponseCode = 46
)

// Enum value maps for ResponseCode.
var (
	ResponseCode_name = map[int32]string{
		0:  "RESPONSE_CODE_UNSPECIFIED",
		1:  "RESPONSE_CODE_REQUESTER_ID_REQUIRED",
		2:  "RESPONSE_CODE_DISPUTE_TYPE_REQUIRED",
		3:  "RESPONSE_CODE_INVALID_DISPUTE_TYPE",
		4:  "RESPONSE_CODE_ACCOUNT_NUMBER_REQUIRED",
		5:  "RESPONSE_CODE_CHANNEL_REQUIRED",
		6:  "RESPONSE_CODE_INVALID_CHANNEL",
		7:  "RESPONSE_CODE_TRANSACTION_DATE_REQUIRED",
		8:  "RESPONSE_CODE_INVALID_TRANSACTION_FROM_DATE",
		9:  "RESPONSE_CODE_INVALID_TRANSACTION_TO_DATE",
		10: "RESPONSE_CODE_TRANSACTION_FROM_DATE_SHOULD_BE_GREATER_THAN_TRANSACTION_TO_DATE",
		11: "RESPONSE_CODE_DISPUTE_CASE_NUMBERS_MISMATCH_WITH_DISPUTE_COUNT",
		12: "RESPONSE_CODE_SUCCESS",
		13: "RESPONSE_CODE_SUCCESS_WITH_NO_TRANSACTIONS",
		14: "RESPONSE_CODE_INVALID_DISPUTE_CASE_NUMBER",
		15: "RESPONSE_CODE_ADD_TRANSACTION_DETAILS",
		16: "RESPONSE_CODE_ADD_QUESTIONNAIRE",
		17: "RESPONSE_CODE_TRANSACTION_ID_REQUIRED",
		19: "RESPONSE_CODE_VALUE_DATE_REQUIRED",
		20: "RESPONSE_CODE_TRANSACTION_TIME_REQUIRED",
		21: "RESPONSE_CODE_TRANSACTION_PARTICULAR_REQUIRED",
		22: "RESPONSE_CODE_TRANSACTION_AMOUNT_REQUIRED",
		23: "RESPONSE_CODE_INVALID_TRANSACTION_DETAILS_CHECK_TRANSACTION_AMOUNT",
		24: "RESPONSE_CODE_INVALID_TRANSACTION_DETAILS_CHECK_VALUE_DATE",
		25: "RESPONSE_CODE_INVALID_TRANSACTION_DETAILS_CHECK_TRANSACTION_TIME",
		26: "RESPONSE_CODE_INVALID_TRANSACTION_DETAILS_CHECK_TRANSACTION_PARTICULAR",
		27: "RESPONSE_CODE_ONE_TRANSACTION_ALLOWED_AGAINST_NORMAL_DISPUTE",
		28: "RESPONSE_CODE_QUESTION_CODE_REQUIRED",
		29: "RESPONSE_CODE_RESPONSE_DATA_TYPE_REQUIRED",
		30: "RESPONSE_CODE_RESPONSE_REQUIRED",
		31: "RESPONSE_CODE_DISPUTE_COUNT_REQUIRED",
		32: "RESPONSE_CODE_AT_LEAST_ONE_DISPUTE_CASE_NUMBER_REQUIRED",
		33: "RESPONSE_CODE_INVALID_RESPONSE_DATA_TYPE",
		34: "RESPONSE_CODE_DISPUTE_CASE_NUMBER_REQUIRED",
		35: "RESPONSE_CODE_FILE_NAME_SHOULD_NOT_BE_EMPTY",
		36: "RESPONSE_CODE_FILE_NAME_SHOULD_CONTAIN_EXTENSION",
		37: "RESPONSE_CODE_INVALID_FILE_TYPE",
		38: "RESPONSE_CODE_FILE_NAME_EXTENSION_AND_FILE_EXTENSION_MUST_BE_EQUAL",
		39: "RESPONSE_CODE_FILE_CONTENT_IS_NOT_VALID_BASE_64",
		40: "RESPONSE_CODE_FILE_SIZE_EXCEEDED_MAXIMUM_ALLOWED_LIMIT",
		41: "RESPONSE_CODE_BASE_64_FILE_CONTENT_TYPE_IS_NOT_MATCHING_WITH_FILE_EXTENSION",
		42: "RESPONSE_CODE_FILE_EXTENSION_SHOULD_NOT_BE_EMPTY",
		43: "RESPONSE_CODE_CORRESPONDENCE_TEXT_SHOULD_NOT_BE_EMPTY",
		44: "RESPONSE_CODE_TRANSACTION_NUMBER_SHOULD_NOT_BE_EMPTY",
		45: "RESPONSE_CODE_INVALID_TRANSACTION_NUMBER",
		46: "RESPONSE_CODE_BASE_64_FORMAT_NOT_SUPPORTED",
	}
	ResponseCode_value = map[string]int32{
		"RESPONSE_CODE_UNSPECIFIED":                                                      0,
		"RESPONSE_CODE_REQUESTER_ID_REQUIRED":                                            1,
		"RESPONSE_CODE_DISPUTE_TYPE_REQUIRED":                                            2,
		"RESPONSE_CODE_INVALID_DISPUTE_TYPE":                                             3,
		"RESPONSE_CODE_ACCOUNT_NUMBER_REQUIRED":                                          4,
		"RESPONSE_CODE_CHANNEL_REQUIRED":                                                 5,
		"RESPONSE_CODE_INVALID_CHANNEL":                                                  6,
		"RESPONSE_CODE_TRANSACTION_DATE_REQUIRED":                                        7,
		"RESPONSE_CODE_INVALID_TRANSACTION_FROM_DATE":                                    8,
		"RESPONSE_CODE_INVALID_TRANSACTION_TO_DATE":                                      9,
		"RESPONSE_CODE_TRANSACTION_FROM_DATE_SHOULD_BE_GREATER_THAN_TRANSACTION_TO_DATE": 10,
		"RESPONSE_CODE_DISPUTE_CASE_NUMBERS_MISMATCH_WITH_DISPUTE_COUNT":                 11,
		"RESPONSE_CODE_SUCCESS":                                                          12,
		"RESPONSE_CODE_SUCCESS_WITH_NO_TRANSACTIONS":                                     13,
		"RESPONSE_CODE_INVALID_DISPUTE_CASE_NUMBER":                                      14,
		"RESPONSE_CODE_ADD_TRANSACTION_DETAILS":                                          15,
		"RESPONSE_CODE_ADD_QUESTIONNAIRE":                                                16,
		"RESPONSE_CODE_TRANSACTION_ID_REQUIRED":                                          17,
		"RESPONSE_CODE_VALUE_DATE_REQUIRED":                                              19,
		"RESPONSE_CODE_TRANSACTION_TIME_REQUIRED":                                        20,
		"RESPONSE_CODE_TRANSACTION_PARTICULAR_REQUIRED":                                  21,
		"RESPONSE_CODE_TRANSACTION_AMOUNT_REQUIRED":                                      22,
		"RESPONSE_CODE_INVALID_TRANSACTION_DETAILS_CHECK_TRANSACTION_AMOUNT":             23,
		"RESPONSE_CODE_INVALID_TRANSACTION_DETAILS_CHECK_VALUE_DATE":                     24,
		"RESPONSE_CODE_INVALID_TRANSACTION_DETAILS_CHECK_TRANSACTION_TIME":               25,
		"RESPONSE_CODE_INVALID_TRANSACTION_DETAILS_CHECK_TRANSACTION_PARTICULAR":         26,
		"RESPONSE_CODE_ONE_TRANSACTION_ALLOWED_AGAINST_NORMAL_DISPUTE":                   27,
		"RESPONSE_CODE_QUESTION_CODE_REQUIRED":                                           28,
		"RESPONSE_CODE_RESPONSE_DATA_TYPE_REQUIRED":                                      29,
		"RESPONSE_CODE_RESPONSE_REQUIRED":                                                30,
		"RESPONSE_CODE_DISPUTE_COUNT_REQUIRED":                                           31,
		"RESPONSE_CODE_AT_LEAST_ONE_DISPUTE_CASE_NUMBER_REQUIRED":                        32,
		"RESPONSE_CODE_INVALID_RESPONSE_DATA_TYPE":                                       33,
		"RESPONSE_CODE_DISPUTE_CASE_NUMBER_REQUIRED":                                     34,
		"RESPONSE_CODE_FILE_NAME_SHOULD_NOT_BE_EMPTY":                                    35,
		"RESPONSE_CODE_FILE_NAME_SHOULD_CONTAIN_EXTENSION":                               36,
		"RESPONSE_CODE_INVALID_FILE_TYPE":                                                37,
		"RESPONSE_CODE_FILE_NAME_EXTENSION_AND_FILE_EXTENSION_MUST_BE_EQUAL":             38,
		"RESPONSE_CODE_FILE_CONTENT_IS_NOT_VALID_BASE_64":                                39,
		"RESPONSE_CODE_FILE_SIZE_EXCEEDED_MAXIMUM_ALLOWED_LIMIT":                         40,
		"RESPONSE_CODE_BASE_64_FILE_CONTENT_TYPE_IS_NOT_MATCHING_WITH_FILE_EXTENSION":    41,
		"RESPONSE_CODE_FILE_EXTENSION_SHOULD_NOT_BE_EMPTY":                               42,
		"RESPONSE_CODE_CORRESPONDENCE_TEXT_SHOULD_NOT_BE_EMPTY":                          43,
		"RESPONSE_CODE_TRANSACTION_NUMBER_SHOULD_NOT_BE_EMPTY":                           44,
		"RESPONSE_CODE_INVALID_TRANSACTION_NUMBER":                                       45,
		"RESPONSE_CODE_BASE_64_FORMAT_NOT_SUPPORTED":                                     46,
	}
)

func (x ResponseCode) Enum() *ResponseCode {
	p := new(ResponseCode)
	*p = x
	return p
}

func (x ResponseCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResponseCode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes[6].Descriptor()
}

func (ResponseCode) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes[6]
}

func (x ResponseCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResponseCode.Descriptor instead.
func (ResponseCode) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_dispute_enums_proto_rawDescGZIP(), []int{6}
}

var File_api_vendorgateway_openbanking_dispute_enums_proto protoreflect.FileDescriptor

var file_api_vendorgateway_openbanking_dispute_enums_proto_rawDesc = []byte{
	0x0a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f,
	0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x21, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x64,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x2a, 0x64, 0x0a, 0x0b, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a,
	0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x5f,
	0x41, 0x55, 0x54, 0x48, 0x4f, 0x52, 0x49, 0x53, 0x45, 0x44, 0x10, 0x02, 0x2a, 0xfc, 0x01, 0x0a,
	0x07, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x48, 0x41, 0x4e,
	0x4e, 0x45, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x55, 0x50, 0x49,
	0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x49, 0x4d,
	0x50, 0x53, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f,
	0x4e, 0x45, 0x46, 0x54, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45,
	0x4c, 0x5f, 0x52, 0x54, 0x47, 0x53, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x48, 0x41, 0x4e,
	0x4e, 0x45, 0x4c, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x41,
	0x54, 0x4d, 0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f,
	0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x06,
	0x12, 0x1b, 0x0a, 0x17, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x44, 0x45, 0x42, 0x49,
	0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x45, 0x43, 0x4f, 0x4d, 0x10, 0x07, 0x12, 0x12, 0x0a,
	0x0e, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x43, 0x48, 0x45, 0x51, 0x55, 0x45, 0x10,
	0x08, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x49, 0x4e, 0x54,
	0x52, 0x41, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x09, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x48, 0x41,
	0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x0a, 0x2a, 0x63, 0x0a, 0x0d, 0x44,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a,
	0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13,
	0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f,
	0x50, 0x45, 0x4e, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x02,
	0x2a, 0x9c, 0x08, 0x0a, 0x0e, 0x57, 0x6f, 0x72, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x1c, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c,
	0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x45, 0x44, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x02, 0x12, 0x2e,
	0x0a, 0x2a, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52,
	0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x03, 0x12, 0x2b,
	0x0a, 0x27, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x48,
	0x41, 0x52, 0x47, 0x45, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x04, 0x12, 0x28, 0x0a, 0x24, 0x57,
	0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f,
	0x56, 0x45, 0x44, 0x10, 0x05, 0x12, 0x28, 0x0a, 0x24, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c,
	0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45,
	0x42, 0x41, 0x43, 0x4b, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x06, 0x12,
	0x2f, 0x0a, 0x2b, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x50,
	0x52, 0x45, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x07,
	0x12, 0x32, 0x0a, 0x2e, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x46,
	0x4f, 0x52, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x08, 0x12, 0x2f, 0x0a, 0x2b, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x44, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x2d, 0x0a, 0x29, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c,
	0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x52,
	0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56,
	0x45, 0x44, 0x10, 0x0a, 0x12, 0x2d, 0x0a, 0x29, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x52, 0x42,
	0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x10, 0x0b, 0x12, 0x1b, 0x0a, 0x17, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x0c,
	0x12, 0x32, 0x0a, 0x2e, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x57, 0x41, 0x49, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x42, 0x52, 0x41, 0x4e,
	0x43, 0x48, 0x10, 0x0d, 0x12, 0x27, 0x0a, 0x23, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45,
	0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x45, 0x44, 0x10, 0x0e, 0x12, 0x27, 0x0a,
	0x23, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x55, 0x54, 0x45, 0x44, 0x10, 0x0f, 0x12, 0x27, 0x0a, 0x23, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x49, 0x41, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x10, 0x12,
	0x27, 0x0a, 0x23, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x41, 0x50,
	0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x11, 0x12, 0x2e, 0x0a, 0x2a, 0x57, 0x4f, 0x52, 0x4b,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x43,
	0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x44, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x41, 0x43, 0x43,
	0x45, 0x50, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x12, 0x12, 0x1e, 0x0a, 0x1a, 0x57, 0x4f, 0x52, 0x4b,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x43,
	0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x10, 0x13, 0x12, 0x1d, 0x0a, 0x19, 0x57, 0x4f, 0x52, 0x4b,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x43,
	0x45, 0x50, 0x54, 0x45, 0x44, 0x10, 0x14, 0x12, 0x2e, 0x0a, 0x2a, 0x57, 0x4f, 0x52, 0x4b, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x43, 0x4f,
	0x4d, 0x4d, 0x45, 0x4e, 0x44, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x4a, 0x45,
	0x43, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x15, 0x12, 0x1f, 0x0a, 0x1b, 0x57, 0x4f, 0x52, 0x4b, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x43, 0x48,
	0x45, 0x43, 0x4b, 0x5f, 0x4e, 0x46, 0x10, 0x16, 0x12, 0x1d, 0x0a, 0x19, 0x57, 0x4f, 0x52, 0x4b,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a,
	0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x17, 0x12, 0x26, 0x0a, 0x22, 0x57, 0x4f, 0x52, 0x4b, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x41, 0x49, 0x53,
	0x45, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x18, 0x2a,
	0xb2, 0x03, 0x0a, 0x1d, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x6e, 0x61, 0x69, 0x72,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x30, 0x0a, 0x2c, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x49,
	0x52, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x2f, 0x0a, 0x2b, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x4e,
	0x41, 0x49, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x59, 0x45, 0x53, 0x5f, 0x41, 0x4e, 0x44, 0x5f,
	0x4e, 0x4f, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e,
	0x4e, 0x41, 0x49, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x02, 0x12,
	0x28, 0x0a, 0x24, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52, 0x45,
	0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x53, 0x10, 0x03, 0x12, 0x2b, 0x0a, 0x27, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f,
	0x4e, 0x53, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x4d,
	0x4f, 0x55, 0x4e, 0x54, 0x10, 0x04, 0x12, 0x35, 0x0a, 0x31, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49,
	0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45,
	0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43,
	0x54, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x05, 0x12, 0x3c, 0x0a,
	0x38, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52, 0x45, 0x5f, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e,
	0x43, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x06, 0x12, 0x37, 0x0a, 0x33, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x4f, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49,
	0x42, 0x45, 0x10, 0x07, 0x2a, 0x89, 0x02, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45,
	0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45,
	0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x44, 0x46, 0x10, 0x01, 0x12, 0x16,
	0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x4a, 0x50, 0x47, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45,
	0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x4a, 0x50, 0x45, 0x47, 0x10, 0x03, 0x12,
	0x16, 0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f,
	0x4e, 0x5f, 0x50, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x4f, 0x43, 0x10, 0x05, 0x12,
	0x16, 0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x58, 0x54, 0x10, 0x06, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x58, 0x4c, 0x53, 0x10, 0x07, 0x12,
	0x17, 0x0a, 0x13, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f,
	0x4e, 0x5f, 0x58, 0x4c, 0x53, 0x58, 0x10, 0x08, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4d, 0x50, 0x10, 0x09,
	0x2a, 0xa7, 0x12, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x27, 0x0a, 0x23, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55,
	0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44,
	0x10, 0x02, 0x12, 0x26, 0x0a, 0x22, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50,
	0x55, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49,
	0x52, 0x45, 0x44, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53,
	0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x05, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x10, 0x06, 0x12, 0x2b, 0x0a, 0x27,
	0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x07, 0x12, 0x2f, 0x0a, 0x2b, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46,
	0x52, 0x4f, 0x4d, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x08, 0x12, 0x2d, 0x0a, 0x29, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x4f, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x09, 0x12, 0x52, 0x0a, 0x4e, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x44, 0x41, 0x54, 0x45,
	0x5f, 0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x5f, 0x42, 0x45, 0x5f, 0x47, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x52, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x4f, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x0a, 0x12, 0x42, 0x0a,
	0x3e, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x44,
	0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42,
	0x45, 0x52, 0x53, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x57, 0x49, 0x54,
	0x48, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10,
	0x0b, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x0c, 0x12, 0x2e, 0x0a, 0x2a,
	0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x55,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x4e, 0x4f, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x0d, 0x12, 0x2d, 0x0a, 0x29,
	0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x41,
	0x53, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x0e, 0x12, 0x29, 0x0a, 0x25, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x44, 0x44,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x10, 0x0f, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e,
	0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x49, 0x4f, 0x4e, 0x4e, 0x41, 0x49, 0x52, 0x45, 0x10, 0x10, 0x12, 0x29, 0x0a, 0x25, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x44, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x49, 0x52, 0x45, 0x44, 0x10, 0x11, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e,
	0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x44, 0x41,
	0x54, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x13, 0x12, 0x2b, 0x0a,
	0x27, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x14, 0x12, 0x31, 0x0a, 0x2d, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x43, 0x55, 0x4c,
	0x41, 0x52, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x15, 0x12, 0x2d, 0x0a,
	0x29, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x16, 0x12, 0x46, 0x0a, 0x42,
	0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4d, 0x4f, 0x55,
	0x4e, 0x54, 0x10, 0x17, 0x12, 0x3e, 0x0a, 0x3a, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45,
	0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x44, 0x41,
	0x54, 0x45, 0x10, 0x18, 0x12, 0x44, 0x0a, 0x40, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45,
	0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x19, 0x12, 0x4a, 0x0a, 0x46, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49, 0x43,
	0x55, 0x4c, 0x41, 0x52, 0x10, 0x1a, 0x12, 0x40, 0x0a, 0x3c, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e,
	0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4f, 0x4e, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x5f,
	0x41, 0x47, 0x41, 0x49, 0x4e, 0x53, 0x54, 0x5f, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x5f, 0x44,
	0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x10, 0x1b, 0x12, 0x28, 0x0a, 0x24, 0x52, 0x45, 0x53, 0x50,
	0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44,
	0x10, 0x1c, 0x12, 0x2d, 0x0a, 0x29, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10,
	0x1d, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x49, 0x52, 0x45, 0x44, 0x10, 0x1e, 0x12, 0x28, 0x0a, 0x24, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e,
	0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x1f,
	0x12, 0x3b, 0x0a, 0x37, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x41, 0x54, 0x5f, 0x4c, 0x45, 0x41, 0x53, 0x54, 0x5f, 0x4f, 0x4e, 0x45, 0x5f, 0x44,
	0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42,
	0x45, 0x52, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x20, 0x12, 0x2c, 0x0a,
	0x28, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x21, 0x12, 0x2e, 0x0a, 0x2a, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x44, 0x49, 0x53,
	0x50, 0x55, 0x54, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x22, 0x12, 0x2f, 0x0a, 0x2b, 0x52,
	0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x42, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x10, 0x23, 0x12, 0x34, 0x0a, 0x30,
	0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x5f, 0x43,
	0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e,
	0x10, 0x24, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x25, 0x12, 0x46, 0x0a, 0x42, 0x52, 0x45, 0x53, 0x50, 0x4f,
	0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4e, 0x44,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x5f,
	0x4d, 0x55, 0x53, 0x54, 0x5f, 0x42, 0x45, 0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x26, 0x12,
	0x33, 0x0a, 0x2f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x53,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x5f,
	0x36, 0x34, 0x10, 0x27, 0x12, 0x3a, 0x0a, 0x36, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45,
	0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x5f,
	0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x58, 0x49, 0x4d, 0x55, 0x4d,
	0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x28,
	0x12, 0x4f, 0x0a, 0x4b, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x5f, 0x36, 0x34, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43,
	0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x53, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x49, 0x54, 0x48,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x10,
	0x29, 0x12, 0x34, 0x0a, 0x30, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x4e, 0x53, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x42, 0x45, 0x5f,
	0x45, 0x4d, 0x50, 0x54, 0x59, 0x10, 0x2a, 0x12, 0x39, 0x0a, 0x35, 0x52, 0x45, 0x53, 0x50, 0x4f,
	0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x53, 0x50,
	0x4f, 0x4e, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x53, 0x48, 0x4f,
	0x55, 0x4c, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x42, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59,
	0x10, 0x2b, 0x12, 0x38, 0x0a, 0x34, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x42, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x10, 0x2c, 0x12, 0x2c, 0x0a, 0x28,
	0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x2d, 0x12, 0x2e, 0x0a, 0x2a, 0x52, 0x45,
	0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x41, 0x53, 0x45,
	0x5f, 0x36, 0x34, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53,
	0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x10, 0x2e, 0x42, 0x7c, 0x0a, 0x3c, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x5a, 0x3c, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x2f, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_openbanking_dispute_enums_proto_rawDescOnce sync.Once
	file_api_vendorgateway_openbanking_dispute_enums_proto_rawDescData = file_api_vendorgateway_openbanking_dispute_enums_proto_rawDesc
)

func file_api_vendorgateway_openbanking_dispute_enums_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_openbanking_dispute_enums_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_openbanking_dispute_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_openbanking_dispute_enums_proto_rawDescData)
	})
	return file_api_vendorgateway_openbanking_dispute_enums_proto_rawDescData
}

var file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_api_vendorgateway_openbanking_dispute_enums_proto_goTypes = []interface{}{
	(DisputeType)(0),                   // 0: vendorgateway.openbanking.dispute.DisputeType
	(Channel)(0),                       // 1: vendorgateway.openbanking.dispute.Channel
	(DisputeStatus)(0),                 // 2: vendorgateway.openbanking.dispute.DisputeStatus
	(WorkFlowStatus)(0),                // 3: vendorgateway.openbanking.dispute.WorkFlowStatus
	(QuestionnaireResponseDataType)(0), // 4: vendorgateway.openbanking.dispute.QuestionnaireResponseDataType
	(FileExtension)(0),                 // 5: vendorgateway.openbanking.dispute.FileExtension
	(ResponseCode)(0),                  // 6: vendorgateway.openbanking.dispute.ResponseCode
}
var file_api_vendorgateway_openbanking_dispute_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_openbanking_dispute_enums_proto_init() }
func file_api_vendorgateway_openbanking_dispute_enums_proto_init() {
	if File_api_vendorgateway_openbanking_dispute_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_openbanking_dispute_enums_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendorgateway_openbanking_dispute_enums_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_openbanking_dispute_enums_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_openbanking_dispute_enums_proto_enumTypes,
	}.Build()
	File_api_vendorgateway_openbanking_dispute_enums_proto = out.File
	file_api_vendorgateway_openbanking_dispute_enums_proto_rawDesc = nil
	file_api_vendorgateway_openbanking_dispute_enums_proto_goTypes = nil
	file_api_vendorgateway_openbanking_dispute_enums_proto_depIdxs = nil
}
