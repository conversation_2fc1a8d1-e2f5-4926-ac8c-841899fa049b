// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/crm/risk/ticket.proto

package risk

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RiskTicketFieldMask int32

const (
	RiskTicketFieldMask_RISK_TICKET_FIELD_MASK_UNSPECIFIED RiskTicketFieldMask = 0
	RiskTicketFieldMask_RISK_TICKET_FIELD_MASK_CREATED_AT  RiskTicketFieldMask = 1
	RiskTicketFieldMask_RISK_TICKET_FIELD_MASK_UPDATED_AT  RiskTicketFieldMask = 2
)

// Enum value maps for RiskTicketFieldMask.
var (
	RiskTicketFieldMask_name = map[int32]string{
		0: "RISK_TICKET_FIELD_MASK_UNSPECIFIED",
		1: "RISK_TICKET_FIELD_MASK_CREATED_AT",
		2: "RISK_TICKET_FIELD_MASK_UPDATED_AT",
	}
	RiskTicketFieldMask_value = map[string]int32{
		"RISK_TICKET_FIELD_MASK_UNSPECIFIED": 0,
		"RISK_TICKET_FIELD_MASK_CREATED_AT":  1,
		"RISK_TICKET_FIELD_MASK_UPDATED_AT":  2,
	}
)

func (x RiskTicketFieldMask) Enum() *RiskTicketFieldMask {
	p := new(RiskTicketFieldMask)
	*p = x
	return p
}

func (x RiskTicketFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskTicketFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_crm_risk_ticket_proto_enumTypes[0].Descriptor()
}

func (RiskTicketFieldMask) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_crm_risk_ticket_proto_enumTypes[0]
}

func (x RiskTicketFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskTicketFieldMask.Descriptor instead.
func (RiskTicketFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_crm_risk_ticket_proto_rawDescGZIP(), []int{0}
}

type FilterOption int32

const (
	FilterOption_FILTER_OPTION_UNSPECIFIED        FilterOption = 0
	FilterOption_FILTER_OPTION_UNIQUE_EXTERNAL_ID FilterOption = 1
)

// Enum value maps for FilterOption.
var (
	FilterOption_name = map[int32]string{
		0: "FILTER_OPTION_UNSPECIFIED",
		1: "FILTER_OPTION_UNIQUE_EXTERNAL_ID",
	}
	FilterOption_value = map[string]int32{
		"FILTER_OPTION_UNSPECIFIED":        0,
		"FILTER_OPTION_UNIQUE_EXTERNAL_ID": 1,
	}
)

func (x FilterOption) Enum() *FilterOption {
	p := new(FilterOption)
	*p = x
	return p
}

func (x FilterOption) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FilterOption) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_crm_risk_ticket_proto_enumTypes[1].Descriptor()
}

func (FilterOption) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_crm_risk_ticket_proto_enumTypes[1]
}

func (x FilterOption) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FilterOption.Descriptor instead.
func (FilterOption) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_crm_risk_ticket_proto_rawDescGZIP(), []int{1}
}

type RiskTicket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique ID of the ticket
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// this field is mandatory for creating a ticket
	UniqueExternalUserId string `protobuf:"bytes,2,opt,name=unique_external_user_id,json=uniqueExternalUserId,proto3" json:"unique_external_user_id,omitempty"`
	// As part of case workflow, some form of communication may be expected b/w risk analyst
	// and the user. Communication can either be over phone or email and will be populated only when
	// needed at CRM's end.
	// e.g., when details like salary proof are required from user
	UserDetails *UserDetails `protobuf:"bytes,3,opt,name=user_details,json=userDetails,proto3" json:"user_details,omitempty"`
	// ID of the user contact created in crm, we will need this id to fetch other users details present in crm
	UserContactId uint64 `protobuf:"varint,4,opt,name=user_contact_id,json=userContactId,proto3" json:"user_contact_id,omitempty"`
	// Subject line of the ticket
	// Optional field
	Subject string `protobuf:"bytes,5,opt,name=subject,proto3" json:"subject,omitempty"`
	// Content of the description in plain text
	// Optional field
	DescriptionText string `protobuf:"bytes,6,opt,name=description_text,json=descriptionText,proto3" json:"description_text,omitempty"`
	// As part of training agents or assessing rules, sample tickets may be generated
	// This flag helps distinguish sample tickets vs live tickets
	IsSample common.BooleanEnum `protobuf:"varint,7,opt,name=is_sample,json=isSample,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_sample,omitempty"`
	// Set to true if the ticket has been deleted/trashed. Deleted tickets will not be displayed in any views except the "deleted" filter
	IsDeleted common.BooleanEnum `protobuf:"varint,8,opt,name=is_deleted,json=isDeleted,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_deleted,omitempty"`
	// Priority of the ticket
	// By default it will be set to low if not passed while creating the ticket
	Priority Priority `protobuf:"varint,9,opt,name=priority,proto3,enum=vendorgateway.crm.risk.Priority" json:"priority,omitempty"`
	// Status of the ticket
	// Status will be set to CREATED by default if not passed while creating a ticket
	Status     Status     `protobuf:"varint,10,opt,name=status,proto3,enum=vendorgateway.crm.risk.Status" json:"status,omitempty"`
	ReviewType ReviewType `protobuf:"varint,11,opt,name=review_type,json=reviewType,proto3,enum=vendorgateway.crm.risk.ReviewType" json:"review_type,omitempty"`
	// Risk tickets are either manually reviewed vs auto reviewed
	// This field indicates the verdict of the ticket post the review
	Verdict Verdict `protobuf:"varint,12,opt,name=verdict,proto3,enum=vendorgateway.crm.risk.Verdict" json:"verdict,omitempty"`
	// agent group to which ticket is assigned to
	// default field in crm
	AgentGroup AgentGroup `protobuf:"varint,13,opt,name=agent_group,json=agentGroup,proto3,enum=vendorgateway.crm.risk.AgentGroup" json:"agent_group,omitempty"`
	// crm contact/agent ID of the agent to whom the ticket has been assigned
	AssignedAgentId uint64 `protobuf:"varint,14,opt,name=assigned_agent_id,json=assignedAgentId,proto3" json:"assigned_agent_id,omitempty"`
	// We will be using it to add tags required for assignment like
	// Rule names, Batch names, Queue types etc
	Tags      []string               `protobuf:"bytes,15,rep,name=tags,proto3" json:"tags,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Ticket will be marked on sleep/hold till expiry and will have limited visibility
	// Ticket will be unsnoozed when either status or assigned agent changes.
	SnoozedTill *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=snoozed_till,json=snoozedTill,proto3" json:"snoozed_till,omitempty"`
	// confidence score of the case (0 - 100)
	ConfidenceScore float32 `protobuf:"fixed32,19,opt,name=confidence_score,json=confidenceScore,proto3" json:"confidence_score,omitempty"`
	// it defines which user is worked on the ticket previously. It will help in determining who we should assign the
	// ticket once it moved back from out-call or other flow. It typically happens when case is moved around to
	// different agents in its lifecycle.
	LastAssignedAnalystEmail string `protobuf:"bytes,20,opt,name=last_assigned_analyst_email,json=lastAssignedAnalystEmail,proto3" json:"last_assigned_analyst_email,omitempty"`
	// new fields to be added to riskticket
	CfModel1Score float32 `protobuf:"fixed32,21,opt,name=cf_model1_score,json=cfModel1Score,proto3" json:"cf_model1_score,omitempty"`
	CfModel1Name  string  `protobuf:"bytes,22,opt,name=cf_model1_name,json=cfModel1Name,proto3" json:"cf_model1_name,omitempty"`
	CfModel2Score float32 `protobuf:"fixed32,23,opt,name=cf_model2_score,json=cfModel2Score,proto3" json:"cf_model2_score,omitempty"`
	CfModel2Name  string  `protobuf:"bytes,24,opt,name=cf_model2_name,json=cfModel2Name,proto3" json:"cf_model2_name,omitempty"`
	ModelSelected string  `protobuf:"bytes,25,opt,name=model_selected,json=modelSelected,proto3" json:"model_selected,omitempty"`
}

func (x *RiskTicket) Reset() {
	*x = RiskTicket{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskTicket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskTicket) ProtoMessage() {}

func (x *RiskTicket) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskTicket.ProtoReflect.Descriptor instead.
func (*RiskTicket) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_crm_risk_ticket_proto_rawDescGZIP(), []int{0}
}

func (x *RiskTicket) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RiskTicket) GetUniqueExternalUserId() string {
	if x != nil {
		return x.UniqueExternalUserId
	}
	return ""
}

func (x *RiskTicket) GetUserDetails() *UserDetails {
	if x != nil {
		return x.UserDetails
	}
	return nil
}

func (x *RiskTicket) GetUserContactId() uint64 {
	if x != nil {
		return x.UserContactId
	}
	return 0
}

func (x *RiskTicket) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *RiskTicket) GetDescriptionText() string {
	if x != nil {
		return x.DescriptionText
	}
	return ""
}

func (x *RiskTicket) GetIsSample() common.BooleanEnum {
	if x != nil {
		return x.IsSample
	}
	return common.BooleanEnum(0)
}

func (x *RiskTicket) GetIsDeleted() common.BooleanEnum {
	if x != nil {
		return x.IsDeleted
	}
	return common.BooleanEnum(0)
}

func (x *RiskTicket) GetPriority() Priority {
	if x != nil {
		return x.Priority
	}
	return Priority_PRIORITY_UNSPECIFIED
}

func (x *RiskTicket) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_STATUS_UNSPECIFIED
}

func (x *RiskTicket) GetReviewType() ReviewType {
	if x != nil {
		return x.ReviewType
	}
	return ReviewType_REVIEW_TYPE_UNSPECIFIED
}

func (x *RiskTicket) GetVerdict() Verdict {
	if x != nil {
		return x.Verdict
	}
	return Verdict_VERDICT_UNSPECIFIED
}

func (x *RiskTicket) GetAgentGroup() AgentGroup {
	if x != nil {
		return x.AgentGroup
	}
	return AgentGroup_AGENT_GROUP_UNSPECIFIED
}

func (x *RiskTicket) GetAssignedAgentId() uint64 {
	if x != nil {
		return x.AssignedAgentId
	}
	return 0
}

func (x *RiskTicket) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *RiskTicket) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *RiskTicket) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *RiskTicket) GetSnoozedTill() *timestamppb.Timestamp {
	if x != nil {
		return x.SnoozedTill
	}
	return nil
}

func (x *RiskTicket) GetConfidenceScore() float32 {
	if x != nil {
		return x.ConfidenceScore
	}
	return 0
}

func (x *RiskTicket) GetLastAssignedAnalystEmail() string {
	if x != nil {
		return x.LastAssignedAnalystEmail
	}
	return ""
}

func (x *RiskTicket) GetCfModel1Score() float32 {
	if x != nil {
		return x.CfModel1Score
	}
	return 0
}

func (x *RiskTicket) GetCfModel1Name() string {
	if x != nil {
		return x.CfModel1Name
	}
	return ""
}

func (x *RiskTicket) GetCfModel2Score() float32 {
	if x != nil {
		return x.CfModel2Score
	}
	return 0
}

func (x *RiskTicket) GetCfModel2Name() string {
	if x != nil {
		return x.CfModel2Name
	}
	return ""
}

func (x *RiskTicket) GetModelSelected() string {
	if x != nil {
		return x.ModelSelected
	}
	return ""
}

type UserDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email       string              `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Name        *common.Name        `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *UserDetails) Reset() {
	*x = UserDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserDetails) ProtoMessage() {}

func (x *UserDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserDetails.ProtoReflect.Descriptor instead.
func (*UserDetails) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_crm_risk_ticket_proto_rawDescGZIP(), []int{1}
}

func (x *UserDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserDetails) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *UserDetails) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

// filters for risk ticket list
type RiskFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// agent email
	AssignedToEmail string       `protobuf:"bytes,2,opt,name=assigned_to_email,json=assignedToEmail,proto3" json:"assigned_to_email,omitempty"`
	Statuses        []Status     `protobuf:"varint,3,rep,packed,name=statuses,proto3,enum=vendorgateway.crm.risk.Status" json:"statuses,omitempty"`
	Priorities      []Priority   `protobuf:"varint,4,rep,packed,name=priorities,proto3,enum=vendorgateway.crm.risk.Priority" json:"priorities,omitempty"`
	ReviewTypes     []ReviewType `protobuf:"varint,5,rep,packed,name=review_types,json=reviewTypes,proto3,enum=vendorgateway.crm.risk.ReviewType" json:"review_types,omitempty"`
	AgentId         uint64       `protobuf:"varint,6,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	// True - if response should have only snoozed tickets
	// False - If response should not have snoozed tickets
	// Unspecified - If response should include both
	Snoozed common.BooleanEnum `protobuf:"varint,7,opt,name=snoozed,proto3,enum=api.typesv2.common.BooleanEnum" json:"snoozed,omitempty"`
	// upper(included) and lower(excluded) limit of confidence score
	ConfidenceScoreRange *NumericRange `protobuf:"bytes,8,opt,name=confidence_score_range,json=confidenceScoreRange,proto3" json:"confidence_score_range,omitempty"`
	// ticket created at timestamp range (from, to]
	CreatedAtRange *TimestampRange `protobuf:"bytes,9,opt,name=created_at_range,json=createdAtRange,proto3" json:"created_at_range,omitempty"`
	Tags           []string        `protobuf:"bytes,10,rep,name=tags,proto3" json:"tags,omitempty"`
	UpdatedAtRange *TimestampRange `protobuf:"bytes,11,opt,name=updated_at_range,json=updatedAtRange,proto3" json:"updated_at_range,omitempty"`
}

func (x *RiskFilters) Reset() {
	*x = RiskFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskFilters) ProtoMessage() {}

func (x *RiskFilters) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskFilters.ProtoReflect.Descriptor instead.
func (*RiskFilters) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_crm_risk_ticket_proto_rawDescGZIP(), []int{2}
}

func (x *RiskFilters) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *RiskFilters) GetAssignedToEmail() string {
	if x != nil {
		return x.AssignedToEmail
	}
	return ""
}

func (x *RiskFilters) GetStatuses() []Status {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *RiskFilters) GetPriorities() []Priority {
	if x != nil {
		return x.Priorities
	}
	return nil
}

func (x *RiskFilters) GetReviewTypes() []ReviewType {
	if x != nil {
		return x.ReviewTypes
	}
	return nil
}

func (x *RiskFilters) GetAgentId() uint64 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *RiskFilters) GetSnoozed() common.BooleanEnum {
	if x != nil {
		return x.Snoozed
	}
	return common.BooleanEnum(0)
}

func (x *RiskFilters) GetConfidenceScoreRange() *NumericRange {
	if x != nil {
		return x.ConfidenceScoreRange
	}
	return nil
}

func (x *RiskFilters) GetCreatedAtRange() *TimestampRange {
	if x != nil {
		return x.CreatedAtRange
	}
	return nil
}

func (x *RiskFilters) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *RiskFilters) GetUpdatedAtRange() *TimestampRange {
	if x != nil {
		return x.UpdatedAtRange
	}
	return nil
}

type NumericRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lower float32 `protobuf:"fixed32,1,opt,name=lower,proto3" json:"lower,omitempty"`
	Upper float32 `protobuf:"fixed32,2,opt,name=upper,proto3" json:"upper,omitempty"`
}

func (x *NumericRange) Reset() {
	*x = NumericRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NumericRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NumericRange) ProtoMessage() {}

func (x *NumericRange) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NumericRange.ProtoReflect.Descriptor instead.
func (*NumericRange) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_crm_risk_ticket_proto_rawDescGZIP(), []int{3}
}

func (x *NumericRange) GetLower() float32 {
	if x != nil {
		return x.Lower
	}
	return 0
}

func (x *NumericRange) GetUpper() float32 {
	if x != nil {
		return x.Upper
	}
	return 0
}

type TimestampRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Exclusive from timestamp.
	From *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	// Inclusive to timestamp.
	To *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
}

func (x *TimestampRange) Reset() {
	*x = TimestampRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimestampRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimestampRange) ProtoMessage() {}

func (x *TimestampRange) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimestampRange.ProtoReflect.Descriptor instead.
func (*TimestampRange) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_crm_risk_ticket_proto_rawDescGZIP(), []int{4}
}

func (x *TimestampRange) GetFrom() *timestamppb.Timestamp {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *TimestampRange) GetTo() *timestamppb.Timestamp {
	if x != nil {
		return x.To
	}
	return nil
}

type SortableRiskFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filters options should include all filters that need to applied
	FilterOptions        []FilterOption `protobuf:"varint,1,rep,packed,name=filter_options,json=filterOptions,proto3,enum=vendorgateway.crm.risk.FilterOption" json:"filter_options,omitempty"`
	UniqueExternalUserId string         `protobuf:"bytes,2,opt,name=unique_external_user_id,json=uniqueExternalUserId,proto3" json:"unique_external_user_id,omitempty"`
	// ticket field on which all tickets will be sorted
	SortByTicketField RiskTicketFieldMask `protobuf:"varint,3,opt,name=sort_by_ticket_field,json=sortByTicketField,proto3,enum=vendorgateway.crm.risk.RiskTicketFieldMask" json:"sort_by_ticket_field,omitempty"`
	Order             common.SortOrder    `protobuf:"varint,4,opt,name=order,proto3,enum=api.typesv2.common.SortOrder" json:"order,omitempty"`
}

func (x *SortableRiskFilters) Reset() {
	*x = SortableRiskFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortableRiskFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortableRiskFilters) ProtoMessage() {}

func (x *SortableRiskFilters) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortableRiskFilters.ProtoReflect.Descriptor instead.
func (*SortableRiskFilters) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_crm_risk_ticket_proto_rawDescGZIP(), []int{5}
}

func (x *SortableRiskFilters) GetFilterOptions() []FilterOption {
	if x != nil {
		return x.FilterOptions
	}
	return nil
}

func (x *SortableRiskFilters) GetUniqueExternalUserId() string {
	if x != nil {
		return x.UniqueExternalUserId
	}
	return ""
}

func (x *SortableRiskFilters) GetSortByTicketField() RiskTicketFieldMask {
	if x != nil {
		return x.SortByTicketField
	}
	return RiskTicketFieldMask_RISK_TICKET_FIELD_MASK_UNSPECIFIED
}

func (x *SortableRiskFilters) GetOrder() common.SortOrder {
	if x != nil {
		return x.Order
	}
	return common.SortOrder(0)
}

var File_api_vendorgateway_crm_risk_ticket_proto protoreflect.FileDescriptor

var file_api_vendorgateway_crm_risk_ticket_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x63, 0x72, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x6d, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x73, 0x6f,
	0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x63, 0x72, 0x6d, 0x2f,
	0x72, 0x69, 0x73, 0x6b, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf4, 0x09, 0x0a, 0x0a, 0x52,
	0x69, 0x73, 0x6b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x35, 0x0a, 0x17, 0x75, 0x6e, 0x69,
	0x71, 0x75, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x75, 0x6e, 0x69, 0x71,
	0x75, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x46, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x6d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x75, 0x73, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x12, 0x3c, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x73, 0x61, 0x6d, 0x70,
	0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x69, 0x73, 0x53, 0x61, 0x6d,
	0x70, 0x6c, 0x65, 0x12, 0x3e, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f,
	0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x6d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x50,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x63, 0x72, 0x6d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x0a, 0x0b, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63,
	0x72, 0x6d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x63, 0x72, 0x6d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x56, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x12, 0x43, 0x0a, 0x0b, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63,
	0x72, 0x6d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x52, 0x0a, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x2a,
	0x0a, 0x11, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f, 0x61, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x64, 0x5f,
	0x74, 0x69, 0x6c, 0x6c, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x64, 0x54,
	0x69, 0x6c, 0x6c, 0x12, 0x3a, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0f, 0xfa,
	0x42, 0x0c, 0x0a, 0x0a, 0x1d, 0x00, 0x00, 0xc8, 0x42, 0x2d, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x3d, 0x0a, 0x1b, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x6c, 0x61, 0x73, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x26,
	0x0a, 0x0f, 0x63, 0x66, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x31, 0x5f, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x63, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x31, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x66, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x31, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x63, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x31, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f,
	0x63, 0x66, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x32, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x63, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x32, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x66, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x32, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x66,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x32, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x22, 0x95, 0x01, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xa3, 0x05, 0x0a, 0x0b, 0x52, 0x69,
	0x73, 0x6b, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x5f, 0x74, 0x6f, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x6f, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x3a, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x63, 0x72, 0x6d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12, 0x40, 0x0a, 0x0a,
	0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x20, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x63, 0x72, 0x6d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x45,
	0x0a, 0x0c, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x6d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x39, 0x0a, 0x07, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x07, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x64, 0x12, 0x5a, 0x0a, 0x16, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f,
	0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x6d, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x52, 0x14, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x50, 0x0a, 0x10, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x63, 0x72, 0x6d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x50, 0x0a,
	0x10, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x6d, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x22,
	0x3a, 0x0a, 0x0c, 0x4e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05,
	0x6c, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x70, 0x70, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x75, 0x70, 0x70, 0x65, 0x72, 0x22, 0x6c, 0x0a, 0x0e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2e, 0x0a,
	0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x2a, 0x0a,
	0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x6f, 0x22, 0xc0, 0x02, 0x0a, 0x13, 0x53, 0x6f,
	0x72, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x4b, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x6d, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0d, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x35,
	0x0a, 0x17, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x66, 0x0a, 0x14, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x62, 0x79,
	0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x6d, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x52, 0x69, 0x73,
	0x6b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x11, 0x73, 0x6f, 0x72, 0x74,
	0x42, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x3d, 0x0a,
	0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2a, 0x8b, 0x01, 0x0a,
	0x13, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x4d, 0x61, 0x73, 0x6b, 0x12, 0x26, 0x0a, 0x22, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x49, 0x43,
	0x4b, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21,
	0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41,
	0x54, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x49, 0x43, 0x4b,
	0x45, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x02, 0x2a, 0x53, 0x0a, 0x0c, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x19, 0x46, 0x49,
	0x4c, 0x54, 0x45, 0x52, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x46, 0x49, 0x4c,
	0x54, 0x45, 0x52, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x49, 0x51, 0x55,
	0x45, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x42,
	0x66, 0x0a, 0x31, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x72, 0x6d, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x5a, 0x31, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x63,
	0x72, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_crm_risk_ticket_proto_rawDescOnce sync.Once
	file_api_vendorgateway_crm_risk_ticket_proto_rawDescData = file_api_vendorgateway_crm_risk_ticket_proto_rawDesc
)

func file_api_vendorgateway_crm_risk_ticket_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_crm_risk_ticket_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_crm_risk_ticket_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_crm_risk_ticket_proto_rawDescData)
	})
	return file_api_vendorgateway_crm_risk_ticket_proto_rawDescData
}

var file_api_vendorgateway_crm_risk_ticket_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_vendorgateway_crm_risk_ticket_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_vendorgateway_crm_risk_ticket_proto_goTypes = []interface{}{
	(RiskTicketFieldMask)(0),      // 0: vendorgateway.crm.risk.RiskTicketFieldMask
	(FilterOption)(0),             // 1: vendorgateway.crm.risk.FilterOption
	(*RiskTicket)(nil),            // 2: vendorgateway.crm.risk.RiskTicket
	(*UserDetails)(nil),           // 3: vendorgateway.crm.risk.UserDetails
	(*RiskFilters)(nil),           // 4: vendorgateway.crm.risk.RiskFilters
	(*NumericRange)(nil),          // 5: vendorgateway.crm.risk.NumericRange
	(*TimestampRange)(nil),        // 6: vendorgateway.crm.risk.TimestampRange
	(*SortableRiskFilters)(nil),   // 7: vendorgateway.crm.risk.SortableRiskFilters
	(common.BooleanEnum)(0),       // 8: api.typesv2.common.BooleanEnum
	(Priority)(0),                 // 9: vendorgateway.crm.risk.Priority
	(Status)(0),                   // 10: vendorgateway.crm.risk.Status
	(ReviewType)(0),               // 11: vendorgateway.crm.risk.ReviewType
	(Verdict)(0),                  // 12: vendorgateway.crm.risk.Verdict
	(AgentGroup)(0),               // 13: vendorgateway.crm.risk.AgentGroup
	(*timestamppb.Timestamp)(nil), // 14: google.protobuf.Timestamp
	(*common.PhoneNumber)(nil),    // 15: api.typesv2.common.PhoneNumber
	(*common.Name)(nil),           // 16: api.typesv2.common.Name
	(common.SortOrder)(0),         // 17: api.typesv2.common.SortOrder
}
var file_api_vendorgateway_crm_risk_ticket_proto_depIdxs = []int32{
	3,  // 0: vendorgateway.crm.risk.RiskTicket.user_details:type_name -> vendorgateway.crm.risk.UserDetails
	8,  // 1: vendorgateway.crm.risk.RiskTicket.is_sample:type_name -> api.typesv2.common.BooleanEnum
	8,  // 2: vendorgateway.crm.risk.RiskTicket.is_deleted:type_name -> api.typesv2.common.BooleanEnum
	9,  // 3: vendorgateway.crm.risk.RiskTicket.priority:type_name -> vendorgateway.crm.risk.Priority
	10, // 4: vendorgateway.crm.risk.RiskTicket.status:type_name -> vendorgateway.crm.risk.Status
	11, // 5: vendorgateway.crm.risk.RiskTicket.review_type:type_name -> vendorgateway.crm.risk.ReviewType
	12, // 6: vendorgateway.crm.risk.RiskTicket.verdict:type_name -> vendorgateway.crm.risk.Verdict
	13, // 7: vendorgateway.crm.risk.RiskTicket.agent_group:type_name -> vendorgateway.crm.risk.AgentGroup
	14, // 8: vendorgateway.crm.risk.RiskTicket.created_at:type_name -> google.protobuf.Timestamp
	14, // 9: vendorgateway.crm.risk.RiskTicket.updated_at:type_name -> google.protobuf.Timestamp
	14, // 10: vendorgateway.crm.risk.RiskTicket.snoozed_till:type_name -> google.protobuf.Timestamp
	15, // 11: vendorgateway.crm.risk.UserDetails.phone_number:type_name -> api.typesv2.common.PhoneNumber
	16, // 12: vendorgateway.crm.risk.UserDetails.name:type_name -> api.typesv2.common.Name
	14, // 13: vendorgateway.crm.risk.RiskFilters.updated_at:type_name -> google.protobuf.Timestamp
	10, // 14: vendorgateway.crm.risk.RiskFilters.statuses:type_name -> vendorgateway.crm.risk.Status
	9,  // 15: vendorgateway.crm.risk.RiskFilters.priorities:type_name -> vendorgateway.crm.risk.Priority
	11, // 16: vendorgateway.crm.risk.RiskFilters.review_types:type_name -> vendorgateway.crm.risk.ReviewType
	8,  // 17: vendorgateway.crm.risk.RiskFilters.snoozed:type_name -> api.typesv2.common.BooleanEnum
	5,  // 18: vendorgateway.crm.risk.RiskFilters.confidence_score_range:type_name -> vendorgateway.crm.risk.NumericRange
	6,  // 19: vendorgateway.crm.risk.RiskFilters.created_at_range:type_name -> vendorgateway.crm.risk.TimestampRange
	6,  // 20: vendorgateway.crm.risk.RiskFilters.updated_at_range:type_name -> vendorgateway.crm.risk.TimestampRange
	14, // 21: vendorgateway.crm.risk.TimestampRange.from:type_name -> google.protobuf.Timestamp
	14, // 22: vendorgateway.crm.risk.TimestampRange.to:type_name -> google.protobuf.Timestamp
	1,  // 23: vendorgateway.crm.risk.SortableRiskFilters.filter_options:type_name -> vendorgateway.crm.risk.FilterOption
	0,  // 24: vendorgateway.crm.risk.SortableRiskFilters.sort_by_ticket_field:type_name -> vendorgateway.crm.risk.RiskTicketFieldMask
	17, // 25: vendorgateway.crm.risk.SortableRiskFilters.order:type_name -> api.typesv2.common.SortOrder
	26, // [26:26] is the sub-list for method output_type
	26, // [26:26] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_crm_risk_ticket_proto_init() }
func file_api_vendorgateway_crm_risk_ticket_proto_init() {
	if File_api_vendorgateway_crm_risk_ticket_proto != nil {
		return
	}
	file_api_vendorgateway_crm_risk_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskTicket); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NumericRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimestampRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_crm_risk_ticket_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortableRiskFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_crm_risk_ticket_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendorgateway_crm_risk_ticket_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_crm_risk_ticket_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_crm_risk_ticket_proto_enumTypes,
		MessageInfos:      file_api_vendorgateway_crm_risk_ticket_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_crm_risk_ticket_proto = out.File
	file_api_vendorgateway_crm_risk_ticket_proto_rawDesc = nil
	file_api_vendorgateway_crm_risk_ticket_proto_goTypes = nil
	file_api_vendorgateway_crm_risk_ticket_proto_depIdxs = nil
}
