// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/user/user.proto

package user

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	employment "github.com/epifi/gamma/api/employment"

	kyc "github.com/epifi/gamma/api/kyc"

	typesv2 "github.com/epifi/gamma/api/typesv2"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.Platform(0)

	_ = employment.OccupationType(0)

	_ = kyc.KYCLevel(0)

	_ = typesv2.MaritalStatus(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on User with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *User) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on User with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in UserMultiError, or nil if none found.
func (m *User) ValidateAll() error {
	return m.validate(true)
}

func (m *User) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetProfile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "Profile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "Profile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProfile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserValidationError{
				field:  "Profile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCustomerInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserValidationError{
						field:  fmt.Sprintf("CustomerInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserValidationError{
						field:  fmt.Sprintf("CustomerInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserValidationError{
					field:  fmt.Sprintf("CustomerInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AccessRevokeState

	if all {
		switch v := interface{}(m.GetAccessRevokeInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "AccessRevokeInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "AccessRevokeInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccessRevokeInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserValidationError{
				field:  "AccessRevokeInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccessRevokeDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "AccessRevokeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "AccessRevokeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccessRevokeDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserValidationError{
				field:  "AccessRevokeDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAcquisitionInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "AcquisitionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "AcquisitionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAcquisitionInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserValidationError{
				field:  "AcquisitionInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletionDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "DeletionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "DeletionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletionDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserValidationError{
				field:  "DeletionDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDataVerificationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "DataVerificationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "DataVerificationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDataVerificationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserValidationError{
				field:  "DataVerificationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UserMultiError(errors)
	}

	return nil
}

// UserMultiError is an error wrapping multiple validation errors returned by
// User.ValidateAll() if the designated constraints aren't met.
type UserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserMultiError) AllErrors() []error { return m }

// UserValidationError is the validation error returned by User.Validate if the
// designated constraints aren't met.
type UserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserValidationError) ErrorName() string { return "UserValidationError" }

// Error satisfies the builtin error interface
func (e UserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUser.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserValidationError{}

// Validate checks the field values on DataVerificationDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DataVerificationDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataVerificationDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DataVerificationDetailMultiError, or nil if none found.
func (m *DataVerificationDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *DataVerificationDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DataType

	// no validation rules for VerificationEntity

	// no validation rules for VerificationMethod

	if all {
		switch v := interface{}(m.GetVerifiedTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataVerificationDetailValidationError{
					field:  "VerifiedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataVerificationDetailValidationError{
					field:  "VerifiedTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVerifiedTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataVerificationDetailValidationError{
				field:  "VerifiedTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.DataValue.(type) {
	case *DataVerificationDetail_PanNumber:
		if v == nil {
			err := DataVerificationDetailValidationError{
				field:  "DataValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PanNumber
	case *DataVerificationDetail_DOB:
		if v == nil {
			err := DataVerificationDetailValidationError{
				field:  "DataValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDOB()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DataVerificationDetailValidationError{
						field:  "DOB",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DataVerificationDetailValidationError{
						field:  "DOB",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDOB()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DataVerificationDetailValidationError{
					field:  "DOB",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *DataVerificationDetail_PanName:
		if v == nil {
			err := DataVerificationDetailValidationError{
				field:  "DataValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPanName()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DataVerificationDetailValidationError{
						field:  "PanName",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DataVerificationDetailValidationError{
						field:  "PanName",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPanName()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DataVerificationDetailValidationError{
					field:  "PanName",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *DataVerificationDetail_EmploymentDetail_:
		if v == nil {
			err := DataVerificationDetailValidationError{
				field:  "DataValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEmploymentDetail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DataVerificationDetailValidationError{
						field:  "EmploymentDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DataVerificationDetailValidationError{
						field:  "EmploymentDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEmploymentDetail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DataVerificationDetailValidationError{
					field:  "EmploymentDetail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *DataVerificationDetail_MaritalStatus:
		if v == nil {
			err := DataVerificationDetailValidationError{
				field:  "DataValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for MaritalStatus
	case *DataVerificationDetail_AddressDetails:
		if v == nil {
			err := DataVerificationDetailValidationError{
				field:  "DataValue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAddressDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DataVerificationDetailValidationError{
						field:  "AddressDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DataVerificationDetailValidationError{
						field:  "AddressDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAddressDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DataVerificationDetailValidationError{
					field:  "AddressDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return DataVerificationDetailMultiError(errors)
	}

	return nil
}

// DataVerificationDetailMultiError is an error wrapping multiple validation
// errors returned by DataVerificationDetail.ValidateAll() if the designated
// constraints aren't met.
type DataVerificationDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataVerificationDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataVerificationDetailMultiError) AllErrors() []error { return m }

// DataVerificationDetailValidationError is the validation error returned by
// DataVerificationDetail.Validate if the designated constraints aren't met.
type DataVerificationDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataVerificationDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataVerificationDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataVerificationDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataVerificationDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataVerificationDetailValidationError) ErrorName() string {
	return "DataVerificationDetailValidationError"
}

// Error satisfies the builtin error interface
func (e DataVerificationDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataVerificationDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataVerificationDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataVerificationDetailValidationError{}

// Validate checks the field values on DataVerificationDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DataVerificationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataVerificationDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DataVerificationDetailsMultiError, or nil if none found.
func (m *DataVerificationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *DataVerificationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDataVerificationDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DataVerificationDetailsValidationError{
						field:  fmt.Sprintf("DataVerificationDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DataVerificationDetailsValidationError{
						field:  fmt.Sprintf("DataVerificationDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DataVerificationDetailsValidationError{
					field:  fmt.Sprintf("DataVerificationDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DataVerificationDetailsMultiError(errors)
	}

	return nil
}

// DataVerificationDetailsMultiError is an error wrapping multiple validation
// errors returned by DataVerificationDetails.ValidateAll() if the designated
// constraints aren't met.
type DataVerificationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataVerificationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataVerificationDetailsMultiError) AllErrors() []error { return m }

// DataVerificationDetailsValidationError is the validation error returned by
// DataVerificationDetails.Validate if the designated constraints aren't met.
type DataVerificationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataVerificationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataVerificationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataVerificationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataVerificationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataVerificationDetailsValidationError) ErrorName() string {
	return "DataVerificationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e DataVerificationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataVerificationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataVerificationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataVerificationDetailsValidationError{}

// Validate checks the field values on DeletionDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeletionDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletionDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeletionDetailsMultiError, or nil if none found.
func (m *DeletionDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletionDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DeletionReason

	if len(errors) > 0 {
		return DeletionDetailsMultiError(errors)
	}

	return nil
}

// DeletionDetailsMultiError is an error wrapping multiple validation errors
// returned by DeletionDetails.ValidateAll() if the designated constraints
// aren't met.
type DeletionDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletionDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletionDetailsMultiError) AllErrors() []error { return m }

// DeletionDetailsValidationError is the validation error returned by
// DeletionDetails.Validate if the designated constraints aren't met.
type DeletionDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletionDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletionDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletionDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletionDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletionDetailsValidationError) ErrorName() string { return "DeletionDetailsValidationError" }

// Error satisfies the builtin error interface
func (e DeletionDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletionDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletionDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletionDetailsValidationError{}

// Validate checks the field values on AccessRevokeInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccessRevokeInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccessRevokeInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccessRevokeInfoMultiError, or nil if none found.
func (m *AccessRevokeInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AccessRevokeInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsAccessRevoked

	// no validation rules for Reason

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccessRevokeInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccessRevokeInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccessRevokeInfoValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UpdatedBy

	if len(errors) > 0 {
		return AccessRevokeInfoMultiError(errors)
	}

	return nil
}

// AccessRevokeInfoMultiError is an error wrapping multiple validation errors
// returned by AccessRevokeInfo.ValidateAll() if the designated constraints
// aren't met.
type AccessRevokeInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccessRevokeInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccessRevokeInfoMultiError) AllErrors() []error { return m }

// AccessRevokeInfoValidationError is the validation error returned by
// AccessRevokeInfo.Validate if the designated constraints aren't met.
type AccessRevokeInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccessRevokeInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccessRevokeInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccessRevokeInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccessRevokeInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccessRevokeInfoValidationError) ErrorName() string { return "AccessRevokeInfoValidationError" }

// Error satisfies the builtin error interface
func (e AccessRevokeInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccessRevokeInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccessRevokeInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccessRevokeInfoValidationError{}

// Validate checks the field values on AccessRevokeDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AccessRevokeDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccessRevokeDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccessRevokeDetailsMultiError, or nil if none found.
func (m *AccessRevokeDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AccessRevokeDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccessRevokeStatus

	// no validation rules for Reason

	// no validation rules for RestoreReason

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccessRevokeDetailsValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccessRevokeDetailsValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccessRevokeDetailsValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UpdatedBy

	if len(errors) > 0 {
		return AccessRevokeDetailsMultiError(errors)
	}

	return nil
}

// AccessRevokeDetailsMultiError is an error wrapping multiple validation
// errors returned by AccessRevokeDetails.ValidateAll() if the designated
// constraints aren't met.
type AccessRevokeDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccessRevokeDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccessRevokeDetailsMultiError) AllErrors() []error { return m }

// AccessRevokeDetailsValidationError is the validation error returned by
// AccessRevokeDetails.Validate if the designated constraints aren't met.
type AccessRevokeDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccessRevokeDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccessRevokeDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccessRevokeDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccessRevokeDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccessRevokeDetailsValidationError) ErrorName() string {
	return "AccessRevokeDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AccessRevokeDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccessRevokeDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccessRevokeDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccessRevokeDetailsValidationError{}

// Validate checks the field values on Profile with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Profile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Profile with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ProfileMultiError, or nil if none found.
func (m *Profile) ValidateAll() error {
	return m.validate(true)
}

func (m *Profile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDateOfBirth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "DateOfBirth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateOfBirth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileValidationError{
				field:  "DateOfBirth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	{
		sorted_keys := make([]string, len(m.GetAddresses()))
		i := 0
		for key := range m.GetAddresses() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetAddresses()[key]
			_ = val

			// no validation rules for Addresses[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ProfileValidationError{
							field:  fmt.Sprintf("Addresses[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ProfileValidationError{
							field:  fmt.Sprintf("Addresses[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ProfileValidationError{
						field:  fmt.Sprintf("Addresses[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for PAN

	// no validation rules for ProfileImageUrl

	if all {
		switch v := interface{}(m.GetMotherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMotherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileValidationError{
				field:  "MotherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFatherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFatherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileValidationError{
				field:  "FatherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrivacySettings()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "PrivacySettings",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "PrivacySettings",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrivacySettings()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileValidationError{
				field:  "PrivacySettings",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLegalName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "LegalName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "LegalName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLegalName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileValidationError{
				field:  "LegalName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoto()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "Photo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "Photo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoto()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileValidationError{
				field:  "Photo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetKycName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "KycName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "KycName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKycName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileValidationError{
				field:  "KycName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPanName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "PanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "PanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPanName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileValidationError{
				field:  "PanName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLegalNameByUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "LegalNameByUser",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "LegalNameByUser",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLegalNameByUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileValidationError{
				field:  "LegalNameByUser",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDebitCardName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "DebitCardName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "DebitCardName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDebitCardName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileValidationError{
				field:  "DebitCardName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSalaryRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "SalaryRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "SalaryRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSalaryRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileValidationError{
				field:  "SalaryRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGmailName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "GmailName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "GmailName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGmailName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileValidationError{
				field:  "GmailName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProfileImageS3FilePath

	// no validation rules for HashedPhoneNumber

	// no validation rules for KycGender

	if all {
		switch v := interface{}(m.GetGivenName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "GivenName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfileValidationError{
					field:  "GivenName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGivenName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfileValidationError{
				field:  "GivenName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GivenGender

	// no validation rules for Qualification

	// no validation rules for Designation

	// no validation rules for Community

	// no validation rules for Religion

	// no validation rules for Category

	// no validation rules for DisabilityType

	if len(errors) > 0 {
		return ProfileMultiError(errors)
	}

	return nil
}

// ProfileMultiError is an error wrapping multiple validation errors returned
// by Profile.ValidateAll() if the designated constraints aren't met.
type ProfileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProfileMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProfileMultiError) AllErrors() []error { return m }

// ProfileValidationError is the validation error returned by Profile.Validate
// if the designated constraints aren't met.
type ProfileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProfileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProfileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProfileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProfileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProfileValidationError) ErrorName() string { return "ProfileValidationError" }

// Error satisfies the builtin error interface
func (e ProfileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProfile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProfileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProfileValidationError{}

// Validate checks the field values on BankCustomerInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BankCustomerInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankCustomerInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BankCustomerInfoMultiError, or nil if none found.
func (m *BankCustomerInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *BankCustomerInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Vendor

	// no validation rules for CustomerCreationStatus

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankCustomerInfoValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankCustomerInfoValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankCustomerInfoValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomerType

	// no validation rules for OriginalKycLevelWithVendor

	// no validation rules for KycLevel

	if all {
		switch v := interface{}(m.GetCreationStartedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankCustomerInfoValidationError{
					field:  "CreationStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankCustomerInfoValidationError{
					field:  "CreationStartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreationStartedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankCustomerInfoValidationError{
				field:  "CreationStartedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorCreationSucceededAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankCustomerInfoValidationError{
					field:  "VendorCreationSucceededAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankCustomerInfoValidationError{
					field:  "VendorCreationSucceededAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorCreationSucceededAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankCustomerInfoValidationError{
				field:  "VendorCreationSucceededAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFiCreationSucceededAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BankCustomerInfoValidationError{
					field:  "FiCreationSucceededAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BankCustomerInfoValidationError{
					field:  "FiCreationSucceededAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFiCreationSucceededAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BankCustomerInfoValidationError{
				field:  "FiCreationSucceededAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EkycRrnNo

	// no validation rules for KycLevelUpdateFlow

	if len(errors) > 0 {
		return BankCustomerInfoMultiError(errors)
	}

	return nil
}

// BankCustomerInfoMultiError is an error wrapping multiple validation errors
// returned by BankCustomerInfo.ValidateAll() if the designated constraints
// aren't met.
type BankCustomerInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankCustomerInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankCustomerInfoMultiError) AllErrors() []error { return m }

// BankCustomerInfoValidationError is the validation error returned by
// BankCustomerInfo.Validate if the designated constraints aren't met.
type BankCustomerInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankCustomerInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankCustomerInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankCustomerInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankCustomerInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankCustomerInfoValidationError) ErrorName() string { return "BankCustomerInfoValidationError" }

// Error satisfies the builtin error interface
func (e BankCustomerInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankCustomerInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankCustomerInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankCustomerInfoValidationError{}

// Validate checks the field values on QueueRetryInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *QueueRetryInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueueRetryInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QueueRetryInfoMultiError,
// or nil if none found.
func (m *QueueRetryInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *QueueRetryInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for QueueMsgId

	// no validation rules for ReqId

	// no validation rules for Attempts

	if len(errors) > 0 {
		return QueueRetryInfoMultiError(errors)
	}

	return nil
}

// QueueRetryInfoMultiError is an error wrapping multiple validation errors
// returned by QueueRetryInfo.ValidateAll() if the designated constraints
// aren't met.
type QueueRetryInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueueRetryInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueueRetryInfoMultiError) AllErrors() []error { return m }

// QueueRetryInfoValidationError is the validation error returned by
// QueueRetryInfo.Validate if the designated constraints aren't met.
type QueueRetryInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueueRetryInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueueRetryInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueueRetryInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueueRetryInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueueRetryInfoValidationError) ErrorName() string { return "QueueRetryInfoValidationError" }

// Error satisfies the builtin error interface
func (e QueueRetryInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueueRetryInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueueRetryInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueueRetryInfoValidationError{}

// Validate checks the field values on ShippingPreference with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ShippingPreference) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShippingPreference with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShippingPreferenceMultiError, or nil if none found.
func (m *ShippingPreference) ValidateAll() error {
	return m.validate(true)
}

func (m *ShippingPreference) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := ShippingPreferenceValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ShippingItem

	// no validation rules for AddressType

	if len(errors) > 0 {
		return ShippingPreferenceMultiError(errors)
	}

	return nil
}

// ShippingPreferenceMultiError is an error wrapping multiple validation errors
// returned by ShippingPreference.ValidateAll() if the designated constraints
// aren't met.
type ShippingPreferenceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShippingPreferenceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShippingPreferenceMultiError) AllErrors() []error { return m }

// ShippingPreferenceValidationError is the validation error returned by
// ShippingPreference.Validate if the designated constraints aren't met.
type ShippingPreferenceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShippingPreferenceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShippingPreferenceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShippingPreferenceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShippingPreferenceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShippingPreferenceValidationError) ErrorName() string {
	return "ShippingPreferenceValidationError"
}

// Error satisfies the builtin error interface
func (e ShippingPreferenceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShippingPreference.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShippingPreferenceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShippingPreferenceValidationError{}

// Validate checks the field values on UserUpdateEvent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UserUpdateEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserUpdateEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserUpdateEventMultiError, or nil if none found.
func (m *UserUpdateEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *UserUpdateEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EventId

	if all {
		switch v := interface{}(m.GetEventTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserUpdateEventValidationError{
					field:  "EventTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserUpdateEventValidationError{
					field:  "EventTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEventTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserUpdateEventValidationError{
				field:  "EventTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserUpdateEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserUpdateEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserUpdateEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for UserId

	// no validation rules for FedCustomerId

	// no validation rules for KycLevel

	if all {
		switch v := interface{}(m.GetVendorStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserUpdateEventValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserUpdateEventValidationError{
					field:  "VendorStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserUpdateEventValidationError{
				field:  "VendorStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomerCreationStatus

	if len(errors) > 0 {
		return UserUpdateEventMultiError(errors)
	}

	return nil
}

// UserUpdateEventMultiError is an error wrapping multiple validation errors
// returned by UserUpdateEvent.ValidateAll() if the designated constraints
// aren't met.
type UserUpdateEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserUpdateEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserUpdateEventMultiError) AllErrors() []error { return m }

// UserUpdateEventValidationError is the validation error returned by
// UserUpdateEvent.Validate if the designated constraints aren't met.
type UserUpdateEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserUpdateEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserUpdateEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserUpdateEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserUpdateEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserUpdateEventValidationError) ErrorName() string { return "UserUpdateEventValidationError" }

// Error satisfies the builtin error interface
func (e UserUpdateEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserUpdateEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserUpdateEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserUpdateEventValidationError{}

// Validate checks the field values on UserDeviceProperty with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UserDeviceProperty) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserDeviceProperty with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserDevicePropertyMultiError, or nil if none found.
func (m *UserDeviceProperty) ValidateAll() error {
	return m.validate(true)
}

func (m *UserDeviceProperty) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for PhoneNumber

	// no validation rules for DeviceProperty

	if all {
		switch v := interface{}(m.GetPropertyValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserDevicePropertyValidationError{
					field:  "PropertyValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserDevicePropertyValidationError{
					field:  "PropertyValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPropertyValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserDevicePropertyValidationError{
				field:  "PropertyValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeletedAtUnix

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserDevicePropertyValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserDevicePropertyValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserDevicePropertyValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UserDevicePropertyMultiError(errors)
	}

	return nil
}

// UserDevicePropertyMultiError is an error wrapping multiple validation errors
// returned by UserDeviceProperty.ValidateAll() if the designated constraints
// aren't met.
type UserDevicePropertyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserDevicePropertyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserDevicePropertyMultiError) AllErrors() []error { return m }

// UserDevicePropertyValidationError is the validation error returned by
// UserDeviceProperty.Validate if the designated constraints aren't met.
type UserDevicePropertyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserDevicePropertyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserDevicePropertyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserDevicePropertyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserDevicePropertyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserDevicePropertyValidationError) ErrorName() string {
	return "UserDevicePropertyValidationError"
}

// Error satisfies the builtin error interface
func (e UserDevicePropertyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserDeviceProperty.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserDevicePropertyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserDevicePropertyValidationError{}

// Validate checks the field values on AcquisitionInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AcquisitionInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AcquisitionInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AcquisitionInfoMultiError, or nil if none found.
func (m *AcquisitionInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AcquisitionInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Platform

	// no validation rules for AcquisitionSource

	// no validation rules for WebUrl

	// no validation rules for AcquisitionChannel

	// no validation rules for AcquisitionIntent

	// no validation rules for AcquisitionIntentRaw

	if all {
		switch v := interface{}(m.GetAttributionDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AcquisitionInfoValidationError{
					field:  "AttributionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AcquisitionInfoValidationError{
					field:  "AttributionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAttributionDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AcquisitionInfoValidationError{
				field:  "AttributionDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AcquisitionInfoMultiError(errors)
	}

	return nil
}

// AcquisitionInfoMultiError is an error wrapping multiple validation errors
// returned by AcquisitionInfo.ValidateAll() if the designated constraints
// aren't met.
type AcquisitionInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AcquisitionInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AcquisitionInfoMultiError) AllErrors() []error { return m }

// AcquisitionInfoValidationError is the validation error returned by
// AcquisitionInfo.Validate if the designated constraints aren't met.
type AcquisitionInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AcquisitionInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AcquisitionInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AcquisitionInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AcquisitionInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AcquisitionInfoValidationError) ErrorName() string { return "AcquisitionInfoValidationError" }

// Error satisfies the builtin error interface
func (e AcquisitionInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAcquisitionInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AcquisitionInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AcquisitionInfoValidationError{}

// Validate checks the field values on AttributionDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AttributionDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttributionDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AttributionDetailsMultiError, or nil if none found.
func (m *AttributionDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AttributionDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAppsflyerAttributionData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AttributionDetailsValidationError{
					field:  "AppsflyerAttributionData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AttributionDetailsValidationError{
					field:  "AppsflyerAttributionData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAppsflyerAttributionData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AttributionDetailsValidationError{
				field:  "AppsflyerAttributionData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInstallReferrerData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AttributionDetailsValidationError{
					field:  "InstallReferrerData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AttributionDetailsValidationError{
					field:  "InstallReferrerData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInstallReferrerData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AttributionDetailsValidationError{
				field:  "InstallReferrerData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AttributionDetailsMultiError(errors)
	}

	return nil
}

// AttributionDetailsMultiError is an error wrapping multiple validation errors
// returned by AttributionDetails.ValidateAll() if the designated constraints
// aren't met.
type AttributionDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttributionDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttributionDetailsMultiError) AllErrors() []error { return m }

// AttributionDetailsValidationError is the validation error returned by
// AttributionDetails.Validate if the designated constraints aren't met.
type AttributionDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttributionDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttributionDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttributionDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttributionDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttributionDetailsValidationError) ErrorName() string {
	return "AttributionDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AttributionDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttributionDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttributionDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttributionDetailsValidationError{}

// Validate checks the field values on DataVerificationDetail_EmploymentDetail
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DataVerificationDetail_EmploymentDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DataVerificationDetail_EmploymentDetail with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// DataVerificationDetail_EmploymentDetailMultiError, or nil if none found.
func (m *DataVerificationDetail_EmploymentDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *DataVerificationDetail_EmploymentDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EmploymentType

	// no validation rules for OrganizationName

	if all {
		switch v := interface{}(m.GetMonthlyIncome()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataVerificationDetail_EmploymentDetailValidationError{
					field:  "MonthlyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataVerificationDetail_EmploymentDetailValidationError{
					field:  "MonthlyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMonthlyIncome()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataVerificationDetail_EmploymentDetailValidationError{
				field:  "MonthlyIncome",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for WorkEmail

	// no validation rules for OccupationType

	// no validation rules for GSTIN

	if all {
		switch v := interface{}(m.GetAnnualRevenue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataVerificationDetail_EmploymentDetailValidationError{
					field:  "AnnualRevenue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataVerificationDetail_EmploymentDetailValidationError{
					field:  "AnnualRevenue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnnualRevenue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataVerificationDetail_EmploymentDetailValidationError{
				field:  "AnnualRevenue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DataVerificationDetail_EmploymentDetailMultiError(errors)
	}

	return nil
}

// DataVerificationDetail_EmploymentDetailMultiError is an error wrapping
// multiple validation errors returned by
// DataVerificationDetail_EmploymentDetail.ValidateAll() if the designated
// constraints aren't met.
type DataVerificationDetail_EmploymentDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataVerificationDetail_EmploymentDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataVerificationDetail_EmploymentDetailMultiError) AllErrors() []error { return m }

// DataVerificationDetail_EmploymentDetailValidationError is the validation
// error returned by DataVerificationDetail_EmploymentDetail.Validate if the
// designated constraints aren't met.
type DataVerificationDetail_EmploymentDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataVerificationDetail_EmploymentDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataVerificationDetail_EmploymentDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataVerificationDetail_EmploymentDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataVerificationDetail_EmploymentDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataVerificationDetail_EmploymentDetailValidationError) ErrorName() string {
	return "DataVerificationDetail_EmploymentDetailValidationError"
}

// Error satisfies the builtin error interface
func (e DataVerificationDetail_EmploymentDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataVerificationDetail_EmploymentDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataVerificationDetail_EmploymentDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataVerificationDetail_EmploymentDetailValidationError{}

// Validate checks the field values on DataVerificationDetail_ResidenceDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DataVerificationDetail_ResidenceDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DataVerificationDetail_ResidenceDetails with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// DataVerificationDetail_ResidenceDetailsMultiError, or nil if none found.
func (m *DataVerificationDetail_ResidenceDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *DataVerificationDetail_ResidenceDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResidentialAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataVerificationDetail_ResidenceDetailsValidationError{
					field:  "ResidentialAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataVerificationDetail_ResidenceDetailsValidationError{
					field:  "ResidentialAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResidentialAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataVerificationDetail_ResidenceDetailsValidationError{
				field:  "ResidentialAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMonthlyRent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataVerificationDetail_ResidenceDetailsValidationError{
					field:  "MonthlyRent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataVerificationDetail_ResidenceDetailsValidationError{
					field:  "MonthlyRent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMonthlyRent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataVerificationDetail_ResidenceDetailsValidationError{
				field:  "MonthlyRent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DataVerificationDetail_ResidenceDetailsMultiError(errors)
	}

	return nil
}

// DataVerificationDetail_ResidenceDetailsMultiError is an error wrapping
// multiple validation errors returned by
// DataVerificationDetail_ResidenceDetails.ValidateAll() if the designated
// constraints aren't met.
type DataVerificationDetail_ResidenceDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataVerificationDetail_ResidenceDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataVerificationDetail_ResidenceDetailsMultiError) AllErrors() []error { return m }

// DataVerificationDetail_ResidenceDetailsValidationError is the validation
// error returned by DataVerificationDetail_ResidenceDetails.Validate if the
// designated constraints aren't met.
type DataVerificationDetail_ResidenceDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataVerificationDetail_ResidenceDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataVerificationDetail_ResidenceDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataVerificationDetail_ResidenceDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataVerificationDetail_ResidenceDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataVerificationDetail_ResidenceDetailsValidationError) ErrorName() string {
	return "DataVerificationDetail_ResidenceDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e DataVerificationDetail_ResidenceDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataVerificationDetail_ResidenceDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataVerificationDetail_ResidenceDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataVerificationDetail_ResidenceDetailsValidationError{}
