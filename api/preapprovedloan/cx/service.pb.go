// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/preapprovedloan/cx/service.proto

package cx

import (
	rpc "github.com/epifi/be-common/api/rpc"
	persistentqueue "github.com/epifi/gamma/api/persistentqueue"
	preapprovedloan "github.com/epifi/gamma/api/preapprovedloan"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	fiftyfin "github.com/epifi/gamma/api/vendors/fiftyfin"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SubmitManualReviewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId         string                                     `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	OrchId          string                                     `protobuf:"bytes,2,opt,name=orch_id,json=orchId,proto3" json:"orch_id,omitempty"`
	Verdict         Verdict                                    `protobuf:"varint,3,opt,name=verdict,proto3,enum=preapprovedloan.cx.Verdict" json:"verdict,omitempty"`
	ReviewerDetails *SubmitManualReviewRequest_ReviewerDetails `protobuf:"bytes,4,opt,name=reviewer_details,json=reviewerDetails,proto3" json:"reviewer_details,omitempty"`
}

func (x *SubmitManualReviewRequest) Reset() {
	*x = SubmitManualReviewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitManualReviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitManualReviewRequest) ProtoMessage() {}

func (x *SubmitManualReviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitManualReviewRequest.ProtoReflect.Descriptor instead.
func (*SubmitManualReviewRequest) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{0}
}

func (x *SubmitManualReviewRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *SubmitManualReviewRequest) GetOrchId() string {
	if x != nil {
		return x.OrchId
	}
	return ""
}

func (x *SubmitManualReviewRequest) GetVerdict() Verdict {
	if x != nil {
		return x.Verdict
	}
	return Verdict_VERDICT_UNSPECIFIED
}

func (x *SubmitManualReviewRequest) GetReviewerDetails() *SubmitManualReviewRequest_ReviewerDetails {
	if x != nil {
		return x.ReviewerDetails
	}
	return nil
}

type SubmitManualReviewResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *SubmitManualReviewResponse) Reset() {
	*x = SubmitManualReviewResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitManualReviewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitManualReviewResponse) ProtoMessage() {}

func (x *SubmitManualReviewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitManualReviewResponse.ProtoReflect.Descriptor instead.
func (*SubmitManualReviewResponse) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{1}
}

func (x *SubmitManualReviewResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetLoanOfferSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string                 `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Vendor  preapprovedloan.Vendor `protobuf:"varint,2,opt,name=vendor,proto3,enum=preapprovedloan.Vendor" json:"vendor,omitempty"`
}

func (x *GetLoanOfferSummaryRequest) Reset() {
	*x = GetLoanOfferSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanOfferSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanOfferSummaryRequest) ProtoMessage() {}

func (x *GetLoanOfferSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanOfferSummaryRequest.ProtoReflect.Descriptor instead.
func (*GetLoanOfferSummaryRequest) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetLoanOfferSummaryRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetLoanOfferSummaryRequest) GetVendor() preapprovedloan.Vendor {
	if x != nil {
		return x.Vendor
	}
	return preapprovedloan.Vendor(0)
}

type GetLoanOfferSummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status    *rpc.Status                                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LoanOffer *GetLoanOfferSummaryResponse_LoanOfferForCX `protobuf:"bytes,2,opt,name=loanOffer,proto3" json:"loanOffer,omitempty"`
}

func (x *GetLoanOfferSummaryResponse) Reset() {
	*x = GetLoanOfferSummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanOfferSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanOfferSummaryResponse) ProtoMessage() {}

func (x *GetLoanOfferSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanOfferSummaryResponse.ProtoReflect.Descriptor instead.
func (*GetLoanOfferSummaryResponse) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetLoanOfferSummaryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLoanOfferSummaryResponse) GetLoanOffer() *GetLoanOfferSummaryResponse_LoanOfferForCX {
	if x != nil {
		return x.LoanOffer
	}
	return nil
}

type GetLoanRequestSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string                 `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Vendor  preapprovedloan.Vendor `protobuf:"varint,2,opt,name=vendor,proto3,enum=preapprovedloan.Vendor" json:"vendor,omitempty"`
}

func (x *GetLoanRequestSummaryRequest) Reset() {
	*x = GetLoanRequestSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanRequestSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanRequestSummaryRequest) ProtoMessage() {}

func (x *GetLoanRequestSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanRequestSummaryRequest.ProtoReflect.Descriptor instead.
func (*GetLoanRequestSummaryRequest) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetLoanRequestSummaryRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetLoanRequestSummaryRequest) GetVendor() preapprovedloan.Vendor {
	if x != nil {
		return x.Vendor
	}
	return preapprovedloan.Vendor(0)
}

type GetLoanRequestSummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status       *rpc.Status                                       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LoanRequests []*GetLoanRequestSummaryResponse_LoanRequestForCX `protobuf:"bytes,2,rep,name=loanRequests,proto3" json:"loanRequests,omitempty"`
}

func (x *GetLoanRequestSummaryResponse) Reset() {
	*x = GetLoanRequestSummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanRequestSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanRequestSummaryResponse) ProtoMessage() {}

func (x *GetLoanRequestSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanRequestSummaryResponse.ProtoReflect.Descriptor instead.
func (*GetLoanRequestSummaryResponse) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetLoanRequestSummaryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLoanRequestSummaryResponse) GetLoanRequests() []*GetLoanRequestSummaryResponse_LoanRequestForCX {
	if x != nil {
		return x.LoanRequests
	}
	return nil
}

type GetLoanAccountSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string                 `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Vendor  preapprovedloan.Vendor `protobuf:"varint,2,opt,name=vendor,proto3,enum=preapprovedloan.Vendor" json:"vendor,omitempty"`
}

func (x *GetLoanAccountSummaryRequest) Reset() {
	*x = GetLoanAccountSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanAccountSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanAccountSummaryRequest) ProtoMessage() {}

func (x *GetLoanAccountSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanAccountSummaryRequest.ProtoReflect.Descriptor instead.
func (*GetLoanAccountSummaryRequest) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetLoanAccountSummaryRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetLoanAccountSummaryRequest) GetVendor() preapprovedloan.Vendor {
	if x != nil {
		return x.Vendor
	}
	return preapprovedloan.Vendor(0)
}

type GetLoanAccountSummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status       *rpc.Status                                       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LoanAccounts []*GetLoanAccountSummaryResponse_LoanAccountForCX `protobuf:"bytes,2,rep,name=loanAccounts,proto3" json:"loanAccounts,omitempty"`
}

func (x *GetLoanAccountSummaryResponse) Reset() {
	*x = GetLoanAccountSummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanAccountSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanAccountSummaryResponse) ProtoMessage() {}

func (x *GetLoanAccountSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanAccountSummaryResponse.ProtoReflect.Descriptor instead.
func (*GetLoanAccountSummaryResponse) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetLoanAccountSummaryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLoanAccountSummaryResponse) GetLoanAccounts() []*GetLoanAccountSummaryResponse_LoanAccountForCX {
	if x != nil {
		return x.LoanAccounts
	}
	return nil
}

type GetLoanUserDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId    string                      `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	LoanHeader *preapprovedloan.LoanHeader `protobuf:"bytes,2,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
}

func (x *GetLoanUserDetailsRequest) Reset() {
	*x = GetLoanUserDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanUserDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanUserDetailsRequest) ProtoMessage() {}

func (x *GetLoanUserDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanUserDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetLoanUserDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetLoanUserDetailsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetLoanUserDetailsRequest) GetLoanHeader() *preapprovedloan.LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

type GetLoanUserDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status                                 *rpc.Status                                                        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CurrentMonthUserLoanEligibilityDetails *GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails `protobuf:"bytes,2,opt,name=current_month_user_loan_eligibility_details,json=currentMonthUserLoanEligibilityDetails,proto3" json:"current_month_user_loan_eligibility_details,omitempty"`
	CurrentUserLoanApplicationDetails      *GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails      `protobuf:"bytes,3,opt,name=current_user_loan_application_details,json=currentUserLoanApplicationDetails,proto3" json:"current_user_loan_application_details,omitempty"`
	LoanOfferDetails                       []*GetLoanUserDetailsResponse_LoanOfferDetails                     `protobuf:"bytes,4,rep,name=loan_offer_details,json=loanOfferDetails,proto3" json:"loan_offer_details,omitempty"`
	EligibilityLoanRequests                []*preapprovedloan.LoanRequest                                     `protobuf:"bytes,5,rep,name=eligibility_loan_requests,json=eligibilityLoanRequests,proto3" json:"eligibility_loan_requests,omitempty"`
	ApplicationLoanRequests                []*preapprovedloan.LoanRequest                                     `protobuf:"bytes,6,rep,name=application_loan_requests,json=applicationLoanRequests,proto3" json:"application_loan_requests,omitempty"`
	Applicants                             []*preapprovedloan.LoanApplicant                                   `protobuf:"bytes,7,rep,name=applicants,proto3" json:"applicants,omitempty"`
}

func (x *GetLoanUserDetailsResponse) Reset() {
	*x = GetLoanUserDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanUserDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanUserDetailsResponse) ProtoMessage() {}

func (x *GetLoanUserDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanUserDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetLoanUserDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetLoanUserDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLoanUserDetailsResponse) GetCurrentMonthUserLoanEligibilityDetails() *GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails {
	if x != nil {
		return x.CurrentMonthUserLoanEligibilityDetails
	}
	return nil
}

func (x *GetLoanUserDetailsResponse) GetCurrentUserLoanApplicationDetails() *GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails {
	if x != nil {
		return x.CurrentUserLoanApplicationDetails
	}
	return nil
}

func (x *GetLoanUserDetailsResponse) GetLoanOfferDetails() []*GetLoanUserDetailsResponse_LoanOfferDetails {
	if x != nil {
		return x.LoanOfferDetails
	}
	return nil
}

func (x *GetLoanUserDetailsResponse) GetEligibilityLoanRequests() []*preapprovedloan.LoanRequest {
	if x != nil {
		return x.EligibilityLoanRequests
	}
	return nil
}

func (x *GetLoanUserDetailsResponse) GetApplicationLoanRequests() []*preapprovedloan.LoanRequest {
	if x != nil {
		return x.ApplicationLoanRequests
	}
	return nil
}

func (x *GetLoanUserDetailsResponse) GetApplicants() []*preapprovedloan.LoanApplicant {
	if x != nil {
		return x.Applicants
	}
	return nil
}

type GetLoanDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId    string                      `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	LoanHeader *preapprovedloan.LoanHeader `protobuf:"bytes,2,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
}

func (x *GetLoanDetailsRequest) Reset() {
	*x = GetLoanDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDetailsRequest) ProtoMessage() {}

func (x *GetLoanDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetLoanDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetLoanDetailsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetLoanDetailsRequest) GetLoanHeader() *preapprovedloan.LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

type GetLoanDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status      *rpc.Status                                `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LoanDetails []*GetLoanDetailsResponse_LoanDetailsForCx `protobuf:"bytes,2,rep,name=loan_details,json=loanDetails,proto3" json:"loan_details,omitempty"`
}

func (x *GetLoanDetailsResponse) Reset() {
	*x = GetLoanDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDetailsResponse) ProtoMessage() {}

func (x *GetLoanDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetLoanDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetLoanDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLoanDetailsResponse) GetLoanDetails() []*GetLoanDetailsResponse_LoanDetailsForCx {
	if x != nil {
		return x.LoanDetails
	}
	return nil
}

type ForeclosureDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalOutstandingAmount     *money.Money `protobuf:"bytes,1,opt,name=total_outstanding_amount,json=totalOutstandingAmount,proto3" json:"total_outstanding_amount,omitempty"`
	PrincipalOutstandingAmount *money.Money `protobuf:"bytes,2,opt,name=principal_outstanding_amount,json=principalOutstandingAmount,proto3" json:"principal_outstanding_amount,omitempty"`
	InterestOutstandingAmount  *money.Money `protobuf:"bytes,3,opt,name=interest_outstanding_amount,json=interestOutstandingAmount,proto3" json:"interest_outstanding_amount,omitempty"`
	PenaltyAmt                 *money.Money `protobuf:"bytes,4,opt,name=penalty_amt,json=penaltyAmt,proto3" json:"penalty_amt,omitempty"`
	FeesAmt                    *money.Money `protobuf:"bytes,5,opt,name=fees_amt,json=feesAmt,proto3" json:"fees_amt,omitempty"`
	OtherCharges               *money.Money `protobuf:"bytes,6,opt,name=other_charges,json=otherCharges,proto3" json:"other_charges,omitempty"`
}

func (x *ForeclosureDetails) Reset() {
	*x = ForeclosureDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForeclosureDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForeclosureDetails) ProtoMessage() {}

func (x *ForeclosureDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForeclosureDetails.ProtoReflect.Descriptor instead.
func (*ForeclosureDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{12}
}

func (x *ForeclosureDetails) GetTotalOutstandingAmount() *money.Money {
	if x != nil {
		return x.TotalOutstandingAmount
	}
	return nil
}

func (x *ForeclosureDetails) GetPrincipalOutstandingAmount() *money.Money {
	if x != nil {
		return x.PrincipalOutstandingAmount
	}
	return nil
}

func (x *ForeclosureDetails) GetInterestOutstandingAmount() *money.Money {
	if x != nil {
		return x.InterestOutstandingAmount
	}
	return nil
}

func (x *ForeclosureDetails) GetPenaltyAmt() *money.Money {
	if x != nil {
		return x.PenaltyAmt
	}
	return nil
}

func (x *ForeclosureDetails) GetFeesAmt() *money.Money {
	if x != nil {
		return x.FeesAmt
	}
	return nil
}

func (x *ForeclosureDetails) GetOtherCharges() *money.Money {
	if x != nil {
		return x.OtherCharges
	}
	return nil
}

type GetQueueElementsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// type of payload required from the queue
	PayloadType persistentqueue.PayloadType `protobuf:"varint,1,opt,name=payload_type,json=payloadType,proto3,enum=persistentqueue.PayloadType" json:"payload_type,omitempty"`
	// Number of elements required from the queue, max supported is 100
	Limit   int32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	PageNum int32 `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	// From time for date filter
	FromTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=from_time,json=fromTime,proto3" json:"from_time,omitempty"`
	// To time for date filter
	ToTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=to_time,json=toTime,proto3" json:"to_time,omitempty"`
}

func (x *GetQueueElementsRequest) Reset() {
	*x = GetQueueElementsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQueueElementsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQueueElementsRequest) ProtoMessage() {}

func (x *GetQueueElementsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQueueElementsRequest.ProtoReflect.Descriptor instead.
func (*GetQueueElementsRequest) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetQueueElementsRequest) GetPayloadType() persistentqueue.PayloadType {
	if x != nil {
		return x.PayloadType
	}
	return persistentqueue.PayloadType(0)
}

func (x *GetQueueElementsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetQueueElementsRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *GetQueueElementsRequest) GetFromTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTime
	}
	return nil
}

func (x *GetQueueElementsRequest) GetToTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ToTime
	}
	return nil
}

type GetQueueElementsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// queue elemnet is either for liveness or facematch
	Elements []*GetQueueElementsResponse_QueueElement `protobuf:"bytes,2,rep,name=elements,proto3" json:"elements,omitempty"`
}

func (x *GetQueueElementsResponse) Reset() {
	*x = GetQueueElementsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQueueElementsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQueueElementsResponse) ProtoMessage() {}

func (x *GetQueueElementsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQueueElementsResponse.ProtoReflect.Descriptor instead.
func (*GetQueueElementsResponse) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetQueueElementsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetQueueElementsResponse) GetElements() []*GetQueueElementsResponse_QueueElement {
	if x != nil {
		return x.Elements
	}
	return nil
}

type MarkLoanRequestCancelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanRequestId string `protobuf:"bytes,1,opt,name=loan_request_id,json=loanRequestId,proto3" json:"loan_request_id,omitempty"`
}

func (x *MarkLoanRequestCancelRequest) Reset() {
	*x = MarkLoanRequestCancelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkLoanRequestCancelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkLoanRequestCancelRequest) ProtoMessage() {}

func (x *MarkLoanRequestCancelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkLoanRequestCancelRequest.ProtoReflect.Descriptor instead.
func (*MarkLoanRequestCancelRequest) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{15}
}

func (x *MarkLoanRequestCancelRequest) GetLoanRequestId() string {
	if x != nil {
		return x.LoanRequestId
	}
	return ""
}

type MarkLoanRequestCancelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *MarkLoanRequestCancelResponse) Reset() {
	*x = MarkLoanRequestCancelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkLoanRequestCancelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkLoanRequestCancelResponse) ProtoMessage() {}

func (x *MarkLoanRequestCancelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkLoanRequestCancelResponse.ProtoReflect.Descriptor instead.
func (*MarkLoanRequestCancelResponse) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{16}
}

func (x *MarkLoanRequestCancelResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type MandateAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Masked account no of mandate account. Only last 4 digits of account number will be exposed
	MaskedAccNo string `protobuf:"bytes,1,opt,name=masked_acc_no,json=maskedAccNo,proto3" json:"masked_acc_no,omitempty"`
	BankName    string `protobuf:"bytes,2,opt,name=bank_name,json=bankName,proto3" json:"bank_name,omitempty"`
}

func (x *MandateAccount) Reset() {
	*x = MandateAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MandateAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MandateAccount) ProtoMessage() {}

func (x *MandateAccount) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MandateAccount.ProtoReflect.Descriptor instead.
func (*MandateAccount) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{17}
}

func (x *MandateAccount) GetMaskedAccNo() string {
	if x != nil {
		return x.MaskedAccNo
	}
	return ""
}

func (x *MandateAccount) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

type GetForeclosureRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanAccountNumber string                      `protobuf:"bytes,1,opt,name=loan_account_number,json=loanAccountNumber,proto3" json:"loan_account_number,omitempty"`
	LoanHeader        *preapprovedloan.LoanHeader `protobuf:"bytes,2,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
}

func (x *GetForeclosureRequest) Reset() {
	*x = GetForeclosureRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetForeclosureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetForeclosureRequest) ProtoMessage() {}

func (x *GetForeclosureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetForeclosureRequest.ProtoReflect.Descriptor instead.
func (*GetForeclosureRequest) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetForeclosureRequest) GetLoanAccountNumber() string {
	if x != nil {
		return x.LoanAccountNumber
	}
	return ""
}

func (x *GetForeclosureRequest) GetLoanHeader() *preapprovedloan.LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

type GetForeclosureResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                  *rpc.Status  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	TotalOutstandingAmt     *money.Money `protobuf:"bytes,2,opt,name=total_outstanding_amt,json=totalOutstandingAmt,proto3" json:"total_outstanding_amt,omitempty"`
	PrincipalOutstandingAmt *money.Money `protobuf:"bytes,3,opt,name=principal_outstanding_amt,json=principalOutstandingAmt,proto3" json:"principal_outstanding_amt,omitempty"`
	InterestOutstandingAmt  *money.Money `protobuf:"bytes,4,opt,name=interest_outstanding_amt,json=interestOutstandingAmt,proto3" json:"interest_outstanding_amt,omitempty"`
	PenaltyAmt              *money.Money `protobuf:"bytes,5,opt,name=penalty_amt,json=penaltyAmt,proto3" json:"penalty_amt,omitempty"`
	FeesAmt                 *money.Money `protobuf:"bytes,6,opt,name=fees_amt,json=feesAmt,proto3" json:"fees_amt,omitempty"`
	OtherCharges            *money.Money `protobuf:"bytes,7,opt,name=other_charges,json=otherCharges,proto3" json:"other_charges,omitempty"`
}

func (x *GetForeclosureResponse) Reset() {
	*x = GetForeclosureResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetForeclosureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetForeclosureResponse) ProtoMessage() {}

func (x *GetForeclosureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetForeclosureResponse.ProtoReflect.Descriptor instead.
func (*GetForeclosureResponse) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetForeclosureResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetForeclosureResponse) GetTotalOutstandingAmt() *money.Money {
	if x != nil {
		return x.TotalOutstandingAmt
	}
	return nil
}

func (x *GetForeclosureResponse) GetPrincipalOutstandingAmt() *money.Money {
	if x != nil {
		return x.PrincipalOutstandingAmt
	}
	return nil
}

func (x *GetForeclosureResponse) GetInterestOutstandingAmt() *money.Money {
	if x != nil {
		return x.InterestOutstandingAmt
	}
	return nil
}

func (x *GetForeclosureResponse) GetPenaltyAmt() *money.Money {
	if x != nil {
		return x.PenaltyAmt
	}
	return nil
}

func (x *GetForeclosureResponse) GetFeesAmt() *money.Money {
	if x != nil {
		return x.FeesAmt
	}
	return nil
}

func (x *GetForeclosureResponse) GetOtherCharges() *money.Money {
	if x != nil {
		return x.OtherCharges
	}
	return nil
}

type GetLoanAccountAdditionalDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader *preapprovedloan.LoanHeader `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	AccountId  string                      `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// Common Data will be returned based on the given field masks. If no field mask is given then no common data will be given.
	FieldMasks []LoanAccountAdditionalDetailsFieldMask `protobuf:"varint,3,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=preapprovedloan.cx.LoanAccountAdditionalDetailsFieldMask" json:"field_masks,omitempty"`
}

func (x *GetLoanAccountAdditionalDetailsRequest) Reset() {
	*x = GetLoanAccountAdditionalDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanAccountAdditionalDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanAccountAdditionalDetailsRequest) ProtoMessage() {}

func (x *GetLoanAccountAdditionalDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanAccountAdditionalDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetLoanAccountAdditionalDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{20}
}

func (x *GetLoanAccountAdditionalDetailsRequest) GetLoanHeader() *preapprovedloan.LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *GetLoanAccountAdditionalDetailsRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetLoanAccountAdditionalDetailsRequest) GetFieldMasks() []LoanAccountAdditionalDetailsFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

type GetLoanAccountAdditionalDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                     *rpc.Status                              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LoanAccount                *preapprovedloan.LoanAccount             `protobuf:"bytes,2,opt,name=loan_account,json=loanAccount,proto3" json:"loan_account,omitempty"`
	LoanPaymentRequests        []*preapprovedloan.LoanPaymentRequest    `protobuf:"bytes,3,rep,name=loan_payment_requests,json=loanPaymentRequests,proto3" json:"loan_payment_requests,omitempty"`
	InstallmentPayouts         []*preapprovedloan.LoanInstallmentPayout `protobuf:"bytes,4,rep,name=installment_payouts,json=installmentPayouts,proto3" json:"installment_payouts,omitempty"`
	LoanAccountClosureRequests []*preapprovedloan.LoanRequest           `protobuf:"bytes,5,rep,name=loan_account_closure_requests,json=loanAccountClosureRequests,proto3" json:"loan_account_closure_requests,omitempty"`
	// Types that are assignable to Details:
	//
	//	*GetLoanAccountAdditionalDetailsResponse_FiftyfinLamfDetails
	Details            isGetLoanAccountAdditionalDetailsResponse_Details `protobuf_oneof:"details"`
	LoanUpcomingEmis   []*LoanUpcomingEmi                                `protobuf:"bytes,7,rep,name=loan_upcoming_emis,json=loanUpcomingEmis,proto3" json:"loan_upcoming_emis,omitempty"`
	BankAccountDetails *typesv2.BankAccountDetails                       `protobuf:"bytes,17,opt,name=bank_account_details,json=bankAccountDetails,proto3" json:"bank_account_details,omitempty"`
}

func (x *GetLoanAccountAdditionalDetailsResponse) Reset() {
	*x = GetLoanAccountAdditionalDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanAccountAdditionalDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanAccountAdditionalDetailsResponse) ProtoMessage() {}

func (x *GetLoanAccountAdditionalDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanAccountAdditionalDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetLoanAccountAdditionalDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetLoanAccountAdditionalDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLoanAccountAdditionalDetailsResponse) GetLoanAccount() *preapprovedloan.LoanAccount {
	if x != nil {
		return x.LoanAccount
	}
	return nil
}

func (x *GetLoanAccountAdditionalDetailsResponse) GetLoanPaymentRequests() []*preapprovedloan.LoanPaymentRequest {
	if x != nil {
		return x.LoanPaymentRequests
	}
	return nil
}

func (x *GetLoanAccountAdditionalDetailsResponse) GetInstallmentPayouts() []*preapprovedloan.LoanInstallmentPayout {
	if x != nil {
		return x.InstallmentPayouts
	}
	return nil
}

func (x *GetLoanAccountAdditionalDetailsResponse) GetLoanAccountClosureRequests() []*preapprovedloan.LoanRequest {
	if x != nil {
		return x.LoanAccountClosureRequests
	}
	return nil
}

func (m *GetLoanAccountAdditionalDetailsResponse) GetDetails() isGetLoanAccountAdditionalDetailsResponse_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *GetLoanAccountAdditionalDetailsResponse) GetFiftyfinLamfDetails() *FiftyfinLamfLoanAccountDetails {
	if x, ok := x.GetDetails().(*GetLoanAccountAdditionalDetailsResponse_FiftyfinLamfDetails); ok {
		return x.FiftyfinLamfDetails
	}
	return nil
}

func (x *GetLoanAccountAdditionalDetailsResponse) GetLoanUpcomingEmis() []*LoanUpcomingEmi {
	if x != nil {
		return x.LoanUpcomingEmis
	}
	return nil
}

func (x *GetLoanAccountAdditionalDetailsResponse) GetBankAccountDetails() *typesv2.BankAccountDetails {
	if x != nil {
		return x.BankAccountDetails
	}
	return nil
}

type isGetLoanAccountAdditionalDetailsResponse_Details interface {
	isGetLoanAccountAdditionalDetailsResponse_Details()
}

type GetLoanAccountAdditionalDetailsResponse_FiftyfinLamfDetails struct {
	FiftyfinLamfDetails *FiftyfinLamfLoanAccountDetails `protobuf:"bytes,6,opt,name=fiftyfin_lamf_details,json=fiftyfinLamfDetails,proto3,oneof"`
}

func (*GetLoanAccountAdditionalDetailsResponse_FiftyfinLamfDetails) isGetLoanAccountAdditionalDetailsResponse_Details() {
}

type LoanUpcomingEmi struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NextEmiDate       *date.Date   `protobuf:"bytes,1,opt,name=next_emi_date,json=nextEmiDate,proto3" json:"next_emi_date,omitempty"`
	LoanAccountNumber string       `protobuf:"bytes,2,opt,name=loan_account_number,json=loanAccountNumber,proto3" json:"loan_account_number,omitempty"`
	Amount            *money.Money `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *LoanUpcomingEmi) Reset() {
	*x = LoanUpcomingEmi{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanUpcomingEmi) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanUpcomingEmi) ProtoMessage() {}

func (x *LoanUpcomingEmi) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanUpcomingEmi.ProtoReflect.Descriptor instead.
func (*LoanUpcomingEmi) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{22}
}

func (x *LoanUpcomingEmi) GetNextEmiDate() *date.Date {
	if x != nil {
		return x.NextEmiDate
	}
	return nil
}

func (x *LoanUpcomingEmi) GetLoanAccountNumber() string {
	if x != nil {
		return x.LoanAccountNumber
	}
	return ""
}

func (x *LoanUpcomingEmi) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

type FiftyfinLamfLoanAccountDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Soa                         *FiftyfinLamfLoanAccountDetails_LoanStatement `protobuf:"bytes,1,opt,name=soa,proto3" json:"soa,omitempty"`
	IsLoanAccountClosedOnVendor bool                                          `protobuf:"varint,2,opt,name=is_loan_account_closed_on_vendor,json=isLoanAccountClosedOnVendor,proto3" json:"is_loan_account_closed_on_vendor,omitempty"`
}

func (x *FiftyfinLamfLoanAccountDetails) Reset() {
	*x = FiftyfinLamfLoanAccountDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiftyfinLamfLoanAccountDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiftyfinLamfLoanAccountDetails) ProtoMessage() {}

func (x *FiftyfinLamfLoanAccountDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiftyfinLamfLoanAccountDetails.ProtoReflect.Descriptor instead.
func (*FiftyfinLamfLoanAccountDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{23}
}

func (x *FiftyfinLamfLoanAccountDetails) GetSoa() *FiftyfinLamfLoanAccountDetails_LoanStatement {
	if x != nil {
		return x.Soa
	}
	return nil
}

func (x *FiftyfinLamfLoanAccountDetails) GetIsLoanAccountClosedOnVendor() bool {
	if x != nil {
		return x.IsLoanAccountClosedOnVendor
	}
	return false
}

type GetLoanRequestAdditionalDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader    *preapprovedloan.LoanHeader `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	LoanRequestId string                      `protobuf:"bytes,2,opt,name=loan_request_id,json=loanRequestId,proto3" json:"loan_request_id,omitempty"`
	// Common Data will be returned based on the given field masks. If no field mask is given then no common data will be given.
	FieldMasks []LoanRequestAdditionalDetailsFieldMask `protobuf:"varint,3,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=preapprovedloan.cx.LoanRequestAdditionalDetailsFieldMask" json:"field_masks,omitempty"`
}

func (x *GetLoanRequestAdditionalDetailsRequest) Reset() {
	*x = GetLoanRequestAdditionalDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanRequestAdditionalDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanRequestAdditionalDetailsRequest) ProtoMessage() {}

func (x *GetLoanRequestAdditionalDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanRequestAdditionalDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetLoanRequestAdditionalDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetLoanRequestAdditionalDetailsRequest) GetLoanHeader() *preapprovedloan.LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *GetLoanRequestAdditionalDetailsRequest) GetLoanRequestId() string {
	if x != nil {
		return x.LoanRequestId
	}
	return ""
}

func (x *GetLoanRequestAdditionalDetailsRequest) GetFieldMasks() []LoanRequestAdditionalDetailsFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

type GetLoanRequestAdditionalDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status                          `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LoanRequest *preapprovedloan.LoanRequest         `protobuf:"bytes,2,opt,name=loan_request,json=loanRequest,proto3" json:"loan_request,omitempty"`
	LoanSteps   []*preapprovedloan.LoanStepExecution `protobuf:"bytes,3,rep,name=loan_steps,json=loanSteps,proto3" json:"loan_steps,omitempty"`
}

func (x *GetLoanRequestAdditionalDetailsResponse) Reset() {
	*x = GetLoanRequestAdditionalDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanRequestAdditionalDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanRequestAdditionalDetailsResponse) ProtoMessage() {}

func (x *GetLoanRequestAdditionalDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanRequestAdditionalDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetLoanRequestAdditionalDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetLoanRequestAdditionalDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLoanRequestAdditionalDetailsResponse) GetLoanRequest() *preapprovedloan.LoanRequest {
	if x != nil {
		return x.LoanRequest
	}
	return nil
}

func (x *GetLoanRequestAdditionalDetailsResponse) GetLoanSteps() []*preapprovedloan.LoanStepExecution {
	if x != nil {
		return x.LoanSteps
	}
	return nil
}

type SubmitManualReviewRequest_ReviewerDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email      string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	ReviewedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=reviewed_at,json=reviewedAt,proto3" json:"reviewed_at,omitempty"`
	Reason     string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *SubmitManualReviewRequest_ReviewerDetails) Reset() {
	*x = SubmitManualReviewRequest_ReviewerDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitManualReviewRequest_ReviewerDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitManualReviewRequest_ReviewerDetails) ProtoMessage() {}

func (x *SubmitManualReviewRequest_ReviewerDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitManualReviewRequest_ReviewerDetails.ProtoReflect.Descriptor instead.
func (*SubmitManualReviewRequest_ReviewerDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *SubmitManualReviewRequest_ReviewerDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *SubmitManualReviewRequest_ReviewerDetails) GetReviewedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ReviewedAt
	}
	return nil
}

func (x *SubmitManualReviewRequest_ReviewerDetails) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type GetLoanOfferSummaryResponse_LoanOfferForCX struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VendorOfferId    string                               `protobuf:"bytes,1,opt,name=vendor_offer_id,json=vendorOfferId,proto3" json:"vendor_offer_id,omitempty"`
	Vendor           preapprovedloan.Vendor               `protobuf:"varint,2,opt,name=vendor,proto3,enum=preapprovedloan.Vendor" json:"vendor,omitempty"`
	OfferConstraints *preapprovedloan.OfferConstraints    `protobuf:"bytes,3,opt,name=offer_constraints,json=offerConstraints,proto3" json:"offer_constraints,omitempty"`
	ProcessingInfo   *preapprovedloan.OfferProcessingInfo `protobuf:"bytes,4,opt,name=processing_info,json=processingInfo,proto3" json:"processing_info,omitempty"`
}

func (x *GetLoanOfferSummaryResponse_LoanOfferForCX) Reset() {
	*x = GetLoanOfferSummaryResponse_LoanOfferForCX{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanOfferSummaryResponse_LoanOfferForCX) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanOfferSummaryResponse_LoanOfferForCX) ProtoMessage() {}

func (x *GetLoanOfferSummaryResponse_LoanOfferForCX) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanOfferSummaryResponse_LoanOfferForCX.ProtoReflect.Descriptor instead.
func (*GetLoanOfferSummaryResponse_LoanOfferForCX) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{3, 0}
}

func (x *GetLoanOfferSummaryResponse_LoanOfferForCX) GetVendorOfferId() string {
	if x != nil {
		return x.VendorOfferId
	}
	return ""
}

func (x *GetLoanOfferSummaryResponse_LoanOfferForCX) GetVendor() preapprovedloan.Vendor {
	if x != nil {
		return x.Vendor
	}
	return preapprovedloan.Vendor(0)
}

func (x *GetLoanOfferSummaryResponse_LoanOfferForCX) GetOfferConstraints() *preapprovedloan.OfferConstraints {
	if x != nil {
		return x.OfferConstraints
	}
	return nil
}

func (x *GetLoanOfferSummaryResponse_LoanOfferForCX) GetProcessingInfo() *preapprovedloan.OfferProcessingInfo {
	if x != nil {
		return x.ProcessingInfo
	}
	return nil
}

type GetLoanRequestSummaryResponse_LoanRequestForCX struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OfferId         string                               `protobuf:"bytes,1,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	LoanAccountId   string                               `protobuf:"bytes,2,opt,name=loan_account_id,json=loanAccountId,proto3" json:"loan_account_id,omitempty"`
	VendorRequestId string                               `protobuf:"bytes,3,opt,name=vendor_request_id,json=vendorRequestId,proto3" json:"vendor_request_id,omitempty"`
	Vendor          preapprovedloan.Vendor               `protobuf:"varint,4,opt,name=vendor,proto3,enum=preapprovedloan.Vendor" json:"vendor,omitempty"`
	Details         *preapprovedloan.LoanRequestDetails  `protobuf:"bytes,5,opt,name=details,proto3" json:"details,omitempty"`
	Type            preapprovedloan.LoanRequestType      `protobuf:"varint,6,opt,name=type,proto3,enum=preapprovedloan.LoanRequestType" json:"type,omitempty"`
	Status          preapprovedloan.LoanRequestStatus    `protobuf:"varint,7,opt,name=status,proto3,enum=preapprovedloan.LoanRequestStatus" json:"status,omitempty"`
	SubStatus       preapprovedloan.LoanRequestSubStatus `protobuf:"varint,8,opt,name=sub_status,json=subStatus,proto3,enum=preapprovedloan.LoanRequestSubStatus" json:"sub_status,omitempty"`
}

func (x *GetLoanRequestSummaryResponse_LoanRequestForCX) Reset() {
	*x = GetLoanRequestSummaryResponse_LoanRequestForCX{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanRequestSummaryResponse_LoanRequestForCX) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanRequestSummaryResponse_LoanRequestForCX) ProtoMessage() {}

func (x *GetLoanRequestSummaryResponse_LoanRequestForCX) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanRequestSummaryResponse_LoanRequestForCX.ProtoReflect.Descriptor instead.
func (*GetLoanRequestSummaryResponse_LoanRequestForCX) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{5, 0}
}

func (x *GetLoanRequestSummaryResponse_LoanRequestForCX) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *GetLoanRequestSummaryResponse_LoanRequestForCX) GetLoanAccountId() string {
	if x != nil {
		return x.LoanAccountId
	}
	return ""
}

func (x *GetLoanRequestSummaryResponse_LoanRequestForCX) GetVendorRequestId() string {
	if x != nil {
		return x.VendorRequestId
	}
	return ""
}

func (x *GetLoanRequestSummaryResponse_LoanRequestForCX) GetVendor() preapprovedloan.Vendor {
	if x != nil {
		return x.Vendor
	}
	return preapprovedloan.Vendor(0)
}

func (x *GetLoanRequestSummaryResponse_LoanRequestForCX) GetDetails() *preapprovedloan.LoanRequestDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *GetLoanRequestSummaryResponse_LoanRequestForCX) GetType() preapprovedloan.LoanRequestType {
	if x != nil {
		return x.Type
	}
	return preapprovedloan.LoanRequestType(0)
}

func (x *GetLoanRequestSummaryResponse_LoanRequestForCX) GetStatus() preapprovedloan.LoanRequestStatus {
	if x != nil {
		return x.Status
	}
	return preapprovedloan.LoanRequestStatus(0)
}

func (x *GetLoanRequestSummaryResponse_LoanRequestForCX) GetSubStatus() preapprovedloan.LoanRequestSubStatus {
	if x != nil {
		return x.SubStatus
	}
	return preapprovedloan.LoanRequestSubStatus(0)
}

type GetLoanAccountSummaryResponse_LoanAccountForCX struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountNumber  string                              `protobuf:"bytes,1,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	Vendor         preapprovedloan.Vendor              `protobuf:"varint,2,opt,name=vendor,proto3,enum=preapprovedloan.Vendor" json:"vendor,omitempty"`
	IfscCode       string                              `protobuf:"bytes,3,opt,name=ifsc_code,json=ifscCode,proto3" json:"ifsc_code,omitempty"`
	LoanType       preapprovedloan.LoanType            `protobuf:"varint,4,opt,name=loan_type,json=loanType,proto3,enum=preapprovedloan.LoanType" json:"loan_type,omitempty"`
	LoanAmountInfo *preapprovedloan.LoanAmountInfo     `protobuf:"bytes,5,opt,name=loan_amount_info,json=loanAmountInfo,proto3" json:"loan_amount_info,omitempty"`
	LoanEndDate    *date.Date                          `protobuf:"bytes,6,opt,name=loan_end_date,json=loanEndDate,proto3" json:"loan_end_date,omitempty"`
	MaturityDate   *date.Date                          `protobuf:"bytes,7,opt,name=maturity_date,json=maturityDate,proto3" json:"maturity_date,omitempty"`
	Details        *preapprovedloan.LoanAccountDetails `protobuf:"bytes,8,opt,name=details,proto3" json:"details,omitempty"`
	Status         preapprovedloan.LoanAccountStatus   `protobuf:"varint,9,opt,name=status,proto3,enum=preapprovedloan.LoanAccountStatus" json:"status,omitempty"`
	LoanProgram    preapprovedloan.LoanProgram         `protobuf:"varint,10,opt,name=loan_program,json=loanProgram,proto3,enum=preapprovedloan.LoanProgram" json:"loan_program,omitempty"`
}

func (x *GetLoanAccountSummaryResponse_LoanAccountForCX) Reset() {
	*x = GetLoanAccountSummaryResponse_LoanAccountForCX{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanAccountSummaryResponse_LoanAccountForCX) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanAccountSummaryResponse_LoanAccountForCX) ProtoMessage() {}

func (x *GetLoanAccountSummaryResponse_LoanAccountForCX) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanAccountSummaryResponse_LoanAccountForCX.ProtoReflect.Descriptor instead.
func (*GetLoanAccountSummaryResponse_LoanAccountForCX) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{7, 0}
}

func (x *GetLoanAccountSummaryResponse_LoanAccountForCX) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *GetLoanAccountSummaryResponse_LoanAccountForCX) GetVendor() preapprovedloan.Vendor {
	if x != nil {
		return x.Vendor
	}
	return preapprovedloan.Vendor(0)
}

func (x *GetLoanAccountSummaryResponse_LoanAccountForCX) GetIfscCode() string {
	if x != nil {
		return x.IfscCode
	}
	return ""
}

func (x *GetLoanAccountSummaryResponse_LoanAccountForCX) GetLoanType() preapprovedloan.LoanType {
	if x != nil {
		return x.LoanType
	}
	return preapprovedloan.LoanType(0)
}

func (x *GetLoanAccountSummaryResponse_LoanAccountForCX) GetLoanAmountInfo() *preapprovedloan.LoanAmountInfo {
	if x != nil {
		return x.LoanAmountInfo
	}
	return nil
}

func (x *GetLoanAccountSummaryResponse_LoanAccountForCX) GetLoanEndDate() *date.Date {
	if x != nil {
		return x.LoanEndDate
	}
	return nil
}

func (x *GetLoanAccountSummaryResponse_LoanAccountForCX) GetMaturityDate() *date.Date {
	if x != nil {
		return x.MaturityDate
	}
	return nil
}

func (x *GetLoanAccountSummaryResponse_LoanAccountForCX) GetDetails() *preapprovedloan.LoanAccountDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *GetLoanAccountSummaryResponse_LoanAccountForCX) GetStatus() preapprovedloan.LoanAccountStatus {
	if x != nil {
		return x.Status
	}
	return preapprovedloan.LoanAccountStatus(0)
}

func (x *GetLoanAccountSummaryResponse_LoanAccountForCX) GetLoanProgram() preapprovedloan.LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return preapprovedloan.LoanProgram(0)
}

type GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsUserSuggestedForLoan bool   `protobuf:"varint,1,opt,name=is_user_suggested_for_loan,json=isUserSuggestedForLoan,proto3" json:"is_user_suggested_for_loan,omitempty"`
	ReasonForIneligibility string `protobuf:"bytes,2,opt,name=reason_for_ineligibility,json=reasonForIneligibility,proto3" json:"reason_for_ineligibility,omitempty"`
}

func (x *GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails) Reset() {
	*x = GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails) ProtoMessage() {}

func (x *GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails.ProtoReflect.Descriptor instead.
func (*GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails) GetIsUserSuggestedForLoan() bool {
	if x != nil {
		return x.IsUserSuggestedForLoan
	}
	return false
}

func (x *GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails) GetReasonForIneligibility() string {
	if x != nil {
		return x.ReasonForIneligibility
	}
	return ""
}

type GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanApplicationStatus    string       `protobuf:"bytes,1,opt,name=loan_application_status,json=loanApplicationStatus,proto3" json:"loan_application_status,omitempty"`
	LoanApplicationSubStatus string       `protobuf:"bytes,2,opt,name=loan_application_sub_status,json=loanApplicationSubStatus,proto3" json:"loan_application_sub_status,omitempty"`
	LoanAmountApplied        *money.Money `protobuf:"bytes,3,opt,name=loan_amount_applied,json=loanAmountApplied,proto3" json:"loan_amount_applied,omitempty"`
	TenureAppliedMonths      int32        `protobuf:"varint,4,opt,name=tenure_applied_months,json=tenureAppliedMonths,proto3" json:"tenure_applied_months,omitempty"`
	Vendor                   string       `protobuf:"bytes,5,opt,name=vendor,proto3" json:"vendor,omitempty"`
	LoanProgram              string       `protobuf:"bytes,6,opt,name=loan_program,json=loanProgram,proto3" json:"loan_program,omitempty"`
}

func (x *GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails) Reset() {
	*x = GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails) ProtoMessage() {}

func (x *GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails.ProtoReflect.Descriptor instead.
func (*GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{9, 1}
}

func (x *GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails) GetLoanApplicationStatus() string {
	if x != nil {
		return x.LoanApplicationStatus
	}
	return ""
}

func (x *GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails) GetLoanApplicationSubStatus() string {
	if x != nil {
		return x.LoanApplicationSubStatus
	}
	return ""
}

func (x *GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails) GetLoanAmountApplied() *money.Money {
	if x != nil {
		return x.LoanAmountApplied
	}
	return nil
}

func (x *GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails) GetTenureAppliedMonths() int32 {
	if x != nil {
		return x.TenureAppliedMonths
	}
	return 0
}

func (x *GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails) GetLoanProgram() string {
	if x != nil {
		return x.LoanProgram
	}
	return ""
}

type GetLoanUserDetailsResponse_LoanOfferDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanOfferId            string       `protobuf:"bytes,1,opt,name=loan_offer_id,json=loanOfferId,proto3" json:"loan_offer_id,omitempty"`
	MinLoanAmount          *money.Money `protobuf:"bytes,2,opt,name=min_loan_amount,json=minLoanAmount,proto3" json:"min_loan_amount,omitempty"`
	MaxLoanAmount          *money.Money `protobuf:"bytes,3,opt,name=max_loan_amount,json=maxLoanAmount,proto3" json:"max_loan_amount,omitempty"`
	MaxEmiAmount           *money.Money `protobuf:"bytes,4,opt,name=max_emi_amount,json=maxEmiAmount,proto3" json:"max_emi_amount,omitempty"`
	Interest               float64      `protobuf:"fixed64,5,opt,name=interest,proto3" json:"interest,omitempty"`
	MinTenure              int32        `protobuf:"varint,6,opt,name=min_tenure,json=minTenure,proto3" json:"min_tenure,omitempty"`
	MaxTenure              int32        `protobuf:"varint,7,opt,name=max_tenure,json=maxTenure,proto3" json:"max_tenure,omitempty"`
	OfferStartDate         *date.Date   `protobuf:"bytes,8,opt,name=offer_start_date,json=offerStartDate,proto3" json:"offer_start_date,omitempty"`
	OfferEndDate           *date.Date   `protobuf:"bytes,9,opt,name=offer_end_date,json=offerEndDate,proto3" json:"offer_end_date,omitempty"`
	AgentInputUserFeedback string       `protobuf:"bytes,10,opt,name=agent_input_user_feedback,json=agentInputUserFeedback,proto3" json:"agent_input_user_feedback,omitempty"`
	Vendor                 string       `protobuf:"bytes,11,opt,name=vendor,proto3" json:"vendor,omitempty"`
	LoanProgram            string       `protobuf:"bytes,12,opt,name=loan_program,json=loanProgram,proto3" json:"loan_program,omitempty"`
	// this field will contain additional constraint details
	//
	// Types that are assignable to AdditionalConstraints:
	//
	//	*GetLoanUserDetailsResponse_LoanOfferDetails_FiftyfinLamfConstraintInfo
	AdditionalConstraints isGetLoanUserDetailsResponse_LoanOfferDetails_AdditionalConstraints `protobuf_oneof:"additional_constraints"`
	LoanOfferType         preapprovedloan.LoanOfferType                                       `protobuf:"varint,20,opt,name=loan_offer_type,json=loanOfferType,proto3,enum=preapprovedloan.LoanOfferType" json:"loan_offer_type,omitempty"`
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) Reset() {
	*x = GetLoanUserDetailsResponse_LoanOfferDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanUserDetailsResponse_LoanOfferDetails) ProtoMessage() {}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanUserDetailsResponse_LoanOfferDetails.ProtoReflect.Descriptor instead.
func (*GetLoanUserDetailsResponse_LoanOfferDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{9, 2}
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) GetLoanOfferId() string {
	if x != nil {
		return x.LoanOfferId
	}
	return ""
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) GetMinLoanAmount() *money.Money {
	if x != nil {
		return x.MinLoanAmount
	}
	return nil
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) GetMaxLoanAmount() *money.Money {
	if x != nil {
		return x.MaxLoanAmount
	}
	return nil
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) GetMaxEmiAmount() *money.Money {
	if x != nil {
		return x.MaxEmiAmount
	}
	return nil
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) GetInterest() float64 {
	if x != nil {
		return x.Interest
	}
	return 0
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) GetMinTenure() int32 {
	if x != nil {
		return x.MinTenure
	}
	return 0
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) GetMaxTenure() int32 {
	if x != nil {
		return x.MaxTenure
	}
	return 0
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) GetOfferStartDate() *date.Date {
	if x != nil {
		return x.OfferStartDate
	}
	return nil
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) GetOfferEndDate() *date.Date {
	if x != nil {
		return x.OfferEndDate
	}
	return nil
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) GetAgentInputUserFeedback() string {
	if x != nil {
		return x.AgentInputUserFeedback
	}
	return ""
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) GetLoanProgram() string {
	if x != nil {
		return x.LoanProgram
	}
	return ""
}

func (m *GetLoanUserDetailsResponse_LoanOfferDetails) GetAdditionalConstraints() isGetLoanUserDetailsResponse_LoanOfferDetails_AdditionalConstraints {
	if m != nil {
		return m.AdditionalConstraints
	}
	return nil
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) GetFiftyfinLamfConstraintInfo() *preapprovedloan.FiftyFinLamfConstraintInfo {
	if x, ok := x.GetAdditionalConstraints().(*GetLoanUserDetailsResponse_LoanOfferDetails_FiftyfinLamfConstraintInfo); ok {
		return x.FiftyfinLamfConstraintInfo
	}
	return nil
}

func (x *GetLoanUserDetailsResponse_LoanOfferDetails) GetLoanOfferType() preapprovedloan.LoanOfferType {
	if x != nil {
		return x.LoanOfferType
	}
	return preapprovedloan.LoanOfferType(0)
}

type isGetLoanUserDetailsResponse_LoanOfferDetails_AdditionalConstraints interface {
	isGetLoanUserDetailsResponse_LoanOfferDetails_AdditionalConstraints()
}

type GetLoanUserDetailsResponse_LoanOfferDetails_FiftyfinLamfConstraintInfo struct {
	// constraints specified by FIFTYFIN vendor for LAMF loan program
	FiftyfinLamfConstraintInfo *preapprovedloan.FiftyFinLamfConstraintInfo `protobuf:"bytes,13,opt,name=fiftyfin_lamf_constraint_info,json=fiftyfinLamfConstraintInfo,proto3,oneof"`
}

func (*GetLoanUserDetailsResponse_LoanOfferDetails_FiftyfinLamfConstraintInfo) isGetLoanUserDetailsResponse_LoanOfferDetails_AdditionalConstraints() {
}

type GetLoanDetailsResponse_LoanDetailsForCx struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanAccountNumber    string                                                         `protobuf:"bytes,1,opt,name=loan_account_number,json=loanAccountNumber,proto3" json:"loan_account_number,omitempty"`
	LoanOpenDate         *timestamppb.Timestamp                                         `protobuf:"bytes,2,opt,name=loan_open_date,json=loanOpenDate,proto3" json:"loan_open_date,omitempty"`
	LoanAmount           *money.Money                                                   `protobuf:"bytes,3,opt,name=loan_amount,json=loanAmount,proto3" json:"loan_amount,omitempty"`
	InterestRate         float64                                                        `protobuf:"fixed64,4,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
	TenureInMonths       int32                                                          `protobuf:"varint,5,opt,name=tenure_in_months,json=tenureInMonths,proto3" json:"tenure_in_months,omitempty"`
	ProcessingFee        *money.Money                                                   `protobuf:"bytes,6,opt,name=processing_fee,json=processingFee,proto3" json:"processing_fee,omitempty"`
	OutstandingAmount    *money.Money                                                   `protobuf:"bytes,7,opt,name=outstanding_amount,json=outstandingAmount,proto3" json:"outstanding_amount,omitempty"`
	Gst                  *money.Money                                                   `protobuf:"bytes,8,opt,name=gst,proto3" json:"gst,omitempty"`
	BrokenPeriodInterest *money.Money                                                   `protobuf:"bytes,9,opt,name=broken_period_interest,json=brokenPeriodInterest,proto3" json:"broken_period_interest,omitempty"`
	PreClosureFee        *money.Money                                                   `protobuf:"bytes,10,opt,name=pre_closure_fee,json=preClosureFee,proto3" json:"pre_closure_fee,omitempty"`
	LoanPastTransactions []*GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction `protobuf:"bytes,11,rep,name=loan_past_transactions,json=loanPastTransactions,proto3" json:"loan_past_transactions,omitempty"`
	LoanUpcomingEmis     []*GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi     `protobuf:"bytes,12,rep,name=loan_upcoming_emis,json=loanUpcomingEmis,proto3" json:"loan_upcoming_emis,omitempty"`
	Vendor               string                                                         `protobuf:"bytes,13,opt,name=vendor,proto3" json:"vendor,omitempty"`
	LoanProgram          string                                                         `protobuf:"bytes,14,opt,name=loan_program,json=loanProgram,proto3" json:"loan_program,omitempty"`
	MfPledgeDetails      []*GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails     `protobuf:"bytes,15,rep,name=mf_pledge_details,json=mfPledgeDetails,proto3" json:"mf_pledge_details,omitempty"`
	MandateAccount       *MandateAccount                                                `protobuf:"bytes,16,opt,name=mandate_account,json=mandateAccount,proto3" json:"mandate_account,omitempty"`
	BankAccountDetails   *typesv2.BankAccountDetails                                    `protobuf:"bytes,17,opt,name=bank_account_details,json=bankAccountDetails,proto3" json:"bank_account_details,omitempty"`
	LoanAccount          *preapprovedloan.LoanAccount                                   `protobuf:"bytes,18,opt,name=loan_account,json=loanAccount,proto3" json:"loan_account,omitempty"`
	ForeClosureDetails   *ForeclosureDetails                                            `protobuf:"bytes,19,opt,name=fore_closure_details,json=foreClosureDetails,proto3" json:"fore_closure_details,omitempty"`
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) Reset() {
	*x = GetLoanDetailsResponse_LoanDetailsForCx{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDetailsResponse_LoanDetailsForCx) ProtoMessage() {}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDetailsResponse_LoanDetailsForCx.ProtoReflect.Descriptor instead.
func (*GetLoanDetailsResponse_LoanDetailsForCx) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{11, 0}
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetLoanAccountNumber() string {
	if x != nil {
		return x.LoanAccountNumber
	}
	return ""
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetLoanOpenDate() *timestamppb.Timestamp {
	if x != nil {
		return x.LoanOpenDate
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetLoanAmount() *money.Money {
	if x != nil {
		return x.LoanAmount
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetInterestRate() float64 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetTenureInMonths() int32 {
	if x != nil {
		return x.TenureInMonths
	}
	return 0
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetProcessingFee() *money.Money {
	if x != nil {
		return x.ProcessingFee
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetOutstandingAmount() *money.Money {
	if x != nil {
		return x.OutstandingAmount
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetGst() *money.Money {
	if x != nil {
		return x.Gst
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetBrokenPeriodInterest() *money.Money {
	if x != nil {
		return x.BrokenPeriodInterest
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetPreClosureFee() *money.Money {
	if x != nil {
		return x.PreClosureFee
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetLoanPastTransactions() []*GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction {
	if x != nil {
		return x.LoanPastTransactions
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetLoanUpcomingEmis() []*GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi {
	if x != nil {
		return x.LoanUpcomingEmis
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetLoanProgram() string {
	if x != nil {
		return x.LoanProgram
	}
	return ""
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetMfPledgeDetails() []*GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails {
	if x != nil {
		return x.MfPledgeDetails
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetMandateAccount() *MandateAccount {
	if x != nil {
		return x.MandateAccount
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetBankAccountDetails() *typesv2.BankAccountDetails {
	if x != nil {
		return x.BankAccountDetails
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetLoanAccount() *preapprovedloan.LoanAccount {
	if x != nil {
		return x.LoanAccount
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx) GetForeClosureDetails() *ForeclosureDetails {
	if x != nil {
		return x.ForeClosureDetails
	}
	return nil
}

type GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp         *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	TransactionType   string                 `protobuf:"bytes,2,opt,name=transaction_type,json=transactionType,proto3" json:"transaction_type,omitempty"`
	Status            string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	OrderId           string                 `protobuf:"bytes,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	FiUtrNumber       string                 `protobuf:"bytes,5,opt,name=fi_utr_number,json=fiUtrNumber,proto3" json:"fi_utr_number,omitempty"`
	LoanAccountNumber string                 `protobuf:"bytes,6,opt,name=loan_account_number,json=loanAccountNumber,proto3" json:"loan_account_number,omitempty"`
	Amount            *money.Money           `protobuf:"bytes,7,opt,name=amount,proto3" json:"amount,omitempty"`
	ModeOfPayment     string                 `protobuf:"bytes,8,opt,name=mode_of_payment,json=modeOfPayment,proto3" json:"mode_of_payment,omitempty"`
	Charges           *money.Money           `protobuf:"bytes,9,opt,name=charges,proto3" json:"charges,omitempty"`
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) Reset() {
	*x = GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) ProtoMessage() {}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction.ProtoReflect.Descriptor instead.
func (*GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{11, 0, 0}
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) GetTransactionType() string {
	if x != nil {
		return x.TransactionType
	}
	return ""
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) GetFiUtrNumber() string {
	if x != nil {
		return x.FiUtrNumber
	}
	return ""
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) GetLoanAccountNumber() string {
	if x != nil {
		return x.LoanAccountNumber
	}
	return ""
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) GetModeOfPayment() string {
	if x != nil {
		return x.ModeOfPayment
	}
	return ""
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) GetCharges() *money.Money {
	if x != nil {
		return x.Charges
	}
	return nil
}

type GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NextEmiDate       *date.Date   `protobuf:"bytes,1,opt,name=next_emi_date,json=nextEmiDate,proto3" json:"next_emi_date,omitempty"`
	LoanAccountNumber string       `protobuf:"bytes,2,opt,name=loan_account_number,json=loanAccountNumber,proto3" json:"loan_account_number,omitempty"`
	Amount            *money.Money `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi) Reset() {
	*x = GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi) ProtoMessage() {}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi.ProtoReflect.Descriptor instead.
func (*GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{11, 0, 1}
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi) GetNextEmiDate() *date.Date {
	if x != nil {
		return x.NextEmiDate
	}
	return nil
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi) GetLoanAccountNumber() string {
	if x != nil {
		return x.LoanAccountNumber
	}
	return ""
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

type GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Isin  string  `protobuf:"bytes,1,opt,name=isin,proto3" json:"isin,omitempty"`
	Units float64 `protobuf:"fixed64,2,opt,name=units,proto3" json:"units,omitempty"`
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails) Reset() {
	*x = GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails) ProtoMessage() {}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails.ProtoReflect.Descriptor instead.
func (*GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{11, 0, 2}
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails) GetIsin() string {
	if x != nil {
		return x.Isin
	}
	return ""
}

func (x *GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails) GetUnits() float64 {
	if x != nil {
		return x.Units
	}
	return 0
}

type GetQueueElementsResponse_QueueElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Types that are assignable to Payload:
	//
	//	*GetQueueElementsResponse_QueueElement_LivenessReview
	//	*GetQueueElementsResponse_QueueElement_FacematchReview
	Payload isGetQueueElementsResponse_QueueElement_Payload `protobuf_oneof:"payload"`
}

func (x *GetQueueElementsResponse_QueueElement) Reset() {
	*x = GetQueueElementsResponse_QueueElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQueueElementsResponse_QueueElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQueueElementsResponse_QueueElement) ProtoMessage() {}

func (x *GetQueueElementsResponse_QueueElement) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQueueElementsResponse_QueueElement.ProtoReflect.Descriptor instead.
func (*GetQueueElementsResponse_QueueElement) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{14, 0}
}

func (x *GetQueueElementsResponse_QueueElement) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (m *GetQueueElementsResponse_QueueElement) GetPayload() isGetQueueElementsResponse_QueueElement_Payload {
	if m != nil {
		return m.Payload
	}
	return nil
}

func (x *GetQueueElementsResponse_QueueElement) GetLivenessReview() *persistentqueue.LivenessReview {
	if x, ok := x.GetPayload().(*GetQueueElementsResponse_QueueElement_LivenessReview); ok {
		return x.LivenessReview
	}
	return nil
}

func (x *GetQueueElementsResponse_QueueElement) GetFacematchReview() *persistentqueue.FacematchReview {
	if x, ok := x.GetPayload().(*GetQueueElementsResponse_QueueElement_FacematchReview); ok {
		return x.FacematchReview
	}
	return nil
}

type isGetQueueElementsResponse_QueueElement_Payload interface {
	isGetQueueElementsResponse_QueueElement_Payload()
}

type GetQueueElementsResponse_QueueElement_LivenessReview struct {
	LivenessReview *persistentqueue.LivenessReview `protobuf:"bytes,2,opt,name=liveness_review,json=livenessReview,proto3,oneof"`
}

type GetQueueElementsResponse_QueueElement_FacematchReview struct {
	FacematchReview *persistentqueue.FacematchReview `protobuf:"bytes,3,opt,name=facematch_review,json=facematchReview,proto3,oneof"`
}

func (*GetQueueElementsResponse_QueueElement_LivenessReview) isGetQueueElementsResponse_QueueElement_Payload() {
}

func (*GetQueueElementsResponse_QueueElement_FacematchReview) isGetQueueElementsResponse_QueueElement_Payload() {
}

type FiftyfinLamfLoanAccountDetails_LoanStatement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Transactions []*fiftyfin.LoanStatementTxn `protobuf:"bytes,1,rep,name=transactions,proto3" json:"transactions,omitempty"`
}

func (x *FiftyfinLamfLoanAccountDetails_LoanStatement) Reset() {
	*x = FiftyfinLamfLoanAccountDetails_LoanStatement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiftyfinLamfLoanAccountDetails_LoanStatement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiftyfinLamfLoanAccountDetails_LoanStatement) ProtoMessage() {}

func (x *FiftyfinLamfLoanAccountDetails_LoanStatement) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_cx_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiftyfinLamfLoanAccountDetails_LoanStatement.ProtoReflect.Descriptor instead.
func (*FiftyfinLamfLoanAccountDetails_LoanStatement) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_cx_service_proto_rawDescGZIP(), []int{23, 0}
}

func (x *FiftyfinLamfLoanAccountDetails_LoanStatement) GetTransactions() []*fiftyfin.LoanStatementTxn {
	if x != nil {
		return x.Transactions
	}
	return nil
}

var File_api_preapprovedloan_cx_service_proto protoreflect.FileDescriptor

var file_api_preapprovedloan_cx_service_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x63, 0x78, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f,
	0x70, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x63, 0x78, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61,
	0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x61, 0x70, 0x69,
	0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x62, 0x61,
	0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x2f, 0x6c,
	0x6f, 0x61, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xee, 0x02, 0x0a, 0x19, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x6f, 0x72, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x72, 0x63, 0x68, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69,
	0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x56, 0x65,
	0x72, 0x64, 0x69, 0x63, 0x74, 0x52, 0x07, 0x76, 0x65, 0x72, 0x64, 0x69, 0x63, 0x74, 0x12, 0x68,
	0x0a, 0x10, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x7c, 0x0a, 0x0f, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x3b, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x64, 0x41, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x41, 0x0a, 0x1a, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x68, 0x0a, 0x1a, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x2f, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x22, 0xab, 0x03, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5c, 0x0a, 0x09, 0x6c, 0x6f, 0x61, 0x6e,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78,
	0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x6f, 0x61,
	0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x43, 0x58, 0x52, 0x09, 0x6c, 0x6f, 0x61,
	0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x1a, 0x88, 0x02, 0x0a, 0x0e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x43, 0x58, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x2f, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x12, 0x4e, 0x0a, 0x11, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x73,
	0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73,
	0x52, 0x10, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e,
	0x74, 0x73, 0x12, 0x4d, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x6a, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x06,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x56,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x22, 0xd8, 0x04,
	0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x66, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x6f,
	0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x43, 0x58, 0x52, 0x0c,
	0x6c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x1a, 0xa9, 0x03, 0x0a,
	0x10, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x43,
	0x58, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x2f, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x17, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x12, 0x3d, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x34, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x44, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x73,
	0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x6a, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x22, 0xe4, 0x05, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x66, 0x0a, 0x0c, 0x6c,
	0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x42, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x46, 0x6f, 0x72, 0x43, 0x58, 0x52, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x1a, 0xb5, 0x04, 0x0a, 0x10, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x43, 0x58, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x2f, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x17, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x12, 0x1b, 0x0a, 0x09, 0x69, 0x66, 0x73, 0x63, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x66, 0x73, 0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a,
	0x09, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x19, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x6c, 0x6f, 0x61,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x49, 0x0a, 0x10, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0e, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x35, 0x0a, 0x0d, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e,
	0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x36, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x75, 0x72, 0x69, 0x74, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x3d, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3a,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a, 0x0c, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1c, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b,
	0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x22, 0x74, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0xf4, 0x0f, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0xb2, 0x01, 0x0a, 0x2b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x61,
	0x6e, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x55, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78,
	0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x61, 0x6e,
	0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x26, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x6e, 0x74, 0x68,
	0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0xa2, 0x01, 0x0a, 0x25, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6c, 0x6f, 0x61, 0x6e,
	0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x21, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x6d, 0x0a, 0x12, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78,
	0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x6f, 0x61, 0x6e,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x10, 0x6c, 0x6f,
	0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x58,
	0x0a, 0x19, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x17, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x61, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x58, 0x0a, 0x19, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f,
	0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x17, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x73, 0x12, 0x3e, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e,
	0x74, 0x73, 0x1a, 0x9e, 0x01, 0x0a, 0x26, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x45, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3a, 0x0a,
	0x1a, 0x69, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x16, 0x69, 0x73, 0x55, 0x73, 0x65, 0x72, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x46, 0x6f, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x12, 0x38, 0x0a, 0x18, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x69, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x49, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x1a, 0xcd, 0x02, 0x0a, 0x21, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x6c, 0x6f, 0x61,
	0x6e, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6c, 0x6f, 0x61, 0x6e,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x3d, 0x0a, 0x1b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x42, 0x0a, 0x13, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x11, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x65, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x13, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x65, 0x64, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x1a, 0x82, 0x06, 0x0a, 0x10, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x6f, 0x61, 0x6e,
	0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0f,
	0x6d, 0x69, 0x6e, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6d, 0x69, 0x6e, 0x4c, 0x6f,
	0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x0f, 0x6d, 0x61, 0x78, 0x5f,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x0e, 0x6d, 0x61, 0x78, 0x5f, 0x65, 0x6d, 0x69, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x0c, 0x6d, 0x61, 0x78, 0x45, 0x6d, 0x69, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x69,
	0x6e, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x6d, 0x69, 0x6e, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78,
	0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d,
	0x61, 0x78, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x3b, 0x0a, 0x10, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x0e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x0c, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x39,
	0x0a, 0x19, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x16, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x12, 0x70, 0x0a, 0x1d, 0x66, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e,
	0x5f, 0x6c, 0x61, 0x6d, 0x66, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x46, 0x69,
	0x66, 0x74, 0x79, 0x46, 0x69, 0x6e, 0x4c, 0x61, 0x6d, 0x66, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72,
	0x61, 0x69, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x1a, 0x66, 0x69, 0x66, 0x74,
	0x79, 0x66, 0x69, 0x6e, 0x4c, 0x61, 0x6d, 0x66, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x46, 0x0a, 0x0f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1e, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0d, 0x6c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x18,
	0x0a, 0x16, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e,
	0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x22, 0x70, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0b,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xae, 0x10, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5e, 0x0a, 0x0c, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x6f, 0x61,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x78, 0x52, 0x0b, 0x6c,
	0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x8e, 0x0f, 0x0a, 0x10, 0x4c,
	0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x78, 0x12,
	0x2e, 0x0a, 0x13, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6c, 0x6f,
	0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x40, 0x0a, 0x0e, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x4f, 0x70, 0x65, 0x6e, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x33, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65,
	0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x74,
	0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x39, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65,
	0x12, 0x41, 0x0a, 0x12, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x11, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x03, 0x67, 0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x03, 0x67, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x16, 0x62, 0x72, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x65, 0x73, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x62,
	0x72, 0x6f, 0x6b, 0x65, 0x6e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x0d, 0x70, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x46, 0x65, 0x65, 0x12,
	0x85, 0x01, 0x0a, 0x16, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x61, 0x73, 0x74, 0x5f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x4f, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x6f, 0x61,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x78, 0x2e, 0x4c, 0x6f,
	0x61, 0x6e, 0x50, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x14, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x61, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x79, 0x0a, 0x12, 0x6c, 0x6f, 0x61, 0x6e, 0x5f,
	0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x69, 0x73, 0x18, 0x0c, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x78,
	0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x69,
	0x52, 0x10, 0x6c, 0x6f, 0x61, 0x6e, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x45, 0x6d,
	0x69, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x77, 0x0a,
	0x11, 0x6d, 0x66, 0x5f, 0x70, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65,
	0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x46, 0x6f, 0x72, 0x43, 0x78, 0x2e, 0x4d, 0x66, 0x50, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x6d, 0x66, 0x50, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4b, 0x0a, 0x0f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x0e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x51, 0x0a, 0x14, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x12, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3f, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c,
	0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x58, 0x0a, 0x14, 0x66, 0x6f, 0x72, 0x65, 0x5f,
	0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x12, 0x66,
	0x6f, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x1a, 0x83, 0x03, 0x0a, 0x13, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x61, 0x73, 0x74, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x0d, 0x66, 0x69, 0x5f, 0x75, 0x74, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x69, 0x55, 0x74, 0x72, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x6f, 0x64, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x6f, 0x64, 0x65,
	0x4f, 0x66, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x07, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x07,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x1a, 0xa4, 0x01, 0x0a, 0x0f, 0x4c, 0x6f, 0x61, 0x6e,
	0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x69, 0x12, 0x35, 0x0a, 0x0d, 0x6e,
	0x65, 0x78, 0x74, 0x5f, 0x65, 0x6d, 0x69, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x45, 0x6d, 0x69, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x3b,
	0x0a, 0x0f, 0x4d, 0x66, 0x50, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x73, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x69, 0x73, 0x69, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x22, 0xa9, 0x03, 0x0a, 0x12,
	0x46, 0x6f, 0x72, 0x65, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x4c, 0x0a, 0x18, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x73,
	0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x16, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f,
	0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x54, 0x0a, 0x1c, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x6f, 0x75,
	0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x1a, 0x70, 0x72, 0x69, 0x6e,
	0x63, 0x69, 0x70, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x52, 0x0a, 0x1b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65,
	0x73, 0x74, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x19, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x0b, 0x70, 0x65,
	0x6e, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x61, 0x6d, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0a, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x41, 0x6d, 0x74, 0x12,
	0x2d, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x73, 0x5f, 0x61, 0x6d, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x07, 0x66, 0x65, 0x65, 0x73, 0x41, 0x6d, 0x74, 0x12, 0x37,
	0x0a, 0x0d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x22, 0xf9, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x70, 0x65, 0x72, 0x73,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x50, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x61,
	0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x33,
	0x0a, 0x07, 0x74, 0x6f, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x06, 0x74, 0x6f, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0xdd, 0x02, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x55, 0x0a, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0xc4, 0x01, 0x0a,
	0x0c, 0x51, 0x75, 0x65, 0x75, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4a, 0x0a,
	0x0f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74,
	0x65, 0x6e, 0x74, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x4c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x0e, 0x6c, 0x69, 0x76, 0x65, 0x6e,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x12, 0x4d, 0x0a, 0x10, 0x66, 0x61, 0x63,
	0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x0f, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x09, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x22, 0x46, 0x0a, 0x1c, 0x4d, 0x61, 0x72, 0x6b, 0x4c, 0x6f, 0x61, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f,
	0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x44, 0x0a, 0x1d, 0x4d,
	0x61, 0x72, 0x6b, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x51, 0x0a, 0x0e, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x73, 0x6b,
	0x65, 0x64, 0x41, 0x63, 0x63, 0x4e, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6b,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x85, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x65,
	0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e,
	0x0a, 0x13, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6c, 0x6f, 0x61,
	0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x3c,
	0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xc0, 0x03, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x65, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x0a, 0x15,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x61, 0x6d, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x41, 0x6d, 0x74, 0x12, 0x4e, 0x0a, 0x19, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61,
	0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x6d,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x17, 0x70, 0x72, 0x69,
	0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x41, 0x6d, 0x74, 0x12, 0x4c, 0x0a, 0x18, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74,
	0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x6d, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x16, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x65, 0x73, 0x74, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41,
	0x6d, 0x74, 0x12, 0x33, 0x0a, 0x0b, 0x70, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x5f, 0x61, 0x6d,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x70, 0x65, 0x6e,
	0x61, 0x6c, 0x74, 0x79, 0x41, 0x6d, 0x74, 0x12, 0x2d, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x73, 0x5f,
	0x61, 0x6d, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x07, 0x66,
	0x65, 0x65, 0x73, 0x41, 0x6d, 0x74, 0x12, 0x37, 0x0a, 0x0d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x22,
	0xe1, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x0b, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x6c, 0x6f,
	0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63,
	0x78, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x73, 0x22, 0xbd, 0x05, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61,
	0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x57, 0x0a, 0x15, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x13, 0x6c, 0x6f, 0x61, 0x6e, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x57,
	0x0a, 0x13, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61,
	0x79, 0x6f, 0x75, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f,
	0x61, 0x6e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79,
	0x6f, 0x75, 0x74, 0x52, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x73, 0x12, 0x5f, 0x0a, 0x1d, 0x6c, 0x6f, 0x61, 0x6e, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x1a, 0x6c, 0x6f,
	0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x12, 0x68, 0x0a, 0x15, 0x66, 0x69, 0x66, 0x74,
	0x79, 0x66, 0x69, 0x6e, 0x5f, 0x6c, 0x61, 0x6d, 0x66, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x46, 0x69, 0x66,
	0x74, 0x79, 0x66, 0x69, 0x6e, 0x4c, 0x61, 0x6d, 0x66, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x13, 0x66,
	0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x4c, 0x61, 0x6d, 0x66, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x51, 0x0a, 0x12, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x75, 0x70, 0x63, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6d, 0x69, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x63, 0x78, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x45, 0x6d, 0x69, 0x52, 0x10, 0x6c, 0x6f, 0x61, 0x6e, 0x55, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x45, 0x6d, 0x69, 0x73, 0x12, 0x51, 0x0a, 0x14, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x12, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x22, 0xa4, 0x01, 0x0a, 0x0f, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x70, 0x63, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x69, 0x12, 0x35, 0x0a, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x65, 0x6d, 0x69, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x45, 0x6d, 0x69, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2e,
	0x0a, 0x13, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6c, 0x6f, 0x61,
	0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2a,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x94, 0x02, 0x0a, 0x1e, 0x46,
	0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x4c, 0x61, 0x6d, 0x66, 0x4c, 0x6f, 0x61, 0x6e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x52, 0x0a,
	0x03, 0x73, 0x6f, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e,
	0x46, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x4c, 0x61, 0x6d, 0x66, 0x4c, 0x6f, 0x61, 0x6e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x4c,
	0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x03, 0x73, 0x6f,
	0x61, 0x12, 0x45, 0x0a, 0x20, 0x69, 0x73, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x69, 0x73, 0x4c,
	0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64,
	0x4f, 0x6e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x1a, 0x57, 0x0a, 0x0d, 0x4c, 0x6f, 0x61, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x0c, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x69, 0x66, 0x74, 0x79, 0x66,
	0x69, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x78, 0x6e, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0xea, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x0b,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x5a, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x4c, 0x6f, 0x61,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x73, 0x22, 0xd2,
	0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x3f, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x41, 0x0a, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x65, 0x70, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x6c, 0x6f, 0x61, 0x6e, 0x53, 0x74,
	0x65, 0x70, 0x73, 0x32, 0xee, 0x0a, 0x0a, 0x02, 0x43, 0x78, 0x12, 0x75, 0x0a, 0x12, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x12, 0x2d, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x75,
	0x61, 0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2e, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x78, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x2e, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65,
	0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65,
	0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x12, 0x30, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x12, 0x30, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61,
	0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x75, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x2d, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2e, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x69, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x29, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2a, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6d, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x2b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x15,
	0x4d, 0x61, 0x72, 0x6b, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x12, 0x30, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x4c,
	0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x4d, 0x61, 0x72,
	0x6b, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x46, 0x6f, 0x72, 0x65, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x29, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x65,
	0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x65, 0x63, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x1f, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3a,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3a, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78,
	0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x63, 0x78, 0x2e, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x5e, 0x0a, 0x2d, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x63, 0x78, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2f, 0x63, 0x78, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_preapprovedloan_cx_service_proto_rawDescOnce sync.Once
	file_api_preapprovedloan_cx_service_proto_rawDescData = file_api_preapprovedloan_cx_service_proto_rawDesc
)

func file_api_preapprovedloan_cx_service_proto_rawDescGZIP() []byte {
	file_api_preapprovedloan_cx_service_proto_rawDescOnce.Do(func() {
		file_api_preapprovedloan_cx_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_preapprovedloan_cx_service_proto_rawDescData)
	})
	return file_api_preapprovedloan_cx_service_proto_rawDescData
}

var file_api_preapprovedloan_cx_service_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_api_preapprovedloan_cx_service_proto_goTypes = []interface{}{
	(*SubmitManualReviewRequest)(nil),                                         // 0: preapprovedloan.cx.SubmitManualReviewRequest
	(*SubmitManualReviewResponse)(nil),                                        // 1: preapprovedloan.cx.SubmitManualReviewResponse
	(*GetLoanOfferSummaryRequest)(nil),                                        // 2: preapprovedloan.cx.GetLoanOfferSummaryRequest
	(*GetLoanOfferSummaryResponse)(nil),                                       // 3: preapprovedloan.cx.GetLoanOfferSummaryResponse
	(*GetLoanRequestSummaryRequest)(nil),                                      // 4: preapprovedloan.cx.GetLoanRequestSummaryRequest
	(*GetLoanRequestSummaryResponse)(nil),                                     // 5: preapprovedloan.cx.GetLoanRequestSummaryResponse
	(*GetLoanAccountSummaryRequest)(nil),                                      // 6: preapprovedloan.cx.GetLoanAccountSummaryRequest
	(*GetLoanAccountSummaryResponse)(nil),                                     // 7: preapprovedloan.cx.GetLoanAccountSummaryResponse
	(*GetLoanUserDetailsRequest)(nil),                                         // 8: preapprovedloan.cx.GetLoanUserDetailsRequest
	(*GetLoanUserDetailsResponse)(nil),                                        // 9: preapprovedloan.cx.GetLoanUserDetailsResponse
	(*GetLoanDetailsRequest)(nil),                                             // 10: preapprovedloan.cx.GetLoanDetailsRequest
	(*GetLoanDetailsResponse)(nil),                                            // 11: preapprovedloan.cx.GetLoanDetailsResponse
	(*ForeclosureDetails)(nil),                                                // 12: preapprovedloan.cx.ForeclosureDetails
	(*GetQueueElementsRequest)(nil),                                           // 13: preapprovedloan.cx.GetQueueElementsRequest
	(*GetQueueElementsResponse)(nil),                                          // 14: preapprovedloan.cx.GetQueueElementsResponse
	(*MarkLoanRequestCancelRequest)(nil),                                      // 15: preapprovedloan.cx.MarkLoanRequestCancelRequest
	(*MarkLoanRequestCancelResponse)(nil),                                     // 16: preapprovedloan.cx.MarkLoanRequestCancelResponse
	(*MandateAccount)(nil),                                                    // 17: preapprovedloan.cx.MandateAccount
	(*GetForeclosureRequest)(nil),                                             // 18: preapprovedloan.cx.GetForeclosureRequest
	(*GetForeclosureResponse)(nil),                                            // 19: preapprovedloan.cx.GetForeclosureResponse
	(*GetLoanAccountAdditionalDetailsRequest)(nil),                            // 20: preapprovedloan.cx.GetLoanAccountAdditionalDetailsRequest
	(*GetLoanAccountAdditionalDetailsResponse)(nil),                           // 21: preapprovedloan.cx.GetLoanAccountAdditionalDetailsResponse
	(*LoanUpcomingEmi)(nil),                                                   // 22: preapprovedloan.cx.LoanUpcomingEmi
	(*FiftyfinLamfLoanAccountDetails)(nil),                                    // 23: preapprovedloan.cx.FiftyfinLamfLoanAccountDetails
	(*GetLoanRequestAdditionalDetailsRequest)(nil),                            // 24: preapprovedloan.cx.GetLoanRequestAdditionalDetailsRequest
	(*GetLoanRequestAdditionalDetailsResponse)(nil),                           // 25: preapprovedloan.cx.GetLoanRequestAdditionalDetailsResponse
	(*SubmitManualReviewRequest_ReviewerDetails)(nil),                         // 26: preapprovedloan.cx.SubmitManualReviewRequest.ReviewerDetails
	(*GetLoanOfferSummaryResponse_LoanOfferForCX)(nil),                        // 27: preapprovedloan.cx.GetLoanOfferSummaryResponse.LoanOfferForCX
	(*GetLoanRequestSummaryResponse_LoanRequestForCX)(nil),                    // 28: preapprovedloan.cx.GetLoanRequestSummaryResponse.LoanRequestForCX
	(*GetLoanAccountSummaryResponse_LoanAccountForCX)(nil),                    // 29: preapprovedloan.cx.GetLoanAccountSummaryResponse.LoanAccountForCX
	(*GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails)(nil), // 30: preapprovedloan.cx.GetLoanUserDetailsResponse.CurrentMonthUserLoanEligibilityDetails
	(*GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails)(nil),      // 31: preapprovedloan.cx.GetLoanUserDetailsResponse.CurrentUserLoanApplicationDetails
	(*GetLoanUserDetailsResponse_LoanOfferDetails)(nil),                       // 32: preapprovedloan.cx.GetLoanUserDetailsResponse.LoanOfferDetails
	(*GetLoanDetailsResponse_LoanDetailsForCx)(nil),                           // 33: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx
	(*GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction)(nil),       // 34: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.LoanPastTransaction
	(*GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi)(nil),           // 35: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.LoanUpcomingEmi
	(*GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails)(nil),           // 36: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.MfPledgeDetails
	(*GetQueueElementsResponse_QueueElement)(nil),                             // 37: preapprovedloan.cx.GetQueueElementsResponse.QueueElement
	(*FiftyfinLamfLoanAccountDetails_LoanStatement)(nil),                      // 38: preapprovedloan.cx.FiftyfinLamfLoanAccountDetails.LoanStatement
	(Verdict)(0),                                       // 39: preapprovedloan.cx.Verdict
	(*rpc.Status)(nil),                                 // 40: rpc.Status
	(preapprovedloan.Vendor)(0),                        // 41: preapprovedloan.Vendor
	(*preapprovedloan.LoanHeader)(nil),                 // 42: preapprovedloan.LoanHeader
	(*preapprovedloan.LoanRequest)(nil),                // 43: preapprovedloan.LoanRequest
	(*preapprovedloan.LoanApplicant)(nil),              // 44: preapprovedloan.LoanApplicant
	(*money.Money)(nil),                                // 45: google.type.Money
	(persistentqueue.PayloadType)(0),                   // 46: persistentqueue.PayloadType
	(*timestamppb.Timestamp)(nil),                      // 47: google.protobuf.Timestamp
	(LoanAccountAdditionalDetailsFieldMask)(0),         // 48: preapprovedloan.cx.LoanAccountAdditionalDetailsFieldMask
	(*preapprovedloan.LoanAccount)(nil),                // 49: preapprovedloan.LoanAccount
	(*preapprovedloan.LoanPaymentRequest)(nil),         // 50: preapprovedloan.LoanPaymentRequest
	(*preapprovedloan.LoanInstallmentPayout)(nil),      // 51: preapprovedloan.LoanInstallmentPayout
	(*typesv2.BankAccountDetails)(nil),                 // 52: api.typesv2.BankAccountDetails
	(*date.Date)(nil),                                  // 53: google.type.Date
	(LoanRequestAdditionalDetailsFieldMask)(0),         // 54: preapprovedloan.cx.LoanRequestAdditionalDetailsFieldMask
	(*preapprovedloan.LoanStepExecution)(nil),          // 55: preapprovedloan.LoanStepExecution
	(*preapprovedloan.OfferConstraints)(nil),           // 56: preapprovedloan.OfferConstraints
	(*preapprovedloan.OfferProcessingInfo)(nil),        // 57: preapprovedloan.OfferProcessingInfo
	(*preapprovedloan.LoanRequestDetails)(nil),         // 58: preapprovedloan.LoanRequestDetails
	(preapprovedloan.LoanRequestType)(0),               // 59: preapprovedloan.LoanRequestType
	(preapprovedloan.LoanRequestStatus)(0),             // 60: preapprovedloan.LoanRequestStatus
	(preapprovedloan.LoanRequestSubStatus)(0),          // 61: preapprovedloan.LoanRequestSubStatus
	(preapprovedloan.LoanType)(0),                      // 62: preapprovedloan.LoanType
	(*preapprovedloan.LoanAmountInfo)(nil),             // 63: preapprovedloan.LoanAmountInfo
	(*preapprovedloan.LoanAccountDetails)(nil),         // 64: preapprovedloan.LoanAccountDetails
	(preapprovedloan.LoanAccountStatus)(0),             // 65: preapprovedloan.LoanAccountStatus
	(preapprovedloan.LoanProgram)(0),                   // 66: preapprovedloan.LoanProgram
	(*preapprovedloan.FiftyFinLamfConstraintInfo)(nil), // 67: preapprovedloan.FiftyFinLamfConstraintInfo
	(preapprovedloan.LoanOfferType)(0),                 // 68: preapprovedloan.LoanOfferType
	(*persistentqueue.LivenessReview)(nil),             // 69: persistentqueue.LivenessReview
	(*persistentqueue.FacematchReview)(nil),            // 70: persistentqueue.FacematchReview
	(*fiftyfin.LoanStatementTxn)(nil),                  // 71: vendors.fiftyfin.LoanStatementTxn
}
var file_api_preapprovedloan_cx_service_proto_depIdxs = []int32{
	39,  // 0: preapprovedloan.cx.SubmitManualReviewRequest.verdict:type_name -> preapprovedloan.cx.Verdict
	26,  // 1: preapprovedloan.cx.SubmitManualReviewRequest.reviewer_details:type_name -> preapprovedloan.cx.SubmitManualReviewRequest.ReviewerDetails
	40,  // 2: preapprovedloan.cx.SubmitManualReviewResponse.status:type_name -> rpc.Status
	41,  // 3: preapprovedloan.cx.GetLoanOfferSummaryRequest.vendor:type_name -> preapprovedloan.Vendor
	40,  // 4: preapprovedloan.cx.GetLoanOfferSummaryResponse.status:type_name -> rpc.Status
	27,  // 5: preapprovedloan.cx.GetLoanOfferSummaryResponse.loanOffer:type_name -> preapprovedloan.cx.GetLoanOfferSummaryResponse.LoanOfferForCX
	41,  // 6: preapprovedloan.cx.GetLoanRequestSummaryRequest.vendor:type_name -> preapprovedloan.Vendor
	40,  // 7: preapprovedloan.cx.GetLoanRequestSummaryResponse.status:type_name -> rpc.Status
	28,  // 8: preapprovedloan.cx.GetLoanRequestSummaryResponse.loanRequests:type_name -> preapprovedloan.cx.GetLoanRequestSummaryResponse.LoanRequestForCX
	41,  // 9: preapprovedloan.cx.GetLoanAccountSummaryRequest.vendor:type_name -> preapprovedloan.Vendor
	40,  // 10: preapprovedloan.cx.GetLoanAccountSummaryResponse.status:type_name -> rpc.Status
	29,  // 11: preapprovedloan.cx.GetLoanAccountSummaryResponse.loanAccounts:type_name -> preapprovedloan.cx.GetLoanAccountSummaryResponse.LoanAccountForCX
	42,  // 12: preapprovedloan.cx.GetLoanUserDetailsRequest.loan_header:type_name -> preapprovedloan.LoanHeader
	40,  // 13: preapprovedloan.cx.GetLoanUserDetailsResponse.status:type_name -> rpc.Status
	30,  // 14: preapprovedloan.cx.GetLoanUserDetailsResponse.current_month_user_loan_eligibility_details:type_name -> preapprovedloan.cx.GetLoanUserDetailsResponse.CurrentMonthUserLoanEligibilityDetails
	31,  // 15: preapprovedloan.cx.GetLoanUserDetailsResponse.current_user_loan_application_details:type_name -> preapprovedloan.cx.GetLoanUserDetailsResponse.CurrentUserLoanApplicationDetails
	32,  // 16: preapprovedloan.cx.GetLoanUserDetailsResponse.loan_offer_details:type_name -> preapprovedloan.cx.GetLoanUserDetailsResponse.LoanOfferDetails
	43,  // 17: preapprovedloan.cx.GetLoanUserDetailsResponse.eligibility_loan_requests:type_name -> preapprovedloan.LoanRequest
	43,  // 18: preapprovedloan.cx.GetLoanUserDetailsResponse.application_loan_requests:type_name -> preapprovedloan.LoanRequest
	44,  // 19: preapprovedloan.cx.GetLoanUserDetailsResponse.applicants:type_name -> preapprovedloan.LoanApplicant
	42,  // 20: preapprovedloan.cx.GetLoanDetailsRequest.loan_header:type_name -> preapprovedloan.LoanHeader
	40,  // 21: preapprovedloan.cx.GetLoanDetailsResponse.status:type_name -> rpc.Status
	33,  // 22: preapprovedloan.cx.GetLoanDetailsResponse.loan_details:type_name -> preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx
	45,  // 23: preapprovedloan.cx.ForeclosureDetails.total_outstanding_amount:type_name -> google.type.Money
	45,  // 24: preapprovedloan.cx.ForeclosureDetails.principal_outstanding_amount:type_name -> google.type.Money
	45,  // 25: preapprovedloan.cx.ForeclosureDetails.interest_outstanding_amount:type_name -> google.type.Money
	45,  // 26: preapprovedloan.cx.ForeclosureDetails.penalty_amt:type_name -> google.type.Money
	45,  // 27: preapprovedloan.cx.ForeclosureDetails.fees_amt:type_name -> google.type.Money
	45,  // 28: preapprovedloan.cx.ForeclosureDetails.other_charges:type_name -> google.type.Money
	46,  // 29: preapprovedloan.cx.GetQueueElementsRequest.payload_type:type_name -> persistentqueue.PayloadType
	47,  // 30: preapprovedloan.cx.GetQueueElementsRequest.from_time:type_name -> google.protobuf.Timestamp
	47,  // 31: preapprovedloan.cx.GetQueueElementsRequest.to_time:type_name -> google.protobuf.Timestamp
	40,  // 32: preapprovedloan.cx.GetQueueElementsResponse.status:type_name -> rpc.Status
	37,  // 33: preapprovedloan.cx.GetQueueElementsResponse.elements:type_name -> preapprovedloan.cx.GetQueueElementsResponse.QueueElement
	40,  // 34: preapprovedloan.cx.MarkLoanRequestCancelResponse.status:type_name -> rpc.Status
	42,  // 35: preapprovedloan.cx.GetForeclosureRequest.loan_header:type_name -> preapprovedloan.LoanHeader
	40,  // 36: preapprovedloan.cx.GetForeclosureResponse.status:type_name -> rpc.Status
	45,  // 37: preapprovedloan.cx.GetForeclosureResponse.total_outstanding_amt:type_name -> google.type.Money
	45,  // 38: preapprovedloan.cx.GetForeclosureResponse.principal_outstanding_amt:type_name -> google.type.Money
	45,  // 39: preapprovedloan.cx.GetForeclosureResponse.interest_outstanding_amt:type_name -> google.type.Money
	45,  // 40: preapprovedloan.cx.GetForeclosureResponse.penalty_amt:type_name -> google.type.Money
	45,  // 41: preapprovedloan.cx.GetForeclosureResponse.fees_amt:type_name -> google.type.Money
	45,  // 42: preapprovedloan.cx.GetForeclosureResponse.other_charges:type_name -> google.type.Money
	42,  // 43: preapprovedloan.cx.GetLoanAccountAdditionalDetailsRequest.loan_header:type_name -> preapprovedloan.LoanHeader
	48,  // 44: preapprovedloan.cx.GetLoanAccountAdditionalDetailsRequest.field_masks:type_name -> preapprovedloan.cx.LoanAccountAdditionalDetailsFieldMask
	40,  // 45: preapprovedloan.cx.GetLoanAccountAdditionalDetailsResponse.status:type_name -> rpc.Status
	49,  // 46: preapprovedloan.cx.GetLoanAccountAdditionalDetailsResponse.loan_account:type_name -> preapprovedloan.LoanAccount
	50,  // 47: preapprovedloan.cx.GetLoanAccountAdditionalDetailsResponse.loan_payment_requests:type_name -> preapprovedloan.LoanPaymentRequest
	51,  // 48: preapprovedloan.cx.GetLoanAccountAdditionalDetailsResponse.installment_payouts:type_name -> preapprovedloan.LoanInstallmentPayout
	43,  // 49: preapprovedloan.cx.GetLoanAccountAdditionalDetailsResponse.loan_account_closure_requests:type_name -> preapprovedloan.LoanRequest
	23,  // 50: preapprovedloan.cx.GetLoanAccountAdditionalDetailsResponse.fiftyfin_lamf_details:type_name -> preapprovedloan.cx.FiftyfinLamfLoanAccountDetails
	22,  // 51: preapprovedloan.cx.GetLoanAccountAdditionalDetailsResponse.loan_upcoming_emis:type_name -> preapprovedloan.cx.LoanUpcomingEmi
	52,  // 52: preapprovedloan.cx.GetLoanAccountAdditionalDetailsResponse.bank_account_details:type_name -> api.typesv2.BankAccountDetails
	53,  // 53: preapprovedloan.cx.LoanUpcomingEmi.next_emi_date:type_name -> google.type.Date
	45,  // 54: preapprovedloan.cx.LoanUpcomingEmi.amount:type_name -> google.type.Money
	38,  // 55: preapprovedloan.cx.FiftyfinLamfLoanAccountDetails.soa:type_name -> preapprovedloan.cx.FiftyfinLamfLoanAccountDetails.LoanStatement
	42,  // 56: preapprovedloan.cx.GetLoanRequestAdditionalDetailsRequest.loan_header:type_name -> preapprovedloan.LoanHeader
	54,  // 57: preapprovedloan.cx.GetLoanRequestAdditionalDetailsRequest.field_masks:type_name -> preapprovedloan.cx.LoanRequestAdditionalDetailsFieldMask
	40,  // 58: preapprovedloan.cx.GetLoanRequestAdditionalDetailsResponse.status:type_name -> rpc.Status
	43,  // 59: preapprovedloan.cx.GetLoanRequestAdditionalDetailsResponse.loan_request:type_name -> preapprovedloan.LoanRequest
	55,  // 60: preapprovedloan.cx.GetLoanRequestAdditionalDetailsResponse.loan_steps:type_name -> preapprovedloan.LoanStepExecution
	47,  // 61: preapprovedloan.cx.SubmitManualReviewRequest.ReviewerDetails.reviewed_at:type_name -> google.protobuf.Timestamp
	41,  // 62: preapprovedloan.cx.GetLoanOfferSummaryResponse.LoanOfferForCX.vendor:type_name -> preapprovedloan.Vendor
	56,  // 63: preapprovedloan.cx.GetLoanOfferSummaryResponse.LoanOfferForCX.offer_constraints:type_name -> preapprovedloan.OfferConstraints
	57,  // 64: preapprovedloan.cx.GetLoanOfferSummaryResponse.LoanOfferForCX.processing_info:type_name -> preapprovedloan.OfferProcessingInfo
	41,  // 65: preapprovedloan.cx.GetLoanRequestSummaryResponse.LoanRequestForCX.vendor:type_name -> preapprovedloan.Vendor
	58,  // 66: preapprovedloan.cx.GetLoanRequestSummaryResponse.LoanRequestForCX.details:type_name -> preapprovedloan.LoanRequestDetails
	59,  // 67: preapprovedloan.cx.GetLoanRequestSummaryResponse.LoanRequestForCX.type:type_name -> preapprovedloan.LoanRequestType
	60,  // 68: preapprovedloan.cx.GetLoanRequestSummaryResponse.LoanRequestForCX.status:type_name -> preapprovedloan.LoanRequestStatus
	61,  // 69: preapprovedloan.cx.GetLoanRequestSummaryResponse.LoanRequestForCX.sub_status:type_name -> preapprovedloan.LoanRequestSubStatus
	41,  // 70: preapprovedloan.cx.GetLoanAccountSummaryResponse.LoanAccountForCX.vendor:type_name -> preapprovedloan.Vendor
	62,  // 71: preapprovedloan.cx.GetLoanAccountSummaryResponse.LoanAccountForCX.loan_type:type_name -> preapprovedloan.LoanType
	63,  // 72: preapprovedloan.cx.GetLoanAccountSummaryResponse.LoanAccountForCX.loan_amount_info:type_name -> preapprovedloan.LoanAmountInfo
	53,  // 73: preapprovedloan.cx.GetLoanAccountSummaryResponse.LoanAccountForCX.loan_end_date:type_name -> google.type.Date
	53,  // 74: preapprovedloan.cx.GetLoanAccountSummaryResponse.LoanAccountForCX.maturity_date:type_name -> google.type.Date
	64,  // 75: preapprovedloan.cx.GetLoanAccountSummaryResponse.LoanAccountForCX.details:type_name -> preapprovedloan.LoanAccountDetails
	65,  // 76: preapprovedloan.cx.GetLoanAccountSummaryResponse.LoanAccountForCX.status:type_name -> preapprovedloan.LoanAccountStatus
	66,  // 77: preapprovedloan.cx.GetLoanAccountSummaryResponse.LoanAccountForCX.loan_program:type_name -> preapprovedloan.LoanProgram
	45,  // 78: preapprovedloan.cx.GetLoanUserDetailsResponse.CurrentUserLoanApplicationDetails.loan_amount_applied:type_name -> google.type.Money
	45,  // 79: preapprovedloan.cx.GetLoanUserDetailsResponse.LoanOfferDetails.min_loan_amount:type_name -> google.type.Money
	45,  // 80: preapprovedloan.cx.GetLoanUserDetailsResponse.LoanOfferDetails.max_loan_amount:type_name -> google.type.Money
	45,  // 81: preapprovedloan.cx.GetLoanUserDetailsResponse.LoanOfferDetails.max_emi_amount:type_name -> google.type.Money
	53,  // 82: preapprovedloan.cx.GetLoanUserDetailsResponse.LoanOfferDetails.offer_start_date:type_name -> google.type.Date
	53,  // 83: preapprovedloan.cx.GetLoanUserDetailsResponse.LoanOfferDetails.offer_end_date:type_name -> google.type.Date
	67,  // 84: preapprovedloan.cx.GetLoanUserDetailsResponse.LoanOfferDetails.fiftyfin_lamf_constraint_info:type_name -> preapprovedloan.FiftyFinLamfConstraintInfo
	68,  // 85: preapprovedloan.cx.GetLoanUserDetailsResponse.LoanOfferDetails.loan_offer_type:type_name -> preapprovedloan.LoanOfferType
	47,  // 86: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.loan_open_date:type_name -> google.protobuf.Timestamp
	45,  // 87: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.loan_amount:type_name -> google.type.Money
	45,  // 88: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.processing_fee:type_name -> google.type.Money
	45,  // 89: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.outstanding_amount:type_name -> google.type.Money
	45,  // 90: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.gst:type_name -> google.type.Money
	45,  // 91: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.broken_period_interest:type_name -> google.type.Money
	45,  // 92: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.pre_closure_fee:type_name -> google.type.Money
	34,  // 93: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.loan_past_transactions:type_name -> preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.LoanPastTransaction
	35,  // 94: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.loan_upcoming_emis:type_name -> preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.LoanUpcomingEmi
	36,  // 95: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.mf_pledge_details:type_name -> preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.MfPledgeDetails
	17,  // 96: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.mandate_account:type_name -> preapprovedloan.cx.MandateAccount
	52,  // 97: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.bank_account_details:type_name -> api.typesv2.BankAccountDetails
	49,  // 98: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.loan_account:type_name -> preapprovedloan.LoanAccount
	12,  // 99: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.fore_closure_details:type_name -> preapprovedloan.cx.ForeclosureDetails
	47,  // 100: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.LoanPastTransaction.timestamp:type_name -> google.protobuf.Timestamp
	45,  // 101: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.LoanPastTransaction.amount:type_name -> google.type.Money
	45,  // 102: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.LoanPastTransaction.charges:type_name -> google.type.Money
	53,  // 103: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.LoanUpcomingEmi.next_emi_date:type_name -> google.type.Date
	45,  // 104: preapprovedloan.cx.GetLoanDetailsResponse.LoanDetailsForCx.LoanUpcomingEmi.amount:type_name -> google.type.Money
	69,  // 105: preapprovedloan.cx.GetQueueElementsResponse.QueueElement.liveness_review:type_name -> persistentqueue.LivenessReview
	70,  // 106: preapprovedloan.cx.GetQueueElementsResponse.QueueElement.facematch_review:type_name -> persistentqueue.FacematchReview
	71,  // 107: preapprovedloan.cx.FiftyfinLamfLoanAccountDetails.LoanStatement.transactions:type_name -> vendors.fiftyfin.LoanStatementTxn
	0,   // 108: preapprovedloan.cx.Cx.SubmitManualReview:input_type -> preapprovedloan.cx.SubmitManualReviewRequest
	2,   // 109: preapprovedloan.cx.Cx.GetLoanOfferSummary:input_type -> preapprovedloan.cx.GetLoanOfferSummaryRequest
	4,   // 110: preapprovedloan.cx.Cx.GetLoanRequestSummary:input_type -> preapprovedloan.cx.GetLoanRequestSummaryRequest
	6,   // 111: preapprovedloan.cx.Cx.GetLoanAccountSummary:input_type -> preapprovedloan.cx.GetLoanAccountSummaryRequest
	8,   // 112: preapprovedloan.cx.Cx.GetLoanUserDetails:input_type -> preapprovedloan.cx.GetLoanUserDetailsRequest
	10,  // 113: preapprovedloan.cx.Cx.GetLoanDetails:input_type -> preapprovedloan.cx.GetLoanDetailsRequest
	13,  // 114: preapprovedloan.cx.Cx.GetQueueElements:input_type -> preapprovedloan.cx.GetQueueElementsRequest
	15,  // 115: preapprovedloan.cx.Cx.MarkLoanRequestCancel:input_type -> preapprovedloan.cx.MarkLoanRequestCancelRequest
	18,  // 116: preapprovedloan.cx.Cx.GetForeclosureDetails:input_type -> preapprovedloan.cx.GetForeclosureRequest
	24,  // 117: preapprovedloan.cx.Cx.GetLoanRequestAdditionalDetails:input_type -> preapprovedloan.cx.GetLoanRequestAdditionalDetailsRequest
	20,  // 118: preapprovedloan.cx.Cx.GetLoanAccountAdditionalDetails:input_type -> preapprovedloan.cx.GetLoanAccountAdditionalDetailsRequest
	1,   // 119: preapprovedloan.cx.Cx.SubmitManualReview:output_type -> preapprovedloan.cx.SubmitManualReviewResponse
	3,   // 120: preapprovedloan.cx.Cx.GetLoanOfferSummary:output_type -> preapprovedloan.cx.GetLoanOfferSummaryResponse
	5,   // 121: preapprovedloan.cx.Cx.GetLoanRequestSummary:output_type -> preapprovedloan.cx.GetLoanRequestSummaryResponse
	7,   // 122: preapprovedloan.cx.Cx.GetLoanAccountSummary:output_type -> preapprovedloan.cx.GetLoanAccountSummaryResponse
	9,   // 123: preapprovedloan.cx.Cx.GetLoanUserDetails:output_type -> preapprovedloan.cx.GetLoanUserDetailsResponse
	11,  // 124: preapprovedloan.cx.Cx.GetLoanDetails:output_type -> preapprovedloan.cx.GetLoanDetailsResponse
	14,  // 125: preapprovedloan.cx.Cx.GetQueueElements:output_type -> preapprovedloan.cx.GetQueueElementsResponse
	16,  // 126: preapprovedloan.cx.Cx.MarkLoanRequestCancel:output_type -> preapprovedloan.cx.MarkLoanRequestCancelResponse
	19,  // 127: preapprovedloan.cx.Cx.GetForeclosureDetails:output_type -> preapprovedloan.cx.GetForeclosureResponse
	25,  // 128: preapprovedloan.cx.Cx.GetLoanRequestAdditionalDetails:output_type -> preapprovedloan.cx.GetLoanRequestAdditionalDetailsResponse
	21,  // 129: preapprovedloan.cx.Cx.GetLoanAccountAdditionalDetails:output_type -> preapprovedloan.cx.GetLoanAccountAdditionalDetailsResponse
	119, // [119:130] is the sub-list for method output_type
	108, // [108:119] is the sub-list for method input_type
	108, // [108:108] is the sub-list for extension type_name
	108, // [108:108] is the sub-list for extension extendee
	0,   // [0:108] is the sub-list for field type_name
}

func init() { file_api_preapprovedloan_cx_service_proto_init() }
func file_api_preapprovedloan_cx_service_proto_init() {
	if File_api_preapprovedloan_cx_service_proto != nil {
		return
	}
	file_api_preapprovedloan_cx_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_preapprovedloan_cx_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitManualReviewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitManualReviewResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanOfferSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanOfferSummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanRequestSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanRequestSummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanAccountSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanAccountSummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanUserDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanUserDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForeclosureDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQueueElementsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQueueElementsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkLoanRequestCancelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkLoanRequestCancelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MandateAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetForeclosureRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetForeclosureResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanAccountAdditionalDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanAccountAdditionalDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanUpcomingEmi); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiftyfinLamfLoanAccountDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanRequestAdditionalDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanRequestAdditionalDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitManualReviewRequest_ReviewerDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanOfferSummaryResponse_LoanOfferForCX); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanRequestSummaryResponse_LoanRequestForCX); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanAccountSummaryResponse_LoanAccountForCX); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanUserDetailsResponse_LoanOfferDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDetailsResponse_LoanDetailsForCx); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQueueElementsResponse_QueueElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_cx_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiftyfinLamfLoanAccountDetails_LoanStatement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_preapprovedloan_cx_service_proto_msgTypes[21].OneofWrappers = []interface{}{
		(*GetLoanAccountAdditionalDetailsResponse_FiftyfinLamfDetails)(nil),
	}
	file_api_preapprovedloan_cx_service_proto_msgTypes[32].OneofWrappers = []interface{}{
		(*GetLoanUserDetailsResponse_LoanOfferDetails_FiftyfinLamfConstraintInfo)(nil),
	}
	file_api_preapprovedloan_cx_service_proto_msgTypes[37].OneofWrappers = []interface{}{
		(*GetQueueElementsResponse_QueueElement_LivenessReview)(nil),
		(*GetQueueElementsResponse_QueueElement_FacematchReview)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_preapprovedloan_cx_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_preapprovedloan_cx_service_proto_goTypes,
		DependencyIndexes: file_api_preapprovedloan_cx_service_proto_depIdxs,
		MessageInfos:      file_api_preapprovedloan_cx_service_proto_msgTypes,
	}.Build()
	File_api_preapprovedloan_cx_service_proto = out.File
	file_api_preapprovedloan_cx_service_proto_rawDesc = nil
	file_api_preapprovedloan_cx_service_proto_goTypes = nil
	file_api_preapprovedloan_cx_service_proto_depIdxs = nil
}
