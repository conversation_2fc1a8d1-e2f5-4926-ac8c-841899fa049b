// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/preapprovedloan/cx/service.proto

package cx

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Cx_SubmitManualReview_FullMethodName              = "/preapprovedloan.cx.Cx/SubmitManualReview"
	Cx_GetLoanOfferSummary_FullMethodName             = "/preapprovedloan.cx.Cx/GetLoanOfferSummary"
	Cx_GetLoanRequestSummary_FullMethodName           = "/preapprovedloan.cx.Cx/GetLoanRequestSummary"
	Cx_GetLoanAccountSummary_FullMethodName           = "/preapprovedloan.cx.Cx/GetLoanAccountSummary"
	Cx_GetLoanUserDetails_FullMethodName              = "/preapprovedloan.cx.Cx/GetLoanUserDetails"
	Cx_GetLoanDetails_FullMethodName                  = "/preapprovedloan.cx.Cx/GetLoanDetails"
	Cx_GetQueueElements_FullMethodName                = "/preapprovedloan.cx.Cx/GetQueueElements"
	Cx_MarkLoanRequestCancel_FullMethodName           = "/preapprovedloan.cx.Cx/MarkLoanRequestCancel"
	Cx_GetForeclosureDetails_FullMethodName           = "/preapprovedloan.cx.Cx/GetForeclosureDetails"
	Cx_GetLoanRequestAdditionalDetails_FullMethodName = "/preapprovedloan.cx.Cx/GetLoanRequestAdditionalDetails"
	Cx_GetLoanAccountAdditionalDetails_FullMethodName = "/preapprovedloan.cx.Cx/GetLoanAccountAdditionalDetails"
)

// CxClient is the client API for Cx service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CxClient interface {
	// update loan step execution records. This is to be used only via dev actions and that too for Op agents to approve/reject
	// liveness or facematch manual review. Other than this step executions are expected to be updated via workflows.
	SubmitManualReview(ctx context.Context, in *SubmitManualReviewRequest, opts ...grpc.CallOption) (*SubmitManualReviewResponse, error)
	// RPC to be used to get details of all the loan offers for an actor
	GetLoanOfferSummary(ctx context.Context, in *GetLoanOfferSummaryRequest, opts ...grpc.CallOption) (*GetLoanOfferSummaryResponse, error)
	// RPC to be used to get details of all the loan requests for an actor
	GetLoanRequestSummary(ctx context.Context, in *GetLoanRequestSummaryRequest, opts ...grpc.CallOption) (*GetLoanRequestSummaryResponse, error)
	// RPC to be used to get details of all the loan account for an actor
	GetLoanAccountSummary(ctx context.Context, in *GetLoanAccountSummaryRequest, opts ...grpc.CallOption) (*GetLoanAccountSummaryResponse, error)
	// RPC to be used to get basic loan details for an actor
	GetLoanUserDetails(ctx context.Context, in *GetLoanUserDetailsRequest, opts ...grpc.CallOption) (*GetLoanUserDetailsResponse, error)
	// RPC to be used to get details of loan availed by an actor
	GetLoanDetails(ctx context.Context, in *GetLoanDetailsRequest, opts ...grpc.CallOption) (*GetLoanDetailsResponse, error)
	// rpc to fetch the elements in the persistent queue based on a specific payload type
	GetQueueElements(ctx context.Context, in *GetQueueElementsRequest, opts ...grpc.CallOption) (*GetQueueElementsResponse, error)
	// rpc to update Loan Request status and sub status
	MarkLoanRequestCancel(ctx context.Context, in *MarkLoanRequestCancelRequest, opts ...grpc.CallOption) (*MarkLoanRequestCancelResponse, error)
	// rpc to return fetch foreclosure details of a user's loan account
	GetForeclosureDetails(ctx context.Context, in *GetForeclosureRequest, opts ...grpc.CallOption) (*GetForeclosureResponse, error)
	// This rpc is used for fetching additional details related to loan request table rows
	GetLoanRequestAdditionalDetails(ctx context.Context, in *GetLoanRequestAdditionalDetailsRequest, opts ...grpc.CallOption) (*GetLoanRequestAdditionalDetailsResponse, error)
	// This rpc is used for fetching additional details related to loan account table rows
	GetLoanAccountAdditionalDetails(ctx context.Context, in *GetLoanAccountAdditionalDetailsRequest, opts ...grpc.CallOption) (*GetLoanAccountAdditionalDetailsResponse, error)
}

type cxClient struct {
	cc grpc.ClientConnInterface
}

func NewCxClient(cc grpc.ClientConnInterface) CxClient {
	return &cxClient{cc}
}

func (c *cxClient) SubmitManualReview(ctx context.Context, in *SubmitManualReviewRequest, opts ...grpc.CallOption) (*SubmitManualReviewResponse, error) {
	out := new(SubmitManualReviewResponse)
	err := c.cc.Invoke(ctx, Cx_SubmitManualReview_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cxClient) GetLoanOfferSummary(ctx context.Context, in *GetLoanOfferSummaryRequest, opts ...grpc.CallOption) (*GetLoanOfferSummaryResponse, error) {
	out := new(GetLoanOfferSummaryResponse)
	err := c.cc.Invoke(ctx, Cx_GetLoanOfferSummary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cxClient) GetLoanRequestSummary(ctx context.Context, in *GetLoanRequestSummaryRequest, opts ...grpc.CallOption) (*GetLoanRequestSummaryResponse, error) {
	out := new(GetLoanRequestSummaryResponse)
	err := c.cc.Invoke(ctx, Cx_GetLoanRequestSummary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cxClient) GetLoanAccountSummary(ctx context.Context, in *GetLoanAccountSummaryRequest, opts ...grpc.CallOption) (*GetLoanAccountSummaryResponse, error) {
	out := new(GetLoanAccountSummaryResponse)
	err := c.cc.Invoke(ctx, Cx_GetLoanAccountSummary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cxClient) GetLoanUserDetails(ctx context.Context, in *GetLoanUserDetailsRequest, opts ...grpc.CallOption) (*GetLoanUserDetailsResponse, error) {
	out := new(GetLoanUserDetailsResponse)
	err := c.cc.Invoke(ctx, Cx_GetLoanUserDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cxClient) GetLoanDetails(ctx context.Context, in *GetLoanDetailsRequest, opts ...grpc.CallOption) (*GetLoanDetailsResponse, error) {
	out := new(GetLoanDetailsResponse)
	err := c.cc.Invoke(ctx, Cx_GetLoanDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cxClient) GetQueueElements(ctx context.Context, in *GetQueueElementsRequest, opts ...grpc.CallOption) (*GetQueueElementsResponse, error) {
	out := new(GetQueueElementsResponse)
	err := c.cc.Invoke(ctx, Cx_GetQueueElements_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cxClient) MarkLoanRequestCancel(ctx context.Context, in *MarkLoanRequestCancelRequest, opts ...grpc.CallOption) (*MarkLoanRequestCancelResponse, error) {
	out := new(MarkLoanRequestCancelResponse)
	err := c.cc.Invoke(ctx, Cx_MarkLoanRequestCancel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cxClient) GetForeclosureDetails(ctx context.Context, in *GetForeclosureRequest, opts ...grpc.CallOption) (*GetForeclosureResponse, error) {
	out := new(GetForeclosureResponse)
	err := c.cc.Invoke(ctx, Cx_GetForeclosureDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cxClient) GetLoanRequestAdditionalDetails(ctx context.Context, in *GetLoanRequestAdditionalDetailsRequest, opts ...grpc.CallOption) (*GetLoanRequestAdditionalDetailsResponse, error) {
	out := new(GetLoanRequestAdditionalDetailsResponse)
	err := c.cc.Invoke(ctx, Cx_GetLoanRequestAdditionalDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cxClient) GetLoanAccountAdditionalDetails(ctx context.Context, in *GetLoanAccountAdditionalDetailsRequest, opts ...grpc.CallOption) (*GetLoanAccountAdditionalDetailsResponse, error) {
	out := new(GetLoanAccountAdditionalDetailsResponse)
	err := c.cc.Invoke(ctx, Cx_GetLoanAccountAdditionalDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CxServer is the server API for Cx service.
// All implementations should embed UnimplementedCxServer
// for forward compatibility
type CxServer interface {
	// update loan step execution records. This is to be used only via dev actions and that too for Op agents to approve/reject
	// liveness or facematch manual review. Other than this step executions are expected to be updated via workflows.
	SubmitManualReview(context.Context, *SubmitManualReviewRequest) (*SubmitManualReviewResponse, error)
	// RPC to be used to get details of all the loan offers for an actor
	GetLoanOfferSummary(context.Context, *GetLoanOfferSummaryRequest) (*GetLoanOfferSummaryResponse, error)
	// RPC to be used to get details of all the loan requests for an actor
	GetLoanRequestSummary(context.Context, *GetLoanRequestSummaryRequest) (*GetLoanRequestSummaryResponse, error)
	// RPC to be used to get details of all the loan account for an actor
	GetLoanAccountSummary(context.Context, *GetLoanAccountSummaryRequest) (*GetLoanAccountSummaryResponse, error)
	// RPC to be used to get basic loan details for an actor
	GetLoanUserDetails(context.Context, *GetLoanUserDetailsRequest) (*GetLoanUserDetailsResponse, error)
	// RPC to be used to get details of loan availed by an actor
	GetLoanDetails(context.Context, *GetLoanDetailsRequest) (*GetLoanDetailsResponse, error)
	// rpc to fetch the elements in the persistent queue based on a specific payload type
	GetQueueElements(context.Context, *GetQueueElementsRequest) (*GetQueueElementsResponse, error)
	// rpc to update Loan Request status and sub status
	MarkLoanRequestCancel(context.Context, *MarkLoanRequestCancelRequest) (*MarkLoanRequestCancelResponse, error)
	// rpc to return fetch foreclosure details of a user's loan account
	GetForeclosureDetails(context.Context, *GetForeclosureRequest) (*GetForeclosureResponse, error)
	// This rpc is used for fetching additional details related to loan request table rows
	GetLoanRequestAdditionalDetails(context.Context, *GetLoanRequestAdditionalDetailsRequest) (*GetLoanRequestAdditionalDetailsResponse, error)
	// This rpc is used for fetching additional details related to loan account table rows
	GetLoanAccountAdditionalDetails(context.Context, *GetLoanAccountAdditionalDetailsRequest) (*GetLoanAccountAdditionalDetailsResponse, error)
}

// UnimplementedCxServer should be embedded to have forward compatible implementations.
type UnimplementedCxServer struct {
}

func (UnimplementedCxServer) SubmitManualReview(context.Context, *SubmitManualReviewRequest) (*SubmitManualReviewResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitManualReview not implemented")
}
func (UnimplementedCxServer) GetLoanOfferSummary(context.Context, *GetLoanOfferSummaryRequest) (*GetLoanOfferSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanOfferSummary not implemented")
}
func (UnimplementedCxServer) GetLoanRequestSummary(context.Context, *GetLoanRequestSummaryRequest) (*GetLoanRequestSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanRequestSummary not implemented")
}
func (UnimplementedCxServer) GetLoanAccountSummary(context.Context, *GetLoanAccountSummaryRequest) (*GetLoanAccountSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanAccountSummary not implemented")
}
func (UnimplementedCxServer) GetLoanUserDetails(context.Context, *GetLoanUserDetailsRequest) (*GetLoanUserDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanUserDetails not implemented")
}
func (UnimplementedCxServer) GetLoanDetails(context.Context, *GetLoanDetailsRequest) (*GetLoanDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanDetails not implemented")
}
func (UnimplementedCxServer) GetQueueElements(context.Context, *GetQueueElementsRequest) (*GetQueueElementsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetQueueElements not implemented")
}
func (UnimplementedCxServer) MarkLoanRequestCancel(context.Context, *MarkLoanRequestCancelRequest) (*MarkLoanRequestCancelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkLoanRequestCancel not implemented")
}
func (UnimplementedCxServer) GetForeclosureDetails(context.Context, *GetForeclosureRequest) (*GetForeclosureResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetForeclosureDetails not implemented")
}
func (UnimplementedCxServer) GetLoanRequestAdditionalDetails(context.Context, *GetLoanRequestAdditionalDetailsRequest) (*GetLoanRequestAdditionalDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanRequestAdditionalDetails not implemented")
}
func (UnimplementedCxServer) GetLoanAccountAdditionalDetails(context.Context, *GetLoanAccountAdditionalDetailsRequest) (*GetLoanAccountAdditionalDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanAccountAdditionalDetails not implemented")
}

// UnsafeCxServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CxServer will
// result in compilation errors.
type UnsafeCxServer interface {
	mustEmbedUnimplementedCxServer()
}

func RegisterCxServer(s grpc.ServiceRegistrar, srv CxServer) {
	s.RegisterService(&Cx_ServiceDesc, srv)
}

func _Cx_SubmitManualReview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitManualReviewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxServer).SubmitManualReview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cx_SubmitManualReview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxServer).SubmitManualReview(ctx, req.(*SubmitManualReviewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cx_GetLoanOfferSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanOfferSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxServer).GetLoanOfferSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cx_GetLoanOfferSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxServer).GetLoanOfferSummary(ctx, req.(*GetLoanOfferSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cx_GetLoanRequestSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanRequestSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxServer).GetLoanRequestSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cx_GetLoanRequestSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxServer).GetLoanRequestSummary(ctx, req.(*GetLoanRequestSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cx_GetLoanAccountSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanAccountSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxServer).GetLoanAccountSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cx_GetLoanAccountSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxServer).GetLoanAccountSummary(ctx, req.(*GetLoanAccountSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cx_GetLoanUserDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanUserDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxServer).GetLoanUserDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cx_GetLoanUserDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxServer).GetLoanUserDetails(ctx, req.(*GetLoanUserDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cx_GetLoanDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxServer).GetLoanDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cx_GetLoanDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxServer).GetLoanDetails(ctx, req.(*GetLoanDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cx_GetQueueElements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQueueElementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxServer).GetQueueElements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cx_GetQueueElements_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxServer).GetQueueElements(ctx, req.(*GetQueueElementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cx_MarkLoanRequestCancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkLoanRequestCancelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxServer).MarkLoanRequestCancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cx_MarkLoanRequestCancel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxServer).MarkLoanRequestCancel(ctx, req.(*MarkLoanRequestCancelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cx_GetForeclosureDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetForeclosureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxServer).GetForeclosureDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cx_GetForeclosureDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxServer).GetForeclosureDetails(ctx, req.(*GetForeclosureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cx_GetLoanRequestAdditionalDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanRequestAdditionalDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxServer).GetLoanRequestAdditionalDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cx_GetLoanRequestAdditionalDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxServer).GetLoanRequestAdditionalDetails(ctx, req.(*GetLoanRequestAdditionalDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cx_GetLoanAccountAdditionalDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanAccountAdditionalDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CxServer).GetLoanAccountAdditionalDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cx_GetLoanAccountAdditionalDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CxServer).GetLoanAccountAdditionalDetails(ctx, req.(*GetLoanAccountAdditionalDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Cx_ServiceDesc is the grpc.ServiceDesc for Cx service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Cx_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "preapprovedloan.cx.Cx",
	HandlerType: (*CxServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SubmitManualReview",
			Handler:    _Cx_SubmitManualReview_Handler,
		},
		{
			MethodName: "GetLoanOfferSummary",
			Handler:    _Cx_GetLoanOfferSummary_Handler,
		},
		{
			MethodName: "GetLoanRequestSummary",
			Handler:    _Cx_GetLoanRequestSummary_Handler,
		},
		{
			MethodName: "GetLoanAccountSummary",
			Handler:    _Cx_GetLoanAccountSummary_Handler,
		},
		{
			MethodName: "GetLoanUserDetails",
			Handler:    _Cx_GetLoanUserDetails_Handler,
		},
		{
			MethodName: "GetLoanDetails",
			Handler:    _Cx_GetLoanDetails_Handler,
		},
		{
			MethodName: "GetQueueElements",
			Handler:    _Cx_GetQueueElements_Handler,
		},
		{
			MethodName: "MarkLoanRequestCancel",
			Handler:    _Cx_MarkLoanRequestCancel_Handler,
		},
		{
			MethodName: "GetForeclosureDetails",
			Handler:    _Cx_GetForeclosureDetails_Handler,
		},
		{
			MethodName: "GetLoanRequestAdditionalDetails",
			Handler:    _Cx_GetLoanRequestAdditionalDetails_Handler,
		},
		{
			MethodName: "GetLoanAccountAdditionalDetails",
			Handler:    _Cx_GetLoanAccountAdditionalDetails_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/preapprovedloan/cx/service.proto",
}
