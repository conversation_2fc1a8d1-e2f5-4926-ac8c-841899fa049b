// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/preapprovedloan/cx/service.proto

package cx

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	persistentqueue "github.com/epifi/gamma/api/persistentqueue"

	preapprovedloan "github.com/epifi/gamma/api/preapprovedloan"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = persistentqueue.PayloadType(0)

	_ = preapprovedloan.Vendor(0)
)

// Validate checks the field values on SubmitManualReviewRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitManualReviewRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitManualReviewRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitManualReviewRequestMultiError, or nil if none found.
func (m *SubmitManualReviewRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitManualReviewRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for OrchId

	// no validation rules for Verdict

	if all {
		switch v := interface{}(m.GetReviewerDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitManualReviewRequestValidationError{
					field:  "ReviewerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitManualReviewRequestValidationError{
					field:  "ReviewerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReviewerDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitManualReviewRequestValidationError{
				field:  "ReviewerDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubmitManualReviewRequestMultiError(errors)
	}

	return nil
}

// SubmitManualReviewRequestMultiError is an error wrapping multiple validation
// errors returned by SubmitManualReviewRequest.ValidateAll() if the
// designated constraints aren't met.
type SubmitManualReviewRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitManualReviewRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitManualReviewRequestMultiError) AllErrors() []error { return m }

// SubmitManualReviewRequestValidationError is the validation error returned by
// SubmitManualReviewRequest.Validate if the designated constraints aren't met.
type SubmitManualReviewRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitManualReviewRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitManualReviewRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitManualReviewRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitManualReviewRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitManualReviewRequestValidationError) ErrorName() string {
	return "SubmitManualReviewRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitManualReviewRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitManualReviewRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitManualReviewRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitManualReviewRequestValidationError{}

// Validate checks the field values on SubmitManualReviewResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitManualReviewResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitManualReviewResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitManualReviewResponseMultiError, or nil if none found.
func (m *SubmitManualReviewResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitManualReviewResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitManualReviewResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitManualReviewResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitManualReviewResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubmitManualReviewResponseMultiError(errors)
	}

	return nil
}

// SubmitManualReviewResponseMultiError is an error wrapping multiple
// validation errors returned by SubmitManualReviewResponse.ValidateAll() if
// the designated constraints aren't met.
type SubmitManualReviewResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitManualReviewResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitManualReviewResponseMultiError) AllErrors() []error { return m }

// SubmitManualReviewResponseValidationError is the validation error returned
// by SubmitManualReviewResponse.Validate if the designated constraints aren't met.
type SubmitManualReviewResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitManualReviewResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitManualReviewResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitManualReviewResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitManualReviewResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitManualReviewResponseValidationError) ErrorName() string {
	return "SubmitManualReviewResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitManualReviewResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitManualReviewResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitManualReviewResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitManualReviewResponseValidationError{}

// Validate checks the field values on GetLoanOfferSummaryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanOfferSummaryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanOfferSummaryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanOfferSummaryRequestMultiError, or nil if none found.
func (m *GetLoanOfferSummaryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanOfferSummaryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Vendor

	if len(errors) > 0 {
		return GetLoanOfferSummaryRequestMultiError(errors)
	}

	return nil
}

// GetLoanOfferSummaryRequestMultiError is an error wrapping multiple
// validation errors returned by GetLoanOfferSummaryRequest.ValidateAll() if
// the designated constraints aren't met.
type GetLoanOfferSummaryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanOfferSummaryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanOfferSummaryRequestMultiError) AllErrors() []error { return m }

// GetLoanOfferSummaryRequestValidationError is the validation error returned
// by GetLoanOfferSummaryRequest.Validate if the designated constraints aren't met.
type GetLoanOfferSummaryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanOfferSummaryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanOfferSummaryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanOfferSummaryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanOfferSummaryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanOfferSummaryRequestValidationError) ErrorName() string {
	return "GetLoanOfferSummaryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanOfferSummaryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanOfferSummaryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanOfferSummaryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanOfferSummaryRequestValidationError{}

// Validate checks the field values on GetLoanOfferSummaryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanOfferSummaryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanOfferSummaryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanOfferSummaryResponseMultiError, or nil if none found.
func (m *GetLoanOfferSummaryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanOfferSummaryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanOfferSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanOfferSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanOfferSummaryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanOffer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanOfferSummaryResponseValidationError{
					field:  "LoanOffer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanOfferSummaryResponseValidationError{
					field:  "LoanOffer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanOffer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanOfferSummaryResponseValidationError{
				field:  "LoanOffer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanOfferSummaryResponseMultiError(errors)
	}

	return nil
}

// GetLoanOfferSummaryResponseMultiError is an error wrapping multiple
// validation errors returned by GetLoanOfferSummaryResponse.ValidateAll() if
// the designated constraints aren't met.
type GetLoanOfferSummaryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanOfferSummaryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanOfferSummaryResponseMultiError) AllErrors() []error { return m }

// GetLoanOfferSummaryResponseValidationError is the validation error returned
// by GetLoanOfferSummaryResponse.Validate if the designated constraints
// aren't met.
type GetLoanOfferSummaryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanOfferSummaryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanOfferSummaryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanOfferSummaryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanOfferSummaryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanOfferSummaryResponseValidationError) ErrorName() string {
	return "GetLoanOfferSummaryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanOfferSummaryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanOfferSummaryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanOfferSummaryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanOfferSummaryResponseValidationError{}

// Validate checks the field values on GetLoanRequestSummaryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanRequestSummaryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanRequestSummaryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanRequestSummaryRequestMultiError, or nil if none found.
func (m *GetLoanRequestSummaryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanRequestSummaryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Vendor

	if len(errors) > 0 {
		return GetLoanRequestSummaryRequestMultiError(errors)
	}

	return nil
}

// GetLoanRequestSummaryRequestMultiError is an error wrapping multiple
// validation errors returned by GetLoanRequestSummaryRequest.ValidateAll() if
// the designated constraints aren't met.
type GetLoanRequestSummaryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanRequestSummaryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanRequestSummaryRequestMultiError) AllErrors() []error { return m }

// GetLoanRequestSummaryRequestValidationError is the validation error returned
// by GetLoanRequestSummaryRequest.Validate if the designated constraints
// aren't met.
type GetLoanRequestSummaryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanRequestSummaryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanRequestSummaryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanRequestSummaryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanRequestSummaryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanRequestSummaryRequestValidationError) ErrorName() string {
	return "GetLoanRequestSummaryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanRequestSummaryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanRequestSummaryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanRequestSummaryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanRequestSummaryRequestValidationError{}

// Validate checks the field values on GetLoanRequestSummaryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanRequestSummaryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanRequestSummaryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLoanRequestSummaryResponseMultiError, or nil if none found.
func (m *GetLoanRequestSummaryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanRequestSummaryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanRequestSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanRequestSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanRequestSummaryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLoanRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanRequestSummaryResponseValidationError{
						field:  fmt.Sprintf("LoanRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanRequestSummaryResponseValidationError{
						field:  fmt.Sprintf("LoanRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanRequestSummaryResponseValidationError{
					field:  fmt.Sprintf("LoanRequests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetLoanRequestSummaryResponseMultiError(errors)
	}

	return nil
}

// GetLoanRequestSummaryResponseMultiError is an error wrapping multiple
// validation errors returned by GetLoanRequestSummaryResponse.ValidateAll()
// if the designated constraints aren't met.
type GetLoanRequestSummaryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanRequestSummaryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanRequestSummaryResponseMultiError) AllErrors() []error { return m }

// GetLoanRequestSummaryResponseValidationError is the validation error
// returned by GetLoanRequestSummaryResponse.Validate if the designated
// constraints aren't met.
type GetLoanRequestSummaryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanRequestSummaryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanRequestSummaryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanRequestSummaryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanRequestSummaryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanRequestSummaryResponseValidationError) ErrorName() string {
	return "GetLoanRequestSummaryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanRequestSummaryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanRequestSummaryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanRequestSummaryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanRequestSummaryResponseValidationError{}

// Validate checks the field values on GetLoanAccountSummaryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanAccountSummaryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanAccountSummaryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanAccountSummaryRequestMultiError, or nil if none found.
func (m *GetLoanAccountSummaryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanAccountSummaryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Vendor

	if len(errors) > 0 {
		return GetLoanAccountSummaryRequestMultiError(errors)
	}

	return nil
}

// GetLoanAccountSummaryRequestMultiError is an error wrapping multiple
// validation errors returned by GetLoanAccountSummaryRequest.ValidateAll() if
// the designated constraints aren't met.
type GetLoanAccountSummaryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanAccountSummaryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanAccountSummaryRequestMultiError) AllErrors() []error { return m }

// GetLoanAccountSummaryRequestValidationError is the validation error returned
// by GetLoanAccountSummaryRequest.Validate if the designated constraints
// aren't met.
type GetLoanAccountSummaryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanAccountSummaryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanAccountSummaryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanAccountSummaryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanAccountSummaryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanAccountSummaryRequestValidationError) ErrorName() string {
	return "GetLoanAccountSummaryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanAccountSummaryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanAccountSummaryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanAccountSummaryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanAccountSummaryRequestValidationError{}

// Validate checks the field values on GetLoanAccountSummaryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanAccountSummaryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanAccountSummaryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLoanAccountSummaryResponseMultiError, or nil if none found.
func (m *GetLoanAccountSummaryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanAccountSummaryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanAccountSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanAccountSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanAccountSummaryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLoanAccounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanAccountSummaryResponseValidationError{
						field:  fmt.Sprintf("LoanAccounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanAccountSummaryResponseValidationError{
						field:  fmt.Sprintf("LoanAccounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanAccountSummaryResponseValidationError{
					field:  fmt.Sprintf("LoanAccounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetLoanAccountSummaryResponseMultiError(errors)
	}

	return nil
}

// GetLoanAccountSummaryResponseMultiError is an error wrapping multiple
// validation errors returned by GetLoanAccountSummaryResponse.ValidateAll()
// if the designated constraints aren't met.
type GetLoanAccountSummaryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanAccountSummaryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanAccountSummaryResponseMultiError) AllErrors() []error { return m }

// GetLoanAccountSummaryResponseValidationError is the validation error
// returned by GetLoanAccountSummaryResponse.Validate if the designated
// constraints aren't met.
type GetLoanAccountSummaryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanAccountSummaryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanAccountSummaryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanAccountSummaryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanAccountSummaryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanAccountSummaryResponseValidationError) ErrorName() string {
	return "GetLoanAccountSummaryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanAccountSummaryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanAccountSummaryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanAccountSummaryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanAccountSummaryResponseValidationError{}

// Validate checks the field values on GetLoanUserDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanUserDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanUserDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanUserDetailsRequestMultiError, or nil if none found.
func (m *GetLoanUserDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanUserDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetLoanHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanUserDetailsRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanUserDetailsRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanUserDetailsRequestValidationError{
				field:  "LoanHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanUserDetailsRequestMultiError(errors)
	}

	return nil
}

// GetLoanUserDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetLoanUserDetailsRequest.ValidateAll() if the
// designated constraints aren't met.
type GetLoanUserDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanUserDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanUserDetailsRequestMultiError) AllErrors() []error { return m }

// GetLoanUserDetailsRequestValidationError is the validation error returned by
// GetLoanUserDetailsRequest.Validate if the designated constraints aren't met.
type GetLoanUserDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanUserDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanUserDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanUserDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanUserDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanUserDetailsRequestValidationError) ErrorName() string {
	return "GetLoanUserDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanUserDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanUserDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanUserDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanUserDetailsRequestValidationError{}

// Validate checks the field values on GetLoanUserDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanUserDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanUserDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanUserDetailsResponseMultiError, or nil if none found.
func (m *GetLoanUserDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanUserDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanUserDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentMonthUserLoanEligibilityDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponseValidationError{
					field:  "CurrentMonthUserLoanEligibilityDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponseValidationError{
					field:  "CurrentMonthUserLoanEligibilityDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentMonthUserLoanEligibilityDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanUserDetailsResponseValidationError{
				field:  "CurrentMonthUserLoanEligibilityDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentUserLoanApplicationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponseValidationError{
					field:  "CurrentUserLoanApplicationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponseValidationError{
					field:  "CurrentUserLoanApplicationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentUserLoanApplicationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanUserDetailsResponseValidationError{
				field:  "CurrentUserLoanApplicationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLoanOfferDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanUserDetailsResponseValidationError{
						field:  fmt.Sprintf("LoanOfferDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanUserDetailsResponseValidationError{
						field:  fmt.Sprintf("LoanOfferDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanUserDetailsResponseValidationError{
					field:  fmt.Sprintf("LoanOfferDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetEligibilityLoanRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanUserDetailsResponseValidationError{
						field:  fmt.Sprintf("EligibilityLoanRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanUserDetailsResponseValidationError{
						field:  fmt.Sprintf("EligibilityLoanRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanUserDetailsResponseValidationError{
					field:  fmt.Sprintf("EligibilityLoanRequests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetApplicationLoanRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanUserDetailsResponseValidationError{
						field:  fmt.Sprintf("ApplicationLoanRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanUserDetailsResponseValidationError{
						field:  fmt.Sprintf("ApplicationLoanRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanUserDetailsResponseValidationError{
					field:  fmt.Sprintf("ApplicationLoanRequests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetApplicants() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanUserDetailsResponseValidationError{
						field:  fmt.Sprintf("Applicants[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanUserDetailsResponseValidationError{
						field:  fmt.Sprintf("Applicants[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanUserDetailsResponseValidationError{
					field:  fmt.Sprintf("Applicants[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetLoanUserDetailsResponseMultiError(errors)
	}

	return nil
}

// GetLoanUserDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by GetLoanUserDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetLoanUserDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanUserDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanUserDetailsResponseMultiError) AllErrors() []error { return m }

// GetLoanUserDetailsResponseValidationError is the validation error returned
// by GetLoanUserDetailsResponse.Validate if the designated constraints aren't met.
type GetLoanUserDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanUserDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanUserDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanUserDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanUserDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanUserDetailsResponseValidationError) ErrorName() string {
	return "GetLoanUserDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanUserDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanUserDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanUserDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanUserDetailsResponseValidationError{}

// Validate checks the field values on GetLoanDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDetailsRequestMultiError, or nil if none found.
func (m *GetLoanDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetLoanHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsRequestValidationError{
				field:  "LoanHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDetailsRequestMultiError(errors)
	}

	return nil
}

// GetLoanDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetLoanDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLoanDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDetailsRequestMultiError) AllErrors() []error { return m }

// GetLoanDetailsRequestValidationError is the validation error returned by
// GetLoanDetailsRequest.Validate if the designated constraints aren't met.
type GetLoanDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDetailsRequestValidationError) ErrorName() string {
	return "GetLoanDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDetailsRequestValidationError{}

// Validate checks the field values on GetLoanDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDetailsResponseMultiError, or nil if none found.
func (m *GetLoanDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLoanDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanDetailsResponseValidationError{
						field:  fmt.Sprintf("LoanDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanDetailsResponseValidationError{
						field:  fmt.Sprintf("LoanDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanDetailsResponseValidationError{
					field:  fmt.Sprintf("LoanDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetLoanDetailsResponseMultiError(errors)
	}

	return nil
}

// GetLoanDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetLoanDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLoanDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDetailsResponseMultiError) AllErrors() []error { return m }

// GetLoanDetailsResponseValidationError is the validation error returned by
// GetLoanDetailsResponse.Validate if the designated constraints aren't met.
type GetLoanDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDetailsResponseValidationError) ErrorName() string {
	return "GetLoanDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDetailsResponseValidationError{}

// Validate checks the field values on ForeclosureDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForeclosureDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForeclosureDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ForeclosureDetailsMultiError, or nil if none found.
func (m *ForeclosureDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ForeclosureDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTotalOutstandingAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "TotalOutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "TotalOutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalOutstandingAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeclosureDetailsValidationError{
				field:  "TotalOutstandingAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrincipalOutstandingAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "PrincipalOutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "PrincipalOutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalOutstandingAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeclosureDetailsValidationError{
				field:  "PrincipalOutstandingAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestOutstandingAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "InterestOutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "InterestOutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestOutstandingAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeclosureDetailsValidationError{
				field:  "InterestOutstandingAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPenaltyAmt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "PenaltyAmt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "PenaltyAmt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPenaltyAmt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeclosureDetailsValidationError{
				field:  "PenaltyAmt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeesAmt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "FeesAmt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "FeesAmt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeesAmt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeclosureDetailsValidationError{
				field:  "FeesAmt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOtherCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "OtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "OtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtherCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeclosureDetailsValidationError{
				field:  "OtherCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ForeclosureDetailsMultiError(errors)
	}

	return nil
}

// ForeclosureDetailsMultiError is an error wrapping multiple validation errors
// returned by ForeclosureDetails.ValidateAll() if the designated constraints
// aren't met.
type ForeclosureDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForeclosureDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForeclosureDetailsMultiError) AllErrors() []error { return m }

// ForeclosureDetailsValidationError is the validation error returned by
// ForeclosureDetails.Validate if the designated constraints aren't met.
type ForeclosureDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForeclosureDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForeclosureDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForeclosureDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForeclosureDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForeclosureDetailsValidationError) ErrorName() string {
	return "ForeclosureDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ForeclosureDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForeclosureDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForeclosureDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForeclosureDetailsValidationError{}

// Validate checks the field values on GetQueueElementsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetQueueElementsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQueueElementsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetQueueElementsRequestMultiError, or nil if none found.
func (m *GetQueueElementsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQueueElementsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PayloadType

	// no validation rules for Limit

	// no validation rules for PageNum

	if all {
		switch v := interface{}(m.GetFromTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetQueueElementsRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetQueueElementsRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetQueueElementsRequestValidationError{
				field:  "FromTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetQueueElementsRequestValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetQueueElementsRequestValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetQueueElementsRequestValidationError{
				field:  "ToTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetQueueElementsRequestMultiError(errors)
	}

	return nil
}

// GetQueueElementsRequestMultiError is an error wrapping multiple validation
// errors returned by GetQueueElementsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetQueueElementsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQueueElementsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQueueElementsRequestMultiError) AllErrors() []error { return m }

// GetQueueElementsRequestValidationError is the validation error returned by
// GetQueueElementsRequest.Validate if the designated constraints aren't met.
type GetQueueElementsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQueueElementsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQueueElementsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQueueElementsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQueueElementsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQueueElementsRequestValidationError) ErrorName() string {
	return "GetQueueElementsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetQueueElementsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQueueElementsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQueueElementsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQueueElementsRequestValidationError{}

// Validate checks the field values on GetQueueElementsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetQueueElementsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQueueElementsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetQueueElementsResponseMultiError, or nil if none found.
func (m *GetQueueElementsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQueueElementsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetQueueElementsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetQueueElementsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetQueueElementsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetElements() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetQueueElementsResponseValidationError{
						field:  fmt.Sprintf("Elements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetQueueElementsResponseValidationError{
						field:  fmt.Sprintf("Elements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetQueueElementsResponseValidationError{
					field:  fmt.Sprintf("Elements[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetQueueElementsResponseMultiError(errors)
	}

	return nil
}

// GetQueueElementsResponseMultiError is an error wrapping multiple validation
// errors returned by GetQueueElementsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetQueueElementsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQueueElementsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQueueElementsResponseMultiError) AllErrors() []error { return m }

// GetQueueElementsResponseValidationError is the validation error returned by
// GetQueueElementsResponse.Validate if the designated constraints aren't met.
type GetQueueElementsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQueueElementsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQueueElementsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQueueElementsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQueueElementsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQueueElementsResponseValidationError) ErrorName() string {
	return "GetQueueElementsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetQueueElementsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQueueElementsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQueueElementsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQueueElementsResponseValidationError{}

// Validate checks the field values on MarkLoanRequestCancelRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MarkLoanRequestCancelRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MarkLoanRequestCancelRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MarkLoanRequestCancelRequestMultiError, or nil if none found.
func (m *MarkLoanRequestCancelRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MarkLoanRequestCancelRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanRequestId

	if len(errors) > 0 {
		return MarkLoanRequestCancelRequestMultiError(errors)
	}

	return nil
}

// MarkLoanRequestCancelRequestMultiError is an error wrapping multiple
// validation errors returned by MarkLoanRequestCancelRequest.ValidateAll() if
// the designated constraints aren't met.
type MarkLoanRequestCancelRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MarkLoanRequestCancelRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MarkLoanRequestCancelRequestMultiError) AllErrors() []error { return m }

// MarkLoanRequestCancelRequestValidationError is the validation error returned
// by MarkLoanRequestCancelRequest.Validate if the designated constraints
// aren't met.
type MarkLoanRequestCancelRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MarkLoanRequestCancelRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MarkLoanRequestCancelRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MarkLoanRequestCancelRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MarkLoanRequestCancelRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MarkLoanRequestCancelRequestValidationError) ErrorName() string {
	return "MarkLoanRequestCancelRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MarkLoanRequestCancelRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMarkLoanRequestCancelRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MarkLoanRequestCancelRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MarkLoanRequestCancelRequestValidationError{}

// Validate checks the field values on MarkLoanRequestCancelResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MarkLoanRequestCancelResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MarkLoanRequestCancelResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// MarkLoanRequestCancelResponseMultiError, or nil if none found.
func (m *MarkLoanRequestCancelResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MarkLoanRequestCancelResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarkLoanRequestCancelResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarkLoanRequestCancelResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarkLoanRequestCancelResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MarkLoanRequestCancelResponseMultiError(errors)
	}

	return nil
}

// MarkLoanRequestCancelResponseMultiError is an error wrapping multiple
// validation errors returned by MarkLoanRequestCancelResponse.ValidateAll()
// if the designated constraints aren't met.
type MarkLoanRequestCancelResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MarkLoanRequestCancelResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MarkLoanRequestCancelResponseMultiError) AllErrors() []error { return m }

// MarkLoanRequestCancelResponseValidationError is the validation error
// returned by MarkLoanRequestCancelResponse.Validate if the designated
// constraints aren't met.
type MarkLoanRequestCancelResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MarkLoanRequestCancelResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MarkLoanRequestCancelResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MarkLoanRequestCancelResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MarkLoanRequestCancelResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MarkLoanRequestCancelResponseValidationError) ErrorName() string {
	return "MarkLoanRequestCancelResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MarkLoanRequestCancelResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMarkLoanRequestCancelResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MarkLoanRequestCancelResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MarkLoanRequestCancelResponseValidationError{}

// Validate checks the field values on MandateAccount with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MandateAccount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MandateAccount with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MandateAccountMultiError,
// or nil if none found.
func (m *MandateAccount) ValidateAll() error {
	return m.validate(true)
}

func (m *MandateAccount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MaskedAccNo

	// no validation rules for BankName

	if len(errors) > 0 {
		return MandateAccountMultiError(errors)
	}

	return nil
}

// MandateAccountMultiError is an error wrapping multiple validation errors
// returned by MandateAccount.ValidateAll() if the designated constraints
// aren't met.
type MandateAccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MandateAccountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MandateAccountMultiError) AllErrors() []error { return m }

// MandateAccountValidationError is the validation error returned by
// MandateAccount.Validate if the designated constraints aren't met.
type MandateAccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MandateAccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MandateAccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MandateAccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MandateAccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MandateAccountValidationError) ErrorName() string { return "MandateAccountValidationError" }

// Error satisfies the builtin error interface
func (e MandateAccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMandateAccount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MandateAccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MandateAccountValidationError{}

// Validate checks the field values on GetForeclosureRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetForeclosureRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetForeclosureRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetForeclosureRequestMultiError, or nil if none found.
func (m *GetForeclosureRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetForeclosureRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanAccountNumber

	if all {
		switch v := interface{}(m.GetLoanHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureRequestValidationError{
				field:  "LoanHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetForeclosureRequestMultiError(errors)
	}

	return nil
}

// GetForeclosureRequestMultiError is an error wrapping multiple validation
// errors returned by GetForeclosureRequest.ValidateAll() if the designated
// constraints aren't met.
type GetForeclosureRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetForeclosureRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetForeclosureRequestMultiError) AllErrors() []error { return m }

// GetForeclosureRequestValidationError is the validation error returned by
// GetForeclosureRequest.Validate if the designated constraints aren't met.
type GetForeclosureRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetForeclosureRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetForeclosureRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetForeclosureRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetForeclosureRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetForeclosureRequestValidationError) ErrorName() string {
	return "GetForeclosureRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetForeclosureRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetForeclosureRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetForeclosureRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetForeclosureRequestValidationError{}

// Validate checks the field values on GetForeclosureResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetForeclosureResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetForeclosureResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetForeclosureResponseMultiError, or nil if none found.
func (m *GetForeclosureResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetForeclosureResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalOutstandingAmt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureResponseValidationError{
					field:  "TotalOutstandingAmt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureResponseValidationError{
					field:  "TotalOutstandingAmt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalOutstandingAmt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureResponseValidationError{
				field:  "TotalOutstandingAmt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrincipalOutstandingAmt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureResponseValidationError{
					field:  "PrincipalOutstandingAmt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureResponseValidationError{
					field:  "PrincipalOutstandingAmt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalOutstandingAmt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureResponseValidationError{
				field:  "PrincipalOutstandingAmt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestOutstandingAmt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureResponseValidationError{
					field:  "InterestOutstandingAmt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureResponseValidationError{
					field:  "InterestOutstandingAmt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestOutstandingAmt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureResponseValidationError{
				field:  "InterestOutstandingAmt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPenaltyAmt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureResponseValidationError{
					field:  "PenaltyAmt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureResponseValidationError{
					field:  "PenaltyAmt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPenaltyAmt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureResponseValidationError{
				field:  "PenaltyAmt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeesAmt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureResponseValidationError{
					field:  "FeesAmt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureResponseValidationError{
					field:  "FeesAmt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeesAmt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureResponseValidationError{
				field:  "FeesAmt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOtherCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureResponseValidationError{
					field:  "OtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureResponseValidationError{
					field:  "OtherCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtherCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureResponseValidationError{
				field:  "OtherCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetForeclosureResponseMultiError(errors)
	}

	return nil
}

// GetForeclosureResponseMultiError is an error wrapping multiple validation
// errors returned by GetForeclosureResponse.ValidateAll() if the designated
// constraints aren't met.
type GetForeclosureResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetForeclosureResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetForeclosureResponseMultiError) AllErrors() []error { return m }

// GetForeclosureResponseValidationError is the validation error returned by
// GetForeclosureResponse.Validate if the designated constraints aren't met.
type GetForeclosureResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetForeclosureResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetForeclosureResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetForeclosureResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetForeclosureResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetForeclosureResponseValidationError) ErrorName() string {
	return "GetForeclosureResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetForeclosureResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetForeclosureResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetForeclosureResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetForeclosureResponseValidationError{}

// Validate checks the field values on GetLoanAccountAdditionalDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetLoanAccountAdditionalDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanAccountAdditionalDetailsRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetLoanAccountAdditionalDetailsRequestMultiError, or nil if none found.
func (m *GetLoanAccountAdditionalDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanAccountAdditionalDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLoanHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanAccountAdditionalDetailsRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanAccountAdditionalDetailsRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanAccountAdditionalDetailsRequestValidationError{
				field:  "LoanHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountId

	if len(errors) > 0 {
		return GetLoanAccountAdditionalDetailsRequestMultiError(errors)
	}

	return nil
}

// GetLoanAccountAdditionalDetailsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetLoanAccountAdditionalDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLoanAccountAdditionalDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanAccountAdditionalDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanAccountAdditionalDetailsRequestMultiError) AllErrors() []error { return m }

// GetLoanAccountAdditionalDetailsRequestValidationError is the validation
// error returned by GetLoanAccountAdditionalDetailsRequest.Validate if the
// designated constraints aren't met.
type GetLoanAccountAdditionalDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanAccountAdditionalDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanAccountAdditionalDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanAccountAdditionalDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanAccountAdditionalDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanAccountAdditionalDetailsRequestValidationError) ErrorName() string {
	return "GetLoanAccountAdditionalDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanAccountAdditionalDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanAccountAdditionalDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanAccountAdditionalDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanAccountAdditionalDetailsRequestValidationError{}

// Validate checks the field values on GetLoanAccountAdditionalDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetLoanAccountAdditionalDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanAccountAdditionalDetailsResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetLoanAccountAdditionalDetailsResponseMultiError, or nil if none found.
func (m *GetLoanAccountAdditionalDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanAccountAdditionalDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanAccountAdditionalDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
					field:  "LoanAccount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
					field:  "LoanAccount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanAccountAdditionalDetailsResponseValidationError{
				field:  "LoanAccount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLoanPaymentRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
						field:  fmt.Sprintf("LoanPaymentRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
						field:  fmt.Sprintf("LoanPaymentRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanAccountAdditionalDetailsResponseValidationError{
					field:  fmt.Sprintf("LoanPaymentRequests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetInstallmentPayouts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
						field:  fmt.Sprintf("InstallmentPayouts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
						field:  fmt.Sprintf("InstallmentPayouts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanAccountAdditionalDetailsResponseValidationError{
					field:  fmt.Sprintf("InstallmentPayouts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetLoanAccountClosureRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
						field:  fmt.Sprintf("LoanAccountClosureRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
						field:  fmt.Sprintf("LoanAccountClosureRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanAccountAdditionalDetailsResponseValidationError{
					field:  fmt.Sprintf("LoanAccountClosureRequests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetLoanUpcomingEmis() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
						field:  fmt.Sprintf("LoanUpcomingEmis[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
						field:  fmt.Sprintf("LoanUpcomingEmis[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanAccountAdditionalDetailsResponseValidationError{
					field:  fmt.Sprintf("LoanUpcomingEmis[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBankAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanAccountAdditionalDetailsResponseValidationError{
				field:  "BankAccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Details.(type) {
	case *GetLoanAccountAdditionalDetailsResponse_FiftyfinLamfDetails:
		if v == nil {
			err := GetLoanAccountAdditionalDetailsResponseValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFiftyfinLamfDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
						field:  "FiftyfinLamfDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanAccountAdditionalDetailsResponseValidationError{
						field:  "FiftyfinLamfDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFiftyfinLamfDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanAccountAdditionalDetailsResponseValidationError{
					field:  "FiftyfinLamfDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetLoanAccountAdditionalDetailsResponseMultiError(errors)
	}

	return nil
}

// GetLoanAccountAdditionalDetailsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetLoanAccountAdditionalDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLoanAccountAdditionalDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanAccountAdditionalDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanAccountAdditionalDetailsResponseMultiError) AllErrors() []error { return m }

// GetLoanAccountAdditionalDetailsResponseValidationError is the validation
// error returned by GetLoanAccountAdditionalDetailsResponse.Validate if the
// designated constraints aren't met.
type GetLoanAccountAdditionalDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanAccountAdditionalDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanAccountAdditionalDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanAccountAdditionalDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanAccountAdditionalDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanAccountAdditionalDetailsResponseValidationError) ErrorName() string {
	return "GetLoanAccountAdditionalDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanAccountAdditionalDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanAccountAdditionalDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanAccountAdditionalDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanAccountAdditionalDetailsResponseValidationError{}

// Validate checks the field values on LoanUpcomingEmi with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LoanUpcomingEmi) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanUpcomingEmi with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanUpcomingEmiMultiError, or nil if none found.
func (m *LoanUpcomingEmi) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanUpcomingEmi) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNextEmiDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanUpcomingEmiValidationError{
					field:  "NextEmiDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanUpcomingEmiValidationError{
					field:  "NextEmiDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextEmiDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanUpcomingEmiValidationError{
				field:  "NextEmiDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanAccountNumber

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanUpcomingEmiValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanUpcomingEmiValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanUpcomingEmiValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanUpcomingEmiMultiError(errors)
	}

	return nil
}

// LoanUpcomingEmiMultiError is an error wrapping multiple validation errors
// returned by LoanUpcomingEmi.ValidateAll() if the designated constraints
// aren't met.
type LoanUpcomingEmiMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanUpcomingEmiMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanUpcomingEmiMultiError) AllErrors() []error { return m }

// LoanUpcomingEmiValidationError is the validation error returned by
// LoanUpcomingEmi.Validate if the designated constraints aren't met.
type LoanUpcomingEmiValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanUpcomingEmiValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanUpcomingEmiValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanUpcomingEmiValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanUpcomingEmiValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanUpcomingEmiValidationError) ErrorName() string { return "LoanUpcomingEmiValidationError" }

// Error satisfies the builtin error interface
func (e LoanUpcomingEmiValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanUpcomingEmi.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanUpcomingEmiValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanUpcomingEmiValidationError{}

// Validate checks the field values on FiftyfinLamfLoanAccountDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FiftyfinLamfLoanAccountDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiftyfinLamfLoanAccountDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FiftyfinLamfLoanAccountDetailsMultiError, or nil if none found.
func (m *FiftyfinLamfLoanAccountDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FiftyfinLamfLoanAccountDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSoa()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinLamfLoanAccountDetailsValidationError{
					field:  "Soa",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinLamfLoanAccountDetailsValidationError{
					field:  "Soa",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSoa()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinLamfLoanAccountDetailsValidationError{
				field:  "Soa",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsLoanAccountClosedOnVendor

	if len(errors) > 0 {
		return FiftyfinLamfLoanAccountDetailsMultiError(errors)
	}

	return nil
}

// FiftyfinLamfLoanAccountDetailsMultiError is an error wrapping multiple
// validation errors returned by FiftyfinLamfLoanAccountDetails.ValidateAll()
// if the designated constraints aren't met.
type FiftyfinLamfLoanAccountDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiftyfinLamfLoanAccountDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiftyfinLamfLoanAccountDetailsMultiError) AllErrors() []error { return m }

// FiftyfinLamfLoanAccountDetailsValidationError is the validation error
// returned by FiftyfinLamfLoanAccountDetails.Validate if the designated
// constraints aren't met.
type FiftyfinLamfLoanAccountDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiftyfinLamfLoanAccountDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiftyfinLamfLoanAccountDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiftyfinLamfLoanAccountDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiftyfinLamfLoanAccountDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiftyfinLamfLoanAccountDetailsValidationError) ErrorName() string {
	return "FiftyfinLamfLoanAccountDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FiftyfinLamfLoanAccountDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiftyfinLamfLoanAccountDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiftyfinLamfLoanAccountDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiftyfinLamfLoanAccountDetailsValidationError{}

// Validate checks the field values on GetLoanRequestAdditionalDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetLoanRequestAdditionalDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanRequestAdditionalDetailsRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetLoanRequestAdditionalDetailsRequestMultiError, or nil if none found.
func (m *GetLoanRequestAdditionalDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanRequestAdditionalDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLoanHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanRequestAdditionalDetailsRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanRequestAdditionalDetailsRequestValidationError{
					field:  "LoanHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanRequestAdditionalDetailsRequestValidationError{
				field:  "LoanHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanRequestId

	if len(errors) > 0 {
		return GetLoanRequestAdditionalDetailsRequestMultiError(errors)
	}

	return nil
}

// GetLoanRequestAdditionalDetailsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetLoanRequestAdditionalDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLoanRequestAdditionalDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanRequestAdditionalDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanRequestAdditionalDetailsRequestMultiError) AllErrors() []error { return m }

// GetLoanRequestAdditionalDetailsRequestValidationError is the validation
// error returned by GetLoanRequestAdditionalDetailsRequest.Validate if the
// designated constraints aren't met.
type GetLoanRequestAdditionalDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanRequestAdditionalDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanRequestAdditionalDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanRequestAdditionalDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanRequestAdditionalDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanRequestAdditionalDetailsRequestValidationError) ErrorName() string {
	return "GetLoanRequestAdditionalDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanRequestAdditionalDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanRequestAdditionalDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanRequestAdditionalDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanRequestAdditionalDetailsRequestValidationError{}

// Validate checks the field values on GetLoanRequestAdditionalDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetLoanRequestAdditionalDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanRequestAdditionalDetailsResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetLoanRequestAdditionalDetailsResponseMultiError, or nil if none found.
func (m *GetLoanRequestAdditionalDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanRequestAdditionalDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanRequestAdditionalDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanRequestAdditionalDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanRequestAdditionalDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanRequestAdditionalDetailsResponseValidationError{
					field:  "LoanRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanRequestAdditionalDetailsResponseValidationError{
					field:  "LoanRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanRequestAdditionalDetailsResponseValidationError{
				field:  "LoanRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLoanSteps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanRequestAdditionalDetailsResponseValidationError{
						field:  fmt.Sprintf("LoanSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanRequestAdditionalDetailsResponseValidationError{
						field:  fmt.Sprintf("LoanSteps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanRequestAdditionalDetailsResponseValidationError{
					field:  fmt.Sprintf("LoanSteps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetLoanRequestAdditionalDetailsResponseMultiError(errors)
	}

	return nil
}

// GetLoanRequestAdditionalDetailsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetLoanRequestAdditionalDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLoanRequestAdditionalDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanRequestAdditionalDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanRequestAdditionalDetailsResponseMultiError) AllErrors() []error { return m }

// GetLoanRequestAdditionalDetailsResponseValidationError is the validation
// error returned by GetLoanRequestAdditionalDetailsResponse.Validate if the
// designated constraints aren't met.
type GetLoanRequestAdditionalDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanRequestAdditionalDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanRequestAdditionalDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanRequestAdditionalDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanRequestAdditionalDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanRequestAdditionalDetailsResponseValidationError) ErrorName() string {
	return "GetLoanRequestAdditionalDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanRequestAdditionalDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanRequestAdditionalDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanRequestAdditionalDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanRequestAdditionalDetailsResponseValidationError{}

// Validate checks the field values on
// SubmitManualReviewRequest_ReviewerDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SubmitManualReviewRequest_ReviewerDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SubmitManualReviewRequest_ReviewerDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// SubmitManualReviewRequest_ReviewerDetailsMultiError, or nil if none found.
func (m *SubmitManualReviewRequest_ReviewerDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitManualReviewRequest_ReviewerDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	if all {
		switch v := interface{}(m.GetReviewedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitManualReviewRequest_ReviewerDetailsValidationError{
					field:  "ReviewedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitManualReviewRequest_ReviewerDetailsValidationError{
					field:  "ReviewedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReviewedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitManualReviewRequest_ReviewerDetailsValidationError{
				field:  "ReviewedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Reason

	if len(errors) > 0 {
		return SubmitManualReviewRequest_ReviewerDetailsMultiError(errors)
	}

	return nil
}

// SubmitManualReviewRequest_ReviewerDetailsMultiError is an error wrapping
// multiple validation errors returned by
// SubmitManualReviewRequest_ReviewerDetails.ValidateAll() if the designated
// constraints aren't met.
type SubmitManualReviewRequest_ReviewerDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitManualReviewRequest_ReviewerDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitManualReviewRequest_ReviewerDetailsMultiError) AllErrors() []error { return m }

// SubmitManualReviewRequest_ReviewerDetailsValidationError is the validation
// error returned by SubmitManualReviewRequest_ReviewerDetails.Validate if the
// designated constraints aren't met.
type SubmitManualReviewRequest_ReviewerDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitManualReviewRequest_ReviewerDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitManualReviewRequest_ReviewerDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitManualReviewRequest_ReviewerDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitManualReviewRequest_ReviewerDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitManualReviewRequest_ReviewerDetailsValidationError) ErrorName() string {
	return "SubmitManualReviewRequest_ReviewerDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitManualReviewRequest_ReviewerDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitManualReviewRequest_ReviewerDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitManualReviewRequest_ReviewerDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitManualReviewRequest_ReviewerDetailsValidationError{}

// Validate checks the field values on
// GetLoanOfferSummaryResponse_LoanOfferForCX with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetLoanOfferSummaryResponse_LoanOfferForCX) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanOfferSummaryResponse_LoanOfferForCX with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetLoanOfferSummaryResponse_LoanOfferForCXMultiError, or nil if none found.
func (m *GetLoanOfferSummaryResponse_LoanOfferForCX) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanOfferSummaryResponse_LoanOfferForCX) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VendorOfferId

	// no validation rules for Vendor

	if all {
		switch v := interface{}(m.GetOfferConstraints()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanOfferSummaryResponse_LoanOfferForCXValidationError{
					field:  "OfferConstraints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanOfferSummaryResponse_LoanOfferForCXValidationError{
					field:  "OfferConstraints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferConstraints()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanOfferSummaryResponse_LoanOfferForCXValidationError{
				field:  "OfferConstraints",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcessingInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanOfferSummaryResponse_LoanOfferForCXValidationError{
					field:  "ProcessingInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanOfferSummaryResponse_LoanOfferForCXValidationError{
					field:  "ProcessingInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessingInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanOfferSummaryResponse_LoanOfferForCXValidationError{
				field:  "ProcessingInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanOfferSummaryResponse_LoanOfferForCXMultiError(errors)
	}

	return nil
}

// GetLoanOfferSummaryResponse_LoanOfferForCXMultiError is an error wrapping
// multiple validation errors returned by
// GetLoanOfferSummaryResponse_LoanOfferForCX.ValidateAll() if the designated
// constraints aren't met.
type GetLoanOfferSummaryResponse_LoanOfferForCXMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanOfferSummaryResponse_LoanOfferForCXMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanOfferSummaryResponse_LoanOfferForCXMultiError) AllErrors() []error { return m }

// GetLoanOfferSummaryResponse_LoanOfferForCXValidationError is the validation
// error returned by GetLoanOfferSummaryResponse_LoanOfferForCX.Validate if
// the designated constraints aren't met.
type GetLoanOfferSummaryResponse_LoanOfferForCXValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanOfferSummaryResponse_LoanOfferForCXValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanOfferSummaryResponse_LoanOfferForCXValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanOfferSummaryResponse_LoanOfferForCXValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanOfferSummaryResponse_LoanOfferForCXValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanOfferSummaryResponse_LoanOfferForCXValidationError) ErrorName() string {
	return "GetLoanOfferSummaryResponse_LoanOfferForCXValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanOfferSummaryResponse_LoanOfferForCXValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanOfferSummaryResponse_LoanOfferForCX.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanOfferSummaryResponse_LoanOfferForCXValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanOfferSummaryResponse_LoanOfferForCXValidationError{}

// Validate checks the field values on
// GetLoanRequestSummaryResponse_LoanRequestForCX with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetLoanRequestSummaryResponse_LoanRequestForCX) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanRequestSummaryResponse_LoanRequestForCX with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetLoanRequestSummaryResponse_LoanRequestForCXMultiError, or nil if none found.
func (m *GetLoanRequestSummaryResponse_LoanRequestForCX) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanRequestSummaryResponse_LoanRequestForCX) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferId

	// no validation rules for LoanAccountId

	// no validation rules for VendorRequestId

	// no validation rules for Vendor

	if all {
		switch v := interface{}(m.GetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanRequestSummaryResponse_LoanRequestForCXValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanRequestSummaryResponse_LoanRequestForCXValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanRequestSummaryResponse_LoanRequestForCXValidationError{
				field:  "Details",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	// no validation rules for Status

	// no validation rules for SubStatus

	if len(errors) > 0 {
		return GetLoanRequestSummaryResponse_LoanRequestForCXMultiError(errors)
	}

	return nil
}

// GetLoanRequestSummaryResponse_LoanRequestForCXMultiError is an error
// wrapping multiple validation errors returned by
// GetLoanRequestSummaryResponse_LoanRequestForCX.ValidateAll() if the
// designated constraints aren't met.
type GetLoanRequestSummaryResponse_LoanRequestForCXMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanRequestSummaryResponse_LoanRequestForCXMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanRequestSummaryResponse_LoanRequestForCXMultiError) AllErrors() []error { return m }

// GetLoanRequestSummaryResponse_LoanRequestForCXValidationError is the
// validation error returned by
// GetLoanRequestSummaryResponse_LoanRequestForCX.Validate if the designated
// constraints aren't met.
type GetLoanRequestSummaryResponse_LoanRequestForCXValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanRequestSummaryResponse_LoanRequestForCXValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanRequestSummaryResponse_LoanRequestForCXValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetLoanRequestSummaryResponse_LoanRequestForCXValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanRequestSummaryResponse_LoanRequestForCXValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanRequestSummaryResponse_LoanRequestForCXValidationError) ErrorName() string {
	return "GetLoanRequestSummaryResponse_LoanRequestForCXValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanRequestSummaryResponse_LoanRequestForCXValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanRequestSummaryResponse_LoanRequestForCX.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanRequestSummaryResponse_LoanRequestForCXValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanRequestSummaryResponse_LoanRequestForCXValidationError{}

// Validate checks the field values on
// GetLoanAccountSummaryResponse_LoanAccountForCX with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetLoanAccountSummaryResponse_LoanAccountForCX) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanAccountSummaryResponse_LoanAccountForCX with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetLoanAccountSummaryResponse_LoanAccountForCXMultiError, or nil if none found.
func (m *GetLoanAccountSummaryResponse_LoanAccountForCX) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanAccountSummaryResponse_LoanAccountForCX) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountNumber

	// no validation rules for Vendor

	// no validation rules for IfscCode

	// no validation rules for LoanType

	if all {
		switch v := interface{}(m.GetLoanAmountInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanAccountSummaryResponse_LoanAccountForCXValidationError{
					field:  "LoanAmountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanAccountSummaryResponse_LoanAccountForCXValidationError{
					field:  "LoanAmountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanAmountInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanAccountSummaryResponse_LoanAccountForCXValidationError{
				field:  "LoanAmountInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanAccountSummaryResponse_LoanAccountForCXValidationError{
					field:  "LoanEndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanAccountSummaryResponse_LoanAccountForCXValidationError{
					field:  "LoanEndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanAccountSummaryResponse_LoanAccountForCXValidationError{
				field:  "LoanEndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaturityDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanAccountSummaryResponse_LoanAccountForCXValidationError{
					field:  "MaturityDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanAccountSummaryResponse_LoanAccountForCXValidationError{
					field:  "MaturityDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaturityDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanAccountSummaryResponse_LoanAccountForCXValidationError{
				field:  "MaturityDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanAccountSummaryResponse_LoanAccountForCXValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanAccountSummaryResponse_LoanAccountForCXValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanAccountSummaryResponse_LoanAccountForCXValidationError{
				field:  "Details",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	// no validation rules for LoanProgram

	if len(errors) > 0 {
		return GetLoanAccountSummaryResponse_LoanAccountForCXMultiError(errors)
	}

	return nil
}

// GetLoanAccountSummaryResponse_LoanAccountForCXMultiError is an error
// wrapping multiple validation errors returned by
// GetLoanAccountSummaryResponse_LoanAccountForCX.ValidateAll() if the
// designated constraints aren't met.
type GetLoanAccountSummaryResponse_LoanAccountForCXMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanAccountSummaryResponse_LoanAccountForCXMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanAccountSummaryResponse_LoanAccountForCXMultiError) AllErrors() []error { return m }

// GetLoanAccountSummaryResponse_LoanAccountForCXValidationError is the
// validation error returned by
// GetLoanAccountSummaryResponse_LoanAccountForCX.Validate if the designated
// constraints aren't met.
type GetLoanAccountSummaryResponse_LoanAccountForCXValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanAccountSummaryResponse_LoanAccountForCXValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanAccountSummaryResponse_LoanAccountForCXValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetLoanAccountSummaryResponse_LoanAccountForCXValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanAccountSummaryResponse_LoanAccountForCXValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanAccountSummaryResponse_LoanAccountForCXValidationError) ErrorName() string {
	return "GetLoanAccountSummaryResponse_LoanAccountForCXValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanAccountSummaryResponse_LoanAccountForCXValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanAccountSummaryResponse_LoanAccountForCX.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanAccountSummaryResponse_LoanAccountForCXValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanAccountSummaryResponse_LoanAccountForCXValidationError{}

// Validate checks the field values on
// GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsMultiError,
// or nil if none found.
func (m *GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsUserSuggestedForLoan

	// no validation rules for ReasonForIneligibility

	if len(errors) > 0 {
		return GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsMultiError(errors)
	}

	return nil
}

// GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsMultiError
// is an error wrapping multiple validation errors returned by
// GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails.ValidateAll()
// if the designated constraints aren't met.
type GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsMultiError) AllErrors() []error {
	return m
}

// GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsValidationError
// is the validation error returned by
// GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails.Validate
// if the designated constraints aren't met.
type GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsValidationError) ErrorName() string {
	return "GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanUserDetailsResponse_CurrentMonthUserLoanEligibilityDetailsValidationError{}

// Validate checks the field values on
// GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsMultiError, or
// nil if none found.
func (m *GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanApplicationStatus

	// no validation rules for LoanApplicationSubStatus

	if all {
		switch v := interface{}(m.GetLoanAmountApplied()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsValidationError{
					field:  "LoanAmountApplied",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsValidationError{
					field:  "LoanAmountApplied",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanAmountApplied()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsValidationError{
				field:  "LoanAmountApplied",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TenureAppliedMonths

	// no validation rules for Vendor

	// no validation rules for LoanProgram

	if len(errors) > 0 {
		return GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsMultiError(errors)
	}

	return nil
}

// GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsMultiError is an
// error wrapping multiple validation errors returned by
// GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails.ValidateAll()
// if the designated constraints aren't met.
type GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsMultiError) AllErrors() []error {
	return m
}

// GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsValidationError
// is the validation error returned by
// GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails.Validate if
// the designated constraints aren't met.
type GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsValidationError) ErrorName() string {
	return "GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanUserDetailsResponse_CurrentUserLoanApplicationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanUserDetailsResponse_CurrentUserLoanApplicationDetailsValidationError{}

// Validate checks the field values on
// GetLoanUserDetailsResponse_LoanOfferDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetLoanUserDetailsResponse_LoanOfferDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanUserDetailsResponse_LoanOfferDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetLoanUserDetailsResponse_LoanOfferDetailsMultiError, or nil if none found.
func (m *GetLoanUserDetailsResponse_LoanOfferDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanUserDetailsResponse_LoanOfferDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanOfferId

	if all {
		switch v := interface{}(m.GetMinLoanAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
					field:  "MinLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
					field:  "MinLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinLoanAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
				field:  "MinLoanAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxLoanAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
					field:  "MaxLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
					field:  "MaxLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxLoanAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
				field:  "MaxLoanAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxEmiAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
					field:  "MaxEmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
					field:  "MaxEmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxEmiAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
				field:  "MaxEmiAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Interest

	// no validation rules for MinTenure

	// no validation rules for MaxTenure

	if all {
		switch v := interface{}(m.GetOfferStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
					field:  "OfferStartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
					field:  "OfferStartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
				field:  "OfferStartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOfferEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
					field:  "OfferEndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
					field:  "OfferEndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
				field:  "OfferEndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AgentInputUserFeedback

	// no validation rules for Vendor

	// no validation rules for LoanProgram

	// no validation rules for LoanOfferType

	switch v := m.AdditionalConstraints.(type) {
	case *GetLoanUserDetailsResponse_LoanOfferDetails_FiftyfinLamfConstraintInfo:
		if v == nil {
			err := GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
				field:  "AdditionalConstraints",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFiftyfinLamfConstraintInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
						field:  "FiftyfinLamfConstraintInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
						field:  "FiftyfinLamfConstraintInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFiftyfinLamfConstraintInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{
					field:  "FiftyfinLamfConstraintInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetLoanUserDetailsResponse_LoanOfferDetailsMultiError(errors)
	}

	return nil
}

// GetLoanUserDetailsResponse_LoanOfferDetailsMultiError is an error wrapping
// multiple validation errors returned by
// GetLoanUserDetailsResponse_LoanOfferDetails.ValidateAll() if the designated
// constraints aren't met.
type GetLoanUserDetailsResponse_LoanOfferDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanUserDetailsResponse_LoanOfferDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanUserDetailsResponse_LoanOfferDetailsMultiError) AllErrors() []error { return m }

// GetLoanUserDetailsResponse_LoanOfferDetailsValidationError is the validation
// error returned by GetLoanUserDetailsResponse_LoanOfferDetails.Validate if
// the designated constraints aren't met.
type GetLoanUserDetailsResponse_LoanOfferDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanUserDetailsResponse_LoanOfferDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanUserDetailsResponse_LoanOfferDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanUserDetailsResponse_LoanOfferDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanUserDetailsResponse_LoanOfferDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanUserDetailsResponse_LoanOfferDetailsValidationError) ErrorName() string {
	return "GetLoanUserDetailsResponse_LoanOfferDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanUserDetailsResponse_LoanOfferDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanUserDetailsResponse_LoanOfferDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanUserDetailsResponse_LoanOfferDetailsValidationError{}

// Validate checks the field values on GetLoanDetailsResponse_LoanDetailsForCx
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetLoanDetailsResponse_LoanDetailsForCx) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanDetailsResponse_LoanDetailsForCx with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetLoanDetailsResponse_LoanDetailsForCxMultiError, or nil if none found.
func (m *GetLoanDetailsResponse_LoanDetailsForCx) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDetailsResponse_LoanDetailsForCx) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanAccountNumber

	if all {
		switch v := interface{}(m.GetLoanOpenDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "LoanOpenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "LoanOpenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanOpenDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCxValidationError{
				field:  "LoanOpenDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "LoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "LoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCxValidationError{
				field:  "LoanAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InterestRate

	// no validation rules for TenureInMonths

	if all {
		switch v := interface{}(m.GetProcessingFee()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "ProcessingFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "ProcessingFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessingFee()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCxValidationError{
				field:  "ProcessingFee",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOutstandingAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "OutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "OutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOutstandingAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCxValidationError{
				field:  "OutstandingAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGst()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "Gst",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "Gst",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGst()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCxValidationError{
				field:  "Gst",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrokenPeriodInterest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "BrokenPeriodInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "BrokenPeriodInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrokenPeriodInterest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCxValidationError{
				field:  "BrokenPeriodInterest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPreClosureFee()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "PreClosureFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "PreClosureFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreClosureFee()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCxValidationError{
				field:  "PreClosureFee",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLoanPastTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
						field:  fmt.Sprintf("LoanPastTransactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
						field:  fmt.Sprintf("LoanPastTransactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  fmt.Sprintf("LoanPastTransactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetLoanUpcomingEmis() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
						field:  fmt.Sprintf("LoanUpcomingEmis[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
						field:  fmt.Sprintf("LoanUpcomingEmis[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  fmt.Sprintf("LoanUpcomingEmis[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Vendor

	// no validation rules for LoanProgram

	for idx, item := range m.GetMfPledgeDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
						field:  fmt.Sprintf("MfPledgeDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
						field:  fmt.Sprintf("MfPledgeDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  fmt.Sprintf("MfPledgeDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetMandateAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "MandateAccount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "MandateAccount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMandateAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCxValidationError{
				field:  "MandateAccount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBankAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCxValidationError{
				field:  "BankAccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoanAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "LoanAccount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "LoanAccount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCxValidationError{
				field:  "LoanAccount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetForeClosureDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "ForeClosureDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCxValidationError{
					field:  "ForeClosureDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForeClosureDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCxValidationError{
				field:  "ForeClosureDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDetailsResponse_LoanDetailsForCxMultiError(errors)
	}

	return nil
}

// GetLoanDetailsResponse_LoanDetailsForCxMultiError is an error wrapping
// multiple validation errors returned by
// GetLoanDetailsResponse_LoanDetailsForCx.ValidateAll() if the designated
// constraints aren't met.
type GetLoanDetailsResponse_LoanDetailsForCxMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDetailsResponse_LoanDetailsForCxMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDetailsResponse_LoanDetailsForCxMultiError) AllErrors() []error { return m }

// GetLoanDetailsResponse_LoanDetailsForCxValidationError is the validation
// error returned by GetLoanDetailsResponse_LoanDetailsForCx.Validate if the
// designated constraints aren't met.
type GetLoanDetailsResponse_LoanDetailsForCxValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDetailsResponse_LoanDetailsForCxValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDetailsResponse_LoanDetailsForCxValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDetailsResponse_LoanDetailsForCxValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDetailsResponse_LoanDetailsForCxValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDetailsResponse_LoanDetailsForCxValidationError) ErrorName() string {
	return "GetLoanDetailsResponse_LoanDetailsForCxValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDetailsResponse_LoanDetailsForCxValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDetailsResponse_LoanDetailsForCx.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDetailsResponse_LoanDetailsForCxValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDetailsResponse_LoanDetailsForCxValidationError{}

// Validate checks the field values on
// GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionMultiError, or
// nil if none found.
func (m *GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError{
					field:  "Timestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError{
					field:  "Timestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError{
				field:  "Timestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransactionType

	// no validation rules for Status

	// no validation rules for OrderId

	// no validation rules for FiUtrNumber

	// no validation rules for LoanAccountNumber

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ModeOfPayment

	if all {
		switch v := interface{}(m.GetCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError{
					field:  "Charges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError{
					field:  "Charges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError{
				field:  "Charges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionMultiError(errors)
	}

	return nil
}

// GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionMultiError is an
// error wrapping multiple validation errors returned by
// GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction.ValidateAll()
// if the designated constraints aren't met.
type GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionMultiError) AllErrors() []error {
	return m
}

// GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError
// is the validation error returned by
// GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction.Validate if the
// designated constraints aren't met.
type GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError) ErrorName() string {
	return "GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransaction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDetailsResponse_LoanDetailsForCx_LoanPastTransactionValidationError{}

// Validate checks the field values on
// GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiMultiError, or nil
// if none found.
func (m *GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNextEmiDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError{
					field:  "NextEmiDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError{
					field:  "NextEmiDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextEmiDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError{
				field:  "NextEmiDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanAccountNumber

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiMultiError(errors)
	}

	return nil
}

// GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiMultiError is an
// error wrapping multiple validation errors returned by
// GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi.ValidateAll() if
// the designated constraints aren't met.
type GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiMultiError) AllErrors() []error {
	return m
}

// GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError is
// the validation error returned by
// GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi.Validate if the
// designated constraints aren't met.
type GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError) ErrorName() string {
	return "GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmi.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDetailsResponse_LoanDetailsForCx_LoanUpcomingEmiValidationError{}

// Validate checks the field values on
// GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsMultiError, or nil
// if none found.
func (m *GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Isin

	// no validation rules for Units

	if len(errors) > 0 {
		return GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsMultiError(errors)
	}

	return nil
}

// GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsMultiError is an
// error wrapping multiple validation errors returned by
// GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails.ValidateAll() if
// the designated constraints aren't met.
type GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsMultiError) AllErrors() []error {
	return m
}

// GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsValidationError is
// the validation error returned by
// GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails.Validate if the
// designated constraints aren't met.
type GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsValidationError) ErrorName() string {
	return "GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDetailsResponse_LoanDetailsForCx_MfPledgeDetailsValidationError{}

// Validate checks the field values on GetQueueElementsResponse_QueueElement
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetQueueElementsResponse_QueueElement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQueueElementsResponse_QueueElement
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetQueueElementsResponse_QueueElementMultiError, or nil if none found.
func (m *GetQueueElementsResponse_QueueElement) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQueueElementsResponse_QueueElement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	switch v := m.Payload.(type) {
	case *GetQueueElementsResponse_QueueElement_LivenessReview:
		if v == nil {
			err := GetQueueElementsResponse_QueueElementValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLivenessReview()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetQueueElementsResponse_QueueElementValidationError{
						field:  "LivenessReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetQueueElementsResponse_QueueElementValidationError{
						field:  "LivenessReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLivenessReview()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetQueueElementsResponse_QueueElementValidationError{
					field:  "LivenessReview",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetQueueElementsResponse_QueueElement_FacematchReview:
		if v == nil {
			err := GetQueueElementsResponse_QueueElementValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFacematchReview()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetQueueElementsResponse_QueueElementValidationError{
						field:  "FacematchReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetQueueElementsResponse_QueueElementValidationError{
						field:  "FacematchReview",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFacematchReview()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetQueueElementsResponse_QueueElementValidationError{
					field:  "FacematchReview",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetQueueElementsResponse_QueueElementMultiError(errors)
	}

	return nil
}

// GetQueueElementsResponse_QueueElementMultiError is an error wrapping
// multiple validation errors returned by
// GetQueueElementsResponse_QueueElement.ValidateAll() if the designated
// constraints aren't met.
type GetQueueElementsResponse_QueueElementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQueueElementsResponse_QueueElementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQueueElementsResponse_QueueElementMultiError) AllErrors() []error { return m }

// GetQueueElementsResponse_QueueElementValidationError is the validation error
// returned by GetQueueElementsResponse_QueueElement.Validate if the
// designated constraints aren't met.
type GetQueueElementsResponse_QueueElementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQueueElementsResponse_QueueElementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQueueElementsResponse_QueueElementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQueueElementsResponse_QueueElementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQueueElementsResponse_QueueElementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQueueElementsResponse_QueueElementValidationError) ErrorName() string {
	return "GetQueueElementsResponse_QueueElementValidationError"
}

// Error satisfies the builtin error interface
func (e GetQueueElementsResponse_QueueElementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQueueElementsResponse_QueueElement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQueueElementsResponse_QueueElementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQueueElementsResponse_QueueElementValidationError{}

// Validate checks the field values on
// FiftyfinLamfLoanAccountDetails_LoanStatement with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FiftyfinLamfLoanAccountDetails_LoanStatement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FiftyfinLamfLoanAccountDetails_LoanStatement with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FiftyfinLamfLoanAccountDetails_LoanStatementMultiError, or nil if none found.
func (m *FiftyfinLamfLoanAccountDetails_LoanStatement) ValidateAll() error {
	return m.validate(true)
}

func (m *FiftyfinLamfLoanAccountDetails_LoanStatement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FiftyfinLamfLoanAccountDetails_LoanStatementValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FiftyfinLamfLoanAccountDetails_LoanStatementValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FiftyfinLamfLoanAccountDetails_LoanStatementValidationError{
					field:  fmt.Sprintf("Transactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FiftyfinLamfLoanAccountDetails_LoanStatementMultiError(errors)
	}

	return nil
}

// FiftyfinLamfLoanAccountDetails_LoanStatementMultiError is an error wrapping
// multiple validation errors returned by
// FiftyfinLamfLoanAccountDetails_LoanStatement.ValidateAll() if the
// designated constraints aren't met.
type FiftyfinLamfLoanAccountDetails_LoanStatementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiftyfinLamfLoanAccountDetails_LoanStatementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiftyfinLamfLoanAccountDetails_LoanStatementMultiError) AllErrors() []error { return m }

// FiftyfinLamfLoanAccountDetails_LoanStatementValidationError is the
// validation error returned by
// FiftyfinLamfLoanAccountDetails_LoanStatement.Validate if the designated
// constraints aren't met.
type FiftyfinLamfLoanAccountDetails_LoanStatementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiftyfinLamfLoanAccountDetails_LoanStatementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiftyfinLamfLoanAccountDetails_LoanStatementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiftyfinLamfLoanAccountDetails_LoanStatementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiftyfinLamfLoanAccountDetails_LoanStatementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiftyfinLamfLoanAccountDetails_LoanStatementValidationError) ErrorName() string {
	return "FiftyfinLamfLoanAccountDetails_LoanStatementValidationError"
}

// Error satisfies the builtin error interface
func (e FiftyfinLamfLoanAccountDetails_LoanStatementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiftyfinLamfLoanAccountDetails_LoanStatement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiftyfinLamfLoanAccountDetails_LoanStatementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiftyfinLamfLoanAccountDetails_LoanStatementValidationError{}
