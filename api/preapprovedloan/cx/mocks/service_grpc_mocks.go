// Code generated by MockGen. DO NOT EDIT.
// Source: api/preapprovedloan/cx/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	cx "github.com/epifi/gamma/api/preapprovedloan/cx"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCxClient is a mock of CxClient interface.
type MockCxClient struct {
	ctrl     *gomock.Controller
	recorder *MockCxClientMockRecorder
}

// MockCxClientMockRecorder is the mock recorder for MockCxClient.
type MockCxClientMockRecorder struct {
	mock *MockCxClient
}

// NewMockCxClient creates a new mock instance.
func NewMockCxClient(ctrl *gomock.Controller) *MockCxClient {
	mock := &MockCxClient{ctrl: ctrl}
	mock.recorder = &MockCxClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCxClient) EXPECT() *MockCxClientMockRecorder {
	return m.recorder
}

// GetForeclosureDetails mocks base method.
func (m *MockCxClient) GetForeclosureDetails(ctx context.Context, in *cx.GetForeclosureRequest, opts ...grpc.CallOption) (*cx.GetForeclosureResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetForeclosureDetails", varargs...)
	ret0, _ := ret[0].(*cx.GetForeclosureResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForeclosureDetails indicates an expected call of GetForeclosureDetails.
func (mr *MockCxClientMockRecorder) GetForeclosureDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForeclosureDetails", reflect.TypeOf((*MockCxClient)(nil).GetForeclosureDetails), varargs...)
}

// GetLoanAccountAdditionalDetails mocks base method.
func (m *MockCxClient) GetLoanAccountAdditionalDetails(ctx context.Context, in *cx.GetLoanAccountAdditionalDetailsRequest, opts ...grpc.CallOption) (*cx.GetLoanAccountAdditionalDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanAccountAdditionalDetails", varargs...)
	ret0, _ := ret[0].(*cx.GetLoanAccountAdditionalDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanAccountAdditionalDetails indicates an expected call of GetLoanAccountAdditionalDetails.
func (mr *MockCxClientMockRecorder) GetLoanAccountAdditionalDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanAccountAdditionalDetails", reflect.TypeOf((*MockCxClient)(nil).GetLoanAccountAdditionalDetails), varargs...)
}

// GetLoanAccountSummary mocks base method.
func (m *MockCxClient) GetLoanAccountSummary(ctx context.Context, in *cx.GetLoanAccountSummaryRequest, opts ...grpc.CallOption) (*cx.GetLoanAccountSummaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanAccountSummary", varargs...)
	ret0, _ := ret[0].(*cx.GetLoanAccountSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanAccountSummary indicates an expected call of GetLoanAccountSummary.
func (mr *MockCxClientMockRecorder) GetLoanAccountSummary(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanAccountSummary", reflect.TypeOf((*MockCxClient)(nil).GetLoanAccountSummary), varargs...)
}

// GetLoanDetails mocks base method.
func (m *MockCxClient) GetLoanDetails(ctx context.Context, in *cx.GetLoanDetailsRequest, opts ...grpc.CallOption) (*cx.GetLoanDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanDetails", varargs...)
	ret0, _ := ret[0].(*cx.GetLoanDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanDetails indicates an expected call of GetLoanDetails.
func (mr *MockCxClientMockRecorder) GetLoanDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanDetails", reflect.TypeOf((*MockCxClient)(nil).GetLoanDetails), varargs...)
}

// GetLoanOfferSummary mocks base method.
func (m *MockCxClient) GetLoanOfferSummary(ctx context.Context, in *cx.GetLoanOfferSummaryRequest, opts ...grpc.CallOption) (*cx.GetLoanOfferSummaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanOfferSummary", varargs...)
	ret0, _ := ret[0].(*cx.GetLoanOfferSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanOfferSummary indicates an expected call of GetLoanOfferSummary.
func (mr *MockCxClientMockRecorder) GetLoanOfferSummary(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanOfferSummary", reflect.TypeOf((*MockCxClient)(nil).GetLoanOfferSummary), varargs...)
}

// GetLoanRequestAdditionalDetails mocks base method.
func (m *MockCxClient) GetLoanRequestAdditionalDetails(ctx context.Context, in *cx.GetLoanRequestAdditionalDetailsRequest, opts ...grpc.CallOption) (*cx.GetLoanRequestAdditionalDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanRequestAdditionalDetails", varargs...)
	ret0, _ := ret[0].(*cx.GetLoanRequestAdditionalDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanRequestAdditionalDetails indicates an expected call of GetLoanRequestAdditionalDetails.
func (mr *MockCxClientMockRecorder) GetLoanRequestAdditionalDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanRequestAdditionalDetails", reflect.TypeOf((*MockCxClient)(nil).GetLoanRequestAdditionalDetails), varargs...)
}

// GetLoanRequestSummary mocks base method.
func (m *MockCxClient) GetLoanRequestSummary(ctx context.Context, in *cx.GetLoanRequestSummaryRequest, opts ...grpc.CallOption) (*cx.GetLoanRequestSummaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanRequestSummary", varargs...)
	ret0, _ := ret[0].(*cx.GetLoanRequestSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanRequestSummary indicates an expected call of GetLoanRequestSummary.
func (mr *MockCxClientMockRecorder) GetLoanRequestSummary(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanRequestSummary", reflect.TypeOf((*MockCxClient)(nil).GetLoanRequestSummary), varargs...)
}

// GetLoanUserDetails mocks base method.
func (m *MockCxClient) GetLoanUserDetails(ctx context.Context, in *cx.GetLoanUserDetailsRequest, opts ...grpc.CallOption) (*cx.GetLoanUserDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanUserDetails", varargs...)
	ret0, _ := ret[0].(*cx.GetLoanUserDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanUserDetails indicates an expected call of GetLoanUserDetails.
func (mr *MockCxClientMockRecorder) GetLoanUserDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanUserDetails", reflect.TypeOf((*MockCxClient)(nil).GetLoanUserDetails), varargs...)
}

// GetQueueElements mocks base method.
func (m *MockCxClient) GetQueueElements(ctx context.Context, in *cx.GetQueueElementsRequest, opts ...grpc.CallOption) (*cx.GetQueueElementsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetQueueElements", varargs...)
	ret0, _ := ret[0].(*cx.GetQueueElementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQueueElements indicates an expected call of GetQueueElements.
func (mr *MockCxClientMockRecorder) GetQueueElements(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQueueElements", reflect.TypeOf((*MockCxClient)(nil).GetQueueElements), varargs...)
}

// MarkLoanRequestCancel mocks base method.
func (m *MockCxClient) MarkLoanRequestCancel(ctx context.Context, in *cx.MarkLoanRequestCancelRequest, opts ...grpc.CallOption) (*cx.MarkLoanRequestCancelResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MarkLoanRequestCancel", varargs...)
	ret0, _ := ret[0].(*cx.MarkLoanRequestCancelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MarkLoanRequestCancel indicates an expected call of MarkLoanRequestCancel.
func (mr *MockCxClientMockRecorder) MarkLoanRequestCancel(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkLoanRequestCancel", reflect.TypeOf((*MockCxClient)(nil).MarkLoanRequestCancel), varargs...)
}

// SubmitManualReview mocks base method.
func (m *MockCxClient) SubmitManualReview(ctx context.Context, in *cx.SubmitManualReviewRequest, opts ...grpc.CallOption) (*cx.SubmitManualReviewResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SubmitManualReview", varargs...)
	ret0, _ := ret[0].(*cx.SubmitManualReviewResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitManualReview indicates an expected call of SubmitManualReview.
func (mr *MockCxClientMockRecorder) SubmitManualReview(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitManualReview", reflect.TypeOf((*MockCxClient)(nil).SubmitManualReview), varargs...)
}

// MockCxServer is a mock of CxServer interface.
type MockCxServer struct {
	ctrl     *gomock.Controller
	recorder *MockCxServerMockRecorder
}

// MockCxServerMockRecorder is the mock recorder for MockCxServer.
type MockCxServerMockRecorder struct {
	mock *MockCxServer
}

// NewMockCxServer creates a new mock instance.
func NewMockCxServer(ctrl *gomock.Controller) *MockCxServer {
	mock := &MockCxServer{ctrl: ctrl}
	mock.recorder = &MockCxServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCxServer) EXPECT() *MockCxServerMockRecorder {
	return m.recorder
}

// GetForeclosureDetails mocks base method.
func (m *MockCxServer) GetForeclosureDetails(arg0 context.Context, arg1 *cx.GetForeclosureRequest) (*cx.GetForeclosureResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetForeclosureDetails", arg0, arg1)
	ret0, _ := ret[0].(*cx.GetForeclosureResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetForeclosureDetails indicates an expected call of GetForeclosureDetails.
func (mr *MockCxServerMockRecorder) GetForeclosureDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetForeclosureDetails", reflect.TypeOf((*MockCxServer)(nil).GetForeclosureDetails), arg0, arg1)
}

// GetLoanAccountAdditionalDetails mocks base method.
func (m *MockCxServer) GetLoanAccountAdditionalDetails(arg0 context.Context, arg1 *cx.GetLoanAccountAdditionalDetailsRequest) (*cx.GetLoanAccountAdditionalDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoanAccountAdditionalDetails", arg0, arg1)
	ret0, _ := ret[0].(*cx.GetLoanAccountAdditionalDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanAccountAdditionalDetails indicates an expected call of GetLoanAccountAdditionalDetails.
func (mr *MockCxServerMockRecorder) GetLoanAccountAdditionalDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanAccountAdditionalDetails", reflect.TypeOf((*MockCxServer)(nil).GetLoanAccountAdditionalDetails), arg0, arg1)
}

// GetLoanAccountSummary mocks base method.
func (m *MockCxServer) GetLoanAccountSummary(arg0 context.Context, arg1 *cx.GetLoanAccountSummaryRequest) (*cx.GetLoanAccountSummaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoanAccountSummary", arg0, arg1)
	ret0, _ := ret[0].(*cx.GetLoanAccountSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanAccountSummary indicates an expected call of GetLoanAccountSummary.
func (mr *MockCxServerMockRecorder) GetLoanAccountSummary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanAccountSummary", reflect.TypeOf((*MockCxServer)(nil).GetLoanAccountSummary), arg0, arg1)
}

// GetLoanDetails mocks base method.
func (m *MockCxServer) GetLoanDetails(arg0 context.Context, arg1 *cx.GetLoanDetailsRequest) (*cx.GetLoanDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoanDetails", arg0, arg1)
	ret0, _ := ret[0].(*cx.GetLoanDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanDetails indicates an expected call of GetLoanDetails.
func (mr *MockCxServerMockRecorder) GetLoanDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanDetails", reflect.TypeOf((*MockCxServer)(nil).GetLoanDetails), arg0, arg1)
}

// GetLoanOfferSummary mocks base method.
func (m *MockCxServer) GetLoanOfferSummary(arg0 context.Context, arg1 *cx.GetLoanOfferSummaryRequest) (*cx.GetLoanOfferSummaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoanOfferSummary", arg0, arg1)
	ret0, _ := ret[0].(*cx.GetLoanOfferSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanOfferSummary indicates an expected call of GetLoanOfferSummary.
func (mr *MockCxServerMockRecorder) GetLoanOfferSummary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanOfferSummary", reflect.TypeOf((*MockCxServer)(nil).GetLoanOfferSummary), arg0, arg1)
}

// GetLoanRequestAdditionalDetails mocks base method.
func (m *MockCxServer) GetLoanRequestAdditionalDetails(arg0 context.Context, arg1 *cx.GetLoanRequestAdditionalDetailsRequest) (*cx.GetLoanRequestAdditionalDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoanRequestAdditionalDetails", arg0, arg1)
	ret0, _ := ret[0].(*cx.GetLoanRequestAdditionalDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanRequestAdditionalDetails indicates an expected call of GetLoanRequestAdditionalDetails.
func (mr *MockCxServerMockRecorder) GetLoanRequestAdditionalDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanRequestAdditionalDetails", reflect.TypeOf((*MockCxServer)(nil).GetLoanRequestAdditionalDetails), arg0, arg1)
}

// GetLoanRequestSummary mocks base method.
func (m *MockCxServer) GetLoanRequestSummary(arg0 context.Context, arg1 *cx.GetLoanRequestSummaryRequest) (*cx.GetLoanRequestSummaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoanRequestSummary", arg0, arg1)
	ret0, _ := ret[0].(*cx.GetLoanRequestSummaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanRequestSummary indicates an expected call of GetLoanRequestSummary.
func (mr *MockCxServerMockRecorder) GetLoanRequestSummary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanRequestSummary", reflect.TypeOf((*MockCxServer)(nil).GetLoanRequestSummary), arg0, arg1)
}

// GetLoanUserDetails mocks base method.
func (m *MockCxServer) GetLoanUserDetails(arg0 context.Context, arg1 *cx.GetLoanUserDetailsRequest) (*cx.GetLoanUserDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoanUserDetails", arg0, arg1)
	ret0, _ := ret[0].(*cx.GetLoanUserDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanUserDetails indicates an expected call of GetLoanUserDetails.
func (mr *MockCxServerMockRecorder) GetLoanUserDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanUserDetails", reflect.TypeOf((*MockCxServer)(nil).GetLoanUserDetails), arg0, arg1)
}

// GetQueueElements mocks base method.
func (m *MockCxServer) GetQueueElements(arg0 context.Context, arg1 *cx.GetQueueElementsRequest) (*cx.GetQueueElementsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetQueueElements", arg0, arg1)
	ret0, _ := ret[0].(*cx.GetQueueElementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQueueElements indicates an expected call of GetQueueElements.
func (mr *MockCxServerMockRecorder) GetQueueElements(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQueueElements", reflect.TypeOf((*MockCxServer)(nil).GetQueueElements), arg0, arg1)
}

// MarkLoanRequestCancel mocks base method.
func (m *MockCxServer) MarkLoanRequestCancel(arg0 context.Context, arg1 *cx.MarkLoanRequestCancelRequest) (*cx.MarkLoanRequestCancelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MarkLoanRequestCancel", arg0, arg1)
	ret0, _ := ret[0].(*cx.MarkLoanRequestCancelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MarkLoanRequestCancel indicates an expected call of MarkLoanRequestCancel.
func (mr *MockCxServerMockRecorder) MarkLoanRequestCancel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MarkLoanRequestCancel", reflect.TypeOf((*MockCxServer)(nil).MarkLoanRequestCancel), arg0, arg1)
}

// SubmitManualReview mocks base method.
func (m *MockCxServer) SubmitManualReview(arg0 context.Context, arg1 *cx.SubmitManualReviewRequest) (*cx.SubmitManualReviewResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubmitManualReview", arg0, arg1)
	ret0, _ := ret[0].(*cx.SubmitManualReviewResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SubmitManualReview indicates an expected call of SubmitManualReview.
func (mr *MockCxServerMockRecorder) SubmitManualReview(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubmitManualReview", reflect.TypeOf((*MockCxServer)(nil).SubmitManualReview), arg0, arg1)
}

// MockUnsafeCxServer is a mock of UnsafeCxServer interface.
type MockUnsafeCxServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCxServerMockRecorder
}

// MockUnsafeCxServerMockRecorder is the mock recorder for MockUnsafeCxServer.
type MockUnsafeCxServerMockRecorder struct {
	mock *MockUnsafeCxServer
}

// NewMockUnsafeCxServer creates a new mock instance.
func NewMockUnsafeCxServer(ctrl *gomock.Controller) *MockUnsafeCxServer {
	mock := &MockUnsafeCxServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCxServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCxServer) EXPECT() *MockUnsafeCxServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCxServer mocks base method.
func (m *MockUnsafeCxServer) mustEmbedUnimplementedCxServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCxServer")
}

// mustEmbedUnimplementedCxServer indicates an expected call of mustEmbedUnimplementedCxServer.
func (mr *MockUnsafeCxServerMockRecorder) mustEmbedUnimplementedCxServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCxServer", reflect.TypeOf((*MockUnsafeCxServer)(nil).mustEmbedUnimplementedCxServer))
}
