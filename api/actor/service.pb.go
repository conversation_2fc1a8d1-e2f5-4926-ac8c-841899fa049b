// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/actor/service.proto

package actor

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	accounts "github.com/epifi/gamma/api/accounts"
	payment "github.com/epifi/gamma/api/order/payment"
	notification "github.com/epifi/gamma/api/order/payment/notification"
	category "github.com/epifi/gamma/api/pay/category"
	paymentinstrument "github.com/epifi/gamma/api/paymentinstrument"
	timeline "github.com/epifi/gamma/api/timeline"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BulkFetchActorToPIsResponse_Status int32

const (
	// request was successful
	BulkFetchActorToPIsResponse_OK BulkFetchActorToPIsResponse_Status = 0
	// System faced internal errors while processing the request
	BulkFetchActorToPIsResponse_INTERNAL BulkFetchActorToPIsResponse_Status = 13
	// No PIs found
	BulkFetchActorToPIsResponse_RECORD_NOT_FOUND BulkFetchActorToPIsResponse_Status = 5
)

// Enum value maps for BulkFetchActorToPIsResponse_Status.
var (
	BulkFetchActorToPIsResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		5:  "RECORD_NOT_FOUND",
	}
	BulkFetchActorToPIsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INTERNAL":         13,
		"RECORD_NOT_FOUND": 5,
	}
)

func (x BulkFetchActorToPIsResponse_Status) Enum() *BulkFetchActorToPIsResponse_Status {
	p := new(BulkFetchActorToPIsResponse_Status)
	*p = x
	return p
}

func (x BulkFetchActorToPIsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BulkFetchActorToPIsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[0].Descriptor()
}

func (BulkFetchActorToPIsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[0]
}

func (x BulkFetchActorToPIsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BulkFetchActorToPIsResponse_Status.Descriptor instead.
func (BulkFetchActorToPIsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{1, 0}
}

type CreateActorResponse_Status int32

const (
	CreateActorResponse_OK CreateActorResponse_Status = 0
)

// Enum value maps for CreateActorResponse_Status.
var (
	CreateActorResponse_Status_name = map[int32]string{
		0: "OK",
	}
	CreateActorResponse_Status_value = map[string]int32{
		"OK": 0,
	}
)

func (x CreateActorResponse_Status) Enum() *CreateActorResponse_Status {
	p := new(CreateActorResponse_Status)
	*p = x
	return p
}

func (x CreateActorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateActorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[1].Descriptor()
}

func (CreateActorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[1]
}

func (x CreateActorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateActorResponse_Status.Descriptor instead.
func (CreateActorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{3, 0}
}

type GetActorByIdResponse_Status int32

const (
	GetActorByIdResponse_OK GetActorByIdResponse_Status = 0
)

// Enum value maps for GetActorByIdResponse_Status.
var (
	GetActorByIdResponse_Status_name = map[int32]string{
		0: "OK",
	}
	GetActorByIdResponse_Status_value = map[string]int32{
		"OK": 0,
	}
)

func (x GetActorByIdResponse_Status) Enum() *GetActorByIdResponse_Status {
	p := new(GetActorByIdResponse_Status)
	*p = x
	return p
}

func (x GetActorByIdResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetActorByIdResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[2].Descriptor()
}

func (GetActorByIdResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[2]
}

func (x GetActorByIdResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetActorByIdResponse_Status.Descriptor instead.
func (GetActorByIdResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{5, 0}
}

type GetActorByEntityIdResponse_Status int32

const (
	GetActorByEntityIdResponse_OK GetActorByEntityIdResponse_Status = 0
)

// Enum value maps for GetActorByEntityIdResponse_Status.
var (
	GetActorByEntityIdResponse_Status_name = map[int32]string{
		0: "OK",
	}
	GetActorByEntityIdResponse_Status_value = map[string]int32{
		"OK": 0,
	}
)

func (x GetActorByEntityIdResponse_Status) Enum() *GetActorByEntityIdResponse_Status {
	p := new(GetActorByEntityIdResponse_Status)
	*p = x
	return p
}

func (x GetActorByEntityIdResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetActorByEntityIdResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[3].Descriptor()
}

func (GetActorByEntityIdResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[3]
}

func (x GetActorByEntityIdResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetActorByEntityIdResponse_Status.Descriptor instead.
func (GetActorByEntityIdResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{7, 0}
}

type CreateActorPiResolutionResponse_Status int32

const (
	CreateActorPiResolutionResponse_OK CreateActorPiResolutionResponse_Status = 0
)

// Enum value maps for CreateActorPiResolutionResponse_Status.
var (
	CreateActorPiResolutionResponse_Status_name = map[int32]string{
		0: "OK",
	}
	CreateActorPiResolutionResponse_Status_value = map[string]int32{
		"OK": 0,
	}
)

func (x CreateActorPiResolutionResponse_Status) Enum() *CreateActorPiResolutionResponse_Status {
	p := new(CreateActorPiResolutionResponse_Status)
	*p = x
	return p
}

func (x CreateActorPiResolutionResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateActorPiResolutionResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[4].Descriptor()
}

func (CreateActorPiResolutionResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[4]
}

func (x CreateActorPiResolutionResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateActorPiResolutionResponse_Status.Descriptor instead.
func (CreateActorPiResolutionResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{9, 0}
}

type ResolveActorToResponse_Status int32

const (
	ResolveActorToResponse_OK ResolveActorToResponse_Status = 0
)

// Enum value maps for ResolveActorToResponse_Status.
var (
	ResolveActorToResponse_Status_name = map[int32]string{
		0: "OK",
	}
	ResolveActorToResponse_Status_value = map[string]int32{
		"OK": 0,
	}
)

func (x ResolveActorToResponse_Status) Enum() *ResolveActorToResponse_Status {
	p := new(ResolveActorToResponse_Status)
	*p = x
	return p
}

func (x ResolveActorToResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResolveActorToResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[5].Descriptor()
}

func (ResolveActorToResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[5]
}

func (x ResolveActorToResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResolveActorToResponse_Status.Descriptor instead.
func (ResolveActorToResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{11, 0}
}

type ResolveActorFromResponse_Status int32

const (
	ResolveActorFromResponse_OK       ResolveActorFromResponse_Status = 0
	ResolveActorFromResponse_INTERNAL ResolveActorFromResponse_Status = 13
)

// Enum value maps for ResolveActorFromResponse_Status.
var (
	ResolveActorFromResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	ResolveActorFromResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x ResolveActorFromResponse_Status) Enum() *ResolveActorFromResponse_Status {
	p := new(ResolveActorFromResponse_Status)
	*p = x
	return p
}

func (x ResolveActorFromResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResolveActorFromResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[6].Descriptor()
}

func (ResolveActorFromResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[6]
}

func (x ResolveActorFromResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResolveActorFromResponse_Status.Descriptor instead.
func (ResolveActorFromResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{13, 0}
}

type GetPIsOfActorToResponse_Status int32

const (
	GetPIsOfActorToResponse_OK GetPIsOfActorToResponse_Status = 0
)

// Enum value maps for GetPIsOfActorToResponse_Status.
var (
	GetPIsOfActorToResponse_Status_name = map[int32]string{
		0: "OK",
	}
	GetPIsOfActorToResponse_Status_value = map[string]int32{
		"OK": 0,
	}
)

func (x GetPIsOfActorToResponse_Status) Enum() *GetPIsOfActorToResponse_Status {
	p := new(GetPIsOfActorToResponse_Status)
	*p = x
	return p
}

func (x GetPIsOfActorToResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetPIsOfActorToResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[7].Descriptor()
}

func (GetPIsOfActorToResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[7]
}

func (x GetPIsOfActorToResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetPIsOfActorToResponse_Status.Descriptor instead.
func (GetPIsOfActorToResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{15, 0}
}

type GetEntityDetailsByActorIdResponse_Status int32

const (
	GetEntityDetailsByActorIdResponse_OK               GetEntityDetailsByActorIdResponse_Status = 0
	GetEntityDetailsByActorIdResponse_INVALID_ARGUMENT GetEntityDetailsByActorIdResponse_Status = 3
	// actor does not exist
	GetEntityDetailsByActorIdResponse_RECORD_NOT_FOUND GetEntityDetailsByActorIdResponse_Status = 5
	GetEntityDetailsByActorIdResponse_INTERNAL         GetEntityDetailsByActorIdResponse_Status = 13
)

// Enum value maps for GetEntityDetailsByActorIdResponse_Status.
var (
	GetEntityDetailsByActorIdResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	GetEntityDetailsByActorIdResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x GetEntityDetailsByActorIdResponse_Status) Enum() *GetEntityDetailsByActorIdResponse_Status {
	p := new(GetEntityDetailsByActorIdResponse_Status)
	*p = x
	return p
}

func (x GetEntityDetailsByActorIdResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetEntityDetailsByActorIdResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[8].Descriptor()
}

func (GetEntityDetailsByActorIdResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[8]
}

func (x GetEntityDetailsByActorIdResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetEntityDetailsByActorIdResponse_Status.Descriptor instead.
func (GetEntityDetailsByActorIdResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{17, 0}
}

type BlockActorResponse_Status int32

const (
	BlockActorResponse_OK BlockActorResponse_Status = 0
	// invalid request arguments
	BlockActorResponse_INVALID_ARGUMENT BlockActorResponse_Status = 3
	// internal server error
	BlockActorResponse_INTERNAL BlockActorResponse_Status = 13
	// actor is already blocked
	BlockActorResponse_ALREADY_PROCESSED BlockActorResponse_Status = 50
)

// Enum value maps for BlockActorResponse_Status.
var (
	BlockActorResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
		50: "ALREADY_PROCESSED",
	}
	BlockActorResponse_Status_value = map[string]int32{
		"OK":                0,
		"INVALID_ARGUMENT":  3,
		"INTERNAL":          13,
		"ALREADY_PROCESSED": 50,
	}
)

func (x BlockActorResponse_Status) Enum() *BlockActorResponse_Status {
	p := new(BlockActorResponse_Status)
	*p = x
	return p
}

func (x BlockActorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BlockActorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[9].Descriptor()
}

func (BlockActorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[9]
}

func (x BlockActorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BlockActorResponse_Status.Descriptor instead.
func (BlockActorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{19, 0}
}

type UnblockActorResponse_Status int32

const (
	UnblockActorResponse_OK UnblockActorResponse_Status = 0
	// invalid request arguments
	UnblockActorResponse_INVALID_ARGUMENT UnblockActorResponse_Status = 3
	// internal server error
	UnblockActorResponse_INTERNAL UnblockActorResponse_Status = 13
	// actor is already unblocked
	UnblockActorResponse_ALREADY_PROCESSED UnblockActorResponse_Status = 50
)

// Enum value maps for UnblockActorResponse_Status.
var (
	UnblockActorResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
		50: "ALREADY_PROCESSED",
	}
	UnblockActorResponse_Status_value = map[string]int32{
		"OK":                0,
		"INVALID_ARGUMENT":  3,
		"INTERNAL":          13,
		"ALREADY_PROCESSED": 50,
	}
)

func (x UnblockActorResponse_Status) Enum() *UnblockActorResponse_Status {
	p := new(UnblockActorResponse_Status)
	*p = x
	return p
}

func (x UnblockActorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UnblockActorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[10].Descriptor()
}

func (UnblockActorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[10]
}

func (x UnblockActorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UnblockActorResponse_Status.Descriptor instead.
func (UnblockActorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{21, 0}
}

type ReportSpamForBlockedActorResponse_Status int32

const (
	ReportSpamForBlockedActorResponse_OK ReportSpamForBlockedActorResponse_Status = 0
	// invalid request arguments
	ReportSpamForBlockedActorResponse_INVALID_ARGUMENT ReportSpamForBlockedActorResponse_Status = 3
	// internal server error
	ReportSpamForBlockedActorResponse_INTERNAL ReportSpamForBlockedActorResponse_Status = 13
	// actor already marked spam once
	ReportSpamForBlockedActorResponse_ALREADY_PROCESSED ReportSpamForBlockedActorResponse_Status = 50
	// the actor is not blocked
	ReportSpamForBlockedActorResponse_ACTOR_NOT_BLOCKED ReportSpamForBlockedActorResponse_Status = 100
)

// Enum value maps for ReportSpamForBlockedActorResponse_Status.
var (
	ReportSpamForBlockedActorResponse_Status_name = map[int32]string{
		0:   "OK",
		3:   "INVALID_ARGUMENT",
		13:  "INTERNAL",
		50:  "ALREADY_PROCESSED",
		100: "ACTOR_NOT_BLOCKED",
	}
	ReportSpamForBlockedActorResponse_Status_value = map[string]int32{
		"OK":                0,
		"INVALID_ARGUMENT":  3,
		"INTERNAL":          13,
		"ALREADY_PROCESSED": 50,
		"ACTOR_NOT_BLOCKED": 100,
	}
)

func (x ReportSpamForBlockedActorResponse_Status) Enum() *ReportSpamForBlockedActorResponse_Status {
	p := new(ReportSpamForBlockedActorResponse_Status)
	*p = x
	return p
}

func (x ReportSpamForBlockedActorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReportSpamForBlockedActorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[11].Descriptor()
}

func (ReportSpamForBlockedActorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[11]
}

func (x ReportSpamForBlockedActorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReportSpamForBlockedActorResponse_Status.Descriptor instead.
func (ReportSpamForBlockedActorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{23, 0}
}

type GetRelationshipWithActorResponse_Status int32

const (
	GetRelationshipWithActorResponse_OK GetRelationshipWithActorResponse_Status = 0
	// invalid request arguments
	GetRelationshipWithActorResponse_INVALID_ARGUMENT GetRelationshipWithActorResponse_Status = 3
	// internal server error
	GetRelationshipWithActorResponse_INTERNAL GetRelationshipWithActorResponse_Status = 13
)

// Enum value maps for GetRelationshipWithActorResponse_Status.
var (
	GetRelationshipWithActorResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	GetRelationshipWithActorResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x GetRelationshipWithActorResponse_Status) Enum() *GetRelationshipWithActorResponse_Status {
	p := new(GetRelationshipWithActorResponse_Status)
	*p = x
	return p
}

func (x GetRelationshipWithActorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetRelationshipWithActorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[12].Descriptor()
}

func (GetRelationshipWithActorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[12]
}

func (x GetRelationshipWithActorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetRelationshipWithActorResponse_Status.Descriptor instead.
func (GetRelationshipWithActorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{25, 0}
}

type GetRelationshipWithActorResponse_Relationship int32

const (
	GetRelationshipWithActorResponse_RELATION_UNSPECIFIED GetRelationshipWithActorResponse_Relationship = 0
	// other actor is blocked by current actor
	GetRelationshipWithActorResponse_BLOCKED GetRelationshipWithActorResponse_Relationship = 1
	// other actor is not blocked by current actor
	GetRelationshipWithActorResponse_NOT_BLOCKED GetRelationshipWithActorResponse_Relationship = 2
	// other actor is blocked and reported by current actor
	GetRelationshipWithActorResponse_REPORTED GetRelationshipWithActorResponse_Relationship = 3
)

// Enum value maps for GetRelationshipWithActorResponse_Relationship.
var (
	GetRelationshipWithActorResponse_Relationship_name = map[int32]string{
		0: "RELATION_UNSPECIFIED",
		1: "BLOCKED",
		2: "NOT_BLOCKED",
		3: "REPORTED",
	}
	GetRelationshipWithActorResponse_Relationship_value = map[string]int32{
		"RELATION_UNSPECIFIED": 0,
		"BLOCKED":              1,
		"NOT_BLOCKED":          2,
		"REPORTED":             3,
	}
)

func (x GetRelationshipWithActorResponse_Relationship) Enum() *GetRelationshipWithActorResponse_Relationship {
	p := new(GetRelationshipWithActorResponse_Relationship)
	*p = x
	return p
}

func (x GetRelationshipWithActorResponse_Relationship) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetRelationshipWithActorResponse_Relationship) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[13].Descriptor()
}

func (GetRelationshipWithActorResponse_Relationship) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[13]
}

func (x GetRelationshipWithActorResponse_Relationship) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetRelationshipWithActorResponse_Relationship.Descriptor instead.
func (GetRelationshipWithActorResponse_Relationship) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{25, 1}
}

type GetActorsByEntityIdsResponse_Status int32

const (
	GetActorsByEntityIdsResponse_OK GetActorsByEntityIdsResponse_Status = 0
	// internal server error
	GetActorsByEntityIdsResponse_INTERNAL         GetActorsByEntityIdsResponse_Status = 13
	GetActorsByEntityIdsResponse_RECORD_NOT_FOUND GetActorsByEntityIdsResponse_Status = 5
)

// Enum value maps for GetActorsByEntityIdsResponse_Status.
var (
	GetActorsByEntityIdsResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		5:  "RECORD_NOT_FOUND",
	}
	GetActorsByEntityIdsResponse_Status_value = map[string]int32{
		"OK":               0,
		"INTERNAL":         13,
		"RECORD_NOT_FOUND": 5,
	}
)

func (x GetActorsByEntityIdsResponse_Status) Enum() *GetActorsByEntityIdsResponse_Status {
	p := new(GetActorsByEntityIdsResponse_Status)
	*p = x
	return p
}

func (x GetActorsByEntityIdsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetActorsByEntityIdsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[14].Descriptor()
}

func (GetActorsByEntityIdsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[14]
}

func (x GetActorsByEntityIdsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetActorsByEntityIdsResponse_Status.Descriptor instead.
func (GetActorsByEntityIdsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{27, 0}
}

type GetEntityDetailsResponse_Status int32

const (
	GetEntityDetailsResponse_OK GetEntityDetailsResponse_Status = 0
	// internal server error
	GetEntityDetailsResponse_INTERNAL GetEntityDetailsResponse_Status = 13
)

// Enum value maps for GetEntityDetailsResponse_Status.
var (
	GetEntityDetailsResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	GetEntityDetailsResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x GetEntityDetailsResponse_Status) Enum() *GetEntityDetailsResponse_Status {
	p := new(GetEntityDetailsResponse_Status)
	*p = x
	return p
}

func (x GetEntityDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetEntityDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[15].Descriptor()
}

func (GetEntityDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[15]
}

func (x GetEntityDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetEntityDetailsResponse_Status.Descriptor instead.
func (GetEntityDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{29, 0}
}

type BatchHardDeleteActorResponse_Status int32

const (
	BatchHardDeleteActorResponse_OK BatchHardDeleteActorResponse_Status = 0
	// internal error
	BatchHardDeleteActorResponse_INTERNAL BatchHardDeleteActorResponse_Status = 13
	// if mentioned actor identifiers are already deleted/not present in the data base.
	BatchHardDeleteActorResponse_ALREADY_PROCESSED BatchHardDeleteActorResponse_Status = 50
)

// Enum value maps for BatchHardDeleteActorResponse_Status.
var (
	BatchHardDeleteActorResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		50: "ALREADY_PROCESSED",
	}
	BatchHardDeleteActorResponse_Status_value = map[string]int32{
		"OK":                0,
		"INTERNAL":          13,
		"ALREADY_PROCESSED": 50,
	}
)

func (x BatchHardDeleteActorResponse_Status) Enum() *BatchHardDeleteActorResponse_Status {
	p := new(BatchHardDeleteActorResponse_Status)
	*p = x
	return p
}

func (x BatchHardDeleteActorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BatchHardDeleteActorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[16].Descriptor()
}

func (BatchHardDeleteActorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[16]
}

func (x BatchHardDeleteActorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BatchHardDeleteActorResponse_Status.Descriptor instead.
func (BatchHardDeleteActorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{33, 0}
}

type BatchHardDeleteActorPiResolutionResponse_Status int32

const (
	BatchHardDeleteActorPiResolutionResponse_OK BatchHardDeleteActorPiResolutionResponse_Status = 0
	// internal error
	BatchHardDeleteActorPiResolutionResponse_INTERNAL BatchHardDeleteActorPiResolutionResponse_Status = 13
	// if mentioned actor identifiers are already deleted/not present in the data base.
	BatchHardDeleteActorPiResolutionResponse_ALREADY_PROCESSED BatchHardDeleteActorPiResolutionResponse_Status = 50
)

// Enum value maps for BatchHardDeleteActorPiResolutionResponse_Status.
var (
	BatchHardDeleteActorPiResolutionResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		50: "ALREADY_PROCESSED",
	}
	BatchHardDeleteActorPiResolutionResponse_Status_value = map[string]int32{
		"OK":                0,
		"INTERNAL":          13,
		"ALREADY_PROCESSED": 50,
	}
)

func (x BatchHardDeleteActorPiResolutionResponse_Status) Enum() *BatchHardDeleteActorPiResolutionResponse_Status {
	p := new(BatchHardDeleteActorPiResolutionResponse_Status)
	*p = x
	return p
}

func (x BatchHardDeleteActorPiResolutionResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BatchHardDeleteActorPiResolutionResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[17].Descriptor()
}

func (BatchHardDeleteActorPiResolutionResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[17]
}

func (x BatchHardDeleteActorPiResolutionResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BatchHardDeleteActorPiResolutionResponse_Status.Descriptor instead.
func (BatchHardDeleteActorPiResolutionResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{35, 0}
}

type GetActorCapabilitiesResponse_Status int32

const (
	GetActorCapabilitiesResponse_OK GetActorCapabilitiesResponse_Status = 0
	// internal error
	GetActorCapabilitiesResponse_INTERNAL         GetActorCapabilitiesResponse_Status = 13
	GetActorCapabilitiesResponse_RECORD_NOT_FOUND GetActorCapabilitiesResponse_Status = 5
)

// Enum value maps for GetActorCapabilitiesResponse_Status.
var (
	GetActorCapabilitiesResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
		5:  "RECORD_NOT_FOUND",
	}
	GetActorCapabilitiesResponse_Status_value = map[string]int32{
		"OK":               0,
		"INTERNAL":         13,
		"RECORD_NOT_FOUND": 5,
	}
)

func (x GetActorCapabilitiesResponse_Status) Enum() *GetActorCapabilitiesResponse_Status {
	p := new(GetActorCapabilitiesResponse_Status)
	*p = x
	return p
}

func (x GetActorCapabilitiesResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetActorCapabilitiesResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[18].Descriptor()
}

func (GetActorCapabilitiesResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[18]
}

func (x GetActorCapabilitiesResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetActorCapabilitiesResponse_Status.Descriptor instead.
func (GetActorCapabilitiesResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{37, 0}
}

type ResolveOtherActorPiAndTimelineResponse_Status int32

const (
	ResolveOtherActorPiAndTimelineResponse_OK               ResolveOtherActorPiAndTimelineResponse_Status = 0
	ResolveOtherActorPiAndTimelineResponse_INVALID_ARGUMENT ResolveOtherActorPiAndTimelineResponse_Status = 3
	// internal error while processing the request
	ResolveOtherActorPiAndTimelineResponse_INTERNAL ResolveOtherActorPiAndTimelineResponse_Status = 13
)

// Enum value maps for ResolveOtherActorPiAndTimelineResponse_Status.
var (
	ResolveOtherActorPiAndTimelineResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	ResolveOtherActorPiAndTimelineResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x ResolveOtherActorPiAndTimelineResponse_Status) Enum() *ResolveOtherActorPiAndTimelineResponse_Status {
	p := new(ResolveOtherActorPiAndTimelineResponse_Status)
	*p = x
	return p
}

func (x ResolveOtherActorPiAndTimelineResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResolveOtherActorPiAndTimelineResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_actor_service_proto_enumTypes[19].Descriptor()
}

func (ResolveOtherActorPiAndTimelineResponse_Status) Type() protoreflect.EnumType {
	return &file_api_actor_service_proto_enumTypes[19]
}

func (x ResolveOtherActorPiAndTimelineResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResolveOtherActorPiAndTimelineResponse_Status.Descriptor instead.
func (ResolveOtherActorPiAndTimelineResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{39, 0}
}

type BulkFetchActorToPIsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Represents an actor who initiates a payment.
	ActorFrom string `protobuf:"bytes,1,opt,name=actor_from,json=actorFrom,proto3" json:"actor_from,omitempty"`
	// Represents list of actors to whom the payment is being made.
	ActorTo []string `protobuf:"bytes,2,rep,name=actor_to,json=actorTo,proto3" json:"actor_to,omitempty"`
}

func (x *BulkFetchActorToPIsRequest) Reset() {
	*x = BulkFetchActorToPIsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkFetchActorToPIsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkFetchActorToPIsRequest) ProtoMessage() {}

func (x *BulkFetchActorToPIsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkFetchActorToPIsRequest.ProtoReflect.Descriptor instead.
func (*BulkFetchActorToPIsRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{0}
}

func (x *BulkFetchActorToPIsRequest) GetActorFrom() string {
	if x != nil {
		return x.ActorFrom
	}
	return ""
}

func (x *BulkFetchActorToPIsRequest) GetActorTo() []string {
	if x != nil {
		return x.ActorTo
	}
	return nil
}

type BulkFetchActorToPIsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// since there can be multiple PIs for a given actor_from, actor_to mapping
	// actor_pi_mapping is a map of actor_to and list of PIs associated
	ActorPiMapping map[string]*structpb.ListValue `protobuf:"bytes,2,rep,name=actor_pi_mapping,json=actorPiMapping,proto3" json:"actor_pi_mapping,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BulkFetchActorToPIsResponse) Reset() {
	*x = BulkFetchActorToPIsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BulkFetchActorToPIsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BulkFetchActorToPIsResponse) ProtoMessage() {}

func (x *BulkFetchActorToPIsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BulkFetchActorToPIsResponse.ProtoReflect.Descriptor instead.
func (*BulkFetchActorToPIsResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{1}
}

func (x *BulkFetchActorToPIsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *BulkFetchActorToPIsResponse) GetActorPiMapping() map[string]*structpb.ListValue {
	if x != nil {
		return x.ActorPiMapping
	}
	return nil
}

type CreateActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type typesv2.Actor_Type `protobuf:"varint,1,opt,name=type,proto3,enum=api.typesv2.Actor_Type" json:"type,omitempty"`
	// User ID or Merchant ID based on the type of actor
	// In case of external actors, this will be empty
	EntityId string `protobuf:"bytes,2,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	// Name to be present if type is ex-user or ex-merchant since we're not creating ex-user
	// and ex-merchant entity
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// if ownership is coming in request, actor is created with requested ownership
	// if ownership field is empty => actor will be created with default(~`EPIFI_TECH`) ownership
	Ownership common.Ownership `protobuf:"varint,4,opt,name=ownership,proto3,enum=api.typesv2.common.Ownership" json:"ownership,omitempty"`
}

func (x *CreateActorRequest) Reset() {
	*x = CreateActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateActorRequest) ProtoMessage() {}

func (x *CreateActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateActorRequest.ProtoReflect.Descriptor instead.
func (*CreateActorRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateActorRequest) GetType() typesv2.Actor_Type {
	if x != nil {
		return x.Type
	}
	return typesv2.Actor_Type(0)
}

func (x *CreateActorRequest) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

func (x *CreateActorRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateActorRequest) GetOwnership() common.Ownership {
	if x != nil {
		return x.Ownership
	}
	return common.Ownership(0)
}

type CreateActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Actor  *typesv2.Actor `protobuf:"bytes,2,opt,name=actor,proto3" json:"actor,omitempty"`
}

func (x *CreateActorResponse) Reset() {
	*x = CreateActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateActorResponse) ProtoMessage() {}

func (x *CreateActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateActorResponse.ProtoReflect.Descriptor instead.
func (*CreateActorResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{3}
}

func (x *CreateActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateActorResponse) GetActor() *typesv2.Actor {
	if x != nil {
		return x.Actor
	}
	return nil
}

type GetActorByIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetActorByIdRequest) Reset() {
	*x = GetActorByIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActorByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActorByIdRequest) ProtoMessage() {}

func (x *GetActorByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActorByIdRequest.ProtoReflect.Descriptor instead.
func (*GetActorByIdRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetActorByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetActorByIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Actor  *typesv2.Actor `protobuf:"bytes,2,opt,name=actor,proto3" json:"actor,omitempty"`
}

func (x *GetActorByIdResponse) Reset() {
	*x = GetActorByIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActorByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActorByIdResponse) ProtoMessage() {}

func (x *GetActorByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActorByIdResponse.ProtoReflect.Descriptor instead.
func (*GetActorByIdResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetActorByIdResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetActorByIdResponse) GetActor() *typesv2.Actor {
	if x != nil {
		return x.Actor
	}
	return nil
}

type GetActorByEntityIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type typesv2.Actor_Type `protobuf:"varint,1,opt,name=type,proto3,enum=api.typesv2.Actor_Type" json:"type,omitempty"`
	// User ID or Merchant ID based on the type of actor
	// In case of external actors, this will be empty
	EntityId string `protobuf:"bytes,2,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
}

func (x *GetActorByEntityIdRequest) Reset() {
	*x = GetActorByEntityIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActorByEntityIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActorByEntityIdRequest) ProtoMessage() {}

func (x *GetActorByEntityIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActorByEntityIdRequest.ProtoReflect.Descriptor instead.
func (*GetActorByEntityIdRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetActorByEntityIdRequest) GetType() typesv2.Actor_Type {
	if x != nil {
		return x.Type
	}
	return typesv2.Actor_Type(0)
}

func (x *GetActorByEntityIdRequest) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

type GetActorByEntityIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Actor  *typesv2.Actor `protobuf:"bytes,2,opt,name=actor,proto3" json:"actor,omitempty"`
}

func (x *GetActorByEntityIdResponse) Reset() {
	*x = GetActorByEntityIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActorByEntityIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActorByEntityIdResponse) ProtoMessage() {}

func (x *GetActorByEntityIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActorByEntityIdResponse.ProtoReflect.Descriptor instead.
func (*GetActorByEntityIdResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetActorByEntityIdResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetActorByEntityIdResponse) GetActor() *typesv2.Actor {
	if x != nil {
		return x.Actor
	}
	return nil
}

// actor_from and actor_to is mandatory.
// Exactly one of pi_to and pi_from must be present.
type CreateActorPiResolutionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Reference to actor id from actor service
	// It represents an actor who initiates a payment.
	// If pi_to is present, it represents an internal actor who initiates a payment
	// else if pi_from is present, it represents an (internal/external) actor
	// from whom payment was received
	ActorFrom string `protobuf:"bytes,1,opt,name=actor_from,json=actorFrom,proto3" json:"actor_from,omitempty"`
	// Reference to actor id from actor service
	// It represents an actor who receives a payment.
	ActorTo string `protobuf:"bytes,2,opt,name=actor_to,json=actorTo,proto3" json:"actor_to,omitempty"`
	// Reference to PI id from PI service
	// For outbound transfer (actor_from, actor_to, pi_to), it holds no significance
	// For inbound transfer (actor_from, actor_to, pi_from), it represents the PI
	// from which money will be debited from.
	PiFrom string `protobuf:"bytes,3,opt,name=pi_from,json=piFrom,proto3" json:"pi_from,omitempty"`
	// Reference to PI id from PI service
	// For outbound transfer (actor_from, actor_to, pi_to), it represents the PI
	// in which money will be credited to.
	// For inbound transfer (actor_from, actor_to, pi_from), it holds no significance
	PiTo string `protobuf:"bytes,4,opt,name=pi_to,json=piTo,proto3" json:"pi_to,omitempty"`
}

func (x *CreateActorPiResolutionRequest) Reset() {
	*x = CreateActorPiResolutionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateActorPiResolutionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateActorPiResolutionRequest) ProtoMessage() {}

func (x *CreateActorPiResolutionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateActorPiResolutionRequest.ProtoReflect.Descriptor instead.
func (*CreateActorPiResolutionRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{8}
}

func (x *CreateActorPiResolutionRequest) GetActorFrom() string {
	if x != nil {
		return x.ActorFrom
	}
	return ""
}

func (x *CreateActorPiResolutionRequest) GetActorTo() string {
	if x != nil {
		return x.ActorTo
	}
	return ""
}

func (x *CreateActorPiResolutionRequest) GetPiFrom() string {
	if x != nil {
		return x.PiFrom
	}
	return ""
}

func (x *CreateActorPiResolutionRequest) GetPiTo() string {
	if x != nil {
		return x.PiTo
	}
	return ""
}

type CreateActorPiResolutionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status            *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ActorPiResolution *ActorPiResolution `protobuf:"bytes,2,opt,name=actor_pi_resolution,json=actorPiResolution,proto3" json:"actor_pi_resolution,omitempty"`
}

func (x *CreateActorPiResolutionResponse) Reset() {
	*x = CreateActorPiResolutionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateActorPiResolutionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateActorPiResolutionResponse) ProtoMessage() {}

func (x *CreateActorPiResolutionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateActorPiResolutionResponse.ProtoReflect.Descriptor instead.
func (*CreateActorPiResolutionResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{9}
}

func (x *CreateActorPiResolutionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateActorPiResolutionResponse) GetActorPiResolution() *ActorPiResolution {
	if x != nil {
		return x.ActorPiResolution
	}
	return nil
}

type ResolveActorToRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Represents an actor who initiates a payment.
	ActorFrom string `protobuf:"bytes,1,opt,name=actor_from,json=actorFrom,proto3" json:"actor_from,omitempty"`
	// Represents the PI in which money will be credited to.
	PiTo string `protobuf:"bytes,2,opt,name=pi_to,json=piTo,proto3" json:"pi_to,omitempty"`
	// Name of the actor to whom money is being transferred
	ActorToName string `protobuf:"bytes,3,opt,name=actor_to_name,json=actorToName,proto3" json:"actor_to_name,omitempty"`
	// ownership for actor; if actor is created during resolve
	Ownership common.Ownership `protobuf:"varint,4,opt,name=ownership,proto3,enum=api.typesv2.common.Ownership" json:"ownership,omitempty"`
}

func (x *ResolveActorToRequest) Reset() {
	*x = ResolveActorToRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResolveActorToRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveActorToRequest) ProtoMessage() {}

func (x *ResolveActorToRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveActorToRequest.ProtoReflect.Descriptor instead.
func (*ResolveActorToRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{10}
}

func (x *ResolveActorToRequest) GetActorFrom() string {
	if x != nil {
		return x.ActorFrom
	}
	return ""
}

func (x *ResolveActorToRequest) GetPiTo() string {
	if x != nil {
		return x.PiTo
	}
	return ""
}

func (x *ResolveActorToRequest) GetActorToName() string {
	if x != nil {
		return x.ActorToName
	}
	return ""
}

func (x *ResolveActorToRequest) GetOwnership() common.Ownership {
	if x != nil {
		return x.Ownership
	}
	return common.Ownership(0)
}

type ResolveActorToResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Represents an actor who receives a payment
	ActorTo string `protobuf:"bytes,2,opt,name=actor_to,json=actorTo,proto3" json:"actor_to,omitempty"`
}

func (x *ResolveActorToResponse) Reset() {
	*x = ResolveActorToResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResolveActorToResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveActorToResponse) ProtoMessage() {}

func (x *ResolveActorToResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveActorToResponse.ProtoReflect.Descriptor instead.
func (*ResolveActorToResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{11}
}

func (x *ResolveActorToResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ResolveActorToResponse) GetActorTo() string {
	if x != nil {
		return x.ActorTo
	}
	return ""
}

type ResolveActorFromRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Represents an actor who received the payment
	ActorTo string `protobuf:"bytes,1,opt,name=actor_to,json=actorTo,proto3" json:"actor_to,omitempty"`
	// Represents the PI from which money was debited from
	PiFrom string `protobuf:"bytes,2,opt,name=pi_from,json=piFrom,proto3" json:"pi_from,omitempty"`
	// Name of the actor from whom money was received
	ActorFromName string `protobuf:"bytes,3,opt,name=actor_from_name,json=actorFromName,proto3" json:"actor_from_name,omitempty"`
	// ownership of the actor to be created
	Ownership common.Ownership `protobuf:"varint,4,opt,name=ownership,proto3,enum=api.typesv2.common.Ownership" json:"ownership,omitempty"`
}

func (x *ResolveActorFromRequest) Reset() {
	*x = ResolveActorFromRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResolveActorFromRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveActorFromRequest) ProtoMessage() {}

func (x *ResolveActorFromRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveActorFromRequest.ProtoReflect.Descriptor instead.
func (*ResolveActorFromRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{12}
}

func (x *ResolveActorFromRequest) GetActorTo() string {
	if x != nil {
		return x.ActorTo
	}
	return ""
}

func (x *ResolveActorFromRequest) GetPiFrom() string {
	if x != nil {
		return x.PiFrom
	}
	return ""
}

func (x *ResolveActorFromRequest) GetActorFromName() string {
	if x != nil {
		return x.ActorFromName
	}
	return ""
}

func (x *ResolveActorFromRequest) GetOwnership() common.Ownership {
	if x != nil {
		return x.Ownership
	}
	return common.Ownership(0)
}

type ResolveActorFromResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Represents an actor from whom payment was received
	ActorFrom string `protobuf:"bytes,2,opt,name=actor_from,json=actorFrom,proto3" json:"actor_from,omitempty"`
}

func (x *ResolveActorFromResponse) Reset() {
	*x = ResolveActorFromResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResolveActorFromResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveActorFromResponse) ProtoMessage() {}

func (x *ResolveActorFromResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveActorFromResponse.ProtoReflect.Descriptor instead.
func (*ResolveActorFromResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{13}
}

func (x *ResolveActorFromResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ResolveActorFromResponse) GetActorFrom() string {
	if x != nil {
		return x.ActorFrom
	}
	return ""
}

type GetPIsOfActorToRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Represents an actor who initiates a payment.
	ActorFrom string `protobuf:"bytes,1,opt,name=actor_from,json=actorFrom,proto3" json:"actor_from,omitempty"`
	// Represents an actor to whom the payment is being made.
	ActorTo string `protobuf:"bytes,2,opt,name=actor_to,json=actorTo,proto3" json:"actor_to,omitempty"`
	// Limit on the number of PIs to be fetched. if limit is not provided or provided as 0, we will fetch all PIs.
	// Note: `Limit` Parameter is only used For `Actor_EXTERNAL_MERCHANT`.For other actor types, the limit is ignored, and we attempt to fetch all PIs
	Limit int32 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *GetPIsOfActorToRequest) Reset() {
	*x = GetPIsOfActorToRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPIsOfActorToRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPIsOfActorToRequest) ProtoMessage() {}

func (x *GetPIsOfActorToRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPIsOfActorToRequest.ProtoReflect.Descriptor instead.
func (*GetPIsOfActorToRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetPIsOfActorToRequest) GetActorFrom() string {
	if x != nil {
		return x.ActorFrom
	}
	return ""
}

func (x *GetPIsOfActorToRequest) GetActorTo() string {
	if x != nil {
		return x.ActorTo
	}
	return ""
}

func (x *GetPIsOfActorToRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GetPIsOfActorToResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of PIs that belongs to actor_to, that actor_from knows of
	PiIds []string `protobuf:"bytes,2,rep,name=pi_ids,json=piIds,proto3" json:"pi_ids,omitempty"`
}

func (x *GetPIsOfActorToResponse) Reset() {
	*x = GetPIsOfActorToResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPIsOfActorToResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPIsOfActorToResponse) ProtoMessage() {}

func (x *GetPIsOfActorToResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPIsOfActorToResponse.ProtoReflect.Descriptor instead.
func (*GetPIsOfActorToResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetPIsOfActorToResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPIsOfActorToResponse) GetPiIds() []string {
	if x != nil {
		return x.PiIds
	}
	return nil
}

type GetEntityDetailsByActorIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id for which we need the details
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetEntityDetailsByActorIdRequest) Reset() {
	*x = GetEntityDetailsByActorIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEntityDetailsByActorIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntityDetailsByActorIdRequest) ProtoMessage() {}

func (x *GetEntityDetailsByActorIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntityDetailsByActorIdRequest.ProtoReflect.Descriptor instead.
func (*GetEntityDetailsByActorIdRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetEntityDetailsByActorIdRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetEntityDetailsByActorIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// User ID or Merchant ID based on the type of actor
	// In case of external actors, this will be empty
	EntityId string `protobuf:"bytes,2,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	// name of the entity (user/merchant)
	Name *common.Name `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// mobile number of the entity (user/merchant)
	MobileNumber *common.PhoneNumber `protobuf:"bytes,4,opt,name=mobile_number,json=mobileNumber,proto3" json:"mobile_number,omitempty"`
	// url profile image of the entity (user/merchant)
	ProfileImageUrl string `protobuf:"bytes,5,opt,name=profile_image_url,json=profileImageUrl,proto3" json:"profile_image_url,omitempty"`
	// email id of the entity (user/merchant)
	EmailId string `protobuf:"bytes,6,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	// Idea is to add entities separately and let the caller take decisions based on type of actor
	// More entities like external user, user, internal merchant can be added need basis
	//
	// Types that are assignable to Entity:
	//
	//	*GetEntityDetailsByActorIdResponse_Merchant_
	Entity isGetEntityDetailsByActorIdResponse_Entity `protobuf_oneof:"entity"`
	// legal name of the entity
	LegalName *common.Name      `protobuf:"bytes,8,opt,name=legal_name,json=legalName,proto3" json:"legal_name,omitempty"`
	Type      typesv2.ActorType `protobuf:"varint,9,opt,name=type,proto3,enum=api.typesv2.ActorType" json:"type,omitempty"`
}

func (x *GetEntityDetailsByActorIdResponse) Reset() {
	*x = GetEntityDetailsByActorIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEntityDetailsByActorIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntityDetailsByActorIdResponse) ProtoMessage() {}

func (x *GetEntityDetailsByActorIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntityDetailsByActorIdResponse.ProtoReflect.Descriptor instead.
func (*GetEntityDetailsByActorIdResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetEntityDetailsByActorIdResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetEntityDetailsByActorIdResponse) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

func (x *GetEntityDetailsByActorIdResponse) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *GetEntityDetailsByActorIdResponse) GetMobileNumber() *common.PhoneNumber {
	if x != nil {
		return x.MobileNumber
	}
	return nil
}

func (x *GetEntityDetailsByActorIdResponse) GetProfileImageUrl() string {
	if x != nil {
		return x.ProfileImageUrl
	}
	return ""
}

func (x *GetEntityDetailsByActorIdResponse) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (m *GetEntityDetailsByActorIdResponse) GetEntity() isGetEntityDetailsByActorIdResponse_Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (x *GetEntityDetailsByActorIdResponse) GetMerchant() *GetEntityDetailsByActorIdResponse_Merchant {
	if x, ok := x.GetEntity().(*GetEntityDetailsByActorIdResponse_Merchant_); ok {
		return x.Merchant
	}
	return nil
}

func (x *GetEntityDetailsByActorIdResponse) GetLegalName() *common.Name {
	if x != nil {
		return x.LegalName
	}
	return nil
}

func (x *GetEntityDetailsByActorIdResponse) GetType() typesv2.ActorType {
	if x != nil {
		return x.Type
	}
	return typesv2.ActorType(0)
}

type isGetEntityDetailsByActorIdResponse_Entity interface {
	isGetEntityDetailsByActorIdResponse_Entity()
}

type GetEntityDetailsByActorIdResponse_Merchant_ struct {
	Merchant *GetEntityDetailsByActorIdResponse_Merchant `protobuf:"bytes,7,opt,name=merchant,proto3,oneof"`
}

func (*GetEntityDetailsByActorIdResponse_Merchant_) isGetEntityDetailsByActorIdResponse_Entity() {}

type BlockActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id for the logged in actor
	CurrentActorId string `protobuf:"bytes,1,opt,name=current_actor_id,json=currentActorId,proto3" json:"current_actor_id,omitempty"`
	// actor id to be blocked
	OtherActorId string `protobuf:"bytes,2,opt,name=other_actor_id,json=otherActorId,proto3" json:"other_actor_id,omitempty"`
	// tells whether to report the user as spam while blocking
	IsSpam bool `protobuf:"varint,3,opt,name=is_spam,json=isSpam,proto3" json:"is_spam,omitempty"`
}

func (x *BlockActorRequest) Reset() {
	*x = BlockActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockActorRequest) ProtoMessage() {}

func (x *BlockActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockActorRequest.ProtoReflect.Descriptor instead.
func (*BlockActorRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{18}
}

func (x *BlockActorRequest) GetCurrentActorId() string {
	if x != nil {
		return x.CurrentActorId
	}
	return ""
}

func (x *BlockActorRequest) GetOtherActorId() string {
	if x != nil {
		return x.OtherActorId
	}
	return ""
}

func (x *BlockActorRequest) GetIsSpam() bool {
	if x != nil {
		return x.IsSpam
	}
	return false
}

type BlockActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *BlockActorResponse) Reset() {
	*x = BlockActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockActorResponse) ProtoMessage() {}

func (x *BlockActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockActorResponse.ProtoReflect.Descriptor instead.
func (*BlockActorResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{19}
}

func (x *BlockActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type UnblockActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id for the logged in actor
	CurrentActorId string `protobuf:"bytes,1,opt,name=current_actor_id,json=currentActorId,proto3" json:"current_actor_id,omitempty"`
	// actor id to be unblocked
	OtherActorId string `protobuf:"bytes,2,opt,name=other_actor_id,json=otherActorId,proto3" json:"other_actor_id,omitempty"`
}

func (x *UnblockActorRequest) Reset() {
	*x = UnblockActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnblockActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnblockActorRequest) ProtoMessage() {}

func (x *UnblockActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnblockActorRequest.ProtoReflect.Descriptor instead.
func (*UnblockActorRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{20}
}

func (x *UnblockActorRequest) GetCurrentActorId() string {
	if x != nil {
		return x.CurrentActorId
	}
	return ""
}

func (x *UnblockActorRequest) GetOtherActorId() string {
	if x != nil {
		return x.OtherActorId
	}
	return ""
}

type UnblockActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UnblockActorResponse) Reset() {
	*x = UnblockActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnblockActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnblockActorResponse) ProtoMessage() {}

func (x *UnblockActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnblockActorResponse.ProtoReflect.Descriptor instead.
func (*UnblockActorResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{21}
}

func (x *UnblockActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ReportSpamForBlockedActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id for the logged in actor
	CurrentActorId string `protobuf:"bytes,1,opt,name=current_actor_id,json=currentActorId,proto3" json:"current_actor_id,omitempty"`
	// blocked actor's identifier
	BlockedActorId string `protobuf:"bytes,2,opt,name=blocked_actor_id,json=blockedActorId,proto3" json:"blocked_actor_id,omitempty"`
}

func (x *ReportSpamForBlockedActorRequest) Reset() {
	*x = ReportSpamForBlockedActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportSpamForBlockedActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportSpamForBlockedActorRequest) ProtoMessage() {}

func (x *ReportSpamForBlockedActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportSpamForBlockedActorRequest.ProtoReflect.Descriptor instead.
func (*ReportSpamForBlockedActorRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{22}
}

func (x *ReportSpamForBlockedActorRequest) GetCurrentActorId() string {
	if x != nil {
		return x.CurrentActorId
	}
	return ""
}

func (x *ReportSpamForBlockedActorRequest) GetBlockedActorId() string {
	if x != nil {
		return x.BlockedActorId
	}
	return ""
}

type ReportSpamForBlockedActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ReportSpamForBlockedActorResponse) Reset() {
	*x = ReportSpamForBlockedActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportSpamForBlockedActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportSpamForBlockedActorResponse) ProtoMessage() {}

func (x *ReportSpamForBlockedActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportSpamForBlockedActorResponse.ProtoReflect.Descriptor instead.
func (*ReportSpamForBlockedActorResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{23}
}

func (x *ReportSpamForBlockedActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetRelationshipWithActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id for the logged in actor
	CurrentActorId string `protobuf:"bytes,1,opt,name=current_actor_id,json=currentActorId,proto3" json:"current_actor_id,omitempty"`
	// actor id against whom query is being made
	OtherActorId string `protobuf:"bytes,2,opt,name=other_actor_id,json=otherActorId,proto3" json:"other_actor_id,omitempty"`
}

func (x *GetRelationshipWithActorRequest) Reset() {
	*x = GetRelationshipWithActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRelationshipWithActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRelationshipWithActorRequest) ProtoMessage() {}

func (x *GetRelationshipWithActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRelationshipWithActorRequest.ProtoReflect.Descriptor instead.
func (*GetRelationshipWithActorRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetRelationshipWithActorRequest) GetCurrentActorId() string {
	if x != nil {
		return x.CurrentActorId
	}
	return ""
}

func (x *GetRelationshipWithActorRequest) GetOtherActorId() string {
	if x != nil {
		return x.OtherActorId
	}
	return ""
}

type GetRelationshipWithActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// relationship between other actor and current logged in actor
	Relationship GetRelationshipWithActorResponse_Relationship `protobuf:"varint,2,opt,name=relationship,proto3,enum=actor.GetRelationshipWithActorResponse_Relationship" json:"relationship,omitempty"`
}

func (x *GetRelationshipWithActorResponse) Reset() {
	*x = GetRelationshipWithActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRelationshipWithActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRelationshipWithActorResponse) ProtoMessage() {}

func (x *GetRelationshipWithActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRelationshipWithActorResponse.ProtoReflect.Descriptor instead.
func (*GetRelationshipWithActorResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetRelationshipWithActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRelationshipWithActorResponse) GetRelationship() GetRelationshipWithActorResponse_Relationship {
	if x != nil {
		return x.Relationship
	}
	return GetRelationshipWithActorResponse_RELATION_UNSPECIFIED
}

type GetActorsByEntityIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of actor id's
	EntityIds []string `protobuf:"bytes,1,rep,name=entity_ids,json=entityIds,proto3" json:"entity_ids,omitempty"`
}

func (x *GetActorsByEntityIdsRequest) Reset() {
	*x = GetActorsByEntityIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActorsByEntityIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActorsByEntityIdsRequest) ProtoMessage() {}

func (x *GetActorsByEntityIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActorsByEntityIdsRequest.ProtoReflect.Descriptor instead.
func (*GetActorsByEntityIdsRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{26}
}

func (x *GetActorsByEntityIdsRequest) GetEntityIds() []string {
	if x != nil {
		return x.EntityIds
	}
	return nil
}

type GetActorsByEntityIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Actors []*typesv2.Actor `protobuf:"bytes,2,rep,name=actors,proto3" json:"actors,omitempty"`
}

func (x *GetActorsByEntityIdsResponse) Reset() {
	*x = GetActorsByEntityIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActorsByEntityIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActorsByEntityIdsResponse) ProtoMessage() {}

func (x *GetActorsByEntityIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActorsByEntityIdsResponse.ProtoReflect.Descriptor instead.
func (*GetActorsByEntityIdsResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetActorsByEntityIdsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetActorsByEntityIdsResponse) GetActors() []*typesv2.Actor {
	if x != nil {
		return x.Actors
	}
	return nil
}

type GetEntityDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of actor id's
	ActorIds []string `protobuf:"bytes,1,rep,name=actor_ids,json=actorIds,proto3" json:"actor_ids,omitempty"`
	// flag to determine if entity details for soft-deleted users should be fetched or not.
	FetchSoftDeletedUsers bool `protobuf:"varint,2,opt,name=fetch_soft_deleted_users,json=fetchSoftDeletedUsers,proto3" json:"fetch_soft_deleted_users,omitempty"`
}

func (x *GetEntityDetailsRequest) Reset() {
	*x = GetEntityDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEntityDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntityDetailsRequest) ProtoMessage() {}

func (x *GetEntityDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntityDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetEntityDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{28}
}

func (x *GetEntityDetailsRequest) GetActorIds() []string {
	if x != nil {
		return x.ActorIds
	}
	return nil
}

func (x *GetEntityDetailsRequest) GetFetchSoftDeletedUsers() bool {
	if x != nil {
		return x.FetchSoftDeletedUsers
	}
	return false
}

type GetEntityDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of entity details. The order of the entity details will be random and is independent of request passed.
	EntityDetails []*GetEntityDetailsResponse_EntityDetail `protobuf:"bytes,2,rep,name=entity_details,json=entityDetails,proto3" json:"entity_details,omitempty"`
}

func (x *GetEntityDetailsResponse) Reset() {
	*x = GetEntityDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEntityDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntityDetailsResponse) ProtoMessage() {}

func (x *GetEntityDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntityDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetEntityDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{29}
}

func (x *GetEntityDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetEntityDetailsResponse) GetEntityDetails() []*GetEntityDetailsResponse_EntityDetail {
	if x != nil {
		return x.EntityDetails
	}
	return nil
}

type DeleteActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *DeleteActorRequest) Reset() {
	*x = DeleteActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteActorRequest) ProtoMessage() {}

func (x *DeleteActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteActorRequest.ProtoReflect.Descriptor instead.
func (*DeleteActorRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{30}
}

func (x *DeleteActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type DeleteActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DeleteActorResponse) Reset() {
	*x = DeleteActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteActorResponse) ProtoMessage() {}

func (x *DeleteActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteActorResponse.ProtoReflect.Descriptor instead.
func (*DeleteActorResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{31}
}

func (x *DeleteActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type BatchHardDeleteActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorIds []string `protobuf:"bytes,1,rep,name=actor_ids,json=actorIds,proto3" json:"actor_ids,omitempty"`
}

func (x *BatchHardDeleteActorRequest) Reset() {
	*x = BatchHardDeleteActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchHardDeleteActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchHardDeleteActorRequest) ProtoMessage() {}

func (x *BatchHardDeleteActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchHardDeleteActorRequest.ProtoReflect.Descriptor instead.
func (*BatchHardDeleteActorRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{32}
}

func (x *BatchHardDeleteActorRequest) GetActorIds() []string {
	if x != nil {
		return x.ActorIds
	}
	return nil
}

type BatchHardDeleteActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *BatchHardDeleteActorResponse) Reset() {
	*x = BatchHardDeleteActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchHardDeleteActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchHardDeleteActorResponse) ProtoMessage() {}

func (x *BatchHardDeleteActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchHardDeleteActorResponse.ProtoReflect.Descriptor instead.
func (*BatchHardDeleteActorResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{33}
}

func (x *BatchHardDeleteActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type BatchHardDeleteActorPiResolutionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorIds []string `protobuf:"bytes,1,rep,name=actor_ids,json=actorIds,proto3" json:"actor_ids,omitempty"`
}

func (x *BatchHardDeleteActorPiResolutionRequest) Reset() {
	*x = BatchHardDeleteActorPiResolutionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchHardDeleteActorPiResolutionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchHardDeleteActorPiResolutionRequest) ProtoMessage() {}

func (x *BatchHardDeleteActorPiResolutionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchHardDeleteActorPiResolutionRequest.ProtoReflect.Descriptor instead.
func (*BatchHardDeleteActorPiResolutionRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{34}
}

func (x *BatchHardDeleteActorPiResolutionRequest) GetActorIds() []string {
	if x != nil {
		return x.ActorIds
	}
	return nil
}

type BatchHardDeleteActorPiResolutionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *BatchHardDeleteActorPiResolutionResponse) Reset() {
	*x = BatchHardDeleteActorPiResolutionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchHardDeleteActorPiResolutionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchHardDeleteActorPiResolutionResponse) ProtoMessage() {}

func (x *BatchHardDeleteActorPiResolutionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchHardDeleteActorPiResolutionResponse.ProtoReflect.Descriptor instead.
func (*BatchHardDeleteActorPiResolutionResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{35}
}

func (x *BatchHardDeleteActorPiResolutionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetActorCapabilitiesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetActorCapabilitiesRequest) Reset() {
	*x = GetActorCapabilitiesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActorCapabilitiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActorCapabilitiesRequest) ProtoMessage() {}

func (x *GetActorCapabilitiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActorCapabilitiesRequest.ProtoReflect.Descriptor instead.
func (*GetActorCapabilitiesRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetActorCapabilitiesRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetActorCapabilitiesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// value true for enable and false for disable
	Value map[int32]bool `protobuf:"bytes,2,rep,name=value,proto3" json:"value,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *GetActorCapabilitiesResponse) Reset() {
	*x = GetActorCapabilitiesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActorCapabilitiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActorCapabilitiesResponse) ProtoMessage() {}

func (x *GetActorCapabilitiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActorCapabilitiesResponse.ProtoReflect.Descriptor instead.
func (*GetActorCapabilitiesResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{37}
}

func (x *GetActorCapabilitiesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetActorCapabilitiesResponse) GetValue() map[int32]bool {
	if x != nil {
		return x.Value
	}
	return nil
}

// If we are using it for ATM merchant creation and resolution points to be noted are:-
// 1. we have to pass transaction_category as ATM_WITHDRAWAL OR ATM DEPOSIT
// 2.AtmAddress, MerchantDetails and OtherActorName should be same as evaluated through parser in vendor notification
// 3.pi_identifier can be passed as card or can be left blank as we are creating it within the RPC
type ResolveOtherActorPiAndTimelineRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id of the internal user
	PrimaryActorId string `protobuf:"bytes,1,opt,name=primary_actor_id,json=primaryActorId,proto3" json:"primary_actor_id,omitempty"`
	// payment protocol eg. UPI, NEFT etc.
	PaymentProtocol payment.PaymentProtocol `protobuf:"varint,2,opt,name=payment_protocol,json=paymentProtocol,proto3,enum=order.payment.PaymentProtocol" json:"payment_protocol,omitempty"`
	// credit/debit transaction
	EventType typesv2.TransactionTransferType `protobuf:"varint,3,opt,name=event_type,json=eventType,proto3,enum=api.typesv2.TransactionTransferType" json:"event_type,omitempty"`
	// type of account involved for the internal actor
	AccountType accounts.Type `protobuf:"varint,4,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
	// Vendor bank where transaction occurred
	PartnerBank vendorgateway.Vendor `protobuf:"varint,5,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// pi identifier for the other actor
	// for credit pi belonging to the payer
	// for debit pi belonging to the payee
	//
	// Types that are assignable to PiIdentifier:
	//
	//	*ResolveOtherActorPiAndTimelineRequest_Vpa
	//	*ResolveOtherActorPiAndTimelineRequest_Account
	//	*ResolveOtherActorPiAndTimelineRequest_PiId
	//	*ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier_
	PiIdentifier isResolveOtherActorPiAndTimelineRequest_PiIdentifier `protobuf_oneof:"pi_identifier"`
	// ownership of the other actor pi (keep entity segregation in mind while passing this)
	// will be assigned tech if not passed
	PiOwnership       paymentinstrument.Ownership `protobuf:"varint,10,opt,name=pi_ownership,json=piOwnership,proto3,enum=paymentinstrument.Ownership" json:"pi_ownership,omitempty"`
	TimelineOwnership timeline.Ownership          `protobuf:"varint,11,opt,name=timeline_ownership,json=timelineOwnership,proto3,enum=timeline.Ownership" json:"timeline_ownership,omitempty"`
	// merchant details in case of card transactions (both credit and debit card)
	MerchantDetails *MerchantDetails `protobuf:"bytes,12,opt,name=merchant_details,json=merchantDetails,proto3" json:"merchant_details,omitempty"`
	OtherActorName  string           `protobuf:"bytes,13,opt,name=other_actor_name,json=otherActorName,proto3" json:"other_actor_name,omitempty"`
	// transaction category (eg. POS,ATM etc.)
	TransactionCategory category.TransactionCategory `protobuf:"varint,14,opt,name=transaction_category,json=transactionCategory,proto3,enum=pay.category.TransactionCategory" json:"transaction_category,omitempty"`
	// address of the ATM machine in case of  ATM withdrawal or cash deposit.
	AtmAddress string `protobuf:"bytes,15,opt,name=atm_address,json=atmAddress,proto3" json:"atm_address,omitempty"`
	// Enum to identify whether the timeline will be created for transactions created via new inbound notification or for transactions
	// getting updated via transaction backfill workflow
	TimelineResolutionSource timeline.TimelineResolutionSource `protobuf:"varint,16,opt,name=timeline_resolution_source,json=timelineResolutionSource,proto3,enum=timeline.TimelineResolutionSource" json:"timeline_resolution_source,omitempty"`
	// details regarding the ecs_enach mandate transactions
	// it can be related to chrarges for these mandates also
	EcsEnachDetails *notification.EcsEnachMandateDetails `protobuf:"bytes,17,opt,name=ecs_enach_details,json=ecsEnachDetails,proto3" json:"ecs_enach_details,omitempty"`
}

func (x *ResolveOtherActorPiAndTimelineRequest) Reset() {
	*x = ResolveOtherActorPiAndTimelineRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResolveOtherActorPiAndTimelineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveOtherActorPiAndTimelineRequest) ProtoMessage() {}

func (x *ResolveOtherActorPiAndTimelineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveOtherActorPiAndTimelineRequest.ProtoReflect.Descriptor instead.
func (*ResolveOtherActorPiAndTimelineRequest) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{38}
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetPrimaryActorId() string {
	if x != nil {
		return x.PrimaryActorId
	}
	return ""
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetPaymentProtocol() payment.PaymentProtocol {
	if x != nil {
		return x.PaymentProtocol
	}
	return payment.PaymentProtocol(0)
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetEventType() typesv2.TransactionTransferType {
	if x != nil {
		return x.EventType
	}
	return typesv2.TransactionTransferType(0)
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (m *ResolveOtherActorPiAndTimelineRequest) GetPiIdentifier() isResolveOtherActorPiAndTimelineRequest_PiIdentifier {
	if m != nil {
		return m.PiIdentifier
	}
	return nil
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetVpa() string {
	if x, ok := x.GetPiIdentifier().(*ResolveOtherActorPiAndTimelineRequest_Vpa); ok {
		return x.Vpa
	}
	return ""
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetAccount() *ResolveOtherActorPiAndTimelineRequest_AccountIdentifier {
	if x, ok := x.GetPiIdentifier().(*ResolveOtherActorPiAndTimelineRequest_Account); ok {
		return x.Account
	}
	return nil
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetPiId() string {
	if x, ok := x.GetPiIdentifier().(*ResolveOtherActorPiAndTimelineRequest_PiId); ok {
		return x.PiId
	}
	return ""
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetCardTransactionIdentifier() *ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier {
	if x, ok := x.GetPiIdentifier().(*ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier_); ok {
		return x.CardTransactionIdentifier
	}
	return nil
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetPiOwnership() paymentinstrument.Ownership {
	if x != nil {
		return x.PiOwnership
	}
	return paymentinstrument.Ownership(0)
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetTimelineOwnership() timeline.Ownership {
	if x != nil {
		return x.TimelineOwnership
	}
	return timeline.Ownership(0)
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetMerchantDetails() *MerchantDetails {
	if x != nil {
		return x.MerchantDetails
	}
	return nil
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetOtherActorName() string {
	if x != nil {
		return x.OtherActorName
	}
	return ""
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetTransactionCategory() category.TransactionCategory {
	if x != nil {
		return x.TransactionCategory
	}
	return category.TransactionCategory(0)
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetAtmAddress() string {
	if x != nil {
		return x.AtmAddress
	}
	return ""
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetTimelineResolutionSource() timeline.TimelineResolutionSource {
	if x != nil {
		return x.TimelineResolutionSource
	}
	return timeline.TimelineResolutionSource(0)
}

func (x *ResolveOtherActorPiAndTimelineRequest) GetEcsEnachDetails() *notification.EcsEnachMandateDetails {
	if x != nil {
		return x.EcsEnachDetails
	}
	return nil
}

type isResolveOtherActorPiAndTimelineRequest_PiIdentifier interface {
	isResolveOtherActorPiAndTimelineRequest_PiIdentifier()
}

type ResolveOtherActorPiAndTimelineRequest_Vpa struct {
	// in case of upi transactions, vpa of the other actor
	Vpa string `protobuf:"bytes,6,opt,name=vpa,proto3,oneof"`
}

type ResolveOtherActorPiAndTimelineRequest_Account struct {
	// in case of non-upi account details of other actor
	Account *ResolveOtherActorPiAndTimelineRequest_AccountIdentifier `protobuf:"bytes,7,opt,name=account,proto3,oneof"`
}

type ResolveOtherActorPiAndTimelineRequest_PiId struct {
	// if pi id is present, other pi identifiers will be ignored
	PiId string `protobuf:"bytes,8,opt,name=pi_id,json=piId,proto3,oneof"`
}

type ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier_ struct {
	// in case of card transactions (debit/credit card) , we will require either MID or other actor name to resolve the pi
	CardTransactionIdentifier *ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier `protobuf:"bytes,9,opt,name=card_transaction_identifier,json=cardTransactionIdentifier,proto3,oneof"`
}

func (*ResolveOtherActorPiAndTimelineRequest_Vpa) isResolveOtherActorPiAndTimelineRequest_PiIdentifier() {
}

func (*ResolveOtherActorPiAndTimelineRequest_Account) isResolveOtherActorPiAndTimelineRequest_PiIdentifier() {
}

func (*ResolveOtherActorPiAndTimelineRequest_PiId) isResolveOtherActorPiAndTimelineRequest_PiIdentifier() {
}

func (*ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier_) isResolveOtherActorPiAndTimelineRequest_PiIdentifier() {
}

type ResolveOtherActorPiAndTimelineResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// other actor pi id
	OtherActorPiId string `protobuf:"bytes,2,opt,name=other_actor_pi_id,json=otherActorPiId,proto3" json:"other_actor_pi_id,omitempty"`
	// timeline id resolved between 2 actors
	TimelineId string `protobuf:"bytes,3,opt,name=timeline_id,json=timelineId,proto3" json:"timeline_id,omitempty"`
}

func (x *ResolveOtherActorPiAndTimelineResponse) Reset() {
	*x = ResolveOtherActorPiAndTimelineResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResolveOtherActorPiAndTimelineResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveOtherActorPiAndTimelineResponse) ProtoMessage() {}

func (x *ResolveOtherActorPiAndTimelineResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveOtherActorPiAndTimelineResponse.ProtoReflect.Descriptor instead.
func (*ResolveOtherActorPiAndTimelineResponse) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{39}
}

func (x *ResolveOtherActorPiAndTimelineResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ResolveOtherActorPiAndTimelineResponse) GetOtherActorPiId() string {
	if x != nil {
		return x.OtherActorPiId
	}
	return ""
}

func (x *ResolveOtherActorPiAndTimelineResponse) GetTimelineId() string {
	if x != nil {
		return x.TimelineId
	}
	return ""
}

// merchant details
type MerchantDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// merchant location (can be empty)
	Location string `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
	// mcc code of the merchant
	MccCode string `protobuf:"bytes,3,opt,name=mcc_code,json=mccCode,proto3" json:"mcc_code,omitempty"`
	// geo code of the merchant (can be empty)
	GeoCode string `protobuf:"bytes,4,opt,name=geo_code,json=geoCode,proto3" json:"geo_code,omitempty"`
	// required for populating the address of the merchant
	// city in which transaction was done
	City string `protobuf:"bytes,5,opt,name=city,proto3" json:"city,omitempty"`
	// alpha-2 country code for the country in which transaction was made
	CountryCode string `protobuf:"bytes,6,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// 15 digit identification number to uniquely identify the merchant
	Mid string `protobuf:"bytes,7,opt,name=mid,proto3" json:"mid,omitempty"`
	// terminal id of the Merchant
	Tid string `protobuf:"bytes,8,opt,name=tid,proto3" json:"tid,omitempty"`
}

func (x *MerchantDetails) Reset() {
	*x = MerchantDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerchantDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantDetails) ProtoMessage() {}

func (x *MerchantDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantDetails.ProtoReflect.Descriptor instead.
func (*MerchantDetails) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{40}
}

func (x *MerchantDetails) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MerchantDetails) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *MerchantDetails) GetMccCode() string {
	if x != nil {
		return x.MccCode
	}
	return ""
}

func (x *MerchantDetails) GetGeoCode() string {
	if x != nil {
		return x.GeoCode
	}
	return ""
}

func (x *MerchantDetails) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *MerchantDetails) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *MerchantDetails) GetMid() string {
	if x != nil {
		return x.Mid
	}
	return ""
}

func (x *MerchantDetails) GetTid() string {
	if x != nil {
		return x.Tid
	}
	return ""
}

type GetEntityDetailsByActorIdResponse_Merchant struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Capabilities map[string]bool `protobuf:"bytes,1,rep,name=capabilities,proto3" json:"capabilities,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *GetEntityDetailsByActorIdResponse_Merchant) Reset() {
	*x = GetEntityDetailsByActorIdResponse_Merchant{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEntityDetailsByActorIdResponse_Merchant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntityDetailsByActorIdResponse_Merchant) ProtoMessage() {}

func (x *GetEntityDetailsByActorIdResponse_Merchant) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntityDetailsByActorIdResponse_Merchant.ProtoReflect.Descriptor instead.
func (*GetEntityDetailsByActorIdResponse_Merchant) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{17, 0}
}

func (x *GetEntityDetailsByActorIdResponse_Merchant) GetCapabilities() map[string]bool {
	if x != nil {
		return x.Capabilities
	}
	return nil
}

type GetEntityDetailsResponse_EntityDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User ID or Merchant ID based on the type of actor
	// In case of external actors, this will be empty
	EntityId string `protobuf:"bytes,1,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	// name of the entity (user/merchant)
	Name *common.Name `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// mobile number of the entity (user/merchant)
	MobileNumber *common.PhoneNumber `protobuf:"bytes,3,opt,name=mobile_number,json=mobileNumber,proto3" json:"mobile_number,omitempty"`
	// url profile image of the entity (user/merchant)
	ProfileImageUrl string `protobuf:"bytes,4,opt,name=profile_image_url,json=profileImageUrl,proto3" json:"profile_image_url,omitempty"`
	// email id of the entity (user/merchant)
	EmailId string `protobuf:"bytes,5,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	// actor id passed in the request
	ActorId string `protobuf:"bytes,6,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Idea is to add entities separately and let the caller take decisions based on type of actor
	// More entities like external user, user, internal merchant can be added need basis
	//
	// Types that are assignable to Entity:
	//
	//	*GetEntityDetailsResponse_EntityDetail_Merchant_
	Entity     isGetEntityDetailsResponse_EntityDetail_Entity `protobuf_oneof:"entity"`
	EntityType typesv2.ActorType                              `protobuf:"varint,8,opt,name=entity_type,json=entityType,proto3,enum=api.typesv2.ActorType" json:"entity_type,omitempty"`
}

func (x *GetEntityDetailsResponse_EntityDetail) Reset() {
	*x = GetEntityDetailsResponse_EntityDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEntityDetailsResponse_EntityDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntityDetailsResponse_EntityDetail) ProtoMessage() {}

func (x *GetEntityDetailsResponse_EntityDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntityDetailsResponse_EntityDetail.ProtoReflect.Descriptor instead.
func (*GetEntityDetailsResponse_EntityDetail) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{29, 0}
}

func (x *GetEntityDetailsResponse_EntityDetail) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

func (x *GetEntityDetailsResponse_EntityDetail) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *GetEntityDetailsResponse_EntityDetail) GetMobileNumber() *common.PhoneNumber {
	if x != nil {
		return x.MobileNumber
	}
	return nil
}

func (x *GetEntityDetailsResponse_EntityDetail) GetProfileImageUrl() string {
	if x != nil {
		return x.ProfileImageUrl
	}
	return ""
}

func (x *GetEntityDetailsResponse_EntityDetail) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *GetEntityDetailsResponse_EntityDetail) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (m *GetEntityDetailsResponse_EntityDetail) GetEntity() isGetEntityDetailsResponse_EntityDetail_Entity {
	if m != nil {
		return m.Entity
	}
	return nil
}

func (x *GetEntityDetailsResponse_EntityDetail) GetMerchant() *GetEntityDetailsResponse_EntityDetail_Merchant {
	if x, ok := x.GetEntity().(*GetEntityDetailsResponse_EntityDetail_Merchant_); ok {
		return x.Merchant
	}
	return nil
}

func (x *GetEntityDetailsResponse_EntityDetail) GetEntityType() typesv2.ActorType {
	if x != nil {
		return x.EntityType
	}
	return typesv2.ActorType(0)
}

type isGetEntityDetailsResponse_EntityDetail_Entity interface {
	isGetEntityDetailsResponse_EntityDetail_Entity()
}

type GetEntityDetailsResponse_EntityDetail_Merchant_ struct {
	Merchant *GetEntityDetailsResponse_EntityDetail_Merchant `protobuf:"bytes,7,opt,name=merchant,proto3,oneof"`
}

func (*GetEntityDetailsResponse_EntityDetail_Merchant_) isGetEntityDetailsResponse_EntityDetail_Entity() {
}

type GetEntityDetailsResponse_EntityDetail_Merchant struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Capabilities map[string]bool `protobuf:"bytes,1,rep,name=capabilities,proto3" json:"capabilities,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *GetEntityDetailsResponse_EntityDetail_Merchant) Reset() {
	*x = GetEntityDetailsResponse_EntityDetail_Merchant{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEntityDetailsResponse_EntityDetail_Merchant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntityDetailsResponse_EntityDetail_Merchant) ProtoMessage() {}

func (x *GetEntityDetailsResponse_EntityDetail_Merchant) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntityDetailsResponse_EntityDetail_Merchant.ProtoReflect.Descriptor instead.
func (*GetEntityDetailsResponse_EntityDetail_Merchant) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{29, 0, 0}
}

func (x *GetEntityDetailsResponse_EntityDetail_Merchant) GetCapabilities() map[string]bool {
	if x != nil {
		return x.Capabilities
	}
	return nil
}

// set of fields to uniquely identify an account
type ResolveOtherActorPiAndTimelineRequest_AccountIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountNumber string `protobuf:"bytes,1,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	IfscCode      string `protobuf:"bytes,2,opt,name=ifsc_code,json=ifscCode,proto3" json:"ifsc_code,omitempty"`
	// type of the pi for eg. BANK_ACCOUNT, PARTIAL_BANK_ACCOUNT, GENERIC
	PiType paymentinstrument.PaymentInstrumentType `protobuf:"varint,3,opt,name=pi_type,json=piType,proto3,enum=paymentinstrument.PaymentInstrumentType" json:"pi_type,omitempty"`
	// Type of the account
	AccountType accounts.Type `protobuf:"varint,4,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
}

func (x *ResolveOtherActorPiAndTimelineRequest_AccountIdentifier) Reset() {
	*x = ResolveOtherActorPiAndTimelineRequest_AccountIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResolveOtherActorPiAndTimelineRequest_AccountIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveOtherActorPiAndTimelineRequest_AccountIdentifier) ProtoMessage() {}

func (x *ResolveOtherActorPiAndTimelineRequest_AccountIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveOtherActorPiAndTimelineRequest_AccountIdentifier.ProtoReflect.Descriptor instead.
func (*ResolveOtherActorPiAndTimelineRequest_AccountIdentifier) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{38, 0}
}

func (x *ResolveOtherActorPiAndTimelineRequest_AccountIdentifier) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *ResolveOtherActorPiAndTimelineRequest_AccountIdentifier) GetIfscCode() string {
	if x != nil {
		return x.IfscCode
	}
	return ""
}

func (x *ResolveOtherActorPiAndTimelineRequest_AccountIdentifier) GetPiType() paymentinstrument.PaymentInstrumentType {
	if x != nil {
		return x.PiType
	}
	return paymentinstrument.PaymentInstrumentType(0)
}

func (x *ResolveOtherActorPiAndTimelineRequest_AccountIdentifier) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

type ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 15 digit identification number to uniquely identify the merchant
	Mid            string `protobuf:"bytes,1,opt,name=mid,proto3" json:"mid,omitempty"`
	OtherActorName string `protobuf:"bytes,2,opt,name=other_actor_name,json=otherActorName,proto3" json:"other_actor_name,omitempty"`
}

func (x *ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier) Reset() {
	*x = ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_actor_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier) ProtoMessage() {}

func (x *ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_actor_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier.ProtoReflect.Descriptor instead.
func (*ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier) Descriptor() ([]byte, []int) {
	return file_api_actor_service_proto_rawDescGZIP(), []int{38, 1}
}

func (x *ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier) GetMid() string {
	if x != nil {
		return x.Mid
	}
	return ""
}

func (x *ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier) GetOtherActorName() string {
	if x != nil {
		return x.OtherActorName
	}
	return ""
}

var File_api_actor_service_proto protoreflect.FileDescriptor

var file_api_actor_service_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x70, 0x69, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x65, 0x63, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x63, 0x68,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28,
	0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61,
	0x79, 0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63,
	0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6b, 0x0a,
	0x1a, 0x42, 0x75, 0x6c, 0x6b, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x54,
	0x6f, 0x50, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x46,
	0x72, 0x6f, 0x6d, 0x12, 0x25, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x6f, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01, 0x18,
	0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x22, 0xb9, 0x02, 0x0a, 0x1b, 0x42,
	0x75, 0x6c, 0x6b, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x50,
	0x49, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x60, 0x0a, 0x10, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x70, 0x69, 0x5f, 0x6d, 0x61, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x54, 0x6f, 0x50, 0x49, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x50, 0x69, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x69, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x1a, 0x5d, 0x0a, 0x13, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x69, 0x4d, 0x61, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d,
	0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x22, 0xb9, 0x01, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x22, 0x76, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28,
	0x0a, 0x05, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x05, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x22, 0x10, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x22, 0x25, 0x0a, 0x13, 0x47, 0x65,
	0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x77, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x79, 0x49,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28,
	0x0a, 0x05, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x05, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x22, 0x10, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x22, 0x65, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49,
	0x64, 0x22, 0x7d, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x79, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x05, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x05, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x22, 0x10,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x22, 0x9a, 0x01, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x50, 0x69, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x66, 0x72, 0x6f,
	0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x09, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x22, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x12,
	0x17, 0x0a, 0x07, 0x70, 0x69, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x70, 0x69, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x13, 0x0a, 0x05, 0x70, 0x69, 0x5f, 0x74,
	0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x69, 0x54, 0x6f, 0x22, 0xa2, 0x01,
	0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x69, 0x52,
	0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x13, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x70, 0x69, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x50, 0x69, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x50, 0x69, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x10, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x22, 0xc7, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x54, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x46, 0x72, 0x6f, 0x6d, 0x12, 0x1c, 0x0a, 0x05, 0x70, 0x69, 0x5f, 0x74, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x70, 0x69,
	0x54, 0x6f, 0x12, 0x2b, 0x0a, 0x0d, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x6f, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x3b, 0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x22, 0x6a, 0x0a, 0x16,
	0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x22, 0x10, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x22, 0xcd, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x73,
	0x6f, 0x6c, 0x76, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x12, 0x20, 0x0a, 0x07, 0x70, 0x69, 0x5f, 0x66,
	0x72, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x06, 0x70, 0x69, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x2f, 0x0a, 0x0f, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x46, 0x72, 0x6f, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x09, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x52, 0x09, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x22, 0x7e, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x6f,
	0x6c, 0x76, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x46, 0x72, 0x6f, 0x6d, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x7a, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50,
	0x49, 0x73, 0x4f, 0x66, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x66, 0x72, 0x6f, 0x6d,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x09, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x22, 0x67, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x49, 0x73, 0x4f, 0x66,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x69, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x70, 0x69, 0x49, 0x64, 0x73, 0x22, 0x10, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x22, 0x3d, 0x0a,
	0x20, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x42, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xe3, 0x05, 0x0a,
	0x21, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x42, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x44, 0x0a, 0x0d, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0c, 0x6d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12,
	0x4f, 0x0a, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x79, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x12, 0x37, 0x0a, 0x0a, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x09,
	0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x1a, 0xb4, 0x01, 0x0a, 0x08, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x12, 0x67, 0x0a, 0x0c, 0x63, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x42, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x70, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x63,
	0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x1a, 0x3f, 0x0a, 0x11, 0x43,
	0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x4a, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14,
	0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45,
	0x4e, 0x54, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x42, 0x08, 0x0a, 0x06, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x22, 0x8e, 0x01, 0x0a, 0x11, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0e, 0x6f,
	0x74, 0x68, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x74,
	0x68, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73,
	0x5f, 0x73, 0x70, 0x61, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x53,
	0x70, 0x61, 0x6d, 0x22, 0x86, 0x01, 0x0a, 0x12, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x4b, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47,
	0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59,
	0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x10, 0x32, 0x22, 0x77, 0x0a, 0x13,
	0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x88, 0x01, 0x0a, 0x14, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x4b, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x4c, 0x52,
	0x45, 0x41, 0x44, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x10, 0x32,
	0x22, 0x88, 0x01, 0x0a, 0x20, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x70, 0x61, 0x6d, 0x46,
	0x6f, 0x72, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x65, 0x64, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xac, 0x01, 0x0a, 0x21,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x70, 0x61, 0x6d, 0x46, 0x6f, 0x72, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x65, 0x64, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x62, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x15, 0x0a, 0x11,
	0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45,
	0x44, 0x10, 0x32, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10, 0x64, 0x22, 0x83, 0x01, 0x0a, 0x1f, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x57, 0x69,
	0x74, 0x68, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31,
	0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x2d, 0x0a, 0x0e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x22, 0xad, 0x02, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x68, 0x69, 0x70, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x58, 0x0a, 0x0c, 0x72, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x34, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x68, 0x69, 0x70, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x54, 0x0a, 0x0c, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45,
	0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x4f, 0x54, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44,
	0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x10, 0x03,
	0x22, 0x3c, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x42, 0x79, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x73, 0x22, 0xa5,
	0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x42, 0x79, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x06, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73,
	0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d,
	0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x22, 0x6f, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x73, 0x12, 0x37,
	0x0a, 0x18, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x6f, 0x66, 0x74, 0x5f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x15, 0x66, 0x65, 0x74, 0x63, 0x68, 0x53, 0x6f, 0x66, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x73, 0x22, 0x8b, 0x06, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x53, 0x0a, 0x0e, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x0d, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0xd4,
	0x04, 0x0a, 0x0c, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x0d, 0x6d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x0c, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x53, 0x0a, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x08, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x1a, 0xb8, 0x01, 0x0a, 0x08, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x12, 0x6b, 0x0a,
	0x0c, 0x63, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x63, 0x61,
	0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x1a, 0x3f, 0x0a, 0x11, 0x43, 0x61,
	0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x2f, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x3a, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x3a, 0x0a, 0x1b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x48, 0x61, 0x72, 0x64, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x73, 0x22, 0x7a,
	0x0a, 0x1c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x48, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x35, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0x0d, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x50,
	0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x10, 0x32, 0x22, 0x46, 0x0a, 0x27, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x48, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x50, 0x69, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x73, 0x22, 0x86, 0x01, 0x0a, 0x28, 0x42, 0x61, 0x74, 0x63, 0x68, 0x48, 0x61, 0x72, 0x64,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x69, 0x52, 0x65, 0x73,
	0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x35, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x0d, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f,
	0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x10, 0x32, 0x22, 0x38, 0x0a, 0x1b, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xf9, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x43, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x70, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x1a, 0x38, 0x0a, 0x0a, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x34, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x52,
	0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10,
	0x05, 0x22, 0xcf, 0x0b, 0x0a, 0x25, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x4f, 0x74, 0x68,
	0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x69, 0x41, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x70,
	0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1e, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52,
	0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x12, 0x4d, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x20, 0x00, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x31, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x38, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61,
	0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52,
	0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x12, 0x0a, 0x03,
	0x76, 0x70, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x76, 0x70, 0x61,
	0x12, 0x5a, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3e, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76,
	0x65, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x69, 0x41, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x15, 0x0a, 0x05,
	0x70, 0x69, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x70,
	0x69, 0x49, 0x64, 0x12, 0x88, 0x01, 0x0a, 0x1b, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x50, 0x69, 0x41, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x48, 0x00, 0x52, 0x19, 0x63, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x3f,
	0x0a, 0x0c, 0x70, 0x69, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x69, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x52, 0x0b, 0x70, 0x69, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x12,
	0x42, 0x0a, 0x12, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x74, 0x69,
	0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x52, 0x11, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x12, 0x41, 0x0a, 0x10, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x54, 0x0a, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21,
	0x2e, 0x70, 0x61, 0x79, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x52, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x74, 0x6d, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x74, 0x6d,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x60, 0x0a, 0x1a, 0x74, 0x69, 0x6d, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x74, 0x69,
	0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52,
	0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x18, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x5e, 0x0a, 0x11, 0x65, 0x63, 0x73,
	0x5f, 0x65, 0x6e, 0x61, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x45, 0x63, 0x73, 0x45, 0x6e, 0x61, 0x63, 0x68, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x65, 0x63, 0x73, 0x45, 0x6e, 0x61,
	0x63, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0xcd, 0x01, 0x0a, 0x11, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12,
	0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x66, 0x73, 0x63, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x66, 0x73, 0x63, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x41, 0x0a, 0x07, 0x70, 0x69, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x69, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06,
	0x70, 0x69, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x57, 0x0a, 0x19, 0x43, 0x61, 0x72,
	0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x70, 0x69, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x22, 0xcf, 0x01, 0x0a, 0x26, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x4f,
	0x74, 0x68, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x69, 0x41, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x11, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x70, 0x69, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x6f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x69, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x22,
	0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47,
	0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xd2, 0x01, 0x0a, 0x0f, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x63, 0x63,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x63, 0x63,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x65, 0x6f, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x65, 0x6f, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x69, 0x74, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x69, 0x64, 0x32, 0xe9, 0x0e, 0x0a, 0x05, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x12, 0x46, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x12, 0x19, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a,
	0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x0c,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x79, 0x49, 0x64, 0x12, 0x1a, 0x2e, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x79, 0x49,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x42, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x20, 0x2e,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x42, 0x79,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x42, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x6a, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x50, 0x69, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x25, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x50, 0x69, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x69, 0x52, 0x65, 0x73, 0x6f,
	0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x4f, 0x0a, 0x0e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x54, 0x6f, 0x12, 0x1c, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c,
	0x76, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1d, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x55, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x1e, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x6f, 0x6c, 0x76, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x6f, 0x6c, 0x76, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x52, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x50,
	0x49, 0x73, 0x4f, 0x66, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x12, 0x1d, 0x2e, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x49, 0x73, 0x4f, 0x66, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x54, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x49, 0x73, 0x4f, 0x66, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x54, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x19,
	0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x42, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x27, 0x2e, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x42, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x79, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x43,
	0x0a, 0x0a, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x2e, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x0c, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x12, 0x1a, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x55, 0x6e, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x55, 0x6e, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x70,
	0x0a, 0x19, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x70, 0x61, 0x6d, 0x46, 0x6f, 0x72, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x27, 0x2e, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x70, 0x61, 0x6d, 0x46, 0x6f,
	0x72, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x53, 0x70, 0x61, 0x6d, 0x46, 0x6f, 0x72, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x65,
	0x64, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x6d, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x68, 0x69, 0x70, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x26, 0x2e, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x68, 0x69, 0x70, 0x57, 0x69, 0x74, 0x68, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x57, 0x69, 0x74, 0x68,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x55, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x1e, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x19, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1a, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a, 0x14,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x48, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x12, 0x22, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x48, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x48, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x83, 0x01,
	0x0a, 0x20, 0x42, 0x61, 0x74, 0x63, 0x68, 0x48, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x69, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2e, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x48, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50,
	0x69, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x48, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50,
	0x69, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x43,
	0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x22, 0x2e, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x70, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x23, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x43, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x73, 0x42, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x73, 0x12, 0x22, 0x2e, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x42, 0x79,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x23, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x73, 0x42, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x13, 0x42, 0x75, 0x6c, 0x6b, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x50, 0x49, 0x73, 0x12, 0x21, 0x2e, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x54, 0x6f, 0x50, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x22, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x42, 0x75, 0x6c, 0x6b, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x50, 0x49, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7f, 0x0a, 0x1e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65,
	0x4f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x69, 0x41, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x2c, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x50, 0x69, 0x41, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x6f, 0x6c, 0x76, 0x65, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50,
	0x69, 0x41, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x44, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5a, 0x20, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_actor_service_proto_rawDescOnce sync.Once
	file_api_actor_service_proto_rawDescData = file_api_actor_service_proto_rawDesc
)

func file_api_actor_service_proto_rawDescGZIP() []byte {
	file_api_actor_service_proto_rawDescOnce.Do(func() {
		file_api_actor_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_actor_service_proto_rawDescData)
	})
	return file_api_actor_service_proto_rawDescData
}

var file_api_actor_service_proto_enumTypes = make([]protoimpl.EnumInfo, 20)
var file_api_actor_service_proto_msgTypes = make([]protoimpl.MessageInfo, 50)
var file_api_actor_service_proto_goTypes = []interface{}{
	(BulkFetchActorToPIsResponse_Status)(0),              // 0: actor.BulkFetchActorToPIsResponse.Status
	(CreateActorResponse_Status)(0),                      // 1: actor.CreateActorResponse.Status
	(GetActorByIdResponse_Status)(0),                     // 2: actor.GetActorByIdResponse.Status
	(GetActorByEntityIdResponse_Status)(0),               // 3: actor.GetActorByEntityIdResponse.Status
	(CreateActorPiResolutionResponse_Status)(0),          // 4: actor.CreateActorPiResolutionResponse.Status
	(ResolveActorToResponse_Status)(0),                   // 5: actor.ResolveActorToResponse.Status
	(ResolveActorFromResponse_Status)(0),                 // 6: actor.ResolveActorFromResponse.Status
	(GetPIsOfActorToResponse_Status)(0),                  // 7: actor.GetPIsOfActorToResponse.Status
	(GetEntityDetailsByActorIdResponse_Status)(0),        // 8: actor.GetEntityDetailsByActorIdResponse.Status
	(BlockActorResponse_Status)(0),                       // 9: actor.BlockActorResponse.Status
	(UnblockActorResponse_Status)(0),                     // 10: actor.UnblockActorResponse.Status
	(ReportSpamForBlockedActorResponse_Status)(0),        // 11: actor.ReportSpamForBlockedActorResponse.Status
	(GetRelationshipWithActorResponse_Status)(0),         // 12: actor.GetRelationshipWithActorResponse.Status
	(GetRelationshipWithActorResponse_Relationship)(0),   // 13: actor.GetRelationshipWithActorResponse.Relationship
	(GetActorsByEntityIdsResponse_Status)(0),             // 14: actor.GetActorsByEntityIdsResponse.Status
	(GetEntityDetailsResponse_Status)(0),                 // 15: actor.GetEntityDetailsResponse.Status
	(BatchHardDeleteActorResponse_Status)(0),             // 16: actor.BatchHardDeleteActorResponse.Status
	(BatchHardDeleteActorPiResolutionResponse_Status)(0), // 17: actor.BatchHardDeleteActorPiResolutionResponse.Status
	(GetActorCapabilitiesResponse_Status)(0),             // 18: actor.GetActorCapabilitiesResponse.Status
	(ResolveOtherActorPiAndTimelineResponse_Status)(0),   // 19: actor.ResolveOtherActorPiAndTimelineResponse.Status
	(*BulkFetchActorToPIsRequest)(nil),                   // 20: actor.BulkFetchActorToPIsRequest
	(*BulkFetchActorToPIsResponse)(nil),                  // 21: actor.BulkFetchActorToPIsResponse
	(*CreateActorRequest)(nil),                           // 22: actor.CreateActorRequest
	(*CreateActorResponse)(nil),                          // 23: actor.CreateActorResponse
	(*GetActorByIdRequest)(nil),                          // 24: actor.GetActorByIdRequest
	(*GetActorByIdResponse)(nil),                         // 25: actor.GetActorByIdResponse
	(*GetActorByEntityIdRequest)(nil),                    // 26: actor.GetActorByEntityIdRequest
	(*GetActorByEntityIdResponse)(nil),                   // 27: actor.GetActorByEntityIdResponse
	(*CreateActorPiResolutionRequest)(nil),               // 28: actor.CreateActorPiResolutionRequest
	(*CreateActorPiResolutionResponse)(nil),              // 29: actor.CreateActorPiResolutionResponse
	(*ResolveActorToRequest)(nil),                        // 30: actor.ResolveActorToRequest
	(*ResolveActorToResponse)(nil),                       // 31: actor.ResolveActorToResponse
	(*ResolveActorFromRequest)(nil),                      // 32: actor.ResolveActorFromRequest
	(*ResolveActorFromResponse)(nil),                     // 33: actor.ResolveActorFromResponse
	(*GetPIsOfActorToRequest)(nil),                       // 34: actor.GetPIsOfActorToRequest
	(*GetPIsOfActorToResponse)(nil),                      // 35: actor.GetPIsOfActorToResponse
	(*GetEntityDetailsByActorIdRequest)(nil),             // 36: actor.GetEntityDetailsByActorIdRequest
	(*GetEntityDetailsByActorIdResponse)(nil),            // 37: actor.GetEntityDetailsByActorIdResponse
	(*BlockActorRequest)(nil),                            // 38: actor.BlockActorRequest
	(*BlockActorResponse)(nil),                           // 39: actor.BlockActorResponse
	(*UnblockActorRequest)(nil),                          // 40: actor.UnblockActorRequest
	(*UnblockActorResponse)(nil),                         // 41: actor.UnblockActorResponse
	(*ReportSpamForBlockedActorRequest)(nil),             // 42: actor.ReportSpamForBlockedActorRequest
	(*ReportSpamForBlockedActorResponse)(nil),            // 43: actor.ReportSpamForBlockedActorResponse
	(*GetRelationshipWithActorRequest)(nil),              // 44: actor.GetRelationshipWithActorRequest
	(*GetRelationshipWithActorResponse)(nil),             // 45: actor.GetRelationshipWithActorResponse
	(*GetActorsByEntityIdsRequest)(nil),                  // 46: actor.GetActorsByEntityIdsRequest
	(*GetActorsByEntityIdsResponse)(nil),                 // 47: actor.GetActorsByEntityIdsResponse
	(*GetEntityDetailsRequest)(nil),                      // 48: actor.GetEntityDetailsRequest
	(*GetEntityDetailsResponse)(nil),                     // 49: actor.GetEntityDetailsResponse
	(*DeleteActorRequest)(nil),                           // 50: actor.DeleteActorRequest
	(*DeleteActorResponse)(nil),                          // 51: actor.DeleteActorResponse
	(*BatchHardDeleteActorRequest)(nil),                  // 52: actor.BatchHardDeleteActorRequest
	(*BatchHardDeleteActorResponse)(nil),                 // 53: actor.BatchHardDeleteActorResponse
	(*BatchHardDeleteActorPiResolutionRequest)(nil),      // 54: actor.BatchHardDeleteActorPiResolutionRequest
	(*BatchHardDeleteActorPiResolutionResponse)(nil),     // 55: actor.BatchHardDeleteActorPiResolutionResponse
	(*GetActorCapabilitiesRequest)(nil),                  // 56: actor.GetActorCapabilitiesRequest
	(*GetActorCapabilitiesResponse)(nil),                 // 57: actor.GetActorCapabilitiesResponse
	(*ResolveOtherActorPiAndTimelineRequest)(nil),        // 58: actor.ResolveOtherActorPiAndTimelineRequest
	(*ResolveOtherActorPiAndTimelineResponse)(nil),       // 59: actor.ResolveOtherActorPiAndTimelineResponse
	(*MerchantDetails)(nil),                              // 60: actor.MerchantDetails
	nil,                                                  // 61: actor.BulkFetchActorToPIsResponse.ActorPiMappingEntry
	(*GetEntityDetailsByActorIdResponse_Merchant)(nil),   // 62: actor.GetEntityDetailsByActorIdResponse.Merchant
	nil, // 63: actor.GetEntityDetailsByActorIdResponse.Merchant.CapabilitiesEntry
	(*GetEntityDetailsResponse_EntityDetail)(nil),          // 64: actor.GetEntityDetailsResponse.EntityDetail
	(*GetEntityDetailsResponse_EntityDetail_Merchant)(nil), // 65: actor.GetEntityDetailsResponse.EntityDetail.Merchant
	nil, // 66: actor.GetEntityDetailsResponse.EntityDetail.Merchant.CapabilitiesEntry
	nil, // 67: actor.GetActorCapabilitiesResponse.ValueEntry
	(*ResolveOtherActorPiAndTimelineRequest_AccountIdentifier)(nil),         // 68: actor.ResolveOtherActorPiAndTimelineRequest.AccountIdentifier
	(*ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier)(nil), // 69: actor.ResolveOtherActorPiAndTimelineRequest.CardTransactionIdentifier
	(*rpc.Status)(nil),                           // 70: rpc.Status
	(typesv2.Actor_Type)(0),                      // 71: api.typesv2.Actor.Type
	(common.Ownership)(0),                        // 72: api.typesv2.common.Ownership
	(*typesv2.Actor)(nil),                        // 73: api.typesv2.Actor
	(*ActorPiResolution)(nil),                    // 74: actor.ActorPiResolution
	(*common.Name)(nil),                          // 75: api.typesv2.common.Name
	(*common.PhoneNumber)(nil),                   // 76: api.typesv2.common.PhoneNumber
	(typesv2.ActorType)(0),                       // 77: api.typesv2.ActorType
	(payment.PaymentProtocol)(0),                 // 78: order.payment.PaymentProtocol
	(typesv2.TransactionTransferType)(0),         // 79: api.typesv2.TransactionTransferType
	(accounts.Type)(0),                           // 80: accounts.Type
	(vendorgateway.Vendor)(0),                    // 81: vendorgateway.Vendor
	(paymentinstrument.Ownership)(0),             // 82: paymentinstrument.Ownership
	(timeline.Ownership)(0),                      // 83: timeline.Ownership
	(category.TransactionCategory)(0),            // 84: pay.category.TransactionCategory
	(timeline.TimelineResolutionSource)(0),       // 85: timeline.TimelineResolutionSource
	(*notification.EcsEnachMandateDetails)(nil),  // 86: order.payment.notification.EcsEnachMandateDetails
	(*structpb.ListValue)(nil),                   // 87: google.protobuf.ListValue
	(paymentinstrument.PaymentInstrumentType)(0), // 88: paymentinstrument.PaymentInstrumentType
}
var file_api_actor_service_proto_depIdxs = []int32{
	70, // 0: actor.BulkFetchActorToPIsResponse.status:type_name -> rpc.Status
	61, // 1: actor.BulkFetchActorToPIsResponse.actor_pi_mapping:type_name -> actor.BulkFetchActorToPIsResponse.ActorPiMappingEntry
	71, // 2: actor.CreateActorRequest.type:type_name -> api.typesv2.Actor.Type
	72, // 3: actor.CreateActorRequest.ownership:type_name -> api.typesv2.common.Ownership
	70, // 4: actor.CreateActorResponse.status:type_name -> rpc.Status
	73, // 5: actor.CreateActorResponse.actor:type_name -> api.typesv2.Actor
	70, // 6: actor.GetActorByIdResponse.status:type_name -> rpc.Status
	73, // 7: actor.GetActorByIdResponse.actor:type_name -> api.typesv2.Actor
	71, // 8: actor.GetActorByEntityIdRequest.type:type_name -> api.typesv2.Actor.Type
	70, // 9: actor.GetActorByEntityIdResponse.status:type_name -> rpc.Status
	73, // 10: actor.GetActorByEntityIdResponse.actor:type_name -> api.typesv2.Actor
	70, // 11: actor.CreateActorPiResolutionResponse.status:type_name -> rpc.Status
	74, // 12: actor.CreateActorPiResolutionResponse.actor_pi_resolution:type_name -> actor.ActorPiResolution
	72, // 13: actor.ResolveActorToRequest.ownership:type_name -> api.typesv2.common.Ownership
	70, // 14: actor.ResolveActorToResponse.status:type_name -> rpc.Status
	72, // 15: actor.ResolveActorFromRequest.ownership:type_name -> api.typesv2.common.Ownership
	70, // 16: actor.ResolveActorFromResponse.status:type_name -> rpc.Status
	70, // 17: actor.GetPIsOfActorToResponse.status:type_name -> rpc.Status
	70, // 18: actor.GetEntityDetailsByActorIdResponse.status:type_name -> rpc.Status
	75, // 19: actor.GetEntityDetailsByActorIdResponse.name:type_name -> api.typesv2.common.Name
	76, // 20: actor.GetEntityDetailsByActorIdResponse.mobile_number:type_name -> api.typesv2.common.PhoneNumber
	62, // 21: actor.GetEntityDetailsByActorIdResponse.merchant:type_name -> actor.GetEntityDetailsByActorIdResponse.Merchant
	75, // 22: actor.GetEntityDetailsByActorIdResponse.legal_name:type_name -> api.typesv2.common.Name
	77, // 23: actor.GetEntityDetailsByActorIdResponse.type:type_name -> api.typesv2.ActorType
	70, // 24: actor.BlockActorResponse.status:type_name -> rpc.Status
	70, // 25: actor.UnblockActorResponse.status:type_name -> rpc.Status
	70, // 26: actor.ReportSpamForBlockedActorResponse.status:type_name -> rpc.Status
	70, // 27: actor.GetRelationshipWithActorResponse.status:type_name -> rpc.Status
	13, // 28: actor.GetRelationshipWithActorResponse.relationship:type_name -> actor.GetRelationshipWithActorResponse.Relationship
	70, // 29: actor.GetActorsByEntityIdsResponse.status:type_name -> rpc.Status
	73, // 30: actor.GetActorsByEntityIdsResponse.actors:type_name -> api.typesv2.Actor
	70, // 31: actor.GetEntityDetailsResponse.status:type_name -> rpc.Status
	64, // 32: actor.GetEntityDetailsResponse.entity_details:type_name -> actor.GetEntityDetailsResponse.EntityDetail
	70, // 33: actor.DeleteActorResponse.status:type_name -> rpc.Status
	70, // 34: actor.BatchHardDeleteActorResponse.status:type_name -> rpc.Status
	70, // 35: actor.BatchHardDeleteActorPiResolutionResponse.status:type_name -> rpc.Status
	70, // 36: actor.GetActorCapabilitiesResponse.status:type_name -> rpc.Status
	67, // 37: actor.GetActorCapabilitiesResponse.value:type_name -> actor.GetActorCapabilitiesResponse.ValueEntry
	78, // 38: actor.ResolveOtherActorPiAndTimelineRequest.payment_protocol:type_name -> order.payment.PaymentProtocol
	79, // 39: actor.ResolveOtherActorPiAndTimelineRequest.event_type:type_name -> api.typesv2.TransactionTransferType
	80, // 40: actor.ResolveOtherActorPiAndTimelineRequest.account_type:type_name -> accounts.Type
	81, // 41: actor.ResolveOtherActorPiAndTimelineRequest.partner_bank:type_name -> vendorgateway.Vendor
	68, // 42: actor.ResolveOtherActorPiAndTimelineRequest.account:type_name -> actor.ResolveOtherActorPiAndTimelineRequest.AccountIdentifier
	69, // 43: actor.ResolveOtherActorPiAndTimelineRequest.card_transaction_identifier:type_name -> actor.ResolveOtherActorPiAndTimelineRequest.CardTransactionIdentifier
	82, // 44: actor.ResolveOtherActorPiAndTimelineRequest.pi_ownership:type_name -> paymentinstrument.Ownership
	83, // 45: actor.ResolveOtherActorPiAndTimelineRequest.timeline_ownership:type_name -> timeline.Ownership
	60, // 46: actor.ResolveOtherActorPiAndTimelineRequest.merchant_details:type_name -> actor.MerchantDetails
	84, // 47: actor.ResolveOtherActorPiAndTimelineRequest.transaction_category:type_name -> pay.category.TransactionCategory
	85, // 48: actor.ResolveOtherActorPiAndTimelineRequest.timeline_resolution_source:type_name -> timeline.TimelineResolutionSource
	86, // 49: actor.ResolveOtherActorPiAndTimelineRequest.ecs_enach_details:type_name -> order.payment.notification.EcsEnachMandateDetails
	70, // 50: actor.ResolveOtherActorPiAndTimelineResponse.status:type_name -> rpc.Status
	87, // 51: actor.BulkFetchActorToPIsResponse.ActorPiMappingEntry.value:type_name -> google.protobuf.ListValue
	63, // 52: actor.GetEntityDetailsByActorIdResponse.Merchant.capabilities:type_name -> actor.GetEntityDetailsByActorIdResponse.Merchant.CapabilitiesEntry
	75, // 53: actor.GetEntityDetailsResponse.EntityDetail.name:type_name -> api.typesv2.common.Name
	76, // 54: actor.GetEntityDetailsResponse.EntityDetail.mobile_number:type_name -> api.typesv2.common.PhoneNumber
	65, // 55: actor.GetEntityDetailsResponse.EntityDetail.merchant:type_name -> actor.GetEntityDetailsResponse.EntityDetail.Merchant
	77, // 56: actor.GetEntityDetailsResponse.EntityDetail.entity_type:type_name -> api.typesv2.ActorType
	66, // 57: actor.GetEntityDetailsResponse.EntityDetail.Merchant.capabilities:type_name -> actor.GetEntityDetailsResponse.EntityDetail.Merchant.CapabilitiesEntry
	88, // 58: actor.ResolveOtherActorPiAndTimelineRequest.AccountIdentifier.pi_type:type_name -> paymentinstrument.PaymentInstrumentType
	80, // 59: actor.ResolveOtherActorPiAndTimelineRequest.AccountIdentifier.account_type:type_name -> accounts.Type
	22, // 60: actor.Actor.CreateActor:input_type -> actor.CreateActorRequest
	24, // 61: actor.Actor.GetActorById:input_type -> actor.GetActorByIdRequest
	26, // 62: actor.Actor.GetActorByEntityId:input_type -> actor.GetActorByEntityIdRequest
	28, // 63: actor.Actor.CreateActorPiResolution:input_type -> actor.CreateActorPiResolutionRequest
	30, // 64: actor.Actor.ResolveActorTo:input_type -> actor.ResolveActorToRequest
	32, // 65: actor.Actor.ResolveActorFrom:input_type -> actor.ResolveActorFromRequest
	34, // 66: actor.Actor.GetPIsOfActorTo:input_type -> actor.GetPIsOfActorToRequest
	36, // 67: actor.Actor.GetEntityDetailsByActorId:input_type -> actor.GetEntityDetailsByActorIdRequest
	38, // 68: actor.Actor.BlockActor:input_type -> actor.BlockActorRequest
	40, // 69: actor.Actor.UnblockActor:input_type -> actor.UnblockActorRequest
	42, // 70: actor.Actor.ReportSpamForBlockedActor:input_type -> actor.ReportSpamForBlockedActorRequest
	44, // 71: actor.Actor.GetRelationshipWithActor:input_type -> actor.GetRelationshipWithActorRequest
	48, // 72: actor.Actor.GetEntityDetails:input_type -> actor.GetEntityDetailsRequest
	50, // 73: actor.Actor.DeleteActor:input_type -> actor.DeleteActorRequest
	52, // 74: actor.Actor.BatchHardDeleteActor:input_type -> actor.BatchHardDeleteActorRequest
	54, // 75: actor.Actor.BatchHardDeleteActorPiResolution:input_type -> actor.BatchHardDeleteActorPiResolutionRequest
	56, // 76: actor.Actor.GetActorCapabilities:input_type -> actor.GetActorCapabilitiesRequest
	46, // 77: actor.Actor.GetActorsByEntityIds:input_type -> actor.GetActorsByEntityIdsRequest
	20, // 78: actor.Actor.BulkFetchActorToPIs:input_type -> actor.BulkFetchActorToPIsRequest
	58, // 79: actor.Actor.ResolveOtherActorPiAndTimeline:input_type -> actor.ResolveOtherActorPiAndTimelineRequest
	23, // 80: actor.Actor.CreateActor:output_type -> actor.CreateActorResponse
	25, // 81: actor.Actor.GetActorById:output_type -> actor.GetActorByIdResponse
	27, // 82: actor.Actor.GetActorByEntityId:output_type -> actor.GetActorByEntityIdResponse
	29, // 83: actor.Actor.CreateActorPiResolution:output_type -> actor.CreateActorPiResolutionResponse
	31, // 84: actor.Actor.ResolveActorTo:output_type -> actor.ResolveActorToResponse
	33, // 85: actor.Actor.ResolveActorFrom:output_type -> actor.ResolveActorFromResponse
	35, // 86: actor.Actor.GetPIsOfActorTo:output_type -> actor.GetPIsOfActorToResponse
	37, // 87: actor.Actor.GetEntityDetailsByActorId:output_type -> actor.GetEntityDetailsByActorIdResponse
	39, // 88: actor.Actor.BlockActor:output_type -> actor.BlockActorResponse
	41, // 89: actor.Actor.UnblockActor:output_type -> actor.UnblockActorResponse
	43, // 90: actor.Actor.ReportSpamForBlockedActor:output_type -> actor.ReportSpamForBlockedActorResponse
	45, // 91: actor.Actor.GetRelationshipWithActor:output_type -> actor.GetRelationshipWithActorResponse
	49, // 92: actor.Actor.GetEntityDetails:output_type -> actor.GetEntityDetailsResponse
	51, // 93: actor.Actor.DeleteActor:output_type -> actor.DeleteActorResponse
	53, // 94: actor.Actor.BatchHardDeleteActor:output_type -> actor.BatchHardDeleteActorResponse
	55, // 95: actor.Actor.BatchHardDeleteActorPiResolution:output_type -> actor.BatchHardDeleteActorPiResolutionResponse
	57, // 96: actor.Actor.GetActorCapabilities:output_type -> actor.GetActorCapabilitiesResponse
	47, // 97: actor.Actor.GetActorsByEntityIds:output_type -> actor.GetActorsByEntityIdsResponse
	21, // 98: actor.Actor.BulkFetchActorToPIs:output_type -> actor.BulkFetchActorToPIsResponse
	59, // 99: actor.Actor.ResolveOtherActorPiAndTimeline:output_type -> actor.ResolveOtherActorPiAndTimelineResponse
	80, // [80:100] is the sub-list for method output_type
	60, // [60:80] is the sub-list for method input_type
	60, // [60:60] is the sub-list for extension type_name
	60, // [60:60] is the sub-list for extension extendee
	0,  // [0:60] is the sub-list for field type_name
}

func init() { file_api_actor_service_proto_init() }
func file_api_actor_service_proto_init() {
	if File_api_actor_service_proto != nil {
		return
	}
	file_api_actor_actor_pi_resolution_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_actor_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkFetchActorToPIsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BulkFetchActorToPIsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActorByIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActorByIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActorByEntityIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActorByEntityIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateActorPiResolutionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateActorPiResolutionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResolveActorToRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResolveActorToResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResolveActorFromRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResolveActorFromResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPIsOfActorToRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPIsOfActorToResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEntityDetailsByActorIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEntityDetailsByActorIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnblockActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnblockActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportSpamForBlockedActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportSpamForBlockedActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRelationshipWithActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRelationshipWithActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActorsByEntityIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActorsByEntityIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEntityDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEntityDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchHardDeleteActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchHardDeleteActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchHardDeleteActorPiResolutionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchHardDeleteActorPiResolutionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActorCapabilitiesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActorCapabilitiesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResolveOtherActorPiAndTimelineRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResolveOtherActorPiAndTimelineResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MerchantDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEntityDetailsByActorIdResponse_Merchant); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEntityDetailsResponse_EntityDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEntityDetailsResponse_EntityDetail_Merchant); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResolveOtherActorPiAndTimelineRequest_AccountIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_actor_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_actor_service_proto_msgTypes[17].OneofWrappers = []interface{}{
		(*GetEntityDetailsByActorIdResponse_Merchant_)(nil),
	}
	file_api_actor_service_proto_msgTypes[38].OneofWrappers = []interface{}{
		(*ResolveOtherActorPiAndTimelineRequest_Vpa)(nil),
		(*ResolveOtherActorPiAndTimelineRequest_Account)(nil),
		(*ResolveOtherActorPiAndTimelineRequest_PiId)(nil),
		(*ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier_)(nil),
	}
	file_api_actor_service_proto_msgTypes[44].OneofWrappers = []interface{}{
		(*GetEntityDetailsResponse_EntityDetail_Merchant_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_actor_service_proto_rawDesc,
			NumEnums:      20,
			NumMessages:   50,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_actor_service_proto_goTypes,
		DependencyIndexes: file_api_actor_service_proto_depIdxs,
		EnumInfos:         file_api_actor_service_proto_enumTypes,
		MessageInfos:      file_api_actor_service_proto_msgTypes,
	}.Build()
	File_api_actor_service_proto = out.File
	file_api_actor_service_proto_rawDesc = nil
	file_api_actor_service_proto_goTypes = nil
	file_api_actor_service_proto_depIdxs = nil
}
