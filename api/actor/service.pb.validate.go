// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/actor/service.proto

package actor

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	accounts "github.com/epifi/gamma/api/accounts"

	category "github.com/epifi/gamma/api/pay/category"

	common "github.com/epifi/be-common/api/typesv2/common"

	payment "github.com/epifi/gamma/api/order/payment"

	paymentinstrument "github.com/epifi/gamma/api/paymentinstrument"

	timeline "github.com/epifi/gamma/api/timeline"

	typesv2 "github.com/epifi/gamma/api/typesv2"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = accounts.Type(0)

	_ = category.TransactionCategory(0)

	_ = common.Ownership(0)

	_ = payment.PaymentProtocol(0)

	_ = paymentinstrument.Ownership(0)

	_ = timeline.Ownership(0)

	_ = typesv2.Actor_Type(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on BulkFetchActorToPIsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BulkFetchActorToPIsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkFetchActorToPIsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BulkFetchActorToPIsRequestMultiError, or nil if none found.
func (m *BulkFetchActorToPIsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkFetchActorToPIsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorFrom()) < 1 {
		err := BulkFetchActorToPIsRequestValidationError{
			field:  "ActorFrom",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetActorTo()) < 1 {
		err := BulkFetchActorToPIsRequestValidationError{
			field:  "ActorTo",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	_BulkFetchActorToPIsRequest_ActorTo_Unique := make(map[string]struct{}, len(m.GetActorTo()))

	for idx, item := range m.GetActorTo() {
		_, _ = idx, item

		if _, exists := _BulkFetchActorToPIsRequest_ActorTo_Unique[item]; exists {
			err := BulkFetchActorToPIsRequestValidationError{
				field:  fmt.Sprintf("ActorTo[%v]", idx),
				reason: "repeated value must contain unique items",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		} else {
			_BulkFetchActorToPIsRequest_ActorTo_Unique[item] = struct{}{}
		}

		// no validation rules for ActorTo[idx]
	}

	if len(errors) > 0 {
		return BulkFetchActorToPIsRequestMultiError(errors)
	}

	return nil
}

// BulkFetchActorToPIsRequestMultiError is an error wrapping multiple
// validation errors returned by BulkFetchActorToPIsRequest.ValidateAll() if
// the designated constraints aren't met.
type BulkFetchActorToPIsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkFetchActorToPIsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkFetchActorToPIsRequestMultiError) AllErrors() []error { return m }

// BulkFetchActorToPIsRequestValidationError is the validation error returned
// by BulkFetchActorToPIsRequest.Validate if the designated constraints aren't met.
type BulkFetchActorToPIsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkFetchActorToPIsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkFetchActorToPIsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkFetchActorToPIsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkFetchActorToPIsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkFetchActorToPIsRequestValidationError) ErrorName() string {
	return "BulkFetchActorToPIsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BulkFetchActorToPIsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkFetchActorToPIsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkFetchActorToPIsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkFetchActorToPIsRequestValidationError{}

// Validate checks the field values on BulkFetchActorToPIsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BulkFetchActorToPIsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkFetchActorToPIsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BulkFetchActorToPIsResponseMultiError, or nil if none found.
func (m *BulkFetchActorToPIsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkFetchActorToPIsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BulkFetchActorToPIsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BulkFetchActorToPIsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BulkFetchActorToPIsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetActorPiMapping()))
		i := 0
		for key := range m.GetActorPiMapping() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetActorPiMapping()[key]
			_ = val

			// no validation rules for ActorPiMapping[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, BulkFetchActorToPIsResponseValidationError{
							field:  fmt.Sprintf("ActorPiMapping[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, BulkFetchActorToPIsResponseValidationError{
							field:  fmt.Sprintf("ActorPiMapping[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return BulkFetchActorToPIsResponseValidationError{
						field:  fmt.Sprintf("ActorPiMapping[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return BulkFetchActorToPIsResponseMultiError(errors)
	}

	return nil
}

// BulkFetchActorToPIsResponseMultiError is an error wrapping multiple
// validation errors returned by BulkFetchActorToPIsResponse.ValidateAll() if
// the designated constraints aren't met.
type BulkFetchActorToPIsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkFetchActorToPIsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkFetchActorToPIsResponseMultiError) AllErrors() []error { return m }

// BulkFetchActorToPIsResponseValidationError is the validation error returned
// by BulkFetchActorToPIsResponse.Validate if the designated constraints
// aren't met.
type BulkFetchActorToPIsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkFetchActorToPIsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkFetchActorToPIsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkFetchActorToPIsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkFetchActorToPIsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkFetchActorToPIsResponseValidationError) ErrorName() string {
	return "BulkFetchActorToPIsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BulkFetchActorToPIsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkFetchActorToPIsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkFetchActorToPIsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkFetchActorToPIsResponseValidationError{}

// Validate checks the field values on CreateActorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateActorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateActorRequestMultiError, or nil if none found.
func (m *CreateActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _CreateActorRequest_Type_NotInLookup[m.GetType()]; ok {
		err := CreateActorRequestValidationError{
			field:  "Type",
			reason: "value must not be in list [TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EntityId

	// no validation rules for Name

	// no validation rules for Ownership

	if len(errors) > 0 {
		return CreateActorRequestMultiError(errors)
	}

	return nil
}

// CreateActorRequestMultiError is an error wrapping multiple validation errors
// returned by CreateActorRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateActorRequestMultiError) AllErrors() []error { return m }

// CreateActorRequestValidationError is the validation error returned by
// CreateActorRequest.Validate if the designated constraints aren't met.
type CreateActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateActorRequestValidationError) ErrorName() string {
	return "CreateActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateActorRequestValidationError{}

var _CreateActorRequest_Type_NotInLookup = map[typesv2.Actor_Type]struct{}{
	0: {},
}

// Validate checks the field values on CreateActorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateActorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateActorResponseMultiError, or nil if none found.
func (m *CreateActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateActorResponseValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateActorResponseValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateActorResponseValidationError{
				field:  "Actor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateActorResponseMultiError(errors)
	}

	return nil
}

// CreateActorResponseMultiError is an error wrapping multiple validation
// errors returned by CreateActorResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateActorResponseMultiError) AllErrors() []error { return m }

// CreateActorResponseValidationError is the validation error returned by
// CreateActorResponse.Validate if the designated constraints aren't met.
type CreateActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateActorResponseValidationError) ErrorName() string {
	return "CreateActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateActorResponseValidationError{}

// Validate checks the field values on GetActorByIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActorByIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActorByIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActorByIdRequestMultiError, or nil if none found.
func (m *GetActorByIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorByIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetActorByIdRequestMultiError(errors)
	}

	return nil
}

// GetActorByIdRequestMultiError is an error wrapping multiple validation
// errors returned by GetActorByIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetActorByIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorByIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorByIdRequestMultiError) AllErrors() []error { return m }

// GetActorByIdRequestValidationError is the validation error returned by
// GetActorByIdRequest.Validate if the designated constraints aren't met.
type GetActorByIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorByIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorByIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorByIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorByIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorByIdRequestValidationError) ErrorName() string {
	return "GetActorByIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorByIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorByIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorByIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorByIdRequestValidationError{}

// Validate checks the field values on GetActorByIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActorByIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActorByIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActorByIdResponseMultiError, or nil if none found.
func (m *GetActorByIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorByIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActorByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActorByIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActorByIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActorByIdResponseValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActorByIdResponseValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActorByIdResponseValidationError{
				field:  "Actor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetActorByIdResponseMultiError(errors)
	}

	return nil
}

// GetActorByIdResponseMultiError is an error wrapping multiple validation
// errors returned by GetActorByIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetActorByIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorByIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorByIdResponseMultiError) AllErrors() []error { return m }

// GetActorByIdResponseValidationError is the validation error returned by
// GetActorByIdResponse.Validate if the designated constraints aren't met.
type GetActorByIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorByIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorByIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorByIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorByIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorByIdResponseValidationError) ErrorName() string {
	return "GetActorByIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorByIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorByIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorByIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorByIdResponseValidationError{}

// Validate checks the field values on GetActorByEntityIdRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActorByEntityIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActorByEntityIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActorByEntityIdRequestMultiError, or nil if none found.
func (m *GetActorByEntityIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorByEntityIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for EntityId

	if len(errors) > 0 {
		return GetActorByEntityIdRequestMultiError(errors)
	}

	return nil
}

// GetActorByEntityIdRequestMultiError is an error wrapping multiple validation
// errors returned by GetActorByEntityIdRequest.ValidateAll() if the
// designated constraints aren't met.
type GetActorByEntityIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorByEntityIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorByEntityIdRequestMultiError) AllErrors() []error { return m }

// GetActorByEntityIdRequestValidationError is the validation error returned by
// GetActorByEntityIdRequest.Validate if the designated constraints aren't met.
type GetActorByEntityIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorByEntityIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorByEntityIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorByEntityIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorByEntityIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorByEntityIdRequestValidationError) ErrorName() string {
	return "GetActorByEntityIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorByEntityIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorByEntityIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorByEntityIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorByEntityIdRequestValidationError{}

// Validate checks the field values on GetActorByEntityIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActorByEntityIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActorByEntityIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActorByEntityIdResponseMultiError, or nil if none found.
func (m *GetActorByEntityIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorByEntityIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActorByEntityIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActorByEntityIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActorByEntityIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActorByEntityIdResponseValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActorByEntityIdResponseValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActorByEntityIdResponseValidationError{
				field:  "Actor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetActorByEntityIdResponseMultiError(errors)
	}

	return nil
}

// GetActorByEntityIdResponseMultiError is an error wrapping multiple
// validation errors returned by GetActorByEntityIdResponse.ValidateAll() if
// the designated constraints aren't met.
type GetActorByEntityIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorByEntityIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorByEntityIdResponseMultiError) AllErrors() []error { return m }

// GetActorByEntityIdResponseValidationError is the validation error returned
// by GetActorByEntityIdResponse.Validate if the designated constraints aren't met.
type GetActorByEntityIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorByEntityIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorByEntityIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorByEntityIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorByEntityIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorByEntityIdResponseValidationError) ErrorName() string {
	return "GetActorByEntityIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorByEntityIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorByEntityIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorByEntityIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorByEntityIdResponseValidationError{}

// Validate checks the field values on CreateActorPiResolutionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateActorPiResolutionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateActorPiResolutionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateActorPiResolutionRequestMultiError, or nil if none found.
func (m *CreateActorPiResolutionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateActorPiResolutionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorFrom()) < 1 {
		err := CreateActorPiResolutionRequestValidationError{
			field:  "ActorFrom",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorTo()) < 1 {
		err := CreateActorPiResolutionRequestValidationError{
			field:  "ActorTo",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PiFrom

	// no validation rules for PiTo

	if len(errors) > 0 {
		return CreateActorPiResolutionRequestMultiError(errors)
	}

	return nil
}

// CreateActorPiResolutionRequestMultiError is an error wrapping multiple
// validation errors returned by CreateActorPiResolutionRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateActorPiResolutionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateActorPiResolutionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateActorPiResolutionRequestMultiError) AllErrors() []error { return m }

// CreateActorPiResolutionRequestValidationError is the validation error
// returned by CreateActorPiResolutionRequest.Validate if the designated
// constraints aren't met.
type CreateActorPiResolutionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateActorPiResolutionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateActorPiResolutionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateActorPiResolutionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateActorPiResolutionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateActorPiResolutionRequestValidationError) ErrorName() string {
	return "CreateActorPiResolutionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateActorPiResolutionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateActorPiResolutionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateActorPiResolutionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateActorPiResolutionRequestValidationError{}

// Validate checks the field values on CreateActorPiResolutionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateActorPiResolutionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateActorPiResolutionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateActorPiResolutionResponseMultiError, or nil if none found.
func (m *CreateActorPiResolutionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateActorPiResolutionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateActorPiResolutionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateActorPiResolutionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateActorPiResolutionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActorPiResolution()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateActorPiResolutionResponseValidationError{
					field:  "ActorPiResolution",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateActorPiResolutionResponseValidationError{
					field:  "ActorPiResolution",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActorPiResolution()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateActorPiResolutionResponseValidationError{
				field:  "ActorPiResolution",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateActorPiResolutionResponseMultiError(errors)
	}

	return nil
}

// CreateActorPiResolutionResponseMultiError is an error wrapping multiple
// validation errors returned by CreateActorPiResolutionResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateActorPiResolutionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateActorPiResolutionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateActorPiResolutionResponseMultiError) AllErrors() []error { return m }

// CreateActorPiResolutionResponseValidationError is the validation error
// returned by CreateActorPiResolutionResponse.Validate if the designated
// constraints aren't met.
type CreateActorPiResolutionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateActorPiResolutionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateActorPiResolutionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateActorPiResolutionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateActorPiResolutionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateActorPiResolutionResponseValidationError) ErrorName() string {
	return "CreateActorPiResolutionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateActorPiResolutionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateActorPiResolutionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateActorPiResolutionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateActorPiResolutionResponseValidationError{}

// Validate checks the field values on ResolveActorToRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResolveActorToRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResolveActorToRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResolveActorToRequestMultiError, or nil if none found.
func (m *ResolveActorToRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResolveActorToRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorFrom()) < 1 {
		err := ResolveActorToRequestValidationError{
			field:  "ActorFrom",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPiTo()) < 1 {
		err := ResolveActorToRequestValidationError{
			field:  "PiTo",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorToName()) < 1 {
		err := ResolveActorToRequestValidationError{
			field:  "ActorToName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Ownership

	if len(errors) > 0 {
		return ResolveActorToRequestMultiError(errors)
	}

	return nil
}

// ResolveActorToRequestMultiError is an error wrapping multiple validation
// errors returned by ResolveActorToRequest.ValidateAll() if the designated
// constraints aren't met.
type ResolveActorToRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResolveActorToRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResolveActorToRequestMultiError) AllErrors() []error { return m }

// ResolveActorToRequestValidationError is the validation error returned by
// ResolveActorToRequest.Validate if the designated constraints aren't met.
type ResolveActorToRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResolveActorToRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResolveActorToRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResolveActorToRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResolveActorToRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResolveActorToRequestValidationError) ErrorName() string {
	return "ResolveActorToRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResolveActorToRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResolveActorToRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResolveActorToRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResolveActorToRequestValidationError{}

// Validate checks the field values on ResolveActorToResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResolveActorToResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResolveActorToResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResolveActorToResponseMultiError, or nil if none found.
func (m *ResolveActorToResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ResolveActorToResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResolveActorToResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResolveActorToResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResolveActorToResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorTo

	if len(errors) > 0 {
		return ResolveActorToResponseMultiError(errors)
	}

	return nil
}

// ResolveActorToResponseMultiError is an error wrapping multiple validation
// errors returned by ResolveActorToResponse.ValidateAll() if the designated
// constraints aren't met.
type ResolveActorToResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResolveActorToResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResolveActorToResponseMultiError) AllErrors() []error { return m }

// ResolveActorToResponseValidationError is the validation error returned by
// ResolveActorToResponse.Validate if the designated constraints aren't met.
type ResolveActorToResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResolveActorToResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResolveActorToResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResolveActorToResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResolveActorToResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResolveActorToResponseValidationError) ErrorName() string {
	return "ResolveActorToResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ResolveActorToResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResolveActorToResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResolveActorToResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResolveActorToResponseValidationError{}

// Validate checks the field values on ResolveActorFromRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResolveActorFromRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResolveActorFromRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResolveActorFromRequestMultiError, or nil if none found.
func (m *ResolveActorFromRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResolveActorFromRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorTo()) < 1 {
		err := ResolveActorFromRequestValidationError{
			field:  "ActorTo",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPiFrom()) < 1 {
		err := ResolveActorFromRequestValidationError{
			field:  "PiFrom",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorFromName()) < 1 {
		err := ResolveActorFromRequestValidationError{
			field:  "ActorFromName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Ownership

	if len(errors) > 0 {
		return ResolveActorFromRequestMultiError(errors)
	}

	return nil
}

// ResolveActorFromRequestMultiError is an error wrapping multiple validation
// errors returned by ResolveActorFromRequest.ValidateAll() if the designated
// constraints aren't met.
type ResolveActorFromRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResolveActorFromRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResolveActorFromRequestMultiError) AllErrors() []error { return m }

// ResolveActorFromRequestValidationError is the validation error returned by
// ResolveActorFromRequest.Validate if the designated constraints aren't met.
type ResolveActorFromRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResolveActorFromRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResolveActorFromRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResolveActorFromRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResolveActorFromRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResolveActorFromRequestValidationError) ErrorName() string {
	return "ResolveActorFromRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResolveActorFromRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResolveActorFromRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResolveActorFromRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResolveActorFromRequestValidationError{}

// Validate checks the field values on ResolveActorFromResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResolveActorFromResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResolveActorFromResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResolveActorFromResponseMultiError, or nil if none found.
func (m *ResolveActorFromResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ResolveActorFromResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResolveActorFromResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResolveActorFromResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResolveActorFromResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorFrom

	if len(errors) > 0 {
		return ResolveActorFromResponseMultiError(errors)
	}

	return nil
}

// ResolveActorFromResponseMultiError is an error wrapping multiple validation
// errors returned by ResolveActorFromResponse.ValidateAll() if the designated
// constraints aren't met.
type ResolveActorFromResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResolveActorFromResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResolveActorFromResponseMultiError) AllErrors() []error { return m }

// ResolveActorFromResponseValidationError is the validation error returned by
// ResolveActorFromResponse.Validate if the designated constraints aren't met.
type ResolveActorFromResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResolveActorFromResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResolveActorFromResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResolveActorFromResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResolveActorFromResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResolveActorFromResponseValidationError) ErrorName() string {
	return "ResolveActorFromResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ResolveActorFromResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResolveActorFromResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResolveActorFromResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResolveActorFromResponseValidationError{}

// Validate checks the field values on GetPIsOfActorToRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPIsOfActorToRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPIsOfActorToRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPIsOfActorToRequestMultiError, or nil if none found.
func (m *GetPIsOfActorToRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPIsOfActorToRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorFrom()) < 1 {
		err := GetPIsOfActorToRequestValidationError{
			field:  "ActorFrom",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorTo()) < 1 {
		err := GetPIsOfActorToRequestValidationError{
			field:  "ActorTo",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Limit

	if len(errors) > 0 {
		return GetPIsOfActorToRequestMultiError(errors)
	}

	return nil
}

// GetPIsOfActorToRequestMultiError is an error wrapping multiple validation
// errors returned by GetPIsOfActorToRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPIsOfActorToRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPIsOfActorToRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPIsOfActorToRequestMultiError) AllErrors() []error { return m }

// GetPIsOfActorToRequestValidationError is the validation error returned by
// GetPIsOfActorToRequest.Validate if the designated constraints aren't met.
type GetPIsOfActorToRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPIsOfActorToRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPIsOfActorToRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPIsOfActorToRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPIsOfActorToRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPIsOfActorToRequestValidationError) ErrorName() string {
	return "GetPIsOfActorToRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPIsOfActorToRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPIsOfActorToRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPIsOfActorToRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPIsOfActorToRequestValidationError{}

// Validate checks the field values on GetPIsOfActorToResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPIsOfActorToResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPIsOfActorToResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPIsOfActorToResponseMultiError, or nil if none found.
func (m *GetPIsOfActorToResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPIsOfActorToResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPIsOfActorToResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPIsOfActorToResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPIsOfActorToResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPIsOfActorToResponseMultiError(errors)
	}

	return nil
}

// GetPIsOfActorToResponseMultiError is an error wrapping multiple validation
// errors returned by GetPIsOfActorToResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPIsOfActorToResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPIsOfActorToResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPIsOfActorToResponseMultiError) AllErrors() []error { return m }

// GetPIsOfActorToResponseValidationError is the validation error returned by
// GetPIsOfActorToResponse.Validate if the designated constraints aren't met.
type GetPIsOfActorToResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPIsOfActorToResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPIsOfActorToResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPIsOfActorToResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPIsOfActorToResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPIsOfActorToResponseValidationError) ErrorName() string {
	return "GetPIsOfActorToResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPIsOfActorToResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPIsOfActorToResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPIsOfActorToResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPIsOfActorToResponseValidationError{}

// Validate checks the field values on GetEntityDetailsByActorIdRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetEntityDetailsByActorIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEntityDetailsByActorIdRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetEntityDetailsByActorIdRequestMultiError, or nil if none found.
func (m *GetEntityDetailsByActorIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEntityDetailsByActorIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetEntityDetailsByActorIdRequestMultiError(errors)
	}

	return nil
}

// GetEntityDetailsByActorIdRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetEntityDetailsByActorIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetEntityDetailsByActorIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEntityDetailsByActorIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEntityDetailsByActorIdRequestMultiError) AllErrors() []error { return m }

// GetEntityDetailsByActorIdRequestValidationError is the validation error
// returned by GetEntityDetailsByActorIdRequest.Validate if the designated
// constraints aren't met.
type GetEntityDetailsByActorIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEntityDetailsByActorIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEntityDetailsByActorIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEntityDetailsByActorIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEntityDetailsByActorIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEntityDetailsByActorIdRequestValidationError) ErrorName() string {
	return "GetEntityDetailsByActorIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEntityDetailsByActorIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEntityDetailsByActorIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEntityDetailsByActorIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEntityDetailsByActorIdRequestValidationError{}

// Validate checks the field values on GetEntityDetailsByActorIdResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetEntityDetailsByActorIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEntityDetailsByActorIdResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetEntityDetailsByActorIdResponseMultiError, or nil if none found.
func (m *GetEntityDetailsByActorIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEntityDetailsByActorIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEntityDetailsByActorIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEntityDetailsByActorIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEntityDetailsByActorIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntityId

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEntityDetailsByActorIdResponseValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEntityDetailsByActorIdResponseValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEntityDetailsByActorIdResponseValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMobileNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEntityDetailsByActorIdResponseValidationError{
					field:  "MobileNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEntityDetailsByActorIdResponseValidationError{
					field:  "MobileNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMobileNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEntityDetailsByActorIdResponseValidationError{
				field:  "MobileNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProfileImageUrl

	// no validation rules for EmailId

	if all {
		switch v := interface{}(m.GetLegalName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEntityDetailsByActorIdResponseValidationError{
					field:  "LegalName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEntityDetailsByActorIdResponseValidationError{
					field:  "LegalName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLegalName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEntityDetailsByActorIdResponseValidationError{
				field:  "LegalName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	switch v := m.Entity.(type) {
	case *GetEntityDetailsByActorIdResponse_Merchant_:
		if v == nil {
			err := GetEntityDetailsByActorIdResponseValidationError{
				field:  "Entity",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMerchant()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetEntityDetailsByActorIdResponseValidationError{
						field:  "Merchant",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetEntityDetailsByActorIdResponseValidationError{
						field:  "Merchant",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMerchant()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetEntityDetailsByActorIdResponseValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetEntityDetailsByActorIdResponseMultiError(errors)
	}

	return nil
}

// GetEntityDetailsByActorIdResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetEntityDetailsByActorIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetEntityDetailsByActorIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEntityDetailsByActorIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEntityDetailsByActorIdResponseMultiError) AllErrors() []error { return m }

// GetEntityDetailsByActorIdResponseValidationError is the validation error
// returned by GetEntityDetailsByActorIdResponse.Validate if the designated
// constraints aren't met.
type GetEntityDetailsByActorIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEntityDetailsByActorIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEntityDetailsByActorIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEntityDetailsByActorIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEntityDetailsByActorIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEntityDetailsByActorIdResponseValidationError) ErrorName() string {
	return "GetEntityDetailsByActorIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEntityDetailsByActorIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEntityDetailsByActorIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEntityDetailsByActorIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEntityDetailsByActorIdResponseValidationError{}

// Validate checks the field values on BlockActorRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BlockActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlockActorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BlockActorRequestMultiError, or nil if none found.
func (m *BlockActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BlockActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetCurrentActorId()) < 1 {
		err := BlockActorRequestValidationError{
			field:  "CurrentActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOtherActorId()) < 1 {
		err := BlockActorRequestValidationError{
			field:  "OtherActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IsSpam

	if len(errors) > 0 {
		return BlockActorRequestMultiError(errors)
	}

	return nil
}

// BlockActorRequestMultiError is an error wrapping multiple validation errors
// returned by BlockActorRequest.ValidateAll() if the designated constraints
// aren't met.
type BlockActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlockActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlockActorRequestMultiError) AllErrors() []error { return m }

// BlockActorRequestValidationError is the validation error returned by
// BlockActorRequest.Validate if the designated constraints aren't met.
type BlockActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlockActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlockActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlockActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlockActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlockActorRequestValidationError) ErrorName() string {
	return "BlockActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BlockActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlockActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlockActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlockActorRequestValidationError{}

// Validate checks the field values on BlockActorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BlockActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlockActorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BlockActorResponseMultiError, or nil if none found.
func (m *BlockActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BlockActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BlockActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BlockActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BlockActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BlockActorResponseMultiError(errors)
	}

	return nil
}

// BlockActorResponseMultiError is an error wrapping multiple validation errors
// returned by BlockActorResponse.ValidateAll() if the designated constraints
// aren't met.
type BlockActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlockActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlockActorResponseMultiError) AllErrors() []error { return m }

// BlockActorResponseValidationError is the validation error returned by
// BlockActorResponse.Validate if the designated constraints aren't met.
type BlockActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlockActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlockActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlockActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlockActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlockActorResponseValidationError) ErrorName() string {
	return "BlockActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BlockActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlockActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlockActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlockActorResponseValidationError{}

// Validate checks the field values on UnblockActorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnblockActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnblockActorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnblockActorRequestMultiError, or nil if none found.
func (m *UnblockActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UnblockActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetCurrentActorId()) < 1 {
		err := UnblockActorRequestValidationError{
			field:  "CurrentActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOtherActorId()) < 1 {
		err := UnblockActorRequestValidationError{
			field:  "OtherActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UnblockActorRequestMultiError(errors)
	}

	return nil
}

// UnblockActorRequestMultiError is an error wrapping multiple validation
// errors returned by UnblockActorRequest.ValidateAll() if the designated
// constraints aren't met.
type UnblockActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnblockActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnblockActorRequestMultiError) AllErrors() []error { return m }

// UnblockActorRequestValidationError is the validation error returned by
// UnblockActorRequest.Validate if the designated constraints aren't met.
type UnblockActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnblockActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnblockActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnblockActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnblockActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnblockActorRequestValidationError) ErrorName() string {
	return "UnblockActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UnblockActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnblockActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnblockActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnblockActorRequestValidationError{}

// Validate checks the field values on UnblockActorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnblockActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnblockActorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnblockActorResponseMultiError, or nil if none found.
func (m *UnblockActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UnblockActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnblockActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnblockActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnblockActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UnblockActorResponseMultiError(errors)
	}

	return nil
}

// UnblockActorResponseMultiError is an error wrapping multiple validation
// errors returned by UnblockActorResponse.ValidateAll() if the designated
// constraints aren't met.
type UnblockActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnblockActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnblockActorResponseMultiError) AllErrors() []error { return m }

// UnblockActorResponseValidationError is the validation error returned by
// UnblockActorResponse.Validate if the designated constraints aren't met.
type UnblockActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnblockActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnblockActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnblockActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnblockActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnblockActorResponseValidationError) ErrorName() string {
	return "UnblockActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UnblockActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnblockActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnblockActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnblockActorResponseValidationError{}

// Validate checks the field values on ReportSpamForBlockedActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ReportSpamForBlockedActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportSpamForBlockedActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ReportSpamForBlockedActorRequestMultiError, or nil if none found.
func (m *ReportSpamForBlockedActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportSpamForBlockedActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetCurrentActorId()) < 1 {
		err := ReportSpamForBlockedActorRequestValidationError{
			field:  "CurrentActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetBlockedActorId()) < 1 {
		err := ReportSpamForBlockedActorRequestValidationError{
			field:  "BlockedActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ReportSpamForBlockedActorRequestMultiError(errors)
	}

	return nil
}

// ReportSpamForBlockedActorRequestMultiError is an error wrapping multiple
// validation errors returned by
// ReportSpamForBlockedActorRequest.ValidateAll() if the designated
// constraints aren't met.
type ReportSpamForBlockedActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportSpamForBlockedActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportSpamForBlockedActorRequestMultiError) AllErrors() []error { return m }

// ReportSpamForBlockedActorRequestValidationError is the validation error
// returned by ReportSpamForBlockedActorRequest.Validate if the designated
// constraints aren't met.
type ReportSpamForBlockedActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportSpamForBlockedActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportSpamForBlockedActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportSpamForBlockedActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportSpamForBlockedActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportSpamForBlockedActorRequestValidationError) ErrorName() string {
	return "ReportSpamForBlockedActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReportSpamForBlockedActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportSpamForBlockedActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportSpamForBlockedActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportSpamForBlockedActorRequestValidationError{}

// Validate checks the field values on ReportSpamForBlockedActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ReportSpamForBlockedActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportSpamForBlockedActorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ReportSpamForBlockedActorResponseMultiError, or nil if none found.
func (m *ReportSpamForBlockedActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportSpamForBlockedActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReportSpamForBlockedActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReportSpamForBlockedActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReportSpamForBlockedActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReportSpamForBlockedActorResponseMultiError(errors)
	}

	return nil
}

// ReportSpamForBlockedActorResponseMultiError is an error wrapping multiple
// validation errors returned by
// ReportSpamForBlockedActorResponse.ValidateAll() if the designated
// constraints aren't met.
type ReportSpamForBlockedActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportSpamForBlockedActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportSpamForBlockedActorResponseMultiError) AllErrors() []error { return m }

// ReportSpamForBlockedActorResponseValidationError is the validation error
// returned by ReportSpamForBlockedActorResponse.Validate if the designated
// constraints aren't met.
type ReportSpamForBlockedActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportSpamForBlockedActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportSpamForBlockedActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportSpamForBlockedActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportSpamForBlockedActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportSpamForBlockedActorResponseValidationError) ErrorName() string {
	return "ReportSpamForBlockedActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReportSpamForBlockedActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportSpamForBlockedActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportSpamForBlockedActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportSpamForBlockedActorResponseValidationError{}

// Validate checks the field values on GetRelationshipWithActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRelationshipWithActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRelationshipWithActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRelationshipWithActorRequestMultiError, or nil if none found.
func (m *GetRelationshipWithActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRelationshipWithActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetCurrentActorId()) < 1 {
		err := GetRelationshipWithActorRequestValidationError{
			field:  "CurrentActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOtherActorId()) < 1 {
		err := GetRelationshipWithActorRequestValidationError{
			field:  "OtherActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetRelationshipWithActorRequestMultiError(errors)
	}

	return nil
}

// GetRelationshipWithActorRequestMultiError is an error wrapping multiple
// validation errors returned by GetRelationshipWithActorRequest.ValidateAll()
// if the designated constraints aren't met.
type GetRelationshipWithActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRelationshipWithActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRelationshipWithActorRequestMultiError) AllErrors() []error { return m }

// GetRelationshipWithActorRequestValidationError is the validation error
// returned by GetRelationshipWithActorRequest.Validate if the designated
// constraints aren't met.
type GetRelationshipWithActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRelationshipWithActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRelationshipWithActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRelationshipWithActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRelationshipWithActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRelationshipWithActorRequestValidationError) ErrorName() string {
	return "GetRelationshipWithActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRelationshipWithActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRelationshipWithActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRelationshipWithActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRelationshipWithActorRequestValidationError{}

// Validate checks the field values on GetRelationshipWithActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetRelationshipWithActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRelationshipWithActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRelationshipWithActorResponseMultiError, or nil if none found.
func (m *GetRelationshipWithActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRelationshipWithActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRelationshipWithActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRelationshipWithActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRelationshipWithActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Relationship

	if len(errors) > 0 {
		return GetRelationshipWithActorResponseMultiError(errors)
	}

	return nil
}

// GetRelationshipWithActorResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetRelationshipWithActorResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRelationshipWithActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRelationshipWithActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRelationshipWithActorResponseMultiError) AllErrors() []error { return m }

// GetRelationshipWithActorResponseValidationError is the validation error
// returned by GetRelationshipWithActorResponse.Validate if the designated
// constraints aren't met.
type GetRelationshipWithActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRelationshipWithActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRelationshipWithActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRelationshipWithActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRelationshipWithActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRelationshipWithActorResponseValidationError) ErrorName() string {
	return "GetRelationshipWithActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRelationshipWithActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRelationshipWithActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRelationshipWithActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRelationshipWithActorResponseValidationError{}

// Validate checks the field values on GetActorsByEntityIdsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActorsByEntityIdsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActorsByEntityIdsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActorsByEntityIdsRequestMultiError, or nil if none found.
func (m *GetActorsByEntityIdsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorsByEntityIdsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetActorsByEntityIdsRequestMultiError(errors)
	}

	return nil
}

// GetActorsByEntityIdsRequestMultiError is an error wrapping multiple
// validation errors returned by GetActorsByEntityIdsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetActorsByEntityIdsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorsByEntityIdsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorsByEntityIdsRequestMultiError) AllErrors() []error { return m }

// GetActorsByEntityIdsRequestValidationError is the validation error returned
// by GetActorsByEntityIdsRequest.Validate if the designated constraints
// aren't met.
type GetActorsByEntityIdsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorsByEntityIdsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorsByEntityIdsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorsByEntityIdsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorsByEntityIdsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorsByEntityIdsRequestValidationError) ErrorName() string {
	return "GetActorsByEntityIdsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorsByEntityIdsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorsByEntityIdsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorsByEntityIdsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorsByEntityIdsRequestValidationError{}

// Validate checks the field values on GetActorsByEntityIdsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActorsByEntityIdsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActorsByEntityIdsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActorsByEntityIdsResponseMultiError, or nil if none found.
func (m *GetActorsByEntityIdsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorsByEntityIdsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActorsByEntityIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActorsByEntityIdsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActorsByEntityIdsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActors() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetActorsByEntityIdsResponseValidationError{
						field:  fmt.Sprintf("Actors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetActorsByEntityIdsResponseValidationError{
						field:  fmt.Sprintf("Actors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetActorsByEntityIdsResponseValidationError{
					field:  fmt.Sprintf("Actors[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetActorsByEntityIdsResponseMultiError(errors)
	}

	return nil
}

// GetActorsByEntityIdsResponseMultiError is an error wrapping multiple
// validation errors returned by GetActorsByEntityIdsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetActorsByEntityIdsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorsByEntityIdsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorsByEntityIdsResponseMultiError) AllErrors() []error { return m }

// GetActorsByEntityIdsResponseValidationError is the validation error returned
// by GetActorsByEntityIdsResponse.Validate if the designated constraints
// aren't met.
type GetActorsByEntityIdsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorsByEntityIdsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorsByEntityIdsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorsByEntityIdsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorsByEntityIdsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorsByEntityIdsResponseValidationError) ErrorName() string {
	return "GetActorsByEntityIdsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorsByEntityIdsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorsByEntityIdsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorsByEntityIdsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorsByEntityIdsResponseValidationError{}

// Validate checks the field values on GetEntityDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEntityDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEntityDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEntityDetailsRequestMultiError, or nil if none found.
func (m *GetEntityDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEntityDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FetchSoftDeletedUsers

	if len(errors) > 0 {
		return GetEntityDetailsRequestMultiError(errors)
	}

	return nil
}

// GetEntityDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetEntityDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetEntityDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEntityDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEntityDetailsRequestMultiError) AllErrors() []error { return m }

// GetEntityDetailsRequestValidationError is the validation error returned by
// GetEntityDetailsRequest.Validate if the designated constraints aren't met.
type GetEntityDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEntityDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEntityDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEntityDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEntityDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEntityDetailsRequestValidationError) ErrorName() string {
	return "GetEntityDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEntityDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEntityDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEntityDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEntityDetailsRequestValidationError{}

// Validate checks the field values on GetEntityDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEntityDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEntityDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEntityDetailsResponseMultiError, or nil if none found.
func (m *GetEntityDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEntityDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEntityDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEntityDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEntityDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetEntityDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetEntityDetailsResponseValidationError{
						field:  fmt.Sprintf("EntityDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetEntityDetailsResponseValidationError{
						field:  fmt.Sprintf("EntityDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetEntityDetailsResponseValidationError{
					field:  fmt.Sprintf("EntityDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetEntityDetailsResponseMultiError(errors)
	}

	return nil
}

// GetEntityDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetEntityDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetEntityDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEntityDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEntityDetailsResponseMultiError) AllErrors() []error { return m }

// GetEntityDetailsResponseValidationError is the validation error returned by
// GetEntityDetailsResponse.Validate if the designated constraints aren't met.
type GetEntityDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEntityDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEntityDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEntityDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEntityDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEntityDetailsResponseValidationError) ErrorName() string {
	return "GetEntityDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEntityDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEntityDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEntityDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEntityDetailsResponseValidationError{}

// Validate checks the field values on DeleteActorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteActorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteActorRequestMultiError, or nil if none found.
func (m *DeleteActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return DeleteActorRequestMultiError(errors)
	}

	return nil
}

// DeleteActorRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteActorRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteActorRequestMultiError) AllErrors() []error { return m }

// DeleteActorRequestValidationError is the validation error returned by
// DeleteActorRequest.Validate if the designated constraints aren't met.
type DeleteActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteActorRequestValidationError) ErrorName() string {
	return "DeleteActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteActorRequestValidationError{}

// Validate checks the field values on DeleteActorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteActorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteActorResponseMultiError, or nil if none found.
func (m *DeleteActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeleteActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeleteActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeleteActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeleteActorResponseMultiError(errors)
	}

	return nil
}

// DeleteActorResponseMultiError is an error wrapping multiple validation
// errors returned by DeleteActorResponse.ValidateAll() if the designated
// constraints aren't met.
type DeleteActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteActorResponseMultiError) AllErrors() []error { return m }

// DeleteActorResponseValidationError is the validation error returned by
// DeleteActorResponse.Validate if the designated constraints aren't met.
type DeleteActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteActorResponseValidationError) ErrorName() string {
	return "DeleteActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteActorResponseValidationError{}

// Validate checks the field values on BatchHardDeleteActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchHardDeleteActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchHardDeleteActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchHardDeleteActorRequestMultiError, or nil if none found.
func (m *BatchHardDeleteActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchHardDeleteActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchHardDeleteActorRequestMultiError(errors)
	}

	return nil
}

// BatchHardDeleteActorRequestMultiError is an error wrapping multiple
// validation errors returned by BatchHardDeleteActorRequest.ValidateAll() if
// the designated constraints aren't met.
type BatchHardDeleteActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchHardDeleteActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchHardDeleteActorRequestMultiError) AllErrors() []error { return m }

// BatchHardDeleteActorRequestValidationError is the validation error returned
// by BatchHardDeleteActorRequest.Validate if the designated constraints
// aren't met.
type BatchHardDeleteActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchHardDeleteActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchHardDeleteActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchHardDeleteActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchHardDeleteActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchHardDeleteActorRequestValidationError) ErrorName() string {
	return "BatchHardDeleteActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchHardDeleteActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchHardDeleteActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchHardDeleteActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchHardDeleteActorRequestValidationError{}

// Validate checks the field values on BatchHardDeleteActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchHardDeleteActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchHardDeleteActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchHardDeleteActorResponseMultiError, or nil if none found.
func (m *BatchHardDeleteActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchHardDeleteActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchHardDeleteActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchHardDeleteActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchHardDeleteActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BatchHardDeleteActorResponseMultiError(errors)
	}

	return nil
}

// BatchHardDeleteActorResponseMultiError is an error wrapping multiple
// validation errors returned by BatchHardDeleteActorResponse.ValidateAll() if
// the designated constraints aren't met.
type BatchHardDeleteActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchHardDeleteActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchHardDeleteActorResponseMultiError) AllErrors() []error { return m }

// BatchHardDeleteActorResponseValidationError is the validation error returned
// by BatchHardDeleteActorResponse.Validate if the designated constraints
// aren't met.
type BatchHardDeleteActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchHardDeleteActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchHardDeleteActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchHardDeleteActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchHardDeleteActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchHardDeleteActorResponseValidationError) ErrorName() string {
	return "BatchHardDeleteActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BatchHardDeleteActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchHardDeleteActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchHardDeleteActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchHardDeleteActorResponseValidationError{}

// Validate checks the field values on BatchHardDeleteActorPiResolutionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *BatchHardDeleteActorPiResolutionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// BatchHardDeleteActorPiResolutionRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// BatchHardDeleteActorPiResolutionRequestMultiError, or nil if none found.
func (m *BatchHardDeleteActorPiResolutionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchHardDeleteActorPiResolutionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchHardDeleteActorPiResolutionRequestMultiError(errors)
	}

	return nil
}

// BatchHardDeleteActorPiResolutionRequestMultiError is an error wrapping
// multiple validation errors returned by
// BatchHardDeleteActorPiResolutionRequest.ValidateAll() if the designated
// constraints aren't met.
type BatchHardDeleteActorPiResolutionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchHardDeleteActorPiResolutionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchHardDeleteActorPiResolutionRequestMultiError) AllErrors() []error { return m }

// BatchHardDeleteActorPiResolutionRequestValidationError is the validation
// error returned by BatchHardDeleteActorPiResolutionRequest.Validate if the
// designated constraints aren't met.
type BatchHardDeleteActorPiResolutionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchHardDeleteActorPiResolutionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchHardDeleteActorPiResolutionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchHardDeleteActorPiResolutionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchHardDeleteActorPiResolutionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchHardDeleteActorPiResolutionRequestValidationError) ErrorName() string {
	return "BatchHardDeleteActorPiResolutionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchHardDeleteActorPiResolutionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchHardDeleteActorPiResolutionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchHardDeleteActorPiResolutionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchHardDeleteActorPiResolutionRequestValidationError{}

// Validate checks the field values on BatchHardDeleteActorPiResolutionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *BatchHardDeleteActorPiResolutionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// BatchHardDeleteActorPiResolutionResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// BatchHardDeleteActorPiResolutionResponseMultiError, or nil if none found.
func (m *BatchHardDeleteActorPiResolutionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchHardDeleteActorPiResolutionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchHardDeleteActorPiResolutionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchHardDeleteActorPiResolutionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchHardDeleteActorPiResolutionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BatchHardDeleteActorPiResolutionResponseMultiError(errors)
	}

	return nil
}

// BatchHardDeleteActorPiResolutionResponseMultiError is an error wrapping
// multiple validation errors returned by
// BatchHardDeleteActorPiResolutionResponse.ValidateAll() if the designated
// constraints aren't met.
type BatchHardDeleteActorPiResolutionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchHardDeleteActorPiResolutionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchHardDeleteActorPiResolutionResponseMultiError) AllErrors() []error { return m }

// BatchHardDeleteActorPiResolutionResponseValidationError is the validation
// error returned by BatchHardDeleteActorPiResolutionResponse.Validate if the
// designated constraints aren't met.
type BatchHardDeleteActorPiResolutionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchHardDeleteActorPiResolutionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchHardDeleteActorPiResolutionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchHardDeleteActorPiResolutionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchHardDeleteActorPiResolutionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchHardDeleteActorPiResolutionResponseValidationError) ErrorName() string {
	return "BatchHardDeleteActorPiResolutionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BatchHardDeleteActorPiResolutionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchHardDeleteActorPiResolutionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchHardDeleteActorPiResolutionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchHardDeleteActorPiResolutionResponseValidationError{}

// Validate checks the field values on GetActorCapabilitiesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActorCapabilitiesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActorCapabilitiesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActorCapabilitiesRequestMultiError, or nil if none found.
func (m *GetActorCapabilitiesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorCapabilitiesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetActorCapabilitiesRequestMultiError(errors)
	}

	return nil
}

// GetActorCapabilitiesRequestMultiError is an error wrapping multiple
// validation errors returned by GetActorCapabilitiesRequest.ValidateAll() if
// the designated constraints aren't met.
type GetActorCapabilitiesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorCapabilitiesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorCapabilitiesRequestMultiError) AllErrors() []error { return m }

// GetActorCapabilitiesRequestValidationError is the validation error returned
// by GetActorCapabilitiesRequest.Validate if the designated constraints
// aren't met.
type GetActorCapabilitiesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorCapabilitiesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorCapabilitiesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorCapabilitiesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorCapabilitiesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorCapabilitiesRequestValidationError) ErrorName() string {
	return "GetActorCapabilitiesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorCapabilitiesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorCapabilitiesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorCapabilitiesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorCapabilitiesRequestValidationError{}

// Validate checks the field values on GetActorCapabilitiesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActorCapabilitiesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActorCapabilitiesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetActorCapabilitiesResponseMultiError, or nil if none found.
func (m *GetActorCapabilitiesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActorCapabilitiesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActorCapabilitiesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActorCapabilitiesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActorCapabilitiesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Value

	if len(errors) > 0 {
		return GetActorCapabilitiesResponseMultiError(errors)
	}

	return nil
}

// GetActorCapabilitiesResponseMultiError is an error wrapping multiple
// validation errors returned by GetActorCapabilitiesResponse.ValidateAll() if
// the designated constraints aren't met.
type GetActorCapabilitiesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActorCapabilitiesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActorCapabilitiesResponseMultiError) AllErrors() []error { return m }

// GetActorCapabilitiesResponseValidationError is the validation error returned
// by GetActorCapabilitiesResponse.Validate if the designated constraints
// aren't met.
type GetActorCapabilitiesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActorCapabilitiesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActorCapabilitiesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActorCapabilitiesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActorCapabilitiesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActorCapabilitiesResponseValidationError) ErrorName() string {
	return "GetActorCapabilitiesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActorCapabilitiesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActorCapabilitiesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActorCapabilitiesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActorCapabilitiesResponseValidationError{}

// Validate checks the field values on ResolveOtherActorPiAndTimelineRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ResolveOtherActorPiAndTimelineRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResolveOtherActorPiAndTimelineRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ResolveOtherActorPiAndTimelineRequestMultiError, or nil if none found.
func (m *ResolveOtherActorPiAndTimelineRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResolveOtherActorPiAndTimelineRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PrimaryActorId

	// no validation rules for PaymentProtocol

	if _, ok := _ResolveOtherActorPiAndTimelineRequest_EventType_NotInLookup[m.GetEventType()]; ok {
		err := ResolveOtherActorPiAndTimelineRequestValidationError{
			field:  "EventType",
			reason: "value must not be in list [TRANSACTION_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AccountType

	// no validation rules for PartnerBank

	// no validation rules for PiOwnership

	// no validation rules for TimelineOwnership

	if all {
		switch v := interface{}(m.GetMerchantDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResolveOtherActorPiAndTimelineRequestValidationError{
					field:  "MerchantDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResolveOtherActorPiAndTimelineRequestValidationError{
					field:  "MerchantDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMerchantDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResolveOtherActorPiAndTimelineRequestValidationError{
				field:  "MerchantDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OtherActorName

	// no validation rules for TransactionCategory

	// no validation rules for AtmAddress

	// no validation rules for TimelineResolutionSource

	if all {
		switch v := interface{}(m.GetEcsEnachDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResolveOtherActorPiAndTimelineRequestValidationError{
					field:  "EcsEnachDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResolveOtherActorPiAndTimelineRequestValidationError{
					field:  "EcsEnachDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEcsEnachDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResolveOtherActorPiAndTimelineRequestValidationError{
				field:  "EcsEnachDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.PiIdentifier.(type) {
	case *ResolveOtherActorPiAndTimelineRequest_Vpa:
		if v == nil {
			err := ResolveOtherActorPiAndTimelineRequestValidationError{
				field:  "PiIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Vpa
	case *ResolveOtherActorPiAndTimelineRequest_Account:
		if v == nil {
			err := ResolveOtherActorPiAndTimelineRequestValidationError{
				field:  "PiIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAccount()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResolveOtherActorPiAndTimelineRequestValidationError{
						field:  "Account",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResolveOtherActorPiAndTimelineRequestValidationError{
						field:  "Account",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResolveOtherActorPiAndTimelineRequestValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ResolveOtherActorPiAndTimelineRequest_PiId:
		if v == nil {
			err := ResolveOtherActorPiAndTimelineRequestValidationError{
				field:  "PiIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PiId
	case *ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier_:
		if v == nil {
			err := ResolveOtherActorPiAndTimelineRequestValidationError{
				field:  "PiIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCardTransactionIdentifier()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResolveOtherActorPiAndTimelineRequestValidationError{
						field:  "CardTransactionIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResolveOtherActorPiAndTimelineRequestValidationError{
						field:  "CardTransactionIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCardTransactionIdentifier()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResolveOtherActorPiAndTimelineRequestValidationError{
					field:  "CardTransactionIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ResolveOtherActorPiAndTimelineRequestMultiError(errors)
	}

	return nil
}

// ResolveOtherActorPiAndTimelineRequestMultiError is an error wrapping
// multiple validation errors returned by
// ResolveOtherActorPiAndTimelineRequest.ValidateAll() if the designated
// constraints aren't met.
type ResolveOtherActorPiAndTimelineRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResolveOtherActorPiAndTimelineRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResolveOtherActorPiAndTimelineRequestMultiError) AllErrors() []error { return m }

// ResolveOtherActorPiAndTimelineRequestValidationError is the validation error
// returned by ResolveOtherActorPiAndTimelineRequest.Validate if the
// designated constraints aren't met.
type ResolveOtherActorPiAndTimelineRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResolveOtherActorPiAndTimelineRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResolveOtherActorPiAndTimelineRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResolveOtherActorPiAndTimelineRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResolveOtherActorPiAndTimelineRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResolveOtherActorPiAndTimelineRequestValidationError) ErrorName() string {
	return "ResolveOtherActorPiAndTimelineRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResolveOtherActorPiAndTimelineRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResolveOtherActorPiAndTimelineRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResolveOtherActorPiAndTimelineRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResolveOtherActorPiAndTimelineRequestValidationError{}

var _ResolveOtherActorPiAndTimelineRequest_EventType_NotInLookup = map[typesv2.TransactionTransferType]struct{}{
	0: {},
}

// Validate checks the field values on ResolveOtherActorPiAndTimelineResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ResolveOtherActorPiAndTimelineResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ResolveOtherActorPiAndTimelineResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ResolveOtherActorPiAndTimelineResponseMultiError, or nil if none found.
func (m *ResolveOtherActorPiAndTimelineResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ResolveOtherActorPiAndTimelineResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResolveOtherActorPiAndTimelineResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResolveOtherActorPiAndTimelineResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResolveOtherActorPiAndTimelineResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OtherActorPiId

	// no validation rules for TimelineId

	if len(errors) > 0 {
		return ResolveOtherActorPiAndTimelineResponseMultiError(errors)
	}

	return nil
}

// ResolveOtherActorPiAndTimelineResponseMultiError is an error wrapping
// multiple validation errors returned by
// ResolveOtherActorPiAndTimelineResponse.ValidateAll() if the designated
// constraints aren't met.
type ResolveOtherActorPiAndTimelineResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResolveOtherActorPiAndTimelineResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResolveOtherActorPiAndTimelineResponseMultiError) AllErrors() []error { return m }

// ResolveOtherActorPiAndTimelineResponseValidationError is the validation
// error returned by ResolveOtherActorPiAndTimelineResponse.Validate if the
// designated constraints aren't met.
type ResolveOtherActorPiAndTimelineResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResolveOtherActorPiAndTimelineResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResolveOtherActorPiAndTimelineResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResolveOtherActorPiAndTimelineResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResolveOtherActorPiAndTimelineResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResolveOtherActorPiAndTimelineResponseValidationError) ErrorName() string {
	return "ResolveOtherActorPiAndTimelineResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ResolveOtherActorPiAndTimelineResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResolveOtherActorPiAndTimelineResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResolveOtherActorPiAndTimelineResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResolveOtherActorPiAndTimelineResponseValidationError{}

// Validate checks the field values on MerchantDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MerchantDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MerchantDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MerchantDetailsMultiError, or nil if none found.
func (m *MerchantDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *MerchantDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Location

	// no validation rules for MccCode

	// no validation rules for GeoCode

	// no validation rules for City

	// no validation rules for CountryCode

	// no validation rules for Mid

	// no validation rules for Tid

	if len(errors) > 0 {
		return MerchantDetailsMultiError(errors)
	}

	return nil
}

// MerchantDetailsMultiError is an error wrapping multiple validation errors
// returned by MerchantDetails.ValidateAll() if the designated constraints
// aren't met.
type MerchantDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MerchantDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MerchantDetailsMultiError) AllErrors() []error { return m }

// MerchantDetailsValidationError is the validation error returned by
// MerchantDetails.Validate if the designated constraints aren't met.
type MerchantDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MerchantDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MerchantDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MerchantDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MerchantDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MerchantDetailsValidationError) ErrorName() string { return "MerchantDetailsValidationError" }

// Error satisfies the builtin error interface
func (e MerchantDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMerchantDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MerchantDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MerchantDetailsValidationError{}

// Validate checks the field values on
// GetEntityDetailsByActorIdResponse_Merchant with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetEntityDetailsByActorIdResponse_Merchant) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetEntityDetailsByActorIdResponse_Merchant with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetEntityDetailsByActorIdResponse_MerchantMultiError, or nil if none found.
func (m *GetEntityDetailsByActorIdResponse_Merchant) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEntityDetailsByActorIdResponse_Merchant) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Capabilities

	if len(errors) > 0 {
		return GetEntityDetailsByActorIdResponse_MerchantMultiError(errors)
	}

	return nil
}

// GetEntityDetailsByActorIdResponse_MerchantMultiError is an error wrapping
// multiple validation errors returned by
// GetEntityDetailsByActorIdResponse_Merchant.ValidateAll() if the designated
// constraints aren't met.
type GetEntityDetailsByActorIdResponse_MerchantMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEntityDetailsByActorIdResponse_MerchantMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEntityDetailsByActorIdResponse_MerchantMultiError) AllErrors() []error { return m }

// GetEntityDetailsByActorIdResponse_MerchantValidationError is the validation
// error returned by GetEntityDetailsByActorIdResponse_Merchant.Validate if
// the designated constraints aren't met.
type GetEntityDetailsByActorIdResponse_MerchantValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEntityDetailsByActorIdResponse_MerchantValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEntityDetailsByActorIdResponse_MerchantValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEntityDetailsByActorIdResponse_MerchantValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEntityDetailsByActorIdResponse_MerchantValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEntityDetailsByActorIdResponse_MerchantValidationError) ErrorName() string {
	return "GetEntityDetailsByActorIdResponse_MerchantValidationError"
}

// Error satisfies the builtin error interface
func (e GetEntityDetailsByActorIdResponse_MerchantValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEntityDetailsByActorIdResponse_Merchant.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEntityDetailsByActorIdResponse_MerchantValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEntityDetailsByActorIdResponse_MerchantValidationError{}

// Validate checks the field values on GetEntityDetailsResponse_EntityDetail
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetEntityDetailsResponse_EntityDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEntityDetailsResponse_EntityDetail
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetEntityDetailsResponse_EntityDetailMultiError, or nil if none found.
func (m *GetEntityDetailsResponse_EntityDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEntityDetailsResponse_EntityDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EntityId

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEntityDetailsResponse_EntityDetailValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEntityDetailsResponse_EntityDetailValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEntityDetailsResponse_EntityDetailValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMobileNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEntityDetailsResponse_EntityDetailValidationError{
					field:  "MobileNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEntityDetailsResponse_EntityDetailValidationError{
					field:  "MobileNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMobileNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEntityDetailsResponse_EntityDetailValidationError{
				field:  "MobileNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProfileImageUrl

	// no validation rules for EmailId

	// no validation rules for ActorId

	// no validation rules for EntityType

	switch v := m.Entity.(type) {
	case *GetEntityDetailsResponse_EntityDetail_Merchant_:
		if v == nil {
			err := GetEntityDetailsResponse_EntityDetailValidationError{
				field:  "Entity",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMerchant()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetEntityDetailsResponse_EntityDetailValidationError{
						field:  "Merchant",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetEntityDetailsResponse_EntityDetailValidationError{
						field:  "Merchant",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMerchant()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetEntityDetailsResponse_EntityDetailValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetEntityDetailsResponse_EntityDetailMultiError(errors)
	}

	return nil
}

// GetEntityDetailsResponse_EntityDetailMultiError is an error wrapping
// multiple validation errors returned by
// GetEntityDetailsResponse_EntityDetail.ValidateAll() if the designated
// constraints aren't met.
type GetEntityDetailsResponse_EntityDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEntityDetailsResponse_EntityDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEntityDetailsResponse_EntityDetailMultiError) AllErrors() []error { return m }

// GetEntityDetailsResponse_EntityDetailValidationError is the validation error
// returned by GetEntityDetailsResponse_EntityDetail.Validate if the
// designated constraints aren't met.
type GetEntityDetailsResponse_EntityDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEntityDetailsResponse_EntityDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEntityDetailsResponse_EntityDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEntityDetailsResponse_EntityDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEntityDetailsResponse_EntityDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEntityDetailsResponse_EntityDetailValidationError) ErrorName() string {
	return "GetEntityDetailsResponse_EntityDetailValidationError"
}

// Error satisfies the builtin error interface
func (e GetEntityDetailsResponse_EntityDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEntityDetailsResponse_EntityDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEntityDetailsResponse_EntityDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEntityDetailsResponse_EntityDetailValidationError{}

// Validate checks the field values on
// GetEntityDetailsResponse_EntityDetail_Merchant with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetEntityDetailsResponse_EntityDetail_Merchant) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetEntityDetailsResponse_EntityDetail_Merchant with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetEntityDetailsResponse_EntityDetail_MerchantMultiError, or nil if none found.
func (m *GetEntityDetailsResponse_EntityDetail_Merchant) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEntityDetailsResponse_EntityDetail_Merchant) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Capabilities

	if len(errors) > 0 {
		return GetEntityDetailsResponse_EntityDetail_MerchantMultiError(errors)
	}

	return nil
}

// GetEntityDetailsResponse_EntityDetail_MerchantMultiError is an error
// wrapping multiple validation errors returned by
// GetEntityDetailsResponse_EntityDetail_Merchant.ValidateAll() if the
// designated constraints aren't met.
type GetEntityDetailsResponse_EntityDetail_MerchantMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEntityDetailsResponse_EntityDetail_MerchantMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEntityDetailsResponse_EntityDetail_MerchantMultiError) AllErrors() []error { return m }

// GetEntityDetailsResponse_EntityDetail_MerchantValidationError is the
// validation error returned by
// GetEntityDetailsResponse_EntityDetail_Merchant.Validate if the designated
// constraints aren't met.
type GetEntityDetailsResponse_EntityDetail_MerchantValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEntityDetailsResponse_EntityDetail_MerchantValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEntityDetailsResponse_EntityDetail_MerchantValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetEntityDetailsResponse_EntityDetail_MerchantValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEntityDetailsResponse_EntityDetail_MerchantValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEntityDetailsResponse_EntityDetail_MerchantValidationError) ErrorName() string {
	return "GetEntityDetailsResponse_EntityDetail_MerchantValidationError"
}

// Error satisfies the builtin error interface
func (e GetEntityDetailsResponse_EntityDetail_MerchantValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEntityDetailsResponse_EntityDetail_Merchant.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEntityDetailsResponse_EntityDetail_MerchantValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEntityDetailsResponse_EntityDetail_MerchantValidationError{}

// Validate checks the field values on
// ResolveOtherActorPiAndTimelineRequest_AccountIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResolveOtherActorPiAndTimelineRequest_AccountIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ResolveOtherActorPiAndTimelineRequest_AccountIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResolveOtherActorPiAndTimelineRequest_AccountIdentifierMultiError, or nil
// if none found.
func (m *ResolveOtherActorPiAndTimelineRequest_AccountIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *ResolveOtherActorPiAndTimelineRequest_AccountIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountNumber

	// no validation rules for IfscCode

	// no validation rules for PiType

	// no validation rules for AccountType

	if len(errors) > 0 {
		return ResolveOtherActorPiAndTimelineRequest_AccountIdentifierMultiError(errors)
	}

	return nil
}

// ResolveOtherActorPiAndTimelineRequest_AccountIdentifierMultiError is an
// error wrapping multiple validation errors returned by
// ResolveOtherActorPiAndTimelineRequest_AccountIdentifier.ValidateAll() if
// the designated constraints aren't met.
type ResolveOtherActorPiAndTimelineRequest_AccountIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResolveOtherActorPiAndTimelineRequest_AccountIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResolveOtherActorPiAndTimelineRequest_AccountIdentifierMultiError) AllErrors() []error {
	return m
}

// ResolveOtherActorPiAndTimelineRequest_AccountIdentifierValidationError is
// the validation error returned by
// ResolveOtherActorPiAndTimelineRequest_AccountIdentifier.Validate if the
// designated constraints aren't met.
type ResolveOtherActorPiAndTimelineRequest_AccountIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResolveOtherActorPiAndTimelineRequest_AccountIdentifierValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ResolveOtherActorPiAndTimelineRequest_AccountIdentifierValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ResolveOtherActorPiAndTimelineRequest_AccountIdentifierValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ResolveOtherActorPiAndTimelineRequest_AccountIdentifierValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e ResolveOtherActorPiAndTimelineRequest_AccountIdentifierValidationError) ErrorName() string {
	return "ResolveOtherActorPiAndTimelineRequest_AccountIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e ResolveOtherActorPiAndTimelineRequest_AccountIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResolveOtherActorPiAndTimelineRequest_AccountIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResolveOtherActorPiAndTimelineRequest_AccountIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResolveOtherActorPiAndTimelineRequest_AccountIdentifierValidationError{}

// Validate checks the field values on
// ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierMultiError,
// or nil if none found.
func (m *ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Mid

	// no validation rules for OtherActorName

	if len(errors) > 0 {
		return ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierMultiError(errors)
	}

	return nil
}

// ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierMultiError is
// an error wrapping multiple validation errors returned by
// ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier.ValidateAll()
// if the designated constraints aren't met.
type ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierMultiError) AllErrors() []error {
	return m
}

// ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierValidationError
// is the validation error returned by
// ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier.Validate if
// the designated constraints aren't met.
type ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierValidationError) ErrorName() string {
	return "ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifierValidationError{}
