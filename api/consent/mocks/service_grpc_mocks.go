// Code generated by MockGen. DO NOT EDIT.
// Source: api/consent/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	consent "github.com/epifi/gamma/api/consent"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockConsentClient is a mock of ConsentClient interface.
type MockConsentClient struct {
	ctrl     *gomock.Controller
	recorder *MockConsentClientMockRecorder
}

// MockConsentClientMockRecorder is the mock recorder for MockConsentClient.
type MockConsentClientMockRecorder struct {
	mock *MockConsentClient
}

// NewMockConsentClient creates a new mock instance.
func NewMockConsentClient(ctrl *gomock.Controller) *MockConsentClient {
	mock := &MockConsentClient{ctrl: ctrl}
	mock.recorder = &MockConsentClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsentClient) EXPECT() *MockConsentClientMockRecorder {
	return m.recorder
}

// CheckConsentRequirement mocks base method.
func (m *MockConsentClient) CheckConsentRequirement(ctx context.Context, in *consent.CheckConsentRequirementRequest, opts ...grpc.CallOption) (*consent.CheckConsentRequirementResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckConsentRequirement", varargs...)
	ret0, _ := ret[0].(*consent.CheckConsentRequirementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckConsentRequirement indicates an expected call of CheckConsentRequirement.
func (mr *MockConsentClientMockRecorder) CheckConsentRequirement(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckConsentRequirement", reflect.TypeOf((*MockConsentClient)(nil).CheckConsentRequirement), varargs...)
}

// DeleteConsents mocks base method.
func (m *MockConsentClient) DeleteConsents(ctx context.Context, in *consent.DeleteConsentsRequest, opts ...grpc.CallOption) (*consent.DeleteConsentsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteConsents", varargs...)
	ret0, _ := ret[0].(*consent.DeleteConsentsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteConsents indicates an expected call of DeleteConsents.
func (mr *MockConsentClientMockRecorder) DeleteConsents(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteConsents", reflect.TypeOf((*MockConsentClient)(nil).DeleteConsents), varargs...)
}

// FetchConsent mocks base method.
func (m *MockConsentClient) FetchConsent(ctx context.Context, in *consent.FetchConsentRequest, opts ...grpc.CallOption) (*consent.FetchConsentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchConsent", varargs...)
	ret0, _ := ret[0].(*consent.FetchConsentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchConsent indicates an expected call of FetchConsent.
func (mr *MockConsentClientMockRecorder) FetchConsent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchConsent", reflect.TypeOf((*MockConsentClient)(nil).FetchConsent), varargs...)
}

// FetchConsentByReqId mocks base method.
func (m *MockConsentClient) FetchConsentByReqId(ctx context.Context, in *consent.FetchConsentByReqIdRequest, opts ...grpc.CallOption) (*consent.FetchConsentByReqIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchConsentByReqId", varargs...)
	ret0, _ := ret[0].(*consent.FetchConsentByReqIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchConsentByReqId indicates an expected call of FetchConsentByReqId.
func (mr *MockConsentClientMockRecorder) FetchConsentByReqId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchConsentByReqId", reflect.TypeOf((*MockConsentClient)(nil).FetchConsentByReqId), varargs...)
}

// RecordConsent mocks base method.
func (m *MockConsentClient) RecordConsent(ctx context.Context, in *consent.RecordConsentRequest, opts ...grpc.CallOption) (*consent.RecordConsentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordConsent", varargs...)
	ret0, _ := ret[0].(*consent.RecordConsentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordConsent indicates an expected call of RecordConsent.
func (mr *MockConsentClientMockRecorder) RecordConsent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordConsent", reflect.TypeOf((*MockConsentClient)(nil).RecordConsent), varargs...)
}

// RecordConsents mocks base method.
func (m *MockConsentClient) RecordConsents(ctx context.Context, in *consent.RecordConsentsRequest, opts ...grpc.CallOption) (*consent.RecordConsentsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecordConsents", varargs...)
	ret0, _ := ret[0].(*consent.RecordConsentsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordConsents indicates an expected call of RecordConsents.
func (mr *MockConsentClientMockRecorder) RecordConsents(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordConsents", reflect.TypeOf((*MockConsentClient)(nil).RecordConsents), varargs...)
}

// MockConsentServer is a mock of ConsentServer interface.
type MockConsentServer struct {
	ctrl     *gomock.Controller
	recorder *MockConsentServerMockRecorder
}

// MockConsentServerMockRecorder is the mock recorder for MockConsentServer.
type MockConsentServerMockRecorder struct {
	mock *MockConsentServer
}

// NewMockConsentServer creates a new mock instance.
func NewMockConsentServer(ctrl *gomock.Controller) *MockConsentServer {
	mock := &MockConsentServer{ctrl: ctrl}
	mock.recorder = &MockConsentServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsentServer) EXPECT() *MockConsentServerMockRecorder {
	return m.recorder
}

// CheckConsentRequirement mocks base method.
func (m *MockConsentServer) CheckConsentRequirement(arg0 context.Context, arg1 *consent.CheckConsentRequirementRequest) (*consent.CheckConsentRequirementResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckConsentRequirement", arg0, arg1)
	ret0, _ := ret[0].(*consent.CheckConsentRequirementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckConsentRequirement indicates an expected call of CheckConsentRequirement.
func (mr *MockConsentServerMockRecorder) CheckConsentRequirement(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckConsentRequirement", reflect.TypeOf((*MockConsentServer)(nil).CheckConsentRequirement), arg0, arg1)
}

// DeleteConsents mocks base method.
func (m *MockConsentServer) DeleteConsents(arg0 context.Context, arg1 *consent.DeleteConsentsRequest) (*consent.DeleteConsentsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteConsents", arg0, arg1)
	ret0, _ := ret[0].(*consent.DeleteConsentsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteConsents indicates an expected call of DeleteConsents.
func (mr *MockConsentServerMockRecorder) DeleteConsents(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteConsents", reflect.TypeOf((*MockConsentServer)(nil).DeleteConsents), arg0, arg1)
}

// FetchConsent mocks base method.
func (m *MockConsentServer) FetchConsent(arg0 context.Context, arg1 *consent.FetchConsentRequest) (*consent.FetchConsentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchConsent", arg0, arg1)
	ret0, _ := ret[0].(*consent.FetchConsentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchConsent indicates an expected call of FetchConsent.
func (mr *MockConsentServerMockRecorder) FetchConsent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchConsent", reflect.TypeOf((*MockConsentServer)(nil).FetchConsent), arg0, arg1)
}

// FetchConsentByReqId mocks base method.
func (m *MockConsentServer) FetchConsentByReqId(arg0 context.Context, arg1 *consent.FetchConsentByReqIdRequest) (*consent.FetchConsentByReqIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchConsentByReqId", arg0, arg1)
	ret0, _ := ret[0].(*consent.FetchConsentByReqIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchConsentByReqId indicates an expected call of FetchConsentByReqId.
func (mr *MockConsentServerMockRecorder) FetchConsentByReqId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchConsentByReqId", reflect.TypeOf((*MockConsentServer)(nil).FetchConsentByReqId), arg0, arg1)
}

// RecordConsent mocks base method.
func (m *MockConsentServer) RecordConsent(arg0 context.Context, arg1 *consent.RecordConsentRequest) (*consent.RecordConsentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordConsent", arg0, arg1)
	ret0, _ := ret[0].(*consent.RecordConsentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordConsent indicates an expected call of RecordConsent.
func (mr *MockConsentServerMockRecorder) RecordConsent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordConsent", reflect.TypeOf((*MockConsentServer)(nil).RecordConsent), arg0, arg1)
}

// RecordConsents mocks base method.
func (m *MockConsentServer) RecordConsents(arg0 context.Context, arg1 *consent.RecordConsentsRequest) (*consent.RecordConsentsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordConsents", arg0, arg1)
	ret0, _ := ret[0].(*consent.RecordConsentsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RecordConsents indicates an expected call of RecordConsents.
func (mr *MockConsentServerMockRecorder) RecordConsents(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordConsents", reflect.TypeOf((*MockConsentServer)(nil).RecordConsents), arg0, arg1)
}

// MockUnsafeConsentServer is a mock of UnsafeConsentServer interface.
type MockUnsafeConsentServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeConsentServerMockRecorder
}

// MockUnsafeConsentServerMockRecorder is the mock recorder for MockUnsafeConsentServer.
type MockUnsafeConsentServerMockRecorder struct {
	mock *MockUnsafeConsentServer
}

// NewMockUnsafeConsentServer creates a new mock instance.
func NewMockUnsafeConsentServer(ctrl *gomock.Controller) *MockUnsafeConsentServer {
	mock := &MockUnsafeConsentServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeConsentServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeConsentServer) EXPECT() *MockUnsafeConsentServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedConsentServer mocks base method.
func (m *MockUnsafeConsentServer) mustEmbedUnimplementedConsentServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedConsentServer")
}

// mustEmbedUnimplementedConsentServer indicates an expected call of mustEmbedUnimplementedConsentServer.
func (mr *MockUnsafeConsentServerMockRecorder) mustEmbedUnimplementedConsentServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedConsentServer", reflect.TypeOf((*MockUnsafeConsentServer)(nil).mustEmbedUnimplementedConsentServer))
}
