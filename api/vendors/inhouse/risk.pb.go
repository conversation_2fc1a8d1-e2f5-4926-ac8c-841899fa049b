// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/inhouse/risk.proto

package inhouse

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	common "github.com/epifi/be-common/api/typesv2/common"
	afu "github.com/epifi/gamma/api/auth/afu"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	user "github.com/epifi/gamma/api/user"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DetectRiskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId   string `protobuf:"bytes,1,opt,name=actor_id,proto3" json:"actor_id,omitempty"`
	RequestId string `protobuf:"bytes,2,opt,name=request_id,proto3" json:"request_id,omitempty"`
	// Actor ID of the user who referred the current user
	ReferrerActorId string `protobuf:"bytes,3,opt,name=referrer_actor_id,proto3" json:"referrer_actor_id,omitempty"`
	// Deprecated: Marked as deprecated in api/vendors/inhouse/risk.proto.
	DeviceId                    string `protobuf:"bytes,4,opt,name=device_id,proto3" json:"device_id,omitempty"`
	EmailId                     string `protobuf:"bytes,5,opt,name=email_id,json=email,proto3" json:"email_id,omitempty"`
	CreditReportPresence        bool   `protobuf:"varint,6,opt,name=credit_report_presence,proto3" json:"credit_report_presence,omitempty"`
	CreditReportDownloadConsent bool   `protobuf:"varint,7,opt,name=credit_report_download_consent,proto3" json:"credit_report_download_consent,omitempty"`
	// Deprecated: Marked as deprecated in api/vendors/inhouse/risk.proto.
	DeviceManufacturer string `protobuf:"bytes,8,opt,name=device_manufacturer,proto3" json:"device_manufacturer,omitempty"`
	// Deprecated: Marked as deprecated in api/vendors/inhouse/risk.proto.
	IsDevicePremium bool  `protobuf:"varint,9,opt,name=is_device_premium,json=device_is_premium,proto3" json:"is_device_premium,omitempty"`
	Age             int32 `protobuf:"varint,10,opt,name=age,proto3" json:"age,omitempty"`
	// Name match score between gmail and pan name
	GmailPanNameMatchScore       float32                      `protobuf:"fixed32,11,opt,name=gmail_pan_name_match_score,json=namematch_gmail_kyc,proto3" json:"gmail_pan_name_match_score,omitempty"`
	PhoneNumber                  string                       `protobuf:"bytes,12,opt,name=phone_number,proto3" json:"phone_number,omitempty"`
	ScreenerMailCount            int32                        `protobuf:"varint,13,opt,name=screener_mail_count,proto3" json:"screener_mail_count,omitempty"`
	Latitude                     float32                      `protobuf:"fixed32,14,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude                    float32                      `protobuf:"fixed32,15,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Threshold                    float32                      `protobuf:"fixed32,16,opt,name=threshold,proto3" json:"threshold,omitempty"`
	LivenessStatuses             []string                     `protobuf:"bytes,17,rep,name=liveness_statuses,json=liveness_status,proto3" json:"liveness_statuses,omitempty"`
	LivenessScores               []float32                    `protobuf:"fixed32,18,rep,packed,name=liveness_scores,json=liveness_score,proto3" json:"liveness_scores,omitempty"`
	LivenessInhouseErrors        []string                     `protobuf:"bytes,19,rep,name=liveness_inhouse_errors,proto3" json:"liveness_inhouse_errors,omitempty"`
	LivenessInhouseScores        []float32                    `protobuf:"fixed32,20,rep,packed,name=liveness_inhouse_scores,json=liveness_inhouse_score,proto3" json:"liveness_inhouse_scores,omitempty"`
	FacematchScores              []float32                    `protobuf:"fixed32,21,rep,packed,name=facematch_scores,json=facematch_score,proto3" json:"facematch_scores,omitempty"`
	CkycErrors                   []string                     `protobuf:"bytes,22,rep,name=ckyc_errors,proto3" json:"ckyc_errors,omitempty"`
	HashedPhoneNumber            string                       `protobuf:"bytes,23,opt,name=hashed_phone_number,json=hashed_number,proto3" json:"hashed_phone_number,omitempty"`
	FatherName                   *common.Name                 `protobuf:"bytes,24,opt,name=father_name,proto3" json:"father_name,omitempty"`
	MotherName                   *common.Name                 `protobuf:"bytes,25,opt,name=mother_name,proto3" json:"mother_name,omitempty"`
	OnboardingEkycNumberMismatch bool                         `protobuf:"varint,26,opt,name=onboarding_ekyc_number_mismatch,json=aadhar_number_mismatch,proto3" json:"onboarding_ekyc_number_mismatch,omitempty"`
	UserPanName                  *common.Name                 `protobuf:"bytes,27,opt,name=user_pan_name,proto3" json:"user_pan_name,omitempty"`
	OtpScores                    []float32                    `protobuf:"fixed32,28,rep,packed,name=otp_scores,json=otp_score,proto3" json:"otp_scores,omitempty"`
	CbDetails                    []*CreditReportAttributeInfo `protobuf:"bytes,29,rep,name=cb_details,proto3" json:"cb_details,omitempty"`
	EmploymentData               *EmploymentData              `protobuf:"bytes,30,opt,name=employment_data,proto3" json:"employment_data,omitempty"`
	DeviceDetails                *DeviceDetails               `protobuf:"bytes,31,opt,name=device_details,proto3" json:"device_details,omitempty"`
}

func (x *DetectRiskRequest) Reset() {
	*x = DetectRiskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectRiskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectRiskRequest) ProtoMessage() {}

func (x *DetectRiskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectRiskRequest.ProtoReflect.Descriptor instead.
func (*DetectRiskRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{0}
}

func (x *DetectRiskRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DetectRiskRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *DetectRiskRequest) GetReferrerActorId() string {
	if x != nil {
		return x.ReferrerActorId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/vendors/inhouse/risk.proto.
func (x *DetectRiskRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *DetectRiskRequest) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *DetectRiskRequest) GetCreditReportPresence() bool {
	if x != nil {
		return x.CreditReportPresence
	}
	return false
}

func (x *DetectRiskRequest) GetCreditReportDownloadConsent() bool {
	if x != nil {
		return x.CreditReportDownloadConsent
	}
	return false
}

// Deprecated: Marked as deprecated in api/vendors/inhouse/risk.proto.
func (x *DetectRiskRequest) GetDeviceManufacturer() string {
	if x != nil {
		return x.DeviceManufacturer
	}
	return ""
}

// Deprecated: Marked as deprecated in api/vendors/inhouse/risk.proto.
func (x *DetectRiskRequest) GetIsDevicePremium() bool {
	if x != nil {
		return x.IsDevicePremium
	}
	return false
}

func (x *DetectRiskRequest) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *DetectRiskRequest) GetGmailPanNameMatchScore() float32 {
	if x != nil {
		return x.GmailPanNameMatchScore
	}
	return 0
}

func (x *DetectRiskRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *DetectRiskRequest) GetScreenerMailCount() int32 {
	if x != nil {
		return x.ScreenerMailCount
	}
	return 0
}

func (x *DetectRiskRequest) GetLatitude() float32 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *DetectRiskRequest) GetLongitude() float32 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *DetectRiskRequest) GetThreshold() float32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *DetectRiskRequest) GetLivenessStatuses() []string {
	if x != nil {
		return x.LivenessStatuses
	}
	return nil
}

func (x *DetectRiskRequest) GetLivenessScores() []float32 {
	if x != nil {
		return x.LivenessScores
	}
	return nil
}

func (x *DetectRiskRequest) GetLivenessInhouseErrors() []string {
	if x != nil {
		return x.LivenessInhouseErrors
	}
	return nil
}

func (x *DetectRiskRequest) GetLivenessInhouseScores() []float32 {
	if x != nil {
		return x.LivenessInhouseScores
	}
	return nil
}

func (x *DetectRiskRequest) GetFacematchScores() []float32 {
	if x != nil {
		return x.FacematchScores
	}
	return nil
}

func (x *DetectRiskRequest) GetCkycErrors() []string {
	if x != nil {
		return x.CkycErrors
	}
	return nil
}

func (x *DetectRiskRequest) GetHashedPhoneNumber() string {
	if x != nil {
		return x.HashedPhoneNumber
	}
	return ""
}

func (x *DetectRiskRequest) GetFatherName() *common.Name {
	if x != nil {
		return x.FatherName
	}
	return nil
}

func (x *DetectRiskRequest) GetMotherName() *common.Name {
	if x != nil {
		return x.MotherName
	}
	return nil
}

func (x *DetectRiskRequest) GetOnboardingEkycNumberMismatch() bool {
	if x != nil {
		return x.OnboardingEkycNumberMismatch
	}
	return false
}

func (x *DetectRiskRequest) GetUserPanName() *common.Name {
	if x != nil {
		return x.UserPanName
	}
	return nil
}

func (x *DetectRiskRequest) GetOtpScores() []float32 {
	if x != nil {
		return x.OtpScores
	}
	return nil
}

func (x *DetectRiskRequest) GetCbDetails() []*CreditReportAttributeInfo {
	if x != nil {
		return x.CbDetails
	}
	return nil
}

func (x *DetectRiskRequest) GetEmploymentData() *EmploymentData {
	if x != nil {
		return x.EmploymentData
	}
	return nil
}

func (x *DetectRiskRequest) GetDeviceDetails() *DeviceDetails {
	if x != nil {
		return x.DeviceDetails
	}
	return nil
}

type CreditReportAttributeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttributeKey   string `protobuf:"bytes,1,opt,name=attribute_key,json=attributeKey,proto3" json:"attribute_key,omitempty"`
	AttributeValue string `protobuf:"bytes,2,opt,name=attribute_value,json=attributeValue,proto3" json:"attribute_value,omitempty"`
}

func (x *CreditReportAttributeInfo) Reset() {
	*x = CreditReportAttributeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditReportAttributeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditReportAttributeInfo) ProtoMessage() {}

func (x *CreditReportAttributeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditReportAttributeInfo.ProtoReflect.Descriptor instead.
func (*CreditReportAttributeInfo) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{1}
}

func (x *CreditReportAttributeInfo) GetAttributeKey() string {
	if x != nil {
		return x.AttributeKey
	}
	return ""
}

func (x *CreditReportAttributeInfo) GetAttributeValue() string {
	if x != nil {
		return x.AttributeValue
	}
	return ""
}

type DetectRiskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score     float32 `protobuf:"fixed32,1,opt,name=score,proto3" json:"score,omitempty"`
	Threshold float32 `protobuf:"fixed32,2,opt,name=threshold,proto3" json:"threshold,omitempty"`
	// bool flag to denote if the user is risky
	RiskyUser bool `protobuf:"varint,3,opt,name=risky_user,proto3" json:"risky_user,omitempty"`
	// response time of the API
	Time float32 `protobuf:"fixed32,4,opt,name=time,proto3" json:"time,omitempty"`
	// list of errors returned by the API
	Error []string `protobuf:"bytes,5,rep,name=error,proto3" json:"error,omitempty"`
}

func (x *DetectRiskResponse) Reset() {
	*x = DetectRiskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectRiskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectRiskResponse) ProtoMessage() {}

func (x *DetectRiskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectRiskResponse.ProtoReflect.Descriptor instead.
func (*DetectRiskResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{2}
}

func (x *DetectRiskResponse) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *DetectRiskResponse) GetThreshold() float32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *DetectRiskResponse) GetRiskyUser() bool {
	if x != nil {
		return x.RiskyUser
	}
	return false
}

func (x *DetectRiskResponse) GetTime() float32 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *DetectRiskResponse) GetError() []string {
	if x != nil {
		return x.Error
	}
	return nil
}

type DetectReOnboardingRiskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId   string `protobuf:"bytes,1,opt,name=actor_id,proto3" json:"actor_id,omitempty"`
	RequestId string `protobuf:"bytes,2,opt,name=request_id,proto3" json:"request_id,omitempty"`
	// Actor ID of the user who referred the current user
	ReferrerActorId string `protobuf:"bytes,3,opt,name=referrer_actor_id,proto3" json:"referrer_actor_id,omitempty"`
	// Deprecated: Marked as deprecated in api/vendors/inhouse/risk.proto.
	DeviceId                    string `protobuf:"bytes,4,opt,name=device_id,proto3" json:"device_id,omitempty"`
	EmailId                     string `protobuf:"bytes,5,opt,name=email_id,json=email,proto3" json:"email_id,omitempty"`
	CreditReportPresence        bool   `protobuf:"varint,6,opt,name=credit_report_presence,proto3" json:"credit_report_presence,omitempty"`
	CreditReportDownloadConsent bool   `protobuf:"varint,7,opt,name=credit_report_download_consent,proto3" json:"credit_report_download_consent,omitempty"`
	// Deprecated: Marked as deprecated in api/vendors/inhouse/risk.proto.
	DeviceManufacturer string `protobuf:"bytes,8,opt,name=device_manufacturer,proto3" json:"device_manufacturer,omitempty"`
	// Deprecated: Marked as deprecated in api/vendors/inhouse/risk.proto.
	IsDevicePremium bool  `protobuf:"varint,9,opt,name=is_device_premium,json=device_is_premium,proto3" json:"is_device_premium,omitempty"`
	Age             int32 `protobuf:"varint,10,opt,name=age,proto3" json:"age,omitempty"`
	// Name match score between gmail and pan name
	GmailPanNameMatchScore       float32               `protobuf:"fixed32,11,opt,name=gmail_pan_name_match_score,json=namematch_gmail_kyc,proto3" json:"gmail_pan_name_match_score,omitempty"`
	PhoneNumber                  string                `protobuf:"bytes,12,opt,name=phone_number,proto3" json:"phone_number,omitempty"`
	ScreenerMailCount            int32                 `protobuf:"varint,13,opt,name=screener_mail_count,proto3" json:"screener_mail_count,omitempty"`
	Latitude                     float32               `protobuf:"fixed32,14,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude                    float32               `protobuf:"fixed32,15,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Threshold                    float32               `protobuf:"fixed32,16,opt,name=threshold,proto3" json:"threshold,omitempty"`
	LivenessStatuses             []string              `protobuf:"bytes,17,rep,name=liveness_statuses,json=liveness_status,proto3" json:"liveness_statuses,omitempty"`
	LivenessScores               []float32             `protobuf:"fixed32,18,rep,packed,name=liveness_scores,json=liveness_score,proto3" json:"liveness_scores,omitempty"`
	LivenessInhouseErrors        []string              `protobuf:"bytes,19,rep,name=liveness_inhouse_errors,proto3" json:"liveness_inhouse_errors,omitempty"`
	LivenessInhouseScores        []float32             `protobuf:"fixed32,20,rep,packed,name=liveness_inhouse_scores,json=liveness_inhouse_score,proto3" json:"liveness_inhouse_scores,omitempty"`
	FacematchScores              []float32             `protobuf:"fixed32,21,rep,packed,name=facematch_scores,json=facematch_score,proto3" json:"facematch_scores,omitempty"`
	CkycErrors                   []string              `protobuf:"bytes,22,rep,name=ckyc_errors,proto3" json:"ckyc_errors,omitempty"`
	HashedPhoneNumber            string                `protobuf:"bytes,23,opt,name=hashed_phone_number,json=hashed_number,proto3" json:"hashed_phone_number,omitempty"`
	FatherName                   *common.Name          `protobuf:"bytes,24,opt,name=father_name,proto3" json:"father_name,omitempty"`
	MotherName                   *common.Name          `protobuf:"bytes,25,opt,name=mother_name,proto3" json:"mother_name,omitempty"`
	OnboardingEkycNumberMismatch bool                  `protobuf:"varint,26,opt,name=onboarding_ekyc_number_mismatch,json=aadhar_number_mismatch,proto3" json:"onboarding_ekyc_number_mismatch,omitempty"`
	UserPanName                  *common.Name          `protobuf:"bytes,27,opt,name=user_pan_name,proto3" json:"user_pan_name,omitempty"`
	OtpScores                    []float32             `protobuf:"fixed32,28,rep,packed,name=otp_scores,json=otp_score,proto3" json:"otp_scores,omitempty"`
	UserCity                     string                `protobuf:"bytes,30,opt,name=user_city,proto3" json:"user_city,omitempty"`
	UserPostalCode               string                `protobuf:"bytes,31,opt,name=user_postal_code,proto3" json:"user_postal_code,omitempty"`
	KycLevel                     string                `protobuf:"bytes,32,opt,name=kyc_level,proto3" json:"kyc_level,omitempty"`
	OverallAfuStatus             string                `protobuf:"bytes,33,opt,name=overall_afu_status,proto3" json:"overall_afu_status,omitempty"`
	AuthFactors                  []string              `protobuf:"bytes,34,rep,name=auth_factors,proto3" json:"auth_factors,omitempty"`
	FailureReason                string                `protobuf:"bytes,35,opt,name=failure_reason,proto3" json:"failure_reason,omitempty"`
	NewValues                    *afu.AuthFactorValues `protobuf:"bytes,36,opt,name=new_values,proto3" json:"new_values,omitempty"`
	AfuAttemptNum                int64                 `protobuf:"varint,37,opt,name=afu_attempt_num,proto3" json:"afu_attempt_num,omitempty"`
	AfuLivenessScore             []float32             `protobuf:"fixed32,38,rep,packed,name=afu_liveness_score,proto3" json:"afu_liveness_score,omitempty"`
	AfuOtpScore                  []float32             `protobuf:"fixed32,39,rep,packed,name=afu_otp_score,proto3" json:"afu_otp_score,omitempty"`
	AfuFacematchScore            []float32             `protobuf:"fixed32,40,rep,packed,name=afu_facematch_score,proto3" json:"afu_facematch_score,omitempty"`
	VendorRequestStatuses        []string              `protobuf:"bytes,41,rep,name=vendor_request_statuses,proto3" json:"vendor_request_statuses,omitempty"`
	EpifiEmailPhoneNumUpdate     string                `protobuf:"bytes,42,opt,name=epifi_email_phone_num_update,proto3" json:"epifi_email_phone_num_update,omitempty"`
	EpifiDeviceUpdate            string                `protobuf:"bytes,43,opt,name=epifi_device_update,proto3" json:"epifi_device_update,omitempty"`
	CurrentValues                *afu.AuthFactorValues `protobuf:"bytes,44,opt,name=current_values,proto3" json:"current_values,omitempty"`
	// Deprecated: Marked as deprecated in api/vendors/inhouse/risk.proto.
	NewDevice                          *common.Device               `protobuf:"bytes,45,opt,name=new_device,proto3" json:"new_device,omitempty"`
	ActorAuthState                     *afu.ActorAuthState          `protobuf:"bytes,46,opt,name=actor_auth_state,proto3" json:"actor_auth_state,omitempty"`
	UserState                          string                       `protobuf:"bytes,47,opt,name=user_state,proto3" json:"user_state,omitempty"`
	AfuLatitude                        float32                      `protobuf:"fixed32,48,opt,name=afu_latitude,proto3" json:"afu_latitude,omitempty"`
	AfuLongitude                       float32                      `protobuf:"fixed32,49,opt,name=afu_longitude,proto3" json:"afu_longitude,omitempty"`
	OnboardingCompletedAt              string                       `protobuf:"bytes,50,opt,name=onboarding_completed_at,proto3" json:"onboarding_completed_at,omitempty"`
	CbDetails                          []*CreditReportAttributeInfo `protobuf:"bytes,51,rep,name=cb_details,proto3" json:"cb_details,omitempty"`
	AfuLivenessStatuses                []string                     `protobuf:"bytes,52,rep,name=afu_liveness_statuses,json=afu_liveness_status,proto3" json:"afu_liveness_statuses,omitempty"`
	EmploymentData                     *EmploymentData              `protobuf:"bytes,53,opt,name=employment_data,proto3" json:"employment_data,omitempty"`
	DeviceDetails                      *DeviceDetails               `protobuf:"bytes,54,opt,name=device_details,proto3" json:"device_details,omitempty"`
	AfuAttempts                        []*AFUAttempt                `protobuf:"bytes,55,rep,name=afu_attempts,proto3" json:"afu_attempts,omitempty"`
	ScreenerChecksInfo                 *ScreenerChecksInfo          `protobuf:"bytes,56,opt,name=screener_checks_info,proto3" json:"screener_checks_info,omitempty"`
	OnboardingModelRiskScore           float32                      `protobuf:"fixed32,58,opt,name=onboarding_model_risk_score,proto3" json:"onboarding_model_risk_score,omitempty"`
	AccountInfo                        *AccountInfo                 `protobuf:"bytes,59,opt,name=account_info,proto3" json:"account_info,omitempty"`
	OldDeviceDetails                   *DeviceDetails               `protobuf:"bytes,60,opt,name=old_device_details,proto3" json:"old_device_details,omitempty"`
	IsCreditReportDownloadConsentGiven string                       `protobuf:"bytes,61,opt,name=is_credit_report_download_consent_given,proto3" json:"is_credit_report_download_consent_given,omitempty"`
	// user image will be removed
	Profile *user.Profile `protobuf:"bytes,62,opt,name=profile,proto3" json:"profile,omitempty"`
}

func (x *DetectReOnboardingRiskRequest) Reset() {
	*x = DetectReOnboardingRiskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectReOnboardingRiskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectReOnboardingRiskRequest) ProtoMessage() {}

func (x *DetectReOnboardingRiskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectReOnboardingRiskRequest.ProtoReflect.Descriptor instead.
func (*DetectReOnboardingRiskRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{3}
}

func (x *DetectReOnboardingRiskRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetReferrerActorId() string {
	if x != nil {
		return x.ReferrerActorId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/vendors/inhouse/risk.proto.
func (x *DetectReOnboardingRiskRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetCreditReportPresence() bool {
	if x != nil {
		return x.CreditReportPresence
	}
	return false
}

func (x *DetectReOnboardingRiskRequest) GetCreditReportDownloadConsent() bool {
	if x != nil {
		return x.CreditReportDownloadConsent
	}
	return false
}

// Deprecated: Marked as deprecated in api/vendors/inhouse/risk.proto.
func (x *DetectReOnboardingRiskRequest) GetDeviceManufacturer() string {
	if x != nil {
		return x.DeviceManufacturer
	}
	return ""
}

// Deprecated: Marked as deprecated in api/vendors/inhouse/risk.proto.
func (x *DetectReOnboardingRiskRequest) GetIsDevicePremium() bool {
	if x != nil {
		return x.IsDevicePremium
	}
	return false
}

func (x *DetectReOnboardingRiskRequest) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *DetectReOnboardingRiskRequest) GetGmailPanNameMatchScore() float32 {
	if x != nil {
		return x.GmailPanNameMatchScore
	}
	return 0
}

func (x *DetectReOnboardingRiskRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetScreenerMailCount() int32 {
	if x != nil {
		return x.ScreenerMailCount
	}
	return 0
}

func (x *DetectReOnboardingRiskRequest) GetLatitude() float32 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *DetectReOnboardingRiskRequest) GetLongitude() float32 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *DetectReOnboardingRiskRequest) GetThreshold() float32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *DetectReOnboardingRiskRequest) GetLivenessStatuses() []string {
	if x != nil {
		return x.LivenessStatuses
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetLivenessScores() []float32 {
	if x != nil {
		return x.LivenessScores
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetLivenessInhouseErrors() []string {
	if x != nil {
		return x.LivenessInhouseErrors
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetLivenessInhouseScores() []float32 {
	if x != nil {
		return x.LivenessInhouseScores
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetFacematchScores() []float32 {
	if x != nil {
		return x.FacematchScores
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetCkycErrors() []string {
	if x != nil {
		return x.CkycErrors
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetHashedPhoneNumber() string {
	if x != nil {
		return x.HashedPhoneNumber
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetFatherName() *common.Name {
	if x != nil {
		return x.FatherName
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetMotherName() *common.Name {
	if x != nil {
		return x.MotherName
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetOnboardingEkycNumberMismatch() bool {
	if x != nil {
		return x.OnboardingEkycNumberMismatch
	}
	return false
}

func (x *DetectReOnboardingRiskRequest) GetUserPanName() *common.Name {
	if x != nil {
		return x.UserPanName
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetOtpScores() []float32 {
	if x != nil {
		return x.OtpScores
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetUserCity() string {
	if x != nil {
		return x.UserCity
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetUserPostalCode() string {
	if x != nil {
		return x.UserPostalCode
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetKycLevel() string {
	if x != nil {
		return x.KycLevel
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetOverallAfuStatus() string {
	if x != nil {
		return x.OverallAfuStatus
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetAuthFactors() []string {
	if x != nil {
		return x.AuthFactors
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetNewValues() *afu.AuthFactorValues {
	if x != nil {
		return x.NewValues
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetAfuAttemptNum() int64 {
	if x != nil {
		return x.AfuAttemptNum
	}
	return 0
}

func (x *DetectReOnboardingRiskRequest) GetAfuLivenessScore() []float32 {
	if x != nil {
		return x.AfuLivenessScore
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetAfuOtpScore() []float32 {
	if x != nil {
		return x.AfuOtpScore
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetAfuFacematchScore() []float32 {
	if x != nil {
		return x.AfuFacematchScore
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetVendorRequestStatuses() []string {
	if x != nil {
		return x.VendorRequestStatuses
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetEpifiEmailPhoneNumUpdate() string {
	if x != nil {
		return x.EpifiEmailPhoneNumUpdate
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetEpifiDeviceUpdate() string {
	if x != nil {
		return x.EpifiDeviceUpdate
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetCurrentValues() *afu.AuthFactorValues {
	if x != nil {
		return x.CurrentValues
	}
	return nil
}

// Deprecated: Marked as deprecated in api/vendors/inhouse/risk.proto.
func (x *DetectReOnboardingRiskRequest) GetNewDevice() *common.Device {
	if x != nil {
		return x.NewDevice
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetActorAuthState() *afu.ActorAuthState {
	if x != nil {
		return x.ActorAuthState
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetUserState() string {
	if x != nil {
		return x.UserState
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetAfuLatitude() float32 {
	if x != nil {
		return x.AfuLatitude
	}
	return 0
}

func (x *DetectReOnboardingRiskRequest) GetAfuLongitude() float32 {
	if x != nil {
		return x.AfuLongitude
	}
	return 0
}

func (x *DetectReOnboardingRiskRequest) GetOnboardingCompletedAt() string {
	if x != nil {
		return x.OnboardingCompletedAt
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetCbDetails() []*CreditReportAttributeInfo {
	if x != nil {
		return x.CbDetails
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetAfuLivenessStatuses() []string {
	if x != nil {
		return x.AfuLivenessStatuses
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetEmploymentData() *EmploymentData {
	if x != nil {
		return x.EmploymentData
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetDeviceDetails() *DeviceDetails {
	if x != nil {
		return x.DeviceDetails
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetAfuAttempts() []*AFUAttempt {
	if x != nil {
		return x.AfuAttempts
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetScreenerChecksInfo() *ScreenerChecksInfo {
	if x != nil {
		return x.ScreenerChecksInfo
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetOnboardingModelRiskScore() float32 {
	if x != nil {
		return x.OnboardingModelRiskScore
	}
	return 0
}

func (x *DetectReOnboardingRiskRequest) GetAccountInfo() *AccountInfo {
	if x != nil {
		return x.AccountInfo
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetOldDeviceDetails() *DeviceDetails {
	if x != nil {
		return x.OldDeviceDetails
	}
	return nil
}

func (x *DetectReOnboardingRiskRequest) GetIsCreditReportDownloadConsentGiven() string {
	if x != nil {
		return x.IsCreditReportDownloadConsentGiven
	}
	return ""
}

func (x *DetectReOnboardingRiskRequest) GetProfile() *user.Profile {
	if x != nil {
		return x.Profile
	}
	return nil
}

type DetectReOnboardingRiskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score     float32 `protobuf:"fixed32,1,opt,name=score,proto3" json:"score,omitempty"`
	Threshold float32 `protobuf:"fixed32,2,opt,name=threshold,proto3" json:"threshold,omitempty"`
	// bool flag to denote if the user is risky
	RiskyUser bool `protobuf:"varint,3,opt,name=risky_user,proto3" json:"risky_user,omitempty"`
	// response time of the API
	Time float32 `protobuf:"fixed32,4,opt,name=time,proto3" json:"time,omitempty"`
	// list of errors returned by the API
	Error []string `protobuf:"bytes,5,rep,name=error,proto3" json:"error,omitempty"`
}

func (x *DetectReOnboardingRiskResponse) Reset() {
	*x = DetectReOnboardingRiskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectReOnboardingRiskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectReOnboardingRiskResponse) ProtoMessage() {}

func (x *DetectReOnboardingRiskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectReOnboardingRiskResponse.ProtoReflect.Descriptor instead.
func (*DetectReOnboardingRiskResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{4}
}

func (x *DetectReOnboardingRiskResponse) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *DetectReOnboardingRiskResponse) GetThreshold() float32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *DetectReOnboardingRiskResponse) GetRiskyUser() bool {
	if x != nil {
		return x.RiskyUser
	}
	return false
}

func (x *DetectReOnboardingRiskResponse) GetTime() float32 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *DetectReOnboardingRiskResponse) GetError() []string {
	if x != nil {
		return x.Error
	}
	return nil
}

type DetectLocationRiskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId   string `protobuf:"bytes,1,opt,name=actor_id,proto3" json:"actor_id,omitempty"`
	RequestId string `protobuf:"bytes,2,opt,name=request_id,proto3" json:"request_id,omitempty"`
	Latitude  string `protobuf:"bytes,3,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude string `protobuf:"bytes,4,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Pincode   string `protobuf:"bytes,5,opt,name=pincode,proto3" json:"pincode,omitempty"`
}

func (x *DetectLocationRiskRequest) Reset() {
	*x = DetectLocationRiskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectLocationRiskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectLocationRiskRequest) ProtoMessage() {}

func (x *DetectLocationRiskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectLocationRiskRequest.ProtoReflect.Descriptor instead.
func (*DetectLocationRiskRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{5}
}

func (x *DetectLocationRiskRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DetectLocationRiskRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *DetectLocationRiskRequest) GetLatitude() string {
	if x != nil {
		return x.Latitude
	}
	return ""
}

func (x *DetectLocationRiskRequest) GetLongitude() string {
	if x != nil {
		return x.Longitude
	}
	return ""
}

func (x *DetectLocationRiskRequest) GetPincode() string {
	if x != nil {
		return x.Pincode
	}
	return ""
}

type DetectLocationRiskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score    float32 `protobuf:"fixed32,1,opt,name=score,proto3" json:"score,omitempty"`
	Severity string  `protobuf:"bytes,2,opt,name=severity,proto3" json:"severity,omitempty"`
	// location risk model version
	Version string `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *DetectLocationRiskResponse) Reset() {
	*x = DetectLocationRiskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetectLocationRiskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectLocationRiskResponse) ProtoMessage() {}

func (x *DetectLocationRiskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectLocationRiskResponse.ProtoReflect.Descriptor instead.
func (*DetectLocationRiskResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{6}
}

func (x *DetectLocationRiskResponse) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *DetectLocationRiskResponse) GetSeverity() string {
	if x != nil {
		return x.Severity
	}
	return ""
}

func (x *DetectLocationRiskResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// EmploymentData will contain the information user has provided about their employment such as salary, employee info.
type EmploymentData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmploymentType string `protobuf:"bytes,1,opt,name=employment_type,proto3" json:"employment_type,omitempty"`
	// company name selected/entered by salaried users
	CompanyName       string                `protobuf:"bytes,2,opt,name=company_name,proto3" json:"company_name,omitempty"`
	AnnualSalaryRange *EmploymentData_Range `protobuf:"bytes,3,opt,name=annual_salary_range,proto3" json:"annual_salary_range,omitempty"`
	// if company is registered with EPFO
	IsCompanyEpfRegistered bool `protobuf:"varint,4,opt,name=is_company_epf_registered,proto3" json:"is_company_epf_registered,omitempty"`
}

func (x *EmploymentData) Reset() {
	*x = EmploymentData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmploymentData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmploymentData) ProtoMessage() {}

func (x *EmploymentData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmploymentData.ProtoReflect.Descriptor instead.
func (*EmploymentData) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{7}
}

func (x *EmploymentData) GetEmploymentType() string {
	if x != nil {
		return x.EmploymentType
	}
	return ""
}

func (x *EmploymentData) GetCompanyName() string {
	if x != nil {
		return x.CompanyName
	}
	return ""
}

func (x *EmploymentData) GetAnnualSalaryRange() *EmploymentData_Range {
	if x != nil {
		return x.AnnualSalaryRange
	}
	return nil
}

func (x *EmploymentData) GetIsCompanyEpfRegistered() bool {
	if x != nil {
		return x.IsCompanyEpfRegistered
	}
	return false
}

// DeviceDetails contains device details and device properties.
type DeviceDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Manufacturer string `protobuf:"bytes,8,opt,name=manufacturer,proto3" json:"manufacturer,omitempty"`
	IsPremium    bool   `protobuf:"varint,9,opt,name=is_premium,proto3" json:"is_premium,omitempty"`
	// app version in semver format e.g. 4.2.0
	AppVersion string `protobuf:"bytes,5,opt,name=app_version,proto3" json:"app_version,omitempty"`
	Language   string `protobuf:"bytes,6,opt,name=language,proto3" json:"language,omitempty"`
	SwVersion  string `protobuf:"bytes,7,opt,name=sw_version,proto3" json:"sw_version,omitempty"`
	// list of all apps installed
	InstalledApps []*typesv2.UserDeviceInstalledAppInfo `protobuf:"bytes,10,rep,name=installed_apps,proto3" json:"installed_apps,omitempty"`
	Model         string                                `protobuf:"bytes,11,opt,name=model,proto3" json:"model,omitempty"`
}

func (x *DeviceDetails) Reset() {
	*x = DeviceDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceDetails) ProtoMessage() {}

func (x *DeviceDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceDetails.ProtoReflect.Descriptor instead.
func (*DeviceDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{8}
}

func (x *DeviceDetails) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeviceDetails) GetManufacturer() string {
	if x != nil {
		return x.Manufacturer
	}
	return ""
}

func (x *DeviceDetails) GetIsPremium() bool {
	if x != nil {
		return x.IsPremium
	}
	return false
}

func (x *DeviceDetails) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *DeviceDetails) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *DeviceDetails) GetSwVersion() string {
	if x != nil {
		return x.SwVersion
	}
	return ""
}

func (x *DeviceDetails) GetInstalledApps() []*typesv2.UserDeviceInstalledAppInfo {
	if x != nil {
		return x.InstalledApps
	}
	return nil
}

func (x *DeviceDetails) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

// AFUAttempt contains afu data for a single afu attempt such as liveness and facematch scores.
type AFUAttempt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OverallStatus    string    `protobuf:"bytes,1,opt,name=overall_status,proto3" json:"overall_status,omitempty"`
	AuthFactors      []string  `protobuf:"bytes,2,rep,name=auth_factors,proto3" json:"auth_factors,omitempty"`
	LivenessScores   []float32 `protobuf:"fixed32,3,rep,packed,name=liveness_scores,proto3" json:"liveness_scores,omitempty"`
	OtpScores        []float32 `protobuf:"fixed32,4,rep,packed,name=otp_scores,proto3" json:"otp_scores,omitempty"`
	FacematchScores  []float32 `protobuf:"fixed32,5,rep,packed,name=facematch_scores,proto3" json:"facematch_scores,omitempty"`
	CreatedAt        string    `protobuf:"bytes,6,opt,name=created_at,proto3" json:"created_at,omitempty"`
	LivenessStatuses []string  `protobuf:"bytes,7,rep,name=liveness_statuses,proto3" json:"liveness_statuses,omitempty"`
}

func (x *AFUAttempt) Reset() {
	*x = AFUAttempt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AFUAttempt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AFUAttempt) ProtoMessage() {}

func (x *AFUAttempt) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AFUAttempt.ProtoReflect.Descriptor instead.
func (*AFUAttempt) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{9}
}

func (x *AFUAttempt) GetOverallStatus() string {
	if x != nil {
		return x.OverallStatus
	}
	return ""
}

func (x *AFUAttempt) GetAuthFactors() []string {
	if x != nil {
		return x.AuthFactors
	}
	return nil
}

func (x *AFUAttempt) GetLivenessScores() []float32 {
	if x != nil {
		return x.LivenessScores
	}
	return nil
}

func (x *AFUAttempt) GetOtpScores() []float32 {
	if x != nil {
		return x.OtpScores
	}
	return nil
}

func (x *AFUAttempt) GetFacematchScores() []float32 {
	if x != nil {
		return x.FacematchScores
	}
	return nil
}

func (x *AFUAttempt) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *AFUAttempt) GetLivenessStatuses() []string {
	if x != nil {
		return x.LivenessStatuses
	}
	return nil
}

// ScreenerChecksInfo contains screener attempts and related checks.
type ScreenerChecksInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreditReportFailCount int32 `protobuf:"varint,1,opt,name=credit_report_fail_count,proto3" json:"credit_report_fail_count,omitempty"`
}

func (x *ScreenerChecksInfo) Reset() {
	*x = ScreenerChecksInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenerChecksInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenerChecksInfo) ProtoMessage() {}

func (x *ScreenerChecksInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenerChecksInfo.ProtoReflect.Descriptor instead.
func (*ScreenerChecksInfo) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{10}
}

func (x *ScreenerChecksInfo) GetCreditReportFailCount() int32 {
	if x != nil {
		return x.CreditReportFailCount
	}
	return 0
}

// AccountInfo contains info related to user account.
type AccountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tier      string `protobuf:"bytes,1,opt,name=tier,proto3" json:"tier,omitempty"`
	CreatedAt string `protobuf:"bytes,2,opt,name=created_at,proto3" json:"created_at,omitempty"`
}

func (x *AccountInfo) Reset() {
	*x = AccountInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountInfo) ProtoMessage() {}

func (x *AccountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountInfo.ProtoReflect.Descriptor instead.
func (*AccountInfo) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{11}
}

func (x *AccountInfo) GetTier() string {
	if x != nil {
		return x.Tier
	}
	return ""
}

func (x *AccountInfo) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type GetCasePrioritisationScoreRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AlertWithRuleDetails []*GetCasePrioritisationScoreRequest_AlertWithRuleDetails `protobuf:"bytes,1,rep,name=alert_with_rule_details,proto3" json:"alert_with_rule_details,omitempty"`
	ActorId              string                                                    `protobuf:"bytes,2,opt,name=actor_id,proto3" json:"actor_id,omitempty"`
	RequestId            string                                                    `protobuf:"bytes,3,opt,name=request_id,proto3" json:"request_id,omitempty"`
	ModelVersion         string                                                    `protobuf:"bytes,4,opt,name=model_version,proto3" json:"model_version,omitempty"`
}

func (x *GetCasePrioritisationScoreRequest) Reset() {
	*x = GetCasePrioritisationScoreRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCasePrioritisationScoreRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCasePrioritisationScoreRequest) ProtoMessage() {}

func (x *GetCasePrioritisationScoreRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCasePrioritisationScoreRequest.ProtoReflect.Descriptor instead.
func (*GetCasePrioritisationScoreRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{12}
}

func (x *GetCasePrioritisationScoreRequest) GetAlertWithRuleDetails() []*GetCasePrioritisationScoreRequest_AlertWithRuleDetails {
	if x != nil {
		return x.AlertWithRuleDetails
	}
	return nil
}

func (x *GetCasePrioritisationScoreRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetCasePrioritisationScoreRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetCasePrioritisationScoreRequest) GetModelVersion() string {
	if x != nil {
		return x.ModelVersion
	}
	return ""
}

type ModelResponseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Score float32 `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty"`
}

func (x *ModelResponseInfo) Reset() {
	*x = ModelResponseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelResponseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelResponseInfo) ProtoMessage() {}

func (x *ModelResponseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelResponseInfo.ProtoReflect.Descriptor instead.
func (*ModelResponseInfo) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{13}
}

func (x *ModelResponseInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelResponseInfo) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

type GetCasePrioritisationScoreResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId    string               `protobuf:"bytes,1,opt,name=request_id,proto3" json:"request_id,omitempty"`
	ModelVersion string               `protobuf:"bytes,2,opt,name=model_version,proto3" json:"model_version,omitempty"`
	Score        float32              `protobuf:"fixed32,3,opt,name=score,proto3" json:"score,omitempty"`
	ModelInfo    []*ModelResponseInfo `protobuf:"bytes,4,rep,name=model_info,json=modelInfo,proto3" json:"model_info,omitempty"`
}

func (x *GetCasePrioritisationScoreResponse) Reset() {
	*x = GetCasePrioritisationScoreResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCasePrioritisationScoreResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCasePrioritisationScoreResponse) ProtoMessage() {}

func (x *GetCasePrioritisationScoreResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCasePrioritisationScoreResponse.ProtoReflect.Descriptor instead.
func (*GetCasePrioritisationScoreResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{14}
}

func (x *GetCasePrioritisationScoreResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetCasePrioritisationScoreResponse) GetModelVersion() string {
	if x != nil {
		return x.ModelVersion
	}
	return ""
}

func (x *GetCasePrioritisationScoreResponse) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *GetCasePrioritisationScoreResponse) GetModelInfo() []*ModelResponseInfo {
	if x != nil {
		return x.ModelInfo
	}
	return nil
}

type EmploymentData_Range struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinValue int32 `protobuf:"varint,1,opt,name=min_value,proto3" json:"min_value,omitempty"`
	MaxValue int32 `protobuf:"varint,2,opt,name=max_value,proto3" json:"max_value,omitempty"`
}

func (x *EmploymentData_Range) Reset() {
	*x = EmploymentData_Range{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmploymentData_Range) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmploymentData_Range) ProtoMessage() {}

func (x *EmploymentData_Range) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmploymentData_Range.ProtoReflect.Descriptor instead.
func (*EmploymentData_Range) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{7, 0}
}

func (x *EmploymentData_Range) GetMinValue() int32 {
	if x != nil {
		return x.MinValue
	}
	return 0
}

func (x *EmploymentData_Range) GetMaxValue() int32 {
	if x != nil {
		return x.MaxValue
	}
	return 0
}

type GetCasePrioritisationScoreRequest_Alert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CaseId    string `protobuf:"bytes,1,opt,name=case_id,proto3" json:"case_id,omitempty"`
	CreatedAt string `protobuf:"bytes,2,opt,name=created_at,proto3" json:"created_at,omitempty"`
}

func (x *GetCasePrioritisationScoreRequest_Alert) Reset() {
	*x = GetCasePrioritisationScoreRequest_Alert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCasePrioritisationScoreRequest_Alert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCasePrioritisationScoreRequest_Alert) ProtoMessage() {}

func (x *GetCasePrioritisationScoreRequest_Alert) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCasePrioritisationScoreRequest_Alert.ProtoReflect.Descriptor instead.
func (*GetCasePrioritisationScoreRequest_Alert) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{12, 0}
}

func (x *GetCasePrioritisationScoreRequest_Alert) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *GetCasePrioritisationScoreRequest_Alert) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type GetCasePrioritisationScoreRequest_Rule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetCasePrioritisationScoreRequest_Rule) Reset() {
	*x = GetCasePrioritisationScoreRequest_Rule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCasePrioritisationScoreRequest_Rule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCasePrioritisationScoreRequest_Rule) ProtoMessage() {}

func (x *GetCasePrioritisationScoreRequest_Rule) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCasePrioritisationScoreRequest_Rule.ProtoReflect.Descriptor instead.
func (*GetCasePrioritisationScoreRequest_Rule) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{12, 1}
}

func (x *GetCasePrioritisationScoreRequest_Rule) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetCasePrioritisationScoreRequest_AlertWithRuleDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Alert *GetCasePrioritisationScoreRequest_Alert `protobuf:"bytes,1,opt,name=alert,proto3" json:"alert,omitempty"`
	Rule  *GetCasePrioritisationScoreRequest_Rule  `protobuf:"bytes,2,opt,name=rule,proto3" json:"rule,omitempty"`
}

func (x *GetCasePrioritisationScoreRequest_AlertWithRuleDetails) Reset() {
	*x = GetCasePrioritisationScoreRequest_AlertWithRuleDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_inhouse_risk_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCasePrioritisationScoreRequest_AlertWithRuleDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCasePrioritisationScoreRequest_AlertWithRuleDetails) ProtoMessage() {}

func (x *GetCasePrioritisationScoreRequest_AlertWithRuleDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_inhouse_risk_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCasePrioritisationScoreRequest_AlertWithRuleDetails.ProtoReflect.Descriptor instead.
func (*GetCasePrioritisationScoreRequest_AlertWithRuleDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_inhouse_risk_proto_rawDescGZIP(), []int{12, 2}
}

func (x *GetCasePrioritisationScoreRequest_AlertWithRuleDetails) GetAlert() *GetCasePrioritisationScoreRequest_Alert {
	if x != nil {
		return x.Alert
	}
	return nil
}

func (x *GetCasePrioritisationScoreRequest_AlertWithRuleDetails) GetRule() *GetCasePrioritisationScoreRequest_Rule {
	if x != nil {
		return x.Rule
	}
	return nil
}

var File_api_vendors_inhouse_risk_proto protoreflect.FileDescriptor

var file_api_vendors_inhouse_risk_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x69, 0x6e,
	0x68, 0x6f, 0x75, 0x73, 0x65, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73,
	0x65, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xd1, 0x0b, 0x0a, 0x11, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x12, 0x2c, 0x0a, 0x11, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x72, 0x5f, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x12, 0x20, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x12, 0x17, 0x0a, 0x08, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x36, 0x0a, 0x16, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x72, 0x65,
	0x73, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65,
	0x6e, 0x63, 0x65, 0x12, 0x46, 0x0a, 0x1e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1e, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x13, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x75, 0x66, 0x61, 0x63, 0x74, 0x75, 0x72,
	0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x13, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x75, 0x66, 0x61, 0x63, 0x74, 0x75, 0x72, 0x65,
	0x72, 0x12, 0x30, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70,
	0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x11, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x6d,
	0x69, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x37, 0x0a, 0x1a, 0x67, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x70,
	0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x6e, 0x61, 0x6d, 0x65, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x5f, 0x67, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x6b, 0x79, 0x63, 0x12, 0x22,
	0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x30, 0x0a, 0x13, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x13, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x2a, 0x0a, 0x11,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65,
	0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28,
	0x02, 0x52, 0x0e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x38, 0x0a, 0x17, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e,
	0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x13, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x17, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x68,
	0x6f, 0x75, 0x73, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x37, 0x0a, 0x17, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x02, 0x52, 0x16, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0f,
	0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x16,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x12, 0x2a, 0x0a, 0x13, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x3a, 0x0a,
	0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0b, 0x66, 0x61,
	0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0b, 0x6d, 0x6f, 0x74,
	0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x1f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f,
	0x6d, 0x69, 0x73, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16,
	0x61, 0x61, 0x64, 0x68, 0x61, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6d, 0x69,
	0x73, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x3e, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70,
	0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x61,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x74, 0x70, 0x5f, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x73, 0x18, 0x1c, 0x20, 0x03, 0x28, 0x02, 0x52, 0x09, 0x6f, 0x74, 0x70, 0x5f,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x4a, 0x0a, 0x0a, 0x63, 0x62, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x63, 0x62, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x49, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x45, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0f, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x12, 0x46, 0x0a, 0x0e,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x1f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69,
	0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x22, 0x69, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x92, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x69,
	0x73, 0x6b, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x72, 0x69, 0x73, 0x6b, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x22, 0xf2, 0x17, 0x0a, 0x1d, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52,
	0x65, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x69, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x12, 0x2c, 0x0a, 0x11, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x72, 0x5f, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x12, 0x20, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x12, 0x17, 0x0a, 0x08, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x36, 0x0a, 0x16, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x72, 0x65,
	0x73, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x73, 0x65,
	0x6e, 0x63, 0x65, 0x12, 0x46, 0x0a, 0x1e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1e, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x13, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x75, 0x66, 0x61, 0x63, 0x74, 0x75, 0x72,
	0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x13, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x75, 0x66, 0x61, 0x63, 0x74, 0x75, 0x72, 0x65,
	0x72, 0x12, 0x30, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70,
	0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x11, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x6d,
	0x69, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x37, 0x0a, 0x1a, 0x67, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x70,
	0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x6e, 0x61, 0x6d, 0x65, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x5f, 0x67, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x6b, 0x79, 0x63, 0x12, 0x22,
	0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x30, 0x0a, 0x13, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x13, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x2a, 0x0a, 0x11,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65,
	0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28,
	0x02, 0x52, 0x0e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x38, 0x0a, 0x17, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e,
	0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x13, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x17, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x68,
	0x6f, 0x75, 0x73, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x37, 0x0a, 0x17, 0x6c,
	0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x02, 0x52, 0x16, 0x6c, 0x69,
	0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0f,
	0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x16,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x73, 0x12, 0x2a, 0x0a, 0x13, 0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x68, 0x61, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x3a, 0x0a,
	0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0b, 0x66, 0x61,
	0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0b, 0x6d, 0x6f, 0x74,
	0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x1f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f,
	0x6d, 0x69, 0x73, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16,
	0x61, 0x61, 0x64, 0x68, 0x61, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6d, 0x69,
	0x73, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x3e, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70,
	0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x61,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x74, 0x70, 0x5f, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x73, 0x18, 0x1c, 0x20, 0x03, 0x28, 0x02, 0x52, 0x09, 0x6f, 0x74, 0x70, 0x5f,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x69,
	0x74, 0x79, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x69, 0x74, 0x79, 0x12, 0x2a, 0x0a, 0x10, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x6f, 0x73, 0x74,
	0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x6b, 0x79, 0x63, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x20, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6b, 0x79, 0x63, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x2e, 0x0a,
	0x12, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x66, 0x75, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6f, 0x76, 0x65, 0x72, 0x61,
	0x6c, 0x6c, 0x5f, 0x61, 0x66, 0x75, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a,
	0x0c, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x22, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x73, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x0a, 0x6e, 0x65, 0x77,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x0a, 0x6e, 0x65, 0x77, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x66, 0x75, 0x5f, 0x61, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x25, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f,
	0x61, 0x66, 0x75, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x12,
	0x2e, 0x0a, 0x12, 0x61, 0x66, 0x75, 0x5f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x26, 0x20, 0x03, 0x28, 0x02, 0x52, 0x12, 0x61, 0x66, 0x75,
	0x5f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x24, 0x0a, 0x0d, 0x61, 0x66, 0x75, 0x5f, 0x6f, 0x74, 0x70, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x27, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0d, 0x61, 0x66, 0x75, 0x5f, 0x6f, 0x74, 0x70, 0x5f,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x61, 0x66, 0x75, 0x5f, 0x66, 0x61, 0x63,
	0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x28, 0x20, 0x03,
	0x28, 0x02, 0x52, 0x13, 0x61, 0x66, 0x75, 0x5f, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x38, 0x0a, 0x17, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x65, 0x73, 0x18, 0x29, 0x20, 0x03, 0x28, 0x09, 0x52, 0x17, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65,
	0x73, 0x12, 0x42, 0x0a, 0x1c, 0x65, 0x70, 0x69, 0x66, 0x69, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x65, 0x70, 0x69, 0x66, 0x69, 0x5f, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x65, 0x70, 0x69, 0x66, 0x69, 0x5f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x2b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x65, 0x70, 0x69, 0x66, 0x69, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x42, 0x0a, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x46,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x0e, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x3e, 0x0a, 0x0a, 0x6e,
	0x65, 0x77, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x0a, 0x6e, 0x65, 0x77, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x10, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x2e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x61, 0x66, 0x75,
	0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x10, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x2f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x66, 0x75, 0x5f, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64,
	0x65, 0x18, 0x30, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x61, 0x66, 0x75, 0x5f, 0x6c, 0x61, 0x74,
	0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x66, 0x75, 0x5f, 0x6c, 0x6f, 0x6e,
	0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x31, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x61, 0x66,
	0x75, 0x5f, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x38, 0x0a, 0x17, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x32, 0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x12, 0x4a, 0x0a, 0x0a, 0x63, 0x62, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x33, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x63, 0x62, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x32, 0x0a, 0x15, 0x61, 0x66, 0x75, 0x5f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x34, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x13, 0x61, 0x66, 0x75, 0x5f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x35, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65,
	0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x46, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x36, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3f, 0x0a, 0x0c, 0x61, 0x66, 0x75, 0x5f,
	0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x37, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65,
	0x2e, 0x41, 0x46, 0x55, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x0c, 0x61, 0x66, 0x75,
	0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x57, 0x0a, 0x14, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x38, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x14, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x12, 0x40, 0x0a, 0x1b, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x1b, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x40, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x4e, 0x0a, 0x12, 0x6f, 0x6c, 0x64, 0x5f, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x3c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68,
	0x6f, 0x75, 0x73, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x12, 0x6f, 0x6c, 0x64, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x58, 0x0a, 0x27, 0x69, 0x73, 0x5f, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x69, 0x76, 0x65,
	0x6e, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x27, 0x69, 0x73, 0x5f, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x69, 0x76, 0x65, 0x6e,
	0x12, 0x27, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x3e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x52, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x9e, 0x01, 0x0a, 0x1e, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x52, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04,
	0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0xd8, 0x01, 0x0a, 0x19, 0x44,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x69, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x12, 0x27, 0x0a,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x09, 0x6c,
	0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x12, 0x21, 0x0a, 0x07, 0x70, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x70, 0x69,
	0x6e, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x68, 0x0a, 0x1a, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x76,
	0x65, 0x72, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x76,
	0x65, 0x72, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0xba, 0x02, 0x0a, 0x0e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x28, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x57, 0x0a, 0x13, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72,
	0x79, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e,
	0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x13, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x73, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x3c, 0x0a, 0x19, 0x69, 0x73, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x65, 0x70, 0x66, 0x5f, 0x72, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x69, 0x73,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x65, 0x70, 0x66, 0x5f, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x65, 0x64, 0x1a, 0x43, 0x0a, 0x05, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xa8, 0x02, 0x0a,
	0x0d, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22,
	0x0a, 0x0c, 0x6d, 0x61, 0x6e, 0x75, 0x66, 0x61, 0x63, 0x74, 0x75, 0x72, 0x65, 0x72, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x6e, 0x75, 0x66, 0x61, 0x63, 0x74, 0x75, 0x72,
	0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x6d, 0x69,
	0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x77, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x77, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x4f, 0x0a, 0x0e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x70,
	0x70, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x9c, 0x02, 0x0a, 0x0a, 0x41, 0x46, 0x55, 0x41,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x6c,
	0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x6f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22,
	0x0a, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0f, 0x6c, 0x69, 0x76,
	0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a,
	0x6f, 0x74, 0x70, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x02,
	0x52, 0x0a, 0x6f, 0x74, 0x70, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x10,
	0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x02, 0x52, 0x10, 0x66, 0x61, 0x63, 0x65, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x12, 0x2c, 0x0a, 0x11, 0x6c, 0x69, 0x76, 0x65,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x11, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x22, 0x50, 0x0a, 0x12, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a, 0x0a, 0x18,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x66, 0x61,
	0x69, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x18,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x66, 0x61,
	0x69, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x41, 0x0a, 0x0b, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x69, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0x9e, 0x04, 0x0a, 0x21,
	0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x81, 0x01, 0x0a, 0x17, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x77, 0x69, 0x74, 0x68,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e,
	0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69,
	0x6f, 0x72, 0x69, 0x74, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x57, 0x69, 0x74,
	0x68, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x17, 0x61, 0x6c,
	0x65, 0x72, 0x74, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x41, 0x0a, 0x05, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x1a, 0x1a, 0x0a, 0x04, 0x52, 0x75,
	0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x1a, 0xb3, 0x01, 0x0a, 0x14, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x57, 0x69, 0x74, 0x68, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x4e, 0x0a, 0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x12,
	0x4b, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x22, 0x3d, 0x0a, 0x11,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xc3, 0x01, 0x0a, 0x22,
	0x47, 0x65, 0x74, 0x43, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x41,
	0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68,
	0x6f, 0x75, 0x73, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x5a,
	0x2a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_inhouse_risk_proto_rawDescOnce sync.Once
	file_api_vendors_inhouse_risk_proto_rawDescData = file_api_vendors_inhouse_risk_proto_rawDesc
)

func file_api_vendors_inhouse_risk_proto_rawDescGZIP() []byte {
	file_api_vendors_inhouse_risk_proto_rawDescOnce.Do(func() {
		file_api_vendors_inhouse_risk_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_inhouse_risk_proto_rawDescData)
	})
	return file_api_vendors_inhouse_risk_proto_rawDescData
}

var file_api_vendors_inhouse_risk_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_api_vendors_inhouse_risk_proto_goTypes = []interface{}{
	(*DetectRiskRequest)(nil),                                      // 0: vendors.inhouse.DetectRiskRequest
	(*CreditReportAttributeInfo)(nil),                              // 1: vendors.inhouse.CreditReportAttributeInfo
	(*DetectRiskResponse)(nil),                                     // 2: vendors.inhouse.DetectRiskResponse
	(*DetectReOnboardingRiskRequest)(nil),                          // 3: vendors.inhouse.DetectReOnboardingRiskRequest
	(*DetectReOnboardingRiskResponse)(nil),                         // 4: vendors.inhouse.DetectReOnboardingRiskResponse
	(*DetectLocationRiskRequest)(nil),                              // 5: vendors.inhouse.DetectLocationRiskRequest
	(*DetectLocationRiskResponse)(nil),                             // 6: vendors.inhouse.DetectLocationRiskResponse
	(*EmploymentData)(nil),                                         // 7: vendors.inhouse.EmploymentData
	(*DeviceDetails)(nil),                                          // 8: vendors.inhouse.DeviceDetails
	(*AFUAttempt)(nil),                                             // 9: vendors.inhouse.AFUAttempt
	(*ScreenerChecksInfo)(nil),                                     // 10: vendors.inhouse.ScreenerChecksInfo
	(*AccountInfo)(nil),                                            // 11: vendors.inhouse.AccountInfo
	(*GetCasePrioritisationScoreRequest)(nil),                      // 12: vendors.inhouse.GetCasePrioritisationScoreRequest
	(*ModelResponseInfo)(nil),                                      // 13: vendors.inhouse.ModelResponseInfo
	(*GetCasePrioritisationScoreResponse)(nil),                     // 14: vendors.inhouse.GetCasePrioritisationScoreResponse
	(*EmploymentData_Range)(nil),                                   // 15: vendors.inhouse.EmploymentData.Range
	(*GetCasePrioritisationScoreRequest_Alert)(nil),                // 16: vendors.inhouse.GetCasePrioritisationScoreRequest.Alert
	(*GetCasePrioritisationScoreRequest_Rule)(nil),                 // 17: vendors.inhouse.GetCasePrioritisationScoreRequest.Rule
	(*GetCasePrioritisationScoreRequest_AlertWithRuleDetails)(nil), // 18: vendors.inhouse.GetCasePrioritisationScoreRequest.AlertWithRuleDetails
	(*common.Name)(nil),                                            // 19: api.typesv2.common.Name
	(*afu.AuthFactorValues)(nil),                                   // 20: auth.afu.AuthFactorValues
	(*common.Device)(nil),                                          // 21: api.typesv2.common.Device
	(*afu.ActorAuthState)(nil),                                     // 22: auth.afu.ActorAuthState
	(*user.Profile)(nil),                                           // 23: user.Profile
	(*typesv2.UserDeviceInstalledAppInfo)(nil),                     // 24: api.typesv2.UserDeviceInstalledAppInfo
}
var file_api_vendors_inhouse_risk_proto_depIdxs = []int32{
	19, // 0: vendors.inhouse.DetectRiskRequest.father_name:type_name -> api.typesv2.common.Name
	19, // 1: vendors.inhouse.DetectRiskRequest.mother_name:type_name -> api.typesv2.common.Name
	19, // 2: vendors.inhouse.DetectRiskRequest.user_pan_name:type_name -> api.typesv2.common.Name
	1,  // 3: vendors.inhouse.DetectRiskRequest.cb_details:type_name -> vendors.inhouse.CreditReportAttributeInfo
	7,  // 4: vendors.inhouse.DetectRiskRequest.employment_data:type_name -> vendors.inhouse.EmploymentData
	8,  // 5: vendors.inhouse.DetectRiskRequest.device_details:type_name -> vendors.inhouse.DeviceDetails
	19, // 6: vendors.inhouse.DetectReOnboardingRiskRequest.father_name:type_name -> api.typesv2.common.Name
	19, // 7: vendors.inhouse.DetectReOnboardingRiskRequest.mother_name:type_name -> api.typesv2.common.Name
	19, // 8: vendors.inhouse.DetectReOnboardingRiskRequest.user_pan_name:type_name -> api.typesv2.common.Name
	20, // 9: vendors.inhouse.DetectReOnboardingRiskRequest.new_values:type_name -> auth.afu.AuthFactorValues
	20, // 10: vendors.inhouse.DetectReOnboardingRiskRequest.current_values:type_name -> auth.afu.AuthFactorValues
	21, // 11: vendors.inhouse.DetectReOnboardingRiskRequest.new_device:type_name -> api.typesv2.common.Device
	22, // 12: vendors.inhouse.DetectReOnboardingRiskRequest.actor_auth_state:type_name -> auth.afu.ActorAuthState
	1,  // 13: vendors.inhouse.DetectReOnboardingRiskRequest.cb_details:type_name -> vendors.inhouse.CreditReportAttributeInfo
	7,  // 14: vendors.inhouse.DetectReOnboardingRiskRequest.employment_data:type_name -> vendors.inhouse.EmploymentData
	8,  // 15: vendors.inhouse.DetectReOnboardingRiskRequest.device_details:type_name -> vendors.inhouse.DeviceDetails
	9,  // 16: vendors.inhouse.DetectReOnboardingRiskRequest.afu_attempts:type_name -> vendors.inhouse.AFUAttempt
	10, // 17: vendors.inhouse.DetectReOnboardingRiskRequest.screener_checks_info:type_name -> vendors.inhouse.ScreenerChecksInfo
	11, // 18: vendors.inhouse.DetectReOnboardingRiskRequest.account_info:type_name -> vendors.inhouse.AccountInfo
	8,  // 19: vendors.inhouse.DetectReOnboardingRiskRequest.old_device_details:type_name -> vendors.inhouse.DeviceDetails
	23, // 20: vendors.inhouse.DetectReOnboardingRiskRequest.profile:type_name -> user.Profile
	15, // 21: vendors.inhouse.EmploymentData.annual_salary_range:type_name -> vendors.inhouse.EmploymentData.Range
	24, // 22: vendors.inhouse.DeviceDetails.installed_apps:type_name -> api.typesv2.UserDeviceInstalledAppInfo
	18, // 23: vendors.inhouse.GetCasePrioritisationScoreRequest.alert_with_rule_details:type_name -> vendors.inhouse.GetCasePrioritisationScoreRequest.AlertWithRuleDetails
	13, // 24: vendors.inhouse.GetCasePrioritisationScoreResponse.model_info:type_name -> vendors.inhouse.ModelResponseInfo
	16, // 25: vendors.inhouse.GetCasePrioritisationScoreRequest.AlertWithRuleDetails.alert:type_name -> vendors.inhouse.GetCasePrioritisationScoreRequest.Alert
	17, // 26: vendors.inhouse.GetCasePrioritisationScoreRequest.AlertWithRuleDetails.rule:type_name -> vendors.inhouse.GetCasePrioritisationScoreRequest.Rule
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_api_vendors_inhouse_risk_proto_init() }
func file_api_vendors_inhouse_risk_proto_init() {
	if File_api_vendors_inhouse_risk_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_inhouse_risk_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectRiskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditReportAttributeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectRiskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectReOnboardingRiskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectReOnboardingRiskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectLocationRiskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetectLocationRiskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmploymentData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AFUAttempt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenerChecksInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCasePrioritisationScoreRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelResponseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCasePrioritisationScoreResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmploymentData_Range); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCasePrioritisationScoreRequest_Alert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCasePrioritisationScoreRequest_Rule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_inhouse_risk_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCasePrioritisationScoreRequest_AlertWithRuleDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_inhouse_risk_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_inhouse_risk_proto_goTypes,
		DependencyIndexes: file_api_vendors_inhouse_risk_proto_depIdxs,
		MessageInfos:      file_api_vendors_inhouse_risk_proto_msgTypes,
	}.Build()
	File_api_vendors_inhouse_risk_proto = out.File
	file_api_vendors_inhouse_risk_proto_rawDesc = nil
	file_api_vendors_inhouse_risk_proto_goTypes = nil
	file_api_vendors_inhouse_risk_proto_depIdxs = nil
}
