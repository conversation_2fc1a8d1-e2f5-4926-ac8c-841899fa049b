// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/dynamic_elements/enums.proto

package dynamic_elements

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The utility type of a dynamic element.
// A dynamic element can be used for Alerts, Marketing, insights etc.
type ElementUtilityType int32

const (
	ElementUtilityType_ELEMENT_UTILITY_TYPE_UNSPECIFIED ElementUtilityType = 0
	// used for alerts e.g: maintenance alerts
	ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT ElementUtilityType = 1
	// used for marketing
	ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING ElementUtilityType = 2
	// used for insights
	ElementUtilityType_ELEMENT_UTILITY_TYPE_INSIGHT ElementUtilityType = 3
	// default utility type
	ElementUtilityType_ELEMENT_UTILITY_TYPE_DEFAULT ElementUtilityType = 4
	// used for collecting consent from user
	ElementUtilityType_ELEMENT_UTILITY_TYPE_CONSENT_COLLECTION ElementUtilityType = 5
)

// Enum value maps for ElementUtilityType.
var (
	ElementUtilityType_name = map[int32]string{
		0: "ELEMENT_UTILITY_TYPE_UNSPECIFIED",
		1: "ELEMENT_UTILITY_TYPE_ALERT",
		2: "ELEMENT_UTILITY_TYPE_MARKETING",
		3: "ELEMENT_UTILITY_TYPE_INSIGHT",
		4: "ELEMENT_UTILITY_TYPE_DEFAULT",
		5: "ELEMENT_UTILITY_TYPE_CONSENT_COLLECTION",
	}
	ElementUtilityType_value = map[string]int32{
		"ELEMENT_UTILITY_TYPE_UNSPECIFIED":        0,
		"ELEMENT_UTILITY_TYPE_ALERT":              1,
		"ELEMENT_UTILITY_TYPE_MARKETING":          2,
		"ELEMENT_UTILITY_TYPE_INSIGHT":            3,
		"ELEMENT_UTILITY_TYPE_DEFAULT":            4,
		"ELEMENT_UTILITY_TYPE_CONSENT_COLLECTION": 5,
	}
)

func (x ElementUtilityType) Enum() *ElementUtilityType {
	p := new(ElementUtilityType)
	*p = x
	return p
}

func (x ElementUtilityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ElementUtilityType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_dynamic_elements_enums_proto_enumTypes[0].Descriptor()
}

func (ElementUtilityType) Type() protoreflect.EnumType {
	return &file_api_dynamic_elements_enums_proto_enumTypes[0]
}

func (x ElementUtilityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ElementUtilityType.Descriptor instead.
func (ElementUtilityType) EnumDescriptor() ([]byte, []int) {
	return file_api_dynamic_elements_enums_proto_rawDescGZIP(), []int{0}
}

// The structure type of a dynamic element
// can be a banner, bottom sheet, pop up etc.
type ElementStructureType int32

const (
	ElementStructureType_ELEMENT_STRUCTURE_TYPE_UNSPECIFIED ElementStructureType = 0
	// used for Banner element
	ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER ElementStructureType = 1
	// used for bottom sheet element
	ElementStructureType_ELEMENT_STRUCTURE_TYPE_BOTTOM_SHEET ElementStructureType = 2
	// used for pop up element
	ElementStructureType_ELEMENT_STRUCTURE_TYPE_POP_UP ElementStructureType = 3
	// used for Banner element v2
	ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2 ElementStructureType = 4
	// used for scrollable banner element
	ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_SCROLLABLE ElementStructureType = 5
	// used for gtm pop ups
	ElementStructureType_ELEMENT_STRUCTURE_TYPE_GTM_POP_UP ElementStructureType = 6
	// used for feature widgets highlighting 4 points
	ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS ElementStructureType = 7
	// used for feature widgets highlighting 3 points
	ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_THREE_POINTS ElementStructureType = 8
	// used for feature widgets highlighting 2 points
	ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_TWO_POINTS ElementStructureType = 9
	// Display content within a card.
	// Optionally include tabs for switching between different tabs content views if available.
	// https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-20723&mode=design&t=Q1lU7tXjuFhkQ8R7-0
	ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_TABBED_CARD ElementStructureType = 10
	ElementStructureType_ELEMENT_STRUCTURE_TYPE_REDIRECT_ELEMENT    ElementStructureType = 11
	// used for BannerElementContentV3
	ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V3 ElementStructureType = 12
	// used for ProgressBarCardContent
	ElementStructureType_ELEMENT_STRUCTURE_TYPE_PROGRESS_BAR_CARD ElementStructureType = 13
)

// Enum value maps for ElementStructureType.
var (
	ElementStructureType_name = map[int32]string{
		0:  "ELEMENT_STRUCTURE_TYPE_UNSPECIFIED",
		1:  "ELEMENT_STRUCTURE_TYPE_BANNER",
		2:  "ELEMENT_STRUCTURE_TYPE_BOTTOM_SHEET",
		3:  "ELEMENT_STRUCTURE_TYPE_POP_UP",
		4:  "ELEMENT_STRUCTURE_TYPE_BANNER_V2",
		5:  "ELEMENT_STRUCTURE_TYPE_BANNER_SCROLLABLE",
		6:  "ELEMENT_STRUCTURE_TYPE_GTM_POP_UP",
		7:  "ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS",
		8:  "ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_THREE_POINTS",
		9:  "ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_TWO_POINTS",
		10: "ELEMENT_STRUCTURE_TYPE_FEATURE_TABBED_CARD",
		11: "ELEMENT_STRUCTURE_TYPE_REDIRECT_ELEMENT",
		12: "ELEMENT_STRUCTURE_TYPE_BANNER_V3",
		13: "ELEMENT_STRUCTURE_TYPE_PROGRESS_BAR_CARD",
	}
	ElementStructureType_value = map[string]int32{
		"ELEMENT_STRUCTURE_TYPE_UNSPECIFIED":                 0,
		"ELEMENT_STRUCTURE_TYPE_BANNER":                      1,
		"ELEMENT_STRUCTURE_TYPE_BOTTOM_SHEET":                2,
		"ELEMENT_STRUCTURE_TYPE_POP_UP":                      3,
		"ELEMENT_STRUCTURE_TYPE_BANNER_V2":                   4,
		"ELEMENT_STRUCTURE_TYPE_BANNER_SCROLLABLE":           5,
		"ELEMENT_STRUCTURE_TYPE_GTM_POP_UP":                  6,
		"ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS":  7,
		"ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_THREE_POINTS": 8,
		"ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_TWO_POINTS":   9,
		"ELEMENT_STRUCTURE_TYPE_FEATURE_TABBED_CARD":         10,
		"ELEMENT_STRUCTURE_TYPE_REDIRECT_ELEMENT":            11,
		"ELEMENT_STRUCTURE_TYPE_BANNER_V3":                   12,
		"ELEMENT_STRUCTURE_TYPE_PROGRESS_BAR_CARD":           13,
	}
)

func (x ElementStructureType) Enum() *ElementStructureType {
	p := new(ElementStructureType)
	*p = x
	return p
}

func (x ElementStructureType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ElementStructureType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_dynamic_elements_enums_proto_enumTypes[1].Descriptor()
}

func (ElementStructureType) Type() protoreflect.EnumType {
	return &file_api_dynamic_elements_enums_proto_enumTypes[1]
}

func (x ElementStructureType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ElementStructureType.Descriptor instead.
func (ElementStructureType) EnumDescriptor() ([]byte, []int) {
	return file_api_dynamic_elements_enums_proto_rawDescGZIP(), []int{1}
}

type BizAnalyticsDataKey int32

const (
	BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_UNSPECIFIED BizAnalyticsDataKey = 0
	// to specify the area surfaced in dynamic element
	BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA BizAnalyticsDataKey = 1
)

// Enum value maps for BizAnalyticsDataKey.
var (
	BizAnalyticsDataKey_name = map[int32]string{
		0: "BIZ_ANALYTICS_DATA_KEY_UNSPECIFIED",
		1: "BIZ_ANALYTICS_DATA_KEY_AREA",
	}
	BizAnalyticsDataKey_value = map[string]int32{
		"BIZ_ANALYTICS_DATA_KEY_UNSPECIFIED": 0,
		"BIZ_ANALYTICS_DATA_KEY_AREA":        1,
	}
)

func (x BizAnalyticsDataKey) Enum() *BizAnalyticsDataKey {
	p := new(BizAnalyticsDataKey)
	*p = x
	return p
}

func (x BizAnalyticsDataKey) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BizAnalyticsDataKey) Descriptor() protoreflect.EnumDescriptor {
	return file_api_dynamic_elements_enums_proto_enumTypes[2].Descriptor()
}

func (BizAnalyticsDataKey) Type() protoreflect.EnumType {
	return &file_api_dynamic_elements_enums_proto_enumTypes[2]
}

func (x BizAnalyticsDataKey) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BizAnalyticsDataKey.Descriptor instead.
func (BizAnalyticsDataKey) EnumDescriptor() ([]byte, []int) {
	return file_api_dynamic_elements_enums_proto_rawDescGZIP(), []int{2}
}

var File_api_dynamic_elements_enums_proto protoreflect.FileDescriptor

var file_api_dynamic_elements_enums_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x10, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x2a, 0xef, 0x01, 0x0a, 0x12, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x55, 0x74, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x20, 0x45,
	0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x54, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x54, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x10,
	0x01, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x54, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x45, 0x54,
	0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x55, 0x54, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e,
	0x53, 0x49, 0x47, 0x48, 0x54, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x4c, 0x45, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x55, 0x54, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x04, 0x12, 0x2b, 0x0a, 0x27, 0x45, 0x4c, 0x45,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x54, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x2a, 0xfe, 0x04, 0x0a, 0x14, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x26, 0x0a, 0x22, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x52, 0x55, 0x43,
	0x54, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x4c, 0x45, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x45, 0x4c,
	0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48, 0x45, 0x45,
	0x54, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53,
	0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f,
	0x50, 0x5f, 0x55, 0x50, 0x10, 0x03, 0x12, 0x24, 0x0a, 0x20, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x56, 0x32, 0x10, 0x04, 0x12, 0x2c, 0x0a, 0x28,
	0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x53, 0x43,
	0x52, 0x4f, 0x4c, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x05, 0x12, 0x25, 0x0a, 0x21, 0x45, 0x4c,
	0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x54, 0x4d, 0x5f, 0x50, 0x4f, 0x50, 0x5f, 0x55, 0x50, 0x10,
	0x06, 0x12, 0x35, 0x0a, 0x31, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x52,
	0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x52, 0x5f,
	0x50, 0x4f, 0x49, 0x4e, 0x54, 0x53, 0x10, 0x07, 0x12, 0x36, 0x0a, 0x32, 0x45, 0x4c, 0x45, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x57, 0x49, 0x44, 0x47, 0x45,
	0x54, 0x5f, 0x54, 0x48, 0x52, 0x45, 0x45, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x53, 0x10, 0x08,
	0x12, 0x34, 0x0a, 0x30, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x52, 0x55,
	0x43, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x45, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x5f, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x57, 0x4f, 0x5f, 0x50, 0x4f,
	0x49, 0x4e, 0x54, 0x53, 0x10, 0x09, 0x12, 0x2e, 0x0a, 0x2a, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x41, 0x42, 0x42, 0x45, 0x44, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x10, 0x0a, 0x12, 0x2b, 0x0a, 0x27, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x52, 0x45, 0x44, 0x49, 0x52, 0x45, 0x43, 0x54, 0x5f, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x0b, 0x12, 0x24, 0x0a, 0x20, 0x45, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53,
	0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41,
	0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x56, 0x33, 0x10, 0x0c, 0x12, 0x2c, 0x0a, 0x28, 0x45, 0x4c, 0x45,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x42, 0x41, 0x52,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x0d, 0x2a, 0x5e, 0x0a, 0x13, 0x42, 0x69, 0x7a, 0x41, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x44, 0x61, 0x74, 0x61, 0x4b, 0x65, 0x79, 0x12, 0x26,
	0x0a, 0x22, 0x42, 0x49, 0x5a, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x54, 0x49, 0x43, 0x53, 0x5f,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x4b, 0x45, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x42, 0x49, 0x5a, 0x5f, 0x41, 0x4e,
	0x41, 0x4c, 0x59, 0x54, 0x49, 0x43, 0x53, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x4b, 0x45, 0x59,
	0x5f, 0x41, 0x52, 0x45, 0x41, 0x10, 0x01, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_dynamic_elements_enums_proto_rawDescOnce sync.Once
	file_api_dynamic_elements_enums_proto_rawDescData = file_api_dynamic_elements_enums_proto_rawDesc
)

func file_api_dynamic_elements_enums_proto_rawDescGZIP() []byte {
	file_api_dynamic_elements_enums_proto_rawDescOnce.Do(func() {
		file_api_dynamic_elements_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_dynamic_elements_enums_proto_rawDescData)
	})
	return file_api_dynamic_elements_enums_proto_rawDescData
}

var file_api_dynamic_elements_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_dynamic_elements_enums_proto_goTypes = []interface{}{
	(ElementUtilityType)(0),   // 0: dynamic_elements.ElementUtilityType
	(ElementStructureType)(0), // 1: dynamic_elements.ElementStructureType
	(BizAnalyticsDataKey)(0),  // 2: dynamic_elements.BizAnalyticsDataKey
}
var file_api_dynamic_elements_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_dynamic_elements_enums_proto_init() }
func file_api_dynamic_elements_enums_proto_init() {
	if File_api_dynamic_elements_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_dynamic_elements_enums_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_dynamic_elements_enums_proto_goTypes,
		DependencyIndexes: file_api_dynamic_elements_enums_proto_depIdxs,
		EnumInfos:         file_api_dynamic_elements_enums_proto_enumTypes,
	}.Build()
	File_api_dynamic_elements_enums_proto = out.File
	file_api_dynamic_elements_enums_proto_rawDesc = nil
	file_api_dynamic_elements_enums_proto_goTypes = nil
	file_api_dynamic_elements_enums_proto_depIdxs = nil
}
