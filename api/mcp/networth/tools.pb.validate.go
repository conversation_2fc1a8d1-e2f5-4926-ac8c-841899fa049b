// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/mcp/networth/tools.proto

package networth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on NetworthDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NetworthDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetworthDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetworthDetailsMultiError, or nil if none found.
func (m *NetworthDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *NetworthDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNetWorthResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthDetailsValidationError{
					field:  "NetWorthResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthDetailsValidationError{
					field:  "NetWorthResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetWorthResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthDetailsValidationError{
				field:  "NetWorthResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMfSchemeAnalytics()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthDetailsValidationError{
					field:  "MfSchemeAnalytics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthDetailsValidationError{
					field:  "MfSchemeAnalytics",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMfSchemeAnalytics()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthDetailsValidationError{
				field:  "MfSchemeAnalytics",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccountDetailsBulkResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthDetailsValidationError{
					field:  "AccountDetailsBulkResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthDetailsValidationError{
					field:  "AccountDetailsBulkResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountDetailsBulkResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthDetailsValidationError{
				field:  "AccountDetailsBulkResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NetworthDetailsMultiError(errors)
	}

	return nil
}

// NetworthDetailsMultiError is an error wrapping multiple validation errors
// returned by NetworthDetails.ValidateAll() if the designated constraints
// aren't met.
type NetworthDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetworthDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetworthDetailsMultiError) AllErrors() []error { return m }

// NetworthDetailsValidationError is the validation error returned by
// NetworthDetails.Validate if the designated constraints aren't met.
type NetworthDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetworthDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetworthDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetworthDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetworthDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetworthDetailsValidationError) ErrorName() string { return "NetworthDetailsValidationError" }

// Error satisfies the builtin error interface
func (e NetworthDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetworthDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetworthDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetworthDetailsValidationError{}

// Validate checks the field values on TransactionDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TransactionDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransactionDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransactionDetailsMultiError, or nil if none found.
func (m *TransactionDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *TransactionDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMutualFundTransactionList()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionDetailsValidationError{
					field:  "MutualFundTransactionList",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionDetailsValidationError{
					field:  "MutualFundTransactionList",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMutualFundTransactionList()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionDetailsValidationError{
				field:  "MutualFundTransactionList",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUanAccountsDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransactionDetailsValidationError{
					field:  "UanAccountsDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransactionDetailsValidationError{
					field:  "UanAccountsDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUanAccountsDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransactionDetailsValidationError{
				field:  "UanAccountsDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TransactionDetailsMultiError(errors)
	}

	return nil
}

// TransactionDetailsMultiError is an error wrapping multiple validation errors
// returned by TransactionDetails.ValidateAll() if the designated constraints
// aren't met.
type TransactionDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransactionDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransactionDetailsMultiError) AllErrors() []error { return m }

// TransactionDetailsValidationError is the validation error returned by
// TransactionDetails.Validate if the designated constraints aren't met.
type TransactionDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransactionDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransactionDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransactionDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransactionDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransactionDetailsValidationError) ErrorName() string {
	return "TransactionDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e TransactionDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransactionDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransactionDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransactionDetailsValidationError{}

// Validate checks the field values on NetWorthDataCollection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NetWorthDataCollection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetWorthDataCollection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetWorthDataCollectionMultiError, or nil if none found.
func (m *NetWorthDataCollection) ValidateAll() error {
	return m.validate(true)
}

func (m *NetWorthDataCollection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDataItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NetWorthDataCollectionValidationError{
						field:  fmt.Sprintf("DataItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NetWorthDataCollectionValidationError{
						field:  fmt.Sprintf("DataItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NetWorthDataCollectionValidationError{
					field:  fmt.Sprintf("DataItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NetWorthDataCollectionMultiError(errors)
	}

	return nil
}

// NetWorthDataCollectionMultiError is an error wrapping multiple validation
// errors returned by NetWorthDataCollection.ValidateAll() if the designated
// constraints aren't met.
type NetWorthDataCollectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetWorthDataCollectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetWorthDataCollectionMultiError) AllErrors() []error { return m }

// NetWorthDataCollectionValidationError is the validation error returned by
// NetWorthDataCollection.Validate if the designated constraints aren't met.
type NetWorthDataCollectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetWorthDataCollectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetWorthDataCollectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetWorthDataCollectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetWorthDataCollectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetWorthDataCollectionValidationError) ErrorName() string {
	return "NetWorthDataCollectionValidationError"
}

// Error satisfies the builtin error interface
func (e NetWorthDataCollectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetWorthDataCollection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetWorthDataCollectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetWorthDataCollectionValidationError{}

// Validate checks the field values on NetWorthDataItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NetWorthDataItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetWorthDataItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetWorthDataItemMultiError, or nil if none found.
func (m *NetWorthDataItem) ValidateAll() error {
	return m.validate(true)
}

func (m *NetWorthDataItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ComponentDescription

	switch v := m.DataObject.(type) {
	case *NetWorthDataItem_NetWorthSummary:
		if v == nil {
			err := NetWorthDataItemValidationError{
				field:  "DataObject",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetWorthSummary()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NetWorthDataItemValidationError{
						field:  "NetWorthSummary",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NetWorthDataItemValidationError{
						field:  "NetWorthSummary",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetWorthSummary()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NetWorthDataItemValidationError{
					field:  "NetWorthSummary",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *NetWorthDataItem_MutualFundAnalytics:
		if v == nil {
			err := NetWorthDataItemValidationError{
				field:  "DataObject",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMutualFundAnalytics()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NetWorthDataItemValidationError{
						field:  "MutualFundAnalytics",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NetWorthDataItemValidationError{
						field:  "MutualFundAnalytics",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMutualFundAnalytics()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NetWorthDataItemValidationError{
					field:  "MutualFundAnalytics",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *NetWorthDataItem_ConnectedAccountDetails:
		if v == nil {
			err := NetWorthDataItemValidationError{
				field:  "DataObject",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetConnectedAccountDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NetWorthDataItemValidationError{
						field:  "ConnectedAccountDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NetWorthDataItemValidationError{
						field:  "ConnectedAccountDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetConnectedAccountDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NetWorthDataItemValidationError{
					field:  "ConnectedAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *NetWorthDataItem_MutualFundTransactionList:
		if v == nil {
			err := NetWorthDataItemValidationError{
				field:  "DataObject",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMutualFundTransactionList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NetWorthDataItemValidationError{
						field:  "MutualFundTransactionList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NetWorthDataItemValidationError{
						field:  "MutualFundTransactionList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMutualFundTransactionList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NetWorthDataItemValidationError{
					field:  "MutualFundTransactionList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *NetWorthDataItem_EpfAccountData:
		if v == nil {
			err := NetWorthDataItemValidationError{
				field:  "DataObject",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEpfAccountData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NetWorthDataItemValidationError{
						field:  "EpfAccountData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NetWorthDataItemValidationError{
						field:  "EpfAccountData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEpfAccountData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NetWorthDataItemValidationError{
					field:  "EpfAccountData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *NetWorthDataItem_CreditReportData:
		if v == nil {
			err := NetWorthDataItemValidationError{
				field:  "DataObject",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCreditReportData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NetWorthDataItemValidationError{
						field:  "CreditReportData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NetWorthDataItemValidationError{
						field:  "CreditReportData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreditReportData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NetWorthDataItemValidationError{
					field:  "CreditReportData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return NetWorthDataItemMultiError(errors)
	}

	return nil
}

// NetWorthDataItemMultiError is an error wrapping multiple validation errors
// returned by NetWorthDataItem.ValidateAll() if the designated constraints
// aren't met.
type NetWorthDataItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetWorthDataItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetWorthDataItemMultiError) AllErrors() []error { return m }

// NetWorthDataItemValidationError is the validation error returned by
// NetWorthDataItem.Validate if the designated constraints aren't met.
type NetWorthDataItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetWorthDataItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetWorthDataItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetWorthDataItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetWorthDataItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetWorthDataItemValidationError) ErrorName() string { return "NetWorthDataItemValidationError" }

// Error satisfies the builtin error interface
func (e NetWorthDataItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetWorthDataItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetWorthDataItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetWorthDataItemValidationError{}

// Validate checks the field values on MutualFundTransactionList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MutualFundTransactionList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MutualFundTransactionList with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MutualFundTransactionListMultiError, or nil if none found.
func (m *MutualFundTransactionList) ValidateAll() error {
	return m.validate(true)
}

func (m *MutualFundTransactionList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MutualFundTransactionListValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MutualFundTransactionListValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MutualFundTransactionListValidationError{
					field:  fmt.Sprintf("Transactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TransactionDescription

	if len(errors) > 0 {
		return MutualFundTransactionListMultiError(errors)
	}

	return nil
}

// MutualFundTransactionListMultiError is an error wrapping multiple validation
// errors returned by MutualFundTransactionList.ValidateAll() if the
// designated constraints aren't met.
type MutualFundTransactionListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MutualFundTransactionListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MutualFundTransactionListMultiError) AllErrors() []error { return m }

// MutualFundTransactionListValidationError is the validation error returned by
// MutualFundTransactionList.Validate if the designated constraints aren't met.
type MutualFundTransactionListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MutualFundTransactionListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MutualFundTransactionListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MutualFundTransactionListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MutualFundTransactionListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MutualFundTransactionListValidationError) ErrorName() string {
	return "MutualFundTransactionListValidationError"
}

// Error satisfies the builtin error interface
func (e MutualFundTransactionListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMutualFundTransactionList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MutualFundTransactionListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MutualFundTransactionListValidationError{}
