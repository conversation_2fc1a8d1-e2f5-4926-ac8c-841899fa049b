// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/mcp/networth/tools.proto

package networth

import (
	mutualfund "github.com/epifi/gamma/api/analyser/variables/mutualfund"
	connected_account "github.com/epifi/gamma/api/connected_account"
	creditreportv2 "github.com/epifi/gamma/api/creditreportv2"
	epf "github.com/epifi/gamma/api/insights/epf"
	networth "github.com/epifi/gamma/api/insights/networth"
	external "github.com/epifi/gamma/api/investment/mutualfund/external"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NetworthDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NetWorthResponse           *networth.GetNetWorthValueResponse               `protobuf:"bytes,1,opt,name=net_worth_response,json=netWorthResponse,proto3" json:"net_worth_response,omitempty"`
	MfSchemeAnalytics          *mutualfund.MfSchemeAnalytics                    `protobuf:"bytes,2,opt,name=mf_scheme_analytics,json=mfSchemeAnalytics,proto3" json:"mf_scheme_analytics,omitempty"`
	AccountDetailsBulkResponse *connected_account.GetAccountDetailsBulkResponse `protobuf:"bytes,3,opt,name=account_details_bulk_response,json=accountDetailsBulkResponse,proto3" json:"account_details_bulk_response,omitempty"`
}

func (x *NetworthDetails) Reset() {
	*x = NetworthDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_mcp_networth_tools_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworthDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworthDetails) ProtoMessage() {}

func (x *NetworthDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_mcp_networth_tools_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworthDetails.ProtoReflect.Descriptor instead.
func (*NetworthDetails) Descriptor() ([]byte, []int) {
	return file_api_mcp_networth_tools_proto_rawDescGZIP(), []int{0}
}

func (x *NetworthDetails) GetNetWorthResponse() *networth.GetNetWorthValueResponse {
	if x != nil {
		return x.NetWorthResponse
	}
	return nil
}

func (x *NetworthDetails) GetMfSchemeAnalytics() *mutualfund.MfSchemeAnalytics {
	if x != nil {
		return x.MfSchemeAnalytics
	}
	return nil
}

func (x *NetworthDetails) GetAccountDetailsBulkResponse() *connected_account.GetAccountDetailsBulkResponse {
	if x != nil {
		return x.AccountDetailsBulkResponse
	}
	return nil
}

type TransactionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MutualFundTransactionList *MutualFundTransactionList  `protobuf:"bytes,1,opt,name=mutual_fund_transaction_list,json=mutualFundTransactionList,proto3" json:"mutual_fund_transaction_list,omitempty"`
	UanAccountsDetails        *epf.GetUANAccountsResponse `protobuf:"bytes,2,opt,name=uan_accounts_details,json=uanAccountsDetails,proto3" json:"uan_accounts_details,omitempty"`
}

func (x *TransactionDetails) Reset() {
	*x = TransactionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_mcp_networth_tools_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionDetails) ProtoMessage() {}

func (x *TransactionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_mcp_networth_tools_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionDetails.ProtoReflect.Descriptor instead.
func (*TransactionDetails) Descriptor() ([]byte, []int) {
	return file_api_mcp_networth_tools_proto_rawDescGZIP(), []int{1}
}

func (x *TransactionDetails) GetMutualFundTransactionList() *MutualFundTransactionList {
	if x != nil {
		return x.MutualFundTransactionList
	}
	return nil
}

func (x *TransactionDetails) GetUanAccountsDetails() *epf.GetUANAccountsResponse {
	if x != nil {
		return x.UanAccountsDetails
	}
	return nil
}

// NetWorthDataCollection represents a comprehensive collection of financial data items
// for AI/LLM tools to analyze and export user's complete financial profile.
type NetWorthDataCollection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataItems []*NetWorthDataItem `protobuf:"bytes,1,rep,name=data_items,json=dataItems,proto3" json:"data_items,omitempty"`
}

func (x *NetWorthDataCollection) Reset() {
	*x = NetWorthDataCollection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_mcp_networth_tools_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetWorthDataCollection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetWorthDataCollection) ProtoMessage() {}

func (x *NetWorthDataCollection) ProtoReflect() protoreflect.Message {
	mi := &file_api_mcp_networth_tools_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetWorthDataCollection.ProtoReflect.Descriptor instead.
func (*NetWorthDataCollection) Descriptor() ([]byte, []int) {
	return file_api_mcp_networth_tools_proto_rawDescGZIP(), []int{2}
}

func (x *NetWorthDataCollection) GetDataItems() []*NetWorthDataItem {
	if x != nil {
		return x.DataItems
	}
	return nil
}

// NetWorthDataItem represents a single component of user's financial data with contextual description.
// This message is designed to support AI/LLM financial analysis tools by providing structured access
// to different types of financial information with human-readable descriptions.
//
// Each item contains:
// - A descriptive label explaining what the data represents
// - One of possible financial data types in a type-safe union
type NetWorthDataItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComponentDescription string `protobuf:"bytes,1,opt,name=component_description,json=componentDescription,proto3" json:"component_description,omitempty"`
	// Types that are assignable to DataObject:
	//
	//	*NetWorthDataItem_NetWorthSummary
	//	*NetWorthDataItem_MutualFundAnalytics
	//	*NetWorthDataItem_ConnectedAccountDetails
	//	*NetWorthDataItem_MutualFundTransactionList
	//	*NetWorthDataItem_EpfAccountData
	//	*NetWorthDataItem_CreditReportData
	DataObject isNetWorthDataItem_DataObject `protobuf_oneof:"data_object"`
}

func (x *NetWorthDataItem) Reset() {
	*x = NetWorthDataItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_mcp_networth_tools_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetWorthDataItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetWorthDataItem) ProtoMessage() {}

func (x *NetWorthDataItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_mcp_networth_tools_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetWorthDataItem.ProtoReflect.Descriptor instead.
func (*NetWorthDataItem) Descriptor() ([]byte, []int) {
	return file_api_mcp_networth_tools_proto_rawDescGZIP(), []int{3}
}

func (x *NetWorthDataItem) GetComponentDescription() string {
	if x != nil {
		return x.ComponentDescription
	}
	return ""
}

func (m *NetWorthDataItem) GetDataObject() isNetWorthDataItem_DataObject {
	if m != nil {
		return m.DataObject
	}
	return nil
}

func (x *NetWorthDataItem) GetNetWorthSummary() *networth.GetNetWorthValueResponse {
	if x, ok := x.GetDataObject().(*NetWorthDataItem_NetWorthSummary); ok {
		return x.NetWorthSummary
	}
	return nil
}

func (x *NetWorthDataItem) GetMutualFundAnalytics() *mutualfund.MfSchemeAnalytics {
	if x, ok := x.GetDataObject().(*NetWorthDataItem_MutualFundAnalytics); ok {
		return x.MutualFundAnalytics
	}
	return nil
}

func (x *NetWorthDataItem) GetConnectedAccountDetails() *connected_account.GetAccountDetailsBulkResponse {
	if x, ok := x.GetDataObject().(*NetWorthDataItem_ConnectedAccountDetails); ok {
		return x.ConnectedAccountDetails
	}
	return nil
}

func (x *NetWorthDataItem) GetMutualFundTransactionList() *MutualFundTransactionList {
	if x, ok := x.GetDataObject().(*NetWorthDataItem_MutualFundTransactionList); ok {
		return x.MutualFundTransactionList
	}
	return nil
}

func (x *NetWorthDataItem) GetEpfAccountData() *epf.GetUANAccountsResponse {
	if x, ok := x.GetDataObject().(*NetWorthDataItem_EpfAccountData); ok {
		return x.EpfAccountData
	}
	return nil
}

func (x *NetWorthDataItem) GetCreditReportData() *creditreportv2.GetCreditReportsResponse {
	if x, ok := x.GetDataObject().(*NetWorthDataItem_CreditReportData); ok {
		return x.CreditReportData
	}
	return nil
}

type isNetWorthDataItem_DataObject interface {
	isNetWorthDataItem_DataObject()
}

type NetWorthDataItem_NetWorthSummary struct {
	NetWorthSummary *networth.GetNetWorthValueResponse `protobuf:"bytes,2,opt,name=net_worth_summary,json=netWorthSummary,proto3,oneof"`
}

type NetWorthDataItem_MutualFundAnalytics struct {
	MutualFundAnalytics *mutualfund.MfSchemeAnalytics `protobuf:"bytes,3,opt,name=mutual_fund_analytics,json=mutualFundAnalytics,proto3,oneof"`
}

type NetWorthDataItem_ConnectedAccountDetails struct {
	ConnectedAccountDetails *connected_account.GetAccountDetailsBulkResponse `protobuf:"bytes,4,opt,name=connected_account_details,json=connectedAccountDetails,proto3,oneof"`
}

type NetWorthDataItem_MutualFundTransactionList struct {
	MutualFundTransactionList *MutualFundTransactionList `protobuf:"bytes,5,opt,name=mutual_fund_transaction_list,json=mutualFundTransactionList,proto3,oneof"`
}

type NetWorthDataItem_EpfAccountData struct {
	EpfAccountData *epf.GetUANAccountsResponse `protobuf:"bytes,6,opt,name=epf_account_data,json=epfAccountData,proto3,oneof"`
}

type NetWorthDataItem_CreditReportData struct {
	CreditReportData *creditreportv2.GetCreditReportsResponse `protobuf:"bytes,7,opt,name=credit_report_data,json=creditReportData,proto3,oneof"`
}

func (*NetWorthDataItem_NetWorthSummary) isNetWorthDataItem_DataObject() {}

func (*NetWorthDataItem_MutualFundAnalytics) isNetWorthDataItem_DataObject() {}

func (*NetWorthDataItem_ConnectedAccountDetails) isNetWorthDataItem_DataObject() {}

func (*NetWorthDataItem_MutualFundTransactionList) isNetWorthDataItem_DataObject() {}

func (*NetWorthDataItem_EpfAccountData) isNetWorthDataItem_DataObject() {}

func (*NetWorthDataItem_CreditReportData) isNetWorthDataItem_DataObject() {}

type MutualFundTransactionList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Transactions           []*external.MutualFundExternalOrder `protobuf:"bytes,1,rep,name=transactions,proto3" json:"transactions,omitempty"`
	TransactionDescription string                              `protobuf:"bytes,2,opt,name=transaction_description,json=transactionDescription,proto3" json:"transaction_description,omitempty"`
}

func (x *MutualFundTransactionList) Reset() {
	*x = MutualFundTransactionList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_mcp_networth_tools_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MutualFundTransactionList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MutualFundTransactionList) ProtoMessage() {}

func (x *MutualFundTransactionList) ProtoReflect() protoreflect.Message {
	mi := &file_api_mcp_networth_tools_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MutualFundTransactionList.ProtoReflect.Descriptor instead.
func (*MutualFundTransactionList) Descriptor() ([]byte, []int) {
	return file_api_mcp_networth_tools_proto_rawDescGZIP(), []int{4}
}

func (x *MutualFundTransactionList) GetTransactions() []*external.MutualFundExternalOrder {
	if x != nil {
		return x.Transactions
	}
	return nil
}

func (x *MutualFundTransactionList) GetTransactionDescription() string {
	if x != nil {
		return x.TransactionDescription
	}
	return ""
}

var File_api_mcp_networth_tools_proto protoreflect.FileDescriptor

var file_api_mcp_networth_tools_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x63, 0x70, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x2f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c,
	0x6d, 0x63, 0x70, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x1a, 0x3e, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x63, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x76, 0x32, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2f, 0x65, 0x70, 0x66, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x43, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc7, 0x02,
	0x0a, 0x0f, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x59, 0x0a, 0x12, 0x6e, 0x65, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x10, 0x6e, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x64, 0x0a, 0x13,
	0x6d, 0x66, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x4d, 0x66,
	0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x52,
	0x11, 0x6d, 0x66, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x73, 0x12, 0x73, 0x0a, 0x1d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x62, 0x75, 0x6c, 0x6b, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42,
	0x75, 0x6c, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x1a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x75, 0x6c, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xd6, 0x01, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x68,
	0x0a, 0x1c, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x63, 0x70, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x19, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x56, 0x0a, 0x14, 0x75, 0x61, 0x6e, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x65, 0x70, 0x66, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x41, 0x4e, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x12, 0x75, 0x61,
	0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x22, 0x57, 0x0a, 0x16, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x61, 0x74, 0x61,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0a, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x6d, 0x63, 0x70, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x4e, 0x65,
	0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x09,
	0x64, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xa5, 0x05, 0x0a, 0x10, 0x4e, 0x65,
	0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x33,
	0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x59, 0x0a, 0x11, 0x6e, 0x65, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0f, 0x6e,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x6a,
	0x0a, 0x15, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x61, 0x72,
	0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x4d, 0x66, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x48, 0x00, 0x52, 0x13, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e,
	0x64, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x12, 0x6e, 0x0a, 0x19, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x42, 0x75, 0x6c, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x00, 0x52, 0x17, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x6a, 0x0a, 0x1c, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x6d, 0x63, 0x70, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e,
	0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x19, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x50, 0x0a, 0x10, 0x65, 0x70, 0x66, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x65, 0x70, 0x66, 0x2e,
	0x47, 0x65, 0x74, 0x55, 0x41, 0x4e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x65, 0x70, 0x66, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x58, 0x0a, 0x12, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00,
	0x52, 0x10, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x42, 0x0d, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x22, 0xb5, 0x01, 0x0a, 0x19, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x5f, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61,
	0x6c, 0x46, 0x75, 0x6e, 0x64, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x37, 0x0a, 0x17, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x52, 0x0a, 0x27, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x63, 0x70, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x5a, 0x27, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x6d, 0x63, 0x70, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_mcp_networth_tools_proto_rawDescOnce sync.Once
	file_api_mcp_networth_tools_proto_rawDescData = file_api_mcp_networth_tools_proto_rawDesc
)

func file_api_mcp_networth_tools_proto_rawDescGZIP() []byte {
	file_api_mcp_networth_tools_proto_rawDescOnce.Do(func() {
		file_api_mcp_networth_tools_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_mcp_networth_tools_proto_rawDescData)
	})
	return file_api_mcp_networth_tools_proto_rawDescData
}

var file_api_mcp_networth_tools_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_mcp_networth_tools_proto_goTypes = []interface{}{
	(*NetworthDetails)(nil),                                 // 0: mcp.networth.NetworthDetails
	(*TransactionDetails)(nil),                              // 1: mcp.networth.TransactionDetails
	(*NetWorthDataCollection)(nil),                          // 2: mcp.networth.NetWorthDataCollection
	(*NetWorthDataItem)(nil),                                // 3: mcp.networth.NetWorthDataItem
	(*MutualFundTransactionList)(nil),                       // 4: mcp.networth.MutualFundTransactionList
	(*networth.GetNetWorthValueResponse)(nil),               // 5: insights.networth.GetNetWorthValueResponse
	(*mutualfund.MfSchemeAnalytics)(nil),                    // 6: api.analyser.variables.mutualfund.MfSchemeAnalytics
	(*connected_account.GetAccountDetailsBulkResponse)(nil), // 7: connected_account.GetAccountDetailsBulkResponse
	(*epf.GetUANAccountsResponse)(nil),                      // 8: insights.epf.GetUANAccountsResponse
	(*creditreportv2.GetCreditReportsResponse)(nil),         // 9: creditreportv2.GetCreditReportsResponse
	(*external.MutualFundExternalOrder)(nil),                // 10: api.investment.mutualfund.external.MutualFundExternalOrder
}
var file_api_mcp_networth_tools_proto_depIdxs = []int32{
	5,  // 0: mcp.networth.NetworthDetails.net_worth_response:type_name -> insights.networth.GetNetWorthValueResponse
	6,  // 1: mcp.networth.NetworthDetails.mf_scheme_analytics:type_name -> api.analyser.variables.mutualfund.MfSchemeAnalytics
	7,  // 2: mcp.networth.NetworthDetails.account_details_bulk_response:type_name -> connected_account.GetAccountDetailsBulkResponse
	4,  // 3: mcp.networth.TransactionDetails.mutual_fund_transaction_list:type_name -> mcp.networth.MutualFundTransactionList
	8,  // 4: mcp.networth.TransactionDetails.uan_accounts_details:type_name -> insights.epf.GetUANAccountsResponse
	3,  // 5: mcp.networth.NetWorthDataCollection.data_items:type_name -> mcp.networth.NetWorthDataItem
	5,  // 6: mcp.networth.NetWorthDataItem.net_worth_summary:type_name -> insights.networth.GetNetWorthValueResponse
	6,  // 7: mcp.networth.NetWorthDataItem.mutual_fund_analytics:type_name -> api.analyser.variables.mutualfund.MfSchemeAnalytics
	7,  // 8: mcp.networth.NetWorthDataItem.connected_account_details:type_name -> connected_account.GetAccountDetailsBulkResponse
	4,  // 9: mcp.networth.NetWorthDataItem.mutual_fund_transaction_list:type_name -> mcp.networth.MutualFundTransactionList
	8,  // 10: mcp.networth.NetWorthDataItem.epf_account_data:type_name -> insights.epf.GetUANAccountsResponse
	9,  // 11: mcp.networth.NetWorthDataItem.credit_report_data:type_name -> creditreportv2.GetCreditReportsResponse
	10, // 12: mcp.networth.MutualFundTransactionList.transactions:type_name -> api.investment.mutualfund.external.MutualFundExternalOrder
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_api_mcp_networth_tools_proto_init() }
func file_api_mcp_networth_tools_proto_init() {
	if File_api_mcp_networth_tools_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_mcp_networth_tools_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworthDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_mcp_networth_tools_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_mcp_networth_tools_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetWorthDataCollection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_mcp_networth_tools_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetWorthDataItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_mcp_networth_tools_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MutualFundTransactionList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_mcp_networth_tools_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*NetWorthDataItem_NetWorthSummary)(nil),
		(*NetWorthDataItem_MutualFundAnalytics)(nil),
		(*NetWorthDataItem_ConnectedAccountDetails)(nil),
		(*NetWorthDataItem_MutualFundTransactionList)(nil),
		(*NetWorthDataItem_EpfAccountData)(nil),
		(*NetWorthDataItem_CreditReportData)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_mcp_networth_tools_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_mcp_networth_tools_proto_goTypes,
		DependencyIndexes: file_api_mcp_networth_tools_proto_depIdxs,
		MessageInfos:      file_api_mcp_networth_tools_proto_msgTypes,
	}.Build()
	File_api_mcp_networth_tools_proto = out.File
	file_api_mcp_networth_tools_proto_rawDesc = nil
	file_api_mcp_networth_tools_proto_goTypes = nil
	file_api_mcp_networth_tools_proto_depIdxs = nil
}
