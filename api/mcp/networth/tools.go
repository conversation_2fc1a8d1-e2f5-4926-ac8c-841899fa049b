package networth

import (
	"github.com/epifi/gamma/api/analyser/variables/mutualfund"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/insights/epf"
	"github.com/epifi/gamma/api/insights/networth"
)

type Component interface {
	*networth.GetNetWorthValueResponse | *mutualfund.MfSchemeAnalytics | *connected_account.GetAccountDetailsBulkResponse | *MutualFundTransactionList | *epf.GetUANAccountsResponse | *creditreportv2.GetCreditReportsResponse
}

func NewNetworthToolComponent[T Component](component T, description string) *NetWorthDataItem {
	networthDataItem := &NetWorthDataItem{ComponentDescription: description}
	switch any(component).(type) {
	case *networth.GetNetWorthValueResponse:
		networthDataItem.DataObject = &NetWorthDataItem_NetWorthSummary{NetWorthSummary: any(component).(*networth.GetNetWorthValueResponse)}
	case *mutualfund.MfSchemeAnalytics:
		networthDataItem.DataObject = &NetWorthDataItem_MutualFundAnalytics{MutualFundAnalytics: any(component).(*mutualfund.MfSchemeAnalytics)}
	case *connected_account.GetAccountDetailsBulkResponse:
		networthDataItem.DataObject = &NetWorthDataItem_ConnectedAccountDetails{ConnectedAccountDetails: any(component).(*connected_account.GetAccountDetailsBulkResponse)}
	case *MutualFundTransactionList:
		networthDataItem.DataObject = &NetWorthDataItem_MutualFundTransactionList{MutualFundTransactionList: any(component).(*MutualFundTransactionList)}
	case *epf.GetUANAccountsResponse:
		networthDataItem.DataObject = &NetWorthDataItem_EpfAccountData{EpfAccountData: any(component).(*epf.GetUANAccountsResponse)}
	case *creditreportv2.GetCreditReportsResponse:
		networthDataItem.DataObject = &NetWorthDataItem_CreditReportData{CreditReportData: any(component).(*creditreportv2.GetCreditReportsResponse)}
	}
	return networthDataItem
}
