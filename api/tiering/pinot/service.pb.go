// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/tiering/pinot/service.proto

package pinot

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetAverageEODBalanceInDateRangeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory actor_id identifier
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// From timestamp of the date range
	// Provided timestamp will be rounded down to start of the day
	FromTimestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=from_timestamp,json=fromTimestamp,proto3" json:"from_timestamp,omitempty"`
	// To timestamp of the date range
	// Provided timestamp will be rounded down to end of the day
	ToTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=to_timestamp,json=toTimestamp,proto3" json:"to_timestamp,omitempty"`
	// Optional timezone for interpreting the timestamps (e.g., "Asia/Kolkata", "UTC")
	// If not provided, timestamps will be interpreted in UTC
	Timezone string `protobuf:"bytes,4,opt,name=timezone,proto3" json:"timezone,omitempty"`
}

func (x *GetAverageEODBalanceInDateRangeRequest) Reset() {
	*x = GetAverageEODBalanceInDateRangeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_pinot_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAverageEODBalanceInDateRangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAverageEODBalanceInDateRangeRequest) ProtoMessage() {}

func (x *GetAverageEODBalanceInDateRangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_pinot_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAverageEODBalanceInDateRangeRequest.ProtoReflect.Descriptor instead.
func (*GetAverageEODBalanceInDateRangeRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_pinot_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetAverageEODBalanceInDateRangeRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAverageEODBalanceInDateRangeRequest) GetFromTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTimestamp
	}
	return nil
}

func (x *GetAverageEODBalanceInDateRangeRequest) GetToTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ToTimestamp
	}
	return nil
}

func (x *GetAverageEODBalanceInDateRangeRequest) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

type GetAverageEODBalanceInDateRangeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Average balance
	AvgBalance float64 `protobuf:"fixed64,2,opt,name=avg_balance,json=avgBalance,proto3" json:"avg_balance,omitempty"`
	// If request number of days data is not available it returns the number of days that the data is returned for
	NumOfDays int64 `protobuf:"varint,3,opt,name=num_of_days,json=numOfDays,proto3" json:"num_of_days,omitempty"`
}

func (x *GetAverageEODBalanceInDateRangeResponse) Reset() {
	*x = GetAverageEODBalanceInDateRangeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_pinot_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAverageEODBalanceInDateRangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAverageEODBalanceInDateRangeResponse) ProtoMessage() {}

func (x *GetAverageEODBalanceInDateRangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_pinot_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAverageEODBalanceInDateRangeResponse.ProtoReflect.Descriptor instead.
func (*GetAverageEODBalanceInDateRangeResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_pinot_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetAverageEODBalanceInDateRangeResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAverageEODBalanceInDateRangeResponse) GetAvgBalance() float64 {
	if x != nil {
		return x.AvgBalance
	}
	return 0
}

func (x *GetAverageEODBalanceInDateRangeResponse) GetNumOfDays() int64 {
	if x != nil {
		return x.NumOfDays
	}
	return 0
}

type GetAverageEODBalanceInDateRangeDayWiseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory actor_id identifier
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// From timestamp of the date range
	// Provided timestamp will be rounded down to start of the day
	FromTimestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=from_timestamp,json=fromTimestamp,proto3" json:"from_timestamp,omitempty"`
	// To timestamp of the date range
	// Provided timestamp will be rounded down to end of the day
	ToTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=to_timestamp,json=toTimestamp,proto3" json:"to_timestamp,omitempty"`
	// Optional timezone for interpreting the timestamps (e.g., "Asia/Kolkata", "UTC")
	// If not provided, timestamps will be interpreted in UTC
	Timezone string `protobuf:"bytes,4,opt,name=timezone,proto3" json:"timezone,omitempty"`
}

func (x *GetAverageEODBalanceInDateRangeDayWiseRequest) Reset() {
	*x = GetAverageEODBalanceInDateRangeDayWiseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_pinot_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAverageEODBalanceInDateRangeDayWiseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAverageEODBalanceInDateRangeDayWiseRequest) ProtoMessage() {}

func (x *GetAverageEODBalanceInDateRangeDayWiseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_pinot_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAverageEODBalanceInDateRangeDayWiseRequest.ProtoReflect.Descriptor instead.
func (*GetAverageEODBalanceInDateRangeDayWiseRequest) Descriptor() ([]byte, []int) {
	return file_api_tiering_pinot_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetAverageEODBalanceInDateRangeDayWiseRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAverageEODBalanceInDateRangeDayWiseRequest) GetFromTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTimestamp
	}
	return nil
}

func (x *GetAverageEODBalanceInDateRangeDayWiseRequest) GetToTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ToTimestamp
	}
	return nil
}

func (x *GetAverageEODBalanceInDateRangeDayWiseRequest) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

type GetAverageEODBalanceInDateRangeDayWiseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status            *rpc.Status                                                        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	DayWiseEodBalance []*GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate `protobuf:"bytes,2,rep,name=day_wise_eod_balance,json=dayWiseEodBalance,proto3" json:"day_wise_eod_balance,omitempty"`
}

func (x *GetAverageEODBalanceInDateRangeDayWiseResponse) Reset() {
	*x = GetAverageEODBalanceInDateRangeDayWiseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_pinot_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAverageEODBalanceInDateRangeDayWiseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAverageEODBalanceInDateRangeDayWiseResponse) ProtoMessage() {}

func (x *GetAverageEODBalanceInDateRangeDayWiseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_pinot_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAverageEODBalanceInDateRangeDayWiseResponse.ProtoReflect.Descriptor instead.
func (*GetAverageEODBalanceInDateRangeDayWiseResponse) Descriptor() ([]byte, []int) {
	return file_api_tiering_pinot_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetAverageEODBalanceInDateRangeDayWiseResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAverageEODBalanceInDateRangeDayWiseResponse) GetDayWiseEodBalance() []*GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate {
	if x != nil {
		return x.DayWiseEodBalance
	}
	return nil
}

type GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// EOD balance
	Balance float64       `protobuf:"fixed64,1,opt,name=balance,proto3" json:"balance,omitempty"`
	Date    *typesv2.Date `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
}

func (x *GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate) Reset() {
	*x = GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_tiering_pinot_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate) ProtoMessage() {}

func (x *GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate) ProtoReflect() protoreflect.Message {
	mi := &file_api_tiering_pinot_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate.ProtoReflect.Descriptor instead.
func (*GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate) Descriptor() ([]byte, []int) {
	return file_api_tiering_pinot_service_proto_rawDescGZIP(), []int{3, 0}
}

func (x *GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate) GetDate() *typesv2.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

var File_api_tiering_pinot_service_proto protoreflect.FileDescriptor

var file_api_tiering_pinot_service_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x69,
	0x6e, 0x6f, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0d, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74,
	0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfe, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74,
	0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x45, 0x4f, 0x44, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x49, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0e, 0x66, 0x72, 0x6f, 0x6d, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0d, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x47, 0x0a, 0x0c, 0x74, 0x6f, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01,
	0x52, 0x0b, 0x74, 0x6f, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1a, 0x0a,
	0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x22, 0x8f, 0x01, 0x0a, 0x27, 0x47, 0x65,
	0x74, 0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x45, 0x4f, 0x44, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x49, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76,
	0x67, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0a, 0x61, 0x76, 0x67, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x6e,
	0x75, 0x6d, 0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x6e, 0x75, 0x6d, 0x4f, 0x66, 0x44, 0x61, 0x79, 0x73, 0x22, 0x85, 0x02, 0x0a, 0x2d,
	0x47, 0x65, 0x74, 0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x45, 0x4f, 0x44, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44,
	0x61, 0x79, 0x57, 0x69, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x4b, 0x0a, 0x0e, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52,
	0x0d, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x47,
	0x0a, 0x0c, 0x74, 0x6f, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0b, 0x74, 0x6f, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a,
	0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a,
	0x6f, 0x6e, 0x65, 0x22, 0xab, 0x02, 0x0a, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x45, 0x4f, 0x44, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x61, 0x79, 0x57, 0x69, 0x73, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x7f, 0x0a, 0x14, 0x64,
	0x61, 0x79, 0x5f, 0x77, 0x69, 0x73, 0x65, 0x5f, 0x65, 0x6f, 0x64, 0x5f, 0x62, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x74, 0x69, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x65,
	0x72, 0x61, 0x67, 0x65, 0x45, 0x4f, 0x44, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x61, 0x79, 0x57, 0x69, 0x73, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x4f, 0x44, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x42, 0x79, 0x44, 0x61, 0x74, 0x65, 0x52, 0x11, 0x64, 0x61, 0x79, 0x57, 0x69,
	0x73, 0x65, 0x45, 0x6f, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x53, 0x0a, 0x10,
	0x45, 0x4f, 0x44, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x79, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x65, 0x32, 0xc7, 0x02, 0x0a, 0x0a, 0x45, 0x4f, 0x44, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x90, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x45,
	0x4f, 0x44, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x35, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x45,
	0x4f, 0x44, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x74, 0x69,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x45, 0x4f, 0x44, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x49, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0xa5, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x41, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x45, 0x4f, 0x44, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x61, 0x79, 0x57, 0x69, 0x73, 0x65, 0x12, 0x3c,
	0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x45, 0x4f, 0x44, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x49, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x61,
	0x79, 0x57, 0x69, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x45, 0x4f, 0x44, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x49, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x61, 0x79, 0x57,
	0x69, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x54, 0x0a, 0x28, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x69, 0x6e, 0x6f,
	0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_tiering_pinot_service_proto_rawDescOnce sync.Once
	file_api_tiering_pinot_service_proto_rawDescData = file_api_tiering_pinot_service_proto_rawDesc
)

func file_api_tiering_pinot_service_proto_rawDescGZIP() []byte {
	file_api_tiering_pinot_service_proto_rawDescOnce.Do(func() {
		file_api_tiering_pinot_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_tiering_pinot_service_proto_rawDescData)
	})
	return file_api_tiering_pinot_service_proto_rawDescData
}

var file_api_tiering_pinot_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_tiering_pinot_service_proto_goTypes = []interface{}{
	(*GetAverageEODBalanceInDateRangeRequest)(nil),                          // 0: tiering.pinot.GetAverageEODBalanceInDateRangeRequest
	(*GetAverageEODBalanceInDateRangeResponse)(nil),                         // 1: tiering.pinot.GetAverageEODBalanceInDateRangeResponse
	(*GetAverageEODBalanceInDateRangeDayWiseRequest)(nil),                   // 2: tiering.pinot.GetAverageEODBalanceInDateRangeDayWiseRequest
	(*GetAverageEODBalanceInDateRangeDayWiseResponse)(nil),                  // 3: tiering.pinot.GetAverageEODBalanceInDateRangeDayWiseResponse
	(*GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate)(nil), // 4: tiering.pinot.GetAverageEODBalanceInDateRangeDayWiseResponse.EODBalanceByDate
	(*timestamppb.Timestamp)(nil),                                           // 5: google.protobuf.Timestamp
	(*rpc.Status)(nil),                                                      // 6: rpc.Status
	(*typesv2.Date)(nil),                                                    // 7: api.typesv2.Date
}
var file_api_tiering_pinot_service_proto_depIdxs = []int32{
	5,  // 0: tiering.pinot.GetAverageEODBalanceInDateRangeRequest.from_timestamp:type_name -> google.protobuf.Timestamp
	5,  // 1: tiering.pinot.GetAverageEODBalanceInDateRangeRequest.to_timestamp:type_name -> google.protobuf.Timestamp
	6,  // 2: tiering.pinot.GetAverageEODBalanceInDateRangeResponse.status:type_name -> rpc.Status
	5,  // 3: tiering.pinot.GetAverageEODBalanceInDateRangeDayWiseRequest.from_timestamp:type_name -> google.protobuf.Timestamp
	5,  // 4: tiering.pinot.GetAverageEODBalanceInDateRangeDayWiseRequest.to_timestamp:type_name -> google.protobuf.Timestamp
	6,  // 5: tiering.pinot.GetAverageEODBalanceInDateRangeDayWiseResponse.status:type_name -> rpc.Status
	4,  // 6: tiering.pinot.GetAverageEODBalanceInDateRangeDayWiseResponse.day_wise_eod_balance:type_name -> tiering.pinot.GetAverageEODBalanceInDateRangeDayWiseResponse.EODBalanceByDate
	7,  // 7: tiering.pinot.GetAverageEODBalanceInDateRangeDayWiseResponse.EODBalanceByDate.date:type_name -> api.typesv2.Date
	0,  // 8: tiering.pinot.EODBalance.GetAverageEODBalanceInDateRange:input_type -> tiering.pinot.GetAverageEODBalanceInDateRangeRequest
	2,  // 9: tiering.pinot.EODBalance.GetAverageEODBalanceInDateRangeDayWise:input_type -> tiering.pinot.GetAverageEODBalanceInDateRangeDayWiseRequest
	1,  // 10: tiering.pinot.EODBalance.GetAverageEODBalanceInDateRange:output_type -> tiering.pinot.GetAverageEODBalanceInDateRangeResponse
	3,  // 11: tiering.pinot.EODBalance.GetAverageEODBalanceInDateRangeDayWise:output_type -> tiering.pinot.GetAverageEODBalanceInDateRangeDayWiseResponse
	10, // [10:12] is the sub-list for method output_type
	8,  // [8:10] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_tiering_pinot_service_proto_init() }
func file_api_tiering_pinot_service_proto_init() {
	if File_api_tiering_pinot_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_tiering_pinot_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAverageEODBalanceInDateRangeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_pinot_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAverageEODBalanceInDateRangeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_pinot_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAverageEODBalanceInDateRangeDayWiseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_pinot_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAverageEODBalanceInDateRangeDayWiseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_tiering_pinot_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAverageEODBalanceInDateRangeDayWiseResponse_EODBalanceByDate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_tiering_pinot_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_tiering_pinot_service_proto_goTypes,
		DependencyIndexes: file_api_tiering_pinot_service_proto_depIdxs,
		MessageInfos:      file_api_tiering_pinot_service_proto_msgTypes,
	}.Build()
	File_api_tiering_pinot_service_proto = out.File
	file_api_tiering_pinot_service_proto_rawDesc = nil
	file_api_tiering_pinot_service_proto_goTypes = nil
	file_api_tiering_pinot_service_proto_depIdxs = nil
}
