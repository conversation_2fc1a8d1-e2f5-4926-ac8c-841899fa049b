package bre

import (
	"context"
	"time"

	"google.golang.org/protobuf/types/known/structpb"

	"github.com/epifi/gamma/api/simulator/lending/bre"
	breVg "github.com/epifi/gamma/api/vendors/inhouse/bre"
)

const timestampFormat = "2006-01-02T15:04:05.000Z"

const (
	moneyviewOnlyPan         = "**********"
	federalOnlyPan           = "**********"
	abflOnlyPan              = "**********"
	stockGuardianOnlyPan     = "**********"
	lendenOnlyPan            = "**********"
	zeroValidLendersPan      = "**********"
	finalBreRejectedPan      = "**********"
	declinedPreBreOfferPan   = "**********"
	MvFedFinalBrePan         = "**********"
	MvFedSgFinalBrePan       = "**********"
	fedVendorBreRejectPan    = "**********"
	fedRejInstantLoanFailPan = "**********"
	fedRejProfileValidPan    = "**********"
)

type Service struct {
	bre.UnimplementedBusinessRuleEngineServer
}

func NewService() *Service {
	return &Service{}
}
func (s *Service) InhouseBreCheckForCC(_ context.Context, req *breVg.InhouseBRECheckForCCRequest) (*breVg.InhouseBRECheckForCCResponse, error) {
	var (
		originalDec, overrideDec string
		override                 int32
		dob                      = req.GetValues().GetInput().GetDob()
	)
	switch dob[len(dob)-4:] {
	case "1995":
		originalDec = "Declined"
		overrideDec = "Declined"
		override = 0
	case "1996":
		originalDec = "Declined"
		overrideDec = "Approved"
		override = 1
	default:
		originalDec = "Approved"
		overrideDec = "Approved"
		override = 0
	}
	return &breVg.InhouseBRECheckForCCResponse{
		Flow: &breVg.InhouseBRECheckForCCResponse_Flow{
			Products: &breVg.InhouseBRECheckForCCResponse_Flow_Products{
				Product: &breVg.InhouseBRECheckForCCResponse_Flow_Products_Product{
					Action: overrideDec,
				},
			},
		},
		Entities: &breVg.InhouseBRECheckForCCResponse_Entities{
			Values: &breVg.InhouseBRECheckForCCResponse_Entities_Values{
				Splitter: &breVg.InhouseBRECheckForCCResponse_Entities_Values_Splitter{
					Split: &breVg.InhouseBRECheckForCCResponse_Entities_Values_Splitter_Split{
						Index: override,
					},
				},
				Strategy: &breVg.InhouseBRECheckForCCResponse_Entities_Values_Strategy{
					CcEligibilityChecks: &breVg.InhouseBRECheckForCCResponse_Entities_Values_Strategy_CCEligibilityChecks{
						Action: originalDec,
					},
				},
			},
		},
	}, nil
}

func (s *Service) GetLoanPreBre(ctx context.Context, req *breVg.GetPreBreEligibilityDetailsRequest) (*breVg.GetPreBreEligibilityDetailsResponse, error) {
	reqInput := req.GetValues().GetInput()
	pan := reqInput.GetCustomerDetails().GetPersonalDetails().GetPan()
	var response *breVg.GetPreBreEligibilityDetailsResponse
	var validLenders []string
	var numLenders int32
	switch pan {
	case zeroValidLendersPan:
		validLenders = []string{}
		numLenders = 0
	case lendenOnlyPan:
		validLenders = []string{"LENDEN"}
		numLenders = 1
	case stockGuardianOnlyPan:
		validLenders = []string{"STOCK_GUARDIAN_LSP"}
		numLenders = 1
	case abflOnlyPan:
		validLenders = []string{"ABFL"}
		numLenders = 1
	case federalOnlyPan, fedVendorBreRejectPan, fedRejInstantLoanFailPan, fedRejProfileValidPan:
		validLenders = []string{"FEDERAL"}
		numLenders = 1
	case moneyviewOnlyPan:
		validLenders = []string{"MONEYVIEW"}
		numLenders = 1
	default:
		validLenders = []string{"LENDEN", "STOCK_GUARDIAN_LSP", "ABFL", "FEDERAL", "MONEYVIEW"}
		numLenders = 5
	}
	response = &breVg.GetPreBreEligibilityDetailsResponse{
		Decision: &breVg.GetPreBreEligibilityDetailsResponse_PreBreDecision{
			ActorId:               reqInput.GetActorId(),
			EvaluationRequestTime: reqInput.GetEvaluationRequestTime(),
			EvaluationRunTime:     reqInput.GetEvaluationRequestTime(),
			RequestId:             reqInput.GetRequestId(),
			ValidTill:             time.Now().Add(time.Hour * 24).Format(timestampFormat),
			NumberValidLenders:    numLenders,
			ValidLenders:          validLenders,
			PolicyParams: &breVg.PolicyParams{
				ExecutionInfo: &breVg.ExecutionInfo{
					Pre: []*breVg.PolicyPramsDetails{
						{
							BatchId:         "_BATCH_20250113",
							SchemeId:        "preBRE_1_001",
							PreBreRequestId: reqInput.GetRequestId(),
						},
					},
				},
			},
		},
		Details: &structpb.Struct{},
	}
	return response, nil
}

// nolint:gocritic,goconst
func (s *Service) GetLoanFinalBre(ctx context.Context, req *breVg.GetFinalBreEligibilityDetailsRequest) (*breVg.GetFinalBreEligibilityDetailsResponse, error) {
	reqInput := req.GetValues().GetInput()
	res := &breVg.GetFinalBreEligibilityDetailsResponse{
		EvaluationRequestTime: reqInput.GetEvaluationRequestTime(),
		RequestId:             reqInput.GetRequestId(),
		ActorId:               reqInput.GetActorId(),
		Vendor:                reqInput.GetVendor(),
		Product:               reqInput.GetProduct(),
		DataAvailability:      reqInput.GetDataAvailability(),
		EvaluationRunTime:     reqInput.GetEvaluationRequestTime(),
		PolicyParams: &breVg.PolicyParams{
			ExecutionInfo: &breVg.ExecutionInfo{
				Pre: []*breVg.PolicyPramsDetails{
					{
						BatchId:         "_BATCH_20250113",
						SchemeId:        "preBRE_1_001",
						PreBreRequestId: reqInput.GetRequestId(),
					},
				},
			},
		},
	}
	approvedDecision := &breVg.FinalBreDecision{
		LendingProgram: "PL_SALARIED_LP02",
		Action:         "Approved",
		OfferDetails: &breVg.OfferDetails{
			EmiDueDate:              "",
			GstPercentage:           18,
			InterestPercentage:      24,
			MaxAmount:               150000.00,
			MaxEmiAmount:            20000.00,
			MaxTenureInMonths:       36,
			MinAmount:               10000.00,
			MinTenureInMonths:       3,
			ProcessingFeePercentage: 4,
			PricingScheme:           "",
		},
		ValidTill: time.Now().Add(time.Hour * 24 * 10).Format(timestampFormat),
	}
	switch {
	case reqInput.GetCustomerDetails().GetPersonalDetails().GetPan() == finalBreRejectedPan:
		res.SubsequentCallAllowed = false
	case reqInput.GetCustomerDetails().GetPersonalDetails().GetPan() == MvFedFinalBrePan:
		if reqInput.GetVendor() != "MONEYVIEW" && reqInput.GetVendor() != "FEDERAL" {
			res.SubsequentCallAllowed = false
			return res, nil
		}

		if !req.GetValues().GetInput().GetDataAvailability().GetCibil().GetIsAvailable() {
			res.SubsequentCallAllowed = true
			switch reqInput.GetVendor() {
			case "MONEYVIEW":
				res.DataRequirements = map[string]bool{"MONEYVIEW_PL_ALL_LP01": true}
			case "FEDERAL":
				res.DataRequirements = map[string]bool{"FEDERAL_PL_SALARIED_LP01": true}
			}
			return res, nil
		}

		res.PrioritizedDecision = &breVg.FinalBreDecision{
			LendingProgram: "PL_SALARIED_LP02",
			Action:         "Approved",
			OfferDetails: &breVg.OfferDetails{
				EmiDueDate:              "",
				GstPercentage:           18,
				InterestPercentage:      24,
				MaxAmount:               150000.00,
				MaxEmiAmount:            20000.00,
				MaxTenureInMonths:       36,
				MinAmount:               1000.00,
				MinTenureInMonths:       3,
				ProcessingFeePercentage: 4,
				PricingScheme:           "",
			},
			ValidTill: time.Now().Add(time.Hour * 24 * 10).Format(timestampFormat),
		}

	case reqInput.GetCustomerDetails().GetPersonalDetails().GetPan() == MvFedSgFinalBrePan:
		allowedVendors := map[string]bool{
			"MONEYVIEW":          true,
			"FEDERAL":            true,
			"STOCK_GUARDIAN_LSP": true,
		}

		if !allowedVendors[reqInput.GetVendor()] {
			res.SubsequentCallAllowed = false
			return res, nil
		}

		if !req.GetValues().GetInput().GetDataAvailability().GetCibil().GetIsAvailable() {
			res.SubsequentCallAllowed = true
			switch reqInput.GetVendor() {
			case "MONEYVIEW":
				res.DataRequirements = map[string]bool{"MONEYVIEW_PL_ALL_LP01": true}
			case "FEDERAL":
				res.DataRequirements = map[string]bool{"FEDERAL_PL_SALARIED_LP01": true}
			case "STOCK_GUARDIAN_LSP":
				res.DataRequirements = map[string]bool{"STOCK_GUARDIAN_LSP_PL_ALL_LP01": true}
			}
			return res, nil
		}

		res.PrioritizedDecision = &breVg.FinalBreDecision{
			LendingProgram: "PL_SALARIED_LP02",
			Action:         "Approved",
			OfferDetails: &breVg.OfferDetails{
				EmiDueDate:              "",
				GstPercentage:           18,
				InterestPercentage:      24,
				MaxAmount:               150000.00,
				MaxEmiAmount:            20000.00,
				MaxTenureInMonths:       36,
				MinAmount:               1000.00,
				MinTenureInMonths:       3,
				ProcessingFeePercentage: 4,
				PricingScheme:           "",
			},
			ValidTill: time.Now().Add(time.Hour * 24 * 10).Format(timestampFormat),
		}

	case reqInput.GetVendor() == "STOCK_GUARDIAN_LSP" && reqInput.GetProduct() == "ES":
		if !req.GetValues().GetInput().GetDataAvailability().GetCibil().GetIsAvailable() {
			res.DataRequirements = map[string]bool{"ES_SALARIED_LP01": true}
			res.SubsequentCallAllowed = true
		} else {
			approvedDecision.OfferDetails.MaxAmount = 50000.00
			approvedDecision.OfferDetails.MinAmount = 10000.00
			approvedDecision.OfferDetails.InterestPercentage = 0.00
			approvedDecision.LendingProgram = "ES_SALARIED_LP01"
			approvedDecision.OfferDetails.MaxTenureInMonths = 1
			approvedDecision.OfferDetails.MinTenureInMonths = 1
			approvedDecision.OfferDetails.MaxEmiAmount = 50000.00
			res.PrioritizedDecision = approvedDecision
		}
	case reqInput.GetVendor() == "STOCK_GUARDIAN_LSP":
		if !req.GetValues().GetInput().GetDataAvailability().GetAa().GetIsAvailable() && req.GetValues().GetInput().GetCustomerDetails().GetRequestedLoanDetails().GetDesiredLoanAmount() > 200000 {
			res.DataRequirements = map[string]bool{"PL_SALARIED_LP03": true}
			res.SubsequentCallAllowed = true
		} else if !req.GetValues().GetInput().GetDataAvailability().GetCibil().GetIsAvailable() {
			res.DataRequirements = map[string]bool{"PL_SALARIED_LP02": true}
			res.SubsequentCallAllowed = true
		} else {
			res.PrioritizedDecision = approvedDecision
		}
	case reqInput.GetVendor() == "ABFL" && !req.GetValues().GetInput().GetDataAvailability().GetCibil().GetIsAvailable():
		res.DataRequirements = map[string]bool{"ABFL_PL_ALL_LP01": true}
		res.SubsequentCallAllowed = true
	case reqInput.GetVendor() == "MONEYVIEW" && !req.GetValues().GetInput().GetDataAvailability().GetCibil().GetIsAvailable():
		res.DataRequirements = map[string]bool{"MONEYVIEW_PL_ALL_LP01": true}
		res.SubsequentCallAllowed = true
	case reqInput.GetVendor() == "LENDEN" && !req.GetValues().GetInput().GetDataAvailability().GetCibil().GetIsAvailable():
		res.DataRequirements = map[string]bool{"LENDEN_PL_ALL_LP01": true}
		res.SubsequentCallAllowed = true
	case reqInput.GetVendor() == "FEDERAL":
		if !req.GetValues().GetInput().GetDataAvailability().GetCibil().GetIsAvailable() {
			res.DataRequirements = map[string]bool{"FEDERAL_PL_SALARIED_LP01": true}
			res.SubsequentCallAllowed = true
		} else {
			res.PrioritizedDecision = &breVg.FinalBreDecision{
				LendingProgram: "PL_SALARIED_LP02",
				Action:         "Approved",
				OfferDetails: &breVg.OfferDetails{
					MaxAmount: 500000.00,
				},
				ValidTill: time.Now().Add(time.Hour * 24 * 10).Format(timestampFormat),
			}
		}
	default:
		res.PrioritizedDecision = approvedDecision
	}
	return res, nil
}

// nolint:gocritic
func (s *Service) GetLoanPreBreOffer(ctx context.Context, req *breVg.GetPreBreEligibilityOfferRequest) (*breVg.GetPreBreEligibilityOfferResponse, error) {
	reqInput := req.GetValues().GetInput()
	pan := reqInput.GetCustomerDetails().GetPersonalDetails().GetPan()
	var decision []*breVg.FinalBreDecision

	if pan == declinedPreBreOfferPan {
		// Declined case for specific PAN
		decision = []*breVg.FinalBreDecision{
			{
				LendingProgram: "PL_SALARIED_LP02",
				Action:         "Declined",
				RejectionReasons: []string{
					"High risk profile",
					"Low credit score",
				},
				ExternalReasons: []string{
					"Credit score below threshold",
				},
				ValidTill: time.Now().Add(time.Hour * 24 * 10).Format(timestampFormat),
			},
		}
	} else {
		// Approved case for all other PANs
		decision = []*breVg.FinalBreDecision{
			{
				LendingProgram: "PL_SALARIED_LP02",
				Action:         "Approved",
				OfferDetails: &breVg.OfferDetails{
					EmiDueDate:              "",
					GstPercentage:           18,
					InterestPercentage:      24,
					MaxAmount:               150000.00,
					MaxEmiAmount:            20000.00,
					MaxTenureInMonths:       36,
					MinAmount:               1000.00,
					MinTenureInMonths:       3,
					ProcessingFeePercentage: 4,
					PricingScheme:           "",
				},
				ValidTill: time.Now().Add(time.Hour * 24 * 10).Format(timestampFormat),
			},
		}
	}

	res := &breVg.GetPreBreEligibilityOfferResponse{
		EvaluationRequestTime: reqInput.GetEvaluationRequestTime(),
		RequestId:             reqInput.GetRequestId(),
		ActorId:               reqInput.GetActorId(),
		Product:               reqInput.GetProduct(),
		DataAvailability:      reqInput.GetDataAvailability(),
		EvaluationRunTime:     reqInput.GetEvaluationRequestTime(),
		DataRequirements:      map[string]bool{},
		SubsequentCallAllowed: false,
		Decisions:             decision,
	}

	return res, nil
}
