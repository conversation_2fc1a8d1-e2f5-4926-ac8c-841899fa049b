package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

const (
	// Event names
	MCPNetWorthFetchedEvent     = "MCPNetWorthFetched"
	MCPTransactionsFetchedEvent = "MCPTransactionsFetched"
	MCPCreditReportFetchedEvent = "MCPCreditReportFetched"
	MCPAuthenticationEvent      = "MCPAuthentication"
	MCPToolCallEvent            = "MCPToolCall"
	MCPOnboardingEvent          = "MCPOnboarding"
	MCPChatEvent                = "MCPChat"

	// Status constants
	Success = "success"
	Failure = "failure"

	// Authentication types
	AuthTypeMobileNo = "mobile_no"
	AuthTypeScan     = "scan"

	// User types
	UserTypeInternal    = "internal"
	UserTypeNonInternal = "non_internal"

	// Service name
	ServiceName = "mcp-networth"
)

// MCPNetWorthFetched tracks when users fetch net worth data via MCP
type MCPNetWorthFetched struct {
	EventType     string
	EventId       string
	ActorId       string
	ProspectId    string
	SessionId     string
	Timestamp     time.Time
	Status        string
	FailureReason string
	ServiceName   string
	// Additional context
	HasMFData     bool
	HasAAData     bool
	NetWorthValue string // obfuscated for privacy
}

func NewMCPNetWorthFetched(actorId, sessionId, status, failureReason string, hasMFData, hasAAData bool, netWorthValue string) *MCPNetWorthFetched {
	return &MCPNetWorthFetched{
		ActorId:       actorId,
		SessionId:     sessionId,
		EventType:     events.EventTrack,
		EventId:       uuid.New().String(),
		Timestamp:     time.Now(),
		Status:        status,
		FailureReason: failureReason,
		ServiceName:   ServiceName,
		HasMFData:     hasMFData,
		HasAAData:     hasAAData,
		NetWorthValue: netWorthValue,
	}
}

func (e *MCPNetWorthFetched) GetEventType() string {
	return e.EventType
}

func (e *MCPNetWorthFetched) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *MCPNetWorthFetched) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *MCPNetWorthFetched) GetEventId() string {
	return e.EventId
}

func (e *MCPNetWorthFetched) GetUserId() string {
	return e.ActorId
}

func (e *MCPNetWorthFetched) GetProspectId() string {
	return e.ProspectId
}

func (e *MCPNetWorthFetched) GetEventName() string {
	return MCPNetWorthFetchedEvent
}

// MCPTransactionsFetched tracks when users fetch transaction data via MCP
type MCPTransactionsFetched struct {
	EventType     string
	EventId       string
	ActorId       string
	ProspectId    string
	SessionId     string
	Timestamp     time.Time
	Status        string
	FailureReason string
	ServiceName   string
	// Additional context
	MFTransactionCount  int
	EPFTransactionCount int
}

func NewMCPTransactionsFetched(actorId, sessionId, status, failureReason string, mfTxnCount, epfTxnCount int) *MCPTransactionsFetched {
	return &MCPTransactionsFetched{
		ActorId:             actorId,
		SessionId:           sessionId,
		EventType:           events.EventTrack,
		EventId:             uuid.New().String(),
		Timestamp:           time.Now(),
		Status:              status,
		FailureReason:       failureReason,
		ServiceName:         ServiceName,
		MFTransactionCount:  mfTxnCount,
		EPFTransactionCount: epfTxnCount,
	}
}

func (e *MCPTransactionsFetched) GetEventType() string {
	return e.EventType
}

func (e *MCPTransactionsFetched) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *MCPTransactionsFetched) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *MCPTransactionsFetched) GetEventId() string {
	return e.EventId
}

func (e *MCPTransactionsFetched) GetUserId() string {
	return e.ActorId
}

func (e *MCPTransactionsFetched) GetProspectId() string {
	return e.ProspectId
}

func (e *MCPTransactionsFetched) GetEventName() string {
	return MCPTransactionsFetchedEvent
}

// MCPCreditReportFetched tracks when users fetch credit report via MCP
type MCPCreditReportFetched struct {
	EventType     string
	EventId       string
	ActorId       string
	ProspectId    string
	SessionId     string
	Timestamp     time.Time
	Status        string
	FailureReason string
	ServiceName   string
	// Additional context
	CreditScore string // obfuscated for privacy
	HasLoanData bool
	HasCardData bool
}

func NewMCPCreditReportFetched(actorId, sessionId, status, failureReason, creditScore string, hasLoanData, hasCardData bool) *MCPCreditReportFetched {
	return &MCPCreditReportFetched{
		ActorId:       actorId,
		SessionId:     sessionId,
		EventType:     events.EventTrack,
		EventId:       uuid.New().String(),
		Timestamp:     time.Now(),
		Status:        status,
		FailureReason: failureReason,
		ServiceName:   ServiceName,
		CreditScore:   creditScore,
		HasLoanData:   hasLoanData,
		HasCardData:   hasCardData,
	}
}

func (e *MCPCreditReportFetched) GetEventType() string {
	return e.EventType
}

func (e *MCPCreditReportFetched) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *MCPCreditReportFetched) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *MCPCreditReportFetched) GetEventId() string {
	return e.EventId
}

func (e *MCPCreditReportFetched) GetUserId() string {
	return e.ActorId
}

func (e *MCPCreditReportFetched) GetProspectId() string {
	return e.ProspectId
}

func (e *MCPCreditReportFetched) GetEventName() string {
	return MCPCreditReportFetchedEvent
}

// MCPAuthentication tracks authentication events in MCP
type MCPAuthentication struct {
	EventType     string
	EventId       string
	ActorId       string
	ProspectId    string
	SessionId     string
	Timestamp     time.Time
	Status        string
	FailureReason string
	ServiceName   string
	AuthAction    string // "session_validation", "login_required", "login_completed"
	AuthType      string // "mobile_no", "scan"
	UserType      string // "internal", "non_internal"
}

func NewMCPAuthentication(actorId, sessionId, status, failureReason, authAction, authType, userType string) *MCPAuthentication {
	return &MCPAuthentication{
		ActorId:       actorId,
		SessionId:     sessionId,
		EventType:     events.EventTrack,
		EventId:       uuid.New().String(),
		Timestamp:     time.Now(),
		Status:        status,
		FailureReason: failureReason,
		ServiceName:   ServiceName,
		AuthAction:    authAction,
		AuthType:      authType,
		UserType:      userType,
	}
}

func (e *MCPAuthentication) GetEventType() string {
	return e.EventType
}

func (e *MCPAuthentication) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *MCPAuthentication) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *MCPAuthentication) GetEventId() string {
	return e.EventId
}

func (e *MCPAuthentication) GetUserId() string {
	return e.ActorId
}

func (e *MCPAuthentication) GetProspectId() string {
	return e.ProspectId
}

func (e *MCPAuthentication) GetEventName() string {
	return MCPAuthenticationEvent
}

// MCPToolCall tracks general tool call events
type MCPToolCall struct {
	EventType     string
	EventId       string
	ActorId       string
	ProspectId    string
	SessionId     string
	Timestamp     time.Time
	Status        string
	FailureReason string
	ServiceName   string
	ToolName      string
	Duration      time.Duration
}

func NewMCPToolCall(actorId, sessionId, status, failureReason, toolName string, duration time.Duration) *MCPToolCall {
	return &MCPToolCall{
		ActorId:       actorId,
		SessionId:     sessionId,
		EventType:     events.EventTrack,
		EventId:       uuid.New().String(),
		Timestamp:     time.Now(),
		Status:        status,
		FailureReason: failureReason,
		ServiceName:   ServiceName,
		ToolName:      toolName,
		Duration:      duration,
	}
}

func (e *MCPToolCall) GetEventType() string {
	return e.EventType
}

func (e *MCPToolCall) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *MCPToolCall) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *MCPToolCall) GetEventId() string {
	return e.EventId
}

func (e *MCPToolCall) GetUserId() string {
	return e.ActorId
}

func (e *MCPToolCall) GetProspectId() string {
	return e.ProspectId
}

func (e *MCPToolCall) GetEventName() string {
	return MCPToolCallEvent
}

// MCPOnboarding tracks onboarding success/failure events
type MCPOnboarding struct {
	EventType      string
	EventId        string
	ActorId        string
	ProspectId     string
	SessionId      string
	Timestamp      time.Time
	Status         string
	FailureReason  string
	ServiceName    string
	OnboardingStep string // e.g., "initial_setup", "profile_creation", "verification"
}

func NewMCPOnboarding(actorId, sessionId, status, failureReason, onboardingStep string) *MCPOnboarding {
	return &MCPOnboarding{
		ActorId:        actorId,
		SessionId:      sessionId,
		EventType:      events.EventTrack,
		EventId:        uuid.New().String(),
		Timestamp:      time.Now(),
		Status:         status,
		FailureReason:  failureReason,
		ServiceName:    ServiceName,
		OnboardingStep: onboardingStep,
	}
}

func (e *MCPOnboarding) GetEventType() string {
	return e.EventType
}

func (e *MCPOnboarding) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *MCPOnboarding) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *MCPOnboarding) GetEventId() string {
	return e.EventId
}

func (e *MCPOnboarding) GetUserId() string {
	return e.ActorId
}

func (e *MCPOnboarding) GetProspectId() string {
	return e.ProspectId
}

func (e *MCPOnboarding) GetEventName() string {
	return MCPOnboardingEvent
}

// MCPChat tracks session-level chat events with tool usage information
type MCPChat struct {
	EventType     string
	EventId       string
	ActorId       string
	ProspectId    string
	SessionId     string
	Timestamp     time.Time
	Status        string
	FailureReason string
	ServiceName   string
	// Tool usage tracking
	CreditReportToolUsed bool
	NetWorthToolUsed     bool
	TransactionsToolUsed bool
	SessionDuration      time.Duration
	TotalToolCalls       int
}

func NewMCPChat(actorId, sessionId, status, failureReason string, creditReportUsed, netWorthUsed, transactionsUsed bool, sessionDuration time.Duration, totalToolCalls int) *MCPChat {
	return &MCPChat{
		ActorId:              actorId,
		SessionId:            sessionId,
		EventType:            events.EventTrack,
		EventId:              uuid.New().String(),
		Timestamp:            time.Now(),
		Status:               status,
		FailureReason:        failureReason,
		ServiceName:          ServiceName,
		CreditReportToolUsed: creditReportUsed,
		NetWorthToolUsed:     netWorthUsed,
		TransactionsToolUsed: transactionsUsed,
		SessionDuration:      sessionDuration,
		TotalToolCalls:       totalToolCalls,
	}
}

func (e *MCPChat) GetEventType() string {
	return e.EventType
}

func (e *MCPChat) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *MCPChat) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *MCPChat) GetEventId() string {
	return e.EventId
}

func (e *MCPChat) GetUserId() string {
	return e.ActorId
}

func (e *MCPChat) GetProspectId() string {
	return e.ProspectId
}

func (e *MCPChat) GetEventName() string {
	return MCPChatEvent
}
