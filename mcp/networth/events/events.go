package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

const (
	MCPOnboardingEvent     = "MCPOnboarding"
	MCPAuthenticationEvent = "MCPAuthentication"
	MCPChatEvent           = "MCPChat"
)

// MCPOnboarding tracks onboarding page landings (session id only)
type MCPOnboarding struct {
	EventType  string
	EventId    string
	SessionId  string
	ActorId    string
	ProspectId string
	Timestamp  time.Time
}

func NewMCPOnboarding(sessionId string, actorId string, prospectId string) *MCPOnboarding {
	return &MCPOnboarding{
		SessionId:  sessionId,
		ActorId:    actorId,
		ProspectId: prospectId,
		EventType:  events.EventTrack,
		EventId:    uuid.New().String(),
		Timestamp:  time.Now(),
	}
}

func (e *MCPOnboarding) GetEventType() string {
	return e.EventType
}

func (e *MCPOnboarding) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *MCPOnboarding) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *MCPOnboarding) GetEventId() string {
	return e.EventId
}

func (e *MCPOnboarding) GetUserId() string {
	return e.ActorId
}

func (e *MCPOnboarding) GetEventName() string {
	return MCPOnboardingEvent
}

func (e *MCPOnboarding) GetProspectId() string {
	return e.ProspectId
}

// MCPAuthentication tracks authentication events with status only
type MCPAuthentication struct {
	EventType  string
	EventId    string
	ActorId    string
	SessionId  string
	ProspectId string
	Timestamp  time.Time
	Status     bool
}

func NewMCPAuthentication(actorId, sessionId string, status bool) *MCPAuthentication {
	return &MCPAuthentication{
		ActorId:   actorId,
		SessionId: sessionId,
		EventType: events.EventTrack,
		EventId:   uuid.New().String(),
		Timestamp: time.Now(),
		Status:    status,
	}
}

func (e *MCPAuthentication) GetEventType() string {
	return e.EventType
}

func (e *MCPAuthentication) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *MCPAuthentication) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *MCPAuthentication) GetEventId() string {
	return e.EventId
}

func (e *MCPAuthentication) GetUserId() string {
	return e.ActorId
}

func (e *MCPAuthentication) GetProspectId() string {
	return e.ProspectId
}

func (e *MCPAuthentication) GetEventName() string {
	return MCPAuthenticationEvent
}

// MCPChat tracks session-level chat events with tool usage and authentication status
type MCPChat struct {
	EventType            string
	EventId              string
	ActorId              string
	SessionId            string
	Timestamp            time.Time
	CreditReportToolUsed bool
	NetWorthToolUsed     bool
	TransactionsToolUsed bool
}

func NewMCPChat(actorId, sessionId string, creditReportUsed, netWorthUsed, transactionsUsed bool) *MCPChat {
	return &MCPChat{
		ActorId:              actorId,
		SessionId:            sessionId,
		EventType:            events.EventTrack,
		EventId:              uuid.New().String(),
		Timestamp:            time.Now(),
		CreditReportToolUsed: creditReportUsed,
		NetWorthToolUsed:     netWorthUsed,
		TransactionsToolUsed: transactionsUsed,
	}
}

func (e *MCPChat) GetEventType() string {
	return e.EventType
}

func (e *MCPChat) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *MCPChat) GetEventTraits() map[string]interface{} {
	return nil
}

func (e *MCPChat) GetEventId() string {
	return e.EventId
}

func (e *MCPChat) GetUserId() string {
	return e.ActorId
}

func (e *MCPChat) GetEventName() string {
	return MCPChatEvent
}

func (e *MCPChat) GetProspectId() string {
	return ""
}
