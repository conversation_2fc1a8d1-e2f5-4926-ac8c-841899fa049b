package events

import (
	"context"
	"time"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
)

// EventPublisher provides methods to publish MCP events
type EventPublisher struct {
	eventBroker events.Broker
}

func NewEventPublisher(eventBroker events.Broker) *EventPublisher {
	return &EventPublisher{
		eventBroker: eventBroker,
	}
}

// PublishOnboardingEvent publishes onboarding success/failure events
func (p *EventPublisher) PublishOnboardingEvent(ctx context.Context, actorId, status, failureReason string) {
	if p.eventBroker == nil {
		logger.WarnWithCtx(ctx, "event broker is nil, skipping onboarding event")
		return
	}

	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		p.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx),
			NewMCPOnboarding(actorId, status, failureReason))
	})
}

// PublishAuthenticationEvent publishes authentication events with type and user type
func (p *EventPublisher) PublishAuthenticationEvent(ctx context.Context, actorId, authType, userType string) {
	if p.eventBroker == nil {
		logger.WarnWithCtx(ctx, "event broker is nil, skipping authentication event")
		return
	}

	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		p.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx),
			NewMCPAuthentication(actorId, authType, userType))
	})
}

// PublishChatEvent publishes session-level chat events with tool usage
func (p *EventPublisher) PublishChatEvent(ctx context.Context, actorId string, creditReportUsed, netWorthUsed, transactionsUsed bool) {
	if p.eventBroker == nil {
		logger.WarnWithCtx(ctx, "event broker is nil, skipping chat event")
		return
	}

	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		p.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx),
			NewMCPChat(actorId, creditReportUsed, netWorthUsed, transactionsUsed))
	})
}
