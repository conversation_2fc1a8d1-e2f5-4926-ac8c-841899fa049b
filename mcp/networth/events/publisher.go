package events

import (
	"context"
	"time"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
)

// EventPublisher provides methods to publish MCP events
type EventPublisher struct {
	eventBroker events.Broker
}

func NewEventPublisher(eventBroker events.Broker) *EventPublisher {
	return &EventPublisher{
		eventBroker: eventBroker,
	}
}

// PublishOnboardingEvent publishes onboarding event with session id, actor id, and prospect id
func (p *EventPublisher) PublishOnboardingEvent(ctx context.Context, sessionId, actorId, prospectId string) {
	if p.eventBroker == nil {
		logger.WarnWithCtx(ctx, "event broker is nil, skipping onboarding event")
		return
	}

	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		p.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx),
			NewMCPOnboarding(sessionId, actorId, prospectId))
	})
}

// PublishAuthenticationEvent publishes authentication events with status only
func (p *EventPublisher) PublishAuthenticationEvent(ctx context.Context, actorId, sessionId string, status bool) {
	if p.eventBroker == nil {
		logger.WarnWithCtx(ctx, "event broker is nil, skipping authentication event")
		return
	}

	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		p.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx),
			NewMCPAuthentication(actorId, sessionId, status))
	})
}

// PublishChatEvent publishes session-level chat events with tool usage
func (p *EventPublisher) PublishChatEvent(ctx context.Context, actorId, sessionId string, creditReportUsed, netWorthUsed, transactionsUsed bool) {
	if p.eventBroker == nil {
		logger.WarnWithCtx(ctx, "event broker is nil, skipping chat event")
		return
	}
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		p.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx),
			NewMCPChat(actorId, sessionId, creditReportUsed, netWorthUsed, transactionsUsed))
	})
}
