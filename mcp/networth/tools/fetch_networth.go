package tools

import (
	"context"
	"fmt"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	protoJson "google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	"github.com/epifi/gamma/api/analyser/variables/mutualfund"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	connectedAccountExternalPb "github.com/epifi/gamma/api/connected_account/external"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	networthTools "github.com/epifi/gamma/api/mcp/networth"
	"github.com/epifi/gamma/pkg/obfuscator"
)

var (
	// We will return the same for any kind of error to reduce attack surface
	fetchNetWorthErrMsg = mcp.NewToolResultText("something went wrong in fetching net worth")
)

type FetchNetWorthHandler struct {
	netWorthClient          networthPb.NetWorthClient
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient
	connectedAccountClient  connectedAccountPb.ConnectedAccountClient
}

func NewFetchNetWorthHandler(
	netWorthClient networthPb.NetWorthClient,
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient,
	connectedAccountClient connectedAccountPb.ConnectedAccountClient,
) *FetchNetWorthHandler {
	return &FetchNetWorthHandler{
		netWorthClient:          netWorthClient,
		variableGeneratorClient: variableGeneratorClient,
		connectedAccountClient:  connectedAccountClient,
	}
}

// GetTool returns tool with name, description and other metadata which gets used by mcp host
func (h *FetchNetWorthHandler) GetTool() mcp.Tool {
	return mcp.NewTool("fetch_net_worth",
		mcp.WithDescription("Calculate comprehensive net worth with detailed asset and liability breakdown including investments (mutual funds, stocks, EPF, etc.), real estate, bank accounts, loans, and credit card debt."),
	)
}

// Handle is the main handler call that gets called on each tool call
func (h *FetchNetWorthHandler) Handle(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	sessionId := server.ClientSessionFromContext(ctx).SessionID()
	obfuscatedSessId := obfuscator.Hashed(sessionId)
	actorId := epificontext.ActorIdFromContext(ctx)
	if actorId == epificontext.UnknownId {
		logger.Error(ctx, "actorId is not populated", zap.String(logger.SESSION_ID, obfuscatedSessId))
		return fetchNetWorthErrMsg, nil
	}
	logger.Debug(ctx, "Received fetch net worth call", zap.String(logger.SESSION_ID, obfuscatedSessId))
	res, fetchNetWorthErr := h.fetchNetWorthAndHoldingsApiCall(ctx, actorId)
	if fetchNetWorthErr != nil {
		logger.Error(ctx, "error in fetching net worth", zap.Error(fetchNetWorthErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.SESSION_ID, obfuscatedSessId))
		return fetchNetWorthErrMsg, nil
	}

	return mcp.NewToolResultText(res), nil
}

func (h *FetchNetWorthHandler) fetchNetWorthAndHoldingsApiCall(ctx context.Context, actorId string) (string, error) {
	var (
		netWorthResp      *networthPb.GetNetWorthValueResponse
		mfSchemeAnalytics *mutualfund.MfSchemeAnalytics
		aaAccountDetails  *connectedAccountPb.GetAccountDetailsBulkResponse
	)
	grp, gCtx := errgroup.WithContext(ctx)

	// total networth, includes vales for asset and liabilities
	grp.Go(func() error {
		var netWorthErr error
		netWorthResp, netWorthErr = h.netWorthClient.GetNetWorthValue(gCtx, &networthPb.GetNetWorthValueRequest{ActorId: actorId})
		if rpcErr := epifigrpc.RPCError(netWorthResp, netWorthErr); rpcErr != nil {
			return fmt.Errorf("error from networth rpc: %v", rpcErr)
		}
		return nil
	})

	// mf portfolio holdings
	grp.Go(func() error {
		var holdingErr error
		mfSchemeAnalytics, holdingErr = h.getMfHoldingDetails(gCtx, actorId)
		if holdingErr != nil {
			return fmt.Errorf("error while getting mf holdings details: %v", holdingErr)
		}
		return nil
	})

	// aa account details (includes all bank account, indian stocks, nps, deposits etc.)
	grp.Go(func() error {
		var aaErr error
		aaAccountDetails, aaErr = h.getAaAccountDetails(gCtx, actorId)
		if aaErr != nil {
			return fmt.Errorf("error while getting aa account details: %v", aaErr)
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		logger.Error(ctx, "error in while getting networth and holding details", zap.Error(err))
		return "", err
	}

	netWorthDetails := &networthTools.NetworthDetails{
		NetWorthResponse:           netWorthResp.MinimalRespForLLM(),
		MfSchemeAnalytics:          mfSchemeAnalytics.MinimalRespForLLM(),
		AccountDetailsBulkResponse: aaAccountDetails.MinimalRespForLLM(),
	}
	byteResp, marshalErr := protoJson.Marshal(netWorthDetails)
	if marshalErr != nil {
		return "", errors.Wrap(marshalErr, "error marshalling net worth details including holdings")
	}
	return string(byteResp), nil
}

func (h *FetchNetWorthHandler) getMfHoldingDetails(ctx context.Context, actorId string) (*mutualfund.MfSchemeAnalytics, error) {
	getAnalysisVariableResp, getAnalysisVariableErr := h.variableGeneratorClient.GetAnalysisVariables(ctx, &analyserVariablePb.GetAnalysisVariablesRequest{
		ActorId:               actorId,
		AnalysisVariableNames: []analyserVariablePb.AnalysisVariableName{analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS},
	})
	if rpcErr := epifigrpc.RPCError(getAnalysisVariableResp, getAnalysisVariableErr); rpcErr != nil {
		return nil, fmt.Errorf("error fetching analysis variable generator: %w", rpcErr)
	}
	variable, ok := getAnalysisVariableResp.GetVariableEnumMap()[analyserVariablePb.AnalysisVariableName_ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS]
	if !ok {
		logger.Info(ctx, "mf scheme analytics not found for actor")
		return nil, nil
	}
	return variable.GetMfSecretsSchemeAnalytics(), nil
}

func (h *FetchNetWorthHandler) getAaAccountDetails(ctx context.Context, actorId string) (*connectedAccountPb.GetAccountDetailsBulkResponse, error) {
	getAccountsResp, getAccountsErr := h.connectedAccountClient.GetAccounts(ctx, &connectedAccountPb.GetAccountsRequest{
		ActorId: actorId,
		AccountFilterList: []connectedAccountExternalPb.AccountFilter{
			connectedAccountExternalPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
		},
	})
	if rpcErr := epifigrpc.RPCError(getAccountsResp, getAccountsErr); rpcErr != nil && !getAccountsResp.GetStatus().IsRecordNotFound() {
		return nil, fmt.Errorf("failed to get account details for actor : %w", rpcErr)
	}
	if len(getAccountsResp.GetAccountDetailsList()) == 0 || getAccountsResp.GetStatus().IsRecordNotFound() {
		return nil, nil
	}
	var accountIds []string
	for _, acc := range getAccountsResp.GetAccountDetailsList() {
		accountIds = append(accountIds, acc.GetAccountId())
	}

	accDetails, err := h.connectedAccountClient.GetAccountDetailsBulk(ctx, &connectedAccountPb.GetAccountDetailsBulkRequest{
		AccountIdList: accountIds,
		AccountDetailsMaskList: []connectedAccountExternalPb.AccountDetailsMask{
			connectedAccountExternalPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_PROFILE,
			connectedAccountExternalPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY,
		},
	})
	if rpcErr := epifigrpc.RPCError(accDetails, err); rpcErr != nil {
		return nil, fmt.Errorf("failed to fetch accDetails for aa transaction: %w", rpcErr)
	}
	return accDetails, nil
}
