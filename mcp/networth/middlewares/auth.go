package middlewares

import (
	"context"
	"fmt"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/pkg/obfuscator"
	sessionPb "github.com/epifi/gamma/api/auth/session"
	mcpEvents "github.com/epifi/gamma/mcp/networth/events"
)

var (
	// We will return the same for any kind of error to reduce attack surface
	somethingWentWrongMsg = mcp.NewToolResultText("something went wrong, try again")
)

type AuthMiddleware struct {
	sessionManagerClient sessionPb.SessionManagerClient
	eventPublisher       *mcpEvents.EventPublisher
}

func NewAuthMiddleware(
	sessionManagerClient sessionPb.SessionManagerClient,
	eventPublisher *mcpEvents.EventPublisher,
) *AuthMiddleware {
	return &AuthMiddleware{
		sessionManagerClient: sessionManagerClient,
		eventPublisher:       eventPublisher,
	}
}

func (m *AuthMiddleware) AuthMiddleware(next server.ToolHandlerFunc) server.ToolHandlerFunc {
	return func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		// fetch sessionId from context
		// this gets populated for every tool call
		sessionId := server.ClientSessionFromContext(ctx).SessionID()
		obfuscatedSessId := obfuscator.Hashed(sessionId)
		validateSessResp, validateSessErr := m.sessionManagerClient.ValidateSession(ctx, &sessionPb.ValidateSessionRequest{
			SessionId: sessionId,
		})
		if rpcErr := epifigrpc.RPCError(validateSessResp, validateSessErr); rpcErr != nil {
			// is session is expired or not created yet, then redirect to creating login url
			if validateSessResp.GetStatus().GetCode() == uint32(sessionPb.CreateSessionResponse_SESSION_EXPIRED) {
				loginUrl, getLoginUrlErr := m.getLoginUrl(ctx, sessionId)
				if getLoginUrlErr != nil {
					logger.Error(ctx, "error in getting login url", zap.Error(getLoginUrlErr), zap.String(logger.SESSION_ID, obfuscatedSessId))
					return somethingWentWrongMsg, nil
				}
				// Onboarding event: user landed on login/auth page (session expired)
				if m.eventPublisher != nil {
					m.eventPublisher.PublishOnboardingEvent(ctx, validateSessResp.GetSessionDetails().GetSessionId(), validateSessResp.GetSessionDetails().GetActorId(), "")
				}
				res := fmt.Sprintf("Please login by clicking this link: [Login](%s)\n\nIf your client supports clickable links, you can render and present it and ask them to click the link above. Otherwise, display the URL and ask them to copy and paste it into their browser: %s\n\nAfter completing the login in your browser, let me know and I'll continue with your request.", loginUrl, loginUrl)
				return mcp.NewToolResultText(res), nil
			}
			logger.Error(ctx, "error in validating session", zap.Error(rpcErr), zap.String(logger.SESSION_ID, obfuscatedSessId))
			return somethingWentWrongMsg, nil
		}
		ctx = epificontext.CtxWithActorId(ctx, validateSessResp.GetSessionDetails().GetActorId())
		// Onboarding event: success (session validated)
		if m.eventPublisher != nil {
			m.eventPublisher.PublishAuthenticationEvent(ctx, validateSessResp.GetSessionDetails().GetActorId(), validateSessResp.GetSessionDetails().GetSessionId(), true)
		}
		return next(ctx, req)
	}
}

// GetLoginUrl fetches dynamic login url for given sessionId
func (m *AuthMiddleware) getLoginUrl(ctx context.Context, sessionId string) (string, error) {
	sessionLoginUrlResp, sessionLoginUrlErr := m.sessionManagerClient.GetSessionLoginUrl(ctx, &sessionPb.GetSessionLoginUrlRequest{
		SessionId: sessionId,
	})
	if rpcErr := epifigrpc.RPCError(sessionLoginUrlResp, sessionLoginUrlErr); rpcErr != nil {
		return "", errors.Wrap(rpcErr, "error in getting session login url")
	}
	return sessionLoginUrlResp.GetSessionLoginUrl(), nil
}
