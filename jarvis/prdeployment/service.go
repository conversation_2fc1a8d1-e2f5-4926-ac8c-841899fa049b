package prdeployment

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"

	"github.com/google/uuid"
	"github.com/slack-go/slack"
	"go.temporal.io/sdk/client"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cfg"
	jarvisns "github.com/epifi/be-common/pkg/epifitemporal/namespace/jarvis"
	workflowPkg "github.com/epifi/be-common/pkg/epifitemporal/workflow"
	"github.com/epifi/be-common/pkg/logger"

	jarvispb "github.com/epifi/gamma/api/jarvis"
	jarvisworkflowpb "github.com/epifi/gamma/api/jarvis/workflow"
	"github.com/epifi/gamma/jarvis/auth"
	"github.com/epifi/gamma/jarvis/config"
	"github.com/epifi/gamma/jarvis/utils"
)

type PrDeploymentService struct {
	temporalClient client.Client
	config         *config.Config
	s3Client       *s3.Client
	slackClient    *slack.Client
}

func NewPrDeploymentService(temporalClient client.Client, config *config.Config, s3Client *s3.Client, slackClient *slack.Client) *PrDeploymentService {
	return &PrDeploymentService{
		temporalClient: temporalClient,
		config:         config,
		s3Client:       s3Client,
		slackClient:    slackClient,
	}
}

type StartDeploymentRequest struct {
	PrNumber                int                       `json:"prNumber"`
	RepoName                string                    `json:"repoName"`
	Servers                 []string                  `json:"servers"`
	Workers                 []string                  `json:"workers"`
	Environments            []string                  `json:"environments"`
	DeploymentMetadata      *utils.DeploymentMetadata `json:"deploymentMetadata"`
	DynamicConfigJobServers []string                  `json:"dynamicConfigJobServers"`
	DynamicConfigJobWorkers []string                  `json:"dynamicConfigJobWorkers"`
	QaCpPrNumber            int64                     `json:"qaCpPrNumber,omitempty"`
	ProdCpPrNumber          int64                     `json:"prodCpPrNumber,omitempty"`
}

// StartPrDeploymentWorkflow handles the workflow start request
func (s *PrDeploymentService) StartPrDeploymentWorkflow(w http.ResponseWriter, req *http.Request) {
	ctx := req.Context()
	// TODO: remove this debug logger once system is stable & working
	logger.Debug(ctx, "Got PR Deployment Request", zap.Any("body", req.Body))

	// Read the request body
	body, err := ioutil.ReadAll(req.Body)
	if err != nil {
		s.InternalServerError(err, "Failed to read request body", w, req)
		return
	}

	var request StartDeploymentRequest

	// Deserialize the protobuf message
	if err = json.Unmarshal(body, &request); err != nil {
		s.InternalServerError(err, "Failed to parse protobuf payload", w, req)
		return
	}

	if err := validateParams(&request); err != nil {
		s.InternalServerError(err, "Invalid request parameters", w, req)
		return
	}
	githubClient, err := utils.CreateGithubClient(ctx, s.config.GithubApp.AppId, s.config.GithubApp.PrivateKey, s.config.GithubApp.InstallationId)
	if err != nil {
		s.InternalServerError(err, "Failed to create GitHub client", w, req)
		return
	}
	pr, _, err := githubClient.PullRequests.Get(ctx, "epifi", request.RepoName, request.PrNumber)
	if err != nil {
		s.InternalServerError(err, "Failed to get pr details", w, req)
	}

	header := &jarvispb.Header{
		Auth: &jarvispb.Auth{
			User: &jarvispb.User{
				Email: ctx.Value("userinfo").(auth.UserInfoStruct).Email,
			},
		},
	}

	id := fmt.Sprintf("pr-%s-%d-%s", request.RepoName, request.PrNumber, uuid.New().String())
	// Initiate workflow
	workflowOptions := client.StartWorkflowOptions{
		TaskQueue: "deploy-jarvis-task-queue",
		ID:        id,
	}

	envToReleaseBranch, err := s.getReleaseBranch(ctx)
	if err != nil {
		s.InternalServerError(err, "Error while getting release branch", w, req)
		return
	}
	var deploymentParams []*jarvisworkflowpb.DeploymentParams

	for _, env := range request.Environments {
		switch env {
		case cfg.QaEnv:
			jenkinsJobPaths := s.buildJenkinsJobPaths(request.Servers, request.Workers, request.DynamicConfigJobServers, request.DynamicConfigJobWorkers, cfg.QaEnv)
			deploymentParam := &jarvisworkflowpb.DeploymentParams{
				Environment:      jarvisworkflowpb.Environment_ENV_QA,
				CherryPickBranch: envToReleaseBranch[cfg.QaEnv],
				JenkinsJobPaths:  jenkinsJobPaths,
			}
			// Add cherry-pick PR number if provided
			if request.QaCpPrNumber > 0 {
				deploymentParam.CherryPickPrNumber = &request.QaCpPrNumber
			}
			deploymentParams = append(deploymentParams, deploymentParam)
		case cfg.ProductionEnv:
			jenkinsJobPaths := s.buildJenkinsJobPaths(request.Servers, request.Workers, request.DynamicConfigJobServers, request.DynamicConfigJobWorkers, cfg.ProductionEnv)
			deploymentParam := &jarvisworkflowpb.DeploymentParams{
				Environment:      jarvisworkflowpb.Environment_ENV_PROD,
				CherryPickBranch: envToReleaseBranch[cfg.ProductionEnv],
				JenkinsJobPaths:  jenkinsJobPaths,
			}
			// Add cherry-pick PR number if provided
			if request.ProdCpPrNumber > 0 {
				deploymentParam.CherryPickPrNumber = &request.ProdCpPrNumber
			}
			deploymentParams = append(deploymentParams, deploymentParam)
		}
	}
	// get user slack id from email
	slackUser, err := s.slackClient.GetUserByEmail(ctx.Value("userinfo").(auth.UserInfoStruct).Email)
	if err != nil {
		s.InternalServerError(err, "Error while getting slack user id", w, req)
		return
	}
	// Create the DeploymentMetadata message
	deploymentMetadata := &jarvisworkflowpb.DeploymentMetadata{
		PrTitle:           pr.GetTitle(),
		MonorailTicket:    request.DeploymentMetadata.MonorailTicket,
		ChangeType:        request.DeploymentMetadata.ChangeType,
		VisibilityType:    request.DeploymentMetadata.VisibilityType,
		QaTestingRequired: request.DeploymentMetadata.QATestingRequired,
		DevTested:         request.DeploymentMetadata.DevTested,
	}

	res, err := workflowPkg.ExecuteAsync(ctx, s.temporalClient, jarvisns.PRDeploymentWorkflow, &jarvisworkflowpb.PRDeploymentWorkflowRequest{
		JarvisHeader:       header,
		PrNumber:           int64(request.PrNumber),
		RepoName:           request.RepoName,
		DeploymentParams:   deploymentParams,
		SlackChannelId:     slackUser.ID,
		UserEmailId:        ctx.Value("userinfo").(auth.UserInfoStruct).Email,
		DeploymentMetadata: deploymentMetadata,
		Workers:            request.Workers,
		Servers:            request.Servers,
		SyncServersCfg:     request.DynamicConfigJobServers,
		SyncWorkersCfg:     request.DynamicConfigJobWorkers,
	}, &workflowOptions)
	if err != nil {
		s.InternalServerError(err, "Error while executing pr deployment workflow", w, req)
		return
	}
	logger.Info(ctx, fmt.Sprintf("Started pr deployment workflow with id : %s, Run Id : %s", res.GetID(), res.GetRunID()))

	resp := &jarvisworkflowpb.PRDeploymentWorkflowResponse{
		Status:     rpc.StatusOk(),
		WorkflowId: res.GetID(),
	}

	protoJsondata, err := protojson.Marshal(resp)
	if err != nil {
		s.InternalServerError(err, "Error happened in JSON marshal.", w, req)
		return
	}

	w.WriteHeader(http.StatusOK)
	_, err = w.Write(protoJsondata)
	if err != nil {
		s.InternalServerError(err, "Error while writing data", w, req)
		return
	}
}

// buildJenkinsJobPaths creates the structured JenkinsJobPaths based on servers and workers
func (s *PrDeploymentService) buildJenkinsJobPaths(servers []string, workers []string, dynamicConfigJobServer []string, dynamicConfigJobWorker []string, environment string) *jarvisworkflowpb.JenkinsJobPaths {
	jenkinsJobPaths := &jarvisworkflowpb.JenkinsJobPaths{}
	// Build server job paths
	switch environment {
	case cfg.QaEnv:
		// QA server deployments
		for _, server := range servers {
			serverJobPath := fmt.Sprintf("V3_Deployment/job/qa/job/%s/", server)
			jenkinsJobPaths.ServerJobPath = append(jenkinsJobPaths.ServerJobPath, serverJobPath)
		}
	case cfg.ProductionEnv:
		// Production AMI builds
		if len(servers) > 0 {
			buildAmiJobPath := "V3_Deployment/job/build/job/build_ami/"
			jenkinsJobPaths.ServerJobPath = append(jenkinsJobPaths.ServerJobPath, buildAmiJobPath)
		}
	}

	// Build worker job paths
	if len(workers) > 0 {
		for _, worker := range workers {
			workerJobPath := &jarvisworkflowpb.WorkerJobPath{
				WorkerName:      worker,
				BinaryBuildPath: fmt.Sprintf("K8s/job/deploy/job/build/job/%s/", worker),
				DeployPath:      fmt.Sprintf("K8s/job/%s/job/%s/", environment, worker),
			}

			// Add image promotion path only for production
			if environment == cfg.ProductionEnv {
				workerJobPath.ImagePromotionPath = stringPtr("K8s/job/promote-image/")
			}

			jenkinsJobPaths.WorkerJobPaths = append(jenkinsJobPaths.WorkerJobPaths, workerJobPath)
		}
	}
	if len(dynamicConfigJobServer) > 0 || len(dynamicConfigJobWorker) > 0 {
		jenkinsJobPaths.SyncDynamicConfigJobPath = "Scripts/job/Backend/job/sync_dynamic_config_v2.1/"
	}

	return jenkinsJobPaths
}

var request struct {
	WorkflowId string `json:"workflow_id"`
	RunId      string `json:"run_id"`
}

// StopPrDeploymentWorkflow handles the workflow stop request
func (s *PrDeploymentService) StopPrDeploymentWorkflow(w http.ResponseWriter, req *http.Request) {
	ctx := req.Context()
	// TODO: remove this debug logger once system is stable & working
	logger.Debug(ctx, "Got PR Deployment Stop Request", zap.Any("body", req.Body))

	// Read the request body
	body, err := ioutil.ReadAll(req.Body)
	if err != nil {
		s.InternalServerError(err, "Failed to read request body", w, req)
		return
	}

	// Unmarshal the request body into the struct
	if err := json.Unmarshal(body, &request); err != nil {
		s.InternalServerError(err, "Failed to parse request body", w, req)
		return
	}

	err = s.temporalClient.TerminateWorkflow(ctx, request.WorkflowId, request.RunId, "terminated")
	if err != nil {
		s.InternalServerError(err, "Failed to parse protobuf payload", w, req)
		return
	}
	w.WriteHeader(http.StatusOK)
}

func (s *PrDeploymentService) InternalServerError(errMsg error, message string, w http.ResponseWriter, r *http.Request) {

	logger.ErrorNoCtx("Internal Server Error", zap.String("err", message), zap.Error(errMsg))
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(http.StatusInternalServerError)
	response_data := map[string]interface{}{
		"response": "Internal Server Error",
		"data":     message,
	}
	jsonResponseData, err := json.Marshal(response_data)
	if err != nil {
		logger.ErrorNoCtx("Error while writing data", zap.Error(err))
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
	_, err = w.Write(jsonResponseData)
	if err != nil {
		logger.ErrorNoCtx("Error while writing data", zap.Error(err))
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
}

func validateParams(request *StartDeploymentRequest) error {
	if request.PrNumber <= 0 {
		return errors.New("PR number must be positive")
	}
	if request.RepoName == "" {
		return errors.New("repository name is required")
	}
	if len(request.Servers) == 0 && len(request.Workers) == 0 && len(request.DynamicConfigJobServers) == 0 && len(request.DynamicConfigJobWorkers) == 0 {
		return errors.New("at least one server or worker configuration is required")
	}
	if len(request.Environments) == 0 {
		return errors.New("at least one environment configuration is required")
	}
	return nil
}

func (s *PrDeploymentService) getReleaseBranch(ctx context.Context) (map[string]string, error) {
	// Read the release_branch.json file from S3
	releaseBranch, err := s.s3Client.Read(ctx, "release_branch.json")
	if err != nil {
		return nil, fmt.Errorf("failed to read release branch file: %w", err)
	}
	var envToReleaseBranch map[string]string

	// Unmarshal the JSON data into the map
	if err := json.Unmarshal(releaseBranch, &envToReleaseBranch); err != nil {
		return nil, fmt.Errorf("failed to unmarshal release branch data: %w", err)
	}
	return envToReleaseBranch, nil
}

type WorkflowStatusRequest struct {
	WorkflowId string `json:"workflowId"`
}

func (s *PrDeploymentService) GetPrDeploymentWorkflowStatus(w http.ResponseWriter, req *http.Request) {
	ctx := req.Context()
	logger.Debug(ctx, "Got workflow status request", zap.Any("body", req.Body))

	// Read and parse request
	body, err := ioutil.ReadAll(req.Body)
	if err != nil {
		s.InternalServerError(err, "Failed to read request body", w, req)
		return
	}

	var request WorkflowStatusRequest
	if err := json.Unmarshal(body, &request); err != nil {
		s.InternalServerError(err, "Failed to parse request body", w, req)
		return
	}

	if request.WorkflowId == "" {
		s.InternalServerError(errors.New("workflow ID is required"), "Missing workflow ID", w, req)
		return
	}

	// query temporal workflow
	var workflowData utils.WorkflowData
	queryResponse, err := s.temporalClient.QueryWorkflow(ctx, request.WorkflowId, "", "getWorkflowStatus")
	if err != nil {
		s.InternalServerError(err, "Failed to query workflow status", w, req)
		return
	}
	if queryResponse == nil {
		s.InternalServerError(errors.New("query response is nil"), "Failed to query workflow status", w, req)
		return
	}
	if err = queryResponse.Get(&workflowData); err != nil {
		s.InternalServerError(err, "Failed to retrieve workflow data", w, req)
		return
	}

	// Send response
	jsonResponse, err := json.Marshal(workflowData)
	if err != nil {
		s.InternalServerError(err, "Failed to marshal response", w, req)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	if _, err := w.Write(jsonResponse); err != nil {
		logger.ErrorNoCtx("Error writing response", zap.Error(err))
	}
}

// TODO(shafi): implement the logic to restart
func (s *PrDeploymentService) RestartPrDeploymentWorkflow(w http.ResponseWriter, req *http.Request) {
	ctx := req.Context()
	logger.Debug(ctx, "Got PR Deployment restart request")

	// Read and parse request
	body, err := ioutil.ReadAll(req.Body)
	if err != nil {
		s.InternalServerError(err, "Failed to read request body", w, req)
		return
	}

	var request WorkflowStatusRequest
	if err := json.Unmarshal(body, &request); err != nil {
		s.InternalServerError(err, "Failed to parse request body", w, req)
		return
	}

	if request.WorkflowId == "" {
		s.InternalServerError(errors.New("workflow ID is required"), "Missing workflow ID", w, req)
		return
	}

	w.WriteHeader(http.StatusOK)
}

// Helper function to return string pointer
func stringPtr(s string) *string {
	return &s
}
