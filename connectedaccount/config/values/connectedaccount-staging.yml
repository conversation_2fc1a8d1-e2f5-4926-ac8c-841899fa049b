Application:
  Environment: "staging"
  Name: "connectedaccount"

Server:
  Ports:
    GrpcPort: 8089
    GrpcSecurePort: 9507
    HttpPort: 9999
    HttpPProfPort: 9990

DocsBucketName: "epifi-staging-docs"

EpifiDb:
  AppName: "connectedaccount"
  StatementTimeout: 1s
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

ConnectedAccountDb:
  DbType: "PGDB"
  AppName: "connectedaccount"
  StatementTimeout: 1m
  Name: "connected_account"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

FeatureEngineeringDb:
  DbType: "PGDB"
  AppName: "connectedaccount"
  StatementTimeout: 5m
  Name: "feature_engineering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "staging/rds/postgres/feature-engineering"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

AWS:
  Region: "ap-south-1"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  HystrixCommand:
    CommandName: "connectedaccount_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

Flags:
  TrimDebugMessageFromStatus: false
  EnableConsentRenewalSegmentNonProdTest: true

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "staging/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "staging/gcloud/profiling-service-account-key"
    ConnectedAccountDbUserNamePassword: "staging/rds/postgres14"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 0.5
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

Consent:
  DataConsumerId: "EPIFISIM"
  ConsentLifeDuration: 720h #30 days
  Mode: "CONSENT_MODE_STORE"
  FetchType: "FETCH_TYPE_PERIODIC"
  ConsentTypes: [ "CONSENT_TYPE_TRANSACTIONS", "CONSENT_TYPE_PROFILE", "CONSENT_TYPE_SUMMARY" ]
  FITypes: [ "FI_TYPE_DEPOSIT", "FI_TYPE_TERM_DEPOSIT", "FI_TYPE_RECURRING_DEPOSIT","FI_TYPE_EQUITIES", "FI_TYPE_REIT", "FI_TYPE_INVIT", "FI_TYPE_NPS" ]
  DataRangeStartYears: -1
  DataLife:
    Unit: DATA_LIFE_MONTH
    Value: 1
  Frequency:
    Unit: FREQUENCY_UNIT_HOUR
    Value: 10
  Purpose: "Personal finance management"
  DataRangeEndMonthValue: 1

NextFetchInterval: 5m
DataFetchDelayMinutes: 5m
BatchSize: 60
BatchProcessDelayMultiplier: 30s
FiDataRangeOverlapInterval: 1h
FiDataRangeOverlapIntervalForCustomfetch: 8760h
ConsentProcessDelay: 10s
NextFetchIntervalFailureCase: 1h #incase data pull fails next fetch for consent is allowed in next hour
AttemptCompletionThreshold: 1s
UserDataRefreshThreshold: 2h
AccountSyncCompletionPollFrequency: 4s
AccountSyncCompletionThreshold: 16s
ConsentDataRequestCountLimit: 400

AaTransactionDaoCacheConfig:
  IsCacheEnabled: true
  Prefix: "AA:TRANSACTION:"
  CacheTTL: "24h"

ProcessConsentSqsPublisher:
  QueueName: "staging-ca-consent-process-queue"

AaTxnBackfillPublisher:
  TopicName: "staging-aa-txn-backfill-topic"

ProcessConsentSqsDelayPublisher:
  QueueName: "staging-ca-consent-process-queue"

FetchDataDelaySqsPublisher:
  QueueName: "staging-ca-data-fetch-delay-queue"

FetchDataSqsPublisher:
  QueueName: "staging-ca-data-fetch-delay-queue"

DecryptDataSqsPublisher:
  QueueName: "staging-ca-data-decrypt-queue"
  BucketName: "epifi-staging-ca-extended-sqs"

ProcessDataSqsPublisher:
  QueueName: "staging-ca-data-process-queue"
  BucketName: "epifi-staging-ca-extended-sqs"

CreateAttemptSqsPublisher:
  QueueName: "staging-ca-attempt-create-queue"

TransactionBatchProcessPublisher:
  QueueName: "staging-ca-transaction-batch-process-queue"

PurgeDataPublisher:
  QueueName: "staging-ca-purge-data-queue"

FirstDataPullSqsPublisher:
  QueueName: "staging-ca-first-data-pull-queue"

ProcessConsentSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-ca-consent-process-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

ProcessAuthTokenCreationEventSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-ca-data-refresh-on-auth-token-creation-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

ProcessConsentCallbackSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-vn-aa-consent-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"

FetchDataSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-ca-data-fetch-delay-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 10
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 80
        Period: 1s
    Namespace: "connectedaccount"

DecryptDataSqsSubscriber:
  BucketName: "epifi-staging-ca-extended-sqs"
  SqsSubscriber:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 10
    QueueName: "staging-ca-data-decrypt-queue"
    RetryStrategy:
      ExponentialBackOff:
        BaseInterval: 30
        MaxAttempts: 10
        TimeUnit: "Second"
    RateLimitConfig:
      ResourceMap:
        subscriber:
          Rate: 5
          Period: 1s

ProcessDataSqsSubscriber:
  BucketName: "epifi-staging-ca-extended-sqs"
  SqsSubscriber:
    StartOnServerStart: true
    NumWorkers: 1
    PollingDuration: 20
    MaxMessages: 10
    QueueName: "staging-ca-data-process-queue"
    RetryStrategy:
      ExponentialBackOff:
        BaseInterval: 30
        MaxAttempts: 10
        TimeUnit: "Second"
    RateLimitConfig:
      ResourceMap:
        subscriber:
          Rate: 5
          Period: 1s

ProcessFICallbackSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-vn-aa-fi-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"

TransactionBatchProcessSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-ca-transaction-batch-process-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"

PurgeDataSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-ca-purge-data-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 60
      MaxAttempts: 10
      TimeUnit: "Second"

ProcessAccountStatusCallbackSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-vn-aa-account-link-status-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"

CreateAttemptSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-ca-attempt-create-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 3
      MaxAttempts: 12
      TimeUnit: "Second"

FirstDataPullSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-ca-first-data-pull-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 10
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 80
        Period: 1s
    Namespace: "connectedaccount"

TransactionEventExternalPublisher:
  TopicName: "staging-ca-data-transaction-topic"

AccountUpdateEventExternalPublisher:
  TopicName: "staging-ca-account-update-topic"

AccountDataSyncExternalPublisher:
  TopicName: "staging-ca-account-data-sync-topic"

PermittedFip: [ "HDFC-FIP", "ICICI-FIP", "AXIS001", "fiplive@indusind", "finsharebank" ]

AaToFipMapping:
  - AA_ENTITY_AA_FINVU: [ "HDFC-FIP","ICICI-FIP","IDFCFirstBank-FIP","fiplive@indusind","FDRLFIPPROD","test-1","test-2","test-3","KotakMahindraBank-FIP","BARB0KIMXXX","ACME-FIP","AXIS001","AUBank-FIP" ,"KarurVysyaBank-FIP" ,"BARBFIP", "UBI-FIP", "fiplive@canarabank", "KBL-FIP", "YESB-FIP","BOI-FIP", "IBFIP", "PSFIP","sbi-fip", "PNB-FIP", "CDSLFIP", "fip@nsdl", "kfinnps-fip", "NPS_PROD", "CAMSCRAFIP" ]
  - AA_ENTITY_AA_ONE_MONEY: [ "HDFC-FIP","ICICI-FIP","IDFCFirstBank-FIP","fiplive@indusind","FDRLFIPPROD","test-1","test-2","test-3","KotakMahindraBank-FIP","BARB0KIMXXX","ACME-FIP","AXIS001","AUBank-FIP" ,"KarurVysyaBank-FIP", "UBI-FIP", "BARBFIP", "UCOB-FIP","YESB-FIP","IOB-FIP", "sbi-fip", "finsharebank", "CAMSCRAFIP" ]

# List of permitted banks and instruments per user group to do controlled testing
# enum for user group needs to match with proto enum value and same for FI TYPES
PermittedFipAndFi:
  - "finsharebank":
      - FNF:
          - "FI_TYPE_DEPOSIT":
          - "FI_TYPE_TERM_DEPOSIT":
          - "FI_TYPE_RECURRING_DEPOSIT":
  - "HDFC-FIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
          - "FI_TYPE_TERM_DEPOSIT":
          - "FI_TYPE_RECURRING_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
          - "FI_TYPE_TERM_DEPOSIT":
          - "FI_TYPE_RECURRING_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
          - "FI_TYPE_TERM_DEPOSIT":
          - "FI_TYPE_RECURRING_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "ICICI-FIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
          - "FI_TYPE_TERM_DEPOSIT":
          - "FI_TYPE_RECURRING_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
          - "FI_TYPE_TERM_DEPOSIT":
          - "FI_TYPE_RECURRING_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
          - "FI_TYPE_TERM_DEPOSIT":
          - "FI_TYPE_RECURRING_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "IDFCFirstBank-FIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
          - "FI_TYPE_TERM_DEPOSIT":
          - "FI_TYPE_RECURRING_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
          - "FI_TYPE_TERM_DEPOSIT":
          - "FI_TYPE_RECURRING_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
          - "FI_TYPE_TERM_DEPOSIT":
          - "FI_TYPE_RECURRING_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "fiplive@indusind":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "FDRLFIPPROD":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "KotakMahindraBank-FIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "BARB0KIMXXX":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "ACME-FIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "AXIS001":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
          - "FI_TYPE_TERM_DEPOSIT":
          - "FI_TYPE_RECURRING_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "AUBank-FIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "KarurVysyaBank-FIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
          - "FI_TYPE_TERM_DEPOSIT":
          - "FI_TYPE_RECURRING_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "test-1":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "test-2":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "test-3":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "UBI-FIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
  - "fiplive@canarabank":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
  - "UCOB-FIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 171
              AndroidPercentage: 100
              IosMinVersion: 256
              IosPercentage: 100
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "YESB-FIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "KBL-FIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 172
              AndroidPercentage: 100
              IosMinVersion: 257
              IosPercentage: 100
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 172
              AndroidPercentage: 100
              IosMinVersion: 257
              IosPercentage: 100
      - FNF:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 172
              AndroidPercentage: 100
              IosMinVersion: 257
              IosPercentage: 100
  - "IOB-FIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 172
              AndroidPercentage: 100
              IosMinVersion: 257
              IosPercentage: 100
      - FNF:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 172
              AndroidPercentage: 100
              IosMinVersion: 257
              IosPercentage: 100
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 172
              AndroidPercentage: 100
              IosMinVersion: 257
              IosPercentage: 100
  - "BOI-FIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 172
              AndroidPercentage: 100
              IosMinVersion: 257
              IosPercentage: 100
      - FNF:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 172
              AndroidPercentage: 100
              IosMinVersion: 257
              IosPercentage: 100
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 172
              AndroidPercentage: 100
              IosMinVersion: 257
              IosPercentage: 100
  - "IBFIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 172
              AndroidPercentage: 100
              IosMinVersion: 257
              IosPercentage: 100
      - FNF:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 172
              AndroidPercentage: 100
              IosMinVersion: 257
              IosPercentage: 100
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 172
              AndroidPercentage: 100
              IosMinVersion: 257
              IosPercentage: 100
  - "PSFIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 172
              AndroidPercentage: 100
              IosMinVersion: 257
              IosPercentage: 100
      - FNF:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 172
              AndroidPercentage: 100
              IosMinVersion: 257
              IosPercentage: 100
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
              AndroidMinVersion: 172
              AndroidPercentage: 100
              IosMinVersion: 257
              IosPercentage: 100
  - "sbi-fip":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "PNB-FIP":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
          - "FI_TYPE_TERM_DEPOSIT":
          - "FI_TYPE_RECURRING_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
  - "idbibank-fip":
      - INTERNAL:
          - "FI_TYPE_DEPOSIT":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_DEPOSIT":
      - FNF:
          - "FI_TYPE_DEPOSIT":
      - EXTERNAL:
          - "FI_TYPE_DEPOSIT":
  - "CDSLFIP":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_EQUITIES":
          - "FI_TYPE_ETF":
          - "FI_TYPE_REIT":
          - "FI_TYPE_INVIT":
      - INTERNAL:
          - "FI_TYPE_EQUITIES":
          - "FI_TYPE_ETF":
          - "FI_TYPE_REIT":
          - "FI_TYPE_INVIT":
      - EXTERNAL:
          - "FI_TYPE_EQUITIES":
          - "FI_TYPE_ETF":
          - "FI_TYPE_REIT":
          - "FI_TYPE_INVIT":
      - FNF:
          - "FI_TYPE_EQUITIES":
          - "FI_TYPE_ETF":
          - "FI_TYPE_REIT":
          - "FI_TYPE_INVIT":
  - "fip@nsdl":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_EQUITIES":
          - "FI_TYPE_ETF":
          - "FI_TYPE_REIT":
          - "FI_TYPE_INVIT":
      - INTERNAL:
          - "FI_TYPE_EQUITIES":
          - "FI_TYPE_ETF":
          - "FI_TYPE_REIT":
          - "FI_TYPE_INVIT":
      - EXTERNAL:
          - "FI_TYPE_EQUITIES":
          - "FI_TYPE_ETF":
          - "FI_TYPE_REIT":
          - "FI_TYPE_INVIT":
      - FNF:
          - "FI_TYPE_EQUITIES":
          - "FI_TYPE_ETF":
          - "FI_TYPE_REIT":
          - "FI_TYPE_INVIT":
  - "kfinnps-fip":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_NPS":
      - EXTERNAL:
          - "FI_TYPE_NPS":
  - "NPS_PROD":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_NPS":
      - EXTERNAL:
          - "FI_TYPE_NPS":
  - "CAMSCRAFIP":
      - CONNECTED_ACCOUNT:
          - "FI_TYPE_NPS":

AaNotifications:
  - CommsType: "COMMS_TYPE_FIRST_DATA_PULL_FAIL"
    CommsChannels:
      - Mode: "COMMS_CHANNEL_NOTIFICATION"
        Enabled: true
        Properties:
          Type: "SYSTEM_TRAY"
          Title: "This is taking longer than expected ⏳"
          Body: "Getting data from your other accounts is taking some time. We'll let you know as soon as it's ready!"
      - Mode: "COMMS_CHANNEL_NOTIFICATION"
        Enabled: true
        Properties:
          Type: "IN_APP"
          Title: "This is taking longer than expected ⏳"
          Body: "Getting data from your other accounts is taking some time. We'll let you know as soon as it's ready!"
      - Mode: "COMMS_CHANNEL_EMAIL"
        Enabled: false
        Properties:
          Template: ""
          TemplateVersion: ""
      - Mode: "COMMS_CHANNEL_SMS"
        Enabled: false
        Properties:
          Template: ""
          TemplateVersion: ""
  - CommsType: "COMMS_TYPE_FIRST_DATA_PULL_SUCCESS"
    CommsChannels:
      - Mode: "COMMS_CHANNEL_NOTIFICATION"
        Enabled: true
        Properties:
          Type: "SYSTEM_TRAY"
          Title: "Your data is here 🤓"
          Body: "Data from your %s accounts is here. Tap to view ➡️"
      - Mode: "COMMS_CHANNEL_NOTIFICATION"
        Enabled: true
        Properties:
          Type: "IN_APP"
          Title: "Your data is here 🤓"
          Body: "Data from your %s accounts is here. Tap to view ➡️"
  - CommsType: "COMMS_TYPE_CONSENT_PAUSED_FROM_AA"
    CommsChannels:
      - Mode: "COMMS_CHANNEL_NOTIFICATION"
        Enabled: false
        Properties:
          Type: "SYSTEM_TRAY"
          Title: "You have paused your data consent"
          Body: "Tap to view"
      - Mode: "COMMS_CHANNEL_NOTIFICATION"
        Enabled: false
        Properties:
          Type: "IN_APP"
          Title: "You have paused your data consent"
          Body: "Tap to view"
  - CommsType: "COMMS_TYPE_ACCOUNT_DELINKED_FROM_AA"
    CommsChannels:
      - Mode: "COMMS_CHANNEL_NOTIFICATION"
        Enabled: true
        Properties:
          Type: "SYSTEM_TRAY"
          Title: "⚠️ %s account has been disconnected."
          Body: "Connect your account again to get insights on your spends. ➡️"
      - Mode: "COMMS_CHANNEL_NOTIFICATION"
        Enabled: true
        Properties:
          Type: "IN_APP"
          Title: "⚠️ %s account has been disconnected."
          Body: "Connect your account again to get insights on your spends. ➡️"
  - CommsType: "COMMS_TYPE_AA_HEARTBEAT_UP"
    CommsChannels:
      - Mode: "COMMS_CHANNEL_NOTIFICATION"
        Enabled: true
        Properties:
          Type: "SYSTEM_TRAY"
          Title: "Aa system is UP"
          Body: "Aa system is UP. Come and connect"
      - Mode: "COMMS_CHANNEL_NOTIFICATION"
        Enabled: true
        Properties:
          Type: "IN_APP"
          Title: "Aa system is UP"
          Body: "Aa system is UP. Come and connect"
  - CommsType: "COMMS_TYPE_CONSENT_EXPIRED"
    CommsChannels:
      - Mode: "COMMS_CHANNEL_NOTIFICATION"
        Enabled: true
        Properties:
          Type: "IN_APP"
          Title: ""
          Body: ""

SendNotificationSqsPublisher:
  QueueName: "staging-ca-send-notification-delay-queue"

SendNotificationSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-ca-send-notification-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"

Tracing:
  Enable: true

ProcessConsentDataRefreshSqsPublisher:
  QueueName: "staging-ca-consent-data-refresh-queue"

ProcessConsentDataRefreshSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-ca-consent-data-refresh-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 2
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "connectedaccount"

CaptureColumnUpdateSqsPublisher:
  QueueName: "staging-ca-capture-column-update-queue"

CaptureColumnUpdateSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-ca-capture-column-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 2
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1s

CaptureHeartbeatAndSendNotificationSqsPublisher:
  QueueName: "staging-ca-user-heartbeat-notification-queue"

CaptureHeartbeatAndSendNotificationSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-ca-user-heartbeat-notification-queue"
  RetryStrategy:
    Hybrid: # Exponential Backoff strategy for first ~2 hours and Regular strategy for next ~22 hours
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 2
          MaxAttempts: 7
          TimeUnit: "Minute"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 22
          TimeUnit: "Hour"
      MaxAttempts: 29
      CutOff: 7

MinAndroidVersionEligibleForAaRouting: 2
MinIosVersionEligibleForAaRouting: 2000

USStocksParams:
  IdealDurationOfDataSyncCompletion: "10s"

UgToPlatformToAaEntityToBoundsMap:
  INTERNAL:
    PlatformToAaEntityToBoundsMap:
      ANDROID:
        AaEntityToBoundsMap:
          AA_ENTITY_AA_ONE_MONEY:
            LowerBound: 0
            UpperBound: 0
          AA_ENTITY_AA_FINVU:
            LowerBound: 1
            UpperBound: 100
      IOS:
        AaEntityToBoundsMap:
          AA_ENTITY_AA_FINVU:
            LowerBound: 1
            UpperBound: 100
          AA_ENTITY_AA_ONE_MONEY:
            LowerBound: 0
            UpperBound: 0
  CONNECTED_ACCOUNT:
    PlatformToAaEntityToBoundsMap:
      ANDROID:
        AaEntityToBoundsMap:
          AA_ENTITY_AA_ONE_MONEY:
            LowerBound: 0
            UpperBound: 0
          AA_ENTITY_AA_FINVU:
            LowerBound: 1
            UpperBound: 100
      IOS:
        AaEntityToBoundsMap:
          AA_ENTITY_AA_FINVU:
            LowerBound: 1
            UpperBound: 100
          AA_ENTITY_AA_ONE_MONEY:
            LowerBound: 0
            UpperBound: 0
  FNF:
    PlatformToAaEntityToBoundsMap:
      ANDROID:
        AaEntityToBoundsMap:
          AA_ENTITY_AA_ONE_MONEY:
            LowerBound: 1
            UpperBound: 100
          AA_ENTITY_AA_FINVU:
            LowerBound: 0
            UpperBound: 0
      IOS:
        AaEntityToBoundsMap:
          AA_ENTITY_AA_FINVU:
            LowerBound: 1
            UpperBound: 100
          AA_ENTITY_AA_ONE_MONEY:
            LowerBound: 0
            UpperBound: 0
  EXTERNAL:
    PlatformToAaEntityToBoundsMap:
      ANDROID:
        AaEntityToBoundsMap:
          AA_ENTITY_AA_ONE_MONEY:
            LowerBound: 1
            UpperBound: 100
          AA_ENTITY_AA_FINVU:
            LowerBound: 0
            UpperBound: 0
      IOS:
        AaEntityToBoundsMap:
          AA_ENTITY_AA_FINVU:
            LowerBound: 1
            UpperBound: 100
          AA_ENTITY_AA_ONE_MONEY:
            LowerBound: 0
            UpperBound: 0

# If FIP is not listed here all flags are enabled and for NextFetchIntervalFailureCase global value is considered
# If FIP is listed here, all fields must be declared explicitly otherwise they would default to false
FipLevelControl:
  - "fip@nsdl":
      IsBalanceEnabled: true
      IsTxnsEnabled: false
  - "CDSLFIP":
      IsBalanceEnabled: true
      IsTxnsEnabled: false
  - "AXIS001":
      IsBalanceEnabled: true
      IsTxnsEnabled: true
      NextFetchIntervalFailureCase: 4h
      DataRangeStartDuration: 24h
  - "AUBank-FIP":
      IsBalanceEnabled: true
      IsTxnsEnabled: false
  - "KarurVysyaBank-FIP":
      IsBalanceEnabled: true
      IsTxnsEnabled: false
  - "BARBFIP":
      IsBalanceEnabled: true
      IsTxnsEnabled: false
  - "UBI-FIP":
      IsBalanceEnabled: true
      IsTxnsEnabled: false
  - "fiplive@canarabank":
      IsBalanceEnabled: true
      IsTxnsEnabled: false
  - "UCOB-FIP":
      IsBalanceEnabled: true
      IsTxnsEnabled: false
  - "IOB-FIP":
      IsBalanceEnabled: true
      IsTxnsEnabled: false
  - "BOI-FIP":
      IsBalanceEnabled: true
      IsTxnsEnabled: false
  - "IBFIP":
      IsBalanceEnabled: true
      IsTxnsEnabled: false
  - "PSFIP":
      IsBalanceEnabled: true
      IsTxnsEnabled: false

FeatureReleaseConfig:
  FeatureConstraints:
    - AA_CONSENT_RENEWAL:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups: [ ]
    - AA_FINVU_TOKEN_AUTHENTICATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 10
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - AA_DATA_FETCH_WAIT_FOR_ALL_NOTIFICATION:
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - AA_PERMITTED_FIP_CONFIG_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 7 # connected_account
    - FEATURE_CONNECTED_ACCOUNTS_SKIP_PAN_DOB:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_WEALTH_BUILDER_PAN_COLLECTION_FORM:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 405
          MinIOSVersion: 2570
    - FEATURE_INDIAN_STOCKS_EXTENDED_RANGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 418
          MinIOSVersion: 583
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100

V2FlowParams:
  UseV2Flow: true
  MinVersionAndroid: 175
  MinVersionIos: 2012

FiuId: "EPIFISIM"

LegalDocuments:
  AaOnemoneyTncUrl: "https://www.onemoney.in/tandc.html"
  AaFinvuTncUrl: "https://finvu.in/terms"

PermittedFIPsForTxnsBackfill: [
  "BARBFIP",
  "YESB-FIP",
  "UCOB-FIP",
  "fiplive@canarabank",
  "UBI-FIP",
  "KarurVysyaBank-FIP",
  "AXIS001AUBank-FIP",
  "ACME-FIP",
  "BARB0KIMXXX",
  "ICICI-FIP",
  "HDFC-FIP",
  "IDFCFirstBank-FIP",
  "PNB-FIP",
  "IOB-FIP",
  "PSFIP",
  "IBFIP"
]
NextTxnBackfillInterval: 48h
TxnBackfillCutOffDate: "2023-01-23T18:30:00Z"
EnableBatchUpsertTransactions: true
ConsentRenewalThreshold: 480h # 20 days
CATxnBackfillBucketName: "epifi-staging-ca-txn-backfill"

QuestSdk:
  Disable: false

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: ca-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80


EnableAATxnDataUploadingToS3: true

CaUserTxnDataBucketName: "epifi-staging-ca-user-txn-data"
CaAnalyticsBucketName: "epifi-staging-connected-account-analytics"
