package activity

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	oldtypes "github.com/epifi/gamma/api/types"

	types "github.com/epifi/gamma/api/typesv2"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"time"

	pkgErrors "github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	accountsPb "github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	ffAccActPb "github.com/epifi/gamma/api/firefly/accounting/activity"
	ffAccEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	timelinePb "github.com/epifi/gamma/api/timeline"
	ffEvents "github.com/epifi/gamma/firefly/events"
)

var (
	cardTxnTypeToTransferTypeMap = map[ffAccEnumsPb.TransactionType]types.TransactionTransferType{
		ffAccEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT:  types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_DEBIT,
		ffAccEnumsPb.TransactionType_TRANSACTION_TYPE_CREDIT: types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_CREDIT,
	}

	cardTxnCategoryToTxnCategoryMap = map[ffAccEnumsPb.TransactionCategory]oldtypes.TransactionCategory{
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_ATM_WITHDRAWAL:                       oldtypes.TransactionCategory_ATM_WITHDRAWAL,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_ECOM:                                 oldtypes.TransactionCategory_ECOMM,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_POS:                                  oldtypes.TransactionCategory_POS,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_ATM_REVERSAL:                         oldtypes.TransactionCategory_ATM_WITHDRAWAL,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_POS_REVERSAL:                         oldtypes.TransactionCategory_POS,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_ECOM_REVERSAL:                        oldtypes.TransactionCategory_ECOMM,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_REFUND:                               oldtypes.TransactionCategory_ECOMM,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_CASH_AT_POS:                          oldtypes.TransactionCategory_POS,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_VIRTUAL_ACCOUNT_CREDIT:               oldtypes.TransactionCategory_ECOMM,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_IMPS_DEBIT:                           oldtypes.TransactionCategory_CASH,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_IMPS_DEBIT_REVERSAL:                  oldtypes.TransactionCategory_CASH,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_FEES:                                 oldtypes.TransactionCategory_MISCELLANEOUS_CHARGES,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_FEES_REVERSAL:                        oldtypes.TransactionCategory_MISCELLANEOUS_CHARGES,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_FUND_POST_CREDIT:                     oldtypes.TransactionCategory_VMT,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_FUND_POST_DEBIT:                      oldtypes.TransactionCategory_VMT,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_C2M:                                  oldtypes.TransactionCategory_ECOMM,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_C2C:                                  oldtypes.TransactionCategory_CASH,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_M2C:                                  oldtypes.TransactionCategory_CASH,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_SERVICE_TAX:                          oldtypes.TransactionCategory_MISCELLANEOUS_CHARGES,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_SERVICE_TAX_REVERSAL:                 oldtypes.TransactionCategory_MISCELLANEOUS_CHARGES,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_INTEREST:                             oldtypes.TransactionCategory_MISCELLANEOUS_CHARGES,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_DIRECT_CREDIT:                        oldtypes.TransactionCategory_VMT,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_PG:                                   oldtypes.TransactionCategory_ECOMM,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_UPI_COLLECT_CREDIT:                   oldtypes.TransactionCategory_ECOMM,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_NETC_REGISTERED_FAILURE:              oldtypes.TransactionCategory_ECOMM,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_NETC_REGISTERED_SUCCESS:              oldtypes.TransactionCategory_ECOMM,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_NETC_CORPORATE_DEBIT:                 oldtypes.TransactionCategory_ECOMM,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_NETC_RETAIL_DEBIT:                    oldtypes.TransactionCategory_ECOMM,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_CASHBACK_CREDIT:                      oldtypes.TransactionCategory_MISCELLANEOUS_CHARGES,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_CHARGEBACK_CREDIT:                    oldtypes.TransactionCategory_MISCELLANEOUS_CHARGES,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_CASH_WITHDRAWAL_FEE:                  oldtypes.TransactionCategory_MISCELLANEOUS_CHARGES,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_DIRECT_DEBIT:                         oldtypes.TransactionCategory_VMT,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRINCIPAL:                       oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_INTEREST:                        oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_INTEREST_TAX:                    oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PROCESSING_FEES:                 oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PROCESSING_FEES_TAX:             oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_PRINCIPAL:           oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_INTEREST:            oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_INTEREST_TAX:        oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_FEES:                oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_FEES_TAX:            oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_PROCESSING_FEES:     oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_PROCESSING_FEES_TAX: oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_CREDIT:              oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_CANCEL_CREDIT:                   oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_CANCEL:                          oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_CANCEL_INTEREST:                 oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PROCESSING_FEES_REVERSAL:        oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_PROCESSING_FEES_TAX_REVERSAL:    oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_LOAN_REVERSAL:                        oldtypes.TransactionCategory_EMI,
		ffAccEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_EMI_CONVERSION:                       oldtypes.TransactionCategory_EMI,
	}
)

func (p *Processor) CreateTransactionAdditionalInfo(ctx context.Context, req *ffAccActPb.CreateTxnAdditionalInfoActivityRequest) (*ffAccActPb.CreateTxnAdditionalInfoActivityResponse, error) {
	var (
		res = &ffAccActPb.CreateTxnAdditionalInfoActivityResponse{}
	)
	lg := activity.GetLogger(ctx)

	if req.GetTransaction().GetTxnStatus() == ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILURE &&
		req.GetTransaction().GetFailureInfo().GetFailureType() == ffAccEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_FRAUD {
		lg.Info("skipping creation for txn additional info for FRM decline", zap.String(logger.TXN_ID, req.GetTransaction().GetId()))
		return res, nil
	}

	txnAdditionalInfo, err := p.txnAdditionalInfoDao.GetByTxnId(ctx, req.GetTransaction().GetId(), nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		txnAdditionalInfo, err = p.resolvePIDetailsForTxn(ctx, req.GetTransaction(), req.GetCard(), req.GetResolvedMerchantName())
		if err != nil {
			lg.Error("error in resolving pi details for txn", zap.Error(err), zap.String(logger.REFERENCE_ID,
				req.GetTransaction().GetRetrievalReferenceNo()), zap.String(logger.TXN_ID, req.GetTransaction().GetId()))
			return nil, pkgErrors.Wrap(epifierrors.ErrTransient, "error in resolving pi details for txn")
		}
		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			p.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), ffEvents.NewCcTransactionEvent(req.GetCard().GetActorId(), req.GetTransaction(), req.GetCreditAccount().GetCardProgram(), txnAdditionalInfo))
		})
		res.TransactionAdditionalInfo = txnAdditionalInfo
		return res, nil
	case err != nil:
		lg.Error("error in fetching transaction additional info", zap.String(logger.TXN_ID, req.GetTransaction().GetId()), zap.Error(err))
		return nil, pkgErrors.Wrap(epifierrors.ErrTransient, "error in fetching transaction additional info")
	default:
		lg.Debug("transaction additional info already exists for txn id", zap.String(logger.TXN_ID, req.GetTransaction().GetId()))
		res.TransactionAdditionalInfo = txnAdditionalInfo
		return res, nil
	}
}

// resolvePIDetailsForTxn fetches to and from payment instrument for a given txn and creates additional txn info entry
// after parsing the payment instrument details
// nolint: funlen
func (p *Processor) resolvePIDetailsForTxn(ctx context.Context, transaction *ffAccPb.CardTransaction,
	creditCard *ffPb.CreditCard, resolvedMerchantName string) (*ffAccPb.TransactionAdditionalInfo, error) {
	var (
		creditCardPi   *piPb.PaymentInstrument
		otherPiId      string
		piFrom         string
		piTo           string
		actorTo        string
		actorFrom      string
		otherActorName string
	)
	getPiRes, err := p.piClient.CreatePiAndAccountPi(ctx, &piPb.CreatePiAndAccountPiRequest{
		ActorId:              creditCard.GetActorId(),
		Type:                 piPb.PaymentInstrumentType_CREDIT_CARD,
		Identifier:           &piPb.CreatePiAndAccountPiRequest_CreditCard{CreditCard: &piPb.CreditCard{Id: creditCard.GetId()}},
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		Ownership:            piPb.Ownership_EPIFI_TECH,
		State:                piPb.PaymentInstrumentState_VERIFIED,
		AccountId:            creditCard.GetAccountId(),
		AccountType:          accountsPb.Type_CREDIT_CARD_ACCOUNT,
	})
	if te := epifigrpc.RPCError(getPiRes, err); te != nil {
		return nil, fmt.Errorf("error in fetching credit card pi %w", te)
	}
	creditCardPi = getPiRes.GetPi()
	eventType, ok := cardTxnTypeToTransferTypeMap[transaction.GetTxnType()]
	if !ok {
		return nil, fmt.Errorf("mapping not found for transaction type %s", transaction.GetTxnType().String())
	}
	txnCategory, ok := cardTxnCategoryToTxnCategoryMap[transaction.GetTxnCategory()]
	if !ok {
		return nil, fmt.Errorf("mapping not found for transaction category %s", transaction.GetTxnCategory().String())
	}
	if resolvedMerchantName == "" {
		otherActorName = transaction.GetBeneficiaryInfo().GetBeneficiaryName()
	} else {
		otherActorName = resolvedMerchantName
	}
	getOtherPiRes, err := p.actorClient.ResolveOtherActorPiAndTimeline(ctx, &actorPb.ResolveOtherActorPiAndTimelineRequest{
		PrimaryActorId:  creditCard.GetActorId(),
		PaymentProtocol: paymentPb.PaymentProtocol_CARD,
		EventType:       eventType,
		AccountType:     accountsPb.Type_CREDIT_CARD_ACCOUNT,
		// TODO(team) : Update this to credit card issuer bank once we have that in place
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		PiIdentifier: &actorPb.ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier_{
			CardTransactionIdentifier: &actorPb.ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier{
				Mid:            transaction.GetBeneficiaryInfo().GetBeneficiaryId(),
				OtherActorName: otherActorName,
			}},
		PiOwnership:       piPb.Ownership_EPIFI_TECH,
		TimelineOwnership: timelinePb.Ownership_EPIFI_TECH,
		MerchantDetails: &actorPb.MerchantDetails{
			Name:    otherActorName,
			MccCode: transaction.GetBeneficiaryInfo().GetMccCode(),
			Mid:     transaction.GetBeneficiaryInfo().GetBeneficiaryId(),
		},
		OtherActorName:      otherActorName,
		TransactionCategory: txnCategory,
	})
	if te := epifigrpc.RPCError(getOtherPiRes, err); te != nil {
		return nil, fmt.Errorf("error in fetching other actor pi %w", te)
	}
	otherPiId = getOtherPiRes.GetOtherActorPiId()
	// for credit transactions, the other beneficiary will be the from payment instrument and CC will be
	// to payment instrument
	if transaction.GetTxnType() == ffAccEnumsPb.TransactionType_TRANSACTION_TYPE_CREDIT {
		piFrom = otherPiId
		piTo = creditCardPi.GetId()
		actorTo = creditCard.GetActorId()
		actorFrom, err = p.resolveOtherActorId(ctx, piFrom, transaction.GetBeneficiaryInfo().GetBeneficiaryName(),
			creditCard.GetActorId(), transaction.GetTxnType())
		if err != nil {
			return nil, fmt.Errorf("error in fetching actor from %w", err)
		}
	} else {
		piFrom = creditCardPi.GetId()
		piTo = otherPiId
		actorFrom = creditCard.GetActorId()
		actorTo, err = p.resolveOtherActorId(ctx, piTo, transaction.GetBeneficiaryInfo().GetBeneficiaryName(),
			creditCard.GetActorId(), transaction.GetTxnType())
		if err != nil {
			return nil, fmt.Errorf("error in fetching actor to %w", err)
		}
	}
	txnAdditionalInfo := &ffAccPb.TransactionAdditionalInfo{
		TransactionId: transaction.GetId(),
		PiTo:          piTo,
		PiFrom:        piFrom,
		ActorFrom:     actorFrom,
		ActorTo:       actorTo,
		TxnTime:       transaction.GetTxnTime(),
	}
	if resolvedMerchantName != "" {
		txnAdditionalInfo.EnrichedBeneficiaryInfo = &ffAccPb.EnrichedBeneficiaryInfo{ResolvedBeneficiaryName: resolvedMerchantName}
	}
	txnAdditionalInfo, err = p.txnAdditionalInfoDao.Create(ctx, txnAdditionalInfo)
	if err != nil {
		return nil, fmt.Errorf("error in creating txn additional info %w", err)
	}
	return txnAdditionalInfo, nil
}

func (p *Processor) resolveOtherActorId(ctx context.Context, piId string, otherActorName, actorId string,
	txnType ffAccEnumsPb.TransactionType) (string, error) {
	if txnType == ffAccEnumsPb.TransactionType_TRANSACTION_TYPE_CREDIT {
		resolveActorRes, err := p.actorClient.ResolveActorFrom(ctx, &actorPb.ResolveActorFromRequest{
			ActorTo:       actorId,
			PiFrom:        piId,
			ActorFromName: otherActorName,
			Ownership:     commontypes.Ownership_EPIFI_TECH,
		})
		if te := epifigrpc.RPCError(resolveActorRes, err); te != nil {
			return "", fmt.Errorf("error in resolving from actor %w", te)
		}
		return resolveActorRes.GetActorFrom(), nil
	} else {
		resolveActorRes, err := p.actorClient.ResolveActorTo(ctx, &actorPb.ResolveActorToRequest{
			ActorFrom:   actorId,
			PiTo:        piId,
			ActorToName: otherActorName,
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		})
		if te := epifigrpc.RPCError(resolveActorRes, err); te != nil {
			return "", fmt.Errorf("error in resolving to actor %w", te)
		}
		return resolveActorRes.GetActorTo(), nil
	}
}
