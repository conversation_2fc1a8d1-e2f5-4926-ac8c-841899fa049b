package v2

import (
	"context"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/firefly/config/genconf"

	"github.com/samber/lo"

	"github.com/google/uuid"
	errorsPkg "github.com/pkg/errors"
	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	authPb "github.com/epifi/gamma/api/auth"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	ffConsumerV2Pb "github.com/epifi/gamma/api/firefly/v2/consumer"
	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	typesV2 "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	ccVgPb "github.com/epifi/gamma/api/vendorgateway/creditcard"
	"github.com/epifi/gamma/firefly/v2/dao"
	wireTypes "github.com/epifi/gamma/firefly/v2/wire/types"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

var (
	cardProgramSourceToApplicantMap = map[typesV2.CardProgramSource]ccEnumsV2Pb.CreditCardApplicantType{
		typesV2.CardProgramSource_CARD_PROGRAM_SOURCE_PREAPPROVED: ccEnumsV2Pb.CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_PRE_APPROVED_ONBOARDING,
	}
	cardProgramOriginToApplicationTypeMap = map[typesV2.CardProgramOrigin]ccEnumsV2Pb.CreditCardApplicantType{
		typesV2.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI:      ccEnumsV2Pb.CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_ETB_ONBOARDING,
		typesV2.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE: ccEnumsV2Pb.CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_NTB_ONBOARDING,
	}

	ccSdkModuleToTokenTypeMap = map[ccVgPb.CreditCardSdkModuleName]typesV2.CreditCardSdkTokenType{
		ccVgPb.CreditCardSdkModuleName_CREDIT_CARD_SDK_MODULE_NAME_CMS:        typesV2.CreditCardSdkTokenType_TOKEN_TYPE_CMS,
		ccVgPb.CreditCardSdkModuleName_CREDIT_CARD_SDK_MODULE_NAME_ONBOARDING: typesV2.CreditCardSdkTokenType_TOKEN_TYPE_ONBOARDING,
	}
)

type Service struct {
	ffBeV2Pb.UnimplementedFireflyV2Server
	conf                                  *genconf.Config
	ccVgClient                            ccVgPb.CreditCardClient
	authClient                            authPb.AuthClient
	cardRequestDao                        dao.CardRequestDao
	creditCardDao                         dao.CreditCardDao
	ccOfferDao                            dao.CreditCardOffersDao
	userClient                            userPb.UsersClient
	bankCustClient                        bankCustPb.BankCustomerServiceClient
	onbClient                             onbPb.OnboardingClient
	ccOnboardingStateUpdateEventPublisher wireTypes.CcOnboardingStateUpdateEventPublisher
	segmentationClient                    segmentPb.SegmentationServiceClient
}

func NewService(conf *genconf.Config, ccVgClient ccVgPb.CreditCardClient, authClient authPb.AuthClient,
	cardRequestDao dao.CardRequestDao, creditCardDao dao.CreditCardDao, ccOfferDao dao.CreditCardOffersDao,
	userClient userPb.UsersClient, bankCustClient bankCustPb.BankCustomerServiceClient,
	onbClient onbPb.OnboardingClient, ccOnboardingStateUpdateEventPublisher wireTypes.CcOnboardingStateUpdateEventPublisher,
	segmentationClient segmentPb.SegmentationServiceClient) *Service {
	return &Service{
		conf:                                  conf,
		ccVgClient:                            ccVgClient,
		authClient:                            authClient,
		cardRequestDao:                        cardRequestDao,
		creditCardDao:                         creditCardDao,
		ccOfferDao:                            ccOfferDao,
		userClient:                            userClient,
		bankCustClient:                        bankCustClient,
		onbClient:                             onbClient,
		ccOnboardingStateUpdateEventPublisher: ccOnboardingStateUpdateEventPublisher,
		segmentationClient:                    segmentationClient,
	}
}

func (s *Service) GetCreditCards(ctx context.Context, req *ffBeV2Pb.GetCreditCardsRequest) (*ffBeV2Pb.GetCreditCardsResponse, error) {
	var (
		res          = &ffBeV2Pb.GetCreditCardsResponse{}
		creditCards  []*ffBeV2Pb.CreditCard
		err          error
		stateFilters = []ccEnumsV2Pb.CardState{ccEnumsV2Pb.CardState_CARD_STATE_CREATED}
	)
	if len(req.GetStateFilters()) != 0 {
		stateFilters = req.GetStateFilters()
	}

	switch {
	case req.GetActorId() != "":
		creditCards, err = s.creditCardDao.GetByActorId(ctx, req.GetActorId())

	case req.GetExternalUserId() != "":
		creditCards, err = s.creditCardDao.GetByExternalUserId(ctx, req.GetExternalUserId())

	default:
		logger.Error(ctx, "invalid request for get credit cards")
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}
	switch {
	case err != nil && storageV2.IsRecordNotFoundError(err) || len(creditCards) == 0:
		logger.Error(ctx, "no credit cards found for user", zap.Error(err))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error fetching credit cards for user", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		for _, cc := range creditCards {
			if lo.Contains(stateFilters, cc.GetState()) {
				res.CreditCards = append(res.GetCreditCards(), cc)
			}
		}
		if len(res.GetCreditCards()) == 0 {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusOk()
		return res, nil
	}
}

func (s *Service) GenerateCreditCardSdkAuthToken(ctx context.Context, req *ffBeV2Pb.GenerateCreditCardSdkAuthTokenRequest) (*ffBeV2Pb.GenerateCreditCardSdkAuthTokenResponse, error) {
	savedCards, err := s.creditCardDao.GetByActorId(ctx, req.GetActorId())
	switch {
	case err != nil && !storageV2.IsRecordNotFoundError(err):
		logger.Error(ctx, "error fetching credit card for actor", zap.Error(err))
		return &ffBeV2Pb.GenerateCreditCardSdkAuthTokenResponse{
			Status: rpc.StatusInternal(),
		}, nil

	case len(savedCards) > 0 && savedCards[0].GetState() == ccEnumsV2Pb.CardState_CARD_STATE_CLOSED:
		logger.Info(ctx, "credit card is already closed", zap.String(logger.CARD_ID, savedCards[0].GetId()))
		return &ffBeV2Pb.GenerateCreditCardSdkAuthTokenResponse{
			Status: rpc.StatusFailedPrecondition(),
		}, nil
	}

	// get or create the latest onboarding request for the actor,
	// new onboarding request is created if no valid request is found or latest request is in failed state
	ccOnbReq, err := s.getOrCreateOnboardingRequest(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error getting or creating onboarding request for actor", zap.Error(err))
		return &ffBeV2Pb.GenerateCreditCardSdkAuthTokenResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	authToke, moduleName, err := s.generateCreditCardSdkAuthToken(ctx, ccOnbReq, req.GetDeviceInfo())
	if err != nil {
		logger.Error(ctx, "error generating credit card sdk auth token", zap.Error(err))
		return &ffBeV2Pb.GenerateCreditCardSdkAuthTokenResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &ffBeV2Pb.GenerateCreditCardSdkAuthTokenResponse{
		Status:    rpc.StatusOk(),
		AuthToken: authToke,
		TokenType: ccSdkModuleToTokenTypeMap[moduleName],
	}, nil
}

func (s *Service) generateCreditCardSdkAuthToken(ctx context.Context, ccOnbReq *ffBeV2Pb.CardRequest, deviceInfoFromFeReq *commontypes.Device) (string, ccVgPb.CreditCardSdkModuleName, error) {
	userDetailsResp, err := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: ccOnbReq.GetActorId(),
		},
	})
	if te := epifigrpc.RPCError(userDetailsResp, err); te != nil {
		return "", 0, errorsPkg.Wrap(te, "error fetching user details for actor id")
	}

	ccOffer, err := s.getCcOfferWithIdNilCheck(ctx, ccOnbReq.GetRequestDetails().GetOnboardingRequestDetails().GetOfferId())
	if err != nil {
		logger.Error(ctx, "error fetching cc offer by id", zap.Error(err))
		return "", 0, errorsPkg.Wrap(err, "error fetching cc offer by id for initial onboarding auth")
	}

	vgAuthRequest := &ccVgPb.GenerateCreditCardSdkAuthTokenRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_SAVEN,
		},
		ApplicantType: ccOnbReq.GetRequestDetails().GetOnboardingRequestDetails().GetApplicantType(),
		UserInfo: &ccVgPb.UserInfo{
			EmailAddress:   userDetailsResp.GetUser().GetProfile().GetEmail(),
			PhoneNumber:    userDetailsResp.GetUser().GetProfile().GetPhoneNumber(),
			InternalUserId: ccOnbReq.GetExternalUserId(),
		},
		DeviceInfo: &ccVgPb.DeviceInfo{
			Device: deviceInfoFromFeReq,
		},
	}

	if ccOffer != nil {
		vgAuthRequest.PreApprovedInfo = &ccVgPb.PreApprovedInfo{
			PreApprovedLimit: ccOffer.GetOfferConstraints().GetLimit(),
			PreApprovedExp:   dateTimePkg.TimeToDateInLoc(ccOffer.GetValidTill().AsTime(), dateTimePkg.IST),
		}
	}

	if (ccOnbReq.GetRequestDetails().GetOnboardingRequestDetails().GetApplicantType() == ccEnumsV2Pb.CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_ETB_ONBOARDING ||
		ccOnbReq.GetRequestDetails().GetOnboardingRequestDetails().GetApplicantType() == ccEnumsV2Pb.CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_PRE_APPROVED_ONBOARDING) &&
		userDetailsResp.GetUser().GetProfile().GetPAN() != "" && userDetailsResp.GetUser().GetProfile().GetPanName() != nil {
		vgAuthRequest.PanInfo = &ccVgPb.PanInfo{
			PanNumber: userDetailsResp.GetUser().GetProfile().GetPAN(),
			UserName:  userDetailsResp.GetUser().GetProfile().GetPanName(),
		}
	}

	vgGenCCAuthTokenResp, err := s.ccVgClient.GenerateCreditCardSdkAuthToken(ctx, vgAuthRequest)
	if te := epifigrpc.RPCError(vgGenCCAuthTokenResp, err); te != nil {
		return "", 0, errorsPkg.Wrap(te, "failed to generate cc auth token for cc sdk at vg")
	}

	if !s.isInitialOnboardingAuthCompleted(ccOnbReq) {
		logger.Info(ctx, "credit card onboarding initiated for actor", zap.String(logger.CARD_REQUEST_ID, ccOnbReq.GetId()))
		if err = s.handleCcOnboardingInitSuccess(ctx, ccOnbReq, vgGenCCAuthTokenResp); err != nil {
			logger.Error(ctx, "error updating onboarding request after generating cc auth token", zap.Error(err))
			return "", 0, errorsPkg.Wrap(err, "error updating onboarding request after generating cc auth token")
		}
	}

	return vgGenCCAuthTokenResp.GetAuthToken(), vgGenCCAuthTokenResp.GetModuleName(), nil
}

func (s *Service) handleCcOnboardingInitSuccess(ctx context.Context, cardRequest *ffBeV2Pb.CardRequest, vgGenCCAuthTokenResp *ccVgPb.GenerateCreditCardSdkAuthTokenResponse) error {
	_, err := s.ccOnboardingStateUpdateEventPublisher.Publish(ctx, &ffConsumerV2Pb.ProcessCreditCardOnboardingStateUpdateEventRequest{
		ExternalUserId:   cardRequest.GetExternalUserId(),
		VendorWorkflowId: vgGenCCAuthTokenResp.GetAdditionalInfo().GetWorkflowId(),
		WorkflowStatus:   vgGenCCAuthTokenResp.GetAdditionalInfo().GetWorkflowStatus(),
		StageTransitionInfo: &ffConsumerV2Pb.CreditCardOnboardingStageTransitionInfo{
			CurrentStage: ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_ONBOARDING_INIT_AT_VENDOR,
			NextStage:    vgGenCCAuthTokenResp.GetAdditionalInfo().GetWorkflowState(),
			Description:  vgGenCCAuthTokenResp.GetAdditionalInfo().GetWorkflowMessage(),
		},
	})
	if err != nil {
		logger.Error(ctx, "error publishing credit card onboarding stage update event", zap.Error(err))
		return err
	}
	return nil
}

// getOrCreateOnboardingRequest retrieves or creates a card onboarding request for the given actor Id.
// It first attempts to fetch existing onboarding requests for the actor. If a valid request is found, it is returned.
// If no valid request is found, a new onboarding request is created and returned.
func (s *Service) getOrCreateOnboardingRequest(ctx context.Context, actorId string) (*ffBeV2Pb.CardRequest, error) {
	cardRequests, err := s.cardRequestDao.GetByActorIdAndRequestType(ctx, actorId, ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING)
	if err != nil && !storageV2.IsRecordNotFoundError(err) {
		return nil, errorsPkg.Wrap(err, "error fetching cc onboarding request for actor id")
	}
	if len(cardRequests) > 0 && cardRequests[0].GetStatus() != ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED {
		return cardRequests[0], nil
	}
	extUserId := uuid.NewString()
	if len(cardRequests) > 0 {
		extUserId = cardRequests[0].GetExternalUserId()
	}
	// Create a new onboarding request
	return s.createOnboardingCardRequest(ctx, actorId, extUserId)
}

func (s *Service) createOnboardingCardRequest(ctx context.Context, actorId, extUserId string) (*ffBeV2Pb.CardRequest, error) {
	cardProgramOrigin, err := s.getCardProgramOriginForCcOnb(ctx, actorId)
	if err != nil {
		return nil, errorsPkg.Wrap(err, "error fetching card program origin for cc onboarding")
	}

	onbRequest := &ffBeV2Pb.CardRequest{
		ActorId: actorId,
		Type:    ccEnumsV2Pb.CardRequestType_CARD_REQUEST_TYPE_ONBOARDING,
		Status:  ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
		RequestDetails: &ffBeV2Pb.CardRequestDetails{
			Data: &ffBeV2Pb.CardRequestDetails_OnboardingRequestDetails{
				OnboardingRequestDetails: &ffBeV2Pb.CardOnboardingRequestDetails{
					// TODO(cb): make changes to get card program from client during onboarding init and modify the logic accordingly. For now this works since we'll be enabling onb for mgnifi only.
					CardProgram: &typesV2.CardProgram{
						CardProgramVendor:     typesV2.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL,
						CardProgramSource:     typesV2.CardProgramSource_CARD_PROGRAM_SOURCE_REALTIME_BRE,
						CardProgramType:       typesV2.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED,
						CardProgramCollateral: typesV2.CardProgramCollateral_CARD_PROGRAM_COLLATERAL_UNSPECIFIED,
						CardProgramOrigin:     cardProgramOrigin,
					},
				},
			},
		},
		StageDetails: &ffBeV2Pb.CardRequestStageDetails{
			Stages: map[string]*ffBeV2Pb.StageInfo{
				ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_ONBOARDING_INIT_AT_VENDOR.String(): {
					State:         ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_INITIATED,
					LastUpdatedAt: timestamp.Now(),
					StartedAt:     timestamp.Now(),
				},
			},
		},
		Vendor:         commonvgpb.Vendor_SAVEN,
		ExternalUserId: extUserId,
	}

	// check for any pre-approved offer for the actor
	ccOffer, err := s.ccOfferDao.GetActiveOfferByActorIdAndVendorAndCardProgram(ctx, actorId, commonvgpb.Vendor_FEDERAL_BANK,
		ffPkg.GetCardProgramStringFromCardProgram(&typesV2.CardProgram{
			CardProgramVendor:     typesV2.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL,
			CardProgramSource:     typesV2.CardProgramSource_CARD_PROGRAM_SOURCE_PREAPPROVED,
			CardProgramType:       typesV2.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED,
			CardProgramCollateral: typesV2.CardProgramCollateral_CARD_PROGRAM_COLLATERAL_UNSPECIFIED,
			CardProgramOrigin:     typesV2.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI,
		}))
	if err != nil && !storageV2.IsRecordNotFoundError(err) {
		return nil, errorsPkg.Wrap(err, "error fetching cc offers for actor id")
	}
	if ccOffer != nil {
		onbRequest.RequestDetails = &ffBeV2Pb.CardRequestDetails{
			Data: &ffBeV2Pb.CardRequestDetails_OnboardingRequestDetails{
				OnboardingRequestDetails: &ffBeV2Pb.CardOnboardingRequestDetails{
					OfferId:     ccOffer.GetId(),
					CardProgram: ccOffer.GetCardProgram(),
				},
			},
		}
	}

	applicantType := getOnboardingApplicationType(onbRequest)
	if applicantType == ccEnumsV2Pb.CreditCardApplicantType_CREDIT_CARD_APPLICANT_TYPE_UNSPECIFIED {
		return nil, errorsPkg.New("invalid applicant type for onboarding request")
	}
	onbRequest.RequestDetails.GetOnboardingRequestDetails().ApplicantType = applicantType

	newCardRequest, err := s.cardRequestDao.Create(ctx, onbRequest)
	if err != nil {
		return nil, errorsPkg.Wrap(err, "error creating cc onboarding request for actor id")
	}
	return newCardRequest, nil
}

// getCcOfferWithIdNilCheck retrieves a credit card offer by its ID if the ID is not empty.
// If the offer ID is empty, it returns nil without an error.
// If an error occurs during the retrieval, it returns the error.
func (s *Service) getCcOfferWithIdNilCheck(ctx context.Context, offerId string) (*ffBeV2Pb.CreditCardOffer, error) {
	if offerId == "" {
		return nil, nil
	}
	ccOffer, err := s.ccOfferDao.GetById(ctx, offerId)
	if err != nil {
		return nil, err
	}
	return ccOffer, nil
}

// isInitialOnboardingAuthCompleted checks if the initial onboarding auth has already been completed for a card request.
func (s *Service) isInitialOnboardingAuthCompleted(ccOnbReq *ffBeV2Pb.CardRequest) bool {
	if ccOnbReq.GetStatus() == ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS {
		return true
	}
	onbVendorInitStageState := ccOnbReq.GetStageDetails().GetStages()[ccEnumsV2Pb.CardRequestStage_CARD_REQUEST_STAGE_ONBOARDING_INIT_AT_VENDOR.String()].GetState()
	return onbVendorInitStageState == ccEnumsV2Pb.CardRequestStageState_CARD_REQUEST_STAGE_STATE_SUCCESS
}

// getOnboardingApplicationType retrieves the onboarding application type for a card request based on its card program.
// It first checks the card program source and then the card program origin if the source is not found.
func getOnboardingApplicationType(ccOnbReq *ffBeV2Pb.CardRequest) ccEnumsV2Pb.CreditCardApplicantType {
	onbApplicationType, ok := cardProgramSourceToApplicantMap[ccOnbReq.GetRequestDetails().GetOnboardingRequestDetails().GetCardProgram().GetCardProgramSource()]
	if !ok {
		onbApplicationType = cardProgramOriginToApplicationTypeMap[ccOnbReq.GetRequestDetails().GetOnboardingRequestDetails().GetCardProgram().GetCardProgramOrigin()]
	}
	return onbApplicationType
}

// TODO(cb): re-evaluate and fix this check, this is just a hack to bypass the check for now, will have to build the flow for proper NTB onboarding.
// We might need to collect pan dob and per dedupe check for NTB onboarding.
func (s *Service) getCardProgramOriginForCcOnb(ctx context.Context, actorId string) (typesV2.CardProgramOrigin, error) {
	// Get the bank customer details for the actor ID
	onbCcFeatureDetails, err := s.onbClient.GetFeatureDetails(ctx, &onbPb.GetFeatureDetailsRequest{
		ActorId: actorId,
		Feature: onbPb.Feature_FEATURE_CC,
	})
	if epifigrpc.RPCError(onbCcFeatureDetails, err) != nil {
		return 0, errorsPkg.Wrap(err, "error fetching CC feature details")
	}
	if onbCcFeatureDetails.GetIsFiLiteUser() && onbCcFeatureDetails.GetFeatureInfo().GetFeatureStatus() == onbPb.FeatureStatus_FEATURE_STATUS_ONBOARDING_IN_PROGRESS {
		return typesV2.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE, nil
	}
	return typesV2.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI, nil
}
