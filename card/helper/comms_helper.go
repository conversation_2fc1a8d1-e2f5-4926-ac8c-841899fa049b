package helper

import (
	"context"
	"fmt"
	"strings"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/samber/lo"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	cardPb "github.com/epifi/gamma/api/card"
	cardCtrlPb "github.com/epifi/gamma/api/card/control"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	"github.com/epifi/gamma/api/card/mappings"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	commsPb "github.com/epifi/gamma/api/comms"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	fcmPb "github.com/epifi/gamma/api/frontend/fcm"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/card/config"
	workerConf "github.com/epifi/gamma/card/config/worker"
)

type CommsHelper struct {
	conf           *config.Config
	workerConf     *workerConf.Config
	userClient     userPb.UsersClient
	cardCtrlClient cardCtrlPb.CardControlClient
}

var (
	carrierNameToTrackingUrlMap = map[string]string{
		"BLUEDART":  "https://www.bluedart.com/",
		"DELHIVERY": "https://www.delhivery.com/",
		"INDIAPOST": "https://www.indiapost.gov.in/",
		"SPEEDPOST": "https://www.indiapost.gov.in/",
	}
	defaultTat = "7-14"
)

func NewCommsHelper(conf *config.Config, workerConf *workerConf.Config, userClient userPb.UsersClient, cardCtrlClient cardCtrlPb.CardControlClient) *CommsHelper {
	return &CommsHelper{
		conf:           conf,
		workerConf:     workerConf,
		userClient:     userClient,
		cardCtrlClient: cardCtrlClient,
	}
}

func (h *CommsHelper) GetPnContentForSwitch(notificationType cardEnumsPb.SwitchNotificationResponse) (string, string, error) {
	pnContent, ok := h.conf.CardSwitchNotificationParams[notificationType.String()]
	if !ok {
		return "", "", fmt.Errorf("no pn content found for switch notification response %s", notificationType.String())
	}
	return pnContent.Title, pnContent.Body, nil
}

func GetPnWithCommonFields(title, body string, deeplink *dlPb.Deeplink) commsPb.CommMessage {
	return &commsPb.SendMessageRequest_Notification{
		Notification: &commsPb.NotificationMessage{
			Notification: &fcmPb.Notification{
				NotificationType: fcmPb.NotificationType_SYSTEM_TRAY,
				NotificationTemplates: &fcmPb.Notification_SystemTrayTemplate{
					SystemTrayTemplate: &fcmPb.SystemTrayTemplate{
						CommonTemplateFields: &fcmPb.CommonTemplateFields{
							Title:    title,
							Body:     body,
							Deeplink: deeplink,
						},
					},
				},
			},
		},
	}
}

func GetInAppNotificationWithCommonFields(title, body string, ctaList ...*fcmPb.NotificationCTA) commsPb.CommMessage {
	return &commsPb.SendMessageRequest_Notification{
		Notification: &commsPb.NotificationMessage{
			Priority: commsPb.NotificationPriority_NORMAL,
			Notification: &fcmPb.Notification{
				NotificationType: fcmPb.NotificationType_IN_APP,
				NotificationTemplates: &fcmPb.Notification_InAppTemplate{
					InAppTemplate: &fcmPb.InAppTemplate{
						CommonTemplateFields: &fcmPb.CommonTemplateFields{
							Title: title,
							Body:  body,
							IconAttributes: &fcmPb.IconAttributes{
								IconUrl: "https://epifi-icons.pointz.in/fibank/fi-logo.png",
							},
							Deeplink: lo.Ternary(len(ctaList) == 0, nil, ctaList[0].Deeplink),
						},
						NotificationCtaList:  ctaList,
						NotificationPriority: fcmPb.InAppNotificationPriority_NOTIFICATION_PRIORITY_CRITICAL,
					},
				},
			},
			ShouldSkipDeviceRegistrationCheck: commontypes.BooleanEnum_TRUE,
		},
	}
}

func (h *CommsHelper) GetTrackingUrlByCarrier(carrier string) (string, error) {
	trackingUrl, ok := carrierNameToTrackingUrlMap[strings.ToUpper(carrier)]
	if !ok {
		return "", fmt.Errorf("error getting tracking url for carrier %s", carrier)
	}
	return trackingUrl, nil
}

// GetDeliveryTatConfigByActorAndPrintingVendor fetch all the addresses and for the user and shipping preferences for DEBIT_CARD,
// based on the shipping address pin-code return delivery TAT for delivery for physical debit card,
// we are all the TAT as configuration for now, if the TAT config not available we will return a default TAT
//
// nolint:funlen
func (h *CommsHelper) GetDeliveryTatConfigByActorAndPrintingVendor(ctx context.Context, actorId string, cardPrintingVendor cpPb.CardPrintingVendor) string {
	var (
		deliveryTatConfigs map[string][]string
	)
	switch {
	case (h.workerConf == nil || h.workerConf.DeliveryCommsTatConfigs == nil) && (h.conf == nil || h.conf.DeliveryCommsTatConfigs == nil):
		logger.Error(ctx, "no config found for delivery tat", zap.String(logger.ACTOR_ID_V2, actorId))
		return defaultTat
	case h.workerConf == nil || h.workerConf.DeliveryCommsTatConfigs == nil:
		deliveryTatConfigs = h.conf.DeliveryCommsTatConfigs
	default:
		deliveryTatConfigs = h.workerConf.DeliveryCommsTatConfigs
	}

	user, userFetchErr := h.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{ActorId: actorId},
	})
	if te := epifigrpc.RPCError(user, userFetchErr); te != nil {
		logger.Error(ctx, "error fetching user",
			zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String(logger.VENDOR, cardPrintingVendor.String()))
		return defaultTat
	}

	allAddress, addrFetchErr := h.userClient.GetAllAddresses(ctx, &userPb.GetAllAddressesRequest{
		UserId: user.GetUser().GetId(),
	})
	if te := epifigrpc.RPCError(allAddress, addrFetchErr); te != nil {
		logger.Error(ctx, "error addresses for user", zap.Error(te),
			zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.USER_ID, user.GetUser().GetId()),
			zap.String(logger.VENDOR, cardPrintingVendor.String()))
		return defaultTat
	}

	shippingPreferences, prefFetchErr := h.userClient.GetShippingPreference(ctx, &userPb.GetShippingPreferenceRequest{
		ActorId:      actorId,
		ShippingItem: types.ShippingItem_DEBIT_CARD,
	})
	if te := epifigrpc.RPCError(shippingPreferences, prefFetchErr); te != nil {
		logger.Error(ctx, "error fetching shipping preferences for actor", zap.Error(te),
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String(logger.SHIPPING_ITEM, types.ShippingItem_DEBIT_CARD.String()),
			zap.String(logger.VENDOR, cardPrintingVendor.String()))
		return defaultTat
	}

	dcShippingAddress, ok := allAddress.GetAddresses()[shippingPreferences.GetPreference().GetAddressType().String()]
	if !ok {
		logger.Error(ctx, "shipping address details not found in all address resp for user",
			zap.String(logger.SHIPPING_PREFERENCE_ID, shippingPreferences.GetPreference().GetId()),
			zap.String(logger.ADDRESS_TYPE, shippingPreferences.GetPreference().GetAddressType().String()),
			zap.String(logger.ACTOR_ID_V2, actorId))
		return defaultTat
	}

	tatSearchString := fmt.Sprintf("%s:%s", cardPrintingVendor.String(), dcShippingAddress.GetAddresses()[0].GetPostalCode())
	for tatKey, searchArray := range deliveryTatConfigs {
		if lo.Contains(searchArray, tatSearchString) {
			return tatKey
		}
	}
	logger.Info(ctx, "tat config not found for key", zap.String("SEARCH_KEY", tatSearchString))
	return defaultTat
}

// GetCardAtmWithdrawalLimits is helper function to fetch card limits for debit card comms framework
// It takes in cardId and country code(default to "IN" is not provided) as parameter and return
// 1. currentSetLimits - limits set by user in app
// 2. maxAllowedLimitByBank - max limit (country-wise) allowed by bank
// 3. err - error in case of any kind of during fetching the limits
func (h *CommsHelper) GetCardAtmWithdrawalLimits(ctx context.Context, cardId string, countryCode string) (*moneyPb.Money, *moneyPb.Money, error) {
	if countryCode == "" {
		countryCode = "IN"
	}

	cardLimitsResp, err := h.cardCtrlClient.GetCardLimits(ctx, &cardCtrlPb.GetCardLimitsRequest{
		CardId: cardId,
	})
	if rpcErr := epifigrpc.RPCError(cardLimitsResp, err); rpcErr != nil {
		return nil, nil, fmt.Errorf("error while fetching card limits: %w", rpcErr)
	}
	var (
		currentSetAllowedLimit *moneyPb.Money
		maxAllowedLimitByBank  *moneyPb.Money
	)

	for _, limits := range cardLimitsResp.GetCardLimitData().GetCardLimitDetails() {
		if limits.GetTxnType() == cardPb.CardTransactionType_ATM &&
			isCountryIndia(countryCode) == (limits.GetLocType() == cardPb.CardUsageLocationType_DOMESTIC) {
			currentSetAllowedLimit = limits.GetCurrentAllowedAmount()
			maxAllowedLimitByBank = limits.GetMaxAllowedAmount()
		}
	}

	// if country is not india, fetch country-wise max limits
	if !isCountryIndia(countryCode) {
		maxAllowedLimitByBank = moneyPkg.ParseInt(mappings.GetCardInternationalTxnLimit(commonvgpb.Vendor_FEDERAL_BANK, countryCode).
			GetAtmWithdrawalLimit(), moneyPkg.RupeeCurrencyCode)
	}
	return currentSetAllowedLimit, maxAllowedLimitByBank, nil
}

func isCountryIndia(countryCode string) bool {
	indiaCountryCodes := []string{"IN", "IND"}
	return lo.Contains(indiaCountryCodes, countryCode)
}
