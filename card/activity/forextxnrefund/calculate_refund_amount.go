package activity

import (
	"context"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	cardPb "github.com/epifi/gamma/api/card"
	cardActivityPb "github.com/epifi/gamma/api/card/activity/forextransactionrefund"
	cardCommsPb "github.com/epifi/gamma/api/card/comms"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	externalPb "github.com/epifi/gamma/api/tiering/external"
	cardDao "github.com/epifi/gamma/card/dao"
	"github.com/epifi/gamma/card/helper"
)

// refers to the change where the forex fees & original transaction are being processed as 2 separate transactions by federal
const (
	fiPlusUsersUpperForexLimitForRefund = 1239.0
)

// CalculateRefundAmount activity will calculate the refund amount to be processed to the user.
func (p *Processor) CalculateRefundAmount(ctx context.Context, req *cardActivityPb.CalculateForexTxnRefundRequest) (*cardActivityPb.CalculateForexTxnRefundRequest, error) {
	var (
		lg              = activity.GetLogger(ctx)
		res             = &cardActivityPb.CalculateForexTxnRefundRequest{}
		actErr          error
		refundAmount    *money.Money
		refundSubStatus cardEnumsPb.RefundSubStatus
		refundStatus    = cardEnumsPb.RefundStatus_REFUND_STATUS_UNSPECIFIED
	)

	forexRefund, err := p.getForexTxnRefundEntryByClientReqId(ctx, req.GetRequestHeader().GetClientReqId())
	if err != nil {
		return nil, err
	}
	switch forexRefund.GetTxnTimeUserTier() {
	case externalPb.Tier_TIER_FI_INFINITE, externalPb.Tier_TIER_FI_SALARY, externalPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		refundAmount = forexRefund.GetTotalTxnAmount()
		refundStatus = cardEnumsPb.RefundStatus_REFUND_STATUS_EVALUATED

	case externalPb.Tier_TIER_FI_PLUS, externalPb.Tier_TIER_FI_AA_SALARY_BAND_1, externalPb.Tier_TIER_FI_AA_SALARY_BAND_2, externalPb.Tier_TIER_FI_SALARY_BASIC:
		eligibleTxnAmountForRefundCalculation, err := p.getTxnAmountForRefundCalculationWithCappedLimit(ctx, forexRefund)
		if err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, "error in checking refund eligibility of user")
		}

		// if the eligibleTxnAmountForRefundCalculation is 0, it means the user has already crossed the 30k limit for the month
		if moneyPkg.IsZero(eligibleTxnAmountForRefundCalculation) {
			refundStatus = cardEnumsPb.RefundStatus_REFUND_STATUS_REJECTED
			refundSubStatus = cardEnumsPb.RefundSubStatus_REFUND_SUB_STATUS_REFUND_CAPPED
			refundAmount = nil
		} else {
			refundAmount = eligibleTxnAmountForRefundCalculation
			refundStatus = cardEnumsPb.RefundStatus_REFUND_STATUS_EVALUATED
		}

	case externalPb.Tier_TIER_FI_BASIC, externalPb.Tier_TIER_FI_REGULAR, externalPb.Tier_TIER_UNSPECIFIED:
		lg.Info("user not eligible for forex refund", zap.String(logger.TXN_ID, forexRefund.GetTxnId()))
		refundStatus = cardEnumsPb.RefundStatus_REFUND_STATUS_REJECTED
		refundSubStatus = cardEnumsPb.RefundSubStatus_REFUND_SUB_STATUS_REJECTED_BY_TIER
		actErr = errors.Wrap(epifierrors.ErrPermanent, "user not eligible for forex refund")
	}

	forexRefund.RefundStatus = refundStatus
	forexRefund.RefundAmount = refundAmount
	forexRefund.RefundSubStatus = refundSubStatus
	updateMasks := []cardEnumsPb.DcForexTxnRefundFieldMask{
		cardEnumsPb.DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_STATUS,
		cardEnumsPb.DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_SUB_STATUS,
	}
	if forexRefund.GetRefundAmount() != nil {
		updateMasks = append(updateMasks, cardEnumsPb.DcForexTxnRefundFieldMask_DC_FOREX_TXN_REFUND_FIELD_MASK_REFUND_AMOUNT)
	}

	if updErr := p.forexTxnDao.Update(ctx, forexRefund, updateMasks); updErr != nil {
		lg.Error("error in updating RefundAmount and RefundStatus for txn")
		return nil, errors.Wrap(updErr, "error in updating RefundAmount and RefundStatus for txn")
	}

	if actErr != nil {
		return nil, actErr
	}

	commsErr := p.sendForexMarkupComms(ctx, forexRefund)
	if commsErr != nil {
		return nil, commsErr
	}
	return res, nil
}

// getTxnAmountForRefundCalculationWithCappedLimit will figure out whether a fi plus/salary band 1,2 user is eligible for refund .
// To do this, we will take the net total txn refund amount
// for the month of the txn and if that amount > fiPlusUsersUpperForexLimitForRefund (₹1239 currently),
// we will only refund upto the cost of fiPlusUsersUpperForexLimitForRefund
func (p *Processor) getTxnAmountForRefundCalculationWithCappedLimit(ctx context.Context, forexRefund *cardPb.DcForexTxnRefund) (*money.Money, error) {
	fiPlusUsersUpperForexLimitForRefundFloat := fiPlusUsersUpperForexLimitForRefund
	currentTxnAmtFloat, _ := moneyPkg.ToDecimal(forexRefund.GetTotalTxnAmount()).Float64()
	txnTime := forexRefund.GetTxnTime()
	startOfMonth := datetime.StartOfMonth(txnTime.AsTime())
	endOfMonth := datetime.EndOfMonth(txnTime.AsTime())
	refundStatusFilterOptions := cardDao.WithRefundStatusFilter([]cardEnumsPb.RefundStatus{
		cardEnumsPb.RefundStatus_REFUND_STATUS_PROCESSED,
		cardEnumsPb.RefundStatus_REFUND_STATUS_EVALUATED,
		cardEnumsPb.RefundStatus_REFUND_STATUS_COMPLETED,
	})
	txnTimeFilterOptions := cardDao.WithTxnTimeFilters(timestamppb.New(startOfMonth), timestamppb.New(endOfMonth))
	monthlyRefunds, err := p.forexTxnDao.GetByActorIdAndTier(ctx, forexRefund.GetActorId(), forexRefund.GetTxnTimeUserTier(), nil, txnTimeFilterOptions, refundStatusFilterOptions)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		// if in the 1st transaction, the amount is greater than fiPlusUsersUpperForexLimitForRefundFloat, the whole
		// amount is refunded instead of a partial refund
		return moneyPkg.ParseFloat(currentTxnAmtFloat, "INR"), nil
	case err != nil:
		return nil, err
	}

	totalForexExpenditure := float64(0)
	for _, ref := range monthlyRefunds {
		var forexAmtFloat float64
		// adding deduping to avoid over calculating the current txn
		if ref.GetId() == forexRefund.GetId() {
			continue
		}

		forexAmtFloat, _ = moneyPkg.ToDecimal(ref.GetTotalTxnAmount()).Float64()
		switch ref.GetTxnType() {
		case cardEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT:
			totalForexExpenditure += forexAmtFloat
		default:
			continue
		}
	}

	// returning 0 INR in case that the total expenditure without the current txn exceeds 30k
	if totalForexExpenditure > fiPlusUsersUpperForexLimitForRefundFloat {
		return &money.Money{
			CurrencyCode: "INR",
			Units:        0,
			Nanos:        0,
		}, nil
	}
	// even if the amount is greater than fiPlusUsersUpperForexLimitForRefundFloat, the whole amount is refunded instead
	// of a partial refund
	return forexRefund.GetTotalTxnAmount(), nil
}

// given the total txn amount, it returns the forex fees amount
func (p *Processor) getFullRefundAmount(totalTxnAmount *money.Money) *money.Money {
	forexChargeWithGst := float64(helper.GetForexChargePercentageWithGST())
	txnAmountFloat, _ := moneyPkg.ToDecimal(totalTxnAmount).Float64()
	txnAmountWithoutForex := txnAmountFloat / (1 + forexChargeWithGst/100)
	refundValue := txnAmountFloat - txnAmountWithoutForex
	return moneyPkg.ParseFloat(refundValue, "INR")
}

func (p *Processor) sendForexMarkupComms(ctx context.Context, forexRefund *cardPb.DcForexTxnRefund) error {
	if !moneyPkg.IsZero(forexRefund.GetRefundAmount()) && forexRefund.GetRefundStatus() == cardEnumsPb.RefundStatus_REFUND_STATUS_EVALUATED {
		// get card details
		getCardsForActorRes, getCardsForActorErr := p.cardDao.GetCardsForActor(ctx, forexRefund.GetActorId(), []cardPb.CardState{cardPb.CardState_ACTIVATED}, []cardPb.CardType{cardPb.CardType_DEBIT}, nil, nil, []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK}, cardPb.CardFieldMask_CARD_CREATED_AT, 1)
		if getCardsForActorErr != nil {
			return getCardsForActorErr
		}
		savedCard := getCardsForActorRes[0]

		// send comms
		_, err := p.commsProcessor.ProcessAction(ctx, &cardCommsPb.ActionData{
			ActorId:                    forexRefund.GetActorId(),
			TxnAmount:                  forexRefund.GetTotalTxnAmount(),
			SwitchNotificationResponse: cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNSPECIFIED,
			CardId:                     savedCard.GetId(),
			CardLastFourDigits:         savedCard.GetBasicInfo().GetMaskedCardNumber()[len(savedCard.GetBasicInfo().GetMaskedCardNumber())-4:],
			CardHolderName:             &commontypes.Name{FirstName: savedCard.GetBasicInfo().GetCustomerName()},
			Data: &cardCommsPb.ActionData_ForexNotificationData{
				ForexNotificationData: &cardCommsPb.ForexNotificationData{TxnTime: forexRefund.GetTxnTime()},
			},
		})
		if err != nil {
			logger.Error(ctx, "error in sending forex markup txn comms", zap.Error(err), zap.String(logger.ACTOR_ID_V2, forexRefund.GetActorId()))
		}
	}
	return nil
}
