// nolint: dupl
package layout

import (
	"context"
	"time"

	"github.com/samber/lo"

	cardPb "github.com/epifi/gamma/api/card"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	externalPb "github.com/epifi/gamma/api/tiering/external"
	accountsTypes "github.com/epifi/gamma/api/typesv2/account"
)

// NOTE: Validator function naming convention - LayoutId{int}Validator
// please right the conditions in comments when adding a new validator

const (
	userAgePivot = -120 * 24 * time.Hour
)

// conditions - UserAge < 120 days, CardForm - DIGITAL
func layoutId1Validator(ctx context.Context, userData *CardUserData, provider *LayoutPriorityProvider) (bool, error) {
	// 1. check for usr age
	if time.Now().Add(userAgePivot).After(userData.AllCards[len(userData.AllCards)-1].GetCreatedAt().AsTime()) {
		return false, nil
	}

	// 2. check card form
	if userData.CurrentCard.GetForm() != cardPb.CardForm_DIGITAL {
		return false, nil
	}
	return true, nil
}

// conditions - UserAge < 120 days, CardForm - PHYSICAL, DeliveryState - RECEIVED_BY_USER
func layoutId2Validator(ctx context.Context, userData *CardUserData, provider *LayoutPriorityProvider) (bool, error) {
	// 1. check for usr age
	if time.Now().Add(userAgePivot).After(userData.AllCards[len(userData.AllCards)-1].GetCreatedAt().AsTime()) {
		return false, nil
	}

	// 2. check card form
	if userData.CurrentCard.GetForm() != cardPb.CardForm_PHYSICAL {
		return false, nil
	}

	// 3. physical card is not yet activated by user i.e. still in delivery
	if userData.CardDeliveryTrackingInfo != nil &&
		userData.CardDeliveryTrackingInfo.GetState() != cpPb.CardDeliveryTrackingState_RECEIVED_BY_USER &&
		userData.LatestPhysicalDispatchRequest != nil {
		return false, nil
	}

	return true, nil
}

// conditions - UserAge > 120 days, CardForm - DIGITAL, TiersList - [standard, plus]
func layoutId3Validator(ctx context.Context, userData *CardUserData, provider *LayoutPriorityProvider) (bool, error) {
	var (
		allowedTiers = []externalPb.Tier{externalPb.Tier_TIER_FI_BASIC, externalPb.Tier_TIER_FI_REGULAR, externalPb.Tier_TIER_FI_PLUS, externalPb.Tier_TIER_FI_AA_SALARY_BAND_1, externalPb.Tier_TIER_FI_AA_SALARY_BAND_2}
	)
	// 1. check for usr age
	if time.Now().Add(userAgePivot).Before(userData.AllCards[len(userData.AllCards)-1].GetCreatedAt().AsTime()) {
		return false, nil
	}

	// 2. check card form
	if userData.CurrentCard.GetForm() != cardPb.CardForm_DIGITAL {
		return false, nil
	}

	// 3. check for tier
	if !lo.Contains(allowedTiers, userData.CurrentTier) {
		return false, nil
	}
	return true, nil
}

// conditions - UserAge > 120 days, CardForm - DIGITAL, TiersList - [infinite, salary]
func layoutId4Validator(ctx context.Context, userData *CardUserData, provider *LayoutPriorityProvider) (bool, error) {
	var (
		allowedTiers = []externalPb.Tier{externalPb.Tier_TIER_FI_INFINITE, externalPb.Tier_TIER_FI_SALARY, externalPb.Tier_TIER_FI_SALARY_BASIC, externalPb.Tier_TIER_FI_AA_SALARY_BAND_3}
	)
	// 1. check for usr age
	if time.Now().Add(userAgePivot).Before(userData.AllCards[len(userData.AllCards)-1].GetCreatedAt().AsTime()) {
		return false, nil
	}

	// 2. check card form
	if userData.CurrentCard.GetForm() != cardPb.CardForm_DIGITAL {
		return false, nil
	}

	// 3. check for tier
	if !lo.Contains(allowedTiers, userData.CurrentTier) {
		return false, nil
	}
	return true, nil
}

// conditions - UserAge > 120 days, CardForm - PHYSICAL, TiersList - [all tiers], DeliveryState - RECEIVED_BY_USER
func layoutId5Validator(ctx context.Context, userData *CardUserData, provider *LayoutPriorityProvider) (bool, error) {
	/*
		var (
			allowedTiers = []externalPb.Tier{externalPb.Tier_TIER_FI_INFINITE, externalPb.Tier_TIER_FI_SALARY,
				externalPb.Tier_TIER_FI_AA_SALARY_BAND_2, externalPb.Tier_TIER_FI_AA_SALARY_BAND_3}
		)
	*/

	// 1. check for user age
	if time.Now().Add(userAgePivot).Before(userData.AllCards[len(userData.AllCards)-1].GetCreatedAt().AsTime()) {
		return false, nil
	}

	// 2. check card form
	if userData.CurrentCard.GetForm() != cardPb.CardForm_PHYSICAL {
		return false, nil
	}

	// 3. physical card is not yet activated by user i.e. still in delivery
	if userData.CardDeliveryTrackingInfo != nil &&
		userData.CardDeliveryTrackingInfo.GetState() != cpPb.CardDeliveryTrackingState_RECEIVED_BY_USER &&
		userData.LatestPhysicalDispatchRequest != nil {
		return false, nil
	}

	// TODO(CB): add separate layout and validator for the same based on tiering.
	/*
		// 3. check for tier
		if !lo.Contains(allowedTiers, userData.CurrentTier) {
			return false, nil
		}
		return true, nil
	*/
	return true, nil
}

// conditions - the user must be NR(savings) account holder
func layoutId6Validator(ctx context.Context, userData *CardUserData, provider *LayoutPriorityProvider) (bool, error) {
	var (
		nrAccountsApoList = []accountsTypes.AccountProductOffering{
			accountsTypes.AccountProductOffering_APO_NRE, accountsTypes.AccountProductOffering_APO_NRO,
		}
	)
	if lo.Contains(nrAccountsApoList, userData.SavingsAccount.GetSkuInfo().GetAccountProductOffering()) {
		return true, nil
	}
	return false, nil
}

func layoutId7Validator(ctx context.Context, userData *CardUserData, provider *LayoutPriorityProvider) (bool, error) {
	return userData.IsTravelModeOn, nil
}
