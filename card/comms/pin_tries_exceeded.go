// nolint: dupl
package comms

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	cardCommsPb "github.com/epifi/gamma/api/card/comms"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/card/helper"
)

type PinTriesExceededRule struct {
	helper *helper.CommsHelper
}

func NewPinTriesExceededRule(helper *helper.CommsHelper) *PinTriesExceededRule {
	return &PinTriesExceededRule{
		helper: helper,
	}
}

func (s *PinTriesExceededRule) GetComms(ctx context.Context, data *cardCommsPb.ActionData) (res []commsPb.CommMessage, resErr error) {

	if data.GetSwitchNotificationResponse() == cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_PIN_TRIES_EXCEEDED_FOR_TRANSACTIONS {
		// SMS Comms
		res = append(res, &commsPb.SendMessageRequest_Sms{
			Sms: &commsPb.SMSMessage{
				SmsOption: &commsPb.SmsOption{
					Option: &commsPb.SmsOption_DebitCardPinTriesExceededForTransactionSmsOption{
						DebitCardPinTriesExceededForTransactionSmsOption: &commsPb.DebitCardPinTriesExceededForTransactionSmsOption{
							SmsType: commsPb.SmsType_DEBIT_CARD_PIN_TRIES_EXCEEDED_FOR_TRANSACTIONS,
							Option: &commsPb.DebitCardPinTriesExceededForTransactionSmsOption_DebitCardPinTriesExceededForTransactionSmsOptionV2{
								DebitCardPinTriesExceededForTransactionSmsOptionV2: &commsPb.DebitCardPinTriesExceededForTransactionSmsOptionV2{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V2,
									LastFourDigit:   data.GetSwitchNotificationData().GetLastFourDigits(),
								},
							},
						},
					},
				},
			},
		})

		pnTitle, pnBody, err := s.helper.GetPnContentForSwitch(data.GetSwitchNotificationResponse())
		if err != nil {
			logger.Error(ctx, "error in fetching pn content", zap.Error(err))
		} else {
			res = append(res, helper.GetPnWithCommonFields(pnTitle, pnBody, helper.NewCardSettingScreenDl()),
				helper.GetInAppNotificationWithCommonFields(pnTitle, pnBody, helper.NewCardSettingNotificationCta()))
		}
	}
	return
}
