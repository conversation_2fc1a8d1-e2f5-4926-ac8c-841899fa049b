// nolint: dupl
package comms

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/frontend/fcm"

	cardCommsPb "github.com/epifi/gamma/api/card/comms"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	commsPb "github.com/epifi/gamma/api/comms"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/card/helper"
)

type UnableToAuthorizeTxnRule struct {
	helper *helper.CommsHelper
}

func NewUnableToAuthorizeTxnRule(helper *helper.CommsHelper) *UnableToAuthorizeTxnRule {
	return &UnableToAuthorizeTxnRule{
		helper: helper,
	}
}

func (s *UnableToAuthorizeTxnRule) GetComms(ctx context.Context, data *cardCommsPb.ActionData) (res []commsPb.CommMessage, resErr error) {

	if data.GetSwitchNotificationResponse() == cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNABLE_TO_AUTHORIZE_TRANSACTION {
		// SMS Comms
		res = append(res, &commsPb.SendMessageRequest_Sms{
			Sms: &commsPb.SMSMessage{
				SmsOption: &commsPb.SmsOption{
					Option: &commsPb.SmsOption_DebitCardUnableToAuthorizeTransactionSmsOption{
						DebitCardUnableToAuthorizeTransactionSmsOption: &commsPb.DebitCardUnableToAuthorizeTransactionSmsOption{
							SmsType: commsPb.SmsType_DEBIT_CARD_UNABLE_TO_AUTHORIZE_TRANSACTION,
							Option: &commsPb.DebitCardUnableToAuthorizeTransactionSmsOption_DebitCardUnableToAuthorizeTransactionSmsOptionV2{
								DebitCardUnableToAuthorizeTransactionSmsOptionV2: &commsPb.DebitCardUnableToAuthorizeTransactionSmsOptionV2{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V2,
									Merchant:        data.GetSwitchNotificationData().GetMerchant(),
									TxnAmount:       data.GetSwitchNotificationData().GetTxnAmount(),
								},
							},
						},
					},
				},
			},
		})

		pnTitle, pnBody, err := s.helper.GetPnContentForSwitch(data.GetSwitchNotificationResponse())
		if err != nil {
			logger.Error(ctx, "error in fetching pn content", zap.Error(err))
		} else {
			res = append(res, helper.GetPnWithCommonFields(pnTitle, pnBody, &dlPb.Deeplink{Screen: dlPb.Screen_CARD_USAGE_SCREEN}),
				helper.GetInAppNotificationWithCommonFields(pnTitle, pnBody, &fcm.NotificationCTA{Deeplink: &dlPb.Deeplink{Screen: dlPb.Screen_CARD_USAGE_SCREEN}}))
		}
	}
	return
}
