// nolint: dupl
package comms

import (
	"context"

	"github.com/epifi/be-common/pkg/logger"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"

	"go.uber.org/zap"

	cardCommsPb "github.com/epifi/gamma/api/card/comms"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/card/helper"
)

type UnAuthorizedUsageRule struct {
	helper *helper.CommsHelper
}

func NewUnAuthorizedUsageRule(helper *helper.CommsHelper) *UnAuthorizedUsageRule {
	return &UnAuthorizedUsageRule{
		helper: helper,
	}
}

func (p *UnAuthorizedUsageRule) GetComms(ctx context.Context, data *cardCommsPb.ActionData) (res []commsPb.CommMessage, resErr error) {
	if p.isApplicable(data) {
		pnTitle, pnBody, err := p.helper.GetPnContentForSwitch(data.GetSwitchNotificationData().GetSwitchNotificationResponse())
		if err != nil {
			logger.Error(ctx, "error in fetching switch notification pn content", zap.Error(err))
		} else {
			res = append(res, helper.GetPnWithCommonFields(pnTitle, pnBody, &dlPb.Deeplink{Screen: dlPb.Screen_CARD_USAGE_SCREEN}),
				helper.GetInAppNotificationWithCommonFields(pnTitle, pnBody, &fcm.NotificationCTA{Deeplink: &dlPb.Deeplink{Screen: dlPb.Screen_CARD_USAGE_SCREEN}}))
		}
		res = append(res, &commsPb.SendMessageRequest_Sms{
			Sms: &commsPb.SMSMessage{
				SmsOption: &commsPb.SmsOption{
					Option: &commsPb.SmsOption_DebitCardUnauthorizedUsageSmsOption{
						DebitCardUnauthorizedUsageSmsOption: &commsPb.DebitCardUnauthorizedUsageSmsOption{
							SmsType: commsPb.SmsType_DEBIT_CARD_UNAUTHORIZED_USAGE,
							Option: &commsPb.DebitCardUnauthorizedUsageSmsOption_DebitCardUnauthorizedUsageSmsOptionV1{
								DebitCardUnauthorizedUsageSmsOptionV1: &commsPb.DebitCardUnauthorizedUsageSmsOptionV1{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
									Merchant:        data.GetSwitchNotificationData().GetMerchant(),
									TxnAmount:       data.GetSwitchNotificationData().GetTxnAmount(),
									LastFourDigit:   data.GetSwitchNotificationData().GetLastFourDigits(),
								},
							},
						},
					},
				},
			},
		})
	}
	return
}

func (p *UnAuthorizedUsageRule) isApplicable(data *cardCommsPb.ActionData) bool {
	if data.GetSwitchNotificationData().GetSwitchNotificationResponse() != cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_UNAUTHORIZED_USAGE {
		return false
	}
	return true
}
