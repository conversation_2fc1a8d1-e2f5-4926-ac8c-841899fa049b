// nolint: dupl
package comms

import (
	"context"

	"github.com/epifi/be-common/pkg/logger"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"

	"go.uber.org/zap"

	cardCommsPb "github.com/epifi/gamma/api/card/comms"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/card/helper"
)

type BadCardStatusRule struct {
	helper *helper.CommsHelper
}

func NewBadCardStatusRule(helper *helper.CommsHelper) *BadCardStatusRule {
	return &BadCardStatusRule{
		helper: helper,
	}
}

func (l *BadCardStatusRule) GetComms(ctx context.Context, data *cardCommsPb.ActionData) (res []commsPb.CommMessage, resErr error) {
	if l.isApplicable(data) {
		pnTitle, pnBody, err := l.helper.GetPnContentForSwitch(data.GetSwitchNotificationData().GetSwitchNotificationResponse())
		if err != nil {
			logger.Error(ctx, "error in fetching switch notification pn content", zap.Error(err))
		} else {
			res = append(res, helper.GetPnWithCommonFields(pnTitle, pnBody, &dlPb.Deeplink{Screen: dlPb.Screen_CARD_HOME_SCREEN}),
				helper.GetInAppNotificationWithCommonFields(pnTitle, pnBody, &fcm.NotificationCTA{Deeplink: &dlPb.Deeplink{Screen: dlPb.Screen_CARD_HOME_SCREEN}}))
		}

		res = append(res, &commsPb.SendMessageRequest_Sms{
			Sms: &commsPb.SMSMessage{
				SmsOption: &commsPb.SmsOption{
					Option: &commsPb.SmsOption_DebitCardBadCardStatusSmsOption{
						DebitCardBadCardStatusSmsOption: &commsPb.DebitCardBadCardStatusSmsOption{
							SmsType: commsPb.SmsType_DEBIT_CARD_BAD_CARD_STATUS,
							Option: &commsPb.DebitCardBadCardStatusSmsOption_DebitCardBadCardStatusSmsOptionV1{
								DebitCardBadCardStatusSmsOptionV1: &commsPb.DebitCardBadCardStatusSmsOptionV1{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
									LastFourDigit:   data.GetSwitchNotificationData().GetLastFourDigits(),
									DownloadLink:    "https://fi.onelink.me/FiMony/yspc3mge",
								},
							},
						},
					},
				},
			},
		})
	}

	return
}

func (l *BadCardStatusRule) isApplicable(data *cardCommsPb.ActionData) bool {
	if data.GetSwitchNotificationData().GetSwitchNotificationResponse() != cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_BAD_CARD_STATUS {
		return false
	}
	return true
}
