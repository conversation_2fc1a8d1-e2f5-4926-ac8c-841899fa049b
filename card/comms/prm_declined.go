// nolint: dupl
package comms

import (
	"context"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/frontend/fcm"

	cardCommsPb "github.com/epifi/gamma/api/card/comms"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	commsPb "github.com/epifi/gamma/api/comms"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/card/helper"
)

type PrmDeclinedRule struct {
	helper *helper.CommsHelper
}

func NewPrmDeclinedRule(helper *helper.CommsHelper) *PrmDeclinedRule {
	return &PrmDeclinedRule{
		helper: helper,
	}
}

func (s *PrmDeclinedRule) GetComms(ctx context.Context, data *cardCommsPb.ActionData) (res []commsPb.CommMessage, resErr error) {

	if s.isApplicable(data) {
		switch {
		case data.GetSwitchNotificationData().GetTxnCategory() == cardEnumsPb.CardTransactionCategory_CARD_TRANSACTION_CATEGORY_ATM_WITHDRAWAL:
			res = append(res, &commsPb.SendMessageRequest_Sms{
				Sms: &commsPb.SMSMessage{
					SmsOption: &commsPb.SmsOption{
						Option: &commsPb.SmsOption_DebitCardPrmDeclinedSmsOption{
							DebitCardPrmDeclinedSmsOption: &commsPb.DebitCardPrmDeclinedSmsOption{
								SmsType: commsPb.SmsType_DEBIT_CARD_PRM_DECLINED,
								Option: &commsPb.DebitCardPrmDeclinedSmsOption_DebitCardPrmDeclinedSmsOptionV2{
									DebitCardPrmDeclinedSmsOptionV2: &commsPb.DebitCardPrmDeclinedSmsOptionV2{
										TemplateVersion: commsPb.TemplateVersion_VERSION_V2,
										Country:         data.GetSwitchNotificationData().GetTxnCountryCode(),
										LastFourDigit:   data.GetSwitchNotificationData().GetLastFourDigits(),
										TxnTime:         data.GetSwitchNotificationData().GetTxnTime(),
										DownloadLink:    "https://fi.onelink.me/FiMony/yspc3mge",
									},
								},
							},
						},
					},
				},
			})

			if data.GetSwitchNotificationData().GetTxnCountryCode() != "" && !lo.Contains(indiaCountryCodes, data.GetSwitchNotificationData().GetTxnCountryCode()) {
				_, maxCountryWiseLimit, limitErr := s.helper.GetCardAtmWithdrawalLimits(ctx, data.GetSwitchNotificationData().GetCardId(), data.GetSwitchNotificationData().GetTxnCountryCode())
				if limitErr != nil {
					logger.Error(ctx, "error while fetching internation limits", zap.String(logger.CARD_ID, data.GetSwitchNotificationData().GetCardId()), zap.Error(limitErr))
					return
				}

				res = append(res, &commsPb.SendMessageRequest_Whatsapp{
					Whatsapp: &commsPb.WhatsappMessage{
						WhatsappOption: &commsPb.WhatsappOption{
							Option: &commsPb.WhatsappOption_DebitCardIntlDailyMaxWithdrawalLimitReached{
								DebitCardIntlDailyMaxWithdrawalLimitReached: &commsPb.DebitCardIntlDailyMaxWithdrawalLimitReached{
									WhatsappType: commsPb.WhatsappType_WHATSAPP_TYPE_DEBIT_CARD_INTL_DAILY_MAX_WITHDRAWAL_LIMIT_REACHED,
									Option: &commsPb.DebitCardIntlDailyMaxWithdrawalLimitReached_DebitCardIntlDailyMaxWithdrawalLimitReachedV1{
										DebitCardIntlDailyMaxWithdrawalLimitReachedV1: &commsPb.DebitCardIntlDailyMaxWithdrawalLimitReachedV1{
											TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
											CardHolderName:  data.GetSwitchNotificationData().GetCardHolderName(),
											LastFourDigits:  data.GetSwitchNotificationData().GetLastFourDigits(),
											TxnTime:         data.GetSwitchNotificationData().GetTxnTime(),
											Country:         data.GetSwitchNotificationData().GetTxnCountryCode(),
											MaxLimit:        maxCountryWiseLimit,
										},
									},
								},
							},
						},
					},
				})
			}

			res = append(res, helper.GetPnWithCommonFields(
				"❌ Payment declined ❌",
				"You have exceeded the daily maximum limit on ATM  withdrawals for this country. Try again tomorrow! ",
				&dlPb.Deeplink{Screen: dlPb.Screen_CARD_LIMITS_HOME_SCREEN}),

				helper.GetInAppNotificationWithCommonFields(
					"❌ Payment declined ❌",
					"You have exceeded the daily maximum limit on ATM  withdrawals for this country. Try again tomorrow! ", &fcm.NotificationCTA{Deeplink: &dlPb.Deeplink{Screen: dlPb.Screen_CARD_LIMITS_HOME_SCREEN}}))
		default:
			res = append(res, &commsPb.SendMessageRequest_Sms{
				Sms: &commsPb.SMSMessage{
					SmsOption: &commsPb.SmsOption{
						Option: &commsPb.SmsOption_DebitCardPrmDeclinedSmsOption{
							DebitCardPrmDeclinedSmsOption: &commsPb.DebitCardPrmDeclinedSmsOption{
								SmsType: commsPb.SmsType_DEBIT_CARD_PRM_DECLINED,
								Option: &commsPb.DebitCardPrmDeclinedSmsOption_DebitCardPrmDeclinedSmsOptionV1{
									DebitCardPrmDeclinedSmsOptionV1: &commsPb.DebitCardPrmDeclinedSmsOptionV1{
										TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
										DownloadLink:    "https://fi.onelink.me/FiMony/yspc3mge",
									},
								},
							},
						},
					},
				},
			})

			pnTitle, pnBody, err := s.helper.GetPnContentForSwitch(data.GetSwitchNotificationData().GetSwitchNotificationResponse())
			if err != nil {
				logger.Error(ctx, "error in fetching pn content", zap.Error(err))
			} else {
				res = append(res, helper.GetPnWithCommonFields(pnTitle, pnBody, &dlPb.Deeplink{Screen: dlPb.Screen_CARD_HOME_SCREEN}),
					helper.GetInAppNotificationWithCommonFields(pnTitle, pnBody))
			}
		}
	}
	return
}

func (s *PrmDeclinedRule) isApplicable(data *cardCommsPb.ActionData) bool {
	if data.GetSwitchNotificationData().GetSwitchNotificationResponse() != cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_PRM_DECLINED {
		return false
	}
	return true
}
