// nolint: dupl
package comms

import (
	"context"

	"github.com/epifi/be-common/pkg/logger"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"

	"go.uber.org/zap"

	cardCommsPb "github.com/epifi/gamma/api/card/comms"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/card/helper"
)

type TokenCofFlagOffRule struct {
	helper *helper.CommsHelper
}

func NewTokenCofFlagOffRule(helper *helper.CommsHelper) *TokenCofFlagOffRule {
	return &TokenCofFlagOffRule{
		helper: helper,
	}
}

func (c *TokenCofFlagOffRule) GetComms(ctx context.Context, data *cardCommsPb.ActionData) (res []commsPb.CommMessage, resErr error) {
	if c.isApplicable(data) {
		// Pn
		pnTitle, pnBody, err := c.helper.GetPnContentForSwitch(data.GetSwitchNotificationData().GetSwitchNotificationResponse())
		if err != nil {
			logger.Error(ctx, "error in fetching pn content", zap.Error(err))
		} else {
			res = append(res, helper.GetPnWithCommonFields(pnTitle, pnBody, &dlPb.Deeplink{Screen: dlPb.Screen_CARD_USAGE_SCREEN}),
				helper.GetInAppNotificationWithCommonFields(pnTitle, pnBody, &fcm.NotificationCTA{Deeplink: &dlPb.Deeplink{Screen: dlPb.Screen_CARD_USAGE_SCREEN}}))
		}
	}

	return
}

func (c *TokenCofFlagOffRule) isApplicable(data *cardCommsPb.ActionData) bool {
	if data.GetSwitchNotificationData().GetSwitchNotificationResponse() != cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_TOKEN_COF_FLAG_OFF {
		return false
	}
	return true
}
