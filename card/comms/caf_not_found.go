// nolint: dupl
package comms

import (
	"context"

	"github.com/epifi/be-common/pkg/logger"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"

	"go.uber.org/zap"

	cardCommsPb "github.com/epifi/gamma/api/card/comms"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/card/helper"
)

type CafNotFoundRule struct {
	helper *helper.CommsHelper
}

func NewCafNotFoundRule(helper *helper.CommsHelper) *CafNotFoundRule {
	return &CafNotFoundRule{
		helper: helper,
	}
}

func (c *CafNotFoundRule) GetComms(ctx context.Context, data *cardCommsPb.ActionData) (res []commsPb.CommMessage, resErr error) {
	if c.isApplicable(data) {
		pnTitle, pnBody, err := c.helper.GetPnContentForSwitch(data.GetSwitchNotificationData().GetSwitchNotificationResponse())
		if err != nil {
			logger.Error(ctx, "error in fetching pn content", zap.Error(err))
		} else {
			res = append(res, helper.GetPnWithCommonFields(pnTitle, pnBody, &dlPb.Deeplink{Screen: dlPb.Screen_CARD_HOME_SCREEN}),
				helper.GetInAppNotificationWithCommonFields(pnTitle, pnBody, &fcm.NotificationCTA{Deeplink: &dlPb.Deeplink{Screen: dlPb.Screen_CARD_HOME_SCREEN}}))
		}

		res = append(res, &commsPb.SendMessageRequest_Sms{
			Sms: &commsPb.SMSMessage{
				SmsOption: &commsPb.SmsOption{
					Option: &commsPb.SmsOption_DebitCardCafNotFoundSmsOption{
						DebitCardCafNotFoundSmsOption: &commsPb.DebitCardCafNotFoundSmsOption{
							SmsType: commsPb.SmsType_DEBIT_CARD_CAF_NOT_FOUND,
							Option: &commsPb.DebitCardCafNotFoundSmsOption_DebitCardCafNotFoundSmsOptionV1{
								DebitCardCafNotFoundSmsOptionV1: &commsPb.DebitCardCafNotFoundSmsOptionV1{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
									Merchant:        data.GetSwitchNotificationData().GetMerchant(),
									LastFourDigit:   data.GetSwitchNotificationData().GetLastFourDigits(),
									DownloadLink:    "https://fi.onelink.me/FiMony/yspc3mge",
								},
							},
						},
					},
				},
			},
		})
	}

	return
}

func (c *CafNotFoundRule) isApplicable(data *cardCommsPb.ActionData) bool {
	if data.GetSwitchNotificationData().GetSwitchNotificationResponse() != cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_CAF_NOT_FOUND {
		return false
	}
	return true
}
