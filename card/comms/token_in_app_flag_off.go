// nolint: dupl
package comms

import (
	"context"

	"github.com/epifi/be-common/pkg/logger"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"

	"go.uber.org/zap"

	cardCommsPb "github.com/epifi/gamma/api/card/comms"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/card/helper"
)

type TokenInAppFlagOffRule struct {
	helper *helper.CommsHelper
}

func NewTokenInAppFlagOffRule(helper *helper.CommsHelper) *TokenInAppFlagOffRule {
	return &TokenInAppFlagOffRule{
		helper: helper,
	}
}

func (c *TokenInAppFlagOffRule) GetComms(ctx context.Context, data *cardCommsPb.ActionData) (res []commsPb.CommMessage, resErr error) {
	if c.isApplicable(data) {
		pnTitle, pnBody, err := c.helper.GetPnContentForSwitch(data.GetSwitchNotificationData().GetSwitchNotificationResponse())
		if err != nil {
			logger.Error(ctx, "error in fetching pn content", zap.Error(err))
		} else {
			res = append(res, helper.GetPnWithCommonFields(pnTitle, pnBody, &dlPb.Deeplink{Screen: dlPb.Screen_CARD_USAGE_SCREEN}),
				helper.GetInAppNotificationWithCommonFields(pnTitle, pnBody, &fcm.NotificationCTA{Deeplink: &dlPb.Deeplink{Screen: dlPb.Screen_CARD_USAGE_SCREEN}}))
		}

		res = append(res, &commsPb.SendMessageRequest_Sms{
			Sms: &commsPb.SMSMessage{
				SmsOption: &commsPb.SmsOption{
					Option: &commsPb.SmsOption_DebitCardTokenInAppFlagOffSmsOption{
						DebitCardTokenInAppFlagOffSmsOption: &commsPb.DebitCardTokenInAppFlagOffSmsOption{
							SmsType: commsPb.SmsType_DEBIT_CARD_TOKEN_IN_APP_FLAG_OFF,
							Option: &commsPb.DebitCardTokenInAppFlagOffSmsOption_DebitCardTokenInAppFlagOffSmsOptionV1{
								DebitCardTokenInAppFlagOffSmsOptionV1: &commsPb.DebitCardTokenInAppFlagOffSmsOptionV1{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
									Merchant:        data.GetSwitchNotificationData().GetMerchant(),
									LastFourDigit:   data.GetSwitchNotificationData().GetLastFourDigits(),
								},
							},
						},
					},
				},
			},
		})
	}

	return
}

func (c *TokenInAppFlagOffRule) isApplicable(data *cardCommsPb.ActionData) bool {
	if data.GetSwitchNotificationData().GetSwitchNotificationResponse() != cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_TOKEN_IN_APP_FLAG_OFF {
		return false
	}
	return true
}
