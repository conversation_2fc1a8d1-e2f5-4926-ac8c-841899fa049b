// nolint: dupl
package comms

import (
	"context"

	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"

	cardCommsPb "github.com/epifi/gamma/api/card/comms"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/card/helper"
)

type InvalidCardRule struct {
	helper *helper.CommsHelper
}

func NewInvalidCardRule(helper *helper.CommsHelper) *InvalidCardRule {
	return &InvalidCardRule{
		helper: helper,
	}
}

func (c *InvalidCardRule) GetComms(ctx context.Context, data *cardCommsPb.ActionData) (res []commsPb.CommMessage, resErr error) {
	if c.isApplicable(data) {
		// Pn
		pnTitle, pnBody, err := c.helper.GetPnContentForSwitch(data.GetSwitchNotificationData().GetSwitchNotificationResponse())
		if err != nil {
			logger.Error(ctx, "error in fetching pn content", zap.Error(err))
		} else {
			res = append(res, helper.GetPnWithCommonFields(pnTitle, pnBody, helper.NewCardSettingScreenDl()),
				helper.GetInAppNotificationWithCommonFields(pnTitle, pnBody, helper.NewCardSettingNotificationCta()))
		}
	}

	return
}

func (c *InvalidCardRule) isApplicable(data *cardCommsPb.ActionData) bool {
	if data.GetSwitchNotificationData().GetSwitchNotificationResponse() != cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_INVALID_CARD {
		return false
	}
	return true
}
