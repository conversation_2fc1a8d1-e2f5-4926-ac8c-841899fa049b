// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	time "time"

	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	genconfig2 "github.com/epifi/be-common/pkg/cmd/config/genconf"
	roarray "github.com/epifi/be-common/pkg/data_structs/roarray"
	keycloak "github.com/epifi/be-common/pkg/keycloak"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	config "github.com/epifi/gamma/cx/config"
	genconfig "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	genpay "github.com/epifi/gamma/pkg/pay/genconf"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MaxCountThresholdForFetchingBulkUserInfo             int64
	_UsePkgRateLimiter                                    uint32
	_IsRedactionEnabledForDBStates                        uint32
	_EnableBalanceMetricsOnCaseManagement                 uint32
	_EnableOutCallDataInCaseManagementForRiskOps          uint32
	_IsNewOperationalStatusAPIEnabled                     uint32
	_WatchlistReasons                                     roarray.ROArray[string]
	_WatchlistReasonsMutex                                *sync.RWMutex
	_CXFreshdeskTicketBaseURL                             string
	_CXFreshdeskTicketBaseURLMutex                        *sync.RWMutex
	_EmailVerification                                    *EmailVerification
	_MobilePromptVerification                             *MobilePromptVerification
	_CustomerAuth                                         *CustomerAuth
	_FreshdeskTicketSubscriber                            *gencfg.SqsSubscriber
	_FreshdeskContactSubscriber                           *gencfg.SqsSubscriber
	_WatsonIncidentReportingSubscriber                    *gencfg.SqsSubscriber
	_WatsonIncidentResolutionSubscriber                   *gencfg.SqsSubscriber
	_WatsonTicketEventSubscriber                          *gencfg.SqsSubscriber
	_WatsonCreateTicketSubscriber                         *gencfg.SqsSubscriber
	_DisputeSubscriber                                    *gencfg.SqsSubscriber
	_DisputeCreateTicketSubscriber                        *gencfg.SqsSubscriber
	_DisputeUpdateTicketSubscriber                        *gencfg.SqsSubscriber
	_DisputeAddNoteTicketSubscriber                       *gencfg.SqsSubscriber
	_DevActionSubscriber                                  *gencfg.SqsSubscriber
	_FreshdeskTicketDataEventSubscriberFifo               *gencfg.SqsSubscriber
	_FreshdeskTicketDataEventSubscriber                   *gencfg.SqsSubscriber
	_CrmIssueTrackerIntegrationSubscriber                 *gencfg.SqsSubscriber
	_Dispute                                              *Dispute
	_AppLog                                               *AppLog
	_Payout                                               *Payout
	_PayoutStatusCheckSubscriber                          *gencfg.SqsSubscriber
	_WaitlistSubscriber                                   *gencfg.SqsSubscriber
	_BulkUserInfoViaEmailConfig                           *BulkUserInfoViaEmailConfig
	_UpiDisputeAutoUpdateEventSubscriber                  *gencfg.SqsSubscriber
	_UpdateTicketSubscriber                               *gencfg.SqsSubscriber
	_CreateTicketSubscriber                               *gencfg.SqsSubscriber
	_TicketConfig                                         *TicketConfig
	_CallConfig                                           *CallConfig
	_OzonetelCallEventSubscriber                          *gencfg.SqsSubscriber
	_CallRoutingConfig                                    *CallRoutingConfig
	_FeatureReleaseConfig                                 *genconfig.FeatureReleaseConfig
	_IssueResolutionFeedbackConfig                        *IssueResolutionFeedbackConfig
	_ChatBotConfig                                        *ChatBotConfig
	_FreshchatEventSubscriber                             *gencfg.SqsSubscriber
	_RiskConfig                                           *RiskConfig
	_InternationalFundTransfer                            *InternationalFundTransfer
	_SalaryOpsConfig                                      *SalaryOpsConfig
	_LandingPageConfig                                    *LandingPageConfig
	_TicketReconciliationEventSubscriber                  *gencfg.SqsSubscriber
	_DevActionHelperConfig                                *DevActionHelperConfig
	_OverrideBankActions                                  *OverrideBankActions
	_ReviewAction                                         *ReviewActionConfig
	_VendorAccountPennyDropViaEmailConfig                 *VendorAccountPennyDropViaEmailConfig
	_EmployerDbConfig                                     *EmployerDbConfig
	_GRPCWebServerConfig                                  *GRPCWebServerConfig
	_SherlockBannersConfig                                *SherlockBannersConfig
	_RudderEventKafkaConsumerGroup                        *gencfg.KafkaConsumerGroup
	_ErrorActivityConfig                                  *ErrorActivityConfig
	_AgentPromptConfig                                    *AgentPromptConfig
	_RiskOpsInstalledAppsConfig                           *RiskOpsInstalledAppsConfig
	_RiskFennelConfig                                     *RiskFennelConfig
	_StageWiseCommsConfig                                 *StageWiseCommsConfig
	_IssueConfigServiceConfig                             *IssueConfigServiceConfig
	_S3EventSubscriber                                    *gencfg.SqsSubscriber
	_S3EventConsumerConfig                                *S3EventConsumerConfig
	_RiskTxnReviewRolloutConfig                           *RiskTxnReviewRolloutConfig
	_RiskOutcallFormRolloutConfig                         *RiskOutcallFormRolloutConfig
	_CallIvrConfig                                        *CallIvrConfig
	_CaseManagementActorActivities                        *CaseManagementActorActivities
	_WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail *WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail
	_ContactUsModelResponseConfig                         *ContactUsModelResponseConfig
	_S3BucketNameForFileGenerator                         *Filegenerator
	_DbStateConfig                                        *DbStateConfig
	_EscalationConfig                                     *EscalationConfig
	_FederalEscalationUpdateEventSubscriber               *gencfg.SqsSubscriber
	_FederalEscalationCreationEventSubscriber             *gencfg.SqsSubscriber
	_FederalEscalationConfig                              *FederalEscalationConfig
	_SaClosureEligibilityConf                             *SaClosureEligibilityConf
	_Application                                          *config.Application
	_Server                                               *config.Server
	_Logging                                              *cfg.Logging
	_EpifiDb                                              *cfg.DB
	_Aws                                                  *config.Aws
	_Cognito                                              *config.Cognito
	_AuthFactorRetryLimit                                 *config.AuthFactorRetryLimit
	_FreshdeskTicketPublisher                             *cfg.SqsPublisher
	_FreshdeskContactPublisher                            *cfg.SqsPublisher
	_WatsonIncidentReportingPublisher                     *cfg.SqsPublisher
	_WatsonIncidentResolutionPublisher                    *cfg.SqsPublisher
	_WatsonTicketEventPublisher                           *cfg.SqsPublisher
	_DisputePublisher                                     *cfg.SqsPublisher
	_DisputeCreateTicketPublisher                         *cfg.SqsPublisher
	_DisputeUpdateTicketPublisher                         *cfg.SqsPublisher
	_DisputeAddNoteTicketPublisher                        *cfg.SqsPublisher
	_DisputeExternalPublisher                             *cfg.SqsPublisher
	_RMSEventPublisher                                    *cfg.SqsPublisher
	_RewardsManualGiveawayEventPublisher                  *cfg.SqsPublisher
	_DevActionPublisher                                   *cfg.SqsPublisher
	_UpdateTicketPublisher                                *cfg.SqsPublisher
	_CreateTicketPublisher                                *cfg.SqsPublisher
	_CelestialSignalWorkflowPublisher                     *cfg.SqsPublisher
	_IFTFileProcessorEventPublisher                       *cfg.SqsPublisher
	_RiskCasePublisher                                    *cfg.SqsPublisher
	_RiskDisputePublisher                                 *cfg.SqsPublisher
	_CrmIssueTrackerIntegrationPublisher                  *cfg.SqsPublisher
	_AuditLog                                             *config.AuditLog
	_Transaction                                          *config.Transaction
	_Sherlock                                             *config.Sherlock
	_Secrets                                              *cfg.Secrets
	_RedisOptions                                         *cfg.RedisOptions
	_Flags                                                *config.Flags
	_Comms                                                *config.Comms
	_PayoutStatusCheckPublisher                           *cfg.SqsPublisher
	_DisputeNotificationTemplates                         map[string]map[string]map[string]map[string]map[string]*config.DisputeNotificationContent
	_OnboardingStageDetailsMapping                        map[string]*config.OnboardingStageDetails
	_AppLogsNotificationContent                           *config.AppLogsNotificationContent
	_KYCConfig                                            *config.KYCConfig
	_RateLimit                                            *config.RateLimit
	_RlConfig                                             *cfg.RateLimitConfig
	_CallRecording                                        *config.CallRecording
	_RudderStack                                          *cfg.RudderStackBroker
	_CxEventAmountCategories                              *[]config.CxEventAmountCategory
	_OrderConfig                                          *config.OrderConfig
	_ReferralConfig                                       *config.ReferralConfig
	_LivenessVideoConfig                                  *config.LivenessVideoConfig
	_SupportTicketFreshdeskConfig                         *cfg.Freshdesk
	_ProcessTicketJobConfig                               *cfg.CxTicketAutoResolutionJobConfig
	_CxS3Config                                           *config.CxS3Config
	_RiskS3Config                                         *config.RiskS3Config
	_DataS3Config                                         *config.DataS3Config
	_EpifiIconS3Config                                    *config.EpifiIconS3Config
	_PriorityRoutingConfig                                *config.PriorityRoutingConfig
	_FreshChatConfig                                      *config.FreshChatConfig
	_AuthValidation                                       *config.AuthValidation
	_BulkTicketJobConfig                                  *config.BulkTicketJobConfig
	_Tracing                                              *cfg.Tracing
	_Profiling                                            *cfg.Profiling
	_UploadCreditMISToVendorPublisher                     *cfg.SqsPublisher
	_BulkAccValidationViaEmailConfig                      *config.BulkAccValidationViaEmailConfig
	_AccountFreezeStatusConfig                            *config.AccountFreezeStatusConfig
	_SherlockFeedbackDetailsConfig                        *config.SherlockFeedbackDetailsConfig
	_SalaryProgramLeadManagementConfig                    *config.SalaryProgramLeadManagementConfig
	_WatsonConfig                                         *config.WatsonConfig
	_SprinklrConfig                                       *config.SprinklrConfig
	_UsStocksOpsConfig                                    *config.UsStocksOpsConfig
	_ClosedAccountConfig                                  *config.ClosedAccountConfig
	_MonorailConfig                                       *cfg.MonorailConfig
	_AirflowConfig                                        *cfg.AirflowConfig
	_FreshdeskMonorailIntegrationConfig                   *config.FreshdeskMonorailIntegrationConfig
	_EmailValidationRegex                                 string
	_SherlockUserRequestsConfig                           *config.SherlockUserRequestsConfig
	_IssueCategoryIdForCategory                           *config.IssueCategoryIdForCategory
	_RewardsOrderUpdateEventQueuePublisher                *cfg.SqsPublisher
	_RewardsCreditCardTxnEventQueuePublisher              *cfg.SqsPublisher
	_CallRoutingEventPublisher                            *cfg.SnsPublisher
	_TicketUpdateEventPublisher                           *cfg.SnsPublisher
	_CasperItcDownloadFileQueuePublisher                  *cfg.SqsPublisher
	_InternationalFundsTransferConfig                     *config.InternationalFundsTransferConfig
	_StrapiConfig                                         *cfg.StrapiConfig
	_OrderUpdateEventForTxnCategorizationPublisher        *cfg.SqsPublisher
	_AATxnCategorizationPublisher                         *cfg.SqsPublisher
	_CCTxnCategorizationPublisher                         *cfg.SqsPublisher
	_CreateTicketEventPublisher                           *cfg.SnsPublisher
	_FederalEscalationCreateEventPublisher                *cfg.SqsPublisher
	_LienConfig                                           *config.LienConfig
}

func (obj *Config) MaxCountThresholdForFetchingBulkUserInfo() int {
	return int(atomic.LoadInt64(&obj._MaxCountThresholdForFetchingBulkUserInfo))
}
func (obj *Config) UsePkgRateLimiter() bool {
	if atomic.LoadUint32(&obj._UsePkgRateLimiter) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) IsRedactionEnabledForDBStates() bool {
	if atomic.LoadUint32(&obj._IsRedactionEnabledForDBStates) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) EnableBalanceMetricsOnCaseManagement() bool {
	if atomic.LoadUint32(&obj._EnableBalanceMetricsOnCaseManagement) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) EnableOutCallDataInCaseManagementForRiskOps() bool {
	if atomic.LoadUint32(&obj._EnableOutCallDataInCaseManagementForRiskOps) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) IsNewOperationalStatusAPIEnabled() bool {
	if atomic.LoadUint32(&obj._IsNewOperationalStatusAPIEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) WatchlistReasons() roarray.ROArray[string] {
	obj._WatchlistReasonsMutex.RLock()
	defer obj._WatchlistReasonsMutex.RUnlock()
	return obj._WatchlistReasons
}
func (obj *Config) CXFreshdeskTicketBaseURL() string {
	obj._CXFreshdeskTicketBaseURLMutex.RLock()
	defer obj._CXFreshdeskTicketBaseURLMutex.RUnlock()
	return obj._CXFreshdeskTicketBaseURL
}
func (obj *Config) EmailVerification() *EmailVerification {
	return obj._EmailVerification
}
func (obj *Config) MobilePromptVerification() *MobilePromptVerification {
	return obj._MobilePromptVerification
}
func (obj *Config) CustomerAuth() *CustomerAuth {
	return obj._CustomerAuth
}
func (obj *Config) FreshdeskTicketSubscriber() *gencfg.SqsSubscriber {
	return obj._FreshdeskTicketSubscriber
}
func (obj *Config) FreshdeskContactSubscriber() *gencfg.SqsSubscriber {
	return obj._FreshdeskContactSubscriber
}
func (obj *Config) WatsonIncidentReportingSubscriber() *gencfg.SqsSubscriber {
	return obj._WatsonIncidentReportingSubscriber
}
func (obj *Config) WatsonIncidentResolutionSubscriber() *gencfg.SqsSubscriber {
	return obj._WatsonIncidentResolutionSubscriber
}
func (obj *Config) WatsonTicketEventSubscriber() *gencfg.SqsSubscriber {
	return obj._WatsonTicketEventSubscriber
}
func (obj *Config) WatsonCreateTicketSubscriber() *gencfg.SqsSubscriber {
	return obj._WatsonCreateTicketSubscriber
}
func (obj *Config) DisputeSubscriber() *gencfg.SqsSubscriber {
	return obj._DisputeSubscriber
}
func (obj *Config) DisputeCreateTicketSubscriber() *gencfg.SqsSubscriber {
	return obj._DisputeCreateTicketSubscriber
}
func (obj *Config) DisputeUpdateTicketSubscriber() *gencfg.SqsSubscriber {
	return obj._DisputeUpdateTicketSubscriber
}
func (obj *Config) DisputeAddNoteTicketSubscriber() *gencfg.SqsSubscriber {
	return obj._DisputeAddNoteTicketSubscriber
}
func (obj *Config) DevActionSubscriber() *gencfg.SqsSubscriber {
	return obj._DevActionSubscriber
}
func (obj *Config) FreshdeskTicketDataEventSubscriberFifo() *gencfg.SqsSubscriber {
	return obj._FreshdeskTicketDataEventSubscriberFifo
}
func (obj *Config) FreshdeskTicketDataEventSubscriber() *gencfg.SqsSubscriber {
	return obj._FreshdeskTicketDataEventSubscriber
}
func (obj *Config) CrmIssueTrackerIntegrationSubscriber() *gencfg.SqsSubscriber {
	return obj._CrmIssueTrackerIntegrationSubscriber
}
func (obj *Config) Dispute() *Dispute {
	return obj._Dispute
}
func (obj *Config) AppLog() *AppLog {
	return obj._AppLog
}
func (obj *Config) Payout() *Payout {
	return obj._Payout
}
func (obj *Config) PayoutStatusCheckSubscriber() *gencfg.SqsSubscriber {
	return obj._PayoutStatusCheckSubscriber
}
func (obj *Config) WaitlistSubscriber() *gencfg.SqsSubscriber {
	return obj._WaitlistSubscriber
}
func (obj *Config) BulkUserInfoViaEmailConfig() *BulkUserInfoViaEmailConfig {
	return obj._BulkUserInfoViaEmailConfig
}
func (obj *Config) UpiDisputeAutoUpdateEventSubscriber() *gencfg.SqsSubscriber {
	return obj._UpiDisputeAutoUpdateEventSubscriber
}
func (obj *Config) UpdateTicketSubscriber() *gencfg.SqsSubscriber {
	return obj._UpdateTicketSubscriber
}
func (obj *Config) CreateTicketSubscriber() *gencfg.SqsSubscriber {
	return obj._CreateTicketSubscriber
}
func (obj *Config) TicketConfig() *TicketConfig {
	return obj._TicketConfig
}
func (obj *Config) CallConfig() *CallConfig {
	return obj._CallConfig
}
func (obj *Config) OzonetelCallEventSubscriber() *gencfg.SqsSubscriber {
	return obj._OzonetelCallEventSubscriber
}
func (obj *Config) CallRoutingConfig() *CallRoutingConfig {
	return obj._CallRoutingConfig
}
func (obj *Config) FeatureReleaseConfig() *genconfig.FeatureReleaseConfig {
	return obj._FeatureReleaseConfig
}
func (obj *Config) IssueResolutionFeedbackConfig() *IssueResolutionFeedbackConfig {
	return obj._IssueResolutionFeedbackConfig
}
func (obj *Config) ChatBotConfig() *ChatBotConfig {
	return obj._ChatBotConfig
}
func (obj *Config) FreshchatEventSubscriber() *gencfg.SqsSubscriber {
	return obj._FreshchatEventSubscriber
}
func (obj *Config) RiskConfig() *RiskConfig {
	return obj._RiskConfig
}
func (obj *Config) InternationalFundTransfer() *InternationalFundTransfer {
	return obj._InternationalFundTransfer
}
func (obj *Config) SalaryOpsConfig() *SalaryOpsConfig {
	return obj._SalaryOpsConfig
}
func (obj *Config) LandingPageConfig() *LandingPageConfig {
	return obj._LandingPageConfig
}
func (obj *Config) TicketReconciliationEventSubscriber() *gencfg.SqsSubscriber {
	return obj._TicketReconciliationEventSubscriber
}
func (obj *Config) DevActionHelperConfig() *DevActionHelperConfig {
	return obj._DevActionHelperConfig
}
func (obj *Config) OverrideBankActions() *OverrideBankActions {
	return obj._OverrideBankActions
}
func (obj *Config) ReviewAction() *ReviewActionConfig {
	return obj._ReviewAction
}
func (obj *Config) VendorAccountPennyDropViaEmailConfig() *VendorAccountPennyDropViaEmailConfig {
	return obj._VendorAccountPennyDropViaEmailConfig
}
func (obj *Config) EmployerDbConfig() *EmployerDbConfig {
	return obj._EmployerDbConfig
}
func (obj *Config) GRPCWebServerConfig() *GRPCWebServerConfig {
	return obj._GRPCWebServerConfig
}
func (obj *Config) SherlockBannersConfig() *SherlockBannersConfig {
	return obj._SherlockBannersConfig
}
func (obj *Config) RudderEventKafkaConsumerGroup() *gencfg.KafkaConsumerGroup {
	return obj._RudderEventKafkaConsumerGroup
}
func (obj *Config) ErrorActivityConfig() *ErrorActivityConfig {
	return obj._ErrorActivityConfig
}
func (obj *Config) AgentPromptConfig() *AgentPromptConfig {
	return obj._AgentPromptConfig
}
func (obj *Config) RiskOpsInstalledAppsConfig() *RiskOpsInstalledAppsConfig {
	return obj._RiskOpsInstalledAppsConfig
}
func (obj *Config) RiskFennelConfig() *RiskFennelConfig {
	return obj._RiskFennelConfig
}
func (obj *Config) StageWiseCommsConfig() *StageWiseCommsConfig {
	return obj._StageWiseCommsConfig
}
func (obj *Config) IssueConfigServiceConfig() *IssueConfigServiceConfig {
	return obj._IssueConfigServiceConfig
}
func (obj *Config) S3EventSubscriber() *gencfg.SqsSubscriber {
	return obj._S3EventSubscriber
}
func (obj *Config) S3EventConsumerConfig() *S3EventConsumerConfig {
	return obj._S3EventConsumerConfig
}
func (obj *Config) RiskTxnReviewRolloutConfig() *RiskTxnReviewRolloutConfig {
	return obj._RiskTxnReviewRolloutConfig
}
func (obj *Config) RiskOutcallFormRolloutConfig() *RiskOutcallFormRolloutConfig {
	return obj._RiskOutcallFormRolloutConfig
}
func (obj *Config) CallIvrConfig() *CallIvrConfig {
	return obj._CallIvrConfig
}
func (obj *Config) CaseManagementActorActivities() *CaseManagementActorActivities {
	return obj._CaseManagementActorActivities
}
func (obj *Config) WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail() *WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail {
	return obj._WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail
}
func (obj *Config) ContactUsModelResponseConfig() *ContactUsModelResponseConfig {
	return obj._ContactUsModelResponseConfig
}
func (obj *Config) S3BucketNameForFileGenerator() *Filegenerator {
	return obj._S3BucketNameForFileGenerator
}
func (obj *Config) DbStateConfig() *DbStateConfig {
	return obj._DbStateConfig
}
func (obj *Config) EscalationConfig() *EscalationConfig {
	return obj._EscalationConfig
}
func (obj *Config) FederalEscalationUpdateEventSubscriber() *gencfg.SqsSubscriber {
	return obj._FederalEscalationUpdateEventSubscriber
}
func (obj *Config) FederalEscalationCreationEventSubscriber() *gencfg.SqsSubscriber {
	return obj._FederalEscalationCreationEventSubscriber
}
func (obj *Config) FederalEscalationConfig() *FederalEscalationConfig {
	return obj._FederalEscalationConfig
}
func (obj *Config) SaClosureEligibilityConf() *SaClosureEligibilityConf {
	return obj._SaClosureEligibilityConf
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) Logging() *cfg.Logging {
	return obj._Logging
}
func (obj *Config) EpifiDb() *cfg.DB {
	return obj._EpifiDb
}
func (obj *Config) Aws() *config.Aws {
	return obj._Aws
}
func (obj *Config) Cognito() *config.Cognito {
	return obj._Cognito
}
func (obj *Config) AuthFactorRetryLimit() *config.AuthFactorRetryLimit {
	return obj._AuthFactorRetryLimit
}
func (obj *Config) FreshdeskTicketPublisher() *cfg.SqsPublisher {
	return obj._FreshdeskTicketPublisher
}
func (obj *Config) FreshdeskContactPublisher() *cfg.SqsPublisher {
	return obj._FreshdeskContactPublisher
}
func (obj *Config) WatsonIncidentReportingPublisher() *cfg.SqsPublisher {
	return obj._WatsonIncidentReportingPublisher
}
func (obj *Config) WatsonIncidentResolutionPublisher() *cfg.SqsPublisher {
	return obj._WatsonIncidentResolutionPublisher
}
func (obj *Config) WatsonTicketEventPublisher() *cfg.SqsPublisher {
	return obj._WatsonTicketEventPublisher
}
func (obj *Config) DisputePublisher() *cfg.SqsPublisher {
	return obj._DisputePublisher
}
func (obj *Config) DisputeCreateTicketPublisher() *cfg.SqsPublisher {
	return obj._DisputeCreateTicketPublisher
}
func (obj *Config) DisputeUpdateTicketPublisher() *cfg.SqsPublisher {
	return obj._DisputeUpdateTicketPublisher
}
func (obj *Config) DisputeAddNoteTicketPublisher() *cfg.SqsPublisher {
	return obj._DisputeAddNoteTicketPublisher
}
func (obj *Config) DisputeExternalPublisher() *cfg.SqsPublisher {
	return obj._DisputeExternalPublisher
}
func (obj *Config) RMSEventPublisher() *cfg.SqsPublisher {
	return obj._RMSEventPublisher
}
func (obj *Config) RewardsManualGiveawayEventPublisher() *cfg.SqsPublisher {
	return obj._RewardsManualGiveawayEventPublisher
}
func (obj *Config) DevActionPublisher() *cfg.SqsPublisher {
	return obj._DevActionPublisher
}
func (obj *Config) UpdateTicketPublisher() *cfg.SqsPublisher {
	return obj._UpdateTicketPublisher
}
func (obj *Config) CreateTicketPublisher() *cfg.SqsPublisher {
	return obj._CreateTicketPublisher
}
func (obj *Config) CelestialSignalWorkflowPublisher() *cfg.SqsPublisher {
	return obj._CelestialSignalWorkflowPublisher
}
func (obj *Config) IFTFileProcessorEventPublisher() *cfg.SqsPublisher {
	return obj._IFTFileProcessorEventPublisher
}
func (obj *Config) RiskCasePublisher() *cfg.SqsPublisher {
	return obj._RiskCasePublisher
}
func (obj *Config) RiskDisputePublisher() *cfg.SqsPublisher {
	return obj._RiskDisputePublisher
}
func (obj *Config) CrmIssueTrackerIntegrationPublisher() *cfg.SqsPublisher {
	return obj._CrmIssueTrackerIntegrationPublisher
}
func (obj *Config) AuditLog() *config.AuditLog {
	return obj._AuditLog
}
func (obj *Config) Transaction() *config.Transaction {
	return obj._Transaction
}
func (obj *Config) Sherlock() *config.Sherlock {
	return obj._Sherlock
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) RedisOptions() *cfg.RedisOptions {
	return obj._RedisOptions
}
func (obj *Config) Flags() *config.Flags {
	return obj._Flags
}
func (obj *Config) Comms() *config.Comms {
	return obj._Comms
}
func (obj *Config) PayoutStatusCheckPublisher() *cfg.SqsPublisher {
	return obj._PayoutStatusCheckPublisher
}
func (obj *Config) DisputeNotificationTemplates() map[string]map[string]map[string]map[string]map[string]*config.DisputeNotificationContent {
	return obj._DisputeNotificationTemplates
}
func (obj *Config) OnboardingStageDetailsMapping() map[string]*config.OnboardingStageDetails {
	return obj._OnboardingStageDetailsMapping
}
func (obj *Config) AppLogsNotificationContent() *config.AppLogsNotificationContent {
	return obj._AppLogsNotificationContent
}
func (obj *Config) KYCConfig() *config.KYCConfig {
	return obj._KYCConfig
}
func (obj *Config) RateLimit() *config.RateLimit {
	return obj._RateLimit
}
func (obj *Config) RlConfig() *cfg.RateLimitConfig {
	return obj._RlConfig
}
func (obj *Config) CallRecording() *config.CallRecording {
	return obj._CallRecording
}
func (obj *Config) RudderStack() *cfg.RudderStackBroker {
	return obj._RudderStack
}
func (obj *Config) CxEventAmountCategories() *[]config.CxEventAmountCategory {
	return obj._CxEventAmountCategories
}
func (obj *Config) OrderConfig() *config.OrderConfig {
	return obj._OrderConfig
}
func (obj *Config) ReferralConfig() *config.ReferralConfig {
	return obj._ReferralConfig
}
func (obj *Config) LivenessVideoConfig() *config.LivenessVideoConfig {
	return obj._LivenessVideoConfig
}
func (obj *Config) SupportTicketFreshdeskConfig() *cfg.Freshdesk {
	return obj._SupportTicketFreshdeskConfig
}
func (obj *Config) ProcessTicketJobConfig() *cfg.CxTicketAutoResolutionJobConfig {
	return obj._ProcessTicketJobConfig
}
func (obj *Config) CxS3Config() *config.CxS3Config {
	return obj._CxS3Config
}
func (obj *Config) RiskS3Config() *config.RiskS3Config {
	return obj._RiskS3Config
}
func (obj *Config) DataS3Config() *config.DataS3Config {
	return obj._DataS3Config
}
func (obj *Config) EpifiIconS3Config() *config.EpifiIconS3Config {
	return obj._EpifiIconS3Config
}
func (obj *Config) PriorityRoutingConfig() *config.PriorityRoutingConfig {
	return obj._PriorityRoutingConfig
}
func (obj *Config) FreshChatConfig() *config.FreshChatConfig {
	return obj._FreshChatConfig
}
func (obj *Config) AuthValidation() *config.AuthValidation {
	return obj._AuthValidation
}
func (obj *Config) BulkTicketJobConfig() *config.BulkTicketJobConfig {
	return obj._BulkTicketJobConfig
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Profiling() *cfg.Profiling {
	return obj._Profiling
}
func (obj *Config) UploadCreditMISToVendorPublisher() *cfg.SqsPublisher {
	return obj._UploadCreditMISToVendorPublisher
}
func (obj *Config) BulkAccValidationViaEmailConfig() *config.BulkAccValidationViaEmailConfig {
	return obj._BulkAccValidationViaEmailConfig
}
func (obj *Config) AccountFreezeStatusConfig() *config.AccountFreezeStatusConfig {
	return obj._AccountFreezeStatusConfig
}
func (obj *Config) SherlockFeedbackDetailsConfig() *config.SherlockFeedbackDetailsConfig {
	return obj._SherlockFeedbackDetailsConfig
}
func (obj *Config) SalaryProgramLeadManagementConfig() *config.SalaryProgramLeadManagementConfig {
	return obj._SalaryProgramLeadManagementConfig
}
func (obj *Config) WatsonConfig() *config.WatsonConfig {
	return obj._WatsonConfig
}
func (obj *Config) SprinklrConfig() *config.SprinklrConfig {
	return obj._SprinklrConfig
}
func (obj *Config) UsStocksOpsConfig() *config.UsStocksOpsConfig {
	return obj._UsStocksOpsConfig
}
func (obj *Config) ClosedAccountConfig() *config.ClosedAccountConfig {
	return obj._ClosedAccountConfig
}
func (obj *Config) MonorailConfig() *cfg.MonorailConfig {
	return obj._MonorailConfig
}
func (obj *Config) AirflowConfig() *cfg.AirflowConfig {
	return obj._AirflowConfig
}
func (obj *Config) FreshdeskMonorailIntegrationConfig() *config.FreshdeskMonorailIntegrationConfig {
	return obj._FreshdeskMonorailIntegrationConfig
}
func (obj *Config) EmailValidationRegex() string {
	return obj._EmailValidationRegex
}
func (obj *Config) SherlockUserRequestsConfig() *config.SherlockUserRequestsConfig {
	return obj._SherlockUserRequestsConfig
}
func (obj *Config) IssueCategoryIdForCategory() *config.IssueCategoryIdForCategory {
	return obj._IssueCategoryIdForCategory
}
func (obj *Config) RewardsOrderUpdateEventQueuePublisher() *cfg.SqsPublisher {
	return obj._RewardsOrderUpdateEventQueuePublisher
}
func (obj *Config) RewardsCreditCardTxnEventQueuePublisher() *cfg.SqsPublisher {
	return obj._RewardsCreditCardTxnEventQueuePublisher
}
func (obj *Config) CallRoutingEventPublisher() *cfg.SnsPublisher {
	return obj._CallRoutingEventPublisher
}
func (obj *Config) TicketUpdateEventPublisher() *cfg.SnsPublisher {
	return obj._TicketUpdateEventPublisher
}
func (obj *Config) CasperItcDownloadFileQueuePublisher() *cfg.SqsPublisher {
	return obj._CasperItcDownloadFileQueuePublisher
}
func (obj *Config) InternationalFundsTransferConfig() *config.InternationalFundsTransferConfig {
	return obj._InternationalFundsTransferConfig
}
func (obj *Config) StrapiConfig() *cfg.StrapiConfig {
	return obj._StrapiConfig
}
func (obj *Config) OrderUpdateEventForTxnCategorizationPublisher() *cfg.SqsPublisher {
	return obj._OrderUpdateEventForTxnCategorizationPublisher
}
func (obj *Config) AATxnCategorizationPublisher() *cfg.SqsPublisher {
	return obj._AATxnCategorizationPublisher
}
func (obj *Config) CCTxnCategorizationPublisher() *cfg.SqsPublisher {
	return obj._CCTxnCategorizationPublisher
}
func (obj *Config) CreateTicketEventPublisher() *cfg.SnsPublisher {
	return obj._CreateTicketEventPublisher
}
func (obj *Config) FederalEscalationCreateEventPublisher() *cfg.SqsPublisher {
	return obj._FederalEscalationCreateEventPublisher
}
func (obj *Config) LienConfig() *config.LienConfig {
	return obj._LienConfig
}

type EmailVerification struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_VerificationUrl      string
	_VerificationUrlMutex *sync.RWMutex
	_FromEmail            string
	_FromEmailMutex       *sync.RWMutex
	_FromEmailName        string
	_FromEmailNameMutex   *sync.RWMutex
}

func (obj *EmailVerification) VerificationUrl() string {
	obj._VerificationUrlMutex.RLock()
	defer obj._VerificationUrlMutex.RUnlock()
	return obj._VerificationUrl
}
func (obj *EmailVerification) FromEmail() string {
	obj._FromEmailMutex.RLock()
	defer obj._FromEmailMutex.RUnlock()
	return obj._FromEmail
}
func (obj *EmailVerification) FromEmailName() string {
	obj._FromEmailNameMutex.RLock()
	defer obj._FromEmailNameMutex.RUnlock()
	return obj._FromEmailName
}

type MobilePromptVerification struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Validity               int32
	_NotificationTitle      string
	_NotificationTitleMutex *sync.RWMutex
	_NotificationBody       string
	_NotificationBodyMutex  *sync.RWMutex
}

func (obj *MobilePromptVerification) Validity() int64 {
	return int64(atomic.LoadInt32(&obj._Validity))
}
func (obj *MobilePromptVerification) NotificationTitle() string {
	obj._NotificationTitleMutex.RLock()
	defer obj._NotificationTitleMutex.RUnlock()
	return obj._NotificationTitle
}
func (obj *MobilePromptVerification) NotificationBody() string {
	obj._NotificationBodyMutex.RLock()
	defer obj._NotificationBodyMutex.RUnlock()
	return obj._NotificationBody
}

type CustomerAuth struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsAgentCachingEnabled      uint32
	_IsInAppNotificationEnabled uint32
	// IsSkippingAuthEnabledAfterExternalAuth flag decides whether we will skip manual auth that is done by agent,
	// after any externally triggered auth is successful like IVR based auth
	_IsSkippingAuthEnabledAfterExternalAuth uint32
	_AuthFactorCacheValidityDuration        int64
	// priority of auth factors as per business logic
	// This is needed since client always needs the list returned in sorted order
	_AuthFactorPriorityByPlatform *syncmap.Map[string, *AuthFactorPriorityByPlatform]
	// AuthFactorCacheKey is the key used store the auth factor for an actor in cache that will be used later
	// to create actual auth factor in db, when agent will try to access the ticket.
	// this is needed because at the time of creation of authId we need agent email
	// but in case of auth that is triggered externally to customer authentication service, for example
	// in app auth or IVR based auth, we don't have agent email available
	_AuthFactorCacheKey           string
	_AuthFactorCacheKeyMutex      *sync.RWMutex
	_InAppNotificationTemplate    *InAppNotificationTemplate
	_AuthValidityDuration         string
	_EmailValidityDuration        string
	_AgentCacheValidityDuration   time.Duration
	_MobilePromptValidityDuration string
	_MaxResetCount                int
}

func (obj *CustomerAuth) IsAgentCachingEnabled() bool {
	if atomic.LoadUint32(&obj._IsAgentCachingEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CustomerAuth) IsInAppNotificationEnabled() bool {
	if atomic.LoadUint32(&obj._IsInAppNotificationEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// IsSkippingAuthEnabledAfterExternalAuth flag decides whether we will skip manual auth that is done by agent,
// after any externally triggered auth is successful like IVR based auth
func (obj *CustomerAuth) IsSkippingAuthEnabledAfterExternalAuth() bool {
	if atomic.LoadUint32(&obj._IsSkippingAuthEnabledAfterExternalAuth) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CustomerAuth) AuthFactorCacheValidityDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._AuthFactorCacheValidityDuration))
}

// priority of auth factors as per business logic
// This is needed since client always needs the list returned in sorted order
func (obj *CustomerAuth) AuthFactorPriorityByPlatform() *syncmap.Map[string, *AuthFactorPriorityByPlatform] {
	return obj._AuthFactorPriorityByPlatform
}

// AuthFactorCacheKey is the key used store the auth factor for an actor in cache that will be used later
// to create actual auth factor in db, when agent will try to access the ticket.
// this is needed because at the time of creation of authId we need agent email
// but in case of auth that is triggered externally to customer authentication service, for example
// in app auth or IVR based auth, we don't have agent email available
func (obj *CustomerAuth) AuthFactorCacheKey() string {
	obj._AuthFactorCacheKeyMutex.RLock()
	defer obj._AuthFactorCacheKeyMutex.RUnlock()
	return obj._AuthFactorCacheKey
}
func (obj *CustomerAuth) InAppNotificationTemplate() *InAppNotificationTemplate {
	return obj._InAppNotificationTemplate
}
func (obj *CustomerAuth) AuthValidityDuration() string {
	return obj._AuthValidityDuration
}
func (obj *CustomerAuth) EmailValidityDuration() string {
	return obj._EmailValidityDuration
}
func (obj *CustomerAuth) AgentCacheValidityDuration() time.Duration {
	return obj._AgentCacheValidityDuration
}
func (obj *CustomerAuth) MobilePromptValidityDuration() string {
	return obj._MobilePromptValidityDuration
}
func (obj *CustomerAuth) MaxResetCount() int {
	return obj._MaxResetCount
}

type AuthFactorPriorityByPlatform struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MinAppVersion uint32
	_PriorityMap   *syncmap.Map[string, int32]
}

func (obj *AuthFactorPriorityByPlatform) MinAppVersion() uint32 {
	return uint32(atomic.LoadUint32(&obj._MinAppVersion))
}
func (obj *AuthFactorPriorityByPlatform) PriorityMap() *syncmap.Map[string, int32] {
	return obj._PriorityMap
}

type InAppNotificationTemplate struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// corresponds to notification time stamp color for fcm common template
	_NotificationTimestampFontColor      string
	_NotificationTimestampFontColorMutex *sync.RWMutex
	// corresponds to icon url values to be required in icon attributes
	_IconUrl      string
	_IconUrlMutex *sync.RWMutex
	// corresponds to icon url values to be required in icon bg color
	_IconBGColor      string
	_IconBGColorMutex *sync.RWMutex
	// corresponds to icon url values to be required in icon font color
	_TitleFontColor      string
	_TitleFontColorMutex *sync.RWMutex
	// corresponds to background color for fcm common template
	_BGColor      string
	_BGColorMutex *sync.RWMutex
	// corresponds to notification dismiss icon bg color for fcm common template
	_NotificationDismissIconBgColor      string
	_NotificationDismissIconBgColorMutex *sync.RWMutex
	// corresponds to notification dismiss icon color for fcm common template
	_NotificationDismissIconColor      string
	_NotificationDismissIconColorMutex *sync.RWMutex
	// corresponds to notification shadow icon color for fcm common template
	_NotificationShadowColor      string
	_NotificationShadowColorMutex *sync.RWMutex
	// corresponds to mobile prompt title for PNs
	_MobilePromptTitlePN      string
	_MobilePromptTitlePNMutex *sync.RWMutex
	// corresponds to mobile prompt title for InApp notification
	_MobilePromptTitleInApp      string
	_MobilePromptTitleInAppMutex *sync.RWMutex
	// corresponds to mobile prompt body for InApp notification
	_MobilePromptBody      string
	_MobilePromptBodyMutex *sync.RWMutex
}

// corresponds to notification time stamp color for fcm common template
func (obj *InAppNotificationTemplate) NotificationTimestampFontColor() string {
	obj._NotificationTimestampFontColorMutex.RLock()
	defer obj._NotificationTimestampFontColorMutex.RUnlock()
	return obj._NotificationTimestampFontColor
}

// corresponds to icon url values to be required in icon attributes
func (obj *InAppNotificationTemplate) IconUrl() string {
	obj._IconUrlMutex.RLock()
	defer obj._IconUrlMutex.RUnlock()
	return obj._IconUrl
}

// corresponds to icon url values to be required in icon bg color
func (obj *InAppNotificationTemplate) IconBGColor() string {
	obj._IconBGColorMutex.RLock()
	defer obj._IconBGColorMutex.RUnlock()
	return obj._IconBGColor
}

// corresponds to icon url values to be required in icon font color
func (obj *InAppNotificationTemplate) TitleFontColor() string {
	obj._TitleFontColorMutex.RLock()
	defer obj._TitleFontColorMutex.RUnlock()
	return obj._TitleFontColor
}

// corresponds to background color for fcm common template
func (obj *InAppNotificationTemplate) BGColor() string {
	obj._BGColorMutex.RLock()
	defer obj._BGColorMutex.RUnlock()
	return obj._BGColor
}

// corresponds to notification dismiss icon bg color for fcm common template
func (obj *InAppNotificationTemplate) NotificationDismissIconBgColor() string {
	obj._NotificationDismissIconBgColorMutex.RLock()
	defer obj._NotificationDismissIconBgColorMutex.RUnlock()
	return obj._NotificationDismissIconBgColor
}

// corresponds to notification dismiss icon color for fcm common template
func (obj *InAppNotificationTemplate) NotificationDismissIconColor() string {
	obj._NotificationDismissIconColorMutex.RLock()
	defer obj._NotificationDismissIconColorMutex.RUnlock()
	return obj._NotificationDismissIconColor
}

// corresponds to notification shadow icon color for fcm common template
func (obj *InAppNotificationTemplate) NotificationShadowColor() string {
	obj._NotificationShadowColorMutex.RLock()
	defer obj._NotificationShadowColorMutex.RUnlock()
	return obj._NotificationShadowColor
}

// corresponds to mobile prompt title for PNs
func (obj *InAppNotificationTemplate) MobilePromptTitlePN() string {
	obj._MobilePromptTitlePNMutex.RLock()
	defer obj._MobilePromptTitlePNMutex.RUnlock()
	return obj._MobilePromptTitlePN
}

// corresponds to mobile prompt title for InApp notification
func (obj *InAppNotificationTemplate) MobilePromptTitleInApp() string {
	obj._MobilePromptTitleInAppMutex.RLock()
	defer obj._MobilePromptTitleInAppMutex.RUnlock()
	return obj._MobilePromptTitleInApp
}

// corresponds to mobile prompt body for InApp notification
func (obj *InAppNotificationTemplate) MobilePromptBody() string {
	obj._MobilePromptBodyMutex.RLock()
	defer obj._MobilePromptBodyMutex.RUnlock()
	return obj._MobilePromptBody
}

type Dispute struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsIssueResolutionFeedbackCommsEnabledForDispute uint32
	// if this flag is set to true, we will use V2 impl for GetNextQuestionForApp RPC
	_IsGetNextQuestionsForAppV2Enabled uint32
	// if this flag is set to true, we will use V2 impl for GetNextQuestion RPC
	_IsGetNextQuestionsV2Enabled uint32
	// if this flag is set to true
	// we will evaluate if given UPI txn belongs to external provenance before querying dispute config table
	_IsUpiExternalProvenanceEvaluationEnabled uint32
	// TTL for idempotency in dispute request
	_DisputeIdempotencyTTL                                     string
	_DisputeIdempotencyTTLMutex                                *sync.RWMutex
	_DisputeJobConfig                                          *DisputeJobConfig
	_GeneratedCsvFolder                                        string
	_S3BucketName                                              string
	_MaxThresholdDurationForEscalation                         string
	_MaxAttemptCountForReverseProcessing                       int
	_DisputeUDIRConfig                                         *config.DisputeUDIRConfig
	_UPIReceiverTypeQuestionCode                               string
	_DefaultDisputeConfigVersion                               string
	_IsRestrictedReleaseEnabledForConfigVersion                bool
	_DisputeConfigVersionToSherlockReleaseConfigMap            map[string]*config.ControlledReleaseConfig
	_DisputeConfigVersionAndPlatformToReleaseConfMap           map[string]map[string]*config.ControlledReleaseConfig
	_ConfigVersionToDisputeConfigMap                           map[string]*config.DisputeVersionSpecificConfig
	_DisputeAppCopies                                          *config.DisputeAppCopies
	_MaxPageSize                                               int32
	_MaximumDaysDuration                                       time.Duration
	_ConfigVersionValidityToPreviousAttemptStatusEvaluationMap map[string]bool
	_MinThresholdDurationForStatusCheck                        string
	_TicketAlreadyExistsErrMsg                                 string
	_DisputeDocumentLinkTagForSherlock                         string
	_IssueCategoryEligibilityForIssueResolutionFeedback        map[string]bool
	_DmpEmailConfig                                            *config.DmpEmailConfig
	_DMPRaiseDisputeWindowConfig                               *config.DMPRaiseDisputeWindowConfig
}

func (obj *Dispute) IsIssueResolutionFeedbackCommsEnabledForDispute() bool {
	if atomic.LoadUint32(&obj._IsIssueResolutionFeedbackCommsEnabledForDispute) == 0 {
		return false
	} else {
		return true
	}
}

// if this flag is set to true, we will use V2 impl for GetNextQuestionForApp RPC
func (obj *Dispute) IsGetNextQuestionsForAppV2Enabled() bool {
	if atomic.LoadUint32(&obj._IsGetNextQuestionsForAppV2Enabled) == 0 {
		return false
	} else {
		return true
	}
}

// if this flag is set to true, we will use V2 impl for GetNextQuestion RPC
func (obj *Dispute) IsGetNextQuestionsV2Enabled() bool {
	if atomic.LoadUint32(&obj._IsGetNextQuestionsV2Enabled) == 0 {
		return false
	} else {
		return true
	}
}

// if this flag is set to true
// we will evaluate if given UPI txn belongs to external provenance before querying dispute config table
func (obj *Dispute) IsUpiExternalProvenanceEvaluationEnabled() bool {
	if atomic.LoadUint32(&obj._IsUpiExternalProvenanceEvaluationEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// TTL for idempotency in dispute request
func (obj *Dispute) DisputeIdempotencyTTL() string {
	obj._DisputeIdempotencyTTLMutex.RLock()
	defer obj._DisputeIdempotencyTTLMutex.RUnlock()
	return obj._DisputeIdempotencyTTL
}
func (obj *Dispute) DisputeJobConfig() *DisputeJobConfig {
	return obj._DisputeJobConfig
}
func (obj *Dispute) GeneratedCsvFolder() string {
	return obj._GeneratedCsvFolder
}
func (obj *Dispute) S3BucketName() string {
	return obj._S3BucketName
}
func (obj *Dispute) MaxThresholdDurationForEscalation() string {
	return obj._MaxThresholdDurationForEscalation
}
func (obj *Dispute) MaxAttemptCountForReverseProcessing() int {
	return obj._MaxAttemptCountForReverseProcessing
}
func (obj *Dispute) DisputeUDIRConfig() *config.DisputeUDIRConfig {
	return obj._DisputeUDIRConfig
}
func (obj *Dispute) UPIReceiverTypeQuestionCode() string {
	return obj._UPIReceiverTypeQuestionCode
}
func (obj *Dispute) DefaultDisputeConfigVersion() string {
	return obj._DefaultDisputeConfigVersion
}
func (obj *Dispute) IsRestrictedReleaseEnabledForConfigVersion() bool {
	return obj._IsRestrictedReleaseEnabledForConfigVersion
}
func (obj *Dispute) DisputeConfigVersionToSherlockReleaseConfigMap() map[string]*config.ControlledReleaseConfig {
	return obj._DisputeConfigVersionToSherlockReleaseConfigMap
}
func (obj *Dispute) DisputeConfigVersionAndPlatformToReleaseConfMap() map[string]map[string]*config.ControlledReleaseConfig {
	return obj._DisputeConfigVersionAndPlatformToReleaseConfMap
}
func (obj *Dispute) ConfigVersionToDisputeConfigMap() map[string]*config.DisputeVersionSpecificConfig {
	return obj._ConfigVersionToDisputeConfigMap
}
func (obj *Dispute) DisputeAppCopies() *config.DisputeAppCopies {
	return obj._DisputeAppCopies
}
func (obj *Dispute) MaxPageSize() int32 {
	return obj._MaxPageSize
}
func (obj *Dispute) MaximumDaysDuration() time.Duration {
	return obj._MaximumDaysDuration
}
func (obj *Dispute) ConfigVersionValidityToPreviousAttemptStatusEvaluationMap() map[string]bool {
	return obj._ConfigVersionValidityToPreviousAttemptStatusEvaluationMap
}
func (obj *Dispute) MinThresholdDurationForStatusCheck() string {
	return obj._MinThresholdDurationForStatusCheck
}
func (obj *Dispute) TicketAlreadyExistsErrMsg() string {
	return obj._TicketAlreadyExistsErrMsg
}
func (obj *Dispute) DisputeDocumentLinkTagForSherlock() string {
	return obj._DisputeDocumentLinkTagForSherlock
}
func (obj *Dispute) IssueCategoryEligibilityForIssueResolutionFeedback() map[string]bool {
	return obj._IssueCategoryEligibilityForIssueResolutionFeedback
}
func (obj *Dispute) DmpEmailConfig() *config.DmpEmailConfig {
	return obj._DmpEmailConfig
}
func (obj *Dispute) DMPRaiseDisputeWindowConfig() *config.DMPRaiseDisputeWindowConfig {
	return obj._DMPRaiseDisputeWindowConfig
}

type DisputeJobConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// delay time between processing 2 dispute tickets to avoid hitting freshdesk rate limits
	_ReverseProcessingDelayDurationBetweenTickets      string
	_ReverseProcessingDelayDurationBetweenTicketsMutex *sync.RWMutex
	// max time period after which job should time out and stop processing disputes
	_ReverseProcessingJobTimeout      string
	_ReverseProcessingJobTimeoutMutex *sync.RWMutex
	_EscalationJobTimeout             string
}

// delay time between processing 2 dispute tickets to avoid hitting freshdesk rate limits
func (obj *DisputeJobConfig) ReverseProcessingDelayDurationBetweenTickets() string {
	obj._ReverseProcessingDelayDurationBetweenTicketsMutex.RLock()
	defer obj._ReverseProcessingDelayDurationBetweenTicketsMutex.RUnlock()
	return obj._ReverseProcessingDelayDurationBetweenTickets
}

// max time period after which job should time out and stop processing disputes
func (obj *DisputeJobConfig) ReverseProcessingJobTimeout() string {
	obj._ReverseProcessingJobTimeoutMutex.RLock()
	defer obj._ReverseProcessingJobTimeoutMutex.RUnlock()
	return obj._ReverseProcessingJobTimeout
}
func (obj *DisputeJobConfig) EscalationJobTimeout() string {
	return obj._EscalationJobTimeout
}

type AppLog struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_LogTTL             string
	_LogTTLMutex        *sync.RWMutex
	_MaxLogCountPerUser int64
	_LogChunkSize       int
}

func (obj *AppLog) LogTTL() string {
	obj._LogTTLMutex.RLock()
	defer obj._LogTTLMutex.RUnlock()
	return obj._LogTTL
}
func (obj *AppLog) MaxLogCountPerUser() int64 {
	return obj._MaxLogCountPerUser
}
func (obj *AppLog) LogChunkSize() int {
	return obj._LogChunkSize
}

type Payout struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_CashPayout                         *CashPayout
	_FiCoinsPayout                      *FiCoinsPayout
	_StatusCheckDelay                   string
	_MaxNumberOfPayoutsAllowedPerTicket uint64
}

func (obj *Payout) CashPayout() *CashPayout {
	return obj._CashPayout
}
func (obj *Payout) FiCoinsPayout() *FiCoinsPayout {
	return obj._FiCoinsPayout
}
func (obj *Payout) StatusCheckDelay() string {
	return obj._StatusCheckDelay
}
func (obj *Payout) MaxNumberOfPayoutsAllowedPerTicket() uint64 {
	return obj._MaxNumberOfPayoutsAllowedPerTicket
}

type CashPayout struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_EnableB2CTransactionViaCelestial uint32
	_MaxPayoutValueAllowedPerTicket   uint64
	_MinPayoutValueAllowedPerTicket   uint64
	_SingleAmountLimitForApproval     int64
	_MaxPayoutAllowedInTimeframe      int64
	_TimeframeDuration                string
	_FromActorIdForOrder              string
	_FromPiIdForOrder                 string
}

func (obj *CashPayout) EnableB2CTransactionViaCelestial() bool {
	if atomic.LoadUint32(&obj._EnableB2CTransactionViaCelestial) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CashPayout) MaxPayoutValueAllowedPerTicket() uint64 {
	return obj._MaxPayoutValueAllowedPerTicket
}
func (obj *CashPayout) MinPayoutValueAllowedPerTicket() uint64 {
	return obj._MinPayoutValueAllowedPerTicket
}
func (obj *CashPayout) SingleAmountLimitForApproval() int64 {
	return obj._SingleAmountLimitForApproval
}
func (obj *CashPayout) MaxPayoutAllowedInTimeframe() int64 {
	return obj._MaxPayoutAllowedInTimeframe
}
func (obj *CashPayout) TimeframeDuration() string {
	return obj._TimeframeDuration
}
func (obj *CashPayout) FromActorIdForOrder() string {
	return obj._FromActorIdForOrder
}
func (obj *CashPayout) FromPiIdForOrder() string {
	return obj._FromPiIdForOrder
}

type FiCoinsPayout struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsPayoutViaFiCoinsEnabled          uint32
	_MaxPayoutValueAllowedPerTicket     uint64
	_SingleAmountLimitForApproval       int64
	_MaxAmountDisbursalInDurationByRole map[string]map[string]config.PayoutAmountDisbursalConstraint
	_MaxAmountDisbursalPerUser          *config.PayoutAmountDisbursalConstraint
	_FiCoinsRewardOfferId               string
	_AllowedValues                      []uint64
}

func (obj *FiCoinsPayout) IsPayoutViaFiCoinsEnabled() bool {
	if atomic.LoadUint32(&obj._IsPayoutViaFiCoinsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *FiCoinsPayout) MaxPayoutValueAllowedPerTicket() uint64 {
	return obj._MaxPayoutValueAllowedPerTicket
}
func (obj *FiCoinsPayout) SingleAmountLimitForApproval() int64 {
	return obj._SingleAmountLimitForApproval
}
func (obj *FiCoinsPayout) MaxAmountDisbursalInDurationByRole() map[string]map[string]config.PayoutAmountDisbursalConstraint {
	return obj._MaxAmountDisbursalInDurationByRole
}
func (obj *FiCoinsPayout) MaxAmountDisbursalPerUser() *config.PayoutAmountDisbursalConstraint {
	return obj._MaxAmountDisbursalPerUser
}
func (obj *FiCoinsPayout) FiCoinsRewardOfferId() string {
	return obj._FiCoinsRewardOfferId
}
func (obj *FiCoinsPayout) AllowedValues() []uint64 {
	return obj._AllowedValues
}

type BulkUserInfoViaEmailConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MaxCountThreshold          int64
	_FromEmailId                string
	_FromEmailIdMutex           *sync.RWMutex
	_FromEmailName              string
	_FromEmailNameMutex         *sync.RWMutex
	_FieldToCsvColNameMap       map[string]string
	_RoleToAllowedRespFieldsMap map[string]map[string]bool
}

func (obj *BulkUserInfoViaEmailConfig) MaxCountThreshold() int {
	return int(atomic.LoadInt64(&obj._MaxCountThreshold))
}
func (obj *BulkUserInfoViaEmailConfig) FromEmailId() string {
	obj._FromEmailIdMutex.RLock()
	defer obj._FromEmailIdMutex.RUnlock()
	return obj._FromEmailId
}
func (obj *BulkUserInfoViaEmailConfig) FromEmailName() string {
	obj._FromEmailNameMutex.RLock()
	defer obj._FromEmailNameMutex.RUnlock()
	return obj._FromEmailName
}
func (obj *BulkUserInfoViaEmailConfig) FieldToCsvColNameMap() map[string]string {
	return obj._FieldToCsvColNameMap
}
func (obj *BulkUserInfoViaEmailConfig) RoleToAllowedRespFieldsMap() map[string]map[string]bool {
	return obj._RoleToAllowedRespFieldsMap
}

type TicketConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// IsTicketListLoggingEnabled is used to temporarily log ticket list in response of getSupportTicketsForSherlock rpc
	_IsTicketListLoggingEnabled uint32
	// IsTicketListLoggingEnabled denotes whether to log an identifier for all events received from Freshdesk
	_IsTicketEventLoggingEnabled uint32
	// IsDefaultTitleAndDescriptionEnabledForInAppTicket flag indicates if DefaultAppTicketTitle and DefaultAppTicketDescription will be
	// populated for in app ticket if corresponding title and description are not found in db
	_IsDefaultTitleAndDescriptionEnabledForInAppTicket uint32
	// feature flag to decide whether to publish ticket update event to SNS topic
	_IsTicketUpdateEventPublishingEnabled uint32
	_TicketFieldCacheValidityDuration     int64
	// user's latest ticket is shown in home page, hence caching it
	_LatestTicketCacheValidityDuration int64
	_DefaultAppTicketTitle             string
	_DefaultAppTicketTitleMutex        *sync.RWMutex
	_DefaultAppTicketDescription       string
	_DefaultAppTicketDescriptionMutex  *sync.RWMutex
	_ShowTicketsInAppConfig            *ShowTicketsInAppConfig
	_SLAConfig                         *SLAConfig
	_CsatConfig                        *CsatConfig
	_URL                               string
	_IsStatusResolvedMap               map[int64]bool
	_BulkResolutionModeValue           string
	_EscalationTeamEnumToValueMapping  map[string]string
	_ProductCategoryFieldId            int64
}

// IsTicketListLoggingEnabled is used to temporarily log ticket list in response of getSupportTicketsForSherlock rpc
func (obj *TicketConfig) IsTicketListLoggingEnabled() bool {
	if atomic.LoadUint32(&obj._IsTicketListLoggingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// IsTicketListLoggingEnabled denotes whether to log an identifier for all events received from Freshdesk
func (obj *TicketConfig) IsTicketEventLoggingEnabled() bool {
	if atomic.LoadUint32(&obj._IsTicketEventLoggingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// IsDefaultTitleAndDescriptionEnabledForInAppTicket flag indicates if DefaultAppTicketTitle and DefaultAppTicketDescription will be
// populated for in app ticket if corresponding title and description are not found in db
func (obj *TicketConfig) IsDefaultTitleAndDescriptionEnabledForInAppTicket() bool {
	if atomic.LoadUint32(&obj._IsDefaultTitleAndDescriptionEnabledForInAppTicket) == 0 {
		return false
	} else {
		return true
	}
}

// feature flag to decide whether to publish ticket update event to SNS topic
func (obj *TicketConfig) IsTicketUpdateEventPublishingEnabled() bool {
	if atomic.LoadUint32(&obj._IsTicketUpdateEventPublishingEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *TicketConfig) TicketFieldCacheValidityDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._TicketFieldCacheValidityDuration))
}

// user's latest ticket is shown in home page, hence caching it
func (obj *TicketConfig) LatestTicketCacheValidityDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._LatestTicketCacheValidityDuration))
}
func (obj *TicketConfig) DefaultAppTicketTitle() string {
	obj._DefaultAppTicketTitleMutex.RLock()
	defer obj._DefaultAppTicketTitleMutex.RUnlock()
	return obj._DefaultAppTicketTitle
}
func (obj *TicketConfig) DefaultAppTicketDescription() string {
	obj._DefaultAppTicketDescriptionMutex.RLock()
	defer obj._DefaultAppTicketDescriptionMutex.RUnlock()
	return obj._DefaultAppTicketDescription
}
func (obj *TicketConfig) ShowTicketsInAppConfig() *ShowTicketsInAppConfig {
	return obj._ShowTicketsInAppConfig
}
func (obj *TicketConfig) SLAConfig() *SLAConfig {
	return obj._SLAConfig
}
func (obj *TicketConfig) CsatConfig() *CsatConfig {
	return obj._CsatConfig
}
func (obj *TicketConfig) URL() string {
	return obj._URL
}
func (obj *TicketConfig) IsStatusResolvedMap() map[int64]bool {
	return obj._IsStatusResolvedMap
}
func (obj *TicketConfig) BulkResolutionModeValue() string {
	return obj._BulkResolutionModeValue
}
func (obj *TicketConfig) EscalationTeamEnumToValueMapping() map[string]string {
	return obj._EscalationTeamEnumToValueMapping
}
func (obj *TicketConfig) ProductCategoryFieldId() int64 {
	return obj._ProductCategoryFieldId
}

type ShowTicketsInAppConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_TimeLimitForUpdatingTicketDetails        int64
	_WhitelistedProductCategories             roarray.ROArray[string]
	_WhitelistedProductCategoriesMutex        *sync.RWMutex
	_MandatoryFieldsRequiredToShowTicket      roarray.ROArray[string]
	_MandatoryFieldsRequiredToShowTicketMutex *sync.RWMutex
	_CutOffDateToShowTickets                  time.Time
	_CutOffDateToShowTicketsMutex             *sync.RWMutex
	_CreatedByEnumToValueMapping              map[string]string
	_CreationModeEnumToValueMapping           map[string]string
}

func (obj *ShowTicketsInAppConfig) TimeLimitForUpdatingTicketDetails() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._TimeLimitForUpdatingTicketDetails))
}
func (obj *ShowTicketsInAppConfig) WhitelistedProductCategories() roarray.ROArray[string] {
	obj._WhitelistedProductCategoriesMutex.RLock()
	defer obj._WhitelistedProductCategoriesMutex.RUnlock()
	return obj._WhitelistedProductCategories
}
func (obj *ShowTicketsInAppConfig) MandatoryFieldsRequiredToShowTicket() roarray.ROArray[string] {
	obj._MandatoryFieldsRequiredToShowTicketMutex.RLock()
	defer obj._MandatoryFieldsRequiredToShowTicketMutex.RUnlock()
	return obj._MandatoryFieldsRequiredToShowTicket
}
func (obj *ShowTicketsInAppConfig) CutOffDateToShowTickets() time.Time {
	obj._CutOffDateToShowTicketsMutex.RLock()
	defer obj._CutOffDateToShowTicketsMutex.RUnlock()
	return obj._CutOffDateToShowTickets
}
func (obj *ShowTicketsInAppConfig) CreatedByEnumToValueMapping() map[string]string {
	return obj._CreatedByEnumToValueMapping
}
func (obj *ShowTicketsInAppConfig) CreationModeEnumToValueMapping() map[string]string {
	return obj._CreationModeEnumToValueMapping
}

type SLAConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// IsSLACalculationEnabledInTicketConsumer flag controls whether SLA calculation will happen in ticket consumer
	_IsSLACalculationEnabledInTicketConsumer uint32
	// IsExpectedResolutionByFieldDeterminedUsingSLA flag controls whether expected resolution time
	// would be populated in GetSupportTicketForApp RPC response
	_IsExpectedResolutionByFieldDeterminedUsingSLA uint32
	// IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk flag determines whether event will be published in common consumer
	// to update expected resolution time on freshdesk "expected resolution date" custom field
	_IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk uint32
}

// IsSLACalculationEnabledInTicketConsumer flag controls whether SLA calculation will happen in ticket consumer
func (obj *SLAConfig) IsSLACalculationEnabledInTicketConsumer() bool {
	if atomic.LoadUint32(&obj._IsSLACalculationEnabledInTicketConsumer) == 0 {
		return false
	} else {
		return true
	}
}

// IsExpectedResolutionByFieldDeterminedUsingSLA flag controls whether expected resolution time
// would be populated in GetSupportTicketForApp RPC response
func (obj *SLAConfig) IsExpectedResolutionByFieldDeterminedUsingSLA() bool {
	if atomic.LoadUint32(&obj._IsExpectedResolutionByFieldDeterminedUsingSLA) == 0 {
		return false
	} else {
		return true
	}
}

// IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk flag determines whether event will be published in common consumer
// to update expected resolution time on freshdesk "expected resolution date" custom field
func (obj *SLAConfig) IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk() bool {
	if atomic.LoadUint32(&obj._IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk) == 0 {
		return false
	} else {
		return true
	}
}

type CsatConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCsatCollectionEnabledViaWeb uint32
	_CsatEligibilityWindow         int64
	// AllowedTicketStatusesForCsat contains the list of status enums in string format
	// for which push notification will be triggered to get in app CSAT
	_AllowedTicketStatusesForCsat      roarray.ROArray[string]
	_AllowedTicketStatusesForCsatMutex *sync.RWMutex
	_AllowedCommsTypeForCsat           roarray.ROArray[string]
	_AllowedCommsTypeForCsatMutex      *sync.RWMutex
	// title of the push notification which is triggered for in app CSAT
	_PushNotificationTitle      string
	_PushNotificationTitleMutex *sync.RWMutex
	// description of the push notification which is triggered for in app CSAT
	_PushNotificationDescription      string
	_PushNotificationDescriptionMutex *sync.RWMutex
	_CommsExternalRefIdPrefix         string
	_CommsExternalRefIdPrefixMutex    *sync.RWMutex
	_WebFormUrl                       string
	_WebFormUrlMutex                  *sync.RWMutex
}

func (obj *CsatConfig) IsCsatCollectionEnabledViaWeb() bool {
	if atomic.LoadUint32(&obj._IsCsatCollectionEnabledViaWeb) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CsatConfig) CsatEligibilityWindow() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CsatEligibilityWindow))
}

// AllowedTicketStatusesForCsat contains the list of status enums in string format
// for which push notification will be triggered to get in app CSAT
func (obj *CsatConfig) AllowedTicketStatusesForCsat() roarray.ROArray[string] {
	obj._AllowedTicketStatusesForCsatMutex.RLock()
	defer obj._AllowedTicketStatusesForCsatMutex.RUnlock()
	return obj._AllowedTicketStatusesForCsat
}
func (obj *CsatConfig) AllowedCommsTypeForCsat() roarray.ROArray[string] {
	obj._AllowedCommsTypeForCsatMutex.RLock()
	defer obj._AllowedCommsTypeForCsatMutex.RUnlock()
	return obj._AllowedCommsTypeForCsat
}

// title of the push notification which is triggered for in app CSAT
func (obj *CsatConfig) PushNotificationTitle() string {
	obj._PushNotificationTitleMutex.RLock()
	defer obj._PushNotificationTitleMutex.RUnlock()
	return obj._PushNotificationTitle
}

// description of the push notification which is triggered for in app CSAT
func (obj *CsatConfig) PushNotificationDescription() string {
	obj._PushNotificationDescriptionMutex.RLock()
	defer obj._PushNotificationDescriptionMutex.RUnlock()
	return obj._PushNotificationDescription
}
func (obj *CsatConfig) CommsExternalRefIdPrefix() string {
	obj._CommsExternalRefIdPrefixMutex.RLock()
	defer obj._CommsExternalRefIdPrefixMutex.RUnlock()
	return obj._CommsExternalRefIdPrefix
}
func (obj *CsatConfig) WebFormUrl() string {
	obj._WebFormUrlMutex.RLock()
	defer obj._WebFormUrlMutex.RUnlock()
	return obj._WebFormUrl
}

type CallConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// if this flag is set to true, we will log events in consumer using info logs
	// this should be used only for debugging purpose to validate delta between raw ozonetel events and parsed events
	// this flag should be by default turned off
	_IsConsumerEventLoggingEnabled uint32
	// feature flag to represent whether the call blocking functionality is enabled
	// [deprecated] moved inside call blocker config
	_IsCallBlockerEnabled            uint32
	_IsCallBlockingEnabledViaIvrFlow uint32
	_AbandonedCallConfig             *AbandonedCallConfig
	_CallBlockerTestConfig           *CallBlockerTestConfig
	_CallBlockerConfig               *CallBlockerConfig
	_MaximumDaysDuration             time.Duration
	_MaxPageSize                     int64
	_CallStartTicketSubject          string
	_CallStartTicketDescription      string
	_UpdateTicketErrMsg              string
	_TicketAlreadyExistsErrMsg       string
	_AgentAssignmentErrMsg           string
	_TicketDbUpdateErrMsg            string
	_FreshdeskTicketCreationErr      string
	_CallEndTicketDescription        string
	_CallEndTicketPrivateNote        string
	_RecordingFilePath               string
	_RecordingFileNotFoundErrMsg     string
	_RecordingLinkTagForSherlock     string
}

// if this flag is set to true, we will log events in consumer using info logs
// this should be used only for debugging purpose to validate delta between raw ozonetel events and parsed events
// this flag should be by default turned off
func (obj *CallConfig) IsConsumerEventLoggingEnabled() bool {
	if atomic.LoadUint32(&obj._IsConsumerEventLoggingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// feature flag to represent whether the call blocking functionality is enabled
// [deprecated] moved inside call blocker config
func (obj *CallConfig) IsCallBlockerEnabled() bool {
	if atomic.LoadUint32(&obj._IsCallBlockerEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CallConfig) IsCallBlockingEnabledViaIvrFlow() bool {
	if atomic.LoadUint32(&obj._IsCallBlockingEnabledViaIvrFlow) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CallConfig) AbandonedCallConfig() *AbandonedCallConfig {
	return obj._AbandonedCallConfig
}
func (obj *CallConfig) CallBlockerTestConfig() *CallBlockerTestConfig {
	return obj._CallBlockerTestConfig
}
func (obj *CallConfig) CallBlockerConfig() *CallBlockerConfig {
	return obj._CallBlockerConfig
}
func (obj *CallConfig) MaximumDaysDuration() time.Duration {
	return obj._MaximumDaysDuration
}
func (obj *CallConfig) MaxPageSize() int64 {
	return obj._MaxPageSize
}
func (obj *CallConfig) CallStartTicketSubject() string {
	return obj._CallStartTicketSubject
}
func (obj *CallConfig) CallStartTicketDescription() string {
	return obj._CallStartTicketDescription
}
func (obj *CallConfig) UpdateTicketErrMsg() string {
	return obj._UpdateTicketErrMsg
}
func (obj *CallConfig) TicketAlreadyExistsErrMsg() string {
	return obj._TicketAlreadyExistsErrMsg
}
func (obj *CallConfig) AgentAssignmentErrMsg() string {
	return obj._AgentAssignmentErrMsg
}
func (obj *CallConfig) TicketDbUpdateErrMsg() string {
	return obj._TicketDbUpdateErrMsg
}
func (obj *CallConfig) FreshdeskTicketCreationErr() string {
	return obj._FreshdeskTicketCreationErr
}
func (obj *CallConfig) CallEndTicketDescription() string {
	return obj._CallEndTicketDescription
}
func (obj *CallConfig) CallEndTicketPrivateNote() string {
	return obj._CallEndTicketPrivateNote
}
func (obj *CallConfig) RecordingFilePath() string {
	return obj._RecordingFilePath
}
func (obj *CallConfig) RecordingFileNotFoundErrMsg() string {
	return obj._RecordingFileNotFoundErrMsg
}
func (obj *CallConfig) RecordingLinkTagForSherlock() string {
	return obj._RecordingLinkTagForSherlock
}

type AbandonedCallConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// flag to decide whether to sent comms to user if the call is abandoned
	_IsAbandonedCallCommsEnabled uint32
	_NotificationTemplate        *genpay.NotificationTemplateParams
}

// flag to decide whether to sent comms to user if the call is abandoned
func (obj *AbandonedCallConfig) IsAbandonedCallCommsEnabled() bool {
	if atomic.LoadUint32(&obj._IsAbandonedCallCommsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AbandonedCallConfig) NotificationTemplate() *genpay.NotificationTemplateParams {
	return obj._NotificationTemplate
}

type CallBlockerTestConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_UnRegisteredUser  uint32
	_AppAccessBlocked  uint32
	_UserReportedIssue uint32
	_IsCreditFreeze    uint32
	_IsStandardTier    uint32
	_IsRiskBlocked     uint32
}

func (obj *CallBlockerTestConfig) UnRegisteredUser() bool {
	if atomic.LoadUint32(&obj._UnRegisteredUser) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CallBlockerTestConfig) AppAccessBlocked() bool {
	if atomic.LoadUint32(&obj._AppAccessBlocked) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CallBlockerTestConfig) UserReportedIssue() bool {
	if atomic.LoadUint32(&obj._UserReportedIssue) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CallBlockerTestConfig) IsCreditFreeze() bool {
	if atomic.LoadUint32(&obj._IsCreditFreeze) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CallBlockerTestConfig) IsStandardTier() bool {
	if atomic.LoadUint32(&obj._IsStandardTier) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CallBlockerTestConfig) IsRiskBlocked() bool {
	if atomic.LoadUint32(&obj._IsRiskBlocked) == 0 {
		return false
	} else {
		return true
	}
}

type CallBlockerConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// feature flag to represent whether the call blocking functionality is enabled
	_IsCallBlockerEnabled            uint32
	_IsCallBlockingEnabledViaIvrFlow uint32
	_RequestTimeout                  int64
	_BlockTierList                   roarray.ROArray[string]
	_BlockTierListMutex              *sync.RWMutex
	// triaged tier means list of tier we want to allow but only if they meet certain criteria
	// (ex. user has an active loan)
	_TriagedTierList                   roarray.ROArray[string]
	_TriagedTierListMutex              *sync.RWMutex
	_CallDropOffNotificationTitle      string
	_CallDropOffNotificationTitleMutex *sync.RWMutex
	_CallDropOffNotificationBody       string
	_CallDropOffNotificationBodyMutex  *sync.RWMutex
	_ContactUsFlowSmsLink              string
	_ContactUsFlowSmsLinkMutex         *sync.RWMutex
	_FiAppDownloadLink                 string
	_FiAppDownloadLinkMutex            *sync.RWMutex
}

// feature flag to represent whether the call blocking functionality is enabled
func (obj *CallBlockerConfig) IsCallBlockerEnabled() bool {
	if atomic.LoadUint32(&obj._IsCallBlockerEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CallBlockerConfig) IsCallBlockingEnabledViaIvrFlow() bool {
	if atomic.LoadUint32(&obj._IsCallBlockingEnabledViaIvrFlow) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CallBlockerConfig) RequestTimeout() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._RequestTimeout))
}
func (obj *CallBlockerConfig) BlockTierList() roarray.ROArray[string] {
	obj._BlockTierListMutex.RLock()
	defer obj._BlockTierListMutex.RUnlock()
	return obj._BlockTierList
}

// triaged tier means list of tier we want to allow but only if they meet certain criteria
// (ex. user has an active loan)
func (obj *CallBlockerConfig) TriagedTierList() roarray.ROArray[string] {
	obj._TriagedTierListMutex.RLock()
	defer obj._TriagedTierListMutex.RUnlock()
	return obj._TriagedTierList
}
func (obj *CallBlockerConfig) CallDropOffNotificationTitle() string {
	obj._CallDropOffNotificationTitleMutex.RLock()
	defer obj._CallDropOffNotificationTitleMutex.RUnlock()
	return obj._CallDropOffNotificationTitle
}
func (obj *CallBlockerConfig) CallDropOffNotificationBody() string {
	obj._CallDropOffNotificationBodyMutex.RLock()
	defer obj._CallDropOffNotificationBodyMutex.RUnlock()
	return obj._CallDropOffNotificationBody
}
func (obj *CallBlockerConfig) ContactUsFlowSmsLink() string {
	obj._ContactUsFlowSmsLinkMutex.RLock()
	defer obj._ContactUsFlowSmsLinkMutex.RUnlock()
	return obj._ContactUsFlowSmsLink
}
func (obj *CallBlockerConfig) FiAppDownloadLink() string {
	obj._FiAppDownloadLinkMutex.RLock()
	defer obj._FiAppDownloadLinkMutex.RUnlock()
	return obj._FiAppDownloadLink
}

type CallRoutingConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// default priority for any user
	_DefaultPriorityValue int64
	// max priority value
	// 1 is the highest priority and 9 is the lowest priority
	// lower the priority value, higher the priority in the queue
	_MaxPriorityValue int64
	// minimum call drop off count threshold for a user in the queue
	_CallDropOffCountThreshold int64
	// factor by which priority value has to be halved for a user
	_HalvingFactor int64
	// number of past call records which has to be queried for a user
	_PastCallRecordsLookupSize int64
	// flag to enable or disable routing users based on manual user-routing channel mappings added via dev action
	_IsManualRoutingEnabled uint32
	// flag to enable or disable routing salary program users to a dedicated channel
	_IsSalaryProgramRoutingEnabled uint32
	// flag to enable or disable routing credit card users to a dedicated channel
	_IsCreditCardRoutingEnabled uint32
	// flag to enable or disable routing loan users to a dedicated channel
	_IsActiveLoanRoutingEnabled uint32
	// flag to enable or disable priority routing at global level
	_IsPriorityRoutingEnabled uint32
	// IsRecordedMessageEventEnabled indicates whether a event will be published to the topic
	// after we have decided to greet an actor with a recorded message
	// other services will use this event for triggering async flows at their end, ex: sending comms
	_IsRecordedMessageEventEnabled uint32
	// list of languages to show to the user to choose as a preference
	_CallLangPreferencesList      roarray.ROArray[string]
	_CallLangPreferencesListMutex *sync.RWMutex
	// list of languages to show for the user to suggest us
	_CallLangSuggestionsList      roarray.ROArray[string]
	_CallLangSuggestionsListMutex *sync.RWMutex
	// minimum time threshold for a user who has waited in the queue
	_QueueWaitTimeDurationThreshold      string
	_QueueWaitTimeDurationThresholdMutex *sync.RWMutex
	// total duration in past for which call details has to be fetched
	_PastCallRecordsLookupDuration           string
	_PastCallRecordsLookupDurationMutex      *sync.RWMutex
	_BlockedAccountCallRoutingConfig         *BlockedAccountCallRoutingConfig
	_PreRecordedMessageConfig                *PreRecordedMessageConfig
	_IssuePriorityCacheConfig                *IssuePriorityCacheConfig
	_CallLangPrefReleaseConfig               *config.ReleaseConfig
	_SegmentIdToPriorityMapForRoutingChannel map[string]map[string]int
	_RoutingChannelToSegmentIdList           map[string][]string
}

// default priority for any user
func (obj *CallRoutingConfig) DefaultPriorityValue() int {
	return int(atomic.LoadInt64(&obj._DefaultPriorityValue))
}

// max priority value
// 1 is the highest priority and 9 is the lowest priority
// lower the priority value, higher the priority in the queue
func (obj *CallRoutingConfig) MaxPriorityValue() int {
	return int(atomic.LoadInt64(&obj._MaxPriorityValue))
}

// minimum call drop off count threshold for a user in the queue
func (obj *CallRoutingConfig) CallDropOffCountThreshold() int {
	return int(atomic.LoadInt64(&obj._CallDropOffCountThreshold))
}

// factor by which priority value has to be halved for a user
func (obj *CallRoutingConfig) HalvingFactor() int {
	return int(atomic.LoadInt64(&obj._HalvingFactor))
}

// number of past call records which has to be queried for a user
func (obj *CallRoutingConfig) PastCallRecordsLookupSize() int {
	return int(atomic.LoadInt64(&obj._PastCallRecordsLookupSize))
}

// flag to enable or disable routing users based on manual user-routing channel mappings added via dev action
func (obj *CallRoutingConfig) IsManualRoutingEnabled() bool {
	if atomic.LoadUint32(&obj._IsManualRoutingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// flag to enable or disable routing salary program users to a dedicated channel
func (obj *CallRoutingConfig) IsSalaryProgramRoutingEnabled() bool {
	if atomic.LoadUint32(&obj._IsSalaryProgramRoutingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// flag to enable or disable routing credit card users to a dedicated channel
func (obj *CallRoutingConfig) IsCreditCardRoutingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCreditCardRoutingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// flag to enable or disable routing loan users to a dedicated channel
func (obj *CallRoutingConfig) IsActiveLoanRoutingEnabled() bool {
	if atomic.LoadUint32(&obj._IsActiveLoanRoutingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// flag to enable or disable priority routing at global level
func (obj *CallRoutingConfig) IsPriorityRoutingEnabled() bool {
	if atomic.LoadUint32(&obj._IsPriorityRoutingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// IsRecordedMessageEventEnabled indicates whether a event will be published to the topic
// after we have decided to greet an actor with a recorded message
// other services will use this event for triggering async flows at their end, ex: sending comms
func (obj *CallRoutingConfig) IsRecordedMessageEventEnabled() bool {
	if atomic.LoadUint32(&obj._IsRecordedMessageEventEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// list of languages to show to the user to choose as a preference
func (obj *CallRoutingConfig) CallLangPreferencesList() roarray.ROArray[string] {
	obj._CallLangPreferencesListMutex.RLock()
	defer obj._CallLangPreferencesListMutex.RUnlock()
	return obj._CallLangPreferencesList
}

// list of languages to show for the user to suggest us
func (obj *CallRoutingConfig) CallLangSuggestionsList() roarray.ROArray[string] {
	obj._CallLangSuggestionsListMutex.RLock()
	defer obj._CallLangSuggestionsListMutex.RUnlock()
	return obj._CallLangSuggestionsList
}

// minimum time threshold for a user who has waited in the queue
func (obj *CallRoutingConfig) QueueWaitTimeDurationThreshold() string {
	obj._QueueWaitTimeDurationThresholdMutex.RLock()
	defer obj._QueueWaitTimeDurationThresholdMutex.RUnlock()
	return obj._QueueWaitTimeDurationThreshold
}

// total duration in past for which call details has to be fetched
func (obj *CallRoutingConfig) PastCallRecordsLookupDuration() string {
	obj._PastCallRecordsLookupDurationMutex.RLock()
	defer obj._PastCallRecordsLookupDurationMutex.RUnlock()
	return obj._PastCallRecordsLookupDuration
}
func (obj *CallRoutingConfig) BlockedAccountCallRoutingConfig() *BlockedAccountCallRoutingConfig {
	return obj._BlockedAccountCallRoutingConfig
}
func (obj *CallRoutingConfig) PreRecordedMessageConfig() *PreRecordedMessageConfig {
	return obj._PreRecordedMessageConfig
}
func (obj *CallRoutingConfig) IssuePriorityCacheConfig() *IssuePriorityCacheConfig {
	return obj._IssuePriorityCacheConfig
}
func (obj *CallRoutingConfig) CallLangPrefReleaseConfig() *config.ReleaseConfig {
	return obj._CallLangPrefReleaseConfig
}
func (obj *CallRoutingConfig) SegmentIdToPriorityMapForRoutingChannel() map[string]map[string]int {
	return obj._SegmentIdToPriorityMapForRoutingChannel
}
func (obj *CallRoutingConfig) RoutingChannelToSegmentIdList() map[string][]string {
	return obj._RoutingChannelToSegmentIdList
}

type BlockedAccountCallRoutingConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// flag to enable or disable blocked account routing in prod
	_IsRoutingEnabled uint32
	// map of freeze status to reason code which should be used to check if acc is blocked
	_FreezeStatusToReasonCodeMap *syncmap.Map[string, string]
	// map of access revoke reason code which should be used to check if acc is blocked
	_AccessRevokeReasonMap *syncmap.Map[string, bool]
}

// flag to enable or disable blocked account routing in prod
func (obj *BlockedAccountCallRoutingConfig) IsRoutingEnabled() bool {
	if atomic.LoadUint32(&obj._IsRoutingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// map of freeze status to reason code which should be used to check if acc is blocked
func (obj *BlockedAccountCallRoutingConfig) FreezeStatusToReasonCodeMap() *syncmap.Map[string, string] {
	return obj._FreezeStatusToReasonCodeMap
}

// map of access revoke reason code which should be used to check if acc is blocked
func (obj *BlockedAccountCallRoutingConfig) AccessRevokeReasonMap() *syncmap.Map[string, bool] {
	return obj._AccessRevokeReasonMap
}

type PreRecordedMessageConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsHighRiskMessageEnabled         uint32
	_IsScreenerRejectedMessageEnabled uint32
}

func (obj *PreRecordedMessageConfig) IsHighRiskMessageEnabled() bool {
	if atomic.LoadUint32(&obj._IsHighRiskMessageEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *PreRecordedMessageConfig) IsScreenerRejectedMessageEnabled() bool {
	if atomic.LoadUint32(&obj._IsScreenerRejectedMessageEnabled) == 0 {
		return false
	} else {
		return true
	}
}

type IssuePriorityCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled uint32
	// if a user has already reported an issue with priority
	// provided in this list and their call will be given max priority
	_AllowMaxPriorityForIssue      roarray.ROArray[string]
	_AllowMaxPriorityForIssueMutex *sync.RWMutex
	_Key                           string
	_KeyMutex                      *sync.RWMutex
}

func (obj *IssuePriorityCacheConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// if a user has already reported an issue with priority
// provided in this list and their call will be given max priority
func (obj *IssuePriorityCacheConfig) AllowMaxPriorityForIssue() roarray.ROArray[string] {
	obj._AllowMaxPriorityForIssueMutex.RLock()
	defer obj._AllowMaxPriorityForIssueMutex.RUnlock()
	return obj._AllowMaxPriorityForIssue
}
func (obj *IssuePriorityCacheConfig) Key() string {
	obj._KeyMutex.RLock()
	defer obj._KeyMutex.RUnlock()
	return obj._Key
}

type IssueResolutionFeedbackConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled     uint32
	_DisputeConfig *DisputeFeedbackConfig
}

func (obj *IssueResolutionFeedbackConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *IssueResolutionFeedbackConfig) DisputeConfig() *DisputeFeedbackConfig {
	return obj._DisputeConfig
}

type DisputeFeedbackConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_WaitDurationAfterFinalComms      int64
	_IssueCategoryIdToTransactionType *syncmap.Map[string, string]
}

func (obj *DisputeFeedbackConfig) WaitDurationAfterFinalComms() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._WaitDurationAfterFinalComms))
}
func (obj *DisputeFeedbackConfig) IssueCategoryIdToTransactionType() *syncmap.Map[string, string] {
	return obj._IssueCategoryIdToTransactionType
}

type ChatBotConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MaxClientSideFailureCountAllowedForAutoRetry int32
	_NumOfTxnsToBeFetched                         int64
	_NumOfChargesToBeDisplayed                    int64
	_IsForceFallbackToDefaultEnabled              uint32
	// flag to enable or disable release evaluation check
	_IsReleaseEvaluationEnabled uint32
	_IsExtraLoggingEnabled      uint32
	// feature flag to determine whether to use the context code provided by client or not
	_IsContextCodePassingFromClientEnabled uint32
	// flag to enable freshchat experiment
	_IsFreshChatExperimentEnabled                         uint32
	_MaxTimeDurationThresholdForLastSuccessfulSessionTime int64
	// list of actor ids enabled for freshchat issue tree experiment
	_ActorIdsEnabledFreshchatIssueTreeExperiment      roarray.ROArray[string]
	_ActorIdsEnabledFreshchatIssueTreeExperimentMutex *sync.RWMutex
	_DefaultInAppChatView                             string
	_DefaultInAppChatViewMutex                        *sync.RWMutex
	_LiveChatFallbackConfig                           *LiveChatFallbackConfig
	_SenseforthChatInitInfo                           *SenseforthChatInitInfo
	_TxnListDisplayFormat                             *ChatBotDisplayFormat
	_NotificationTemplatesMap                         map[string]*config.ChatBotNotificationContent
	_CreateTicketTag                                  string
	_TxnReasonDisplayMap                              map[string]string
	_NumOfFailedTxnsToBeDisplayed                     int
	_NumberOfATMTxnsToBeFetched                       int
	_IsPredefinedMessageTemplateEnabled               bool
	_IsPostOnboardingHighRiskMessageEnabled           bool
	_PostOnboardingHighRiskUserMessage                string
}

func (obj *ChatBotConfig) MaxClientSideFailureCountAllowedForAutoRetry() int64 {
	return int64(atomic.LoadInt32(&obj._MaxClientSideFailureCountAllowedForAutoRetry))
}
func (obj *ChatBotConfig) NumOfTxnsToBeFetched() int {
	return int(atomic.LoadInt64(&obj._NumOfTxnsToBeFetched))
}
func (obj *ChatBotConfig) NumOfChargesToBeDisplayed() int {
	return int(atomic.LoadInt64(&obj._NumOfChargesToBeDisplayed))
}
func (obj *ChatBotConfig) IsForceFallbackToDefaultEnabled() bool {
	if atomic.LoadUint32(&obj._IsForceFallbackToDefaultEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// flag to enable or disable release evaluation check
func (obj *ChatBotConfig) IsReleaseEvaluationEnabled() bool {
	if atomic.LoadUint32(&obj._IsReleaseEvaluationEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *ChatBotConfig) IsExtraLoggingEnabled() bool {
	if atomic.LoadUint32(&obj._IsExtraLoggingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// feature flag to determine whether to use the context code provided by client or not
func (obj *ChatBotConfig) IsContextCodePassingFromClientEnabled() bool {
	if atomic.LoadUint32(&obj._IsContextCodePassingFromClientEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// flag to enable freshchat experiment
func (obj *ChatBotConfig) IsFreshChatExperimentEnabled() bool {
	if atomic.LoadUint32(&obj._IsFreshChatExperimentEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *ChatBotConfig) MaxTimeDurationThresholdForLastSuccessfulSessionTime() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._MaxTimeDurationThresholdForLastSuccessfulSessionTime))
}

// list of actor ids enabled for freshchat issue tree experiment
func (obj *ChatBotConfig) ActorIdsEnabledFreshchatIssueTreeExperiment() roarray.ROArray[string] {
	obj._ActorIdsEnabledFreshchatIssueTreeExperimentMutex.RLock()
	defer obj._ActorIdsEnabledFreshchatIssueTreeExperimentMutex.RUnlock()
	return obj._ActorIdsEnabledFreshchatIssueTreeExperiment
}
func (obj *ChatBotConfig) DefaultInAppChatView() string {
	obj._DefaultInAppChatViewMutex.RLock()
	defer obj._DefaultInAppChatViewMutex.RUnlock()
	return obj._DefaultInAppChatView
}
func (obj *ChatBotConfig) LiveChatFallbackConfig() *LiveChatFallbackConfig {
	return obj._LiveChatFallbackConfig
}
func (obj *ChatBotConfig) SenseforthChatInitInfo() *SenseforthChatInitInfo {
	return obj._SenseforthChatInitInfo
}
func (obj *ChatBotConfig) TxnListDisplayFormat() *ChatBotDisplayFormat {
	return obj._TxnListDisplayFormat
}
func (obj *ChatBotConfig) NotificationTemplatesMap() map[string]*config.ChatBotNotificationContent {
	return obj._NotificationTemplatesMap
}
func (obj *ChatBotConfig) CreateTicketTag() string {
	return obj._CreateTicketTag
}
func (obj *ChatBotConfig) TxnReasonDisplayMap() map[string]string {
	return obj._TxnReasonDisplayMap
}
func (obj *ChatBotConfig) NumOfFailedTxnsToBeDisplayed() int {
	return obj._NumOfFailedTxnsToBeDisplayed
}
func (obj *ChatBotConfig) NumberOfATMTxnsToBeFetched() int {
	return obj._NumberOfATMTxnsToBeFetched
}
func (obj *ChatBotConfig) IsPredefinedMessageTemplateEnabled() bool {
	return obj._IsPredefinedMessageTemplateEnabled
}
func (obj *ChatBotConfig) IsPostOnboardingHighRiskMessageEnabled() bool {
	return obj._IsPostOnboardingHighRiskMessageEnabled
}
func (obj *ChatBotConfig) PostOnboardingHighRiskUserMessage() string {
	return obj._PostOnboardingHighRiskUserMessage
}

type LiveChatFallbackConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ChannelId      string
	_ChannelIdMutex *sync.RWMutex
}

func (obj *LiveChatFallbackConfig) ChannelId() string {
	obj._ChannelIdMutex.RLock()
	defer obj._ChannelIdMutex.RUnlock()
	return obj._ChannelId
}

type SenseforthChatInitInfo struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_WebViewURLMap *syncmap.Map[string, string]
}

func (obj *SenseforthChatInitInfo) WebViewURLMap() *syncmap.Map[string, string] {
	return obj._WebViewURLMap
}

type ChatBotDisplayFormat struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// The order of the fields in the string format
	_FieldsOrder      roarray.ROArray[string]
	_FieldsOrderMutex *sync.RWMutex
	// Delimiter to be used between these fields
	_Delimiter      string
	_DelimiterMutex *sync.RWMutex
}

// The order of the fields in the string format
func (obj *ChatBotDisplayFormat) FieldsOrder() roarray.ROArray[string] {
	obj._FieldsOrderMutex.RLock()
	defer obj._FieldsOrderMutex.RUnlock()
	return obj._FieldsOrder
}

// Delimiter to be used between these fields
func (obj *ChatBotDisplayFormat) Delimiter() string {
	obj._DelimiterMutex.RLock()
	defer obj._DelimiterMutex.RUnlock()
	return obj._Delimiter
}

type RiskConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_EnableBackendDrivenCharts uint32
	_DevActionConfig           *UploadRiskCaseDevActionConfig
}

func (obj *RiskConfig) EnableBackendDrivenCharts() bool {
	if atomic.LoadUint32(&obj._EnableBackendDrivenCharts) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *RiskConfig) DevActionConfig() *UploadRiskCaseDevActionConfig {
	return obj._DevActionConfig
}

type UploadRiskCaseDevActionConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// batch size for number of cases to be published in a single queue event
	_RiskCaseEventBatchSize     int64
	_SupportedPayloadTypes      roarray.ROArray[string]
	_SupportedPayloadTypesMutex *sync.RWMutex
}

// batch size for number of cases to be published in a single queue event
func (obj *UploadRiskCaseDevActionConfig) RiskCaseEventBatchSize() int {
	return int(atomic.LoadInt64(&obj._RiskCaseEventBatchSize))
}
func (obj *UploadRiskCaseDevActionConfig) SupportedPayloadTypes() roarray.ROArray[string] {
	obj._SupportedPayloadTypesMutex.RLock()
	defer obj._SupportedPayloadTypesMutex.RUnlock()
	return obj._SupportedPayloadTypes
}

type InternationalFundTransfer struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_EnableLRSCheckFromVendor uint32
	_DocumentsBucketName      string
	_DocumentsBucketNameMutex *sync.RWMutex
}

func (obj *InternationalFundTransfer) EnableLRSCheckFromVendor() bool {
	if atomic.LoadUint32(&obj._EnableLRSCheckFromVendor) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *InternationalFundTransfer) DocumentsBucketName() string {
	obj._DocumentsBucketNameMutex.RLock()
	defer obj._DocumentsBucketNameMutex.RUnlock()
	return obj._DocumentsBucketName
}

type SalaryOpsConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MaxBEPaginatedCallsForFiltering                      int64
	_SalaryTransactionFilters                             *SalaryTransactionFilters
	_SalaryProgramHealthInsuranceConfig                   *SalaryProgramHealthInsuranceConfig
	_VerificationFailureReasonsCategoryToDisplayString    map[string]string
	_VerificationFailureReasonsSubCategoryToDisplayString map[string]string
	_NonIncomeRelatedTxnCategoryOntologyIds               []string
	_SalaryProgramS3BucketName                            string
}

func (obj *SalaryOpsConfig) MaxBEPaginatedCallsForFiltering() int {
	return int(atomic.LoadInt64(&obj._MaxBEPaginatedCallsForFiltering))
}
func (obj *SalaryOpsConfig) SalaryTransactionFilters() *SalaryTransactionFilters {
	return obj._SalaryTransactionFilters
}
func (obj *SalaryOpsConfig) SalaryProgramHealthInsuranceConfig() *SalaryProgramHealthInsuranceConfig {
	return obj._SalaryProgramHealthInsuranceConfig
}
func (obj *SalaryOpsConfig) VerificationFailureReasonsCategoryToDisplayString() map[string]string {
	return obj._VerificationFailureReasonsCategoryToDisplayString
}
func (obj *SalaryOpsConfig) VerificationFailureReasonsSubCategoryToDisplayString() map[string]string {
	return obj._VerificationFailureReasonsSubCategoryToDisplayString
}
func (obj *SalaryOpsConfig) NonIncomeRelatedTxnCategoryOntologyIds() []string {
	return obj._NonIncomeRelatedTxnCategoryOntologyIds
}
func (obj *SalaryOpsConfig) SalaryProgramS3BucketName() string {
	return obj._SalaryProgramS3BucketName
}

type SalaryTransactionFilters struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MinSalaryAmount int32
	// min required duration from last verified salary txn for a transaction to be considered for salary txn
	_MinReqDurationFromLastVerification int64
	// max allowed duration from last verified salary txn for a transaction to be considered for salary txn
	_MaxAllowedDurationFromLastVerification int64
	_AllowedTransactionProtocols            roarray.ROArray[string]
	_AllowedTransactionProtocolsMutex       *sync.RWMutex
}

func (obj *SalaryTransactionFilters) MinSalaryAmount() int64 {
	return int64(atomic.LoadInt32(&obj._MinSalaryAmount))
}

// min required duration from last verified salary txn for a transaction to be considered for salary txn
func (obj *SalaryTransactionFilters) MinReqDurationFromLastVerification() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._MinReqDurationFromLastVerification))
}

// max allowed duration from last verified salary txn for a transaction to be considered for salary txn
func (obj *SalaryTransactionFilters) MaxAllowedDurationFromLastVerification() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._MaxAllowedDurationFromLastVerification))
}
func (obj *SalaryTransactionFilters) AllowedTransactionProtocols() roarray.ROArray[string] {
	obj._AllowedTransactionProtocolsMutex.RLock()
	defer obj._AllowedTransactionProtocolsMutex.RUnlock()
	return obj._AllowedTransactionProtocols
}

type SalaryProgramHealthInsuranceConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_PolicyFAQsDocS3Path                           string
	_PolicyFAQsDocS3PathMutex                      *sync.RWMutex
	_PolicyClaimProcessDocS3Path                   string
	_PolicyClaimProcessDocS3PathMutex              *sync.RWMutex
	_InclusionExclusionAndHowItWorksDocS3Path      string
	_InclusionExclusionAndHowItWorksDocS3PathMutex *sync.RWMutex
	_TncsDocS3Path                                 string
	_TncsDocS3PathMutex                            *sync.RWMutex
}

func (obj *SalaryProgramHealthInsuranceConfig) PolicyFAQsDocS3Path() string {
	obj._PolicyFAQsDocS3PathMutex.RLock()
	defer obj._PolicyFAQsDocS3PathMutex.RUnlock()
	return obj._PolicyFAQsDocS3Path
}
func (obj *SalaryProgramHealthInsuranceConfig) PolicyClaimProcessDocS3Path() string {
	obj._PolicyClaimProcessDocS3PathMutex.RLock()
	defer obj._PolicyClaimProcessDocS3PathMutex.RUnlock()
	return obj._PolicyClaimProcessDocS3Path
}
func (obj *SalaryProgramHealthInsuranceConfig) InclusionExclusionAndHowItWorksDocS3Path() string {
	obj._InclusionExclusionAndHowItWorksDocS3PathMutex.RLock()
	defer obj._InclusionExclusionAndHowItWorksDocS3PathMutex.RUnlock()
	return obj._InclusionExclusionAndHowItWorksDocS3Path
}
func (obj *SalaryProgramHealthInsuranceConfig) TncsDocS3Path() string {
	obj._TncsDocS3PathMutex.RLock()
	defer obj._TncsDocS3PathMutex.RUnlock()
	return obj._TncsDocS3Path
}

type LandingPageConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// IsExpectedResolutionTimeFieldPopulatedInLandingPageService flag determines if expected resolution time
	// field would be populated in response of landing page service
	_IsExpectedResolutionTimeFieldPopulatedInLandingPageService uint32
	_RecentUserQueryConfig                                      *RecentUserQueryConfig
	_UserDetailTabsEnumToStringMapping                          map[string]string
}

// IsExpectedResolutionTimeFieldPopulatedInLandingPageService flag determines if expected resolution time
// field would be populated in response of landing page service
func (obj *LandingPageConfig) IsExpectedResolutionTimeFieldPopulatedInLandingPageService() bool {
	if atomic.LoadUint32(&obj._IsExpectedResolutionTimeFieldPopulatedInLandingPageService) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *LandingPageConfig) RecentUserQueryConfig() *RecentUserQueryConfig {
	return obj._RecentUserQueryConfig
}
func (obj *LandingPageConfig) UserDetailTabsEnumToStringMapping() map[string]string {
	return obj._UserDetailTabsEnumToStringMapping
}

type RecentUserQueryConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_NumberOfQueriesToDisplay int32
	_DateFormat               string
	_DateFormatMutex          *sync.RWMutex
	_TimeFormat               string
	_TimeFormatMutex          *sync.RWMutex
}

func (obj *RecentUserQueryConfig) NumberOfQueriesToDisplay() int64 {
	return int64(atomic.LoadInt32(&obj._NumberOfQueriesToDisplay))
}
func (obj *RecentUserQueryConfig) DateFormat() string {
	obj._DateFormatMutex.RLock()
	defer obj._DateFormatMutex.RUnlock()
	return obj._DateFormat
}
func (obj *RecentUserQueryConfig) TimeFormat() string {
	obj._TimeFormatMutex.RLock()
	defer obj._TimeFormatMutex.RUnlock()
	return obj._TimeFormat
}

type DevActionHelperConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// IsBulkResourceAccessibilityCheckEnabled decides whether accessibility of dev actions
	// is checked using BulkCheckResourceAccessibility RPC from casbin service
	_IsBulkResourceAccessibilityCheckEnabled uint32
}

// IsBulkResourceAccessibilityCheckEnabled decides whether accessibility of dev actions
// is checked using BulkCheckResourceAccessibility RPC from casbin service
func (obj *DevActionHelperConfig) IsBulkResourceAccessibilityCheckEnabled() bool {
	if atomic.LoadUint32(&obj._IsBulkResourceAccessibilityCheckEnabled) == 0 {
		return false
	} else {
		return true
	}
}

type OverrideBankActions struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Max requests sets the maximum allowable length of the input array for the override action.
	_MaxRequests int64
}

// Max requests sets the maximum allowable length of the input array for the override action.
func (obj *OverrideBankActions) MaxRequests() int {
	return int(atomic.LoadInt64(&obj._MaxRequests))
}

type ReviewActionConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// list of common question to a user in outcall flow
	_CommonQuestionsToUser      roarray.ROArray[string]
	_CommonQuestionsToUserMutex *sync.RWMutex
}

// list of common question to a user in outcall flow
func (obj *ReviewActionConfig) CommonQuestionsToUser() roarray.ROArray[string] {
	obj._CommonQuestionsToUserMutex.RLock()
	defer obj._CommonQuestionsToUserMutex.RUnlock()
	return obj._CommonQuestionsToUser
}

type VendorAccountPennyDropViaEmailConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ToEmailName      string
	_ToEmailNameMutex *sync.RWMutex
	_FromEmailId      string
	_FromEmailName    string
	_ToEmailId        string
}

func (obj *VendorAccountPennyDropViaEmailConfig) ToEmailName() string {
	obj._ToEmailNameMutex.RLock()
	defer obj._ToEmailNameMutex.RUnlock()
	return obj._ToEmailName
}
func (obj *VendorAccountPennyDropViaEmailConfig) FromEmailId() string {
	return obj._FromEmailId
}
func (obj *VendorAccountPennyDropViaEmailConfig) FromEmailName() string {
	return obj._FromEmailName
}
func (obj *VendorAccountPennyDropViaEmailConfig) ToEmailId() string {
	return obj._ToEmailId
}

type EmployerDbConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// time range till which indexing of added employers will take place
	_IndexingTimeDuration int64
	_EsHostUrl            string
}

// time range till which indexing of added employers will take place
func (obj *EmployerDbConfig) IndexingTimeDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._IndexingTimeDuration))
}
func (obj *EmployerDbConfig) EsHostUrl() string {
	return obj._EsHostUrl
}

type GRPCWebServerConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_JarvisInterceptorConf *genconfig2.JarvisInterceptorConf
	_HttpCorsOptions       *genconfig2.HttpCorsOptions
	_KeycloakAuth          *keycloak.Config
}

func (obj *GRPCWebServerConfig) JarvisInterceptorConf() *genconfig2.JarvisInterceptorConf {
	return obj._JarvisInterceptorConf
}
func (obj *GRPCWebServerConfig) HttpCorsOptions() *genconfig2.HttpCorsOptions {
	return obj._HttpCorsOptions
}
func (obj *GRPCWebServerConfig) KeycloakAuth() *keycloak.Config {
	return obj._KeycloakAuth
}

type SherlockBannersConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Sherlock banners service will invoke these enabled services real-time to fetch any banners configured for given input parameters
	// This a map of Service Name and a bool indicating whether it is enabled or not
	// The service name should be String form of enum types.ServiceName (api/types/service_name.proto)
	_IsServiceEnabledForDynamicFetching *syncmap.Map[string, bool]
	_PriorityOrder                      map[string][]string
}

// Sherlock banners service will invoke these enabled services real-time to fetch any banners configured for given input parameters
// This a map of Service Name and a bool indicating whether it is enabled or not
// The service name should be String form of enum types.ServiceName (api/types/service_name.proto)
func (obj *SherlockBannersConfig) IsServiceEnabledForDynamicFetching() *syncmap.Map[string, bool] {
	return obj._IsServiceEnabledForDynamicFetching
}
func (obj *SherlockBannersConfig) PriorityOrder() map[string][]string {
	return obj._PriorityOrder
}

type ErrorActivityConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Feature flag to determine whether errors processed needs to be piped to watson or not
	_IsPipingErrorEventToWatsonEnabled uint32
	// cool-off period that system should wait for, before creating new incident of same type for same user
	_DefaultIncidentCreationCoolOffPeriod int64
	// key used to get issue category id from event payload
	_IssueCategoryIdEventPayloadKey      string
	_IssueCategoryIdEventPayloadKeyMutex *sync.RWMutex
	// key used to get client request id from event payload
	_ClientRequestIdEventPayloadKey      string
	_ClientRequestIdEventPayloadKeyMutex *sync.RWMutex
	// key used to get is resolution event boolean from event payload
	_IsResolutionEventBooleanEventPayloadKey      string
	_IsResolutionEventBooleanEventPayloadKeyMutex *sync.RWMutex
}

// Feature flag to determine whether errors processed needs to be piped to watson or not
func (obj *ErrorActivityConfig) IsPipingErrorEventToWatsonEnabled() bool {
	if atomic.LoadUint32(&obj._IsPipingErrorEventToWatsonEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// cool-off period that system should wait for, before creating new incident of same type for same user
func (obj *ErrorActivityConfig) DefaultIncidentCreationCoolOffPeriod() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._DefaultIncidentCreationCoolOffPeriod))
}

// key used to get issue category id from event payload
func (obj *ErrorActivityConfig) IssueCategoryIdEventPayloadKey() string {
	obj._IssueCategoryIdEventPayloadKeyMutex.RLock()
	defer obj._IssueCategoryIdEventPayloadKeyMutex.RUnlock()
	return obj._IssueCategoryIdEventPayloadKey
}

// key used to get client request id from event payload
func (obj *ErrorActivityConfig) ClientRequestIdEventPayloadKey() string {
	obj._ClientRequestIdEventPayloadKeyMutex.RLock()
	defer obj._ClientRequestIdEventPayloadKeyMutex.RUnlock()
	return obj._ClientRequestIdEventPayloadKey
}

// key used to get is resolution event boolean from event payload
func (obj *ErrorActivityConfig) IsResolutionEventBooleanEventPayloadKey() string {
	obj._IsResolutionEventBooleanEventPayloadKeyMutex.RLock()
	defer obj._IsResolutionEventBooleanEventPayloadKeyMutex.RUnlock()
	return obj._IsResolutionEventBooleanEventPayloadKey
}

type AgentPromptConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_AgentPromptInfoMap *syncmap.Map[string, *AgentPromptInfo]
}

func (obj *AgentPromptConfig) AgentPromptInfoMap() *syncmap.Map[string, *AgentPromptInfo] {
	return obj._AgentPromptInfoMap
}

type AgentPromptInfo struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsPromptEnabled          uint32
	_PromptValueForAgent      string
	_PromptValueForAgentMutex *sync.RWMutex
	_Description              string
	_DescriptionMutex         *sync.RWMutex
	_PromptCommsTemplate      *PromptCommsTemplate
}

func (obj *AgentPromptInfo) IsPromptEnabled() bool {
	if atomic.LoadUint32(&obj._IsPromptEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AgentPromptInfo) PromptValueForAgent() string {
	obj._PromptValueForAgentMutex.RLock()
	defer obj._PromptValueForAgentMutex.RUnlock()
	return obj._PromptValueForAgent
}
func (obj *AgentPromptInfo) Description() string {
	obj._DescriptionMutex.RLock()
	defer obj._DescriptionMutex.RUnlock()
	return obj._Description
}
func (obj *AgentPromptInfo) PromptCommsTemplate() *PromptCommsTemplate {
	return obj._PromptCommsTemplate
}

type PromptCommsTemplate struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Title            string
	_TitleMutex       *sync.RWMutex
	_Description      string
	_DescriptionMutex *sync.RWMutex
}

func (obj *PromptCommsTemplate) Title() string {
	obj._TitleMutex.RLock()
	defer obj._TitleMutex.RUnlock()
	return obj._Title
}
func (obj *PromptCommsTemplate) Description() string {
	obj._DescriptionMutex.RLock()
	defer obj._DescriptionMutex.RUnlock()
	return obj._Description
}

type RiskOpsInstalledAppsConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled                               uint32
	_RiskScoreMinThreshold                   float64
	_NumberOfLEAWithAppInstalledMinThreshold int
}

func (obj *RiskOpsInstalledAppsConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *RiskOpsInstalledAppsConfig) RiskScoreMinThreshold() float64 {
	return obj._RiskScoreMinThreshold
}
func (obj *RiskOpsInstalledAppsConfig) NumberOfLEAWithAppInstalledMinThreshold() int {
	return obj._NumberOfLEAWithAppInstalledMinThreshold
}

type RiskFennelConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_APIVersion int64
}

func (obj *RiskFennelConfig) APIVersion() int {
	return int(atomic.LoadInt64(&obj._APIVersion))
}

type StageWiseCommsConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// IsGenericCommsEnabled flag decides if fallback comms are enabled for stagewise comms
	_IsGenericCommsEnabled uint32
	// IsIssueConfigSpecificCommsEnabled flag decides if issue config specific comms are enabled
	// if this flag is disabled for all the ticket status, comms will depend on
	// IsGenericCommsEnabled flag
	_IsIssueConfigSpecificCommsEnabled uint32
	// IsPublishingManualTicketCreationEventEnabled flag decides if event will be published
	// to send stage wise comms when a ticket is created on freshdesk
	_IsPublishingManualTicketCreationEventEnabled uint32
	// IsPublishingManualTicketUpdateEventEnabled flag decides if event will be published
	// to send stage wise comms when a ticket is updated on freshdesk
	_IsPublishingManualTicketUpdateEventEnabled uint32
}

// IsGenericCommsEnabled flag decides if fallback comms are enabled for stagewise comms
func (obj *StageWiseCommsConfig) IsGenericCommsEnabled() bool {
	if atomic.LoadUint32(&obj._IsGenericCommsEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// IsIssueConfigSpecificCommsEnabled flag decides if issue config specific comms are enabled
// if this flag is disabled for all the ticket status, comms will depend on
// IsGenericCommsEnabled flag
func (obj *StageWiseCommsConfig) IsIssueConfigSpecificCommsEnabled() bool {
	if atomic.LoadUint32(&obj._IsIssueConfigSpecificCommsEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// IsPublishingManualTicketCreationEventEnabled flag decides if event will be published
// to send stage wise comms when a ticket is created on freshdesk
func (obj *StageWiseCommsConfig) IsPublishingManualTicketCreationEventEnabled() bool {
	if atomic.LoadUint32(&obj._IsPublishingManualTicketCreationEventEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// IsPublishingManualTicketUpdateEventEnabled flag decides if event will be published
// to send stage wise comms when a ticket is updated on freshdesk
func (obj *StageWiseCommsConfig) IsPublishingManualTicketUpdateEventEnabled() bool {
	if atomic.LoadUint32(&obj._IsPublishingManualTicketUpdateEventEnabled) == 0 {
		return false
	} else {
		return true
	}
}

type IssueConfigServiceConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_UseNewCategoryMappingForLLMScreen     uint32
	_IsCacheEnabled                        uint32
	_IssueConfigLevelCacheValidityDuration int64
	_ConfigTypeMapping                     *syncmap.Map[string, string]
	// IssueConfigLevelCacheKey and IssueConfigLevelCacheValidityDuration determine the cache key as well as the respective
	// validity duration, for which an issue config level would be persisted
	_IssueConfigLevelCacheKey          string
	_IssueConfigLevelCacheKeyMutex     *sync.RWMutex
	_IssueCategoryCreatedFromTime      time.Time
	_IssueCategoryCreatedFromTimeMutex *sync.RWMutex
	_IssueCategoryCreatedToTime        time.Time
	_IssueCategoryCreatedToTimeMutex   *sync.RWMutex
}

func (obj *IssueConfigServiceConfig) UseNewCategoryMappingForLLMScreen() bool {
	if atomic.LoadUint32(&obj._UseNewCategoryMappingForLLMScreen) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *IssueConfigServiceConfig) IsCacheEnabled() bool {
	if atomic.LoadUint32(&obj._IsCacheEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *IssueConfigServiceConfig) IssueConfigLevelCacheValidityDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._IssueConfigLevelCacheValidityDuration))
}
func (obj *IssueConfigServiceConfig) ConfigTypeMapping() *syncmap.Map[string, string] {
	return obj._ConfigTypeMapping
}

// IssueConfigLevelCacheKey and IssueConfigLevelCacheValidityDuration determine the cache key as well as the respective
// validity duration, for which an issue config level would be persisted
func (obj *IssueConfigServiceConfig) IssueConfigLevelCacheKey() string {
	obj._IssueConfigLevelCacheKeyMutex.RLock()
	defer obj._IssueConfigLevelCacheKeyMutex.RUnlock()
	return obj._IssueConfigLevelCacheKey
}
func (obj *IssueConfigServiceConfig) IssueCategoryCreatedFromTime() time.Time {
	obj._IssueCategoryCreatedFromTimeMutex.RLock()
	defer obj._IssueCategoryCreatedFromTimeMutex.RUnlock()
	return obj._IssueCategoryCreatedFromTime
}
func (obj *IssueConfigServiceConfig) IssueCategoryCreatedToTime() time.Time {
	obj._IssueCategoryCreatedToTimeMutex.RLock()
	defer obj._IssueCategoryCreatedToTimeMutex.RUnlock()
	return obj._IssueCategoryCreatedToTime
}

type S3EventConsumerConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsCallSummarizationProcessingEnabled uint32
	_BucketName                           string
	_BucketNameMutex                      *sync.RWMutex
	_CallSummarizationFilePath            string
	_CallSummarizationFilePathMutex       *sync.RWMutex
}

func (obj *S3EventConsumerConfig) IsCallSummarizationProcessingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCallSummarizationProcessingEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *S3EventConsumerConfig) BucketName() string {
	obj._BucketNameMutex.RLock()
	defer obj._BucketNameMutex.RUnlock()
	return obj._BucketName
}
func (obj *S3EventConsumerConfig) CallSummarizationFilePath() string {
	obj._CallSummarizationFilePathMutex.RLock()
	defer obj._CallSummarizationFilePathMutex.RUnlock()
	return obj._CallSummarizationFilePath
}

type RiskTxnReviewRolloutConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Setting this flag true will enable new selected order rpc for all analyst
	_IsSelectedOrderRpcEnabledForAll uint32
	// If the above flag is set to false, factory will check for logged in agent email and use the new rpc if part of whitelist
	_SelectedOrderRpcWhitelistedEmails      roarray.ROArray[string]
	_SelectedOrderRpcWhitelistedEmailsMutex *sync.RWMutex
}

// Setting this flag true will enable new selected order rpc for all analyst
func (obj *RiskTxnReviewRolloutConfig) IsSelectedOrderRpcEnabledForAll() bool {
	if atomic.LoadUint32(&obj._IsSelectedOrderRpcEnabledForAll) == 0 {
		return false
	} else {
		return true
	}
}

// If the above flag is set to false, factory will check for logged in agent email and use the new rpc if part of whitelist
func (obj *RiskTxnReviewRolloutConfig) SelectedOrderRpcWhitelistedEmails() roarray.ROArray[string] {
	obj._SelectedOrderRpcWhitelistedEmailsMutex.RLock()
	defer obj._SelectedOrderRpcWhitelistedEmailsMutex.RUnlock()
	return obj._SelectedOrderRpcWhitelistedEmails
}

type RiskOutcallFormRolloutConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Represents the maximum number of forms that can be sent to a user.
	_MaxFormsPerUser int64
	// Setting this flag true will disable outcall form option for all
	_DisableForAllAgents uint32
	// Form will be shown in cases with these review types.
	_WhitelistedReviewTypes      roarray.ROArray[string]
	_WhitelistedReviewTypesMutex *sync.RWMutex
	// Only these questionnaire templates will be shown for outcall form.
	_WhitelistedQuestionnaireTemplates      roarray.ROArray[string]
	_WhitelistedQuestionnaireTemplatesMutex *sync.RWMutex
}

// Represents the maximum number of forms that can be sent to a user.
func (obj *RiskOutcallFormRolloutConfig) MaxFormsPerUser() int {
	return int(atomic.LoadInt64(&obj._MaxFormsPerUser))
}

// Setting this flag true will disable outcall form option for all
func (obj *RiskOutcallFormRolloutConfig) DisableForAllAgents() bool {
	if atomic.LoadUint32(&obj._DisableForAllAgents) == 0 {
		return false
	} else {
		return true
	}
}

// Form will be shown in cases with these review types.
func (obj *RiskOutcallFormRolloutConfig) WhitelistedReviewTypes() roarray.ROArray[string] {
	obj._WhitelistedReviewTypesMutex.RLock()
	defer obj._WhitelistedReviewTypesMutex.RUnlock()
	return obj._WhitelistedReviewTypes
}

// Only these questionnaire templates will be shown for outcall form.
func (obj *RiskOutcallFormRolloutConfig) WhitelistedQuestionnaireTemplates() roarray.ROArray[string] {
	obj._WhitelistedQuestionnaireTemplatesMutex.RLock()
	defer obj._WhitelistedQuestionnaireTemplatesMutex.RUnlock()
	return obj._WhitelistedQuestionnaireTemplates
}

type CallIvrConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// IvrPollCountThreshold denotes the number of times we will allow the user auth status
	// to be polled before informing the user that we were not able to authenticate them
	_IvrPollCountThreshold int32
	// MaxInvalidInputCount denotes the maximum number of invalid inputs a user can give, after that will disconnect the call.
	_MaxInvalidInputCount int32
	_MaxRepeatInputCount  int32
	// IsIvrEnabled indicates whether the users will go through a IVR flow
	_IsIvrEnabled uint32
	// IsCardBlockingEnabled controls whether the actual card blocking API should be called in the IVR flow
	// If false, the flow will work as normal but skip the actual card blocking API call
	_IsCardBlockingEnabled uint32
	// IvrPollCountCacheDuration denotes the duration of time for which the poll count
	// will be stored in the cache
	_IvrPollCountCacheDuration int64
	// IvrPollCountCacheKey will be used to store the number of polls
	// done for an actor id, after a certain number of polls, we can move the actor to other IVR question
	_IvrPollCountCacheKey      string
	_IvrPollCountCacheKeyMutex *sync.RWMutex
}

// IvrPollCountThreshold denotes the number of times we will allow the user auth status
// to be polled before informing the user that we were not able to authenticate them
func (obj *CallIvrConfig) IvrPollCountThreshold() int32 {
	return int32(atomic.LoadInt32(&obj._IvrPollCountThreshold))
}

// MaxInvalidInputCount denotes the maximum number of invalid inputs a user can give, after that will disconnect the call.
func (obj *CallIvrConfig) MaxInvalidInputCount() int32 {
	return int32(atomic.LoadInt32(&obj._MaxInvalidInputCount))
}
func (obj *CallIvrConfig) MaxRepeatInputCount() int32 {
	return int32(atomic.LoadInt32(&obj._MaxRepeatInputCount))
}

// IsIvrEnabled indicates whether the users will go through a IVR flow
func (obj *CallIvrConfig) IsIvrEnabled() bool {
	if atomic.LoadUint32(&obj._IsIvrEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// IsCardBlockingEnabled controls whether the actual card blocking API should be called in the IVR flow
// If false, the flow will work as normal but skip the actual card blocking API call
func (obj *CallIvrConfig) IsCardBlockingEnabled() bool {
	if atomic.LoadUint32(&obj._IsCardBlockingEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// IvrPollCountCacheDuration denotes the duration of time for which the poll count
// will be stored in the cache
func (obj *CallIvrConfig) IvrPollCountCacheDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._IvrPollCountCacheDuration))
}

// IvrPollCountCacheKey will be used to store the number of polls
// done for an actor id, after a certain number of polls, we can move the actor to other IVR question
func (obj *CallIvrConfig) IvrPollCountCacheKey() string {
	obj._IvrPollCountCacheKeyMutex.RLock()
	defer obj._IvrPollCountCacheKeyMutex.RUnlock()
	return obj._IvrPollCountCacheKey
}

type CaseManagementActorActivities struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled uint32
	// context timeout for RPC to avoid any issues with latencies
	_Timeout int64
	// allowed review types to display actor activities
	_AllowedReviewTypes      roarray.ROArray[string]
	_AllowedReviewTypesMutex *sync.RWMutex
}

func (obj *CaseManagementActorActivities) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// context timeout for RPC to avoid any issues with latencies
func (obj *CaseManagementActorActivities) Timeout() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._Timeout))
}

// allowed review types to display actor activities
func (obj *CaseManagementActorActivities) AllowedReviewTypes() roarray.ROArray[string] {
	obj._AllowedReviewTypesMutex.RLock()
	defer obj._AllowedReviewTypesMutex.RUnlock()
	return obj._AllowedReviewTypes
}

type WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_AllowedAccessLevels      roarray.ROArray[string]
	_AllowedAccessLevelsMutex *sync.RWMutex
}

func (obj *WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail) AllowedAccessLevels() roarray.ROArray[string] {
	obj._AllowedAccessLevelsMutex.RLock()
	defer obj._AllowedAccessLevelsMutex.RUnlock()
	return obj._AllowedAccessLevels
}

type ContactUsModelResponseConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ResponseCacheValidityDuration int64
	_ResponseCacheKey              string
	_ResponseCacheKeyMutex         *sync.RWMutex
}

func (obj *ContactUsModelResponseConfig) ResponseCacheValidityDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ResponseCacheValidityDuration))
}
func (obj *ContactUsModelResponseConfig) ResponseCacheKey() string {
	obj._ResponseCacheKeyMutex.RLock()
	defer obj._ResponseCacheKeyMutex.RUnlock()
	return obj._ResponseCacheKey
}

type Filegenerator struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_CamsS3Bucket       string
	_CamsS3BucketMutex  *sync.RWMutex
	_KarvyS3Bucket      string
	_KarvyS3BucketMutex *sync.RWMutex
}

func (obj *Filegenerator) CamsS3Bucket() string {
	obj._CamsS3BucketMutex.RLock()
	defer obj._CamsS3BucketMutex.RUnlock()
	return obj._CamsS3Bucket
}
func (obj *Filegenerator) KarvyS3Bucket() string {
	obj._KarvyS3BucketMutex.RLock()
	defer obj._KarvyS3BucketMutex.RUnlock()
	return obj._KarvyS3Bucket
}

type DbStateConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// feature flag to check whether role-based access control is enabled
	_IsRbacEnabled uint32
}

// feature flag to check whether role-based access control is enabled
func (obj *DbStateConfig) IsRbacEnabled() bool {
	if atomic.LoadUint32(&obj._IsRbacEnabled) == 0 {
		return false
	} else {
		return true
	}
}

type EscalationConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// feature flag to decide whether escalations via Backend is enabled or not
	_IsEscalationEnabled uint32
}

// feature flag to decide whether escalations via Backend is enabled or not
func (obj *EscalationConfig) IsEscalationEnabled() bool {
	if atomic.LoadUint32(&obj._IsEscalationEnabled) == 0 {
		return false
	} else {
		return true
	}
}

type FederalEscalationConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_QPHRateLimit                          uint32
	_IsUpdateConsumerEnabled               uint32
	_QueueId                               string
	_QueueIdMutex                          *sync.RWMutex
	_FederalEscalationAttachmentBucketName string
}

func (obj *FederalEscalationConfig) QPHRateLimit() uint32 {
	return uint32(atomic.LoadUint32(&obj._QPHRateLimit))
}
func (obj *FederalEscalationConfig) IsUpdateConsumerEnabled() bool {
	if atomic.LoadUint32(&obj._IsUpdateConsumerEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *FederalEscalationConfig) QueueId() string {
	obj._QueueIdMutex.RLock()
	defer obj._QueueIdMutex.RUnlock()
	return obj._QueueId
}
func (obj *FederalEscalationConfig) FederalEscalationAttachmentBucketName() string {
	return obj._FederalEscalationAttachmentBucketName
}

type SaClosureEligibilityConf struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_QpsRateLimit          uint32
	_MaxConcurrentWorkers  uint32
	_MaxActorsAllowedInCsv uint32
	_MaxProcessingDuration int64
}

func (obj *SaClosureEligibilityConf) QpsRateLimit() uint32 {
	return uint32(atomic.LoadUint32(&obj._QpsRateLimit))
}
func (obj *SaClosureEligibilityConf) MaxConcurrentWorkers() uint32 {
	return uint32(atomic.LoadUint32(&obj._MaxConcurrentWorkers))
}
func (obj *SaClosureEligibilityConf) MaxActorsAllowedInCsv() uint32 {
	return uint32(atomic.LoadUint32(&obj._MaxActorsAllowedInCsv))
}
func (obj *SaClosureEligibilityConf) MaxProcessingDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._MaxProcessingDuration))
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxcountthresholdforfetchingbulkuserinfo"] = _obj.SetMaxCountThresholdForFetchingBulkUserInfo
	_setters["usepkgratelimiter"] = _obj.SetUsePkgRateLimiter
	_setters["isredactionenabledfordbstates"] = _obj.SetIsRedactionEnabledForDBStates
	_setters["enablebalancemetricsoncasemanagement"] = _obj.SetEnableBalanceMetricsOnCaseManagement
	_setters["enableoutcalldataincasemanagementforriskops"] = _obj.SetEnableOutCallDataInCaseManagementForRiskOps
	_setters["isnewoperationalstatusapienabled"] = _obj.SetIsNewOperationalStatusAPIEnabled
	_setters["watchlistreasons"] = _obj.SetWatchlistReasons
	_obj._WatchlistReasonsMutex = &sync.RWMutex{}
	_setters["cxfreshdeskticketbaseurl"] = _obj.SetCXFreshdeskTicketBaseURL
	_obj._CXFreshdeskTicketBaseURLMutex = &sync.RWMutex{}
	_EmailVerification, _fieldSetters := NewEmailVerification()
	_obj._EmailVerification = _EmailVerification
	helper.AddFieldSetters("emailverification", _fieldSetters, _setters)
	_MobilePromptVerification, _fieldSetters := NewMobilePromptVerification()
	_obj._MobilePromptVerification = _MobilePromptVerification
	helper.AddFieldSetters("mobilepromptverification", _fieldSetters, _setters)
	_CustomerAuth, _fieldSetters := NewCustomerAuth()
	_obj._CustomerAuth = _CustomerAuth
	helper.AddFieldSetters("customerauth", _fieldSetters, _setters)
	_FreshdeskTicketSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FreshdeskTicketSubscriber = _FreshdeskTicketSubscriber
	helper.AddFieldSetters("freshdeskticketsubscriber", _fieldSetters, _setters)
	_FreshdeskContactSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FreshdeskContactSubscriber = _FreshdeskContactSubscriber
	helper.AddFieldSetters("freshdeskcontactsubscriber", _fieldSetters, _setters)
	_WatsonIncidentReportingSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._WatsonIncidentReportingSubscriber = _WatsonIncidentReportingSubscriber
	helper.AddFieldSetters("watsonincidentreportingsubscriber", _fieldSetters, _setters)
	_WatsonIncidentResolutionSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._WatsonIncidentResolutionSubscriber = _WatsonIncidentResolutionSubscriber
	helper.AddFieldSetters("watsonincidentresolutionsubscriber", _fieldSetters, _setters)
	_WatsonTicketEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._WatsonTicketEventSubscriber = _WatsonTicketEventSubscriber
	helper.AddFieldSetters("watsonticketeventsubscriber", _fieldSetters, _setters)
	_WatsonCreateTicketSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._WatsonCreateTicketSubscriber = _WatsonCreateTicketSubscriber
	helper.AddFieldSetters("watsoncreateticketsubscriber", _fieldSetters, _setters)
	_DisputeSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DisputeSubscriber = _DisputeSubscriber
	helper.AddFieldSetters("disputesubscriber", _fieldSetters, _setters)
	_DisputeCreateTicketSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DisputeCreateTicketSubscriber = _DisputeCreateTicketSubscriber
	helper.AddFieldSetters("disputecreateticketsubscriber", _fieldSetters, _setters)
	_DisputeUpdateTicketSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DisputeUpdateTicketSubscriber = _DisputeUpdateTicketSubscriber
	helper.AddFieldSetters("disputeupdateticketsubscriber", _fieldSetters, _setters)
	_DisputeAddNoteTicketSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DisputeAddNoteTicketSubscriber = _DisputeAddNoteTicketSubscriber
	helper.AddFieldSetters("disputeaddnoteticketsubscriber", _fieldSetters, _setters)
	_DevActionSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DevActionSubscriber = _DevActionSubscriber
	helper.AddFieldSetters("devactionsubscriber", _fieldSetters, _setters)
	_FreshdeskTicketDataEventSubscriberFifo, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FreshdeskTicketDataEventSubscriberFifo = _FreshdeskTicketDataEventSubscriberFifo
	helper.AddFieldSetters("freshdeskticketdataeventsubscriberfifo", _fieldSetters, _setters)
	_FreshdeskTicketDataEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FreshdeskTicketDataEventSubscriber = _FreshdeskTicketDataEventSubscriber
	helper.AddFieldSetters("freshdeskticketdataeventsubscriber", _fieldSetters, _setters)
	_CrmIssueTrackerIntegrationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CrmIssueTrackerIntegrationSubscriber = _CrmIssueTrackerIntegrationSubscriber
	helper.AddFieldSetters("crmissuetrackerintegrationsubscriber", _fieldSetters, _setters)
	_Dispute, _fieldSetters := NewDispute()
	_obj._Dispute = _Dispute
	helper.AddFieldSetters("dispute", _fieldSetters, _setters)
	_AppLog, _fieldSetters := NewAppLog()
	_obj._AppLog = _AppLog
	helper.AddFieldSetters("applog", _fieldSetters, _setters)
	_Payout, _fieldSetters := NewPayout()
	_obj._Payout = _Payout
	helper.AddFieldSetters("payout", _fieldSetters, _setters)
	_PayoutStatusCheckSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._PayoutStatusCheckSubscriber = _PayoutStatusCheckSubscriber
	helper.AddFieldSetters("payoutstatuschecksubscriber", _fieldSetters, _setters)
	_WaitlistSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._WaitlistSubscriber = _WaitlistSubscriber
	helper.AddFieldSetters("waitlistsubscriber", _fieldSetters, _setters)
	_BulkUserInfoViaEmailConfig, _fieldSetters := NewBulkUserInfoViaEmailConfig()
	_obj._BulkUserInfoViaEmailConfig = _BulkUserInfoViaEmailConfig
	helper.AddFieldSetters("bulkuserinfoviaemailconfig", _fieldSetters, _setters)
	_UpiDisputeAutoUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UpiDisputeAutoUpdateEventSubscriber = _UpiDisputeAutoUpdateEventSubscriber
	helper.AddFieldSetters("upidisputeautoupdateeventsubscriber", _fieldSetters, _setters)
	_UpdateTicketSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UpdateTicketSubscriber = _UpdateTicketSubscriber
	helper.AddFieldSetters("updateticketsubscriber", _fieldSetters, _setters)
	_CreateTicketSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreateTicketSubscriber = _CreateTicketSubscriber
	helper.AddFieldSetters("createticketsubscriber", _fieldSetters, _setters)
	_TicketConfig, _fieldSetters := NewTicketConfig()
	_obj._TicketConfig = _TicketConfig
	helper.AddFieldSetters("ticketconfig", _fieldSetters, _setters)
	_CallConfig, _fieldSetters := NewCallConfig()
	_obj._CallConfig = _CallConfig
	helper.AddFieldSetters("callconfig", _fieldSetters, _setters)
	_OzonetelCallEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OzonetelCallEventSubscriber = _OzonetelCallEventSubscriber
	helper.AddFieldSetters("ozonetelcalleventsubscriber", _fieldSetters, _setters)
	_CallRoutingConfig, _fieldSetters := NewCallRoutingConfig()
	_obj._CallRoutingConfig = _CallRoutingConfig
	helper.AddFieldSetters("callroutingconfig", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_IssueResolutionFeedbackConfig, _fieldSetters := NewIssueResolutionFeedbackConfig()
	_obj._IssueResolutionFeedbackConfig = _IssueResolutionFeedbackConfig
	helper.AddFieldSetters("issueresolutionfeedbackconfig", _fieldSetters, _setters)
	_ChatBotConfig, _fieldSetters := NewChatBotConfig()
	_obj._ChatBotConfig = _ChatBotConfig
	helper.AddFieldSetters("chatbotconfig", _fieldSetters, _setters)
	_FreshchatEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FreshchatEventSubscriber = _FreshchatEventSubscriber
	helper.AddFieldSetters("freshchateventsubscriber", _fieldSetters, _setters)
	_RiskConfig, _fieldSetters := NewRiskConfig()
	_obj._RiskConfig = _RiskConfig
	helper.AddFieldSetters("riskconfig", _fieldSetters, _setters)
	_InternationalFundTransfer, _fieldSetters := NewInternationalFundTransfer()
	_obj._InternationalFundTransfer = _InternationalFundTransfer
	helper.AddFieldSetters("internationalfundtransfer", _fieldSetters, _setters)
	_SalaryOpsConfig, _fieldSetters := NewSalaryOpsConfig()
	_obj._SalaryOpsConfig = _SalaryOpsConfig
	helper.AddFieldSetters("salaryopsconfig", _fieldSetters, _setters)
	_LandingPageConfig, _fieldSetters := NewLandingPageConfig()
	_obj._LandingPageConfig = _LandingPageConfig
	helper.AddFieldSetters("landingpageconfig", _fieldSetters, _setters)
	_TicketReconciliationEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._TicketReconciliationEventSubscriber = _TicketReconciliationEventSubscriber
	helper.AddFieldSetters("ticketreconciliationeventsubscriber", _fieldSetters, _setters)
	_DevActionHelperConfig, _fieldSetters := NewDevActionHelperConfig()
	_obj._DevActionHelperConfig = _DevActionHelperConfig
	helper.AddFieldSetters("devactionhelperconfig", _fieldSetters, _setters)
	_OverrideBankActions, _fieldSetters := NewOverrideBankActions()
	_obj._OverrideBankActions = _OverrideBankActions
	helper.AddFieldSetters("overridebankactions", _fieldSetters, _setters)
	_ReviewAction, _fieldSetters := NewReviewActionConfig()
	_obj._ReviewAction = _ReviewAction
	helper.AddFieldSetters("reviewaction", _fieldSetters, _setters)
	_VendorAccountPennyDropViaEmailConfig, _fieldSetters := NewVendorAccountPennyDropViaEmailConfig()
	_obj._VendorAccountPennyDropViaEmailConfig = _VendorAccountPennyDropViaEmailConfig
	helper.AddFieldSetters("vendoraccountpennydropviaemailconfig", _fieldSetters, _setters)
	_EmployerDbConfig, _fieldSetters := NewEmployerDbConfig()
	_obj._EmployerDbConfig = _EmployerDbConfig
	helper.AddFieldSetters("employerdbconfig", _fieldSetters, _setters)
	_GRPCWebServerConfig, _fieldSetters := NewGRPCWebServerConfig()
	_obj._GRPCWebServerConfig = _GRPCWebServerConfig
	helper.AddFieldSetters("grpcwebserverconfig", _fieldSetters, _setters)
	_SherlockBannersConfig, _fieldSetters := NewSherlockBannersConfig()
	_obj._SherlockBannersConfig = _SherlockBannersConfig
	helper.AddFieldSetters("sherlockbannersconfig", _fieldSetters, _setters)
	_RudderEventKafkaConsumerGroup, _fieldSetters := gencfg.NewKafkaConsumerGroup()
	_obj._RudderEventKafkaConsumerGroup = _RudderEventKafkaConsumerGroup
	helper.AddFieldSetters("ruddereventkafkaconsumergroup", _fieldSetters, _setters)
	_ErrorActivityConfig, _fieldSetters := NewErrorActivityConfig()
	_obj._ErrorActivityConfig = _ErrorActivityConfig
	helper.AddFieldSetters("erroractivityconfig", _fieldSetters, _setters)
	_AgentPromptConfig, _fieldSetters := NewAgentPromptConfig()
	_obj._AgentPromptConfig = _AgentPromptConfig
	helper.AddFieldSetters("agentpromptconfig", _fieldSetters, _setters)
	_RiskOpsInstalledAppsConfig, _fieldSetters := NewRiskOpsInstalledAppsConfig()
	_obj._RiskOpsInstalledAppsConfig = _RiskOpsInstalledAppsConfig
	helper.AddFieldSetters("riskopsinstalledappsconfig", _fieldSetters, _setters)
	_RiskFennelConfig, _fieldSetters := NewRiskFennelConfig()
	_obj._RiskFennelConfig = _RiskFennelConfig
	helper.AddFieldSetters("riskfennelconfig", _fieldSetters, _setters)
	_StageWiseCommsConfig, _fieldSetters := NewStageWiseCommsConfig()
	_obj._StageWiseCommsConfig = _StageWiseCommsConfig
	helper.AddFieldSetters("stagewisecommsconfig", _fieldSetters, _setters)
	_IssueConfigServiceConfig, _fieldSetters := NewIssueConfigServiceConfig()
	_obj._IssueConfigServiceConfig = _IssueConfigServiceConfig
	helper.AddFieldSetters("issueconfigserviceconfig", _fieldSetters, _setters)
	_S3EventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._S3EventSubscriber = _S3EventSubscriber
	helper.AddFieldSetters("s3eventsubscriber", _fieldSetters, _setters)
	_S3EventConsumerConfig, _fieldSetters := NewS3EventConsumerConfig()
	_obj._S3EventConsumerConfig = _S3EventConsumerConfig
	helper.AddFieldSetters("s3eventconsumerconfig", _fieldSetters, _setters)
	_RiskTxnReviewRolloutConfig, _fieldSetters := NewRiskTxnReviewRolloutConfig()
	_obj._RiskTxnReviewRolloutConfig = _RiskTxnReviewRolloutConfig
	helper.AddFieldSetters("risktxnreviewrolloutconfig", _fieldSetters, _setters)
	_RiskOutcallFormRolloutConfig, _fieldSetters := NewRiskOutcallFormRolloutConfig()
	_obj._RiskOutcallFormRolloutConfig = _RiskOutcallFormRolloutConfig
	helper.AddFieldSetters("riskoutcallformrolloutconfig", _fieldSetters, _setters)
	_CallIvrConfig, _fieldSetters := NewCallIvrConfig()
	_obj._CallIvrConfig = _CallIvrConfig
	helper.AddFieldSetters("callivrconfig", _fieldSetters, _setters)
	_CaseManagementActorActivities, _fieldSetters := NewCaseManagementActorActivities()
	_obj._CaseManagementActorActivities = _CaseManagementActorActivities
	helper.AddFieldSetters("casemanagementactoractivities", _fieldSetters, _setters)
	_WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail, _fieldSetters := NewWhiteListAccessLevelForActorEnrichmentByPhoneOrEmail()
	_obj._WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail = _WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail
	helper.AddFieldSetters("whitelistaccesslevelforactorenrichmentbyphoneoremail", _fieldSetters, _setters)
	_ContactUsModelResponseConfig, _fieldSetters := NewContactUsModelResponseConfig()
	_obj._ContactUsModelResponseConfig = _ContactUsModelResponseConfig
	helper.AddFieldSetters("contactusmodelresponseconfig", _fieldSetters, _setters)
	_S3BucketNameForFileGenerator, _fieldSetters := NewFilegenerator()
	_obj._S3BucketNameForFileGenerator = _S3BucketNameForFileGenerator
	helper.AddFieldSetters("s3bucketnameforfilegenerator", _fieldSetters, _setters)
	_DbStateConfig, _fieldSetters := NewDbStateConfig()
	_obj._DbStateConfig = _DbStateConfig
	helper.AddFieldSetters("dbstateconfig", _fieldSetters, _setters)
	_EscalationConfig, _fieldSetters := NewEscalationConfig()
	_obj._EscalationConfig = _EscalationConfig
	helper.AddFieldSetters("escalationconfig", _fieldSetters, _setters)
	_FederalEscalationUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FederalEscalationUpdateEventSubscriber = _FederalEscalationUpdateEventSubscriber
	helper.AddFieldSetters("federalescalationupdateeventsubscriber", _fieldSetters, _setters)
	_FederalEscalationCreationEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FederalEscalationCreationEventSubscriber = _FederalEscalationCreationEventSubscriber
	helper.AddFieldSetters("federalescalationcreationeventsubscriber", _fieldSetters, _setters)
	_FederalEscalationConfig, _fieldSetters := NewFederalEscalationConfig()
	_obj._FederalEscalationConfig = _FederalEscalationConfig
	helper.AddFieldSetters("federalescalationconfig", _fieldSetters, _setters)
	_SaClosureEligibilityConf, _fieldSetters := NewSaClosureEligibilityConf()
	_obj._SaClosureEligibilityConf = _SaClosureEligibilityConf
	helper.AddFieldSetters("saclosureeligibilityconf", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxcountthresholdforfetchingbulkuserinfo"] = _obj.SetMaxCountThresholdForFetchingBulkUserInfo
	_setters["usepkgratelimiter"] = _obj.SetUsePkgRateLimiter
	_setters["isredactionenabledfordbstates"] = _obj.SetIsRedactionEnabledForDBStates
	_setters["enablebalancemetricsoncasemanagement"] = _obj.SetEnableBalanceMetricsOnCaseManagement
	_setters["enableoutcalldataincasemanagementforriskops"] = _obj.SetEnableOutCallDataInCaseManagementForRiskOps
	_setters["isnewoperationalstatusapienabled"] = _obj.SetIsNewOperationalStatusAPIEnabled
	_setters["watchlistreasons"] = _obj.SetWatchlistReasons
	_obj._WatchlistReasonsMutex = &sync.RWMutex{}
	_setters["cxfreshdeskticketbaseurl"] = _obj.SetCXFreshdeskTicketBaseURL
	_obj._CXFreshdeskTicketBaseURLMutex = &sync.RWMutex{}
	_EmailVerification, _fieldSetters := NewEmailVerification()
	_obj._EmailVerification = _EmailVerification
	helper.AddFieldSetters("emailverification", _fieldSetters, _setters)
	_MobilePromptVerification, _fieldSetters := NewMobilePromptVerification()
	_obj._MobilePromptVerification = _MobilePromptVerification
	helper.AddFieldSetters("mobilepromptverification", _fieldSetters, _setters)
	_CustomerAuth, _fieldSetters := NewCustomerAuth()
	_obj._CustomerAuth = _CustomerAuth
	helper.AddFieldSetters("customerauth", _fieldSetters, _setters)
	_FreshdeskTicketSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FreshdeskTicketSubscriber = _FreshdeskTicketSubscriber
	helper.AddFieldSetters("freshdeskticketsubscriber", _fieldSetters, _setters)
	_FreshdeskContactSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FreshdeskContactSubscriber = _FreshdeskContactSubscriber
	helper.AddFieldSetters("freshdeskcontactsubscriber", _fieldSetters, _setters)
	_WatsonIncidentReportingSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._WatsonIncidentReportingSubscriber = _WatsonIncidentReportingSubscriber
	helper.AddFieldSetters("watsonincidentreportingsubscriber", _fieldSetters, _setters)
	_WatsonIncidentResolutionSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._WatsonIncidentResolutionSubscriber = _WatsonIncidentResolutionSubscriber
	helper.AddFieldSetters("watsonincidentresolutionsubscriber", _fieldSetters, _setters)
	_WatsonTicketEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._WatsonTicketEventSubscriber = _WatsonTicketEventSubscriber
	helper.AddFieldSetters("watsonticketeventsubscriber", _fieldSetters, _setters)
	_WatsonCreateTicketSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._WatsonCreateTicketSubscriber = _WatsonCreateTicketSubscriber
	helper.AddFieldSetters("watsoncreateticketsubscriber", _fieldSetters, _setters)
	_DisputeSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DisputeSubscriber = _DisputeSubscriber
	helper.AddFieldSetters("disputesubscriber", _fieldSetters, _setters)
	_DisputeCreateTicketSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DisputeCreateTicketSubscriber = _DisputeCreateTicketSubscriber
	helper.AddFieldSetters("disputecreateticketsubscriber", _fieldSetters, _setters)
	_DisputeUpdateTicketSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DisputeUpdateTicketSubscriber = _DisputeUpdateTicketSubscriber
	helper.AddFieldSetters("disputeupdateticketsubscriber", _fieldSetters, _setters)
	_DisputeAddNoteTicketSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DisputeAddNoteTicketSubscriber = _DisputeAddNoteTicketSubscriber
	helper.AddFieldSetters("disputeaddnoteticketsubscriber", _fieldSetters, _setters)
	_DevActionSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DevActionSubscriber = _DevActionSubscriber
	helper.AddFieldSetters("devactionsubscriber", _fieldSetters, _setters)
	_FreshdeskTicketDataEventSubscriberFifo, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FreshdeskTicketDataEventSubscriberFifo = _FreshdeskTicketDataEventSubscriberFifo
	helper.AddFieldSetters("freshdeskticketdataeventsubscriberfifo", _fieldSetters, _setters)
	_FreshdeskTicketDataEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FreshdeskTicketDataEventSubscriber = _FreshdeskTicketDataEventSubscriber
	helper.AddFieldSetters("freshdeskticketdataeventsubscriber", _fieldSetters, _setters)
	_CrmIssueTrackerIntegrationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CrmIssueTrackerIntegrationSubscriber = _CrmIssueTrackerIntegrationSubscriber
	helper.AddFieldSetters("crmissuetrackerintegrationsubscriber", _fieldSetters, _setters)
	_Dispute, _fieldSetters := NewDispute()
	_obj._Dispute = _Dispute
	helper.AddFieldSetters("dispute", _fieldSetters, _setters)
	_AppLog, _fieldSetters := NewAppLog()
	_obj._AppLog = _AppLog
	helper.AddFieldSetters("applog", _fieldSetters, _setters)
	_Payout, _fieldSetters := NewPayout()
	_obj._Payout = _Payout
	helper.AddFieldSetters("payout", _fieldSetters, _setters)
	_PayoutStatusCheckSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._PayoutStatusCheckSubscriber = _PayoutStatusCheckSubscriber
	helper.AddFieldSetters("payoutstatuschecksubscriber", _fieldSetters, _setters)
	_WaitlistSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._WaitlistSubscriber = _WaitlistSubscriber
	helper.AddFieldSetters("waitlistsubscriber", _fieldSetters, _setters)
	_BulkUserInfoViaEmailConfig, _fieldSetters := NewBulkUserInfoViaEmailConfig()
	_obj._BulkUserInfoViaEmailConfig = _BulkUserInfoViaEmailConfig
	helper.AddFieldSetters("bulkuserinfoviaemailconfig", _fieldSetters, _setters)
	_UpiDisputeAutoUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UpiDisputeAutoUpdateEventSubscriber = _UpiDisputeAutoUpdateEventSubscriber
	helper.AddFieldSetters("upidisputeautoupdateeventsubscriber", _fieldSetters, _setters)
	_UpdateTicketSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UpdateTicketSubscriber = _UpdateTicketSubscriber
	helper.AddFieldSetters("updateticketsubscriber", _fieldSetters, _setters)
	_CreateTicketSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreateTicketSubscriber = _CreateTicketSubscriber
	helper.AddFieldSetters("createticketsubscriber", _fieldSetters, _setters)
	_TicketConfig, _fieldSetters := NewTicketConfig()
	_obj._TicketConfig = _TicketConfig
	helper.AddFieldSetters("ticketconfig", _fieldSetters, _setters)
	_CallConfig, _fieldSetters := NewCallConfig()
	_obj._CallConfig = _CallConfig
	helper.AddFieldSetters("callconfig", _fieldSetters, _setters)
	_OzonetelCallEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OzonetelCallEventSubscriber = _OzonetelCallEventSubscriber
	helper.AddFieldSetters("ozonetelcalleventsubscriber", _fieldSetters, _setters)
	_CallRoutingConfig, _fieldSetters := NewCallRoutingConfig()
	_obj._CallRoutingConfig = _CallRoutingConfig
	helper.AddFieldSetters("callroutingconfig", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_IssueResolutionFeedbackConfig, _fieldSetters := NewIssueResolutionFeedbackConfig()
	_obj._IssueResolutionFeedbackConfig = _IssueResolutionFeedbackConfig
	helper.AddFieldSetters("issueresolutionfeedbackconfig", _fieldSetters, _setters)
	_ChatBotConfig, _fieldSetters := NewChatBotConfig()
	_obj._ChatBotConfig = _ChatBotConfig
	helper.AddFieldSetters("chatbotconfig", _fieldSetters, _setters)
	_FreshchatEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FreshchatEventSubscriber = _FreshchatEventSubscriber
	helper.AddFieldSetters("freshchateventsubscriber", _fieldSetters, _setters)
	_RiskConfig, _fieldSetters := NewRiskConfig()
	_obj._RiskConfig = _RiskConfig
	helper.AddFieldSetters("riskconfig", _fieldSetters, _setters)
	_InternationalFundTransfer, _fieldSetters := NewInternationalFundTransfer()
	_obj._InternationalFundTransfer = _InternationalFundTransfer
	helper.AddFieldSetters("internationalfundtransfer", _fieldSetters, _setters)
	_SalaryOpsConfig, _fieldSetters := NewSalaryOpsConfig()
	_obj._SalaryOpsConfig = _SalaryOpsConfig
	helper.AddFieldSetters("salaryopsconfig", _fieldSetters, _setters)
	_LandingPageConfig, _fieldSetters := NewLandingPageConfig()
	_obj._LandingPageConfig = _LandingPageConfig
	helper.AddFieldSetters("landingpageconfig", _fieldSetters, _setters)
	_TicketReconciliationEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._TicketReconciliationEventSubscriber = _TicketReconciliationEventSubscriber
	helper.AddFieldSetters("ticketreconciliationeventsubscriber", _fieldSetters, _setters)
	_DevActionHelperConfig, _fieldSetters := NewDevActionHelperConfig()
	_obj._DevActionHelperConfig = _DevActionHelperConfig
	helper.AddFieldSetters("devactionhelperconfig", _fieldSetters, _setters)
	_OverrideBankActions, _fieldSetters := NewOverrideBankActions()
	_obj._OverrideBankActions = _OverrideBankActions
	helper.AddFieldSetters("overridebankactions", _fieldSetters, _setters)
	_ReviewAction, _fieldSetters := NewReviewActionConfig()
	_obj._ReviewAction = _ReviewAction
	helper.AddFieldSetters("reviewaction", _fieldSetters, _setters)
	_VendorAccountPennyDropViaEmailConfig, _fieldSetters := NewVendorAccountPennyDropViaEmailConfig()
	_obj._VendorAccountPennyDropViaEmailConfig = _VendorAccountPennyDropViaEmailConfig
	helper.AddFieldSetters("vendoraccountpennydropviaemailconfig", _fieldSetters, _setters)
	_EmployerDbConfig, _fieldSetters := NewEmployerDbConfig()
	_obj._EmployerDbConfig = _EmployerDbConfig
	helper.AddFieldSetters("employerdbconfig", _fieldSetters, _setters)
	_GRPCWebServerConfig, _fieldSetters := NewGRPCWebServerConfig()
	_obj._GRPCWebServerConfig = _GRPCWebServerConfig
	helper.AddFieldSetters("grpcwebserverconfig", _fieldSetters, _setters)
	_SherlockBannersConfig, _fieldSetters := NewSherlockBannersConfig()
	_obj._SherlockBannersConfig = _SherlockBannersConfig
	helper.AddFieldSetters("sherlockbannersconfig", _fieldSetters, _setters)
	_RudderEventKafkaConsumerGroup, _fieldSetters := gencfg.NewKafkaConsumerGroup()
	_obj._RudderEventKafkaConsumerGroup = _RudderEventKafkaConsumerGroup
	helper.AddFieldSetters("ruddereventkafkaconsumergroup", _fieldSetters, _setters)
	_ErrorActivityConfig, _fieldSetters := NewErrorActivityConfig()
	_obj._ErrorActivityConfig = _ErrorActivityConfig
	helper.AddFieldSetters("erroractivityconfig", _fieldSetters, _setters)
	_AgentPromptConfig, _fieldSetters := NewAgentPromptConfig()
	_obj._AgentPromptConfig = _AgentPromptConfig
	helper.AddFieldSetters("agentpromptconfig", _fieldSetters, _setters)
	_RiskOpsInstalledAppsConfig, _fieldSetters := NewRiskOpsInstalledAppsConfig()
	_obj._RiskOpsInstalledAppsConfig = _RiskOpsInstalledAppsConfig
	helper.AddFieldSetters("riskopsinstalledappsconfig", _fieldSetters, _setters)
	_RiskFennelConfig, _fieldSetters := NewRiskFennelConfig()
	_obj._RiskFennelConfig = _RiskFennelConfig
	helper.AddFieldSetters("riskfennelconfig", _fieldSetters, _setters)
	_StageWiseCommsConfig, _fieldSetters := NewStageWiseCommsConfig()
	_obj._StageWiseCommsConfig = _StageWiseCommsConfig
	helper.AddFieldSetters("stagewisecommsconfig", _fieldSetters, _setters)
	_IssueConfigServiceConfig, _fieldSetters := NewIssueConfigServiceConfig()
	_obj._IssueConfigServiceConfig = _IssueConfigServiceConfig
	helper.AddFieldSetters("issueconfigserviceconfig", _fieldSetters, _setters)
	_S3EventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._S3EventSubscriber = _S3EventSubscriber
	helper.AddFieldSetters("s3eventsubscriber", _fieldSetters, _setters)
	_S3EventConsumerConfig, _fieldSetters := NewS3EventConsumerConfig()
	_obj._S3EventConsumerConfig = _S3EventConsumerConfig
	helper.AddFieldSetters("s3eventconsumerconfig", _fieldSetters, _setters)
	_RiskTxnReviewRolloutConfig, _fieldSetters := NewRiskTxnReviewRolloutConfig()
	_obj._RiskTxnReviewRolloutConfig = _RiskTxnReviewRolloutConfig
	helper.AddFieldSetters("risktxnreviewrolloutconfig", _fieldSetters, _setters)
	_RiskOutcallFormRolloutConfig, _fieldSetters := NewRiskOutcallFormRolloutConfig()
	_obj._RiskOutcallFormRolloutConfig = _RiskOutcallFormRolloutConfig
	helper.AddFieldSetters("riskoutcallformrolloutconfig", _fieldSetters, _setters)
	_CallIvrConfig, _fieldSetters := NewCallIvrConfig()
	_obj._CallIvrConfig = _CallIvrConfig
	helper.AddFieldSetters("callivrconfig", _fieldSetters, _setters)
	_CaseManagementActorActivities, _fieldSetters := NewCaseManagementActorActivities()
	_obj._CaseManagementActorActivities = _CaseManagementActorActivities
	helper.AddFieldSetters("casemanagementactoractivities", _fieldSetters, _setters)
	_WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail, _fieldSetters := NewWhiteListAccessLevelForActorEnrichmentByPhoneOrEmail()
	_obj._WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail = _WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail
	helper.AddFieldSetters("whitelistaccesslevelforactorenrichmentbyphoneoremail", _fieldSetters, _setters)
	_ContactUsModelResponseConfig, _fieldSetters := NewContactUsModelResponseConfig()
	_obj._ContactUsModelResponseConfig = _ContactUsModelResponseConfig
	helper.AddFieldSetters("contactusmodelresponseconfig", _fieldSetters, _setters)
	_S3BucketNameForFileGenerator, _fieldSetters := NewFilegenerator()
	_obj._S3BucketNameForFileGenerator = _S3BucketNameForFileGenerator
	helper.AddFieldSetters("s3bucketnameforfilegenerator", _fieldSetters, _setters)
	_DbStateConfig, _fieldSetters := NewDbStateConfig()
	_obj._DbStateConfig = _DbStateConfig
	helper.AddFieldSetters("dbstateconfig", _fieldSetters, _setters)
	_EscalationConfig, _fieldSetters := NewEscalationConfig()
	_obj._EscalationConfig = _EscalationConfig
	helper.AddFieldSetters("escalationconfig", _fieldSetters, _setters)
	_FederalEscalationUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FederalEscalationUpdateEventSubscriber = _FederalEscalationUpdateEventSubscriber
	helper.AddFieldSetters("federalescalationupdateeventsubscriber", _fieldSetters, _setters)
	_FederalEscalationCreationEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FederalEscalationCreationEventSubscriber = _FederalEscalationCreationEventSubscriber
	helper.AddFieldSetters("federalescalationcreationeventsubscriber", _fieldSetters, _setters)
	_FederalEscalationConfig, _fieldSetters := NewFederalEscalationConfig()
	_obj._FederalEscalationConfig = _FederalEscalationConfig
	helper.AddFieldSetters("federalescalationconfig", _fieldSetters, _setters)
	_SaClosureEligibilityConf, _fieldSetters := NewSaClosureEligibilityConf()
	_obj._SaClosureEligibilityConf = _SaClosureEligibilityConf
	helper.AddFieldSetters("saclosureeligibilityconf", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maxcountthresholdforfetchingbulkuserinfo":
		return obj.SetMaxCountThresholdForFetchingBulkUserInfo(v.MaxCountThresholdForFetchingBulkUserInfo, true, nil)
	case "usepkgratelimiter":
		return obj.SetUsePkgRateLimiter(v.UsePkgRateLimiter, true, nil)
	case "isredactionenabledfordbstates":
		return obj.SetIsRedactionEnabledForDBStates(v.IsRedactionEnabledForDBStates, true, nil)
	case "enablebalancemetricsoncasemanagement":
		return obj.SetEnableBalanceMetricsOnCaseManagement(v.EnableBalanceMetricsOnCaseManagement, true, nil)
	case "enableoutcalldataincasemanagementforriskops":
		return obj.SetEnableOutCallDataInCaseManagementForRiskOps(v.EnableOutCallDataInCaseManagementForRiskOps, true, nil)
	case "isnewoperationalstatusapienabled":
		return obj.SetIsNewOperationalStatusAPIEnabled(v.IsNewOperationalStatusAPIEnabled, true, nil)
	case "watchlistreasons":
		return obj.SetWatchlistReasons(v.WatchlistReasons, true, path)
	case "cxfreshdeskticketbaseurl":
		return obj.SetCXFreshdeskTicketBaseURL(v.CXFreshdeskTicketBaseURL, true, nil)
	case "emailverification":
		return obj._EmailVerification.Set(v.EmailVerification, true, path)
	case "mobilepromptverification":
		return obj._MobilePromptVerification.Set(v.MobilePromptVerification, true, path)
	case "customerauth":
		return obj._CustomerAuth.Set(v.CustomerAuth, true, path)
	case "freshdeskticketsubscriber":
		return obj._FreshdeskTicketSubscriber.Set(v.FreshdeskTicketSubscriber, true, path)
	case "freshdeskcontactsubscriber":
		return obj._FreshdeskContactSubscriber.Set(v.FreshdeskContactSubscriber, true, path)
	case "watsonincidentreportingsubscriber":
		return obj._WatsonIncidentReportingSubscriber.Set(v.WatsonIncidentReportingSubscriber, true, path)
	case "watsonincidentresolutionsubscriber":
		return obj._WatsonIncidentResolutionSubscriber.Set(v.WatsonIncidentResolutionSubscriber, true, path)
	case "watsonticketeventsubscriber":
		return obj._WatsonTicketEventSubscriber.Set(v.WatsonTicketEventSubscriber, true, path)
	case "watsoncreateticketsubscriber":
		return obj._WatsonCreateTicketSubscriber.Set(v.WatsonCreateTicketSubscriber, true, path)
	case "disputesubscriber":
		return obj._DisputeSubscriber.Set(v.DisputeSubscriber, true, path)
	case "disputecreateticketsubscriber":
		return obj._DisputeCreateTicketSubscriber.Set(v.DisputeCreateTicketSubscriber, true, path)
	case "disputeupdateticketsubscriber":
		return obj._DisputeUpdateTicketSubscriber.Set(v.DisputeUpdateTicketSubscriber, true, path)
	case "disputeaddnoteticketsubscriber":
		return obj._DisputeAddNoteTicketSubscriber.Set(v.DisputeAddNoteTicketSubscriber, true, path)
	case "devactionsubscriber":
		return obj._DevActionSubscriber.Set(v.DevActionSubscriber, true, path)
	case "freshdeskticketdataeventsubscriberfifo":
		return obj._FreshdeskTicketDataEventSubscriberFifo.Set(v.FreshdeskTicketDataEventSubscriberFifo, true, path)
	case "freshdeskticketdataeventsubscriber":
		return obj._FreshdeskTicketDataEventSubscriber.Set(v.FreshdeskTicketDataEventSubscriber, true, path)
	case "crmissuetrackerintegrationsubscriber":
		return obj._CrmIssueTrackerIntegrationSubscriber.Set(v.CrmIssueTrackerIntegrationSubscriber, true, path)
	case "dispute":
		return obj._Dispute.Set(v.Dispute, true, path)
	case "applog":
		return obj._AppLog.Set(v.AppLog, true, path)
	case "payout":
		return obj._Payout.Set(v.Payout, true, path)
	case "payoutstatuschecksubscriber":
		return obj._PayoutStatusCheckSubscriber.Set(v.PayoutStatusCheckSubscriber, true, path)
	case "waitlistsubscriber":
		return obj._WaitlistSubscriber.Set(v.WaitlistSubscriber, true, path)
	case "bulkuserinfoviaemailconfig":
		return obj._BulkUserInfoViaEmailConfig.Set(v.BulkUserInfoViaEmailConfig, true, path)
	case "upidisputeautoupdateeventsubscriber":
		return obj._UpiDisputeAutoUpdateEventSubscriber.Set(v.UpiDisputeAutoUpdateEventSubscriber, true, path)
	case "updateticketsubscriber":
		return obj._UpdateTicketSubscriber.Set(v.UpdateTicketSubscriber, true, path)
	case "createticketsubscriber":
		return obj._CreateTicketSubscriber.Set(v.CreateTicketSubscriber, true, path)
	case "ticketconfig":
		return obj._TicketConfig.Set(v.TicketConfig, true, path)
	case "callconfig":
		return obj._CallConfig.Set(v.CallConfig, true, path)
	case "ozonetelcalleventsubscriber":
		return obj._OzonetelCallEventSubscriber.Set(v.OzonetelCallEventSubscriber, true, path)
	case "callroutingconfig":
		return obj._CallRoutingConfig.Set(v.CallRoutingConfig, true, path)
	case "featurereleaseconfig":
		return obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, true, path)
	case "issueresolutionfeedbackconfig":
		return obj._IssueResolutionFeedbackConfig.Set(v.IssueResolutionFeedbackConfig, true, path)
	case "chatbotconfig":
		return obj._ChatBotConfig.Set(v.ChatBotConfig, true, path)
	case "freshchateventsubscriber":
		return obj._FreshchatEventSubscriber.Set(v.FreshchatEventSubscriber, true, path)
	case "riskconfig":
		return obj._RiskConfig.Set(v.RiskConfig, true, path)
	case "internationalfundtransfer":
		return obj._InternationalFundTransfer.Set(v.InternationalFundTransfer, true, path)
	case "salaryopsconfig":
		return obj._SalaryOpsConfig.Set(v.SalaryOpsConfig, true, path)
	case "landingpageconfig":
		return obj._LandingPageConfig.Set(v.LandingPageConfig, true, path)
	case "ticketreconciliationeventsubscriber":
		return obj._TicketReconciliationEventSubscriber.Set(v.TicketReconciliationEventSubscriber, true, path)
	case "devactionhelperconfig":
		return obj._DevActionHelperConfig.Set(v.DevActionHelperConfig, true, path)
	case "overridebankactions":
		return obj._OverrideBankActions.Set(v.OverrideBankActions, true, path)
	case "reviewaction":
		return obj._ReviewAction.Set(v.ReviewAction, true, path)
	case "vendoraccountpennydropviaemailconfig":
		return obj._VendorAccountPennyDropViaEmailConfig.Set(v.VendorAccountPennyDropViaEmailConfig, true, path)
	case "employerdbconfig":
		return obj._EmployerDbConfig.Set(v.EmployerDbConfig, true, path)
	case "grpcwebserverconfig":
		return obj._GRPCWebServerConfig.Set(v.GRPCWebServerConfig, true, path)
	case "sherlockbannersconfig":
		return obj._SherlockBannersConfig.Set(v.SherlockBannersConfig, true, path)
	case "ruddereventkafkaconsumergroup":
		return obj._RudderEventKafkaConsumerGroup.Set(v.RudderEventKafkaConsumerGroup, true, path)
	case "erroractivityconfig":
		return obj._ErrorActivityConfig.Set(v.ErrorActivityConfig, true, path)
	case "agentpromptconfig":
		return obj._AgentPromptConfig.Set(v.AgentPromptConfig, true, path)
	case "riskopsinstalledappsconfig":
		return obj._RiskOpsInstalledAppsConfig.Set(v.RiskOpsInstalledAppsConfig, true, path)
	case "riskfennelconfig":
		return obj._RiskFennelConfig.Set(v.RiskFennelConfig, true, path)
	case "stagewisecommsconfig":
		return obj._StageWiseCommsConfig.Set(v.StageWiseCommsConfig, true, path)
	case "issueconfigserviceconfig":
		return obj._IssueConfigServiceConfig.Set(v.IssueConfigServiceConfig, true, path)
	case "s3eventsubscriber":
		return obj._S3EventSubscriber.Set(v.S3EventSubscriber, true, path)
	case "s3eventconsumerconfig":
		return obj._S3EventConsumerConfig.Set(v.S3EventConsumerConfig, true, path)
	case "risktxnreviewrolloutconfig":
		return obj._RiskTxnReviewRolloutConfig.Set(v.RiskTxnReviewRolloutConfig, true, path)
	case "riskoutcallformrolloutconfig":
		return obj._RiskOutcallFormRolloutConfig.Set(v.RiskOutcallFormRolloutConfig, true, path)
	case "callivrconfig":
		return obj._CallIvrConfig.Set(v.CallIvrConfig, true, path)
	case "casemanagementactoractivities":
		return obj._CaseManagementActorActivities.Set(v.CaseManagementActorActivities, true, path)
	case "whitelistaccesslevelforactorenrichmentbyphoneoremail":
		return obj._WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail.Set(v.WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail, true, path)
	case "contactusmodelresponseconfig":
		return obj._ContactUsModelResponseConfig.Set(v.ContactUsModelResponseConfig, true, path)
	case "s3bucketnameforfilegenerator":
		return obj._S3BucketNameForFileGenerator.Set(v.S3BucketNameForFileGenerator, true, path)
	case "dbstateconfig":
		return obj._DbStateConfig.Set(v.DbStateConfig, true, path)
	case "escalationconfig":
		return obj._EscalationConfig.Set(v.EscalationConfig, true, path)
	case "federalescalationupdateeventsubscriber":
		return obj._FederalEscalationUpdateEventSubscriber.Set(v.FederalEscalationUpdateEventSubscriber, true, path)
	case "federalescalationcreationeventsubscriber":
		return obj._FederalEscalationCreationEventSubscriber.Set(v.FederalEscalationCreationEventSubscriber, true, path)
	case "federalescalationconfig":
		return obj._FederalEscalationConfig.Set(v.FederalEscalationConfig, true, path)
	case "saclosureeligibilityconf":
		return obj._SaClosureEligibilityConf.Set(v.SaClosureEligibilityConf, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj.SetMaxCountThresholdForFetchingBulkUserInfo(v.MaxCountThresholdForFetchingBulkUserInfo, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUsePkgRateLimiter(v.UsePkgRateLimiter, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsRedactionEnabledForDBStates(v.IsRedactionEnabledForDBStates, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableBalanceMetricsOnCaseManagement(v.EnableBalanceMetricsOnCaseManagement, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableOutCallDataInCaseManagementForRiskOps(v.EnableOutCallDataInCaseManagementForRiskOps, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsNewOperationalStatusAPIEnabled(v.IsNewOperationalStatusAPIEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetWatchlistReasons(v.WatchlistReasons, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetCXFreshdeskTicketBaseURL(v.CXFreshdeskTicketBaseURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._EmailVerification.Set(v.EmailVerification, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._MobilePromptVerification.Set(v.MobilePromptVerification, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CustomerAuth.Set(v.CustomerAuth, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FreshdeskTicketSubscriber.Set(v.FreshdeskTicketSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FreshdeskContactSubscriber.Set(v.FreshdeskContactSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._WatsonIncidentReportingSubscriber.Set(v.WatsonIncidentReportingSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._WatsonIncidentResolutionSubscriber.Set(v.WatsonIncidentResolutionSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._WatsonTicketEventSubscriber.Set(v.WatsonTicketEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._WatsonCreateTicketSubscriber.Set(v.WatsonCreateTicketSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DisputeSubscriber.Set(v.DisputeSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DisputeCreateTicketSubscriber.Set(v.DisputeCreateTicketSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DisputeUpdateTicketSubscriber.Set(v.DisputeUpdateTicketSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DisputeAddNoteTicketSubscriber.Set(v.DisputeAddNoteTicketSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DevActionSubscriber.Set(v.DevActionSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FreshdeskTicketDataEventSubscriberFifo.Set(v.FreshdeskTicketDataEventSubscriberFifo, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FreshdeskTicketDataEventSubscriber.Set(v.FreshdeskTicketDataEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CrmIssueTrackerIntegrationSubscriber.Set(v.CrmIssueTrackerIntegrationSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Dispute.Set(v.Dispute, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AppLog.Set(v.AppLog, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Payout.Set(v.Payout, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PayoutStatusCheckSubscriber.Set(v.PayoutStatusCheckSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._WaitlistSubscriber.Set(v.WaitlistSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._BulkUserInfoViaEmailConfig.Set(v.BulkUserInfoViaEmailConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UpiDisputeAutoUpdateEventSubscriber.Set(v.UpiDisputeAutoUpdateEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UpdateTicketSubscriber.Set(v.UpdateTicketSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreateTicketSubscriber.Set(v.CreateTicketSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TicketConfig.Set(v.TicketConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CallConfig.Set(v.CallConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OzonetelCallEventSubscriber.Set(v.OzonetelCallEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CallRoutingConfig.Set(v.CallRoutingConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._IssueResolutionFeedbackConfig.Set(v.IssueResolutionFeedbackConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ChatBotConfig.Set(v.ChatBotConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FreshchatEventSubscriber.Set(v.FreshchatEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RiskConfig.Set(v.RiskConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._InternationalFundTransfer.Set(v.InternationalFundTransfer, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SalaryOpsConfig.Set(v.SalaryOpsConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._LandingPageConfig.Set(v.LandingPageConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TicketReconciliationEventSubscriber.Set(v.TicketReconciliationEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DevActionHelperConfig.Set(v.DevActionHelperConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OverrideBankActions.Set(v.OverrideBankActions, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ReviewAction.Set(v.ReviewAction, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._VendorAccountPennyDropViaEmailConfig.Set(v.VendorAccountPennyDropViaEmailConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EmployerDbConfig.Set(v.EmployerDbConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._GRPCWebServerConfig.Set(v.GRPCWebServerConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SherlockBannersConfig.Set(v.SherlockBannersConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RudderEventKafkaConsumerGroup.Set(v.RudderEventKafkaConsumerGroup, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ErrorActivityConfig.Set(v.ErrorActivityConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AgentPromptConfig.Set(v.AgentPromptConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RiskOpsInstalledAppsConfig.Set(v.RiskOpsInstalledAppsConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RiskFennelConfig.Set(v.RiskFennelConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._StageWiseCommsConfig.Set(v.StageWiseCommsConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._IssueConfigServiceConfig.Set(v.IssueConfigServiceConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._S3EventSubscriber.Set(v.S3EventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._S3EventConsumerConfig.Set(v.S3EventConsumerConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RiskTxnReviewRolloutConfig.Set(v.RiskTxnReviewRolloutConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RiskOutcallFormRolloutConfig.Set(v.RiskOutcallFormRolloutConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CallIvrConfig.Set(v.CallIvrConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CaseManagementActorActivities.Set(v.CaseManagementActorActivities, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail.Set(v.WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ContactUsModelResponseConfig.Set(v.ContactUsModelResponseConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._S3BucketNameForFileGenerator.Set(v.S3BucketNameForFileGenerator, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DbStateConfig.Set(v.DbStateConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EscalationConfig.Set(v.EscalationConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FederalEscalationUpdateEventSubscriber.Set(v.FederalEscalationUpdateEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FederalEscalationCreationEventSubscriber.Set(v.FederalEscalationCreationEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FederalEscalationConfig.Set(v.FederalEscalationConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SaClosureEligibilityConf.Set(v.SaClosureEligibilityConf, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._Logging = v.Logging
	obj._EpifiDb = v.EpifiDb
	obj._Aws = v.Aws
	obj._Cognito = v.Cognito
	obj._AuthFactorRetryLimit = v.AuthFactorRetryLimit
	obj._FreshdeskTicketPublisher = v.FreshdeskTicketPublisher
	obj._FreshdeskContactPublisher = v.FreshdeskContactPublisher
	obj._WatsonIncidentReportingPublisher = v.WatsonIncidentReportingPublisher
	obj._WatsonIncidentResolutionPublisher = v.WatsonIncidentResolutionPublisher
	obj._WatsonTicketEventPublisher = v.WatsonTicketEventPublisher
	obj._DisputePublisher = v.DisputePublisher
	obj._DisputeCreateTicketPublisher = v.DisputeCreateTicketPublisher
	obj._DisputeUpdateTicketPublisher = v.DisputeUpdateTicketPublisher
	obj._DisputeAddNoteTicketPublisher = v.DisputeAddNoteTicketPublisher
	obj._DisputeExternalPublisher = v.DisputeExternalPublisher
	obj._RMSEventPublisher = v.RMSEventPublisher
	obj._RewardsManualGiveawayEventPublisher = v.RewardsManualGiveawayEventPublisher
	obj._DevActionPublisher = v.DevActionPublisher
	obj._UpdateTicketPublisher = v.UpdateTicketPublisher
	obj._CreateTicketPublisher = v.CreateTicketPublisher
	obj._CelestialSignalWorkflowPublisher = v.CelestialSignalWorkflowPublisher
	obj._IFTFileProcessorEventPublisher = v.IFTFileProcessorEventPublisher
	obj._RiskCasePublisher = v.RiskCasePublisher
	obj._RiskDisputePublisher = v.RiskDisputePublisher
	obj._CrmIssueTrackerIntegrationPublisher = v.CrmIssueTrackerIntegrationPublisher
	obj._AuditLog = v.AuditLog
	obj._Transaction = v.Transaction
	obj._Sherlock = v.Sherlock
	obj._Secrets = v.Secrets
	obj._RedisOptions = v.RedisOptions
	obj._Flags = v.Flags
	obj._Comms = v.Comms
	obj._PayoutStatusCheckPublisher = v.PayoutStatusCheckPublisher
	obj._DisputeNotificationTemplates = v.DisputeNotificationTemplates
	obj._OnboardingStageDetailsMapping = v.OnboardingStageDetailsMapping
	obj._AppLogsNotificationContent = v.AppLogsNotificationContent
	obj._KYCConfig = v.KYCConfig
	obj._RateLimit = v.RateLimit
	obj._RlConfig = v.RlConfig
	obj._CallRecording = v.CallRecording
	obj._RudderStack = v.RudderStack
	obj._CxEventAmountCategories = v.CxEventAmountCategories
	obj._OrderConfig = v.OrderConfig
	obj._ReferralConfig = v.ReferralConfig
	obj._LivenessVideoConfig = v.LivenessVideoConfig
	obj._SupportTicketFreshdeskConfig = v.SupportTicketFreshdeskConfig
	obj._ProcessTicketJobConfig = v.ProcessTicketJobConfig
	obj._CxS3Config = v.CxS3Config
	obj._RiskS3Config = v.RiskS3Config
	obj._DataS3Config = v.DataS3Config
	obj._EpifiIconS3Config = v.EpifiIconS3Config
	obj._PriorityRoutingConfig = v.PriorityRoutingConfig
	obj._FreshChatConfig = v.FreshChatConfig
	obj._AuthValidation = v.AuthValidation
	obj._BulkTicketJobConfig = v.BulkTicketJobConfig
	obj._Tracing = v.Tracing
	obj._Profiling = v.Profiling
	obj._UploadCreditMISToVendorPublisher = v.UploadCreditMISToVendorPublisher
	obj._BulkAccValidationViaEmailConfig = v.BulkAccValidationViaEmailConfig
	obj._AccountFreezeStatusConfig = v.AccountFreezeStatusConfig
	obj._SherlockFeedbackDetailsConfig = v.SherlockFeedbackDetailsConfig
	obj._SalaryProgramLeadManagementConfig = v.SalaryProgramLeadManagementConfig
	obj._WatsonConfig = v.WatsonConfig
	obj._SprinklrConfig = v.SprinklrConfig
	obj._UsStocksOpsConfig = v.UsStocksOpsConfig
	obj._ClosedAccountConfig = v.ClosedAccountConfig
	obj._MonorailConfig = v.MonorailConfig
	obj._AirflowConfig = v.AirflowConfig
	obj._FreshdeskMonorailIntegrationConfig = v.FreshdeskMonorailIntegrationConfig
	obj._EmailValidationRegex = v.EmailValidationRegex
	obj._SherlockUserRequestsConfig = v.SherlockUserRequestsConfig
	obj._IssueCategoryIdForCategory = v.IssueCategoryIdForCategory
	obj._RewardsOrderUpdateEventQueuePublisher = v.RewardsOrderUpdateEventQueuePublisher
	obj._RewardsCreditCardTxnEventQueuePublisher = v.RewardsCreditCardTxnEventQueuePublisher
	obj._CallRoutingEventPublisher = v.CallRoutingEventPublisher
	obj._TicketUpdateEventPublisher = v.TicketUpdateEventPublisher
	obj._CasperItcDownloadFileQueuePublisher = v.CasperItcDownloadFileQueuePublisher
	obj._InternationalFundsTransferConfig = v.InternationalFundsTransferConfig
	obj._StrapiConfig = v.StrapiConfig
	obj._OrderUpdateEventForTxnCategorizationPublisher = v.OrderUpdateEventForTxnCategorizationPublisher
	obj._AATxnCategorizationPublisher = v.AATxnCategorizationPublisher
	obj._CCTxnCategorizationPublisher = v.CCTxnCategorizationPublisher
	obj._CreateTicketEventPublisher = v.CreateTicketEventPublisher
	obj._FederalEscalationCreateEventPublisher = v.FederalEscalationCreateEventPublisher
	obj._LienConfig = v.LienConfig
	return nil
}

func (obj *Config) SetMaxCountThresholdForFetchingBulkUserInfo(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.MaxCountThresholdForFetchingBulkUserInfo", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxCountThresholdForFetchingBulkUserInfo, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxCountThresholdForFetchingBulkUserInfo")
	}
	return nil
}
func (obj *Config) SetUsePkgRateLimiter(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.UsePkgRateLimiter", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UsePkgRateLimiter, 1)
	} else {
		atomic.StoreUint32(&obj._UsePkgRateLimiter, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UsePkgRateLimiter")
	}
	return nil
}
func (obj *Config) SetIsRedactionEnabledForDBStates(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.IsRedactionEnabledForDBStates", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsRedactionEnabledForDBStates, 1)
	} else {
		atomic.StoreUint32(&obj._IsRedactionEnabledForDBStates, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsRedactionEnabledForDBStates")
	}
	return nil
}
func (obj *Config) SetEnableBalanceMetricsOnCaseManagement(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableBalanceMetricsOnCaseManagement", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableBalanceMetricsOnCaseManagement, 1)
	} else {
		atomic.StoreUint32(&obj._EnableBalanceMetricsOnCaseManagement, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableBalanceMetricsOnCaseManagement")
	}
	return nil
}
func (obj *Config) SetEnableOutCallDataInCaseManagementForRiskOps(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableOutCallDataInCaseManagementForRiskOps", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableOutCallDataInCaseManagementForRiskOps, 1)
	} else {
		atomic.StoreUint32(&obj._EnableOutCallDataInCaseManagementForRiskOps, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableOutCallDataInCaseManagementForRiskOps")
	}
	return nil
}
func (obj *Config) SetIsNewOperationalStatusAPIEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.IsNewOperationalStatusAPIEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsNewOperationalStatusAPIEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsNewOperationalStatusAPIEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsNewOperationalStatusAPIEnabled")
	}
	return nil
}
func (obj *Config) SetWatchlistReasons(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.WatchlistReasons", reflect.TypeOf(val))
	}
	obj._WatchlistReasonsMutex.Lock()
	defer obj._WatchlistReasonsMutex.Unlock()
	obj._WatchlistReasons = roarray.New[string](v)
	return nil
}
func (obj *Config) SetCXFreshdeskTicketBaseURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.CXFreshdeskTicketBaseURL", reflect.TypeOf(val))
	}
	obj._CXFreshdeskTicketBaseURLMutex.Lock()
	defer obj._CXFreshdeskTicketBaseURLMutex.Unlock()
	obj._CXFreshdeskTicketBaseURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CXFreshdeskTicketBaseURL")
	}
	return nil
}

func NewEmailVerification() (_obj *EmailVerification, _setters map[string]dynconf.SetFunc) {
	_obj = &EmailVerification{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["verificationurl"] = _obj.SetVerificationUrl
	_obj._VerificationUrlMutex = &sync.RWMutex{}
	_setters["fromemail"] = _obj.SetFromEmail
	_obj._FromEmailMutex = &sync.RWMutex{}
	_setters["fromemailname"] = _obj.SetFromEmailName
	_obj._FromEmailNameMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *EmailVerification) Init() {
	newObj, _ := NewEmailVerification()
	*obj = *newObj
}

func (obj *EmailVerification) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EmailVerification) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.EmailVerification)
	if !ok {
		return fmt.Errorf("invalid data type %v *EmailVerification", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EmailVerification) setDynamicField(v *config.EmailVerification, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "verificationurl":
		return obj.SetVerificationUrl(v.VerificationUrl, true, nil)
	case "fromemail":
		return obj.SetFromEmail(v.FromEmail, true, nil)
	case "fromemailname":
		return obj.SetFromEmailName(v.FromEmailName, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EmailVerification) setDynamicFields(v *config.EmailVerification, dynamic bool, path []string) (err error) {

	err = obj.SetVerificationUrl(v.VerificationUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFromEmail(v.FromEmail, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFromEmailName(v.FromEmailName, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EmailVerification) setStaticFields(v *config.EmailVerification) error {

	return nil
}

func (obj *EmailVerification) SetVerificationUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *EmailVerification.VerificationUrl", reflect.TypeOf(val))
	}
	obj._VerificationUrlMutex.Lock()
	defer obj._VerificationUrlMutex.Unlock()
	obj._VerificationUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "VerificationUrl")
	}
	return nil
}
func (obj *EmailVerification) SetFromEmail(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *EmailVerification.FromEmail", reflect.TypeOf(val))
	}
	obj._FromEmailMutex.Lock()
	defer obj._FromEmailMutex.Unlock()
	obj._FromEmail = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FromEmail")
	}
	return nil
}
func (obj *EmailVerification) SetFromEmailName(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *EmailVerification.FromEmailName", reflect.TypeOf(val))
	}
	obj._FromEmailNameMutex.Lock()
	defer obj._FromEmailNameMutex.Unlock()
	obj._FromEmailName = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FromEmailName")
	}
	return nil
}

func NewMobilePromptVerification() (_obj *MobilePromptVerification, _setters map[string]dynconf.SetFunc) {
	_obj = &MobilePromptVerification{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["validity"] = _obj.SetValidity
	_setters["notificationtitle"] = _obj.SetNotificationTitle
	_obj._NotificationTitleMutex = &sync.RWMutex{}
	_setters["notificationbody"] = _obj.SetNotificationBody
	_obj._NotificationBodyMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *MobilePromptVerification) Init() {
	newObj, _ := NewMobilePromptVerification()
	*obj = *newObj
}

func (obj *MobilePromptVerification) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *MobilePromptVerification) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.MobilePromptVerification)
	if !ok {
		return fmt.Errorf("invalid data type %v *MobilePromptVerification", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *MobilePromptVerification) setDynamicField(v *config.MobilePromptVerification, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "validity":
		return obj.SetValidity(v.Validity, true, nil)
	case "notificationtitle":
		return obj.SetNotificationTitle(v.NotificationTitle, true, nil)
	case "notificationbody":
		return obj.SetNotificationBody(v.NotificationBody, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *MobilePromptVerification) setDynamicFields(v *config.MobilePromptVerification, dynamic bool, path []string) (err error) {

	err = obj.SetValidity(v.Validity, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNotificationTitle(v.NotificationTitle, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNotificationBody(v.NotificationBody, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *MobilePromptVerification) setStaticFields(v *config.MobilePromptVerification) error {

	return nil
}

func (obj *MobilePromptVerification) SetValidity(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *MobilePromptVerification.Validity", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._Validity, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Validity")
	}
	return nil
}
func (obj *MobilePromptVerification) SetNotificationTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *MobilePromptVerification.NotificationTitle", reflect.TypeOf(val))
	}
	obj._NotificationTitleMutex.Lock()
	defer obj._NotificationTitleMutex.Unlock()
	obj._NotificationTitle = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "NotificationTitle")
	}
	return nil
}
func (obj *MobilePromptVerification) SetNotificationBody(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *MobilePromptVerification.NotificationBody", reflect.TypeOf(val))
	}
	obj._NotificationBodyMutex.Lock()
	defer obj._NotificationBodyMutex.Unlock()
	obj._NotificationBody = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "NotificationBody")
	}
	return nil
}

func NewCustomerAuth() (_obj *CustomerAuth, _setters map[string]dynconf.SetFunc) {
	_obj = &CustomerAuth{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isagentcachingenabled"] = _obj.SetIsAgentCachingEnabled
	_setters["isinappnotificationenabled"] = _obj.SetIsInAppNotificationEnabled
	_setters["isskippingauthenabledafterexternalauth"] = _obj.SetIsSkippingAuthEnabledAfterExternalAuth
	_setters["authfactorcachevalidityduration"] = _obj.SetAuthFactorCacheValidityDuration

	_obj._AuthFactorPriorityByPlatform = &syncmap.Map[string, *AuthFactorPriorityByPlatform]{}
	_setters["authfactorprioritybyplatform"] = _obj.SetAuthFactorPriorityByPlatform
	_setters["authfactorcachekey"] = _obj.SetAuthFactorCacheKey
	_obj._AuthFactorCacheKeyMutex = &sync.RWMutex{}
	_InAppNotificationTemplate, _fieldSetters := NewInAppNotificationTemplate()
	_obj._InAppNotificationTemplate = _InAppNotificationTemplate
	helper.AddFieldSetters("inappnotificationtemplate", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *CustomerAuth) Init() {
	newObj, _ := NewCustomerAuth()
	*obj = *newObj
}

func (obj *CustomerAuth) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CustomerAuth) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CustomerAuth)
	if !ok {
		return fmt.Errorf("invalid data type %v *CustomerAuth", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CustomerAuth) setDynamicField(v *config.CustomerAuth, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isagentcachingenabled":
		return obj.SetIsAgentCachingEnabled(v.IsAgentCachingEnabled, true, nil)
	case "isinappnotificationenabled":
		return obj.SetIsInAppNotificationEnabled(v.IsInAppNotificationEnabled, true, nil)
	case "isskippingauthenabledafterexternalauth":
		return obj.SetIsSkippingAuthEnabledAfterExternalAuth(v.IsSkippingAuthEnabledAfterExternalAuth, true, nil)
	case "authfactorcachevalidityduration":
		return obj.SetAuthFactorCacheValidityDuration(v.AuthFactorCacheValidityDuration, true, nil)
	case "authfactorprioritybyplatform":
		return obj.SetAuthFactorPriorityByPlatform(v.AuthFactorPriorityByPlatform, true, path)
	case "authfactorcachekey":
		return obj.SetAuthFactorCacheKey(v.AuthFactorCacheKey, true, nil)
	case "inappnotificationtemplate":
		return obj._InAppNotificationTemplate.Set(v.InAppNotificationTemplate, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CustomerAuth) setDynamicFields(v *config.CustomerAuth, dynamic bool, path []string) (err error) {

	err = obj.SetIsAgentCachingEnabled(v.IsAgentCachingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsInAppNotificationEnabled(v.IsInAppNotificationEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsSkippingAuthEnabledAfterExternalAuth(v.IsSkippingAuthEnabledAfterExternalAuth, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAuthFactorCacheValidityDuration(v.AuthFactorCacheValidityDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAuthFactorPriorityByPlatform(v.AuthFactorPriorityByPlatform, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetAuthFactorCacheKey(v.AuthFactorCacheKey, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._InAppNotificationTemplate.Set(v.InAppNotificationTemplate, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CustomerAuth) setStaticFields(v *config.CustomerAuth) error {

	obj._AuthValidityDuration = v.AuthValidityDuration
	obj._EmailValidityDuration = v.EmailValidityDuration
	obj._AgentCacheValidityDuration = v.AgentCacheValidityDuration
	obj._MobilePromptValidityDuration = v.MobilePromptValidityDuration
	obj._MaxResetCount = v.MaxResetCount
	return nil
}

func (obj *CustomerAuth) SetIsAgentCachingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CustomerAuth.IsAgentCachingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAgentCachingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsAgentCachingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAgentCachingEnabled")
	}
	return nil
}
func (obj *CustomerAuth) SetIsInAppNotificationEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CustomerAuth.IsInAppNotificationEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsInAppNotificationEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsInAppNotificationEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsInAppNotificationEnabled")
	}
	return nil
}
func (obj *CustomerAuth) SetIsSkippingAuthEnabledAfterExternalAuth(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CustomerAuth.IsSkippingAuthEnabledAfterExternalAuth", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsSkippingAuthEnabledAfterExternalAuth, 1)
	} else {
		atomic.StoreUint32(&obj._IsSkippingAuthEnabledAfterExternalAuth, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsSkippingAuthEnabledAfterExternalAuth")
	}
	return nil
}
func (obj *CustomerAuth) SetAuthFactorCacheValidityDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *CustomerAuth.AuthFactorCacheValidityDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._AuthFactorCacheValidityDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AuthFactorCacheValidityDuration")
	}
	return nil
}
func (obj *CustomerAuth) SetAuthFactorPriorityByPlatform(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.AuthFactorPriorityByPlatform)
	if !ok {
		return fmt.Errorf("invalid data type %v *CustomerAuth.AuthFactorPriorityByPlatform", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._AuthFactorPriorityByPlatform, v, dynamic, path)

}
func (obj *CustomerAuth) SetAuthFactorCacheKey(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CustomerAuth.AuthFactorCacheKey", reflect.TypeOf(val))
	}
	obj._AuthFactorCacheKeyMutex.Lock()
	defer obj._AuthFactorCacheKeyMutex.Unlock()
	obj._AuthFactorCacheKey = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "AuthFactorCacheKey")
	}
	return nil
}

func NewAuthFactorPriorityByPlatform() (_obj *AuthFactorPriorityByPlatform, _setters map[string]dynconf.SetFunc) {
	_obj = &AuthFactorPriorityByPlatform{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minappversion"] = _obj.SetMinAppVersion

	_obj._PriorityMap = &syncmap.Map[string, int32]{}
	_setters["prioritymap"] = _obj.SetPriorityMap
	return _obj, _setters
}

func (obj *AuthFactorPriorityByPlatform) Init() {
	newObj, _ := NewAuthFactorPriorityByPlatform()
	*obj = *newObj
}

func (obj *AuthFactorPriorityByPlatform) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AuthFactorPriorityByPlatform) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AuthFactorPriorityByPlatform)
	if !ok {
		return fmt.Errorf("invalid data type %v *AuthFactorPriorityByPlatform", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AuthFactorPriorityByPlatform) setDynamicField(v *config.AuthFactorPriorityByPlatform, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "minappversion":
		return obj.SetMinAppVersion(v.MinAppVersion, true, nil)
	case "prioritymap":
		return obj.SetPriorityMap(v.PriorityMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AuthFactorPriorityByPlatform) setDynamicFields(v *config.AuthFactorPriorityByPlatform, dynamic bool, path []string) (err error) {

	err = obj.SetMinAppVersion(v.MinAppVersion, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPriorityMap(v.PriorityMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AuthFactorPriorityByPlatform) setStaticFields(v *config.AuthFactorPriorityByPlatform) error {

	return nil
}

func (obj *AuthFactorPriorityByPlatform) SetMinAppVersion(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *AuthFactorPriorityByPlatform.MinAppVersion", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._MinAppVersion, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinAppVersion")
	}
	return nil
}
func (obj *AuthFactorPriorityByPlatform) SetPriorityMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *AuthFactorPriorityByPlatform.PriorityMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._PriorityMap, v, path)
}

func NewInAppNotificationTemplate() (_obj *InAppNotificationTemplate, _setters map[string]dynconf.SetFunc) {
	_obj = &InAppNotificationTemplate{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["notificationtimestampfontcolor"] = _obj.SetNotificationTimestampFontColor
	_obj._NotificationTimestampFontColorMutex = &sync.RWMutex{}
	_setters["iconurl"] = _obj.SetIconUrl
	_obj._IconUrlMutex = &sync.RWMutex{}
	_setters["iconbgcolor"] = _obj.SetIconBGColor
	_obj._IconBGColorMutex = &sync.RWMutex{}
	_setters["titlefontcolor"] = _obj.SetTitleFontColor
	_obj._TitleFontColorMutex = &sync.RWMutex{}
	_setters["bgcolor"] = _obj.SetBGColor
	_obj._BGColorMutex = &sync.RWMutex{}
	_setters["notificationdismissiconbgcolor"] = _obj.SetNotificationDismissIconBgColor
	_obj._NotificationDismissIconBgColorMutex = &sync.RWMutex{}
	_setters["notificationdismissiconcolor"] = _obj.SetNotificationDismissIconColor
	_obj._NotificationDismissIconColorMutex = &sync.RWMutex{}
	_setters["notificationshadowcolor"] = _obj.SetNotificationShadowColor
	_obj._NotificationShadowColorMutex = &sync.RWMutex{}
	_setters["mobileprompttitlepn"] = _obj.SetMobilePromptTitlePN
	_obj._MobilePromptTitlePNMutex = &sync.RWMutex{}
	_setters["mobileprompttitleinapp"] = _obj.SetMobilePromptTitleInApp
	_obj._MobilePromptTitleInAppMutex = &sync.RWMutex{}
	_setters["mobilepromptbody"] = _obj.SetMobilePromptBody
	_obj._MobilePromptBodyMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *InAppNotificationTemplate) Init() {
	newObj, _ := NewInAppNotificationTemplate()
	*obj = *newObj
}

func (obj *InAppNotificationTemplate) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *InAppNotificationTemplate) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.InAppNotificationTemplate)
	if !ok {
		return fmt.Errorf("invalid data type %v *InAppNotificationTemplate", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *InAppNotificationTemplate) setDynamicField(v *config.InAppNotificationTemplate, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "notificationtimestampfontcolor":
		return obj.SetNotificationTimestampFontColor(v.NotificationTimestampFontColor, true, nil)
	case "iconurl":
		return obj.SetIconUrl(v.IconUrl, true, nil)
	case "iconbgcolor":
		return obj.SetIconBGColor(v.IconBGColor, true, nil)
	case "titlefontcolor":
		return obj.SetTitleFontColor(v.TitleFontColor, true, nil)
	case "bgcolor":
		return obj.SetBGColor(v.BGColor, true, nil)
	case "notificationdismissiconbgcolor":
		return obj.SetNotificationDismissIconBgColor(v.NotificationDismissIconBgColor, true, nil)
	case "notificationdismissiconcolor":
		return obj.SetNotificationDismissIconColor(v.NotificationDismissIconColor, true, nil)
	case "notificationshadowcolor":
		return obj.SetNotificationShadowColor(v.NotificationShadowColor, true, nil)
	case "mobileprompttitlepn":
		return obj.SetMobilePromptTitlePN(v.MobilePromptTitlePN, true, nil)
	case "mobileprompttitleinapp":
		return obj.SetMobilePromptTitleInApp(v.MobilePromptTitleInApp, true, nil)
	case "mobilepromptbody":
		return obj.SetMobilePromptBody(v.MobilePromptBody, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *InAppNotificationTemplate) setDynamicFields(v *config.InAppNotificationTemplate, dynamic bool, path []string) (err error) {

	err = obj.SetNotificationTimestampFontColor(v.NotificationTimestampFontColor, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIconUrl(v.IconUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIconBGColor(v.IconBGColor, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitleFontColor(v.TitleFontColor, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBGColor(v.BGColor, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNotificationDismissIconBgColor(v.NotificationDismissIconBgColor, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNotificationDismissIconColor(v.NotificationDismissIconColor, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNotificationShadowColor(v.NotificationShadowColor, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMobilePromptTitlePN(v.MobilePromptTitlePN, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMobilePromptTitleInApp(v.MobilePromptTitleInApp, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMobilePromptBody(v.MobilePromptBody, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *InAppNotificationTemplate) setStaticFields(v *config.InAppNotificationTemplate) error {

	return nil
}

func (obj *InAppNotificationTemplate) SetNotificationTimestampFontColor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InAppNotificationTemplate.NotificationTimestampFontColor", reflect.TypeOf(val))
	}
	obj._NotificationTimestampFontColorMutex.Lock()
	defer obj._NotificationTimestampFontColorMutex.Unlock()
	obj._NotificationTimestampFontColor = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "NotificationTimestampFontColor")
	}
	return nil
}
func (obj *InAppNotificationTemplate) SetIconUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InAppNotificationTemplate.IconUrl", reflect.TypeOf(val))
	}
	obj._IconUrlMutex.Lock()
	defer obj._IconUrlMutex.Unlock()
	obj._IconUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconUrl")
	}
	return nil
}
func (obj *InAppNotificationTemplate) SetIconBGColor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InAppNotificationTemplate.IconBGColor", reflect.TypeOf(val))
	}
	obj._IconBGColorMutex.Lock()
	defer obj._IconBGColorMutex.Unlock()
	obj._IconBGColor = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconBGColor")
	}
	return nil
}
func (obj *InAppNotificationTemplate) SetTitleFontColor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InAppNotificationTemplate.TitleFontColor", reflect.TypeOf(val))
	}
	obj._TitleFontColorMutex.Lock()
	defer obj._TitleFontColorMutex.Unlock()
	obj._TitleFontColor = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "TitleFontColor")
	}
	return nil
}
func (obj *InAppNotificationTemplate) SetBGColor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InAppNotificationTemplate.BGColor", reflect.TypeOf(val))
	}
	obj._BGColorMutex.Lock()
	defer obj._BGColorMutex.Unlock()
	obj._BGColor = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BGColor")
	}
	return nil
}
func (obj *InAppNotificationTemplate) SetNotificationDismissIconBgColor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InAppNotificationTemplate.NotificationDismissIconBgColor", reflect.TypeOf(val))
	}
	obj._NotificationDismissIconBgColorMutex.Lock()
	defer obj._NotificationDismissIconBgColorMutex.Unlock()
	obj._NotificationDismissIconBgColor = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "NotificationDismissIconBgColor")
	}
	return nil
}
func (obj *InAppNotificationTemplate) SetNotificationDismissIconColor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InAppNotificationTemplate.NotificationDismissIconColor", reflect.TypeOf(val))
	}
	obj._NotificationDismissIconColorMutex.Lock()
	defer obj._NotificationDismissIconColorMutex.Unlock()
	obj._NotificationDismissIconColor = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "NotificationDismissIconColor")
	}
	return nil
}
func (obj *InAppNotificationTemplate) SetNotificationShadowColor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InAppNotificationTemplate.NotificationShadowColor", reflect.TypeOf(val))
	}
	obj._NotificationShadowColorMutex.Lock()
	defer obj._NotificationShadowColorMutex.Unlock()
	obj._NotificationShadowColor = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "NotificationShadowColor")
	}
	return nil
}
func (obj *InAppNotificationTemplate) SetMobilePromptTitlePN(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InAppNotificationTemplate.MobilePromptTitlePN", reflect.TypeOf(val))
	}
	obj._MobilePromptTitlePNMutex.Lock()
	defer obj._MobilePromptTitlePNMutex.Unlock()
	obj._MobilePromptTitlePN = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "MobilePromptTitlePN")
	}
	return nil
}
func (obj *InAppNotificationTemplate) SetMobilePromptTitleInApp(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InAppNotificationTemplate.MobilePromptTitleInApp", reflect.TypeOf(val))
	}
	obj._MobilePromptTitleInAppMutex.Lock()
	defer obj._MobilePromptTitleInAppMutex.Unlock()
	obj._MobilePromptTitleInApp = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "MobilePromptTitleInApp")
	}
	return nil
}
func (obj *InAppNotificationTemplate) SetMobilePromptBody(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InAppNotificationTemplate.MobilePromptBody", reflect.TypeOf(val))
	}
	obj._MobilePromptBodyMutex.Lock()
	defer obj._MobilePromptBodyMutex.Unlock()
	obj._MobilePromptBody = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "MobilePromptBody")
	}
	return nil
}

func NewDispute() (_obj *Dispute, _setters map[string]dynconf.SetFunc) {
	_obj = &Dispute{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isissueresolutionfeedbackcommsenabledfordispute"] = _obj.SetIsIssueResolutionFeedbackCommsEnabledForDispute
	_setters["isgetnextquestionsforappv2enabled"] = _obj.SetIsGetNextQuestionsForAppV2Enabled
	_setters["isgetnextquestionsv2enabled"] = _obj.SetIsGetNextQuestionsV2Enabled
	_setters["isupiexternalprovenanceevaluationenabled"] = _obj.SetIsUpiExternalProvenanceEvaluationEnabled
	_setters["disputeidempotencyttl"] = _obj.SetDisputeIdempotencyTTL
	_obj._DisputeIdempotencyTTLMutex = &sync.RWMutex{}
	_DisputeJobConfig, _fieldSetters := NewDisputeJobConfig()
	_obj._DisputeJobConfig = _DisputeJobConfig
	helper.AddFieldSetters("disputejobconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *Dispute) Init() {
	newObj, _ := NewDispute()
	*obj = *newObj
}

func (obj *Dispute) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Dispute) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Dispute)
	if !ok {
		return fmt.Errorf("invalid data type %v *Dispute", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Dispute) setDynamicField(v *config.Dispute, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isissueresolutionfeedbackcommsenabledfordispute":
		return obj.SetIsIssueResolutionFeedbackCommsEnabledForDispute(v.IsIssueResolutionFeedbackCommsEnabledForDispute, true, nil)
	case "isgetnextquestionsforappv2enabled":
		return obj.SetIsGetNextQuestionsForAppV2Enabled(v.IsGetNextQuestionsForAppV2Enabled, true, nil)
	case "isgetnextquestionsv2enabled":
		return obj.SetIsGetNextQuestionsV2Enabled(v.IsGetNextQuestionsV2Enabled, true, nil)
	case "isupiexternalprovenanceevaluationenabled":
		return obj.SetIsUpiExternalProvenanceEvaluationEnabled(v.IsUpiExternalProvenanceEvaluationEnabled, true, nil)
	case "disputeidempotencyttl":
		return obj.SetDisputeIdempotencyTTL(v.DisputeIdempotencyTTL, true, nil)
	case "disputejobconfig":
		return obj._DisputeJobConfig.Set(v.DisputeJobConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Dispute) setDynamicFields(v *config.Dispute, dynamic bool, path []string) (err error) {

	err = obj.SetIsIssueResolutionFeedbackCommsEnabledForDispute(v.IsIssueResolutionFeedbackCommsEnabledForDispute, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsGetNextQuestionsForAppV2Enabled(v.IsGetNextQuestionsForAppV2Enabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsGetNextQuestionsV2Enabled(v.IsGetNextQuestionsV2Enabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsUpiExternalProvenanceEvaluationEnabled(v.IsUpiExternalProvenanceEvaluationEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisputeIdempotencyTTL(v.DisputeIdempotencyTTL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._DisputeJobConfig.Set(v.DisputeJobConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Dispute) setStaticFields(v *config.Dispute) error {

	obj._GeneratedCsvFolder = v.GeneratedCsvFolder
	obj._S3BucketName = v.S3BucketName
	obj._MaxThresholdDurationForEscalation = v.MaxThresholdDurationForEscalation
	obj._MaxAttemptCountForReverseProcessing = v.MaxAttemptCountForReverseProcessing
	obj._DisputeUDIRConfig = v.DisputeUDIRConfig
	obj._UPIReceiverTypeQuestionCode = v.UPIReceiverTypeQuestionCode
	obj._DefaultDisputeConfigVersion = v.DefaultDisputeConfigVersion
	obj._IsRestrictedReleaseEnabledForConfigVersion = v.IsRestrictedReleaseEnabledForConfigVersion
	obj._DisputeConfigVersionToSherlockReleaseConfigMap = v.DisputeConfigVersionToSherlockReleaseConfigMap
	obj._DisputeConfigVersionAndPlatformToReleaseConfMap = v.DisputeConfigVersionAndPlatformToReleaseConfMap
	obj._ConfigVersionToDisputeConfigMap = v.ConfigVersionToDisputeConfigMap
	obj._DisputeAppCopies = v.DisputeAppCopies
	obj._MaxPageSize = v.MaxPageSize
	obj._MaximumDaysDuration = v.MaximumDaysDuration
	obj._ConfigVersionValidityToPreviousAttemptStatusEvaluationMap = v.ConfigVersionValidityToPreviousAttemptStatusEvaluationMap
	obj._MinThresholdDurationForStatusCheck = v.MinThresholdDurationForStatusCheck
	obj._TicketAlreadyExistsErrMsg = v.TicketAlreadyExistsErrMsg
	obj._DisputeDocumentLinkTagForSherlock = v.DisputeDocumentLinkTagForSherlock
	obj._IssueCategoryEligibilityForIssueResolutionFeedback = v.IssueCategoryEligibilityForIssueResolutionFeedback
	obj._DmpEmailConfig = v.DmpEmailConfig
	obj._DMPRaiseDisputeWindowConfig = v.DMPRaiseDisputeWindowConfig
	return nil
}

func (obj *Dispute) SetIsIssueResolutionFeedbackCommsEnabledForDispute(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Dispute.IsIssueResolutionFeedbackCommsEnabledForDispute", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsIssueResolutionFeedbackCommsEnabledForDispute, 1)
	} else {
		atomic.StoreUint32(&obj._IsIssueResolutionFeedbackCommsEnabledForDispute, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsIssueResolutionFeedbackCommsEnabledForDispute")
	}
	return nil
}
func (obj *Dispute) SetIsGetNextQuestionsForAppV2Enabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Dispute.IsGetNextQuestionsForAppV2Enabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsGetNextQuestionsForAppV2Enabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsGetNextQuestionsForAppV2Enabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsGetNextQuestionsForAppV2Enabled")
	}
	return nil
}
func (obj *Dispute) SetIsGetNextQuestionsV2Enabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Dispute.IsGetNextQuestionsV2Enabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsGetNextQuestionsV2Enabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsGetNextQuestionsV2Enabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsGetNextQuestionsV2Enabled")
	}
	return nil
}
func (obj *Dispute) SetIsUpiExternalProvenanceEvaluationEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Dispute.IsUpiExternalProvenanceEvaluationEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsUpiExternalProvenanceEvaluationEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsUpiExternalProvenanceEvaluationEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsUpiExternalProvenanceEvaluationEnabled")
	}
	return nil
}
func (obj *Dispute) SetDisputeIdempotencyTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Dispute.DisputeIdempotencyTTL", reflect.TypeOf(val))
	}
	obj._DisputeIdempotencyTTLMutex.Lock()
	defer obj._DisputeIdempotencyTTLMutex.Unlock()
	obj._DisputeIdempotencyTTL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisputeIdempotencyTTL")
	}
	return nil
}

func NewDisputeJobConfig() (_obj *DisputeJobConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DisputeJobConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["reverseprocessingdelaydurationbetweentickets"] = _obj.SetReverseProcessingDelayDurationBetweenTickets
	_obj._ReverseProcessingDelayDurationBetweenTicketsMutex = &sync.RWMutex{}
	_setters["reverseprocessingjobtimeout"] = _obj.SetReverseProcessingJobTimeout
	_obj._ReverseProcessingJobTimeoutMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *DisputeJobConfig) Init() {
	newObj, _ := NewDisputeJobConfig()
	*obj = *newObj
}

func (obj *DisputeJobConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DisputeJobConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DisputeJobConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *DisputeJobConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DisputeJobConfig) setDynamicField(v *config.DisputeJobConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "reverseprocessingdelaydurationbetweentickets":
		return obj.SetReverseProcessingDelayDurationBetweenTickets(v.ReverseProcessingDelayDurationBetweenTickets, true, nil)
	case "reverseprocessingjobtimeout":
		return obj.SetReverseProcessingJobTimeout(v.ReverseProcessingJobTimeout, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DisputeJobConfig) setDynamicFields(v *config.DisputeJobConfig, dynamic bool, path []string) (err error) {

	err = obj.SetReverseProcessingDelayDurationBetweenTickets(v.ReverseProcessingDelayDurationBetweenTickets, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetReverseProcessingJobTimeout(v.ReverseProcessingJobTimeout, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DisputeJobConfig) setStaticFields(v *config.DisputeJobConfig) error {

	obj._EscalationJobTimeout = v.EscalationJobTimeout
	return nil
}

func (obj *DisputeJobConfig) SetReverseProcessingDelayDurationBetweenTickets(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *DisputeJobConfig.ReverseProcessingDelayDurationBetweenTickets", reflect.TypeOf(val))
	}
	obj._ReverseProcessingDelayDurationBetweenTicketsMutex.Lock()
	defer obj._ReverseProcessingDelayDurationBetweenTicketsMutex.Unlock()
	obj._ReverseProcessingDelayDurationBetweenTickets = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ReverseProcessingDelayDurationBetweenTickets")
	}
	return nil
}
func (obj *DisputeJobConfig) SetReverseProcessingJobTimeout(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *DisputeJobConfig.ReverseProcessingJobTimeout", reflect.TypeOf(val))
	}
	obj._ReverseProcessingJobTimeoutMutex.Lock()
	defer obj._ReverseProcessingJobTimeoutMutex.Unlock()
	obj._ReverseProcessingJobTimeout = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ReverseProcessingJobTimeout")
	}
	return nil
}

func NewAppLog() (_obj *AppLog, _setters map[string]dynconf.SetFunc) {
	_obj = &AppLog{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["logttl"] = _obj.SetLogTTL
	_obj._LogTTLMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *AppLog) Init() {
	newObj, _ := NewAppLog()
	*obj = *newObj
}

func (obj *AppLog) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AppLog) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AppLog)
	if !ok {
		return fmt.Errorf("invalid data type %v *AppLog", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AppLog) setDynamicField(v *config.AppLog, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "logttl":
		return obj.SetLogTTL(v.LogTTL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AppLog) setDynamicFields(v *config.AppLog, dynamic bool, path []string) (err error) {

	err = obj.SetLogTTL(v.LogTTL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AppLog) setStaticFields(v *config.AppLog) error {

	obj._MaxLogCountPerUser = v.MaxLogCountPerUser
	obj._LogChunkSize = v.LogChunkSize
	return nil
}

func (obj *AppLog) SetLogTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AppLog.LogTTL", reflect.TypeOf(val))
	}
	obj._LogTTLMutex.Lock()
	defer obj._LogTTLMutex.Unlock()
	obj._LogTTL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "LogTTL")
	}
	return nil
}

func NewPayout() (_obj *Payout, _setters map[string]dynconf.SetFunc) {
	_obj = &Payout{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_CashPayout, _fieldSetters := NewCashPayout()
	_obj._CashPayout = _CashPayout
	helper.AddFieldSetters("cashpayout", _fieldSetters, _setters)
	_FiCoinsPayout, _fieldSetters := NewFiCoinsPayout()
	_obj._FiCoinsPayout = _FiCoinsPayout
	helper.AddFieldSetters("ficoinspayout", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *Payout) Init() {
	newObj, _ := NewPayout()
	*obj = *newObj
}

func (obj *Payout) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Payout) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Payout)
	if !ok {
		return fmt.Errorf("invalid data type %v *Payout", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Payout) setDynamicField(v *config.Payout, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "cashpayout":
		return obj._CashPayout.Set(v.CashPayout, true, path)
	case "ficoinspayout":
		return obj._FiCoinsPayout.Set(v.FiCoinsPayout, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Payout) setDynamicFields(v *config.Payout, dynamic bool, path []string) (err error) {

	err = obj._CashPayout.Set(v.CashPayout, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FiCoinsPayout.Set(v.FiCoinsPayout, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Payout) setStaticFields(v *config.Payout) error {

	obj._StatusCheckDelay = v.StatusCheckDelay
	obj._MaxNumberOfPayoutsAllowedPerTicket = v.MaxNumberOfPayoutsAllowedPerTicket
	return nil
}

func NewCashPayout() (_obj *CashPayout, _setters map[string]dynconf.SetFunc) {
	_obj = &CashPayout{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableb2ctransactionviacelestial"] = _obj.SetEnableB2CTransactionViaCelestial
	return _obj, _setters
}

func (obj *CashPayout) Init() {
	newObj, _ := NewCashPayout()
	*obj = *newObj
}

func (obj *CashPayout) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CashPayout) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CashPayout)
	if !ok {
		return fmt.Errorf("invalid data type %v *CashPayout", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CashPayout) setDynamicField(v *config.CashPayout, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enableb2ctransactionviacelestial":
		return obj.SetEnableB2CTransactionViaCelestial(v.EnableB2CTransactionViaCelestial, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CashPayout) setDynamicFields(v *config.CashPayout, dynamic bool, path []string) (err error) {

	err = obj.SetEnableB2CTransactionViaCelestial(v.EnableB2CTransactionViaCelestial, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CashPayout) setStaticFields(v *config.CashPayout) error {

	obj._MaxPayoutValueAllowedPerTicket = v.MaxPayoutValueAllowedPerTicket
	obj._MinPayoutValueAllowedPerTicket = v.MinPayoutValueAllowedPerTicket
	obj._SingleAmountLimitForApproval = v.SingleAmountLimitForApproval
	obj._MaxPayoutAllowedInTimeframe = v.MaxPayoutAllowedInTimeframe
	obj._TimeframeDuration = v.TimeframeDuration
	obj._FromActorIdForOrder = v.FromActorIdForOrder
	obj._FromPiIdForOrder = v.FromPiIdForOrder
	return nil
}

func (obj *CashPayout) SetEnableB2CTransactionViaCelestial(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CashPayout.EnableB2CTransactionViaCelestial", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableB2CTransactionViaCelestial, 1)
	} else {
		atomic.StoreUint32(&obj._EnableB2CTransactionViaCelestial, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableB2CTransactionViaCelestial")
	}
	return nil
}

func NewFiCoinsPayout() (_obj *FiCoinsPayout, _setters map[string]dynconf.SetFunc) {
	_obj = &FiCoinsPayout{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ispayoutviaficoinsenabled"] = _obj.SetIsPayoutViaFiCoinsEnabled
	return _obj, _setters
}

func (obj *FiCoinsPayout) Init() {
	newObj, _ := NewFiCoinsPayout()
	*obj = *newObj
}

func (obj *FiCoinsPayout) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FiCoinsPayout) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.FiCoinsPayout)
	if !ok {
		return fmt.Errorf("invalid data type %v *FiCoinsPayout", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FiCoinsPayout) setDynamicField(v *config.FiCoinsPayout, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "ispayoutviaficoinsenabled":
		return obj.SetIsPayoutViaFiCoinsEnabled(v.IsPayoutViaFiCoinsEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FiCoinsPayout) setDynamicFields(v *config.FiCoinsPayout, dynamic bool, path []string) (err error) {

	err = obj.SetIsPayoutViaFiCoinsEnabled(v.IsPayoutViaFiCoinsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FiCoinsPayout) setStaticFields(v *config.FiCoinsPayout) error {

	obj._MaxPayoutValueAllowedPerTicket = v.MaxPayoutValueAllowedPerTicket
	obj._SingleAmountLimitForApproval = v.SingleAmountLimitForApproval
	obj._MaxAmountDisbursalInDurationByRole = v.MaxAmountDisbursalInDurationByRole
	obj._MaxAmountDisbursalPerUser = v.MaxAmountDisbursalPerUser
	obj._FiCoinsRewardOfferId = v.FiCoinsRewardOfferId
	obj._AllowedValues = v.AllowedValues
	return nil
}

func (obj *FiCoinsPayout) SetIsPayoutViaFiCoinsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FiCoinsPayout.IsPayoutViaFiCoinsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsPayoutViaFiCoinsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsPayoutViaFiCoinsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsPayoutViaFiCoinsEnabled")
	}
	return nil
}

func NewBulkUserInfoViaEmailConfig() (_obj *BulkUserInfoViaEmailConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &BulkUserInfoViaEmailConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxcountthreshold"] = _obj.SetMaxCountThreshold
	_setters["fromemailid"] = _obj.SetFromEmailId
	_obj._FromEmailIdMutex = &sync.RWMutex{}
	_setters["fromemailname"] = _obj.SetFromEmailName
	_obj._FromEmailNameMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *BulkUserInfoViaEmailConfig) Init() {
	newObj, _ := NewBulkUserInfoViaEmailConfig()
	*obj = *newObj
}

func (obj *BulkUserInfoViaEmailConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *BulkUserInfoViaEmailConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.BulkUserInfoViaEmailConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *BulkUserInfoViaEmailConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *BulkUserInfoViaEmailConfig) setDynamicField(v *config.BulkUserInfoViaEmailConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maxcountthreshold":
		return obj.SetMaxCountThreshold(v.MaxCountThreshold, true, nil)
	case "fromemailid":
		return obj.SetFromEmailId(v.FromEmailId, true, nil)
	case "fromemailname":
		return obj.SetFromEmailName(v.FromEmailName, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *BulkUserInfoViaEmailConfig) setDynamicFields(v *config.BulkUserInfoViaEmailConfig, dynamic bool, path []string) (err error) {

	err = obj.SetMaxCountThreshold(v.MaxCountThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFromEmailId(v.FromEmailId, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFromEmailName(v.FromEmailName, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *BulkUserInfoViaEmailConfig) setStaticFields(v *config.BulkUserInfoViaEmailConfig) error {

	obj._FieldToCsvColNameMap = v.FieldToCsvColNameMap
	obj._RoleToAllowedRespFieldsMap = v.RoleToAllowedRespFieldsMap
	return nil
}

func (obj *BulkUserInfoViaEmailConfig) SetMaxCountThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *BulkUserInfoViaEmailConfig.MaxCountThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxCountThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxCountThreshold")
	}
	return nil
}
func (obj *BulkUserInfoViaEmailConfig) SetFromEmailId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *BulkUserInfoViaEmailConfig.FromEmailId", reflect.TypeOf(val))
	}
	obj._FromEmailIdMutex.Lock()
	defer obj._FromEmailIdMutex.Unlock()
	obj._FromEmailId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FromEmailId")
	}
	return nil
}
func (obj *BulkUserInfoViaEmailConfig) SetFromEmailName(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *BulkUserInfoViaEmailConfig.FromEmailName", reflect.TypeOf(val))
	}
	obj._FromEmailNameMutex.Lock()
	defer obj._FromEmailNameMutex.Unlock()
	obj._FromEmailName = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FromEmailName")
	}
	return nil
}

func NewTicketConfig() (_obj *TicketConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &TicketConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isticketlistloggingenabled"] = _obj.SetIsTicketListLoggingEnabled
	_setters["isticketeventloggingenabled"] = _obj.SetIsTicketEventLoggingEnabled
	_setters["isdefaulttitleanddescriptionenabledforinappticket"] = _obj.SetIsDefaultTitleAndDescriptionEnabledForInAppTicket
	_setters["isticketupdateeventpublishingenabled"] = _obj.SetIsTicketUpdateEventPublishingEnabled
	_setters["ticketfieldcachevalidityduration"] = _obj.SetTicketFieldCacheValidityDuration
	_setters["latestticketcachevalidityduration"] = _obj.SetLatestTicketCacheValidityDuration
	_setters["defaultapptickettitle"] = _obj.SetDefaultAppTicketTitle
	_obj._DefaultAppTicketTitleMutex = &sync.RWMutex{}
	_setters["defaultappticketdescription"] = _obj.SetDefaultAppTicketDescription
	_obj._DefaultAppTicketDescriptionMutex = &sync.RWMutex{}
	_ShowTicketsInAppConfig, _fieldSetters := NewShowTicketsInAppConfig()
	_obj._ShowTicketsInAppConfig = _ShowTicketsInAppConfig
	helper.AddFieldSetters("showticketsinappconfig", _fieldSetters, _setters)
	_SLAConfig, _fieldSetters := NewSLAConfig()
	_obj._SLAConfig = _SLAConfig
	helper.AddFieldSetters("slaconfig", _fieldSetters, _setters)
	_CsatConfig, _fieldSetters := NewCsatConfig()
	_obj._CsatConfig = _CsatConfig
	helper.AddFieldSetters("csatconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *TicketConfig) Init() {
	newObj, _ := NewTicketConfig()
	*obj = *newObj
}

func (obj *TicketConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *TicketConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.TicketConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *TicketConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *TicketConfig) setDynamicField(v *config.TicketConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isticketlistloggingenabled":
		return obj.SetIsTicketListLoggingEnabled(v.IsTicketListLoggingEnabled, true, nil)
	case "isticketeventloggingenabled":
		return obj.SetIsTicketEventLoggingEnabled(v.IsTicketEventLoggingEnabled, true, nil)
	case "isdefaulttitleanddescriptionenabledforinappticket":
		return obj.SetIsDefaultTitleAndDescriptionEnabledForInAppTicket(v.IsDefaultTitleAndDescriptionEnabledForInAppTicket, true, nil)
	case "isticketupdateeventpublishingenabled":
		return obj.SetIsTicketUpdateEventPublishingEnabled(v.IsTicketUpdateEventPublishingEnabled, true, nil)
	case "ticketfieldcachevalidityduration":
		return obj.SetTicketFieldCacheValidityDuration(v.TicketFieldCacheValidityDuration, true, nil)
	case "latestticketcachevalidityduration":
		return obj.SetLatestTicketCacheValidityDuration(v.LatestTicketCacheValidityDuration, true, nil)
	case "defaultapptickettitle":
		return obj.SetDefaultAppTicketTitle(v.DefaultAppTicketTitle, true, nil)
	case "defaultappticketdescription":
		return obj.SetDefaultAppTicketDescription(v.DefaultAppTicketDescription, true, nil)
	case "showticketsinappconfig":
		return obj._ShowTicketsInAppConfig.Set(v.ShowTicketsInAppConfig, true, path)
	case "slaconfig":
		return obj._SLAConfig.Set(v.SLAConfig, true, path)
	case "csatconfig":
		return obj._CsatConfig.Set(v.CsatConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *TicketConfig) setDynamicFields(v *config.TicketConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsTicketListLoggingEnabled(v.IsTicketListLoggingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsTicketEventLoggingEnabled(v.IsTicketEventLoggingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsDefaultTitleAndDescriptionEnabledForInAppTicket(v.IsDefaultTitleAndDescriptionEnabledForInAppTicket, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsTicketUpdateEventPublishingEnabled(v.IsTicketUpdateEventPublishingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTicketFieldCacheValidityDuration(v.TicketFieldCacheValidityDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLatestTicketCacheValidityDuration(v.LatestTicketCacheValidityDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDefaultAppTicketTitle(v.DefaultAppTicketTitle, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDefaultAppTicketDescription(v.DefaultAppTicketDescription, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._ShowTicketsInAppConfig.Set(v.ShowTicketsInAppConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SLAConfig.Set(v.SLAConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CsatConfig.Set(v.CsatConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *TicketConfig) setStaticFields(v *config.TicketConfig) error {

	obj._URL = v.URL
	obj._IsStatusResolvedMap = v.IsStatusResolvedMap
	obj._BulkResolutionModeValue = v.BulkResolutionModeValue
	obj._EscalationTeamEnumToValueMapping = v.EscalationTeamEnumToValueMapping
	obj._ProductCategoryFieldId = v.ProductCategoryFieldId
	return nil
}

func (obj *TicketConfig) SetIsTicketListLoggingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TicketConfig.IsTicketListLoggingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsTicketListLoggingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsTicketListLoggingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsTicketListLoggingEnabled")
	}
	return nil
}
func (obj *TicketConfig) SetIsTicketEventLoggingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TicketConfig.IsTicketEventLoggingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsTicketEventLoggingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsTicketEventLoggingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsTicketEventLoggingEnabled")
	}
	return nil
}
func (obj *TicketConfig) SetIsDefaultTitleAndDescriptionEnabledForInAppTicket(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TicketConfig.IsDefaultTitleAndDescriptionEnabledForInAppTicket", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsDefaultTitleAndDescriptionEnabledForInAppTicket, 1)
	} else {
		atomic.StoreUint32(&obj._IsDefaultTitleAndDescriptionEnabledForInAppTicket, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsDefaultTitleAndDescriptionEnabledForInAppTicket")
	}
	return nil
}
func (obj *TicketConfig) SetIsTicketUpdateEventPublishingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TicketConfig.IsTicketUpdateEventPublishingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsTicketUpdateEventPublishingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsTicketUpdateEventPublishingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsTicketUpdateEventPublishingEnabled")
	}
	return nil
}
func (obj *TicketConfig) SetTicketFieldCacheValidityDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *TicketConfig.TicketFieldCacheValidityDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._TicketFieldCacheValidityDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "TicketFieldCacheValidityDuration")
	}
	return nil
}
func (obj *TicketConfig) SetLatestTicketCacheValidityDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *TicketConfig.LatestTicketCacheValidityDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._LatestTicketCacheValidityDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "LatestTicketCacheValidityDuration")
	}
	return nil
}
func (obj *TicketConfig) SetDefaultAppTicketTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *TicketConfig.DefaultAppTicketTitle", reflect.TypeOf(val))
	}
	obj._DefaultAppTicketTitleMutex.Lock()
	defer obj._DefaultAppTicketTitleMutex.Unlock()
	obj._DefaultAppTicketTitle = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DefaultAppTicketTitle")
	}
	return nil
}
func (obj *TicketConfig) SetDefaultAppTicketDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *TicketConfig.DefaultAppTicketDescription", reflect.TypeOf(val))
	}
	obj._DefaultAppTicketDescriptionMutex.Lock()
	defer obj._DefaultAppTicketDescriptionMutex.Unlock()
	obj._DefaultAppTicketDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DefaultAppTicketDescription")
	}
	return nil
}

func NewShowTicketsInAppConfig() (_obj *ShowTicketsInAppConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ShowTicketsInAppConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["timelimitforupdatingticketdetails"] = _obj.SetTimeLimitForUpdatingTicketDetails
	_setters["whitelistedproductcategories"] = _obj.SetWhitelistedProductCategories
	_obj._WhitelistedProductCategoriesMutex = &sync.RWMutex{}
	_setters["mandatoryfieldsrequiredtoshowticket"] = _obj.SetMandatoryFieldsRequiredToShowTicket
	_obj._MandatoryFieldsRequiredToShowTicketMutex = &sync.RWMutex{}
	_setters["cutoffdatetoshowtickets"] = _obj.SetCutOffDateToShowTickets
	_obj._CutOffDateToShowTicketsMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *ShowTicketsInAppConfig) Init() {
	newObj, _ := NewShowTicketsInAppConfig()
	*obj = *newObj
}

func (obj *ShowTicketsInAppConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ShowTicketsInAppConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ShowTicketsInAppConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ShowTicketsInAppConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ShowTicketsInAppConfig) setDynamicField(v *config.ShowTicketsInAppConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "timelimitforupdatingticketdetails":
		return obj.SetTimeLimitForUpdatingTicketDetails(v.TimeLimitForUpdatingTicketDetails, true, nil)
	case "whitelistedproductcategories":
		return obj.SetWhitelistedProductCategories(v.WhitelistedProductCategories, true, path)
	case "mandatoryfieldsrequiredtoshowticket":
		return obj.SetMandatoryFieldsRequiredToShowTicket(v.MandatoryFieldsRequiredToShowTicket, true, path)
	case "cutoffdatetoshowtickets":
		return obj.SetCutOffDateToShowTickets(v.CutOffDateToShowTickets, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ShowTicketsInAppConfig) setDynamicFields(v *config.ShowTicketsInAppConfig, dynamic bool, path []string) (err error) {

	err = obj.SetTimeLimitForUpdatingTicketDetails(v.TimeLimitForUpdatingTicketDetails, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetWhitelistedProductCategories(v.WhitelistedProductCategories, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetMandatoryFieldsRequiredToShowTicket(v.MandatoryFieldsRequiredToShowTicket, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetCutOffDateToShowTickets(v.CutOffDateToShowTickets, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ShowTicketsInAppConfig) setStaticFields(v *config.ShowTicketsInAppConfig) error {

	obj._CreatedByEnumToValueMapping = v.CreatedByEnumToValueMapping
	obj._CreationModeEnumToValueMapping = v.CreationModeEnumToValueMapping
	return nil
}

func (obj *ShowTicketsInAppConfig) SetTimeLimitForUpdatingTicketDetails(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *ShowTicketsInAppConfig.TimeLimitForUpdatingTicketDetails", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._TimeLimitForUpdatingTicketDetails, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "TimeLimitForUpdatingTicketDetails")
	}
	return nil
}
func (obj *ShowTicketsInAppConfig) SetWhitelistedProductCategories(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ShowTicketsInAppConfig.WhitelistedProductCategories", reflect.TypeOf(val))
	}
	obj._WhitelistedProductCategoriesMutex.Lock()
	defer obj._WhitelistedProductCategoriesMutex.Unlock()
	obj._WhitelistedProductCategories = roarray.New[string](v)
	return nil
}
func (obj *ShowTicketsInAppConfig) SetMandatoryFieldsRequiredToShowTicket(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ShowTicketsInAppConfig.MandatoryFieldsRequiredToShowTicket", reflect.TypeOf(val))
	}
	obj._MandatoryFieldsRequiredToShowTicketMutex.Lock()
	defer obj._MandatoryFieldsRequiredToShowTicketMutex.Unlock()
	obj._MandatoryFieldsRequiredToShowTicket = roarray.New[string](v)
	return nil
}
func (obj *ShowTicketsInAppConfig) SetCutOffDateToShowTickets(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Time)
	if !ok {
		return fmt.Errorf("invalid data type %v *ShowTicketsInAppConfig.CutOffDateToShowTickets", reflect.TypeOf(val))
	}
	obj._CutOffDateToShowTicketsMutex.Lock()
	defer obj._CutOffDateToShowTicketsMutex.Unlock()
	obj._CutOffDateToShowTickets = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CutOffDateToShowTickets")
	}
	return nil
}

func NewSLAConfig() (_obj *SLAConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &SLAConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isslacalculationenabledinticketconsumer"] = _obj.SetIsSLACalculationEnabledInTicketConsumer
	_setters["isexpectedresolutionbyfielddeterminedusingsla"] = _obj.SetIsExpectedResolutionByFieldDeterminedUsingSLA
	_setters["iseventpublishedtoupdateexpectedresolutiontimefieldonfreshdesk"] = _obj.SetIsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk
	return _obj, _setters
}

func (obj *SLAConfig) Init() {
	newObj, _ := NewSLAConfig()
	*obj = *newObj
}

func (obj *SLAConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SLAConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SLAConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *SLAConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SLAConfig) setDynamicField(v *config.SLAConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isslacalculationenabledinticketconsumer":
		return obj.SetIsSLACalculationEnabledInTicketConsumer(v.IsSLACalculationEnabledInTicketConsumer, true, nil)
	case "isexpectedresolutionbyfielddeterminedusingsla":
		return obj.SetIsExpectedResolutionByFieldDeterminedUsingSLA(v.IsExpectedResolutionByFieldDeterminedUsingSLA, true, nil)
	case "iseventpublishedtoupdateexpectedresolutiontimefieldonfreshdesk":
		return obj.SetIsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk(v.IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SLAConfig) setDynamicFields(v *config.SLAConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsSLACalculationEnabledInTicketConsumer(v.IsSLACalculationEnabledInTicketConsumer, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsExpectedResolutionByFieldDeterminedUsingSLA(v.IsExpectedResolutionByFieldDeterminedUsingSLA, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk(v.IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SLAConfig) setStaticFields(v *config.SLAConfig) error {

	return nil
}

func (obj *SLAConfig) SetIsSLACalculationEnabledInTicketConsumer(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *SLAConfig.IsSLACalculationEnabledInTicketConsumer", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsSLACalculationEnabledInTicketConsumer, 1)
	} else {
		atomic.StoreUint32(&obj._IsSLACalculationEnabledInTicketConsumer, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsSLACalculationEnabledInTicketConsumer")
	}
	return nil
}
func (obj *SLAConfig) SetIsExpectedResolutionByFieldDeterminedUsingSLA(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *SLAConfig.IsExpectedResolutionByFieldDeterminedUsingSLA", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsExpectedResolutionByFieldDeterminedUsingSLA, 1)
	} else {
		atomic.StoreUint32(&obj._IsExpectedResolutionByFieldDeterminedUsingSLA, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsExpectedResolutionByFieldDeterminedUsingSLA")
	}
	return nil
}
func (obj *SLAConfig) SetIsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *SLAConfig.IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk, 1)
	} else {
		atomic.StoreUint32(&obj._IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk")
	}
	return nil
}

func NewCsatConfig() (_obj *CsatConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CsatConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscsatcollectionenabledviaweb"] = _obj.SetIsCsatCollectionEnabledViaWeb
	_setters["csateligibilitywindow"] = _obj.SetCsatEligibilityWindow
	_setters["allowedticketstatusesforcsat"] = _obj.SetAllowedTicketStatusesForCsat
	_obj._AllowedTicketStatusesForCsatMutex = &sync.RWMutex{}
	_setters["allowedcommstypeforcsat"] = _obj.SetAllowedCommsTypeForCsat
	_obj._AllowedCommsTypeForCsatMutex = &sync.RWMutex{}
	_setters["pushnotificationtitle"] = _obj.SetPushNotificationTitle
	_obj._PushNotificationTitleMutex = &sync.RWMutex{}
	_setters["pushnotificationdescription"] = _obj.SetPushNotificationDescription
	_obj._PushNotificationDescriptionMutex = &sync.RWMutex{}
	_setters["commsexternalrefidprefix"] = _obj.SetCommsExternalRefIdPrefix
	_obj._CommsExternalRefIdPrefixMutex = &sync.RWMutex{}
	_setters["webformurl"] = _obj.SetWebFormUrl
	_obj._WebFormUrlMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *CsatConfig) Init() {
	newObj, _ := NewCsatConfig()
	*obj = *newObj
}

func (obj *CsatConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CsatConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CsatConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CsatConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CsatConfig) setDynamicField(v *config.CsatConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscsatcollectionenabledviaweb":
		return obj.SetIsCsatCollectionEnabledViaWeb(v.IsCsatCollectionEnabledViaWeb, true, nil)
	case "csateligibilitywindow":
		return obj.SetCsatEligibilityWindow(v.CsatEligibilityWindow, true, nil)
	case "allowedticketstatusesforcsat":
		return obj.SetAllowedTicketStatusesForCsat(v.AllowedTicketStatusesForCsat, true, path)
	case "allowedcommstypeforcsat":
		return obj.SetAllowedCommsTypeForCsat(v.AllowedCommsTypeForCsat, true, path)
	case "pushnotificationtitle":
		return obj.SetPushNotificationTitle(v.PushNotificationTitle, true, nil)
	case "pushnotificationdescription":
		return obj.SetPushNotificationDescription(v.PushNotificationDescription, true, nil)
	case "commsexternalrefidprefix":
		return obj.SetCommsExternalRefIdPrefix(v.CommsExternalRefIdPrefix, true, nil)
	case "webformurl":
		return obj.SetWebFormUrl(v.WebFormUrl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CsatConfig) setDynamicFields(v *config.CsatConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCsatCollectionEnabledViaWeb(v.IsCsatCollectionEnabledViaWeb, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCsatEligibilityWindow(v.CsatEligibilityWindow, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAllowedTicketStatusesForCsat(v.AllowedTicketStatusesForCsat, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetAllowedCommsTypeForCsat(v.AllowedCommsTypeForCsat, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetPushNotificationTitle(v.PushNotificationTitle, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPushNotificationDescription(v.PushNotificationDescription, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCommsExternalRefIdPrefix(v.CommsExternalRefIdPrefix, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetWebFormUrl(v.WebFormUrl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CsatConfig) setStaticFields(v *config.CsatConfig) error {

	return nil
}

func (obj *CsatConfig) SetIsCsatCollectionEnabledViaWeb(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CsatConfig.IsCsatCollectionEnabledViaWeb", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCsatCollectionEnabledViaWeb, 1)
	} else {
		atomic.StoreUint32(&obj._IsCsatCollectionEnabledViaWeb, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCsatCollectionEnabledViaWeb")
	}
	return nil
}
func (obj *CsatConfig) SetCsatEligibilityWindow(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *CsatConfig.CsatEligibilityWindow", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CsatEligibilityWindow, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CsatEligibilityWindow")
	}
	return nil
}
func (obj *CsatConfig) SetAllowedTicketStatusesForCsat(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CsatConfig.AllowedTicketStatusesForCsat", reflect.TypeOf(val))
	}
	obj._AllowedTicketStatusesForCsatMutex.Lock()
	defer obj._AllowedTicketStatusesForCsatMutex.Unlock()
	obj._AllowedTicketStatusesForCsat = roarray.New[string](v)
	return nil
}
func (obj *CsatConfig) SetAllowedCommsTypeForCsat(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CsatConfig.AllowedCommsTypeForCsat", reflect.TypeOf(val))
	}
	obj._AllowedCommsTypeForCsatMutex.Lock()
	defer obj._AllowedCommsTypeForCsatMutex.Unlock()
	obj._AllowedCommsTypeForCsat = roarray.New[string](v)
	return nil
}
func (obj *CsatConfig) SetPushNotificationTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CsatConfig.PushNotificationTitle", reflect.TypeOf(val))
	}
	obj._PushNotificationTitleMutex.Lock()
	defer obj._PushNotificationTitleMutex.Unlock()
	obj._PushNotificationTitle = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "PushNotificationTitle")
	}
	return nil
}
func (obj *CsatConfig) SetPushNotificationDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CsatConfig.PushNotificationDescription", reflect.TypeOf(val))
	}
	obj._PushNotificationDescriptionMutex.Lock()
	defer obj._PushNotificationDescriptionMutex.Unlock()
	obj._PushNotificationDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "PushNotificationDescription")
	}
	return nil
}
func (obj *CsatConfig) SetCommsExternalRefIdPrefix(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CsatConfig.CommsExternalRefIdPrefix", reflect.TypeOf(val))
	}
	obj._CommsExternalRefIdPrefixMutex.Lock()
	defer obj._CommsExternalRefIdPrefixMutex.Unlock()
	obj._CommsExternalRefIdPrefix = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CommsExternalRefIdPrefix")
	}
	return nil
}
func (obj *CsatConfig) SetWebFormUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CsatConfig.WebFormUrl", reflect.TypeOf(val))
	}
	obj._WebFormUrlMutex.Lock()
	defer obj._WebFormUrlMutex.Unlock()
	obj._WebFormUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "WebFormUrl")
	}
	return nil
}

func NewCallConfig() (_obj *CallConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CallConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isconsumereventloggingenabled"] = _obj.SetIsConsumerEventLoggingEnabled
	_setters["iscallblockerenabled"] = _obj.SetIsCallBlockerEnabled
	_setters["iscallblockingenabledviaivrflow"] = _obj.SetIsCallBlockingEnabledViaIvrFlow
	_AbandonedCallConfig, _fieldSetters := NewAbandonedCallConfig()
	_obj._AbandonedCallConfig = _AbandonedCallConfig
	helper.AddFieldSetters("abandonedcallconfig", _fieldSetters, _setters)
	_CallBlockerTestConfig, _fieldSetters := NewCallBlockerTestConfig()
	_obj._CallBlockerTestConfig = _CallBlockerTestConfig
	helper.AddFieldSetters("callblockertestconfig", _fieldSetters, _setters)
	_CallBlockerConfig, _fieldSetters := NewCallBlockerConfig()
	_obj._CallBlockerConfig = _CallBlockerConfig
	helper.AddFieldSetters("callblockerconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *CallConfig) Init() {
	newObj, _ := NewCallConfig()
	*obj = *newObj
}

func (obj *CallConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CallConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CallConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CallConfig) setDynamicField(v *config.CallConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isconsumereventloggingenabled":
		return obj.SetIsConsumerEventLoggingEnabled(v.IsConsumerEventLoggingEnabled, true, nil)
	case "iscallblockerenabled":
		return obj.SetIsCallBlockerEnabled(v.IsCallBlockerEnabled, true, nil)
	case "iscallblockingenabledviaivrflow":
		return obj.SetIsCallBlockingEnabledViaIvrFlow(v.IsCallBlockingEnabledViaIvrFlow, true, nil)
	case "abandonedcallconfig":
		return obj._AbandonedCallConfig.Set(v.AbandonedCallConfig, true, path)
	case "callblockertestconfig":
		return obj._CallBlockerTestConfig.Set(v.CallBlockerTestConfig, true, path)
	case "callblockerconfig":
		return obj._CallBlockerConfig.Set(v.CallBlockerConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CallConfig) setDynamicFields(v *config.CallConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsConsumerEventLoggingEnabled(v.IsConsumerEventLoggingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsCallBlockerEnabled(v.IsCallBlockerEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsCallBlockingEnabledViaIvrFlow(v.IsCallBlockingEnabledViaIvrFlow, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._AbandonedCallConfig.Set(v.AbandonedCallConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CallBlockerTestConfig.Set(v.CallBlockerTestConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CallBlockerConfig.Set(v.CallBlockerConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CallConfig) setStaticFields(v *config.CallConfig) error {

	obj._MaximumDaysDuration = v.MaximumDaysDuration
	obj._MaxPageSize = v.MaxPageSize
	obj._CallStartTicketSubject = v.CallStartTicketSubject
	obj._CallStartTicketDescription = v.CallStartTicketDescription
	obj._UpdateTicketErrMsg = v.UpdateTicketErrMsg
	obj._TicketAlreadyExistsErrMsg = v.TicketAlreadyExistsErrMsg
	obj._AgentAssignmentErrMsg = v.AgentAssignmentErrMsg
	obj._TicketDbUpdateErrMsg = v.TicketDbUpdateErrMsg
	obj._FreshdeskTicketCreationErr = v.FreshdeskTicketCreationErr
	obj._CallEndTicketDescription = v.CallEndTicketDescription
	obj._CallEndTicketPrivateNote = v.CallEndTicketPrivateNote
	obj._RecordingFilePath = v.RecordingFilePath
	obj._RecordingFileNotFoundErrMsg = v.RecordingFileNotFoundErrMsg
	obj._RecordingLinkTagForSherlock = v.RecordingLinkTagForSherlock
	return nil
}

func (obj *CallConfig) SetIsConsumerEventLoggingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallConfig.IsConsumerEventLoggingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsConsumerEventLoggingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsConsumerEventLoggingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsConsumerEventLoggingEnabled")
	}
	return nil
}
func (obj *CallConfig) SetIsCallBlockerEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallConfig.IsCallBlockerEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCallBlockerEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCallBlockerEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCallBlockerEnabled")
	}
	return nil
}
func (obj *CallConfig) SetIsCallBlockingEnabledViaIvrFlow(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallConfig.IsCallBlockingEnabledViaIvrFlow", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCallBlockingEnabledViaIvrFlow, 1)
	} else {
		atomic.StoreUint32(&obj._IsCallBlockingEnabledViaIvrFlow, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCallBlockingEnabledViaIvrFlow")
	}
	return nil
}

func NewAbandonedCallConfig() (_obj *AbandonedCallConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &AbandonedCallConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isabandonedcallcommsenabled"] = _obj.SetIsAbandonedCallCommsEnabled
	_NotificationTemplate, _fieldSetters := genpay.NewNotificationTemplateParams()
	_obj._NotificationTemplate = _NotificationTemplate
	helper.AddFieldSetters("notificationtemplate", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *AbandonedCallConfig) Init() {
	newObj, _ := NewAbandonedCallConfig()
	*obj = *newObj
}

func (obj *AbandonedCallConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AbandonedCallConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AbandonedCallConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *AbandonedCallConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AbandonedCallConfig) setDynamicField(v *config.AbandonedCallConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isabandonedcallcommsenabled":
		return obj.SetIsAbandonedCallCommsEnabled(v.IsAbandonedCallCommsEnabled, true, nil)
	case "notificationtemplate":
		return obj._NotificationTemplate.Set(v.NotificationTemplate, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AbandonedCallConfig) setDynamicFields(v *config.AbandonedCallConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsAbandonedCallCommsEnabled(v.IsAbandonedCallCommsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._NotificationTemplate.Set(v.NotificationTemplate, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AbandonedCallConfig) setStaticFields(v *config.AbandonedCallConfig) error {

	return nil
}

func (obj *AbandonedCallConfig) SetIsAbandonedCallCommsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AbandonedCallConfig.IsAbandonedCallCommsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsAbandonedCallCommsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsAbandonedCallCommsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsAbandonedCallCommsEnabled")
	}
	return nil
}

func NewCallBlockerTestConfig() (_obj *CallBlockerTestConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CallBlockerTestConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["unregistereduser"] = _obj.SetUnRegisteredUser
	_setters["appaccessblocked"] = _obj.SetAppAccessBlocked
	_setters["userreportedissue"] = _obj.SetUserReportedIssue
	_setters["iscreditfreeze"] = _obj.SetIsCreditFreeze
	_setters["isstandardtier"] = _obj.SetIsStandardTier
	_setters["isriskblocked"] = _obj.SetIsRiskBlocked
	return _obj, _setters
}

func (obj *CallBlockerTestConfig) Init() {
	newObj, _ := NewCallBlockerTestConfig()
	*obj = *newObj
}

func (obj *CallBlockerTestConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CallBlockerTestConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CallBlockerTestConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerTestConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CallBlockerTestConfig) setDynamicField(v *config.CallBlockerTestConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "unregistereduser":
		return obj.SetUnRegisteredUser(v.UnRegisteredUser, true, nil)
	case "appaccessblocked":
		return obj.SetAppAccessBlocked(v.AppAccessBlocked, true, nil)
	case "userreportedissue":
		return obj.SetUserReportedIssue(v.UserReportedIssue, true, nil)
	case "iscreditfreeze":
		return obj.SetIsCreditFreeze(v.IsCreditFreeze, true, nil)
	case "isstandardtier":
		return obj.SetIsStandardTier(v.IsStandardTier, true, nil)
	case "isriskblocked":
		return obj.SetIsRiskBlocked(v.IsRiskBlocked, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CallBlockerTestConfig) setDynamicFields(v *config.CallBlockerTestConfig, dynamic bool, path []string) (err error) {

	err = obj.SetUnRegisteredUser(v.UnRegisteredUser, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAppAccessBlocked(v.AppAccessBlocked, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUserReportedIssue(v.UserReportedIssue, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsCreditFreeze(v.IsCreditFreeze, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsStandardTier(v.IsStandardTier, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsRiskBlocked(v.IsRiskBlocked, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CallBlockerTestConfig) setStaticFields(v *config.CallBlockerTestConfig) error {

	return nil
}

func (obj *CallBlockerTestConfig) SetUnRegisteredUser(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerTestConfig.UnRegisteredUser", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UnRegisteredUser, 1)
	} else {
		atomic.StoreUint32(&obj._UnRegisteredUser, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UnRegisteredUser")
	}
	return nil
}
func (obj *CallBlockerTestConfig) SetAppAccessBlocked(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerTestConfig.AppAccessBlocked", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._AppAccessBlocked, 1)
	} else {
		atomic.StoreUint32(&obj._AppAccessBlocked, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "AppAccessBlocked")
	}
	return nil
}
func (obj *CallBlockerTestConfig) SetUserReportedIssue(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerTestConfig.UserReportedIssue", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UserReportedIssue, 1)
	} else {
		atomic.StoreUint32(&obj._UserReportedIssue, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UserReportedIssue")
	}
	return nil
}
func (obj *CallBlockerTestConfig) SetIsCreditFreeze(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerTestConfig.IsCreditFreeze", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCreditFreeze, 1)
	} else {
		atomic.StoreUint32(&obj._IsCreditFreeze, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCreditFreeze")
	}
	return nil
}
func (obj *CallBlockerTestConfig) SetIsStandardTier(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerTestConfig.IsStandardTier", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsStandardTier, 1)
	} else {
		atomic.StoreUint32(&obj._IsStandardTier, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsStandardTier")
	}
	return nil
}
func (obj *CallBlockerTestConfig) SetIsRiskBlocked(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerTestConfig.IsRiskBlocked", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsRiskBlocked, 1)
	} else {
		atomic.StoreUint32(&obj._IsRiskBlocked, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsRiskBlocked")
	}
	return nil
}

func NewCallBlockerConfig() (_obj *CallBlockerConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CallBlockerConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscallblockerenabled"] = _obj.SetIsCallBlockerEnabled
	_setters["iscallblockingenabledviaivrflow"] = _obj.SetIsCallBlockingEnabledViaIvrFlow
	_setters["requesttimeout"] = _obj.SetRequestTimeout
	_setters["blocktierlist"] = _obj.SetBlockTierList
	_obj._BlockTierListMutex = &sync.RWMutex{}
	_setters["triagedtierlist"] = _obj.SetTriagedTierList
	_obj._TriagedTierListMutex = &sync.RWMutex{}
	_setters["calldropoffnotificationtitle"] = _obj.SetCallDropOffNotificationTitle
	_obj._CallDropOffNotificationTitleMutex = &sync.RWMutex{}
	_setters["calldropoffnotificationbody"] = _obj.SetCallDropOffNotificationBody
	_obj._CallDropOffNotificationBodyMutex = &sync.RWMutex{}
	_setters["contactusflowsmslink"] = _obj.SetContactUsFlowSmsLink
	_obj._ContactUsFlowSmsLinkMutex = &sync.RWMutex{}
	_setters["fiappdownloadlink"] = _obj.SetFiAppDownloadLink
	_obj._FiAppDownloadLinkMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *CallBlockerConfig) Init() {
	newObj, _ := NewCallBlockerConfig()
	*obj = *newObj
}

func (obj *CallBlockerConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CallBlockerConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CallBlockerConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CallBlockerConfig) setDynamicField(v *config.CallBlockerConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscallblockerenabled":
		return obj.SetIsCallBlockerEnabled(v.IsCallBlockerEnabled, true, nil)
	case "iscallblockingenabledviaivrflow":
		return obj.SetIsCallBlockingEnabledViaIvrFlow(v.IsCallBlockingEnabledViaIvrFlow, true, nil)
	case "requesttimeout":
		return obj.SetRequestTimeout(v.RequestTimeout, true, nil)
	case "blocktierlist":
		return obj.SetBlockTierList(v.BlockTierList, true, path)
	case "triagedtierlist":
		return obj.SetTriagedTierList(v.TriagedTierList, true, path)
	case "calldropoffnotificationtitle":
		return obj.SetCallDropOffNotificationTitle(v.CallDropOffNotificationTitle, true, nil)
	case "calldropoffnotificationbody":
		return obj.SetCallDropOffNotificationBody(v.CallDropOffNotificationBody, true, nil)
	case "contactusflowsmslink":
		return obj.SetContactUsFlowSmsLink(v.ContactUsFlowSmsLink, true, nil)
	case "fiappdownloadlink":
		return obj.SetFiAppDownloadLink(v.FiAppDownloadLink, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CallBlockerConfig) setDynamicFields(v *config.CallBlockerConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCallBlockerEnabled(v.IsCallBlockerEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsCallBlockingEnabledViaIvrFlow(v.IsCallBlockingEnabledViaIvrFlow, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRequestTimeout(v.RequestTimeout, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBlockTierList(v.BlockTierList, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetTriagedTierList(v.TriagedTierList, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetCallDropOffNotificationTitle(v.CallDropOffNotificationTitle, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCallDropOffNotificationBody(v.CallDropOffNotificationBody, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetContactUsFlowSmsLink(v.ContactUsFlowSmsLink, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFiAppDownloadLink(v.FiAppDownloadLink, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CallBlockerConfig) setStaticFields(v *config.CallBlockerConfig) error {

	return nil
}

func (obj *CallBlockerConfig) SetIsCallBlockerEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerConfig.IsCallBlockerEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCallBlockerEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCallBlockerEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCallBlockerEnabled")
	}
	return nil
}
func (obj *CallBlockerConfig) SetIsCallBlockingEnabledViaIvrFlow(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerConfig.IsCallBlockingEnabledViaIvrFlow", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCallBlockingEnabledViaIvrFlow, 1)
	} else {
		atomic.StoreUint32(&obj._IsCallBlockingEnabledViaIvrFlow, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCallBlockingEnabledViaIvrFlow")
	}
	return nil
}
func (obj *CallBlockerConfig) SetRequestTimeout(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerConfig.RequestTimeout", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._RequestTimeout, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RequestTimeout")
	}
	return nil
}
func (obj *CallBlockerConfig) SetBlockTierList(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerConfig.BlockTierList", reflect.TypeOf(val))
	}
	obj._BlockTierListMutex.Lock()
	defer obj._BlockTierListMutex.Unlock()
	obj._BlockTierList = roarray.New[string](v)
	return nil
}
func (obj *CallBlockerConfig) SetTriagedTierList(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerConfig.TriagedTierList", reflect.TypeOf(val))
	}
	obj._TriagedTierListMutex.Lock()
	defer obj._TriagedTierListMutex.Unlock()
	obj._TriagedTierList = roarray.New[string](v)
	return nil
}
func (obj *CallBlockerConfig) SetCallDropOffNotificationTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerConfig.CallDropOffNotificationTitle", reflect.TypeOf(val))
	}
	obj._CallDropOffNotificationTitleMutex.Lock()
	defer obj._CallDropOffNotificationTitleMutex.Unlock()
	obj._CallDropOffNotificationTitle = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CallDropOffNotificationTitle")
	}
	return nil
}
func (obj *CallBlockerConfig) SetCallDropOffNotificationBody(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerConfig.CallDropOffNotificationBody", reflect.TypeOf(val))
	}
	obj._CallDropOffNotificationBodyMutex.Lock()
	defer obj._CallDropOffNotificationBodyMutex.Unlock()
	obj._CallDropOffNotificationBody = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CallDropOffNotificationBody")
	}
	return nil
}
func (obj *CallBlockerConfig) SetContactUsFlowSmsLink(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerConfig.ContactUsFlowSmsLink", reflect.TypeOf(val))
	}
	obj._ContactUsFlowSmsLinkMutex.Lock()
	defer obj._ContactUsFlowSmsLinkMutex.Unlock()
	obj._ContactUsFlowSmsLink = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ContactUsFlowSmsLink")
	}
	return nil
}
func (obj *CallBlockerConfig) SetFiAppDownloadLink(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallBlockerConfig.FiAppDownloadLink", reflect.TypeOf(val))
	}
	obj._FiAppDownloadLinkMutex.Lock()
	defer obj._FiAppDownloadLinkMutex.Unlock()
	obj._FiAppDownloadLink = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FiAppDownloadLink")
	}
	return nil
}

func NewCallRoutingConfig() (_obj *CallRoutingConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CallRoutingConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["defaultpriorityvalue"] = _obj.SetDefaultPriorityValue
	_setters["maxpriorityvalue"] = _obj.SetMaxPriorityValue
	_setters["calldropoffcountthreshold"] = _obj.SetCallDropOffCountThreshold
	_setters["halvingfactor"] = _obj.SetHalvingFactor
	_setters["pastcallrecordslookupsize"] = _obj.SetPastCallRecordsLookupSize
	_setters["ismanualroutingenabled"] = _obj.SetIsManualRoutingEnabled
	_setters["issalaryprogramroutingenabled"] = _obj.SetIsSalaryProgramRoutingEnabled
	_setters["iscreditcardroutingenabled"] = _obj.SetIsCreditCardRoutingEnabled
	_setters["isactiveloanroutingenabled"] = _obj.SetIsActiveLoanRoutingEnabled
	_setters["ispriorityroutingenabled"] = _obj.SetIsPriorityRoutingEnabled
	_setters["isrecordedmessageeventenabled"] = _obj.SetIsRecordedMessageEventEnabled
	_setters["calllangpreferenceslist"] = _obj.SetCallLangPreferencesList
	_obj._CallLangPreferencesListMutex = &sync.RWMutex{}
	_setters["calllangsuggestionslist"] = _obj.SetCallLangSuggestionsList
	_obj._CallLangSuggestionsListMutex = &sync.RWMutex{}
	_setters["queuewaittimedurationthreshold"] = _obj.SetQueueWaitTimeDurationThreshold
	_obj._QueueWaitTimeDurationThresholdMutex = &sync.RWMutex{}
	_setters["pastcallrecordslookupduration"] = _obj.SetPastCallRecordsLookupDuration
	_obj._PastCallRecordsLookupDurationMutex = &sync.RWMutex{}
	_BlockedAccountCallRoutingConfig, _fieldSetters := NewBlockedAccountCallRoutingConfig()
	_obj._BlockedAccountCallRoutingConfig = _BlockedAccountCallRoutingConfig
	helper.AddFieldSetters("blockedaccountcallroutingconfig", _fieldSetters, _setters)
	_PreRecordedMessageConfig, _fieldSetters := NewPreRecordedMessageConfig()
	_obj._PreRecordedMessageConfig = _PreRecordedMessageConfig
	helper.AddFieldSetters("prerecordedmessageconfig", _fieldSetters, _setters)
	_IssuePriorityCacheConfig, _fieldSetters := NewIssuePriorityCacheConfig()
	_obj._IssuePriorityCacheConfig = _IssuePriorityCacheConfig
	helper.AddFieldSetters("issueprioritycacheconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *CallRoutingConfig) Init() {
	newObj, _ := NewCallRoutingConfig()
	*obj = *newObj
}

func (obj *CallRoutingConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CallRoutingConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CallRoutingConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CallRoutingConfig) setDynamicField(v *config.CallRoutingConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "defaultpriorityvalue":
		return obj.SetDefaultPriorityValue(v.DefaultPriorityValue, true, nil)
	case "maxpriorityvalue":
		return obj.SetMaxPriorityValue(v.MaxPriorityValue, true, nil)
	case "calldropoffcountthreshold":
		return obj.SetCallDropOffCountThreshold(v.CallDropOffCountThreshold, true, nil)
	case "halvingfactor":
		return obj.SetHalvingFactor(v.HalvingFactor, true, nil)
	case "pastcallrecordslookupsize":
		return obj.SetPastCallRecordsLookupSize(v.PastCallRecordsLookupSize, true, nil)
	case "ismanualroutingenabled":
		return obj.SetIsManualRoutingEnabled(v.IsManualRoutingEnabled, true, nil)
	case "issalaryprogramroutingenabled":
		return obj.SetIsSalaryProgramRoutingEnabled(v.IsSalaryProgramRoutingEnabled, true, nil)
	case "iscreditcardroutingenabled":
		return obj.SetIsCreditCardRoutingEnabled(v.IsCreditCardRoutingEnabled, true, nil)
	case "isactiveloanroutingenabled":
		return obj.SetIsActiveLoanRoutingEnabled(v.IsActiveLoanRoutingEnabled, true, nil)
	case "ispriorityroutingenabled":
		return obj.SetIsPriorityRoutingEnabled(v.IsPriorityRoutingEnabled, true, nil)
	case "isrecordedmessageeventenabled":
		return obj.SetIsRecordedMessageEventEnabled(v.IsRecordedMessageEventEnabled, true, nil)
	case "calllangpreferenceslist":
		return obj.SetCallLangPreferencesList(v.CallLangPreferencesList, true, path)
	case "calllangsuggestionslist":
		return obj.SetCallLangSuggestionsList(v.CallLangSuggestionsList, true, path)
	case "queuewaittimedurationthreshold":
		return obj.SetQueueWaitTimeDurationThreshold(v.QueueWaitTimeDurationThreshold, true, nil)
	case "pastcallrecordslookupduration":
		return obj.SetPastCallRecordsLookupDuration(v.PastCallRecordsLookupDuration, true, nil)
	case "blockedaccountcallroutingconfig":
		return obj._BlockedAccountCallRoutingConfig.Set(v.BlockedAccountCallRoutingConfig, true, path)
	case "prerecordedmessageconfig":
		return obj._PreRecordedMessageConfig.Set(v.PreRecordedMessageConfig, true, path)
	case "issueprioritycacheconfig":
		return obj._IssuePriorityCacheConfig.Set(v.IssuePriorityCacheConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CallRoutingConfig) setDynamicFields(v *config.CallRoutingConfig, dynamic bool, path []string) (err error) {

	err = obj.SetDefaultPriorityValue(v.DefaultPriorityValue, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxPriorityValue(v.MaxPriorityValue, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCallDropOffCountThreshold(v.CallDropOffCountThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetHalvingFactor(v.HalvingFactor, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPastCallRecordsLookupSize(v.PastCallRecordsLookupSize, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsManualRoutingEnabled(v.IsManualRoutingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsSalaryProgramRoutingEnabled(v.IsSalaryProgramRoutingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsCreditCardRoutingEnabled(v.IsCreditCardRoutingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsActiveLoanRoutingEnabled(v.IsActiveLoanRoutingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsPriorityRoutingEnabled(v.IsPriorityRoutingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsRecordedMessageEventEnabled(v.IsRecordedMessageEventEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCallLangPreferencesList(v.CallLangPreferencesList, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetCallLangSuggestionsList(v.CallLangSuggestionsList, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetQueueWaitTimeDurationThreshold(v.QueueWaitTimeDurationThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPastCallRecordsLookupDuration(v.PastCallRecordsLookupDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._BlockedAccountCallRoutingConfig.Set(v.BlockedAccountCallRoutingConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PreRecordedMessageConfig.Set(v.PreRecordedMessageConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._IssuePriorityCacheConfig.Set(v.IssuePriorityCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CallRoutingConfig) setStaticFields(v *config.CallRoutingConfig) error {

	obj._CallLangPrefReleaseConfig = v.CallLangPrefReleaseConfig
	obj._SegmentIdToPriorityMapForRoutingChannel = v.SegmentIdToPriorityMapForRoutingChannel
	obj._RoutingChannelToSegmentIdList = v.RoutingChannelToSegmentIdList
	return nil
}

func (obj *CallRoutingConfig) SetDefaultPriorityValue(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig.DefaultPriorityValue", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._DefaultPriorityValue, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "DefaultPriorityValue")
	}
	return nil
}
func (obj *CallRoutingConfig) SetMaxPriorityValue(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig.MaxPriorityValue", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxPriorityValue, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxPriorityValue")
	}
	return nil
}
func (obj *CallRoutingConfig) SetCallDropOffCountThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig.CallDropOffCountThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CallDropOffCountThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CallDropOffCountThreshold")
	}
	return nil
}
func (obj *CallRoutingConfig) SetHalvingFactor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig.HalvingFactor", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._HalvingFactor, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "HalvingFactor")
	}
	return nil
}
func (obj *CallRoutingConfig) SetPastCallRecordsLookupSize(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig.PastCallRecordsLookupSize", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._PastCallRecordsLookupSize, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "PastCallRecordsLookupSize")
	}
	return nil
}
func (obj *CallRoutingConfig) SetIsManualRoutingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig.IsManualRoutingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsManualRoutingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsManualRoutingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsManualRoutingEnabled")
	}
	return nil
}
func (obj *CallRoutingConfig) SetIsSalaryProgramRoutingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig.IsSalaryProgramRoutingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsSalaryProgramRoutingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsSalaryProgramRoutingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsSalaryProgramRoutingEnabled")
	}
	return nil
}
func (obj *CallRoutingConfig) SetIsCreditCardRoutingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig.IsCreditCardRoutingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCreditCardRoutingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCreditCardRoutingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCreditCardRoutingEnabled")
	}
	return nil
}
func (obj *CallRoutingConfig) SetIsActiveLoanRoutingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig.IsActiveLoanRoutingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsActiveLoanRoutingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsActiveLoanRoutingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsActiveLoanRoutingEnabled")
	}
	return nil
}
func (obj *CallRoutingConfig) SetIsPriorityRoutingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig.IsPriorityRoutingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsPriorityRoutingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsPriorityRoutingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsPriorityRoutingEnabled")
	}
	return nil
}
func (obj *CallRoutingConfig) SetIsRecordedMessageEventEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig.IsRecordedMessageEventEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsRecordedMessageEventEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsRecordedMessageEventEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsRecordedMessageEventEnabled")
	}
	return nil
}
func (obj *CallRoutingConfig) SetCallLangPreferencesList(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig.CallLangPreferencesList", reflect.TypeOf(val))
	}
	obj._CallLangPreferencesListMutex.Lock()
	defer obj._CallLangPreferencesListMutex.Unlock()
	obj._CallLangPreferencesList = roarray.New[string](v)
	return nil
}
func (obj *CallRoutingConfig) SetCallLangSuggestionsList(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig.CallLangSuggestionsList", reflect.TypeOf(val))
	}
	obj._CallLangSuggestionsListMutex.Lock()
	defer obj._CallLangSuggestionsListMutex.Unlock()
	obj._CallLangSuggestionsList = roarray.New[string](v)
	return nil
}
func (obj *CallRoutingConfig) SetQueueWaitTimeDurationThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig.QueueWaitTimeDurationThreshold", reflect.TypeOf(val))
	}
	obj._QueueWaitTimeDurationThresholdMutex.Lock()
	defer obj._QueueWaitTimeDurationThresholdMutex.Unlock()
	obj._QueueWaitTimeDurationThreshold = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "QueueWaitTimeDurationThreshold")
	}
	return nil
}
func (obj *CallRoutingConfig) SetPastCallRecordsLookupDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallRoutingConfig.PastCallRecordsLookupDuration", reflect.TypeOf(val))
	}
	obj._PastCallRecordsLookupDurationMutex.Lock()
	defer obj._PastCallRecordsLookupDurationMutex.Unlock()
	obj._PastCallRecordsLookupDuration = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "PastCallRecordsLookupDuration")
	}
	return nil
}

func NewBlockedAccountCallRoutingConfig() (_obj *BlockedAccountCallRoutingConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &BlockedAccountCallRoutingConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isroutingenabled"] = _obj.SetIsRoutingEnabled

	_obj._FreezeStatusToReasonCodeMap = &syncmap.Map[string, string]{}
	_setters["freezestatustoreasoncodemap"] = _obj.SetFreezeStatusToReasonCodeMap

	_obj._AccessRevokeReasonMap = &syncmap.Map[string, bool]{}
	_setters["accessrevokereasonmap"] = _obj.SetAccessRevokeReasonMap
	return _obj, _setters
}

func (obj *BlockedAccountCallRoutingConfig) Init() {
	newObj, _ := NewBlockedAccountCallRoutingConfig()
	*obj = *newObj
}

func (obj *BlockedAccountCallRoutingConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *BlockedAccountCallRoutingConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.BlockedAccountCallRoutingConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *BlockedAccountCallRoutingConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *BlockedAccountCallRoutingConfig) setDynamicField(v *config.BlockedAccountCallRoutingConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isroutingenabled":
		return obj.SetIsRoutingEnabled(v.IsRoutingEnabled, true, nil)
	case "freezestatustoreasoncodemap":
		return obj.SetFreezeStatusToReasonCodeMap(v.FreezeStatusToReasonCodeMap, true, path)
	case "accessrevokereasonmap":
		return obj.SetAccessRevokeReasonMap(v.AccessRevokeReasonMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *BlockedAccountCallRoutingConfig) setDynamicFields(v *config.BlockedAccountCallRoutingConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsRoutingEnabled(v.IsRoutingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFreezeStatusToReasonCodeMap(v.FreezeStatusToReasonCodeMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetAccessRevokeReasonMap(v.AccessRevokeReasonMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *BlockedAccountCallRoutingConfig) setStaticFields(v *config.BlockedAccountCallRoutingConfig) error {

	return nil
}

func (obj *BlockedAccountCallRoutingConfig) SetIsRoutingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *BlockedAccountCallRoutingConfig.IsRoutingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsRoutingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsRoutingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsRoutingEnabled")
	}
	return nil
}
func (obj *BlockedAccountCallRoutingConfig) SetFreezeStatusToReasonCodeMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *BlockedAccountCallRoutingConfig.FreezeStatusToReasonCodeMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._FreezeStatusToReasonCodeMap, v, path)
}
func (obj *BlockedAccountCallRoutingConfig) SetAccessRevokeReasonMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *BlockedAccountCallRoutingConfig.AccessRevokeReasonMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._AccessRevokeReasonMap, v, path)
}

func NewPreRecordedMessageConfig() (_obj *PreRecordedMessageConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &PreRecordedMessageConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ishighriskmessageenabled"] = _obj.SetIsHighRiskMessageEnabled
	_setters["isscreenerrejectedmessageenabled"] = _obj.SetIsScreenerRejectedMessageEnabled
	return _obj, _setters
}

func (obj *PreRecordedMessageConfig) Init() {
	newObj, _ := NewPreRecordedMessageConfig()
	*obj = *newObj
}

func (obj *PreRecordedMessageConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PreRecordedMessageConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PreRecordedMessageConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *PreRecordedMessageConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PreRecordedMessageConfig) setDynamicField(v *config.PreRecordedMessageConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "ishighriskmessageenabled":
		return obj.SetIsHighRiskMessageEnabled(v.IsHighRiskMessageEnabled, true, nil)
	case "isscreenerrejectedmessageenabled":
		return obj.SetIsScreenerRejectedMessageEnabled(v.IsScreenerRejectedMessageEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PreRecordedMessageConfig) setDynamicFields(v *config.PreRecordedMessageConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsHighRiskMessageEnabled(v.IsHighRiskMessageEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsScreenerRejectedMessageEnabled(v.IsScreenerRejectedMessageEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PreRecordedMessageConfig) setStaticFields(v *config.PreRecordedMessageConfig) error {

	return nil
}

func (obj *PreRecordedMessageConfig) SetIsHighRiskMessageEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *PreRecordedMessageConfig.IsHighRiskMessageEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsHighRiskMessageEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsHighRiskMessageEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsHighRiskMessageEnabled")
	}
	return nil
}
func (obj *PreRecordedMessageConfig) SetIsScreenerRejectedMessageEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *PreRecordedMessageConfig.IsScreenerRejectedMessageEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsScreenerRejectedMessageEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsScreenerRejectedMessageEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsScreenerRejectedMessageEnabled")
	}
	return nil
}

func NewIssuePriorityCacheConfig() (_obj *IssuePriorityCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &IssuePriorityCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["allowmaxpriorityforissue"] = _obj.SetAllowMaxPriorityForIssue
	_obj._AllowMaxPriorityForIssueMutex = &sync.RWMutex{}
	_setters["key"] = _obj.SetKey
	_obj._KeyMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *IssuePriorityCacheConfig) Init() {
	newObj, _ := NewIssuePriorityCacheConfig()
	*obj = *newObj
}

func (obj *IssuePriorityCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *IssuePriorityCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.IssuePriorityCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *IssuePriorityCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *IssuePriorityCacheConfig) setDynamicField(v *config.IssuePriorityCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "allowmaxpriorityforissue":
		return obj.SetAllowMaxPriorityForIssue(v.AllowMaxPriorityForIssue, true, path)
	case "key":
		return obj.SetKey(v.Key, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *IssuePriorityCacheConfig) setDynamicFields(v *config.IssuePriorityCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAllowMaxPriorityForIssue(v.AllowMaxPriorityForIssue, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetKey(v.Key, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *IssuePriorityCacheConfig) setStaticFields(v *config.IssuePriorityCacheConfig) error {

	return nil
}

func (obj *IssuePriorityCacheConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *IssuePriorityCacheConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *IssuePriorityCacheConfig) SetAllowMaxPriorityForIssue(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *IssuePriorityCacheConfig.AllowMaxPriorityForIssue", reflect.TypeOf(val))
	}
	obj._AllowMaxPriorityForIssueMutex.Lock()
	defer obj._AllowMaxPriorityForIssueMutex.Unlock()
	obj._AllowMaxPriorityForIssue = roarray.New[string](v)
	return nil
}
func (obj *IssuePriorityCacheConfig) SetKey(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *IssuePriorityCacheConfig.Key", reflect.TypeOf(val))
	}
	obj._KeyMutex.Lock()
	defer obj._KeyMutex.Unlock()
	obj._Key = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Key")
	}
	return nil
}

func NewIssueResolutionFeedbackConfig() (_obj *IssueResolutionFeedbackConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &IssueResolutionFeedbackConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_DisputeConfig, _fieldSetters := NewDisputeFeedbackConfig()
	_obj._DisputeConfig = _DisputeConfig
	helper.AddFieldSetters("disputeconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *IssueResolutionFeedbackConfig) Init() {
	newObj, _ := NewIssueResolutionFeedbackConfig()
	*obj = *newObj
}

func (obj *IssueResolutionFeedbackConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *IssueResolutionFeedbackConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.IssueResolutionFeedbackConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *IssueResolutionFeedbackConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *IssueResolutionFeedbackConfig) setDynamicField(v *config.IssueResolutionFeedbackConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "disputeconfig":
		return obj._DisputeConfig.Set(v.DisputeConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *IssueResolutionFeedbackConfig) setDynamicFields(v *config.IssueResolutionFeedbackConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._DisputeConfig.Set(v.DisputeConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *IssueResolutionFeedbackConfig) setStaticFields(v *config.IssueResolutionFeedbackConfig) error {

	return nil
}

func (obj *IssueResolutionFeedbackConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *IssueResolutionFeedbackConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}

func NewDisputeFeedbackConfig() (_obj *DisputeFeedbackConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DisputeFeedbackConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["waitdurationafterfinalcomms"] = _obj.SetWaitDurationAfterFinalComms

	_obj._IssueCategoryIdToTransactionType = &syncmap.Map[string, string]{}
	_setters["issuecategoryidtotransactiontype"] = _obj.SetIssueCategoryIdToTransactionType
	return _obj, _setters
}

func (obj *DisputeFeedbackConfig) Init() {
	newObj, _ := NewDisputeFeedbackConfig()
	*obj = *newObj
}

func (obj *DisputeFeedbackConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DisputeFeedbackConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DisputeFeedbackConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *DisputeFeedbackConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DisputeFeedbackConfig) setDynamicField(v *config.DisputeFeedbackConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "waitdurationafterfinalcomms":
		return obj.SetWaitDurationAfterFinalComms(v.WaitDurationAfterFinalComms, true, nil)
	case "issuecategoryidtotransactiontype":
		return obj.SetIssueCategoryIdToTransactionType(v.IssueCategoryIdToTransactionType, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DisputeFeedbackConfig) setDynamicFields(v *config.DisputeFeedbackConfig, dynamic bool, path []string) (err error) {

	err = obj.SetWaitDurationAfterFinalComms(v.WaitDurationAfterFinalComms, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIssueCategoryIdToTransactionType(v.IssueCategoryIdToTransactionType, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DisputeFeedbackConfig) setStaticFields(v *config.DisputeFeedbackConfig) error {

	return nil
}

func (obj *DisputeFeedbackConfig) SetWaitDurationAfterFinalComms(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *DisputeFeedbackConfig.WaitDurationAfterFinalComms", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._WaitDurationAfterFinalComms, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "WaitDurationAfterFinalComms")
	}
	return nil
}
func (obj *DisputeFeedbackConfig) SetIssueCategoryIdToTransactionType(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *DisputeFeedbackConfig.IssueCategoryIdToTransactionType", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._IssueCategoryIdToTransactionType, v, path)
}

func NewChatBotConfig() (_obj *ChatBotConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ChatBotConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxclientsidefailurecountallowedforautoretry"] = _obj.SetMaxClientSideFailureCountAllowedForAutoRetry
	_setters["numoftxnstobefetched"] = _obj.SetNumOfTxnsToBeFetched
	_setters["numofchargestobedisplayed"] = _obj.SetNumOfChargesToBeDisplayed
	_setters["isforcefallbacktodefaultenabled"] = _obj.SetIsForceFallbackToDefaultEnabled
	_setters["isreleaseevaluationenabled"] = _obj.SetIsReleaseEvaluationEnabled
	_setters["isextraloggingenabled"] = _obj.SetIsExtraLoggingEnabled
	_setters["iscontextcodepassingfromclientenabled"] = _obj.SetIsContextCodePassingFromClientEnabled
	_setters["isfreshchatexperimentenabled"] = _obj.SetIsFreshChatExperimentEnabled
	_setters["maxtimedurationthresholdforlastsuccessfulsessiontime"] = _obj.SetMaxTimeDurationThresholdForLastSuccessfulSessionTime
	_setters["actoridsenabledfreshchatissuetreeexperiment"] = _obj.SetActorIdsEnabledFreshchatIssueTreeExperiment
	_obj._ActorIdsEnabledFreshchatIssueTreeExperimentMutex = &sync.RWMutex{}
	_setters["defaultinappchatview"] = _obj.SetDefaultInAppChatView
	_obj._DefaultInAppChatViewMutex = &sync.RWMutex{}
	_LiveChatFallbackConfig, _fieldSetters := NewLiveChatFallbackConfig()
	_obj._LiveChatFallbackConfig = _LiveChatFallbackConfig
	helper.AddFieldSetters("livechatfallbackconfig", _fieldSetters, _setters)
	_SenseforthChatInitInfo, _fieldSetters := NewSenseforthChatInitInfo()
	_obj._SenseforthChatInitInfo = _SenseforthChatInitInfo
	helper.AddFieldSetters("senseforthchatinitinfo", _fieldSetters, _setters)
	_TxnListDisplayFormat, _fieldSetters := NewChatBotDisplayFormat()
	_obj._TxnListDisplayFormat = _TxnListDisplayFormat
	helper.AddFieldSetters("txnlistdisplayformat", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *ChatBotConfig) Init() {
	newObj, _ := NewChatBotConfig()
	*obj = *newObj
}

func (obj *ChatBotConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ChatBotConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ChatBotConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChatBotConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ChatBotConfig) setDynamicField(v *config.ChatBotConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maxclientsidefailurecountallowedforautoretry":
		return obj.SetMaxClientSideFailureCountAllowedForAutoRetry(v.MaxClientSideFailureCountAllowedForAutoRetry, true, nil)
	case "numoftxnstobefetched":
		return obj.SetNumOfTxnsToBeFetched(v.NumOfTxnsToBeFetched, true, nil)
	case "numofchargestobedisplayed":
		return obj.SetNumOfChargesToBeDisplayed(v.NumOfChargesToBeDisplayed, true, nil)
	case "isforcefallbacktodefaultenabled":
		return obj.SetIsForceFallbackToDefaultEnabled(v.IsForceFallbackToDefaultEnabled, true, nil)
	case "isreleaseevaluationenabled":
		return obj.SetIsReleaseEvaluationEnabled(v.IsReleaseEvaluationEnabled, true, nil)
	case "isextraloggingenabled":
		return obj.SetIsExtraLoggingEnabled(v.IsExtraLoggingEnabled, true, nil)
	case "iscontextcodepassingfromclientenabled":
		return obj.SetIsContextCodePassingFromClientEnabled(v.IsContextCodePassingFromClientEnabled, true, nil)
	case "isfreshchatexperimentenabled":
		return obj.SetIsFreshChatExperimentEnabled(v.IsFreshChatExperimentEnabled, true, nil)
	case "maxtimedurationthresholdforlastsuccessfulsessiontime":
		return obj.SetMaxTimeDurationThresholdForLastSuccessfulSessionTime(v.MaxTimeDurationThresholdForLastSuccessfulSessionTime, true, nil)
	case "actoridsenabledfreshchatissuetreeexperiment":
		return obj.SetActorIdsEnabledFreshchatIssueTreeExperiment(v.ActorIdsEnabledFreshchatIssueTreeExperiment, true, path)
	case "defaultinappchatview":
		return obj.SetDefaultInAppChatView(v.DefaultInAppChatView, true, nil)
	case "livechatfallbackconfig":
		return obj._LiveChatFallbackConfig.Set(v.LiveChatFallbackConfig, true, path)
	case "senseforthchatinitinfo":
		return obj._SenseforthChatInitInfo.Set(v.SenseforthChatInitInfo, true, path)
	case "txnlistdisplayformat":
		return obj._TxnListDisplayFormat.Set(v.TxnListDisplayFormat, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ChatBotConfig) setDynamicFields(v *config.ChatBotConfig, dynamic bool, path []string) (err error) {

	err = obj.SetMaxClientSideFailureCountAllowedForAutoRetry(v.MaxClientSideFailureCountAllowedForAutoRetry, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNumOfTxnsToBeFetched(v.NumOfTxnsToBeFetched, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNumOfChargesToBeDisplayed(v.NumOfChargesToBeDisplayed, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsForceFallbackToDefaultEnabled(v.IsForceFallbackToDefaultEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsReleaseEvaluationEnabled(v.IsReleaseEvaluationEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsExtraLoggingEnabled(v.IsExtraLoggingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsContextCodePassingFromClientEnabled(v.IsContextCodePassingFromClientEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsFreshChatExperimentEnabled(v.IsFreshChatExperimentEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxTimeDurationThresholdForLastSuccessfulSessionTime(v.MaxTimeDurationThresholdForLastSuccessfulSessionTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetActorIdsEnabledFreshchatIssueTreeExperiment(v.ActorIdsEnabledFreshchatIssueTreeExperiment, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetDefaultInAppChatView(v.DefaultInAppChatView, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._LiveChatFallbackConfig.Set(v.LiveChatFallbackConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SenseforthChatInitInfo.Set(v.SenseforthChatInitInfo, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TxnListDisplayFormat.Set(v.TxnListDisplayFormat, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ChatBotConfig) setStaticFields(v *config.ChatBotConfig) error {

	obj._NotificationTemplatesMap = v.NotificationTemplatesMap
	obj._CreateTicketTag = v.CreateTicketTag
	obj._TxnReasonDisplayMap = v.TxnReasonDisplayMap
	obj._NumOfFailedTxnsToBeDisplayed = v.NumOfFailedTxnsToBeDisplayed
	obj._NumberOfATMTxnsToBeFetched = v.NumberOfATMTxnsToBeFetched
	obj._IsPredefinedMessageTemplateEnabled = v.IsPredefinedMessageTemplateEnabled
	obj._IsPostOnboardingHighRiskMessageEnabled = v.IsPostOnboardingHighRiskMessageEnabled
	obj._PostOnboardingHighRiskUserMessage = v.PostOnboardingHighRiskUserMessage
	return nil
}

func (obj *ChatBotConfig) SetMaxClientSideFailureCountAllowedForAutoRetry(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChatBotConfig.MaxClientSideFailureCountAllowedForAutoRetry", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MaxClientSideFailureCountAllowedForAutoRetry, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxClientSideFailureCountAllowedForAutoRetry")
	}
	return nil
}
func (obj *ChatBotConfig) SetNumOfTxnsToBeFetched(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChatBotConfig.NumOfTxnsToBeFetched", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._NumOfTxnsToBeFetched, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "NumOfTxnsToBeFetched")
	}
	return nil
}
func (obj *ChatBotConfig) SetNumOfChargesToBeDisplayed(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChatBotConfig.NumOfChargesToBeDisplayed", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._NumOfChargesToBeDisplayed, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "NumOfChargesToBeDisplayed")
	}
	return nil
}
func (obj *ChatBotConfig) SetIsForceFallbackToDefaultEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChatBotConfig.IsForceFallbackToDefaultEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsForceFallbackToDefaultEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsForceFallbackToDefaultEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsForceFallbackToDefaultEnabled")
	}
	return nil
}
func (obj *ChatBotConfig) SetIsReleaseEvaluationEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChatBotConfig.IsReleaseEvaluationEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsReleaseEvaluationEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsReleaseEvaluationEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsReleaseEvaluationEnabled")
	}
	return nil
}
func (obj *ChatBotConfig) SetIsExtraLoggingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChatBotConfig.IsExtraLoggingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsExtraLoggingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsExtraLoggingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsExtraLoggingEnabled")
	}
	return nil
}
func (obj *ChatBotConfig) SetIsContextCodePassingFromClientEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChatBotConfig.IsContextCodePassingFromClientEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsContextCodePassingFromClientEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsContextCodePassingFromClientEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsContextCodePassingFromClientEnabled")
	}
	return nil
}
func (obj *ChatBotConfig) SetIsFreshChatExperimentEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChatBotConfig.IsFreshChatExperimentEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsFreshChatExperimentEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsFreshChatExperimentEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsFreshChatExperimentEnabled")
	}
	return nil
}
func (obj *ChatBotConfig) SetMaxTimeDurationThresholdForLastSuccessfulSessionTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChatBotConfig.MaxTimeDurationThresholdForLastSuccessfulSessionTime", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxTimeDurationThresholdForLastSuccessfulSessionTime, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxTimeDurationThresholdForLastSuccessfulSessionTime")
	}
	return nil
}
func (obj *ChatBotConfig) SetActorIdsEnabledFreshchatIssueTreeExperiment(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChatBotConfig.ActorIdsEnabledFreshchatIssueTreeExperiment", reflect.TypeOf(val))
	}
	obj._ActorIdsEnabledFreshchatIssueTreeExperimentMutex.Lock()
	defer obj._ActorIdsEnabledFreshchatIssueTreeExperimentMutex.Unlock()
	obj._ActorIdsEnabledFreshchatIssueTreeExperiment = roarray.New[string](v)
	return nil
}
func (obj *ChatBotConfig) SetDefaultInAppChatView(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChatBotConfig.DefaultInAppChatView", reflect.TypeOf(val))
	}
	obj._DefaultInAppChatViewMutex.Lock()
	defer obj._DefaultInAppChatViewMutex.Unlock()
	obj._DefaultInAppChatView = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DefaultInAppChatView")
	}
	return nil
}

func NewLiveChatFallbackConfig() (_obj *LiveChatFallbackConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &LiveChatFallbackConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["channelid"] = _obj.SetChannelId
	_obj._ChannelIdMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *LiveChatFallbackConfig) Init() {
	newObj, _ := NewLiveChatFallbackConfig()
	*obj = *newObj
}

func (obj *LiveChatFallbackConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *LiveChatFallbackConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.LiveChatFallbackConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *LiveChatFallbackConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *LiveChatFallbackConfig) setDynamicField(v *config.LiveChatFallbackConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "channelid":
		return obj.SetChannelId(v.ChannelId, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *LiveChatFallbackConfig) setDynamicFields(v *config.LiveChatFallbackConfig, dynamic bool, path []string) (err error) {

	err = obj.SetChannelId(v.ChannelId, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *LiveChatFallbackConfig) setStaticFields(v *config.LiveChatFallbackConfig) error {

	return nil
}

func (obj *LiveChatFallbackConfig) SetChannelId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *LiveChatFallbackConfig.ChannelId", reflect.TypeOf(val))
	}
	obj._ChannelIdMutex.Lock()
	defer obj._ChannelIdMutex.Unlock()
	obj._ChannelId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ChannelId")
	}
	return nil
}

func NewSenseforthChatInitInfo() (_obj *SenseforthChatInitInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &SenseforthChatInitInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._WebViewURLMap = &syncmap.Map[string, string]{}
	_setters["webviewurlmap"] = _obj.SetWebViewURLMap
	return _obj, _setters
}

func (obj *SenseforthChatInitInfo) Init() {
	newObj, _ := NewSenseforthChatInitInfo()
	*obj = *newObj
}

func (obj *SenseforthChatInitInfo) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SenseforthChatInitInfo) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SenseforthChatInitInfo)
	if !ok {
		return fmt.Errorf("invalid data type %v *SenseforthChatInitInfo", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SenseforthChatInitInfo) setDynamicField(v *config.SenseforthChatInitInfo, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "webviewurlmap":
		return obj.SetWebViewURLMap(v.WebViewURLMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SenseforthChatInitInfo) setDynamicFields(v *config.SenseforthChatInitInfo, dynamic bool, path []string) (err error) {

	err = obj.SetWebViewURLMap(v.WebViewURLMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SenseforthChatInitInfo) setStaticFields(v *config.SenseforthChatInitInfo) error {

	return nil
}

func (obj *SenseforthChatInitInfo) SetWebViewURLMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *SenseforthChatInitInfo.WebViewURLMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._WebViewURLMap, v, path)
}

func NewChatBotDisplayFormat() (_obj *ChatBotDisplayFormat, _setters map[string]dynconf.SetFunc) {
	_obj = &ChatBotDisplayFormat{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["fieldsorder"] = _obj.SetFieldsOrder
	_obj._FieldsOrderMutex = &sync.RWMutex{}
	_setters["delimiter"] = _obj.SetDelimiter
	_obj._DelimiterMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *ChatBotDisplayFormat) Init() {
	newObj, _ := NewChatBotDisplayFormat()
	*obj = *newObj
}

func (obj *ChatBotDisplayFormat) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ChatBotDisplayFormat) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ChatBotDisplayFormat)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChatBotDisplayFormat", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ChatBotDisplayFormat) setDynamicField(v *config.ChatBotDisplayFormat, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "fieldsorder":
		return obj.SetFieldsOrder(v.FieldsOrder, true, path)
	case "delimiter":
		return obj.SetDelimiter(v.Delimiter, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ChatBotDisplayFormat) setDynamicFields(v *config.ChatBotDisplayFormat, dynamic bool, path []string) (err error) {

	err = obj.SetFieldsOrder(v.FieldsOrder, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetDelimiter(v.Delimiter, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ChatBotDisplayFormat) setStaticFields(v *config.ChatBotDisplayFormat) error {

	return nil
}

func (obj *ChatBotDisplayFormat) SetFieldsOrder(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChatBotDisplayFormat.FieldsOrder", reflect.TypeOf(val))
	}
	obj._FieldsOrderMutex.Lock()
	defer obj._FieldsOrderMutex.Unlock()
	obj._FieldsOrder = roarray.New[string](v)
	return nil
}
func (obj *ChatBotDisplayFormat) SetDelimiter(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ChatBotDisplayFormat.Delimiter", reflect.TypeOf(val))
	}
	obj._DelimiterMutex.Lock()
	defer obj._DelimiterMutex.Unlock()
	obj._Delimiter = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Delimiter")
	}
	return nil
}

func NewRiskConfig() (_obj *RiskConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &RiskConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablebackenddrivencharts"] = _obj.SetEnableBackendDrivenCharts
	_DevActionConfig, _fieldSetters := NewUploadRiskCaseDevActionConfig()
	_obj._DevActionConfig = _DevActionConfig
	helper.AddFieldSetters("devactionconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *RiskConfig) Init() {
	newObj, _ := NewRiskConfig()
	*obj = *newObj
}

func (obj *RiskConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RiskConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RiskConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RiskConfig) setDynamicField(v *config.RiskConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablebackenddrivencharts":
		return obj.SetEnableBackendDrivenCharts(v.EnableBackendDrivenCharts, true, nil)
	case "devactionconfig":
		return obj._DevActionConfig.Set(v.DevActionConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RiskConfig) setDynamicFields(v *config.RiskConfig, dynamic bool, path []string) (err error) {

	err = obj.SetEnableBackendDrivenCharts(v.EnableBackendDrivenCharts, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._DevActionConfig.Set(v.DevActionConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RiskConfig) setStaticFields(v *config.RiskConfig) error {

	return nil
}

func (obj *RiskConfig) SetEnableBackendDrivenCharts(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskConfig.EnableBackendDrivenCharts", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableBackendDrivenCharts, 1)
	} else {
		atomic.StoreUint32(&obj._EnableBackendDrivenCharts, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableBackendDrivenCharts")
	}
	return nil
}

func NewUploadRiskCaseDevActionConfig() (_obj *UploadRiskCaseDevActionConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &UploadRiskCaseDevActionConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["riskcaseeventbatchsize"] = _obj.SetRiskCaseEventBatchSize
	_setters["supportedpayloadtypes"] = _obj.SetSupportedPayloadTypes
	_obj._SupportedPayloadTypesMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *UploadRiskCaseDevActionConfig) Init() {
	newObj, _ := NewUploadRiskCaseDevActionConfig()
	*obj = *newObj
}

func (obj *UploadRiskCaseDevActionConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *UploadRiskCaseDevActionConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.UploadRiskCaseDevActionConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *UploadRiskCaseDevActionConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *UploadRiskCaseDevActionConfig) setDynamicField(v *config.UploadRiskCaseDevActionConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "riskcaseeventbatchsize":
		return obj.SetRiskCaseEventBatchSize(v.RiskCaseEventBatchSize, true, nil)
	case "supportedpayloadtypes":
		return obj.SetSupportedPayloadTypes(v.SupportedPayloadTypes, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *UploadRiskCaseDevActionConfig) setDynamicFields(v *config.UploadRiskCaseDevActionConfig, dynamic bool, path []string) (err error) {

	err = obj.SetRiskCaseEventBatchSize(v.RiskCaseEventBatchSize, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSupportedPayloadTypes(v.SupportedPayloadTypes, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *UploadRiskCaseDevActionConfig) setStaticFields(v *config.UploadRiskCaseDevActionConfig) error {

	return nil
}

func (obj *UploadRiskCaseDevActionConfig) SetRiskCaseEventBatchSize(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *UploadRiskCaseDevActionConfig.RiskCaseEventBatchSize", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._RiskCaseEventBatchSize, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RiskCaseEventBatchSize")
	}
	return nil
}
func (obj *UploadRiskCaseDevActionConfig) SetSupportedPayloadTypes(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *UploadRiskCaseDevActionConfig.SupportedPayloadTypes", reflect.TypeOf(val))
	}
	obj._SupportedPayloadTypesMutex.Lock()
	defer obj._SupportedPayloadTypesMutex.Unlock()
	obj._SupportedPayloadTypes = roarray.New[string](v)
	return nil
}

func NewInternationalFundTransfer() (_obj *InternationalFundTransfer, _setters map[string]dynconf.SetFunc) {
	_obj = &InternationalFundTransfer{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablelrscheckfromvendor"] = _obj.SetEnableLRSCheckFromVendor
	_setters["documentsbucketname"] = _obj.SetDocumentsBucketName
	_obj._DocumentsBucketNameMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *InternationalFundTransfer) Init() {
	newObj, _ := NewInternationalFundTransfer()
	*obj = *newObj
}

func (obj *InternationalFundTransfer) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *InternationalFundTransfer) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.InternationalFundTransfer)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *InternationalFundTransfer) setDynamicField(v *config.InternationalFundTransfer, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablelrscheckfromvendor":
		return obj.SetEnableLRSCheckFromVendor(v.EnableLRSCheckFromVendor, true, nil)
	case "documentsbucketname":
		return obj.SetDocumentsBucketName(v.DocumentsBucketName, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *InternationalFundTransfer) setDynamicFields(v *config.InternationalFundTransfer, dynamic bool, path []string) (err error) {

	err = obj.SetEnableLRSCheckFromVendor(v.EnableLRSCheckFromVendor, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDocumentsBucketName(v.DocumentsBucketName, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *InternationalFundTransfer) setStaticFields(v *config.InternationalFundTransfer) error {

	return nil
}

func (obj *InternationalFundTransfer) SetEnableLRSCheckFromVendor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.EnableLRSCheckFromVendor", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableLRSCheckFromVendor, 1)
	} else {
		atomic.StoreUint32(&obj._EnableLRSCheckFromVendor, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableLRSCheckFromVendor")
	}
	return nil
}
func (obj *InternationalFundTransfer) SetDocumentsBucketName(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InternationalFundTransfer.DocumentsBucketName", reflect.TypeOf(val))
	}
	obj._DocumentsBucketNameMutex.Lock()
	defer obj._DocumentsBucketNameMutex.Unlock()
	obj._DocumentsBucketName = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DocumentsBucketName")
	}
	return nil
}

func NewSalaryOpsConfig() (_obj *SalaryOpsConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &SalaryOpsConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxbepaginatedcallsforfiltering"] = _obj.SetMaxBEPaginatedCallsForFiltering
	_SalaryTransactionFilters, _fieldSetters := NewSalaryTransactionFilters()
	_obj._SalaryTransactionFilters = _SalaryTransactionFilters
	helper.AddFieldSetters("salarytransactionfilters", _fieldSetters, _setters)
	_SalaryProgramHealthInsuranceConfig, _fieldSetters := NewSalaryProgramHealthInsuranceConfig()
	_obj._SalaryProgramHealthInsuranceConfig = _SalaryProgramHealthInsuranceConfig
	helper.AddFieldSetters("salaryprogramhealthinsuranceconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *SalaryOpsConfig) Init() {
	newObj, _ := NewSalaryOpsConfig()
	*obj = *newObj
}

func (obj *SalaryOpsConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SalaryOpsConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SalaryOpsConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *SalaryOpsConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SalaryOpsConfig) setDynamicField(v *config.SalaryOpsConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maxbepaginatedcallsforfiltering":
		return obj.SetMaxBEPaginatedCallsForFiltering(v.MaxBEPaginatedCallsForFiltering, true, nil)
	case "salarytransactionfilters":
		return obj._SalaryTransactionFilters.Set(v.SalaryTransactionFilters, true, path)
	case "salaryprogramhealthinsuranceconfig":
		return obj._SalaryProgramHealthInsuranceConfig.Set(v.SalaryProgramHealthInsuranceConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SalaryOpsConfig) setDynamicFields(v *config.SalaryOpsConfig, dynamic bool, path []string) (err error) {

	err = obj.SetMaxBEPaginatedCallsForFiltering(v.MaxBEPaginatedCallsForFiltering, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._SalaryTransactionFilters.Set(v.SalaryTransactionFilters, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SalaryProgramHealthInsuranceConfig.Set(v.SalaryProgramHealthInsuranceConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SalaryOpsConfig) setStaticFields(v *config.SalaryOpsConfig) error {

	obj._VerificationFailureReasonsCategoryToDisplayString = v.VerificationFailureReasonsCategoryToDisplayString
	obj._VerificationFailureReasonsSubCategoryToDisplayString = v.VerificationFailureReasonsSubCategoryToDisplayString
	obj._NonIncomeRelatedTxnCategoryOntologyIds = v.NonIncomeRelatedTxnCategoryOntologyIds
	obj._SalaryProgramS3BucketName = v.SalaryProgramS3BucketName
	return nil
}

func (obj *SalaryOpsConfig) SetMaxBEPaginatedCallsForFiltering(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *SalaryOpsConfig.MaxBEPaginatedCallsForFiltering", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxBEPaginatedCallsForFiltering, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxBEPaginatedCallsForFiltering")
	}
	return nil
}

func NewSalaryTransactionFilters() (_obj *SalaryTransactionFilters, _setters map[string]dynconf.SetFunc) {
	_obj = &SalaryTransactionFilters{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minsalaryamount"] = _obj.SetMinSalaryAmount
	_setters["minreqdurationfromlastverification"] = _obj.SetMinReqDurationFromLastVerification
	_setters["maxalloweddurationfromlastverification"] = _obj.SetMaxAllowedDurationFromLastVerification
	_setters["allowedtransactionprotocols"] = _obj.SetAllowedTransactionProtocols
	_obj._AllowedTransactionProtocolsMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *SalaryTransactionFilters) Init() {
	newObj, _ := NewSalaryTransactionFilters()
	*obj = *newObj
}

func (obj *SalaryTransactionFilters) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SalaryTransactionFilters) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SalaryTransactionFilters)
	if !ok {
		return fmt.Errorf("invalid data type %v *SalaryTransactionFilters", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SalaryTransactionFilters) setDynamicField(v *config.SalaryTransactionFilters, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "minsalaryamount":
		return obj.SetMinSalaryAmount(v.MinSalaryAmount, true, nil)
	case "minreqdurationfromlastverification":
		return obj.SetMinReqDurationFromLastVerification(v.MinReqDurationFromLastVerification, true, nil)
	case "maxalloweddurationfromlastverification":
		return obj.SetMaxAllowedDurationFromLastVerification(v.MaxAllowedDurationFromLastVerification, true, nil)
	case "allowedtransactionprotocols":
		return obj.SetAllowedTransactionProtocols(v.AllowedTransactionProtocols, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SalaryTransactionFilters) setDynamicFields(v *config.SalaryTransactionFilters, dynamic bool, path []string) (err error) {

	err = obj.SetMinSalaryAmount(v.MinSalaryAmount, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinReqDurationFromLastVerification(v.MinReqDurationFromLastVerification, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxAllowedDurationFromLastVerification(v.MaxAllowedDurationFromLastVerification, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAllowedTransactionProtocols(v.AllowedTransactionProtocols, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SalaryTransactionFilters) setStaticFields(v *config.SalaryTransactionFilters) error {

	return nil
}

func (obj *SalaryTransactionFilters) SetMinSalaryAmount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *SalaryTransactionFilters.MinSalaryAmount", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinSalaryAmount, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinSalaryAmount")
	}
	return nil
}
func (obj *SalaryTransactionFilters) SetMinReqDurationFromLastVerification(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *SalaryTransactionFilters.MinReqDurationFromLastVerification", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MinReqDurationFromLastVerification, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinReqDurationFromLastVerification")
	}
	return nil
}
func (obj *SalaryTransactionFilters) SetMaxAllowedDurationFromLastVerification(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *SalaryTransactionFilters.MaxAllowedDurationFromLastVerification", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxAllowedDurationFromLastVerification, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxAllowedDurationFromLastVerification")
	}
	return nil
}
func (obj *SalaryTransactionFilters) SetAllowedTransactionProtocols(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *SalaryTransactionFilters.AllowedTransactionProtocols", reflect.TypeOf(val))
	}
	obj._AllowedTransactionProtocolsMutex.Lock()
	defer obj._AllowedTransactionProtocolsMutex.Unlock()
	obj._AllowedTransactionProtocols = roarray.New[string](v)
	return nil
}

func NewSalaryProgramHealthInsuranceConfig() (_obj *SalaryProgramHealthInsuranceConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &SalaryProgramHealthInsuranceConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["policyfaqsdocs3path"] = _obj.SetPolicyFAQsDocS3Path
	_obj._PolicyFAQsDocS3PathMutex = &sync.RWMutex{}
	_setters["policyclaimprocessdocs3path"] = _obj.SetPolicyClaimProcessDocS3Path
	_obj._PolicyClaimProcessDocS3PathMutex = &sync.RWMutex{}
	_setters["inclusionexclusionandhowitworksdocs3path"] = _obj.SetInclusionExclusionAndHowItWorksDocS3Path
	_obj._InclusionExclusionAndHowItWorksDocS3PathMutex = &sync.RWMutex{}
	_setters["tncsdocs3path"] = _obj.SetTncsDocS3Path
	_obj._TncsDocS3PathMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *SalaryProgramHealthInsuranceConfig) Init() {
	newObj, _ := NewSalaryProgramHealthInsuranceConfig()
	*obj = *newObj
}

func (obj *SalaryProgramHealthInsuranceConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SalaryProgramHealthInsuranceConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SalaryProgramHealthInsuranceConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *SalaryProgramHealthInsuranceConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SalaryProgramHealthInsuranceConfig) setDynamicField(v *config.SalaryProgramHealthInsuranceConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "policyfaqsdocs3path":
		return obj.SetPolicyFAQsDocS3Path(v.PolicyFAQsDocS3Path, true, nil)
	case "policyclaimprocessdocs3path":
		return obj.SetPolicyClaimProcessDocS3Path(v.PolicyClaimProcessDocS3Path, true, nil)
	case "inclusionexclusionandhowitworksdocs3path":
		return obj.SetInclusionExclusionAndHowItWorksDocS3Path(v.InclusionExclusionAndHowItWorksDocS3Path, true, nil)
	case "tncsdocs3path":
		return obj.SetTncsDocS3Path(v.TncsDocS3Path, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SalaryProgramHealthInsuranceConfig) setDynamicFields(v *config.SalaryProgramHealthInsuranceConfig, dynamic bool, path []string) (err error) {

	err = obj.SetPolicyFAQsDocS3Path(v.PolicyFAQsDocS3Path, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPolicyClaimProcessDocS3Path(v.PolicyClaimProcessDocS3Path, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetInclusionExclusionAndHowItWorksDocS3Path(v.InclusionExclusionAndHowItWorksDocS3Path, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTncsDocS3Path(v.TncsDocS3Path, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SalaryProgramHealthInsuranceConfig) setStaticFields(v *config.SalaryProgramHealthInsuranceConfig) error {

	return nil
}

func (obj *SalaryProgramHealthInsuranceConfig) SetPolicyFAQsDocS3Path(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *SalaryProgramHealthInsuranceConfig.PolicyFAQsDocS3Path", reflect.TypeOf(val))
	}
	obj._PolicyFAQsDocS3PathMutex.Lock()
	defer obj._PolicyFAQsDocS3PathMutex.Unlock()
	obj._PolicyFAQsDocS3Path = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "PolicyFAQsDocS3Path")
	}
	return nil
}
func (obj *SalaryProgramHealthInsuranceConfig) SetPolicyClaimProcessDocS3Path(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *SalaryProgramHealthInsuranceConfig.PolicyClaimProcessDocS3Path", reflect.TypeOf(val))
	}
	obj._PolicyClaimProcessDocS3PathMutex.Lock()
	defer obj._PolicyClaimProcessDocS3PathMutex.Unlock()
	obj._PolicyClaimProcessDocS3Path = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "PolicyClaimProcessDocS3Path")
	}
	return nil
}
func (obj *SalaryProgramHealthInsuranceConfig) SetInclusionExclusionAndHowItWorksDocS3Path(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *SalaryProgramHealthInsuranceConfig.InclusionExclusionAndHowItWorksDocS3Path", reflect.TypeOf(val))
	}
	obj._InclusionExclusionAndHowItWorksDocS3PathMutex.Lock()
	defer obj._InclusionExclusionAndHowItWorksDocS3PathMutex.Unlock()
	obj._InclusionExclusionAndHowItWorksDocS3Path = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "InclusionExclusionAndHowItWorksDocS3Path")
	}
	return nil
}
func (obj *SalaryProgramHealthInsuranceConfig) SetTncsDocS3Path(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *SalaryProgramHealthInsuranceConfig.TncsDocS3Path", reflect.TypeOf(val))
	}
	obj._TncsDocS3PathMutex.Lock()
	defer obj._TncsDocS3PathMutex.Unlock()
	obj._TncsDocS3Path = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "TncsDocS3Path")
	}
	return nil
}

func NewLandingPageConfig() (_obj *LandingPageConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &LandingPageConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isexpectedresolutiontimefieldpopulatedinlandingpageservice"] = _obj.SetIsExpectedResolutionTimeFieldPopulatedInLandingPageService
	_RecentUserQueryConfig, _fieldSetters := NewRecentUserQueryConfig()
	_obj._RecentUserQueryConfig = _RecentUserQueryConfig
	helper.AddFieldSetters("recentuserqueryconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *LandingPageConfig) Init() {
	newObj, _ := NewLandingPageConfig()
	*obj = *newObj
}

func (obj *LandingPageConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *LandingPageConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.LandingPageConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *LandingPageConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *LandingPageConfig) setDynamicField(v *config.LandingPageConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isexpectedresolutiontimefieldpopulatedinlandingpageservice":
		return obj.SetIsExpectedResolutionTimeFieldPopulatedInLandingPageService(v.IsExpectedResolutionTimeFieldPopulatedInLandingPageService, true, nil)
	case "recentuserqueryconfig":
		return obj._RecentUserQueryConfig.Set(v.RecentUserQueryConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *LandingPageConfig) setDynamicFields(v *config.LandingPageConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsExpectedResolutionTimeFieldPopulatedInLandingPageService(v.IsExpectedResolutionTimeFieldPopulatedInLandingPageService, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._RecentUserQueryConfig.Set(v.RecentUserQueryConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *LandingPageConfig) setStaticFields(v *config.LandingPageConfig) error {

	obj._UserDetailTabsEnumToStringMapping = v.UserDetailTabsEnumToStringMapping
	return nil
}

func (obj *LandingPageConfig) SetIsExpectedResolutionTimeFieldPopulatedInLandingPageService(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *LandingPageConfig.IsExpectedResolutionTimeFieldPopulatedInLandingPageService", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsExpectedResolutionTimeFieldPopulatedInLandingPageService, 1)
	} else {
		atomic.StoreUint32(&obj._IsExpectedResolutionTimeFieldPopulatedInLandingPageService, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsExpectedResolutionTimeFieldPopulatedInLandingPageService")
	}
	return nil
}

func NewRecentUserQueryConfig() (_obj *RecentUserQueryConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &RecentUserQueryConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["numberofqueriestodisplay"] = _obj.SetNumberOfQueriesToDisplay
	_setters["dateformat"] = _obj.SetDateFormat
	_obj._DateFormatMutex = &sync.RWMutex{}
	_setters["timeformat"] = _obj.SetTimeFormat
	_obj._TimeFormatMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *RecentUserQueryConfig) Init() {
	newObj, _ := NewRecentUserQueryConfig()
	*obj = *newObj
}

func (obj *RecentUserQueryConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RecentUserQueryConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RecentUserQueryConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *RecentUserQueryConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RecentUserQueryConfig) setDynamicField(v *config.RecentUserQueryConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "numberofqueriestodisplay":
		return obj.SetNumberOfQueriesToDisplay(v.NumberOfQueriesToDisplay, true, nil)
	case "dateformat":
		return obj.SetDateFormat(v.DateFormat, true, nil)
	case "timeformat":
		return obj.SetTimeFormat(v.TimeFormat, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RecentUserQueryConfig) setDynamicFields(v *config.RecentUserQueryConfig, dynamic bool, path []string) (err error) {

	err = obj.SetNumberOfQueriesToDisplay(v.NumberOfQueriesToDisplay, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDateFormat(v.DateFormat, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTimeFormat(v.TimeFormat, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RecentUserQueryConfig) setStaticFields(v *config.RecentUserQueryConfig) error {

	return nil
}

func (obj *RecentUserQueryConfig) SetNumberOfQueriesToDisplay(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *RecentUserQueryConfig.NumberOfQueriesToDisplay", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._NumberOfQueriesToDisplay, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "NumberOfQueriesToDisplay")
	}
	return nil
}
func (obj *RecentUserQueryConfig) SetDateFormat(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RecentUserQueryConfig.DateFormat", reflect.TypeOf(val))
	}
	obj._DateFormatMutex.Lock()
	defer obj._DateFormatMutex.Unlock()
	obj._DateFormat = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DateFormat")
	}
	return nil
}
func (obj *RecentUserQueryConfig) SetTimeFormat(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RecentUserQueryConfig.TimeFormat", reflect.TypeOf(val))
	}
	obj._TimeFormatMutex.Lock()
	defer obj._TimeFormatMutex.Unlock()
	obj._TimeFormat = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "TimeFormat")
	}
	return nil
}

func NewDevActionHelperConfig() (_obj *DevActionHelperConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DevActionHelperConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isbulkresourceaccessibilitycheckenabled"] = _obj.SetIsBulkResourceAccessibilityCheckEnabled
	return _obj, _setters
}

func (obj *DevActionHelperConfig) Init() {
	newObj, _ := NewDevActionHelperConfig()
	*obj = *newObj
}

func (obj *DevActionHelperConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DevActionHelperConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DevActionHelperConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *DevActionHelperConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DevActionHelperConfig) setDynamicField(v *config.DevActionHelperConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isbulkresourceaccessibilitycheckenabled":
		return obj.SetIsBulkResourceAccessibilityCheckEnabled(v.IsBulkResourceAccessibilityCheckEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DevActionHelperConfig) setDynamicFields(v *config.DevActionHelperConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsBulkResourceAccessibilityCheckEnabled(v.IsBulkResourceAccessibilityCheckEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DevActionHelperConfig) setStaticFields(v *config.DevActionHelperConfig) error {

	return nil
}

func (obj *DevActionHelperConfig) SetIsBulkResourceAccessibilityCheckEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DevActionHelperConfig.IsBulkResourceAccessibilityCheckEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsBulkResourceAccessibilityCheckEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsBulkResourceAccessibilityCheckEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsBulkResourceAccessibilityCheckEnabled")
	}
	return nil
}

func NewOverrideBankActions() (_obj *OverrideBankActions, _setters map[string]dynconf.SetFunc) {
	_obj = &OverrideBankActions{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxrequests"] = _obj.SetMaxRequests
	return _obj, _setters
}

func (obj *OverrideBankActions) Init() {
	newObj, _ := NewOverrideBankActions()
	*obj = *newObj
}

func (obj *OverrideBankActions) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OverrideBankActions) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OverrideBankActions)
	if !ok {
		return fmt.Errorf("invalid data type %v *OverrideBankActions", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OverrideBankActions) setDynamicField(v *config.OverrideBankActions, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maxrequests":
		return obj.SetMaxRequests(v.MaxRequests, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OverrideBankActions) setDynamicFields(v *config.OverrideBankActions, dynamic bool, path []string) (err error) {

	err = obj.SetMaxRequests(v.MaxRequests, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *OverrideBankActions) setStaticFields(v *config.OverrideBankActions) error {

	return nil
}

func (obj *OverrideBankActions) SetMaxRequests(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *OverrideBankActions.MaxRequests", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxRequests, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxRequests")
	}
	return nil
}

func NewReviewActionConfig() (_obj *ReviewActionConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ReviewActionConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["commonquestionstouser"] = _obj.SetCommonQuestionsToUser
	_obj._CommonQuestionsToUserMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *ReviewActionConfig) Init() {
	newObj, _ := NewReviewActionConfig()
	*obj = *newObj
}

func (obj *ReviewActionConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ReviewActionConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ReviewActionConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReviewActionConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ReviewActionConfig) setDynamicField(v *config.ReviewActionConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "commonquestionstouser":
		return obj.SetCommonQuestionsToUser(v.CommonQuestionsToUser, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ReviewActionConfig) setDynamicFields(v *config.ReviewActionConfig, dynamic bool, path []string) (err error) {

	err = obj.SetCommonQuestionsToUser(v.CommonQuestionsToUser, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ReviewActionConfig) setStaticFields(v *config.ReviewActionConfig) error {

	return nil
}

func (obj *ReviewActionConfig) SetCommonQuestionsToUser(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReviewActionConfig.CommonQuestionsToUser", reflect.TypeOf(val))
	}
	obj._CommonQuestionsToUserMutex.Lock()
	defer obj._CommonQuestionsToUserMutex.Unlock()
	obj._CommonQuestionsToUser = roarray.New[string](v)
	return nil
}

func NewVendorAccountPennyDropViaEmailConfig() (_obj *VendorAccountPennyDropViaEmailConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &VendorAccountPennyDropViaEmailConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["toemailname"] = _obj.SetToEmailName
	_obj._ToEmailNameMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *VendorAccountPennyDropViaEmailConfig) Init() {
	newObj, _ := NewVendorAccountPennyDropViaEmailConfig()
	*obj = *newObj
}

func (obj *VendorAccountPennyDropViaEmailConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *VendorAccountPennyDropViaEmailConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.VendorAccountPennyDropViaEmailConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *VendorAccountPennyDropViaEmailConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *VendorAccountPennyDropViaEmailConfig) setDynamicField(v *config.VendorAccountPennyDropViaEmailConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "toemailname":
		return obj.SetToEmailName(v.ToEmailName, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *VendorAccountPennyDropViaEmailConfig) setDynamicFields(v *config.VendorAccountPennyDropViaEmailConfig, dynamic bool, path []string) (err error) {

	err = obj.SetToEmailName(v.ToEmailName, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *VendorAccountPennyDropViaEmailConfig) setStaticFields(v *config.VendorAccountPennyDropViaEmailConfig) error {

	obj._FromEmailId = v.FromEmailId
	obj._FromEmailName = v.FromEmailName
	obj._ToEmailId = v.ToEmailId
	return nil
}

func (obj *VendorAccountPennyDropViaEmailConfig) SetToEmailName(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *VendorAccountPennyDropViaEmailConfig.ToEmailName", reflect.TypeOf(val))
	}
	obj._ToEmailNameMutex.Lock()
	defer obj._ToEmailNameMutex.Unlock()
	obj._ToEmailName = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ToEmailName")
	}
	return nil
}

func NewEmployerDbConfig() (_obj *EmployerDbConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &EmployerDbConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["indexingtimeduration"] = _obj.SetIndexingTimeDuration
	return _obj, _setters
}

func (obj *EmployerDbConfig) Init() {
	newObj, _ := NewEmployerDbConfig()
	*obj = *newObj
}

func (obj *EmployerDbConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EmployerDbConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.EmployerDbConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *EmployerDbConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EmployerDbConfig) setDynamicField(v *config.EmployerDbConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "indexingtimeduration":
		return obj.SetIndexingTimeDuration(v.IndexingTimeDuration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EmployerDbConfig) setDynamicFields(v *config.EmployerDbConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIndexingTimeDuration(v.IndexingTimeDuration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EmployerDbConfig) setStaticFields(v *config.EmployerDbConfig) error {

	obj._EsHostUrl = v.EsHostUrl
	return nil
}

func (obj *EmployerDbConfig) SetIndexingTimeDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *EmployerDbConfig.IndexingTimeDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._IndexingTimeDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "IndexingTimeDuration")
	}
	return nil
}

func NewGRPCWebServerConfig() (_obj *GRPCWebServerConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &GRPCWebServerConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_JarvisInterceptorConf, _fieldSetters := genconfig2.NewJarvisInterceptorConf()
	_obj._JarvisInterceptorConf = _JarvisInterceptorConf
	helper.AddFieldSetters("jarvisinterceptorconf", _fieldSetters, _setters)
	_HttpCorsOptions, _fieldSetters := genconfig2.NewHttpCorsOptions()
	_obj._HttpCorsOptions = _HttpCorsOptions
	helper.AddFieldSetters("httpcorsoptions", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *GRPCWebServerConfig) Init() {
	newObj, _ := NewGRPCWebServerConfig()
	*obj = *newObj
}

func (obj *GRPCWebServerConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *GRPCWebServerConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.GRPCWebServerConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *GRPCWebServerConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *GRPCWebServerConfig) setDynamicField(v *config.GRPCWebServerConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "jarvisinterceptorconf":
		return obj._JarvisInterceptorConf.Set(v.JarvisInterceptorConf, true, path)
	case "httpcorsoptions":
		return obj._HttpCorsOptions.Set(v.HttpCorsOptions, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *GRPCWebServerConfig) setDynamicFields(v *config.GRPCWebServerConfig, dynamic bool, path []string) (err error) {

	err = obj._JarvisInterceptorConf.Set(v.JarvisInterceptorConf, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._HttpCorsOptions.Set(v.HttpCorsOptions, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *GRPCWebServerConfig) setStaticFields(v *config.GRPCWebServerConfig) error {

	obj._KeycloakAuth = v.KeycloakAuth
	return nil
}

func NewSherlockBannersConfig() (_obj *SherlockBannersConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &SherlockBannersConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._IsServiceEnabledForDynamicFetching = &syncmap.Map[string, bool]{}
	_setters["isserviceenabledfordynamicfetching"] = _obj.SetIsServiceEnabledForDynamicFetching
	return _obj, _setters
}

func (obj *SherlockBannersConfig) Init() {
	newObj, _ := NewSherlockBannersConfig()
	*obj = *newObj
}

func (obj *SherlockBannersConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SherlockBannersConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SherlockBannersConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *SherlockBannersConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SherlockBannersConfig) setDynamicField(v *config.SherlockBannersConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isserviceenabledfordynamicfetching":
		return obj.SetIsServiceEnabledForDynamicFetching(v.IsServiceEnabledForDynamicFetching, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SherlockBannersConfig) setDynamicFields(v *config.SherlockBannersConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsServiceEnabledForDynamicFetching(v.IsServiceEnabledForDynamicFetching, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SherlockBannersConfig) setStaticFields(v *config.SherlockBannersConfig) error {

	obj._PriorityOrder = v.PriorityOrder
	return nil
}

func (obj *SherlockBannersConfig) SetIsServiceEnabledForDynamicFetching(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *SherlockBannersConfig.IsServiceEnabledForDynamicFetching", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._IsServiceEnabledForDynamicFetching, v, path)
}

func NewErrorActivityConfig() (_obj *ErrorActivityConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ErrorActivityConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ispipingerroreventtowatsonenabled"] = _obj.SetIsPipingErrorEventToWatsonEnabled
	_setters["defaultincidentcreationcooloffperiod"] = _obj.SetDefaultIncidentCreationCoolOffPeriod
	_setters["issuecategoryideventpayloadkey"] = _obj.SetIssueCategoryIdEventPayloadKey
	_obj._IssueCategoryIdEventPayloadKeyMutex = &sync.RWMutex{}
	_setters["clientrequestideventpayloadkey"] = _obj.SetClientRequestIdEventPayloadKey
	_obj._ClientRequestIdEventPayloadKeyMutex = &sync.RWMutex{}
	_setters["isresolutioneventbooleaneventpayloadkey"] = _obj.SetIsResolutionEventBooleanEventPayloadKey
	_obj._IsResolutionEventBooleanEventPayloadKeyMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *ErrorActivityConfig) Init() {
	newObj, _ := NewErrorActivityConfig()
	*obj = *newObj
}

func (obj *ErrorActivityConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ErrorActivityConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ErrorActivityConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ErrorActivityConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ErrorActivityConfig) setDynamicField(v *config.ErrorActivityConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "ispipingerroreventtowatsonenabled":
		return obj.SetIsPipingErrorEventToWatsonEnabled(v.IsPipingErrorEventToWatsonEnabled, true, nil)
	case "defaultincidentcreationcooloffperiod":
		return obj.SetDefaultIncidentCreationCoolOffPeriod(v.DefaultIncidentCreationCoolOffPeriod, true, nil)
	case "issuecategoryideventpayloadkey":
		return obj.SetIssueCategoryIdEventPayloadKey(v.IssueCategoryIdEventPayloadKey, true, nil)
	case "clientrequestideventpayloadkey":
		return obj.SetClientRequestIdEventPayloadKey(v.ClientRequestIdEventPayloadKey, true, nil)
	case "isresolutioneventbooleaneventpayloadkey":
		return obj.SetIsResolutionEventBooleanEventPayloadKey(v.IsResolutionEventBooleanEventPayloadKey, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ErrorActivityConfig) setDynamicFields(v *config.ErrorActivityConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsPipingErrorEventToWatsonEnabled(v.IsPipingErrorEventToWatsonEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDefaultIncidentCreationCoolOffPeriod(v.DefaultIncidentCreationCoolOffPeriod, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIssueCategoryIdEventPayloadKey(v.IssueCategoryIdEventPayloadKey, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetClientRequestIdEventPayloadKey(v.ClientRequestIdEventPayloadKey, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsResolutionEventBooleanEventPayloadKey(v.IsResolutionEventBooleanEventPayloadKey, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ErrorActivityConfig) setStaticFields(v *config.ErrorActivityConfig) error {

	return nil
}

func (obj *ErrorActivityConfig) SetIsPipingErrorEventToWatsonEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ErrorActivityConfig.IsPipingErrorEventToWatsonEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsPipingErrorEventToWatsonEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsPipingErrorEventToWatsonEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsPipingErrorEventToWatsonEnabled")
	}
	return nil
}
func (obj *ErrorActivityConfig) SetDefaultIncidentCreationCoolOffPeriod(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *ErrorActivityConfig.DefaultIncidentCreationCoolOffPeriod", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._DefaultIncidentCreationCoolOffPeriod, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "DefaultIncidentCreationCoolOffPeriod")
	}
	return nil
}
func (obj *ErrorActivityConfig) SetIssueCategoryIdEventPayloadKey(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ErrorActivityConfig.IssueCategoryIdEventPayloadKey", reflect.TypeOf(val))
	}
	obj._IssueCategoryIdEventPayloadKeyMutex.Lock()
	defer obj._IssueCategoryIdEventPayloadKeyMutex.Unlock()
	obj._IssueCategoryIdEventPayloadKey = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IssueCategoryIdEventPayloadKey")
	}
	return nil
}
func (obj *ErrorActivityConfig) SetClientRequestIdEventPayloadKey(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ErrorActivityConfig.ClientRequestIdEventPayloadKey", reflect.TypeOf(val))
	}
	obj._ClientRequestIdEventPayloadKeyMutex.Lock()
	defer obj._ClientRequestIdEventPayloadKeyMutex.Unlock()
	obj._ClientRequestIdEventPayloadKey = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ClientRequestIdEventPayloadKey")
	}
	return nil
}
func (obj *ErrorActivityConfig) SetIsResolutionEventBooleanEventPayloadKey(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ErrorActivityConfig.IsResolutionEventBooleanEventPayloadKey", reflect.TypeOf(val))
	}
	obj._IsResolutionEventBooleanEventPayloadKeyMutex.Lock()
	defer obj._IsResolutionEventBooleanEventPayloadKeyMutex.Unlock()
	obj._IsResolutionEventBooleanEventPayloadKey = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsResolutionEventBooleanEventPayloadKey")
	}
	return nil
}

func NewAgentPromptConfig() (_obj *AgentPromptConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &AgentPromptConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._AgentPromptInfoMap = &syncmap.Map[string, *AgentPromptInfo]{}
	_setters["agentpromptinfomap"] = _obj.SetAgentPromptInfoMap
	return _obj, _setters
}

func (obj *AgentPromptConfig) Init() {
	newObj, _ := NewAgentPromptConfig()
	*obj = *newObj
}

func (obj *AgentPromptConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AgentPromptConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AgentPromptConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *AgentPromptConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AgentPromptConfig) setDynamicField(v *config.AgentPromptConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "agentpromptinfomap":
		return obj.SetAgentPromptInfoMap(v.AgentPromptInfoMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AgentPromptConfig) setDynamicFields(v *config.AgentPromptConfig, dynamic bool, path []string) (err error) {

	err = obj.SetAgentPromptInfoMap(v.AgentPromptInfoMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AgentPromptConfig) setStaticFields(v *config.AgentPromptConfig) error {

	return nil
}

func (obj *AgentPromptConfig) SetAgentPromptInfoMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.AgentPromptInfo)
	if !ok {
		return fmt.Errorf("invalid data type %v *AgentPromptConfig.AgentPromptInfoMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._AgentPromptInfoMap, v, dynamic, path)

}

func NewAgentPromptInfo() (_obj *AgentPromptInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &AgentPromptInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ispromptenabled"] = _obj.SetIsPromptEnabled
	_setters["promptvalueforagent"] = _obj.SetPromptValueForAgent
	_obj._PromptValueForAgentMutex = &sync.RWMutex{}
	_setters["description"] = _obj.SetDescription
	_obj._DescriptionMutex = &sync.RWMutex{}
	_PromptCommsTemplate, _fieldSetters := NewPromptCommsTemplate()
	_obj._PromptCommsTemplate = _PromptCommsTemplate
	helper.AddFieldSetters("promptcommstemplate", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *AgentPromptInfo) Init() {
	newObj, _ := NewAgentPromptInfo()
	*obj = *newObj
}

func (obj *AgentPromptInfo) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AgentPromptInfo) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AgentPromptInfo)
	if !ok {
		return fmt.Errorf("invalid data type %v *AgentPromptInfo", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AgentPromptInfo) setDynamicField(v *config.AgentPromptInfo, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "ispromptenabled":
		return obj.SetIsPromptEnabled(v.IsPromptEnabled, true, nil)
	case "promptvalueforagent":
		return obj.SetPromptValueForAgent(v.PromptValueForAgent, true, nil)
	case "description":
		return obj.SetDescription(v.Description, true, nil)
	case "promptcommstemplate":
		return obj._PromptCommsTemplate.Set(v.PromptCommsTemplate, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AgentPromptInfo) setDynamicFields(v *config.AgentPromptInfo, dynamic bool, path []string) (err error) {

	err = obj.SetIsPromptEnabled(v.IsPromptEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPromptValueForAgent(v.PromptValueForAgent, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDescription(v.Description, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._PromptCommsTemplate.Set(v.PromptCommsTemplate, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AgentPromptInfo) setStaticFields(v *config.AgentPromptInfo) error {

	return nil
}

func (obj *AgentPromptInfo) SetIsPromptEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AgentPromptInfo.IsPromptEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsPromptEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsPromptEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsPromptEnabled")
	}
	return nil
}
func (obj *AgentPromptInfo) SetPromptValueForAgent(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AgentPromptInfo.PromptValueForAgent", reflect.TypeOf(val))
	}
	obj._PromptValueForAgentMutex.Lock()
	defer obj._PromptValueForAgentMutex.Unlock()
	obj._PromptValueForAgent = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "PromptValueForAgent")
	}
	return nil
}
func (obj *AgentPromptInfo) SetDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AgentPromptInfo.Description", reflect.TypeOf(val))
	}
	obj._DescriptionMutex.Lock()
	defer obj._DescriptionMutex.Unlock()
	obj._Description = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Description")
	}
	return nil
}

func NewPromptCommsTemplate() (_obj *PromptCommsTemplate, _setters map[string]dynconf.SetFunc) {
	_obj = &PromptCommsTemplate{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["description"] = _obj.SetDescription
	_obj._DescriptionMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *PromptCommsTemplate) Init() {
	newObj, _ := NewPromptCommsTemplate()
	*obj = *newObj
}

func (obj *PromptCommsTemplate) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PromptCommsTemplate) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PromptCommsTemplate)
	if !ok {
		return fmt.Errorf("invalid data type %v *PromptCommsTemplate", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PromptCommsTemplate) setDynamicField(v *config.PromptCommsTemplate, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "title":
		return obj.SetTitle(v.Title, true, nil)
	case "description":
		return obj.SetDescription(v.Description, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PromptCommsTemplate) setDynamicFields(v *config.PromptCommsTemplate, dynamic bool, path []string) (err error) {

	err = obj.SetTitle(v.Title, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDescription(v.Description, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PromptCommsTemplate) setStaticFields(v *config.PromptCommsTemplate) error {

	return nil
}

func (obj *PromptCommsTemplate) SetTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *PromptCommsTemplate.Title", reflect.TypeOf(val))
	}
	obj._TitleMutex.Lock()
	defer obj._TitleMutex.Unlock()
	obj._Title = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title")
	}
	return nil
}
func (obj *PromptCommsTemplate) SetDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *PromptCommsTemplate.Description", reflect.TypeOf(val))
	}
	obj._DescriptionMutex.Lock()
	defer obj._DescriptionMutex.Unlock()
	obj._Description = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Description")
	}
	return nil
}

func NewRiskOpsInstalledAppsConfig() (_obj *RiskOpsInstalledAppsConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &RiskOpsInstalledAppsConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	return _obj, _setters
}

func (obj *RiskOpsInstalledAppsConfig) Init() {
	newObj, _ := NewRiskOpsInstalledAppsConfig()
	*obj = *newObj
}

func (obj *RiskOpsInstalledAppsConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RiskOpsInstalledAppsConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RiskOpsInstalledAppsConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskOpsInstalledAppsConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RiskOpsInstalledAppsConfig) setDynamicField(v *config.RiskOpsInstalledAppsConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RiskOpsInstalledAppsConfig) setDynamicFields(v *config.RiskOpsInstalledAppsConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RiskOpsInstalledAppsConfig) setStaticFields(v *config.RiskOpsInstalledAppsConfig) error {

	obj._RiskScoreMinThreshold = v.RiskScoreMinThreshold
	obj._NumberOfLEAWithAppInstalledMinThreshold = v.NumberOfLEAWithAppInstalledMinThreshold
	return nil
}

func (obj *RiskOpsInstalledAppsConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskOpsInstalledAppsConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}

func NewRiskFennelConfig() (_obj *RiskFennelConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &RiskFennelConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["apiversion"] = _obj.SetAPIVersion
	return _obj, _setters
}

func (obj *RiskFennelConfig) Init() {
	newObj, _ := NewRiskFennelConfig()
	*obj = *newObj
}

func (obj *RiskFennelConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RiskFennelConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RiskFennelConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskFennelConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RiskFennelConfig) setDynamicField(v *config.RiskFennelConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "apiversion":
		return obj.SetAPIVersion(v.APIVersion, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RiskFennelConfig) setDynamicFields(v *config.RiskFennelConfig, dynamic bool, path []string) (err error) {

	err = obj.SetAPIVersion(v.APIVersion, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RiskFennelConfig) setStaticFields(v *config.RiskFennelConfig) error {

	return nil
}

func (obj *RiskFennelConfig) SetAPIVersion(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskFennelConfig.APIVersion", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._APIVersion, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "APIVersion")
	}
	return nil
}

func NewStageWiseCommsConfig() (_obj *StageWiseCommsConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &StageWiseCommsConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isgenericcommsenabled"] = _obj.SetIsGenericCommsEnabled
	_setters["isissueconfigspecificcommsenabled"] = _obj.SetIsIssueConfigSpecificCommsEnabled
	_setters["ispublishingmanualticketcreationeventenabled"] = _obj.SetIsPublishingManualTicketCreationEventEnabled
	_setters["ispublishingmanualticketupdateeventenabled"] = _obj.SetIsPublishingManualTicketUpdateEventEnabled
	return _obj, _setters
}

func (obj *StageWiseCommsConfig) Init() {
	newObj, _ := NewStageWiseCommsConfig()
	*obj = *newObj
}

func (obj *StageWiseCommsConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *StageWiseCommsConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.StageWiseCommsConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *StageWiseCommsConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *StageWiseCommsConfig) setDynamicField(v *config.StageWiseCommsConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isgenericcommsenabled":
		return obj.SetIsGenericCommsEnabled(v.IsGenericCommsEnabled, true, nil)
	case "isissueconfigspecificcommsenabled":
		return obj.SetIsIssueConfigSpecificCommsEnabled(v.IsIssueConfigSpecificCommsEnabled, true, nil)
	case "ispublishingmanualticketcreationeventenabled":
		return obj.SetIsPublishingManualTicketCreationEventEnabled(v.IsPublishingManualTicketCreationEventEnabled, true, nil)
	case "ispublishingmanualticketupdateeventenabled":
		return obj.SetIsPublishingManualTicketUpdateEventEnabled(v.IsPublishingManualTicketUpdateEventEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *StageWiseCommsConfig) setDynamicFields(v *config.StageWiseCommsConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsGenericCommsEnabled(v.IsGenericCommsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsIssueConfigSpecificCommsEnabled(v.IsIssueConfigSpecificCommsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsPublishingManualTicketCreationEventEnabled(v.IsPublishingManualTicketCreationEventEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsPublishingManualTicketUpdateEventEnabled(v.IsPublishingManualTicketUpdateEventEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *StageWiseCommsConfig) setStaticFields(v *config.StageWiseCommsConfig) error {

	return nil
}

func (obj *StageWiseCommsConfig) SetIsGenericCommsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *StageWiseCommsConfig.IsGenericCommsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsGenericCommsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsGenericCommsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsGenericCommsEnabled")
	}
	return nil
}
func (obj *StageWiseCommsConfig) SetIsIssueConfigSpecificCommsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *StageWiseCommsConfig.IsIssueConfigSpecificCommsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsIssueConfigSpecificCommsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsIssueConfigSpecificCommsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsIssueConfigSpecificCommsEnabled")
	}
	return nil
}
func (obj *StageWiseCommsConfig) SetIsPublishingManualTicketCreationEventEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *StageWiseCommsConfig.IsPublishingManualTicketCreationEventEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsPublishingManualTicketCreationEventEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsPublishingManualTicketCreationEventEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsPublishingManualTicketCreationEventEnabled")
	}
	return nil
}
func (obj *StageWiseCommsConfig) SetIsPublishingManualTicketUpdateEventEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *StageWiseCommsConfig.IsPublishingManualTicketUpdateEventEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsPublishingManualTicketUpdateEventEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsPublishingManualTicketUpdateEventEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsPublishingManualTicketUpdateEventEnabled")
	}
	return nil
}

func NewIssueConfigServiceConfig() (_obj *IssueConfigServiceConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &IssueConfigServiceConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["usenewcategorymappingforllmscreen"] = _obj.SetUseNewCategoryMappingForLLMScreen
	_setters["iscacheenabled"] = _obj.SetIsCacheEnabled
	_setters["issueconfiglevelcachevalidityduration"] = _obj.SetIssueConfigLevelCacheValidityDuration

	_obj._ConfigTypeMapping = &syncmap.Map[string, string]{}
	_setters["configtypemapping"] = _obj.SetConfigTypeMapping
	_setters["issueconfiglevelcachekey"] = _obj.SetIssueConfigLevelCacheKey
	_obj._IssueConfigLevelCacheKeyMutex = &sync.RWMutex{}
	_setters["issuecategorycreatedfromtime"] = _obj.SetIssueCategoryCreatedFromTime
	_obj._IssueCategoryCreatedFromTimeMutex = &sync.RWMutex{}
	_setters["issuecategorycreatedtotime"] = _obj.SetIssueCategoryCreatedToTime
	_obj._IssueCategoryCreatedToTimeMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *IssueConfigServiceConfig) Init() {
	newObj, _ := NewIssueConfigServiceConfig()
	*obj = *newObj
}

func (obj *IssueConfigServiceConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *IssueConfigServiceConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.IssueConfigServiceConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *IssueConfigServiceConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *IssueConfigServiceConfig) setDynamicField(v *config.IssueConfigServiceConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "usenewcategorymappingforllmscreen":
		return obj.SetUseNewCategoryMappingForLLMScreen(v.UseNewCategoryMappingForLLMScreen, true, nil)
	case "iscacheenabled":
		return obj.SetIsCacheEnabled(v.IsCacheEnabled, true, nil)
	case "issueconfiglevelcachevalidityduration":
		return obj.SetIssueConfigLevelCacheValidityDuration(v.IssueConfigLevelCacheValidityDuration, true, nil)
	case "configtypemapping":
		return obj.SetConfigTypeMapping(v.ConfigTypeMapping, true, path)
	case "issueconfiglevelcachekey":
		return obj.SetIssueConfigLevelCacheKey(v.IssueConfigLevelCacheKey, true, nil)
	case "issuecategorycreatedfromtime":
		return obj.SetIssueCategoryCreatedFromTime(v.IssueCategoryCreatedFromTime, true, nil)
	case "issuecategorycreatedtotime":
		return obj.SetIssueCategoryCreatedToTime(v.IssueCategoryCreatedToTime, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *IssueConfigServiceConfig) setDynamicFields(v *config.IssueConfigServiceConfig, dynamic bool, path []string) (err error) {

	err = obj.SetUseNewCategoryMappingForLLMScreen(v.UseNewCategoryMappingForLLMScreen, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsCacheEnabled(v.IsCacheEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIssueConfigLevelCacheValidityDuration(v.IssueConfigLevelCacheValidityDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetConfigTypeMapping(v.ConfigTypeMapping, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetIssueConfigLevelCacheKey(v.IssueConfigLevelCacheKey, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIssueCategoryCreatedFromTime(v.IssueCategoryCreatedFromTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIssueCategoryCreatedToTime(v.IssueCategoryCreatedToTime, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *IssueConfigServiceConfig) setStaticFields(v *config.IssueConfigServiceConfig) error {

	return nil
}

func (obj *IssueConfigServiceConfig) SetUseNewCategoryMappingForLLMScreen(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *IssueConfigServiceConfig.UseNewCategoryMappingForLLMScreen", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UseNewCategoryMappingForLLMScreen, 1)
	} else {
		atomic.StoreUint32(&obj._UseNewCategoryMappingForLLMScreen, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UseNewCategoryMappingForLLMScreen")
	}
	return nil
}
func (obj *IssueConfigServiceConfig) SetIsCacheEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *IssueConfigServiceConfig.IsCacheEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCacheEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCacheEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCacheEnabled")
	}
	return nil
}
func (obj *IssueConfigServiceConfig) SetIssueConfigLevelCacheValidityDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *IssueConfigServiceConfig.IssueConfigLevelCacheValidityDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._IssueConfigLevelCacheValidityDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "IssueConfigLevelCacheValidityDuration")
	}
	return nil
}
func (obj *IssueConfigServiceConfig) SetConfigTypeMapping(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *IssueConfigServiceConfig.ConfigTypeMapping", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._ConfigTypeMapping, v, path)
}
func (obj *IssueConfigServiceConfig) SetIssueConfigLevelCacheKey(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *IssueConfigServiceConfig.IssueConfigLevelCacheKey", reflect.TypeOf(val))
	}
	obj._IssueConfigLevelCacheKeyMutex.Lock()
	defer obj._IssueConfigLevelCacheKeyMutex.Unlock()
	obj._IssueConfigLevelCacheKey = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IssueConfigLevelCacheKey")
	}
	return nil
}
func (obj *IssueConfigServiceConfig) SetIssueCategoryCreatedFromTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Time)
	if !ok {
		return fmt.Errorf("invalid data type %v *IssueConfigServiceConfig.IssueCategoryCreatedFromTime", reflect.TypeOf(val))
	}
	obj._IssueCategoryCreatedFromTimeMutex.Lock()
	defer obj._IssueCategoryCreatedFromTimeMutex.Unlock()
	obj._IssueCategoryCreatedFromTime = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IssueCategoryCreatedFromTime")
	}
	return nil
}
func (obj *IssueConfigServiceConfig) SetIssueCategoryCreatedToTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Time)
	if !ok {
		return fmt.Errorf("invalid data type %v *IssueConfigServiceConfig.IssueCategoryCreatedToTime", reflect.TypeOf(val))
	}
	obj._IssueCategoryCreatedToTimeMutex.Lock()
	defer obj._IssueCategoryCreatedToTimeMutex.Unlock()
	obj._IssueCategoryCreatedToTime = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IssueCategoryCreatedToTime")
	}
	return nil
}

func NewS3EventConsumerConfig() (_obj *S3EventConsumerConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &S3EventConsumerConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iscallsummarizationprocessingenabled"] = _obj.SetIsCallSummarizationProcessingEnabled
	_setters["bucketname"] = _obj.SetBucketName
	_obj._BucketNameMutex = &sync.RWMutex{}
	_setters["callsummarizationfilepath"] = _obj.SetCallSummarizationFilePath
	_obj._CallSummarizationFilePathMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *S3EventConsumerConfig) Init() {
	newObj, _ := NewS3EventConsumerConfig()
	*obj = *newObj
}

func (obj *S3EventConsumerConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *S3EventConsumerConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.S3EventConsumerConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *S3EventConsumerConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *S3EventConsumerConfig) setDynamicField(v *config.S3EventConsumerConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iscallsummarizationprocessingenabled":
		return obj.SetIsCallSummarizationProcessingEnabled(v.IsCallSummarizationProcessingEnabled, true, nil)
	case "bucketname":
		return obj.SetBucketName(v.BucketName, true, nil)
	case "callsummarizationfilepath":
		return obj.SetCallSummarizationFilePath(v.CallSummarizationFilePath, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *S3EventConsumerConfig) setDynamicFields(v *config.S3EventConsumerConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsCallSummarizationProcessingEnabled(v.IsCallSummarizationProcessingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBucketName(v.BucketName, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCallSummarizationFilePath(v.CallSummarizationFilePath, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *S3EventConsumerConfig) setStaticFields(v *config.S3EventConsumerConfig) error {

	return nil
}

func (obj *S3EventConsumerConfig) SetIsCallSummarizationProcessingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *S3EventConsumerConfig.IsCallSummarizationProcessingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCallSummarizationProcessingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCallSummarizationProcessingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCallSummarizationProcessingEnabled")
	}
	return nil
}
func (obj *S3EventConsumerConfig) SetBucketName(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *S3EventConsumerConfig.BucketName", reflect.TypeOf(val))
	}
	obj._BucketNameMutex.Lock()
	defer obj._BucketNameMutex.Unlock()
	obj._BucketName = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BucketName")
	}
	return nil
}
func (obj *S3EventConsumerConfig) SetCallSummarizationFilePath(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *S3EventConsumerConfig.CallSummarizationFilePath", reflect.TypeOf(val))
	}
	obj._CallSummarizationFilePathMutex.Lock()
	defer obj._CallSummarizationFilePathMutex.Unlock()
	obj._CallSummarizationFilePath = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CallSummarizationFilePath")
	}
	return nil
}

func NewRiskTxnReviewRolloutConfig() (_obj *RiskTxnReviewRolloutConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &RiskTxnReviewRolloutConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isselectedorderrpcenabledforall"] = _obj.SetIsSelectedOrderRpcEnabledForAll
	_setters["selectedorderrpcwhitelistedemails"] = _obj.SetSelectedOrderRpcWhitelistedEmails
	_obj._SelectedOrderRpcWhitelistedEmailsMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *RiskTxnReviewRolloutConfig) Init() {
	newObj, _ := NewRiskTxnReviewRolloutConfig()
	*obj = *newObj
}

func (obj *RiskTxnReviewRolloutConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RiskTxnReviewRolloutConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RiskTxnReviewRolloutConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskTxnReviewRolloutConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RiskTxnReviewRolloutConfig) setDynamicField(v *config.RiskTxnReviewRolloutConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isselectedorderrpcenabledforall":
		return obj.SetIsSelectedOrderRpcEnabledForAll(v.IsSelectedOrderRpcEnabledForAll, true, nil)
	case "selectedorderrpcwhitelistedemails":
		return obj.SetSelectedOrderRpcWhitelistedEmails(v.SelectedOrderRpcWhitelistedEmails, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RiskTxnReviewRolloutConfig) setDynamicFields(v *config.RiskTxnReviewRolloutConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsSelectedOrderRpcEnabledForAll(v.IsSelectedOrderRpcEnabledForAll, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSelectedOrderRpcWhitelistedEmails(v.SelectedOrderRpcWhitelistedEmails, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RiskTxnReviewRolloutConfig) setStaticFields(v *config.RiskTxnReviewRolloutConfig) error {

	return nil
}

func (obj *RiskTxnReviewRolloutConfig) SetIsSelectedOrderRpcEnabledForAll(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskTxnReviewRolloutConfig.IsSelectedOrderRpcEnabledForAll", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsSelectedOrderRpcEnabledForAll, 1)
	} else {
		atomic.StoreUint32(&obj._IsSelectedOrderRpcEnabledForAll, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsSelectedOrderRpcEnabledForAll")
	}
	return nil
}
func (obj *RiskTxnReviewRolloutConfig) SetSelectedOrderRpcWhitelistedEmails(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskTxnReviewRolloutConfig.SelectedOrderRpcWhitelistedEmails", reflect.TypeOf(val))
	}
	obj._SelectedOrderRpcWhitelistedEmailsMutex.Lock()
	defer obj._SelectedOrderRpcWhitelistedEmailsMutex.Unlock()
	obj._SelectedOrderRpcWhitelistedEmails = roarray.New[string](v)
	return nil
}

func NewRiskOutcallFormRolloutConfig() (_obj *RiskOutcallFormRolloutConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &RiskOutcallFormRolloutConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxformsperuser"] = _obj.SetMaxFormsPerUser
	_setters["disableforallagents"] = _obj.SetDisableForAllAgents
	_setters["whitelistedreviewtypes"] = _obj.SetWhitelistedReviewTypes
	_obj._WhitelistedReviewTypesMutex = &sync.RWMutex{}
	_setters["whitelistedquestionnairetemplates"] = _obj.SetWhitelistedQuestionnaireTemplates
	_obj._WhitelistedQuestionnaireTemplatesMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *RiskOutcallFormRolloutConfig) Init() {
	newObj, _ := NewRiskOutcallFormRolloutConfig()
	*obj = *newObj
}

func (obj *RiskOutcallFormRolloutConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RiskOutcallFormRolloutConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RiskOutcallFormRolloutConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskOutcallFormRolloutConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RiskOutcallFormRolloutConfig) setDynamicField(v *config.RiskOutcallFormRolloutConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maxformsperuser":
		return obj.SetMaxFormsPerUser(v.MaxFormsPerUser, true, nil)
	case "disableforallagents":
		return obj.SetDisableForAllAgents(v.DisableForAllAgents, true, nil)
	case "whitelistedreviewtypes":
		return obj.SetWhitelistedReviewTypes(v.WhitelistedReviewTypes, true, path)
	case "whitelistedquestionnairetemplates":
		return obj.SetWhitelistedQuestionnaireTemplates(v.WhitelistedQuestionnaireTemplates, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RiskOutcallFormRolloutConfig) setDynamicFields(v *config.RiskOutcallFormRolloutConfig, dynamic bool, path []string) (err error) {

	err = obj.SetMaxFormsPerUser(v.MaxFormsPerUser, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableForAllAgents(v.DisableForAllAgents, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetWhitelistedReviewTypes(v.WhitelistedReviewTypes, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetWhitelistedQuestionnaireTemplates(v.WhitelistedQuestionnaireTemplates, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RiskOutcallFormRolloutConfig) setStaticFields(v *config.RiskOutcallFormRolloutConfig) error {

	return nil
}

func (obj *RiskOutcallFormRolloutConfig) SetMaxFormsPerUser(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskOutcallFormRolloutConfig.MaxFormsPerUser", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxFormsPerUser, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxFormsPerUser")
	}
	return nil
}
func (obj *RiskOutcallFormRolloutConfig) SetDisableForAllAgents(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskOutcallFormRolloutConfig.DisableForAllAgents", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableForAllAgents, 1)
	} else {
		atomic.StoreUint32(&obj._DisableForAllAgents, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableForAllAgents")
	}
	return nil
}
func (obj *RiskOutcallFormRolloutConfig) SetWhitelistedReviewTypes(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskOutcallFormRolloutConfig.WhitelistedReviewTypes", reflect.TypeOf(val))
	}
	obj._WhitelistedReviewTypesMutex.Lock()
	defer obj._WhitelistedReviewTypesMutex.Unlock()
	obj._WhitelistedReviewTypes = roarray.New[string](v)
	return nil
}
func (obj *RiskOutcallFormRolloutConfig) SetWhitelistedQuestionnaireTemplates(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskOutcallFormRolloutConfig.WhitelistedQuestionnaireTemplates", reflect.TypeOf(val))
	}
	obj._WhitelistedQuestionnaireTemplatesMutex.Lock()
	defer obj._WhitelistedQuestionnaireTemplatesMutex.Unlock()
	obj._WhitelistedQuestionnaireTemplates = roarray.New[string](v)
	return nil
}

func NewCallIvrConfig() (_obj *CallIvrConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CallIvrConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ivrpollcountthreshold"] = _obj.SetIvrPollCountThreshold
	_setters["maxinvalidinputcount"] = _obj.SetMaxInvalidInputCount
	_setters["maxrepeatinputcount"] = _obj.SetMaxRepeatInputCount
	_setters["isivrenabled"] = _obj.SetIsIvrEnabled
	_setters["iscardblockingenabled"] = _obj.SetIsCardBlockingEnabled
	_setters["ivrpollcountcacheduration"] = _obj.SetIvrPollCountCacheDuration
	_setters["ivrpollcountcachekey"] = _obj.SetIvrPollCountCacheKey
	_obj._IvrPollCountCacheKeyMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *CallIvrConfig) Init() {
	newObj, _ := NewCallIvrConfig()
	*obj = *newObj
}

func (obj *CallIvrConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CallIvrConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CallIvrConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallIvrConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CallIvrConfig) setDynamicField(v *config.CallIvrConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "ivrpollcountthreshold":
		return obj.SetIvrPollCountThreshold(v.IvrPollCountThreshold, true, nil)
	case "maxinvalidinputcount":
		return obj.SetMaxInvalidInputCount(v.MaxInvalidInputCount, true, nil)
	case "maxrepeatinputcount":
		return obj.SetMaxRepeatInputCount(v.MaxRepeatInputCount, true, nil)
	case "isivrenabled":
		return obj.SetIsIvrEnabled(v.IsIvrEnabled, true, nil)
	case "iscardblockingenabled":
		return obj.SetIsCardBlockingEnabled(v.IsCardBlockingEnabled, true, nil)
	case "ivrpollcountcacheduration":
		return obj.SetIvrPollCountCacheDuration(v.IvrPollCountCacheDuration, true, nil)
	case "ivrpollcountcachekey":
		return obj.SetIvrPollCountCacheKey(v.IvrPollCountCacheKey, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CallIvrConfig) setDynamicFields(v *config.CallIvrConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIvrPollCountThreshold(v.IvrPollCountThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxInvalidInputCount(v.MaxInvalidInputCount, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxRepeatInputCount(v.MaxRepeatInputCount, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsIvrEnabled(v.IsIvrEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsCardBlockingEnabled(v.IsCardBlockingEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIvrPollCountCacheDuration(v.IvrPollCountCacheDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIvrPollCountCacheKey(v.IvrPollCountCacheKey, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CallIvrConfig) setStaticFields(v *config.CallIvrConfig) error {

	return nil
}

func (obj *CallIvrConfig) SetIvrPollCountThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallIvrConfig.IvrPollCountThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._IvrPollCountThreshold, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "IvrPollCountThreshold")
	}
	return nil
}
func (obj *CallIvrConfig) SetMaxInvalidInputCount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallIvrConfig.MaxInvalidInputCount", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MaxInvalidInputCount, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxInvalidInputCount")
	}
	return nil
}
func (obj *CallIvrConfig) SetMaxRepeatInputCount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallIvrConfig.MaxRepeatInputCount", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MaxRepeatInputCount, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxRepeatInputCount")
	}
	return nil
}
func (obj *CallIvrConfig) SetIsIvrEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallIvrConfig.IsIvrEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsIvrEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsIvrEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsIvrEnabled")
	}
	return nil
}
func (obj *CallIvrConfig) SetIsCardBlockingEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallIvrConfig.IsCardBlockingEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsCardBlockingEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsCardBlockingEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsCardBlockingEnabled")
	}
	return nil
}
func (obj *CallIvrConfig) SetIvrPollCountCacheDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallIvrConfig.IvrPollCountCacheDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._IvrPollCountCacheDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "IvrPollCountCacheDuration")
	}
	return nil
}
func (obj *CallIvrConfig) SetIvrPollCountCacheKey(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallIvrConfig.IvrPollCountCacheKey", reflect.TypeOf(val))
	}
	obj._IvrPollCountCacheKeyMutex.Lock()
	defer obj._IvrPollCountCacheKeyMutex.Unlock()
	obj._IvrPollCountCacheKey = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IvrPollCountCacheKey")
	}
	return nil
}

func NewCaseManagementActorActivities() (_obj *CaseManagementActorActivities, _setters map[string]dynconf.SetFunc) {
	_obj = &CaseManagementActorActivities{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["timeout"] = _obj.SetTimeout
	_setters["allowedreviewtypes"] = _obj.SetAllowedReviewTypes
	_obj._AllowedReviewTypesMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *CaseManagementActorActivities) Init() {
	newObj, _ := NewCaseManagementActorActivities()
	*obj = *newObj
}

func (obj *CaseManagementActorActivities) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CaseManagementActorActivities) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CaseManagementActorActivities)
	if !ok {
		return fmt.Errorf("invalid data type %v *CaseManagementActorActivities", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CaseManagementActorActivities) setDynamicField(v *config.CaseManagementActorActivities, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "timeout":
		return obj.SetTimeout(v.Timeout, true, nil)
	case "allowedreviewtypes":
		return obj.SetAllowedReviewTypes(v.AllowedReviewTypes, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CaseManagementActorActivities) setDynamicFields(v *config.CaseManagementActorActivities, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTimeout(v.Timeout, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAllowedReviewTypes(v.AllowedReviewTypes, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CaseManagementActorActivities) setStaticFields(v *config.CaseManagementActorActivities) error {

	return nil
}

func (obj *CaseManagementActorActivities) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CaseManagementActorActivities.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *CaseManagementActorActivities) SetTimeout(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *CaseManagementActorActivities.Timeout", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._Timeout, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Timeout")
	}
	return nil
}
func (obj *CaseManagementActorActivities) SetAllowedReviewTypes(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CaseManagementActorActivities.AllowedReviewTypes", reflect.TypeOf(val))
	}
	obj._AllowedReviewTypesMutex.Lock()
	defer obj._AllowedReviewTypesMutex.Unlock()
	obj._AllowedReviewTypes = roarray.New[string](v)
	return nil
}

func NewWhiteListAccessLevelForActorEnrichmentByPhoneOrEmail() (_obj *WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail, _setters map[string]dynconf.SetFunc) {
	_obj = &WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["allowedaccesslevels"] = _obj.SetAllowedAccessLevels
	_obj._AllowedAccessLevelsMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail) Init() {
	newObj, _ := NewWhiteListAccessLevelForActorEnrichmentByPhoneOrEmail()
	*obj = *newObj
}

func (obj *WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail)
	if !ok {
		return fmt.Errorf("invalid data type %v *WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail) setDynamicField(v *config.WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "allowedaccesslevels":
		return obj.SetAllowedAccessLevels(v.AllowedAccessLevels, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail) setDynamicFields(v *config.WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail, dynamic bool, path []string) (err error) {

	err = obj.SetAllowedAccessLevels(v.AllowedAccessLevels, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail) setStaticFields(v *config.WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail) error {

	return nil
}

func (obj *WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail) SetAllowedAccessLevels(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail.AllowedAccessLevels", reflect.TypeOf(val))
	}
	obj._AllowedAccessLevelsMutex.Lock()
	defer obj._AllowedAccessLevelsMutex.Unlock()
	obj._AllowedAccessLevels = roarray.New[string](v)
	return nil
}

func NewContactUsModelResponseConfig() (_obj *ContactUsModelResponseConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ContactUsModelResponseConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["responsecachevalidityduration"] = _obj.SetResponseCacheValidityDuration
	_setters["responsecachekey"] = _obj.SetResponseCacheKey
	_obj._ResponseCacheKeyMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *ContactUsModelResponseConfig) Init() {
	newObj, _ := NewContactUsModelResponseConfig()
	*obj = *newObj
}

func (obj *ContactUsModelResponseConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ContactUsModelResponseConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ContactUsModelResponseConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ContactUsModelResponseConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ContactUsModelResponseConfig) setDynamicField(v *config.ContactUsModelResponseConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "responsecachevalidityduration":
		return obj.SetResponseCacheValidityDuration(v.ResponseCacheValidityDuration, true, nil)
	case "responsecachekey":
		return obj.SetResponseCacheKey(v.ResponseCacheKey, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ContactUsModelResponseConfig) setDynamicFields(v *config.ContactUsModelResponseConfig, dynamic bool, path []string) (err error) {

	err = obj.SetResponseCacheValidityDuration(v.ResponseCacheValidityDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetResponseCacheKey(v.ResponseCacheKey, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ContactUsModelResponseConfig) setStaticFields(v *config.ContactUsModelResponseConfig) error {

	return nil
}

func (obj *ContactUsModelResponseConfig) SetResponseCacheValidityDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *ContactUsModelResponseConfig.ResponseCacheValidityDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ResponseCacheValidityDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ResponseCacheValidityDuration")
	}
	return nil
}
func (obj *ContactUsModelResponseConfig) SetResponseCacheKey(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ContactUsModelResponseConfig.ResponseCacheKey", reflect.TypeOf(val))
	}
	obj._ResponseCacheKeyMutex.Lock()
	defer obj._ResponseCacheKeyMutex.Unlock()
	obj._ResponseCacheKey = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ResponseCacheKey")
	}
	return nil
}

func NewFilegenerator() (_obj *Filegenerator, _setters map[string]dynconf.SetFunc) {
	_obj = &Filegenerator{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["camss3bucket"] = _obj.SetCamsS3Bucket
	_obj._CamsS3BucketMutex = &sync.RWMutex{}
	_setters["karvys3bucket"] = _obj.SetKarvyS3Bucket
	_obj._KarvyS3BucketMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *Filegenerator) Init() {
	newObj, _ := NewFilegenerator()
	*obj = *newObj
}

func (obj *Filegenerator) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Filegenerator) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Filegenerator)
	if !ok {
		return fmt.Errorf("invalid data type %v *Filegenerator", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Filegenerator) setDynamicField(v *config.Filegenerator, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "camss3bucket":
		return obj.SetCamsS3Bucket(v.CamsS3Bucket, true, nil)
	case "karvys3bucket":
		return obj.SetKarvyS3Bucket(v.KarvyS3Bucket, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Filegenerator) setDynamicFields(v *config.Filegenerator, dynamic bool, path []string) (err error) {

	err = obj.SetCamsS3Bucket(v.CamsS3Bucket, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetKarvyS3Bucket(v.KarvyS3Bucket, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Filegenerator) setStaticFields(v *config.Filegenerator) error {

	return nil
}

func (obj *Filegenerator) SetCamsS3Bucket(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Filegenerator.CamsS3Bucket", reflect.TypeOf(val))
	}
	obj._CamsS3BucketMutex.Lock()
	defer obj._CamsS3BucketMutex.Unlock()
	obj._CamsS3Bucket = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CamsS3Bucket")
	}
	return nil
}
func (obj *Filegenerator) SetKarvyS3Bucket(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Filegenerator.KarvyS3Bucket", reflect.TypeOf(val))
	}
	obj._KarvyS3BucketMutex.Lock()
	defer obj._KarvyS3BucketMutex.Unlock()
	obj._KarvyS3Bucket = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "KarvyS3Bucket")
	}
	return nil
}

func NewDbStateConfig() (_obj *DbStateConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DbStateConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isrbacenabled"] = _obj.SetIsRbacEnabled
	return _obj, _setters
}

func (obj *DbStateConfig) Init() {
	newObj, _ := NewDbStateConfig()
	*obj = *newObj
}

func (obj *DbStateConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DbStateConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DbStateConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *DbStateConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DbStateConfig) setDynamicField(v *config.DbStateConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isrbacenabled":
		return obj.SetIsRbacEnabled(v.IsRbacEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DbStateConfig) setDynamicFields(v *config.DbStateConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsRbacEnabled(v.IsRbacEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DbStateConfig) setStaticFields(v *config.DbStateConfig) error {

	return nil
}

func (obj *DbStateConfig) SetIsRbacEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DbStateConfig.IsRbacEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsRbacEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsRbacEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsRbacEnabled")
	}
	return nil
}

func NewEscalationConfig() (_obj *EscalationConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &EscalationConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isescalationenabled"] = _obj.SetIsEscalationEnabled
	return _obj, _setters
}

func (obj *EscalationConfig) Init() {
	newObj, _ := NewEscalationConfig()
	*obj = *newObj
}

func (obj *EscalationConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EscalationConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.EscalationConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *EscalationConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EscalationConfig) setDynamicField(v *config.EscalationConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isescalationenabled":
		return obj.SetIsEscalationEnabled(v.IsEscalationEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EscalationConfig) setDynamicFields(v *config.EscalationConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEscalationEnabled(v.IsEscalationEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EscalationConfig) setStaticFields(v *config.EscalationConfig) error {

	return nil
}

func (obj *EscalationConfig) SetIsEscalationEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *EscalationConfig.IsEscalationEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEscalationEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEscalationEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEscalationEnabled")
	}
	return nil
}

func NewFederalEscalationConfig() (_obj *FederalEscalationConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &FederalEscalationConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["qphratelimit"] = _obj.SetQPHRateLimit
	_setters["isupdateconsumerenabled"] = _obj.SetIsUpdateConsumerEnabled
	_setters["queueid"] = _obj.SetQueueId
	_obj._QueueIdMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *FederalEscalationConfig) Init() {
	newObj, _ := NewFederalEscalationConfig()
	*obj = *newObj
}

func (obj *FederalEscalationConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FederalEscalationConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.FederalEscalationConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *FederalEscalationConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FederalEscalationConfig) setDynamicField(v *config.FederalEscalationConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "qphratelimit":
		return obj.SetQPHRateLimit(v.QPHRateLimit, true, nil)
	case "isupdateconsumerenabled":
		return obj.SetIsUpdateConsumerEnabled(v.IsUpdateConsumerEnabled, true, nil)
	case "queueid":
		return obj.SetQueueId(v.QueueId, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FederalEscalationConfig) setDynamicFields(v *config.FederalEscalationConfig, dynamic bool, path []string) (err error) {

	err = obj.SetQPHRateLimit(v.QPHRateLimit, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsUpdateConsumerEnabled(v.IsUpdateConsumerEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetQueueId(v.QueueId, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FederalEscalationConfig) setStaticFields(v *config.FederalEscalationConfig) error {

	obj._FederalEscalationAttachmentBucketName = v.FederalEscalationAttachmentBucketName
	return nil
}

func (obj *FederalEscalationConfig) SetQPHRateLimit(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *FederalEscalationConfig.QPHRateLimit", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._QPHRateLimit, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "QPHRateLimit")
	}
	return nil
}
func (obj *FederalEscalationConfig) SetIsUpdateConsumerEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FederalEscalationConfig.IsUpdateConsumerEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsUpdateConsumerEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsUpdateConsumerEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsUpdateConsumerEnabled")
	}
	return nil
}
func (obj *FederalEscalationConfig) SetQueueId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FederalEscalationConfig.QueueId", reflect.TypeOf(val))
	}
	obj._QueueIdMutex.Lock()
	defer obj._QueueIdMutex.Unlock()
	obj._QueueId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "QueueId")
	}
	return nil
}

func NewSaClosureEligibilityConf() (_obj *SaClosureEligibilityConf, _setters map[string]dynconf.SetFunc) {
	_obj = &SaClosureEligibilityConf{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["qpsratelimit"] = _obj.SetQpsRateLimit
	_setters["maxconcurrentworkers"] = _obj.SetMaxConcurrentWorkers
	_setters["maxactorsallowedincsv"] = _obj.SetMaxActorsAllowedInCsv
	_setters["maxprocessingduration"] = _obj.SetMaxProcessingDuration
	return _obj, _setters
}

func (obj *SaClosureEligibilityConf) Init() {
	newObj, _ := NewSaClosureEligibilityConf()
	*obj = *newObj
}

func (obj *SaClosureEligibilityConf) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SaClosureEligibilityConf) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SaClosureEligibilityConf)
	if !ok {
		return fmt.Errorf("invalid data type %v *SaClosureEligibilityConf", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SaClosureEligibilityConf) setDynamicField(v *config.SaClosureEligibilityConf, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "qpsratelimit":
		return obj.SetQpsRateLimit(v.QpsRateLimit, true, nil)
	case "maxconcurrentworkers":
		return obj.SetMaxConcurrentWorkers(v.MaxConcurrentWorkers, true, nil)
	case "maxactorsallowedincsv":
		return obj.SetMaxActorsAllowedInCsv(v.MaxActorsAllowedInCsv, true, nil)
	case "maxprocessingduration":
		return obj.SetMaxProcessingDuration(v.MaxProcessingDuration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SaClosureEligibilityConf) setDynamicFields(v *config.SaClosureEligibilityConf, dynamic bool, path []string) (err error) {

	err = obj.SetQpsRateLimit(v.QpsRateLimit, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxConcurrentWorkers(v.MaxConcurrentWorkers, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxActorsAllowedInCsv(v.MaxActorsAllowedInCsv, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxProcessingDuration(v.MaxProcessingDuration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SaClosureEligibilityConf) setStaticFields(v *config.SaClosureEligibilityConf) error {

	return nil
}

func (obj *SaClosureEligibilityConf) SetQpsRateLimit(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *SaClosureEligibilityConf.QpsRateLimit", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._QpsRateLimit, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "QpsRateLimit")
	}
	return nil
}
func (obj *SaClosureEligibilityConf) SetMaxConcurrentWorkers(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *SaClosureEligibilityConf.MaxConcurrentWorkers", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._MaxConcurrentWorkers, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxConcurrentWorkers")
	}
	return nil
}
func (obj *SaClosureEligibilityConf) SetMaxActorsAllowedInCsv(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *SaClosureEligibilityConf.MaxActorsAllowedInCsv", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._MaxActorsAllowedInCsv, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxActorsAllowedInCsv")
	}
	return nil
}
func (obj *SaClosureEligibilityConf) SetMaxProcessingDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *SaClosureEligibilityConf.MaxProcessingDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxProcessingDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxProcessingDuration")
	}
	return nil
}
