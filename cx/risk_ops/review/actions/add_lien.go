package actions

// nolint: goimports,
import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"github.com/epifi/be-common/pkg/logger"

	vendorgatewayPb "github.com/epifi/be-common/api/vendorgateway"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/cx/risk_ops"
	cmPb "github.com/epifi/gamma/api/risk/case_management"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	enumsPb "github.com/epifi/gamma/api/risk/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/typesv2/account"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/cx/config"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

type AddLien struct {
	caseManagementClient cmPb.CaseManagementClient
	savingsClient        savingsPb.SavingsClient
	config               *config.Config
}

func (f *AddLien) FetchFormFields(ctx context.Context, actionInput *ActionInput) ([]*risk_ops.FormField, error) {
	return nil, epifierrors.ErrUnimplemented
}

func NewAddLien(caseManagementClient cmPb.CaseManagementClient, savingsClient savingsPb.SavingsClient,
	config *config.Config) *AddLien {
	return &AddLien{
		caseManagementClient: caseManagementClient,
		savingsClient:        savingsClient,
		config:               config,
	}
}

// nolint: gosec
func (f *AddLien) IsPermitted(ctx context.Context, actionInput *ActionInput) (bool, error) {

	isReasonForLienAllowed := false
	for _, allowedReason := range f.config.LienConfig.AllowedReasons {
		if allowedReason == string(actionInput.freezeReason) {
			isReasonForLienAllowed = true
			break
		}
	}
	logger.Info(ctx, "isReasonForLienAllowed logging", zap.Bool("isReasonForLienAllowed", isReasonForLienAllowed))
	if !isReasonForLienAllowed {
		return false, nil
	}

	rand.Seed(time.Now().UnixNano()) // Seed with current time
	randInt := rand.Intn(100)
	logger.Info(ctx, "Rollout percentage", zap.Int("rollout", randInt))
	if randInt >= f.config.LienConfig.LienRolloutPercentage {
		return false, nil
	}

	reviewType := actionInput.GetCaseDetails().GetReviewType()
	// if case is in user info flow or user does not have an account, don't allow the action
	return actionInput.GetCaseDetails().GetStatus() != reviewPb.Status_STATUS_PENDING_USER_INFO &&
		actionInput.GetOnboardingDetailsMin().GetAccountCreationStatus() == onboardingPb.OnboardingState_SUCCESS &&
		reviewType != reviewPb.ReviewType_REVIEW_TYPE_AFU_REVIEW &&
		reviewType != reviewPb.ReviewType_REVIEW_TYPE_LEA_COMPLAINT_REVIEW &&
		reviewType != reviewPb.ReviewType_REVIEW_TYPE_ESCALATION_REVIEW, nil
}

func (f *AddLien) Perform(ctx context.Context, actionInput *ActionInput, actionParameters []*dsPb.Filter) ([]*actionPb.ActionResponse, error) {
	performActionReq, err := f.buildPerformActionRequest(ctx, actionInput.GetCaseDetails(), actionInput.GetHeader().GetAgentEmail(), actionParameters)
	if err != nil {
		return nil, fmt.Errorf("failed to build perform action request %w", err)
	}

	resp, err := f.caseManagementClient.PerformAction(ctx, performActionReq)
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return nil, fmt.Errorf("failed to perform action %w", err)
	}

	res, err := protojson.Marshal(resp)
	if err != nil {
		cxLogger.Error(ctx, "cannot marshal perform action response to json", zap.Error(err))
		return []*actionPb.ActionResponse{}, nil
	}
	return []*actionPb.ActionResponse{
		{
			ActionResponseType: actionPb.ActionResponseType_JSON,
			ActionResponse: &actionPb.ActionResponse_JsonResp{
				JsonResp: string(res),
			},
		},
	}, nil
}

func (f *AddLien) FetchFormElements(ctx context.Context, actionInput *ActionInput) ([]*dsPb.ParameterMeta, error) {
	return nil, epifierrors.ErrUnimplemented
}

// nolint: gosec
func (f *AddLien) buildPerformActionRequest(ctx context.Context, caseDetails *reviewPb.Case, analystEmail string, actionParameters []*dsPb.Filter) (*cmPb.PerformActionRequest, error) {

	getAccountRes, err := f.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_ActorUniqueAccountIdentifier{
			ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
				ActorId:                caseDetails.GetActorId(),
				AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
				PartnerBank:            vendorgatewayPb.Vendor_FEDERAL_BANK,
			},
		},
	})
	if err != nil {
		cxLogger.Error(ctx, "failed to get account", zap.Error(err))
		return nil, fmt.Errorf("failed to get account")
	}

	var freezeReason FreezeReason

	for _, actionParameter := range actionParameters {
		if actionParameter.GetParameterName() == freezeReasonName {
			freezeReason = FreezeReason(actionParameter.GetDropdownValue())
		}
	}

	reqReason := freezeReasonReqReasonMap[freezeReason]
	if reqReason == enumsPb.RequestReason_REQUEST_REASON_UNSPECIFIED {
		switch caseDetails.GetReviewType() {
		case reviewPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW:
			reqReason = enumsPb.RequestReason_REQUEST_REASON_TM_ALERT
		default:
			reqReason = enumsPb.RequestReason_REQUEST_REASON_CORE_KYC_ISSUE
		}
	}

	remarks := freezeReasonVendorRemarksMap[freezeReason]
	if remarks == "" {
		switch caseDetails.GetReviewType() {
		case reviewPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW:
			remarks = vendorDefaultTransactionReviewRemarks
		default:
			remarks = vendorDefaultRemarks
		}
	}

	return &cmPb.PerformActionRequest{
		CaseId:       caseDetails.GetId(),
		ActionType:   reviewPb.ActionType_ACTION_TYPE_ADD_LIEN,
		AnalystEmail: analystEmail,
		Source:       reviewPb.ActionSource_ACTION_SOURCE_REVIEW_FLOW,
		ActionParameters: &reviewPb.ActionParameters{
			Parameter: &reviewPb.ActionParameters_AccountLienParameters{
				AccountLienParameters: &reviewPb.AccountLienParameters{
					RequestReason: &reviewPb.RequestReason{
						Reason:  reqReason,
						Remarks: remarks,
					},
					LienAmount:          f.config.LienConfig.LienAmount,
					LienDurationInHours: int32(f.config.LienConfig.LienDuration),
					CommsTemplate:       []enumsPb.CommsTemplate{},
					AccountNumber:       getAccountRes.GetAccount().GetAccountNo(),
				},
			},
		},
	}, nil

}
