package issue_config

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	protoJson "google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	mock_cache "github.com/epifi/be-common/pkg/cache/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	mock_dao2 "github.com/epifi/gamma/cx/test/mocks/issue_category/dao"

	rpcPb "github.com/epifi/be-common/api/rpc"
	issueCategoryPb "github.com/epifi/gamma/api/cx/issue_category"
	icPb "github.com/epifi/gamma/api/cx/issue_config"
	mock_manager "github.com/epifi/gamma/cx/test/mocks/issue_category/manager"
	mock_dao "github.com/epifi/gamma/cx/test/mocks/issue_config/dao"
)

var (
	getIssueConfigLevelRequest1 = &icPb.GetIssueConfigLevelRequest{
		Filter:            &icPb.IssueConfigFilter{},
		ShouldNotUseCache: false,
	}
	getIssueConfigLevelRequest2 = &icPb.GetIssueConfigLevelRequest{
		Filter: &icPb.IssueConfigFilter{
			Category:   "Transaction",
			ConfigType: icPb.ConfigType_CONFIG_TYPE_IN_APP_TICKET_DETAILS,
		},
		ShouldNotUseCache: false,
	}
	getIssueConfigLevelRequest3 = &icPb.GetIssueConfigLevelRequest{
		Filter: &icPb.IssueConfigFilter{
			Category:   "Onboarding",
			ConfigType: icPb.ConfigType_CONFIG_TYPE_IN_APP_TICKET_DETAILS,
		},
		ShouldNotUseCache: false,
	}
)

func TestIssueConfig_GetIssueConfigLevel(t *testing.T) {
	t.Parallel()
	validIssueConfigLevel := &icPb.IssueConfigLevel{
		IssueConfigs: []*icPb.IssueConfig{
			{
				Id: "config-id-1",
			},
			{
				Id: "config-id-2",
			},
		},
	}
	marshaledValidIssueConfigLevel, _ := protoJson.Marshal(validIssueConfigLevel)
	type args struct {
		ctx     context.Context
		request *icPb.GetIssueConfigLevelRequest
	}
	tests := []struct {
		name       string
		args       args
		want       *icPb.GetIssueConfigLevelResponse
		setupMocks func(mockManager *mock_manager.MockIssueCategoryManager, mockDao *mock_dao.MockIssueConfigDao, mockCache *mock_cache.MockCacheStorage, mockIssueCategoryDao *mock_dao2.MockIIssueCategoryDao)
		wantErr    bool
	}{
		{
			name: "should return invalid argument when config type is unspecified in filter",
			args: args{
				request: getIssueConfigLevelRequest1,
				ctx:     context.Background(),
			},
			setupMocks: func(mockManager *mock_manager.MockIssueCategoryManager, mockDao *mock_dao.MockIssueConfigDao, mockCache *mock_cache.MockCacheStorage, mockIssueCategoryDao *mock_dao2.MockIIssueCategoryDao) {
			},
			want: &icPb.GetIssueConfigLevelResponse{Status: rpcPb.StatusInvalidArgument()},
		},
		{
			name: "should return not found when no matching issue category ids are available as per filter",
			args: args{
				request: getIssueConfigLevelRequest2,
				ctx:     context.Background(),
			},
			setupMocks: func(mockManager *mock_manager.MockIssueCategoryManager, mockDao *mock_dao.MockIssueConfigDao, mockCache *mock_cache.MockCacheStorage, mockIssueCategoryDao *mock_dao2.MockIIssueCategoryDao) {
				mockCache.EXPECT().Get(gomock.Any(), gomock.Any()).Return("", epifierrors.ErrRecordNotFound).AnyTimes()
				mockIssueCategoryDao.EXPECT().GetIssueCategoryLevel(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &icPb.GetIssueConfigLevelResponse{Status: rpcPb.StatusRecordNotFound()},
		},
		{
			name: "should return not found when no matching issue configs are available as per filter",
			args: args{
				request: getIssueConfigLevelRequest3,
				ctx:     context.Background(),
			},
			setupMocks: func(mockManager *mock_manager.MockIssueCategoryManager, mockDao *mock_dao.MockIssueConfigDao, mockCache *mock_cache.MockCacheStorage, mockIssueCategoryDao *mock_dao2.MockIIssueCategoryDao) {
				mockCache.EXPECT().Get(gomock.Any(), gomock.Any()).Return("", epifierrors.ErrRecordNotFound).AnyTimes()
				mockIssueCategoryDao.EXPECT().GetIssueCategoryLevel(gomock.Any(), gomock.Any()).Return([]*issueCategoryPb.IssueCategory{
					{
						Id: "issue-category-id-1",
					},
					{
						Id: "issue-category-id-2",
					},
					{
						Id: "issue-category-id-3",
					},
				}, nil)
				mockDao.EXPECT().GetConfigByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, epifierrors.ErrRecordNotFound)
			},
			want: &icPb.GetIssueConfigLevelResponse{Status: rpcPb.StatusRecordNotFound()},
		},
		{
			name: "should return success when matching issue configs are available as per filter",
			args: args{
				request: getIssueConfigLevelRequest3,
				ctx:     context.Background(),
			},
			setupMocks: func(mockManager *mock_manager.MockIssueCategoryManager, mockDao *mock_dao.MockIssueConfigDao, mockCache *mock_cache.MockCacheStorage, mockIssueCategoryDao *mock_dao2.MockIIssueCategoryDao) {
				mockCache.EXPECT().Get(gomock.Any(), gomock.Any()).Return("", epifierrors.ErrRecordNotFound).AnyTimes()
				mockIssueCategoryDao.EXPECT().GetIssueCategoryLevel(gomock.Any(), gomock.Any()).Return([]*issueCategoryPb.IssueCategory{
					{
						Id: "issue-category-id-1",
					},
					{
						Id: "issue-category-id-2",
					},
					{
						Id: "issue-category-id-3",
					},
				}, nil)
				mockDao.EXPECT().GetConfigByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*icPb.IssueConfig{
					{
						Id: "config-id-1",
					},
					{
						Id: "config-id-2",
					},
				}, nil, nil)
				mockCache.EXPECT().Set(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &icPb.GetIssueConfigLevelResponse{
				Status:           rpcPb.StatusOk(),
				IssueConfigLevel: validIssueConfigLevel,
			},
		},
		{
			name: "should return success when matching issue configs are available in cache",
			args: args{
				request: getIssueConfigLevelRequest3,
				ctx:     context.Background(),
			},
			setupMocks: func(mockManager *mock_manager.MockIssueCategoryManager, mockDao *mock_dao.MockIssueConfigDao, mockCache *mock_cache.MockCacheStorage, mockIssueCategoryDao *mock_dao2.MockIIssueCategoryDao) {
				mockCache.EXPECT().Get(gomock.Any(), gomock.Any()).Return(string(marshaledValidIssueConfigLevel), nil).AnyTimes()
				mockIssueCategoryDao.EXPECT().GetIssueCategoryLevel(gomock.Any(), gomock.Any()).Return([]*issueCategoryPb.IssueCategory{
					{
						Id: "issue-category-id-1",
					},
					{
						Id: "issue-category-id-2",
					},
					{
						Id: "issue-category-id-3",
					},
				}, nil)
				mockDao.EXPECT().GetConfigByFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*icPb.IssueConfig{
					{
						Id: "config-id-1",
					},
					{
						Id: "config-id-2",
					},
				}, nil, nil)
				mockCache.EXPECT().Set(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			want: &icPb.GetIssueConfigLevelResponse{
				Status:           rpcPb.StatusOk(),
				IssueConfigLevel: validIssueConfigLevel,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mockIssueCategoryManager := mock_manager.NewMockIssueCategoryManager(ctr)
			mockIssueConfigDao := mock_dao.NewMockIssueConfigDao(ctr)
			mockCache := mock_cache.NewMockCacheStorage(ctr)
			mockIssueCategoryDao := mock_dao2.NewMockIIssueCategoryDao(ctr)
			tt.setupMocks(mockIssueCategoryManager, mockIssueConfigDao, mockCache, mockIssueCategoryDao)
			i := &IssueConfig{
				issueCategoryDao:     mockIssueCategoryDao,
				issueCategoryManager: mockIssueCategoryManager,
				issueConfigDao:       mockIssueConfigDao,
				cacheStorage:         mockCache,
				genConf:              issueConfigTS.genConf,
			}

			got, err := i.GetIssueConfigLevel(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetIssueConfigLevel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetIssueConfigLevel() got = %v, want %v", got, tt.want)
			}
		})
	}
}
