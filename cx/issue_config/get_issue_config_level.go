package issue_config

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	issueCategoryPb "github.com/epifi/gamma/api/cx/issue_category"
	icPb "github.com/epifi/gamma/api/cx/issue_config"
)

func (i *IssueConfig) getCacheKeyForIssueConfigLevel(filter *icPb.IssueConfigFilter) string {
	return fmt.Sprintf(i.genConf.IssueConfigServiceConfig().IssueConfigLevelCacheKey(), filter.GetCategory(), filter.GetCategoryDetail(), filter.GetSubCategory(), filter.GetConfigType())
}

func (i *IssueConfig) getIssueConfigLevelFromCache(ctx context.Context, filter *icPb.IssueConfigFilter) (*icPb.IssueConfigLevel, error) {
	cacheKey := i.getCacheKeyForIssueConfigLevel(filter)
	cachedData, err := i.cacheStorage.Get(ctx, cacheKey)
	if err != nil {
		return nil, errors.Wrap(err, "getIssueConfigLevelFromCache error")
	}
	issueConfigLevel := &icPb.IssueConfigLevel{}
	unmarshalErr := protojson.Unmarshal([]byte(cachedData), issueConfigLevel)
	if unmarshalErr != nil {
		return nil, errors.Wrap(unmarshalErr, "getIssueConfigLevelFromCache unmarhsal error")
	}
	return issueConfigLevel, nil
}

func (i *IssueConfig) setIssueConfigLevelInCache(ctx context.Context, filter *icPb.IssueConfigFilter, issueConfigLevel *icPb.IssueConfigLevel) error {
	cacheKey := i.getCacheKeyForIssueConfigLevel(filter)
	issueConfigLevelBytes, marshalErr := protojson.Marshal(issueConfigLevel)
	if marshalErr != nil {
		return errors.Wrap(marshalErr, "setIssueConfigLevelInCache marshal error")
	}
	return i.cacheStorage.Set(ctx, cacheKey, string(issueConfigLevelBytes), i.genConf.IssueConfigServiceConfig().IssueConfigLevelCacheValidityDuration())
}

func (i *IssueConfig) getIssueCategoryIdsForLevel(ctx context.Context, request *icPb.GetIssueConfigLevelRequest) ([]string, error) {
	// fetch all issue categories corresponding the request parameters
	issueCategories, err := i.issueCategoryDao.GetIssueCategoryLevel(ctx, &issueCategoryPb.IssueCategoryFilter{
		IssueCategory: &issueCategoryPb.IssueCategory{
			ProductCategory:        request.GetFilter().GetCategory(),
			ProductCategoryDetails: request.GetFilter().GetCategoryDetail(),
			SubCategory:            request.GetFilter().GetSubCategory(),
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "issue category level dao error")
	}

	issueConf := i.genConf.IssueConfigServiceConfig()
	var issueCategoryIds []string
	for _, issueCategory := range issueCategories {
		if issueConf.UseNewCategoryMappingForLLMScreen() {
			startTime := timestamppb.New(issueConf.IssueCategoryCreatedFromTime())
			endTime := timestamppb.New(issueConf.IssueCategoryCreatedToTime())
			if issueCategory.GetCreatedAt().AsTime().Before(startTime.AsTime()) || issueCategory.GetCreatedAt().AsTime().After(endTime.AsTime()) {
				continue
			}
		}
		issueCategoryIds = append(issueCategoryIds, issueCategory.GetId())
	}
	return issueCategoryIds, nil
}

func (i *IssueConfig) GetIssueConfigLevel(ctx context.Context, request *icPb.GetIssueConfigLevelRequest) (*icPb.GetIssueConfigLevelResponse, error) {
	if request.GetFilter().GetConfigType() == icPb.ConfigType_CONFIG_TYPE_UNSPECIFIED {
		return &icPb.GetIssueConfigLevelResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	// check if the level is present in cache
	if i.genConf.IssueConfigServiceConfig().IsCacheEnabled() && !request.GetShouldNotUseCache() {
		cachedIssueConfigLevel, cachedIssueConfigErr := i.getIssueConfigLevelFromCache(ctx, request.GetFilter())
		if cachedIssueConfigErr == nil {
			return &icPb.GetIssueConfigLevelResponse{
				Status:           rpcPb.StatusOk(),
				IssueConfigLevel: cachedIssueConfigLevel,
			}, nil
		}
		if !errors.Is(cachedIssueConfigErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error while fetching issue config level from cache", zap.Error(cachedIssueConfigErr), zap.String(logger.PRODUCT_CATEGORY, request.GetFilter().GetCategory()), zap.String(logger.PRODUCT_CATEGORY_DETAILS, request.GetFilter().GetCategoryDetail()), zap.String(logger.PRODUCT_SUB_CATEGORY, request.GetFilter().GetSubCategory()))
		}
	}
	// get issue category ids for level
	issueCategoryIds, err := i.getIssueCategoryIdsForLevel(ctx, request)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &icPb.GetIssueConfigLevelResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error while fetching issue category ids for level", zap.Error(err), zap.String(logger.PRODUCT_CATEGORY, request.GetFilter().GetCategory()), zap.String(logger.PRODUCT_CATEGORY_DETAILS, request.GetFilter().GetCategoryDetail()), zap.String(logger.PRODUCT_SUB_CATEGORY, request.GetFilter().GetSubCategory()))
		return &icPb.GetIssueConfigLevelResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	// get issue configs corresponding to the issue category ids
	issueConfigs, _, err := i.issueConfigDao.GetConfigByFilters(ctx, issueCategoryIds, request.GetFilter().GetConfigType(), nil, -1)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &icPb.GetIssueConfigLevelResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error while fetching issue category ids for level", zap.Error(err), zap.String(logger.PRODUCT_CATEGORY, request.GetFilter().GetCategory()), zap.String(logger.PRODUCT_CATEGORY_DETAILS, request.GetFilter().GetCategoryDetail()), zap.String(logger.PRODUCT_SUB_CATEGORY, request.GetFilter().GetSubCategory()))
		return &icPb.GetIssueConfigLevelResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	// persist the issue configs in cache
	issueConfigLevel := &icPb.IssueConfigLevel{
		IssueConfigs: issueConfigs,
	}
	cacheErr := i.setIssueConfigLevelInCache(ctx, request.GetFilter(), issueConfigLevel)
	if cacheErr != nil {
		logger.Error(ctx, "error while persisting issue config level in cache", zap.Error(cacheErr))
	}
	// issue config level fetched successfully
	return &icPb.GetIssueConfigLevelResponse{
		Status:           rpcPb.StatusOk(),
		IssueConfigLevel: issueConfigLevel,
	}, nil
}
