package chat

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	mock_events "github.com/epifi/be-common/pkg/events/mocks"
	accountEnumsPb "github.com/epifi/gamma/api/accounts/enums"
	mocks5 "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	mockVgFd "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk/mocks"
	mock_helper "github.com/epifi/gamma/cx/test/mocks/helper"
	mockAccountStatus "github.com/epifi/gamma/risk/test/mocks/accountstatus"

	"context"
	"errors"
	"os"
	"reflect"
	"testing"
	"time"

	"google.golang.org/grpc/metadata"

	"github.com/golang/mock/gomock"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	mockOnboardingPb "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/cx/config"
	mockHelper "github.com/epifi/gamma/cx/test/mocks/chat/helper"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/queue/mocks"
	actorPb "github.com/epifi/gamma/api/actor"
	mocks2 "github.com/epifi/gamma/api/actor/mocks"
	authPb "github.com/epifi/gamma/api/auth"
	mocks3 "github.com/epifi/gamma/api/auth/mocks"
	chatPb "github.com/epifi/gamma/api/cx/chat"
	typesPb "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendormapping"
	mock_vendormapping "github.com/epifi/gamma/api/vendormapping/mocks"
	"github.com/epifi/gamma/cx/chat/dao/model"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/test"
	mock_dao "github.com/epifi/gamma/cx/test/mocks/chat/dao"
	releaseEvaluatorMocks "github.com/epifi/gamma/pkg/feature/release/mocks"
	accountstatus "github.com/epifi/gamma/risk/accountstatus"
)

type ChatServiceTestSuite struct {
	genConf *cxGenConf.Config
	conf    *config.Config
}

func NewChatServiceTestSuite(genConf *cxGenConf.Config, conf *config.Config) ChatServiceTestSuite {
	return ChatServiceTestSuite{
		genConf: genConf,
		conf:    conf,
	}
}

func TestMain(m *testing.M) {
	var teardown func()
	conf, genConf, _, teardown := test.InitTestServer(false)
	cts = NewChatServiceTestSuite(genConf, conf)
	exitCode := m.Run()
	teardown()

	os.Exit(exitCode)
}

const actorId1 = "act-101"

var (
	cts                    ChatServiceTestSuite
	shortToken1            = "shortToken111"
	senseforthChatInitInfo = &chatPb.SenseforthChatInitInformation{
		WebViewUrl:     "https://chatbot-frontend.deploy.pointz.in/chatsdk/v1/index.html?userAgent=android",
		ShortToken:     shortToken1,
		ReuseCacheData: commontypes.BooleanEnum_FALSE,
	}
	senseforthChatInitInfoFiLite = &chatPb.SenseforthChatInitInformation{
		WebViewUrl:     "https://chatbot-frontend.deploy.pointz.in/chatsdk/v1/index.html?userAgent=android",
		ShortToken:     shortToken1,
		ReuseCacheData: commontypes.BooleanEnum_FALSE,
		BotContextCode: ContextCodeFiLiteUser,
	}
	senseforthChatInitInfoUseCache = &chatPb.SenseforthChatInitInformation{
		WebViewUrl:     "https://chatbot-frontend.deploy.pointz.in/chatsdk/v1/index.html?userAgent=android",
		ReuseCacheData: commontypes.BooleanEnum_TRUE,
	}
	senseforthChatInitInfoUseCacheFiLite = &chatPb.SenseforthChatInitInformation{
		WebViewUrl:     "https://chatbot-frontend.deploy.pointz.in/chatsdk/v1/index.html?userAgent=android",
		ReuseCacheData: commontypes.BooleanEnum_TRUE,
		BotContextCode: ContextCodeFiLiteUser,
	}
)

func TestService_GetChatInitInformationForActor(t *testing.T) {
	t.Parallel()

	ctr := gomock.NewController(t)
	mockFresh := mock_dao.NewMockIFreshdeskUpdateEventDAO(ctr)
	mockTick := mocks.NewMockPublisher(ctr)
	mockVM := mock_vendormapping.NewMockVendorMappingServiceClient(ctr)
	idgenerator := idgen.NewDomainIdGenerator(idgen.NewClock())
	mockActorClient := mocks2.NewMockActorClient(ctr)
	mockAuthClient := mocks3.NewMockAuthClient(ctr)
	mockReleaseEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctr)
	mockFcUserMappingHelper := mockHelper.NewMockIFreshchatUserMappingHelper(ctr)
	mockOnboardingClient := mockOnboardingPb.NewMockOnboardingClient(ctr)
	mockUserClient := mocks5.NewMockUsersClient(ctr)
	mockAccountStatusFetcher := mockAccountStatus.NewMockFetcher(ctr)
	mockEventBroker := mock_events.NewMockBroker(ctr)
	mockVgFdClient := mockVgFd.NewMockFreshdeskClient(ctr)
	mockCustomerIdentifier := mock_helper.NewMockICustomerIdentifier(ctr)

	metaData := metadata.New(map[string]string{})
	metaData.Set("app_platform", commontypes.Platform_ANDROID.String())
	ctx := metadata.NewIncomingContext(context.Background(), metaData)

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *chatPb.GetChatInitInformationForActorRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *chatPb.GetChatInitInformationForActorResponse
		wantErr bool
	}{
		{
			name: "empty actor id",
			args: args{
				ctx: ctx,
				req: &chatPb.GetChatInitInformationForActorRequest{},
			},
			want: &chatPb.GetChatInitInformationForActorResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor id is mandatory"),
			},
			wantErr: false,
		},
		{
			name: "release error",
			args: args{
				mocks: []interface{}{
					mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, errors.New("release error")),
					mockVM.EXPECT().GetBEMappingById(gomock.Any(), gomock.Any()).Return(&vendormapping.GetBEMappingByIdResponse{Status: rpcPb.StatusOk(), FreshdeskId: "f101"}, nil),
					mockFcUserMappingHelper.EXPECT().GetCustomUserPropertiesMap(ctx, gomock.Any()).
						Return(nil),
				},
				ctx: ctx,
				req: &chatPb.GetChatInitInformationForActorRequest{ActorId: "act-101"},
			},
			want: &chatPb.GetChatInitInformationForActorResponse{
				Status:             rpcPb.StatusOk(),
				ReferenceId:        "f101",
				AppId:              "ece35ccc-cf4d-4f25-aba2-2ac4a893eea4",
				AppKey:             "34411d8f-0a2a-41d5-a282-63ca27c82f74",
				Domain:             "msdk.in.freshchat.com",
				Email:              "<EMAIL>",
				ChatViewToBeLoaded: typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_FRESHCHAT_SDK,
				ShouldAutoRetry:    commontypes.BooleanEnum_TRUE,
			},
			wantErr: false,
		},
		{
			name: "release evaluator returned false",
			args: args{
				mocks: []interface{}{
					mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil),
					mockVM.EXPECT().GetBEMappingById(gomock.Any(), gomock.Any()).Return(&vendormapping.GetBEMappingByIdResponse{Status: rpcPb.StatusOk(), FreshdeskId: "f101"}, nil),
					mockFcUserMappingHelper.EXPECT().GetCustomUserPropertiesMap(ctx, gomock.Any()).
						Return(nil),
				},
				ctx: ctx,
				req: &chatPb.GetChatInitInformationForActorRequest{ActorId: "act-101"},
			},
			want: &chatPb.GetChatInitInformationForActorResponse{
				Status:             rpcPb.StatusOk(),
				ReferenceId:        "f101",
				AppId:              "ece35ccc-cf4d-4f25-aba2-2ac4a893eea4",
				AppKey:             "34411d8f-0a2a-41d5-a282-63ca27c82f74",
				Domain:             "msdk.in.freshchat.com",
				Email:              "<EMAIL>",
				ChatViewToBeLoaded: typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_FRESHCHAT_SDK,
				ShouldAutoRetry:    commontypes.BooleanEnum_TRUE,
			},
			wantErr: false,
		},
		{
			name: "release evaluator returned true, senseforth chat building failed",
			args: args{
				mocks: []interface{}{
					mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil),
					mockVM.EXPECT().GetBEMappingById(gomock.Any(), gomock.Any()).Return(&vendormapping.GetBEMappingByIdResponse{Status: rpcPb.StatusOk(), FreshdeskId: "f101"}, nil),
					mockFcUserMappingHelper.EXPECT().GetCustomUserPropertiesMap(ctx, gomock.Any()).
						Return(nil),
					mockOnboardingClient.EXPECT().GetFeatureDetails(gomock.Any(), &onboardingPb.GetFeatureDetailsRequest{
						ActorId: actorId1,
						Feature: onboardingPb.Feature_FEATURE_FI_LITE,
					}).
						Return(&onboardingPb.GetFeatureDetailsResponse{Status: rpcPb.StatusOk(), IsFiLiteUser: true}, nil),
					mockActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
						Status: rpcPb.StatusInternal(),
					}, nil),
				},
				ctx: ctx,
				req: &chatPb.GetChatInitInformationForActorRequest{ActorId: "act-101"},
			},
			want: &chatPb.GetChatInitInformationForActorResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "release evaluator returned true, senseforth chat building passed",
			args: args{
				mocks: []interface{}{
					mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil),
					mockVM.EXPECT().GetBEMappingById(gomock.Any(), gomock.Any()).Return(&vendormapping.GetBEMappingByIdResponse{Status: rpcPb.StatusOk(), FreshdeskId: "f101"}, nil),
					mockFcUserMappingHelper.EXPECT().GetCustomUserPropertiesMap(ctx, gomock.Any()).
						Return(nil),
					mockOnboardingClient.EXPECT().GetFeatureDetails(gomock.Any(), &onboardingPb.GetFeatureDetailsRequest{
						ActorId: actorId1,
						Feature: onboardingPb.Feature_FEATURE_FI_LITE,
					}).
						Return(&onboardingPb.GetFeatureDetailsResponse{Status: rpcPb.StatusOk(), IsFiLiteUser: false}, nil),
					mockActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
						Status: rpcPb.StatusOk(),
						Actor:  &typesPb.Actor{Id: "act101"},
					}, nil),
					mockAuthClient.EXPECT().CreateToken(gomock.Any(), gomock.Any()).Return(&authPb.CreateTokenResponse{
						Status: rpcPb.StatusOk(),
						Token:  shortToken1,
					}, nil),
				},
				ctx: ctx,
				req: &chatPb.GetChatInitInformationForActorRequest{ActorId: "act-101"},
			},
			want: &chatPb.GetChatInitInformationForActorResponse{
				Status:                        rpcPb.StatusOk(),
				ChatViewToBeLoaded:            typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW,
				SenseforthChatInitInformation: senseforthChatInitInfo,
				ReferenceId:                   "f101",
				AppId:                         "ece35ccc-cf4d-4f25-aba2-2ac4a893eea4",
				AppKey:                        "34411d8f-0a2a-41d5-a282-63ca27c82f74",
				Domain:                        "msdk.in.freshchat.com",
				Email:                         "<EMAIL>",
				ShouldAutoRetry:               commontypes.BooleanEnum_TRUE,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(idgenerator, mockTick, mockFresh, mockVM, cts.genConf, mockAuthClient, mockActorClient, mockUserClient,
				mockReleaseEvaluator, mockFcUserMappingHelper, mockOnboardingClient, cts.conf, mockAccountStatusFetcher, mockEventBroker, mockVgFdClient, mockCustomerIdentifier)
			got, err := s.GetChatInitInformationForActor(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChatInitInformationForActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				tt.want.CustomUserProperties = got.GetCustomUserProperties()
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetChatInitInformationForActor() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_BuildChatInitResponse(t *testing.T) {
	t.Parallel()

	ctr := gomock.NewController(t)
	mockFresh := mock_dao.NewMockIFreshdeskUpdateEventDAO(ctr)
	mockTick := mocks.NewMockPublisher(ctr)
	mockVM := mock_vendormapping.NewMockVendorMappingServiceClient(ctr)
	idgenerator := idgen.NewDomainIdGenerator(idgen.NewClock())
	mockActorClient := mocks2.NewMockActorClient(ctr)
	mockAuthClient := mocks3.NewMockAuthClient(ctr)
	mockReleaseEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctr)
	mockFcUserMappingHelper := mockHelper.NewMockIFreshchatUserMappingHelper(ctr)
	mockOnboardingClient := mockOnboardingPb.NewMockOnboardingClient(ctr)
	mockUserClient := mocks5.NewMockUsersClient(ctr)
	mockAccountStatusFetcher := mockAccountStatus.NewMockFetcher(ctr)
	mockEventBroker := mock_events.NewMockBroker(ctr)
	mockVgFdClient := mockVgFd.NewMockFreshdeskClient(ctr)
	mockCustomerIdentifier := mock_helper.NewMockICustomerIdentifier(ctr)

	metaData := metadata.New(map[string]string{})
	metaData.Set("app_platform", commontypes.Platform_ANDROID.String())
	ctx := metadata.NewIncomingContext(context.Background(), metaData)
	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *chatPb.GetChatInitInformationForActorRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *chatPb.GetChatInitInformationForActorResponse
		wantErr bool
	}{
		{
			name: "client side failure count is in multiple of threshold",
			args: args{
				ctx: ctx,
				req: &chatPb.GetChatInitInformationForActorRequest{
					ClientSideChatFailureInfo: &chatPb.ClientSideChatFailureInfo{
						FailureCount: 6,
					},
					ActorId: "act-101",
				},
			},
			want: &chatPb.GetChatInitInformationForActorResponse{
				Status:          rpcPb.StatusPermanentFailure(),
				ShouldAutoRetry: commontypes.BooleanEnum_FALSE,
			},
			wantErr: false,
		},
		{
			name: "last session chat view and time under bounds with force new session set to true",
			args: args{
				mocks: []interface{}{
					mockOnboardingClient.EXPECT().GetFeatureDetails(gomock.Any(), &onboardingPb.GetFeatureDetailsRequest{
						ActorId: actorId1,
						Feature: onboardingPb.Feature_FEATURE_FI_LITE,
					}).Return(&onboardingPb.GetFeatureDetailsResponse{Status: rpcPb.StatusOk(), IsFiLiteUser: true}, nil),
					mockActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
						Actor:  &typesPb.Actor{Id: "act101"},
						Status: rpcPb.StatusOk(),
					}, nil),
					mockAuthClient.EXPECT().CreateToken(gomock.Any(), gomock.Any()).Return(&authPb.CreateTokenResponse{
						Token:  shortToken1,
						Status: rpcPb.StatusOk(),
					}, nil),
					mockVM.EXPECT().GetBEMappingById(gomock.Any(), gomock.Any()).Return(&vendormapping.GetBEMappingByIdResponse{
						Status:      rpcPb.StatusOk(),
						FreshdeskId: "f101",
					}, nil),
					mockFcUserMappingHelper.EXPECT().GetCustomUserPropertiesMap(ctx, gomock.Any()).
						Return(nil),
				},
				ctx: ctx,
				req: &chatPb.GetChatInitInformationForActorRequest{
					ClientSideChatFailureInfo: &chatPb.ClientSideChatFailureInfo{
						FailureCount: 1,
					},
					ActorId:                        "act-101",
					LastSuccessfullyLoadedChatView: typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW,
					LastSuccessfulSessionTime:      timestampPb.New(time.Now().Add(-10 * time.Minute)),
					ForceNewSession:                commontypes.BooleanEnum_TRUE,
				},
			},
			want: &chatPb.GetChatInitInformationForActorResponse{
				Status:                        rpcPb.StatusOk(),
				SenseforthChatInitInformation: senseforthChatInitInfoFiLite,
				ReferenceId:                   "f101",
				AppId:                         "ece35ccc-cf4d-4f25-aba2-2ac4a893eea4",
				AppKey:                        "34411d8f-0a2a-41d5-a282-63ca27c82f74",
				Domain:                        "msdk.in.freshchat.com",
				Email:                         "<EMAIL>",
				ChatViewToBeLoaded:            typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW,
				ShouldAutoRetry:               commontypes.BooleanEnum_TRUE,
			},
			wantErr: false,
		},
		{
			name: "last session chat view and time under bounds with force new session as false",
			args: args{
				mocks: []interface{}{
					mockVM.EXPECT().GetBEMappingById(gomock.Any(), gomock.Any()).Return(&vendormapping.GetBEMappingByIdResponse{
						Status:      rpcPb.StatusOk(),
						FreshdeskId: "f101",
					}, nil),
					mockFcUserMappingHelper.EXPECT().GetCustomUserPropertiesMap(ctx, gomock.Any()).
						Return(nil),
					mockOnboardingClient.EXPECT().GetFeatureDetails(gomock.Any(), &onboardingPb.GetFeatureDetailsRequest{
						ActorId: actorId1,
						Feature: onboardingPb.Feature_FEATURE_FI_LITE,
					}).Return(&onboardingPb.GetFeatureDetailsResponse{Status: rpcPb.StatusOk(), IsFiLiteUser: false}, nil),
				},
				ctx: ctx,
				req: &chatPb.GetChatInitInformationForActorRequest{
					ClientSideChatFailureInfo: &chatPb.ClientSideChatFailureInfo{
						FailureCount: 1,
					},
					ActorId:                        "act-101",
					LastSuccessfullyLoadedChatView: typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW,
					LastSuccessfulSessionTime:      timestampPb.New(time.Now().Add(-10 * time.Minute)),
					ForceNewSession:                commontypes.BooleanEnum_FALSE,
				},
			},
			want: &chatPb.GetChatInitInformationForActorResponse{
				Status:                        rpcPb.StatusOk(),
				SenseforthChatInitInformation: senseforthChatInitInfoUseCache,
				ReferenceId:                   "f101",
				AppId:                         "ece35ccc-cf4d-4f25-aba2-2ac4a893eea4",
				AppKey:                        "34411d8f-0a2a-41d5-a282-63ca27c82f74",
				Domain:                        "msdk.in.freshchat.com",
				Email:                         "<EMAIL>",
				ChatViewToBeLoaded:            typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW,
				ShouldAutoRetry:               commontypes.BooleanEnum_TRUE,
			},
			wantErr: false,
		},
		{
			name: "last session chat view and time under bounds with force new session as false - Fi lite",
			args: args{
				mocks: []interface{}{
					mockVM.EXPECT().GetBEMappingById(gomock.Any(), gomock.Any()).Return(&vendormapping.GetBEMappingByIdResponse{
						Status:      rpcPb.StatusOk(),
						FreshdeskId: "f101",
					}, nil),
					mockFcUserMappingHelper.EXPECT().GetCustomUserPropertiesMap(ctx, gomock.Any()).
						Return(nil),
					mockOnboardingClient.EXPECT().GetFeatureDetails(gomock.Any(), &onboardingPb.GetFeatureDetailsRequest{
						ActorId: actorId1,
						Feature: onboardingPb.Feature_FEATURE_FI_LITE,
					}).Return(&onboardingPb.GetFeatureDetailsResponse{Status: rpcPb.StatusOk(), IsFiLiteUser: true}, nil),
				},
				ctx: ctx,
				req: &chatPb.GetChatInitInformationForActorRequest{
					ClientSideChatFailureInfo: &chatPb.ClientSideChatFailureInfo{
						FailureCount: 1,
					},
					ActorId:                        "act-101",
					LastSuccessfullyLoadedChatView: typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW,
					LastSuccessfulSessionTime:      timestampPb.New(time.Now().Add(-10 * time.Minute)),
					ForceNewSession:                commontypes.BooleanEnum_FALSE,
				},
			},
			want: &chatPb.GetChatInitInformationForActorResponse{
				Status:                        rpcPb.StatusOk(),
				SenseforthChatInitInformation: senseforthChatInitInfoUseCacheFiLite,
				ReferenceId:                   "f101",
				AppId:                         "ece35ccc-cf4d-4f25-aba2-2ac4a893eea4",
				AppKey:                        "34411d8f-0a2a-41d5-a282-63ca27c82f74",
				Domain:                        "msdk.in.freshchat.com",
				Email:                         "<EMAIL>",
				ChatViewToBeLoaded:            typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW,
				ShouldAutoRetry:               commontypes.BooleanEnum_TRUE,
			},
			wantErr: false,
		},
		{
			name: "release error, last loaded view senseforth",
			args: args{
				mocks: []interface{}{
					mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, errors.New("release error")),
					mockVM.EXPECT().GetBEMappingById(gomock.Any(), gomock.Any()).Return(&vendormapping.GetBEMappingByIdResponse{
						Status:      rpcPb.StatusOk(),
						FreshdeskId: "f101",
					}, nil),
					mockFcUserMappingHelper.EXPECT().GetCustomUserPropertiesMap(ctx, gomock.Any()).
						Return(nil),
					mockOnboardingClient.EXPECT().GetFeatureDetails(gomock.Any(), &onboardingPb.GetFeatureDetailsRequest{
						ActorId: actorId1,
						Feature: onboardingPb.Feature_FEATURE_FI_LITE,
					}).Return(&onboardingPb.GetFeatureDetailsResponse{Status: rpcPb.StatusOk(), IsFiLiteUser: false}, nil),
					mockActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
						Actor:  &typesPb.Actor{Id: "act101"},
						Status: rpcPb.StatusOk(),
					}, nil),
					mockAuthClient.EXPECT().CreateToken(gomock.Any(), gomock.Any()).Return(&authPb.CreateTokenResponse{
						Token:  shortToken1,
						Status: rpcPb.StatusOk(),
					}, nil),
				},
				ctx: ctx,
				req: &chatPb.GetChatInitInformationForActorRequest{
					ClientSideChatFailureInfo: &chatPb.ClientSideChatFailureInfo{
						FailureCount: 1,
					},
					ActorId:                        "act-101",
					LastSuccessfullyLoadedChatView: typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW,
					LastSuccessfulSessionTime:      timestampPb.New(time.Now().Add(-100 * time.Hour)),
				},
			},
			want: &chatPb.GetChatInitInformationForActorResponse{
				Status:                        rpcPb.StatusOk(),
				SenseforthChatInitInformation: senseforthChatInitInfo,
				ReferenceId:                   "f101",
				AppId:                         "ece35ccc-cf4d-4f25-aba2-2ac4a893eea4",
				AppKey:                        "34411d8f-0a2a-41d5-a282-63ca27c82f74",
				Domain:                        "msdk.in.freshchat.com",
				Email:                         "<EMAIL>",
				ChatViewToBeLoaded:            typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW,
				ShouldAutoRetry:               commontypes.BooleanEnum_TRUE,
			},
			wantErr: false,
		},
		{
			name: "actor not eligible for senseforth",
			args: args{
				mocks: []interface{}{
					mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil),
					mockVM.EXPECT().GetBEMappingById(gomock.Any(), gomock.Any()).Return(&vendormapping.GetBEMappingByIdResponse{
						Status:      rpcPb.StatusOk(),
						FreshdeskId: "f101",
					}, nil),
					mockFcUserMappingHelper.EXPECT().GetCustomUserPropertiesMap(ctx, gomock.Any()).
						Return(nil),
				},
				ctx: ctx,
				req: &chatPb.GetChatInitInformationForActorRequest{
					ClientSideChatFailureInfo: &chatPb.ClientSideChatFailureInfo{
						FailureCount: 1,
					},
					ActorId:                        "act-101",
					LastSuccessfullyLoadedChatView: typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW,
					LastSuccessfulSessionTime:      timestampPb.New(time.Now().Add(-100 * time.Hour)),
				},
			},
			want: &chatPb.GetChatInitInformationForActorResponse{
				Status:             rpcPb.StatusOk(),
				ReferenceId:        "f101",
				AppId:              "ece35ccc-cf4d-4f25-aba2-2ac4a893eea4",
				AppKey:             "34411d8f-0a2a-41d5-a282-63ca27c82f74",
				Domain:             "msdk.in.freshchat.com",
				Email:              "<EMAIL>",
				ChatViewToBeLoaded: typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_FRESHCHAT_SDK,
				ShouldAutoRetry:    commontypes.BooleanEnum_TRUE,
			},
			wantErr: false,
		},
		{
			name: "actor eligible for senseforth",
			args: args{
				mocks: []interface{}{
					mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil),
					mockVM.EXPECT().GetBEMappingById(gomock.Any(), gomock.Any()).Return(&vendormapping.GetBEMappingByIdResponse{
						Status:      rpcPb.StatusOk(),
						FreshdeskId: "f101",
					}, nil),
					mockFcUserMappingHelper.EXPECT().GetCustomUserPropertiesMap(ctx, gomock.Any()).
						Return(nil),
					mockOnboardingClient.EXPECT().GetFeatureDetails(gomock.Any(), &onboardingPb.GetFeatureDetailsRequest{
						ActorId: actorId1,
						Feature: onboardingPb.Feature_FEATURE_FI_LITE,
					}).Return(&onboardingPb.GetFeatureDetailsResponse{Status: rpcPb.StatusOk(), IsFiLiteUser: false}, nil),
					mockActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
						Actor:  &typesPb.Actor{Id: "act101"},
						Status: rpcPb.StatusOk(),
					}, nil),
					mockAuthClient.EXPECT().CreateToken(gomock.Any(), gomock.Any()).Return(&authPb.CreateTokenResponse{
						Token:  shortToken1,
						Status: rpcPb.StatusOk(),
					}, nil),
				},
				ctx: ctx,
				req: &chatPb.GetChatInitInformationForActorRequest{
					ClientSideChatFailureInfo: &chatPb.ClientSideChatFailureInfo{
						FailureCount: 1,
					},
					ActorId:                        "act-101",
					LastSuccessfullyLoadedChatView: typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW,
					LastSuccessfulSessionTime:      timestampPb.New(time.Now().Add(-100 * time.Hour)),
				},
			},
			want: &chatPb.GetChatInitInformationForActorResponse{
				Status:                        rpcPb.StatusOk(),
				SenseforthChatInitInformation: senseforthChatInitInfo,
				ReferenceId:                   "f101",
				AppId:                         "ece35ccc-cf4d-4f25-aba2-2ac4a893eea4",
				AppKey:                        "34411d8f-0a2a-41d5-a282-63ca27c82f74",
				Domain:                        "msdk.in.freshchat.com",
				Email:                         "<EMAIL>",
				ChatViewToBeLoaded:            typesPb.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW,
				ShouldAutoRetry:               commontypes.BooleanEnum_TRUE,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(idgenerator, mockTick, mockFresh, mockVM, cts.genConf, mockAuthClient, mockActorClient, mockUserClient,
				mockReleaseEvaluator, mockFcUserMappingHelper, mockOnboardingClient, cts.conf, mockAccountStatusFetcher, mockEventBroker, mockVgFdClient, mockCustomerIdentifier)
			got, err := s.BuildChatInitResponse(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BuildChatInitResponse() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				tt.want.CustomUserProperties = got.GetCustomUserProperties()
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BuildChatInitResponse() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_UpdateTicketForSource(t *testing.T) {
	t.Parallel()

	ctr := gomock.NewController(t)
	mockFresh := mock_dao.NewMockIFreshdeskUpdateEventDAO(ctr)
	mockTick := mocks.NewMockPublisher(ctr)
	mockVM := mock_vendormapping.NewMockVendorMappingServiceClient(ctr)
	idgenerator := idgen.NewDomainIdGenerator(idgen.NewClock())
	mockActorClient := mocks2.NewMockActorClient(ctr)
	mockAuthClient := mocks3.NewMockAuthClient(ctr)
	mockReleaseEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctr)
	mockFcUserMappingHelper := mockHelper.NewMockIFreshchatUserMappingHelper(ctr)
	mockOnboardingClient := mockOnboardingPb.NewMockOnboardingClient(ctr)
	mockUserClient := mocks5.NewMockUsersClient(ctr)
	mockAccountStatusFetcher := mockAccountStatus.NewMockFetcher(ctr)
	mockEventBroker := mock_events.NewMockBroker(ctr)
	mockVgFdClient := mockVgFd.NewMockFreshdeskClient(ctr)
	mockCustomerIdentifier := mock_helper.NewMockICustomerIdentifier(ctr)

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *chatPb.UpdateTicketForSourceRequest
	}

	tests := []struct {
		name    string
		args    args
		want    *chatPb.UpdateTicketForSourceResponse
		wantErr bool
	}{
		{
			name: "invalid input - missing ticket id and phone/email",
			args: args{
				mocks: nil,
				ctx:   context.Background(),
				req:   &chatPb.UpdateTicketForSourceRequest{TicketId: 0, PhoneNumber: "", Email: ""},
			},
			want: &chatPb.UpdateTicketForSourceResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("ticket id and phone number or emails are mandatory params"),
			},
			wantErr: false,
		},
		{
			name: "non-indian phone number skips publish",
			args: args{
				mocks: []interface{}{
					mockFresh.EXPECT().Create(gomock.Any(), int64(123), gomock.Any()).Return(&model.FreshdeskUpdateEvent{Id: "msgid"}, nil).AnyTimes(),
				},
				ctx: context.Background(),
				req: &chatPb.UpdateTicketForSourceRequest{
					TicketId:    123,
					PhoneNumber: "+12025550123", // US number
					Source:      "Phone",
				},
			},
			want: &chatPb.UpdateTicketForSourceResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "phone number parse error proceeds",
			args: args{
				mocks: []interface{}{
					mockFresh.EXPECT().Create(gomock.Any(), int64(12345), gomock.Any()).Return(&model.FreshdeskUpdateEvent{Id: "msgid"}, nil).AnyTimes(),
					mockTick.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("sqsid", nil).AnyTimes(),
				},
				ctx: context.Background(),
				req: &chatPb.UpdateTicketForSourceRequest{
					TicketId:    123,
					PhoneNumber: "notaphone", // Will cause parse error
					Source:      "Phone",
				},
			},
			want: &chatPb.UpdateTicketForSourceResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "email source triggers risk ops reroute (user/actor not found, not frozen, update ticket group error)",
			args: args{
				mocks: []interface{}{
					mockFresh.EXPECT().Create(gomock.Any(), int64(123), gomock.Any()).Return(&model.FreshdeskUpdateEvent{Id: "msgid"}, nil).AnyTimes(),
					mockTick.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("sqsid", nil).AnyTimes(),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(nil, errors.New("user not found")).AnyTimes(),
					mockActorClient.EXPECT().GetActorByEntityId(gomock.Any(), gomock.Any()).Return(nil, errors.New("actor not found")).AnyTimes(),
					mockAccountStatusFetcher.EXPECT().FetchAccountStatusForActor(gomock.Any(), "actorid", gomock.Any(), gomock.Any()).Return(&accountstatus.OperationalDetails{FreezeStatus: accountEnumsPb.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE}, nil).AnyTimes(),
					mockVgFdClient.EXPECT().UpdateTicket(gomock.Any(), gomock.Any()).Return(nil, errors.New("update group error")).AnyTimes(),
					mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes(),
				},
				ctx: context.Background(),
				req: &chatPb.UpdateTicketForSourceRequest{
					TicketId: 123,
					Email:    "<EMAIL>",
					Source:   "Email",
				},
			},
			want: &chatPb.UpdateTicketForSourceResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "happy path - phone with country code normalization",
			args: args{
				mocks: []interface{}{
					mockFresh.EXPECT().Create(gomock.Any(), int64(123), gomock.Any()).Return(&model.FreshdeskUpdateEvent{Id: "msgid"}, nil).AnyTimes(),
					mockTick.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("sqsid", nil).AnyTimes(),
				},
				ctx: context.Background(),
				req: &chatPb.UpdateTicketForSourceRequest{
					TicketId:    123,
					PhoneNumber: "+919999999999", // Will parse to country code 91
					Source:      "Phone",
				},
			},
			want: &chatPb.UpdateTicketForSourceResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			// --- Risk Ops Reroute: Happy Path ---
			name: "email source risk ops reroute happy path (user, actor, frozen, update group succeeds)",
			args: args{
				mocks: []interface{}{
					mockFresh.EXPECT().Create(gomock.Any(), int64(123), gomock.Any()).Return(&model.FreshdeskUpdateEvent{Id: "msgid"}, nil).AnyTimes(),
					mockTick.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("sqsid", nil).AnyTimes(),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{Status: rpcPb.StatusOk(), User: &userPb.User{Id: "userid"}}, nil).AnyTimes(),
					mockActorClient.EXPECT().GetActorByEntityId(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByEntityIdResponse{Status: rpcPb.StatusOk(), Actor: &typesPb.Actor{Id: "actorid"}}, nil).AnyTimes(),
					mockAccountStatusFetcher.EXPECT().FetchAccountStatusForActor(gomock.Any(), "actorid", gomock.Any(), gomock.Any()).Return(&accountstatus.OperationalDetails{FreezeStatus: accountEnumsPb.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE}, nil).AnyTimes(),
					mockVgFdClient.EXPECT().UpdateTicket(gomock.Any(), gomock.Any()).Return(&freshdesk.UpdateTicketResponse{}, nil).AnyTimes(),
					mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes(),
				},
				ctx: context.Background(),
				req: &chatPb.UpdateTicketForSourceRequest{
					TicketId: 123,
					Email:    "<EMAIL>",
					Source:   "Email",
				},
			},
			want: &chatPb.UpdateTicketForSourceResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			// --- Risk Ops Reroute: Update Group Fails ---
			name: "email source risk ops reroute update group fails",
			args: args{
				mocks: []interface{}{
					mockFresh.EXPECT().Create(gomock.Any(), int64(123), gomock.Any()).Return(&model.FreshdeskUpdateEvent{Id: "msgid"}, nil).AnyTimes(),
					mockTick.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("sqsid", nil).AnyTimes(),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{Status: rpcPb.StatusOk(), User: &userPb.User{Id: "userid"}}, nil).AnyTimes(),
					mockActorClient.EXPECT().GetActorByEntityId(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByEntityIdResponse{Status: rpcPb.StatusOk(), Actor: &typesPb.Actor{Id: "actorid"}}, nil).AnyTimes(),
					mockAccountStatusFetcher.EXPECT().FetchAccountStatusForActor(gomock.Any(), "actorid", gomock.Any(), gomock.Any()).Return(&accountstatus.OperationalDetails{FreezeStatus: accountEnumsPb.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE}, nil).AnyTimes(),
					mockVgFdClient.EXPECT().UpdateTicket(gomock.Any(), gomock.Any()).Return(nil, errors.New("update group error")).AnyTimes(),
					// Event broker not called since update group fails before event
				},
				ctx: context.Background(),
				req: &chatPb.UpdateTicketForSourceRequest{
					TicketId: 123,
					Email:    "<EMAIL>",
					Source:   "Email",
				},
			},
			want: &chatPb.UpdateTicketForSourceResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			// --- Risk Ops Reroute: Not Frozen ---
			name: "email source risk ops reroute not frozen",
			args: args{
				mocks: []interface{}{
					mockFresh.EXPECT().Create(gomock.Any(), int64(123), gomock.Any()).Return(&model.FreshdeskUpdateEvent{Id: "msgid"}, nil).AnyTimes(),
					mockTick.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("sqsid", nil).AnyTimes(),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{Status: rpcPb.StatusOk(), User: &userPb.User{Id: "userid"}}, nil).AnyTimes(),
					mockActorClient.EXPECT().GetActorByEntityId(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByEntityIdResponse{Status: rpcPb.StatusOk(), Actor: &typesPb.Actor{Id: "actorid"}}, nil).AnyTimes(),
					mockAccountStatusFetcher.EXPECT().FetchAccountStatusForActor(gomock.Any(), "actorid", gomock.Any(), gomock.Any()).Return(&accountstatus.OperationalDetails{FreezeStatus: accountEnumsPb.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE}, nil).AnyTimes(),
					// No update ticket or event broker call
				},
				ctx: context.Background(),
				req: &chatPb.UpdateTicketForSourceRequest{
					TicketId: 123,
					Email:    "<EMAIL>",
					Source:   "Email",
				},
			},
			want: &chatPb.UpdateTicketForSourceResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			// --- Risk Ops Reroute: User Not Found ---
			name: "email source risk ops reroute user not found",
			args: args{
				mocks: []interface{}{
					mockFresh.EXPECT().Create(gomock.Any(), int64(123), gomock.Any()).Return(&model.FreshdeskUpdateEvent{Id: "msgid"}, nil).AnyTimes(),
					mockTick.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("sqsid", nil).AnyTimes(),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(nil, errors.New("user not found")).AnyTimes(),
					// No actor, account status, update ticket, or event broker call
				},
				ctx: context.Background(),
				req: &chatPb.UpdateTicketForSourceRequest{
					TicketId: 123,
					Email:    "<EMAIL>",
					Source:   "Email",
				},
			},
			want: &chatPb.UpdateTicketForSourceResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			// --- Risk Ops Reroute: Actor Not Found ---
			name: "email source risk ops reroute actor not found",
			args: args{
				mocks: []interface{}{
					mockFresh.EXPECT().Create(gomock.Any(), int64(123), gomock.Any()).Return(&model.FreshdeskUpdateEvent{Id: "msgid"}, nil).AnyTimes(),
					mockTick.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("sqsid", nil).AnyTimes(),
					mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{Status: rpcPb.StatusOk(), User: &userPb.User{Id: "userid"}}, nil).AnyTimes(),
					mockActorClient.EXPECT().GetActorByEntityId(gomock.Any(), gomock.Any()).Return(nil, errors.New("actor not found")).AnyTimes(),
					// No account status, update ticket, or event broker call
				},
				ctx: context.Background(),
				req: &chatPb.UpdateTicketForSourceRequest{
					TicketId: 123,
					Email:    "<EMAIL>",
					Source:   "Email",
				},
			},
			want: &chatPb.UpdateTicketForSourceResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(idgenerator, mockTick, mockFresh, mockVM, cts.genConf, mockAuthClient, mockActorClient, mockUserClient,
				mockReleaseEvaluator, mockFcUserMappingHelper, mockOnboardingClient, cts.conf, mockAccountStatusFetcher, mockEventBroker, mockVgFdClient, mockCustomerIdentifier)
			got, err := s.UpdateTicketForSource(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateTicketForSource() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateTicketForSource() got = %v, want %v", got, tt.want)
			}
		})
	}
}
