package actor

import (
	"github.com/samber/lo"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/errgroup"

	tspUserPb "github.com/epifi/gamma/api/tspuser"
	oldtypes "github.com/epifi/gamma/api/types"
	gammanames "github.com/epifi/gamma/pkg/names"
	"github.com/epifi/gamma/pkg/owner"

	types "github.com/epifi/gamma/api/typesv2"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	protoStruct "github.com/golang/protobuf/ptypes/struct"
	errPkg "github.com/pkg/errors"
	"github.com/thoas/go-funk"
	"go.uber.org/zap"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/gamma/actor/config/genconf"
	"github.com/epifi/gamma/api/order/payment/notification"
	iftPkg "github.com/epifi/gamma/pkg/internationalfundtransfer"
	"github.com/epifi/gamma/pkg/merchant"
	upiPkg "github.com/epifi/gamma/pkg/upi"

	"github.com/epifi/be-common/pkg/async/goroutine"

	merchantResolutionPb "github.com/epifi/gamma/api/vendorgateway/merchantresolution"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/actor/config"
	"github.com/epifi/gamma/actor/dao"
	events2 "github.com/epifi/gamma/actor/events"
	actorWireTypes "github.com/epifi/gamma/actor/wire/types"
	actorPb "github.com/epifi/gamma/api/actor"
	actorCapPb "github.com/epifi/gamma/api/actor/capability"
	actorConsumerPb "github.com/epifi/gamma/api/actor/consumer"
	authPb "github.com/epifi/gamma/api/auth"
	mPb "github.com/epifi/gamma/api/merchant"
	beneficiaryManagementPb "github.com/epifi/gamma/api/pay/beneficiarymanagement"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	timelinePb "github.com/epifi/gamma/api/timeline"
	upiPb "github.com/epifi/gamma/api/upi"
	userPb "github.com/epifi/gamma/api/user"
	pkgMap "github.com/epifi/gamma/pkg/map"
	"github.com/epifi/gamma/pkg/pay"
)

var (
	statusActorNotBlocked rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(uint32(actorPb.ReportSpamForBlockedActorResponse_ACTOR_NOT_BLOCKED), "actor not blocked", "")
	}
	InvalidVPAError     = errors.New("VPA is invalid")
	PSPNotRegisterError = errors.New("PSP not registered")
	ErrorValidatingVpa  = fmt.Errorf("error validating vpa")
)

type Service struct {
	// UnimplementedActorServer is embedded to have forward compatible implementations
	actorPb.UnimplementedActorServer
	actorDao                    dao.ActorDao
	actorPiResolutionDao        dao.ActorPiResolutionDao
	blockedActorMapDao          dao.BlockedActorMapDao
	piClient                    piPb.PiClient
	accountPiRelationClient     accountPiPb.AccountPIRelationClient
	usersClient                 userPb.UsersClient
	eventBroker                 events.Broker
	merchantClient              mPb.MerchantServiceClient
	upiClient                   upiPb.UPIClient
	staticActorResolutionMap    map[string]string
	StaticExternalUserIconMap   map[string]string
	staticActorPiResolutionMap  map[string]string
	staticActorCapabilityMap    map[string]map[string]bool
	actorCreationEventPublisher actorWireTypes.ActorCreationEventPublisher
	timelineClient              timelinePb.TimelineServiceClient
	authClient                  authPb.AuthClient
	conf                        *config.Config
	dynConf                     *genconf.Config
	merchantResolutionPkg       merchantResolutionPb.MerchantResolutionServer
	beneficiaryManagementClient beneficiaryManagementPb.BeneficiaryManagementClient
	tspUserClient               tspUserPb.TspUserServiceClient
}

// Factory method for creating an instance of actor service.
func NewService(conf *config.Config, dynConf *genconf.Config, actorDao dao.ActorDao, actorPiResolutionDao dao.ActorPiResolutionDao, piClient piPb.PiClient, accountPiRelationClient accountPiPb.AccountPIRelationClient, usersClient userPb.UsersClient, blockedActorMapDao dao.BlockedActorMapDao,
	broker events.Broker, merchantClient mPb.MerchantServiceClient, upiClient upiPb.UPIClient, actorCreationEventPublisher actorWireTypes.ActorCreationEventPublisher, timelineClient timelinePb.TimelineServiceClient, authClient authPb.AuthClient,
	beneficiaryManagementClient beneficiaryManagementPb.BeneficiaryManagementClient, merchantResolutionPkg merchantResolutionPb.MerchantResolutionServer, tspUserClient tspUserPb.TspUserServiceClient) *Service {
	return &Service{
		actorDao:                    actorDao,
		actorPiResolutionDao:        actorPiResolutionDao,
		piClient:                    piClient,
		accountPiRelationClient:     accountPiRelationClient,
		usersClient:                 usersClient,
		blockedActorMapDao:          blockedActorMapDao,
		eventBroker:                 broker,
		merchantClient:              merchantClient,
		upiClient:                   upiClient,
		staticActorResolutionMap:    conf.StaticActorResolutionMap,
		StaticExternalUserIconMap:   conf.StaticExternalUserIconMap,
		staticActorPiResolutionMap:  getStaticActorPiResolutionMap(conf.StaticActorResolutionMap),
		staticActorCapabilityMap:    conf.StaticActorCapabilityMap,
		actorCreationEventPublisher: actorCreationEventPublisher,
		timelineClient:              timelineClient,
		authClient:                  authClient,
		conf:                        conf,
		dynConf:                     dynConf,
		merchantResolutionPkg:       merchantResolutionPkg,
		beneficiaryManagementClient: beneficiaryManagementClient,
		tspUserClient:               tspUserClient,
	}
}

func getStaticActorPiResolutionMap(StaticActorResolutionMap map[string]string) map[string]string {
	staticActorPiResolutionMap := pkgMap.ReverseMap(StaticActorResolutionMap)
	// Since multiple pis (international and pool accounts) are mapped to "actor-alpaca", in the forward map we will get a random value each time the map in initialised
	// This might lead to unwanted behavior, hence we manually override the value
	staticActorPiResolutionMap[iftPkg.AlpacaActorID] = iftPkg.AlpacaWalletFundingPI
	return staticActorPiResolutionMap
}

// CreateActor creates an actor
func (s *Service) CreateActor(ctx context.Context, req *actorPb.CreateActorRequest) (*actorPb.CreateActorResponse, error) {
	var (
		savedActor *types.Actor
		err        error
		res        = &actorPb.CreateActorResponse{}
		ownership  = req.GetOwnership()
	)

	actor := &types.Actor{
		Type:      req.Type,
		EntityId:  req.EntityId,
		Name:      req.Name,
		Ownership: ownership,
	}

	// TODO(kunal): Should this call user or merchant service to verify if entity_id exists?
	if savedActor, err = s.actorDao.Create(ctx, actor); err != nil {
		if errors.Is(err, epifierrors.ErrUniqueConstraintViolation) {
			res.Status = rpc.StatusAlreadyExists()
			logger.Debug(ctx, "actor with entity_id already exists", zap.Error(err))
			return res, nil
		}
		res.Status = rpc.StatusInternal()
		logger.Error(ctx, "failed to create actor", zap.Error(err))
		//nocustomlint:goroutine
		go s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events2.NewCreatedActorID("", "", time.Now(), events2.Failure, err.Error(), req.GetType()))
		return res, nil
	}

	//nocustomlint:goroutine
	go s.PublishActorCreationEvent(epificontext.CloneCtx(ctx), &actorConsumerPb.ActorCreationEvent{ActorId: savedActor.GetId(), Type: ActorTypeConversionToV2(savedActor.GetType())})
	//nocustomlint:goroutine
	go s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events2.NewCreatedActorID("", "", time.Now(), events2.Success, "", req.GetType()))
	res.Actor = savedActor
	res.Status = rpc.StatusOk()
	return res, nil
}

// GetActorById fetches actor by id
func (s *Service) GetActorById(ctx context.Context, req *actorPb.GetActorByIdRequest) (*actorPb.GetActorByIdResponse, error) {
	var (
		fetchedActor *types.Actor
		err          error
		res          = &actorPb.GetActorByIdResponse{}
	)

	if fetchedActor, err = s.actorDao.GetById(ctx, req.Id); err != nil {
		logger.Error(ctx, "failed to get actor by id", zap.String("actor_id", req.Id), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Actor = fetchedActor
	res.Status = rpc.StatusOk()
	return res, nil
}

// GetActorByEntityId fetches actor by entity_id and actor_type
func (s *Service) GetActorByEntityId(ctx context.Context, req *actorPb.GetActorByEntityIdRequest) (*actorPb.GetActorByEntityIdResponse, error) {
	var (
		fetchedActor *types.Actor
		err          error
		res          = &actorPb.GetActorByEntityIdResponse{}
	)

	if fetchedActor, err = s.actorDao.GetByEntityId(ctx, req.EntityId, req.Type); err != nil {
		logger.Error(ctx, "failed to get actor by entity id", zap.Error(err))
		if storageV2.IsRecordNotFoundError(err) {
			return &actorPb.GetActorByEntityIdResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Actor = fetchedActor
	res.Status = rpc.StatusOk()
	return res, nil
}

// CreateActorPiResolution creates a ActorPiResolution
func (s *Service) CreateActorPiResolution(ctx context.Context, req *actorPb.CreateActorPiResolutionRequest) (*actorPb.CreateActorPiResolutionResponse, error) {
	var (
		savedActorPiResolution *actorPb.ActorPiResolution
		err                    error
		res                    = &actorPb.CreateActorPiResolutionResponse{}
	)

	// Exactly one of PiFrom or PiTo should be non empty
	// TODO(kunal): Try with oneof
	if (req.PiFrom == "" && req.PiTo == "") || (req.PiFrom != "" && req.PiTo != "") {
		res.Status = rpc.StatusInvalidArgument()
		return res, err
	}

	actorPiResolution := &actorPb.ActorPiResolution{
		ActorFrom: req.ActorFrom,
		ActorTo:   req.ActorTo,
		PiFrom:    req.PiFrom,
		PiTo:      req.PiTo,
	}

	if savedActorPiResolution, err = s.actorPiResolutionDao.Create(ctx, actorPiResolution); err != nil {
		res.Status = rpc.StatusInternal()
		logger.Error(ctx, "failed to create actor_pi_resolution", zap.Error(err))
		return res, nil
	}

	res.ActorPiResolution = savedActorPiResolution
	res.Status = rpc.StatusOk()
	return res, nil
}

// ResolveActorTo resolves the actor to whom the payment is being done (actorTo) for given actorFrom and PiTo
// We first check if the actorTo can be resolved via actor_pi_resolution table
// If not we check if the piTo is internal or external.
// For internal, we resolve the actor who owns this PI via account_pi relation
// For external, we create an external actor
// For both the above cases, we make an entry in actor_pi_resolution table
//
//nolint:funlen,dupl
func (s *Service) ResolveActorTo(
	ctx context.Context,
	req *actorPb.ResolveActorToRequest,
) (*actorPb.ResolveActorToResponse, error) {
	var (
		actorTo        string
		err            error
		res            = &actorPb.ResolveActorToResponse{}
		piTo           *piPb.PaymentInstrument
		isMerchant, ok bool
	)

	// if pi is a static pi then return actor id corresponding to that
	if actorTo, ok = s.staticActorResolutionMap[req.GetPiTo()]; ok {
		res.Status = rpc.StatusOk()
		res.ActorTo = actorTo
		return res, nil
	}

	// For users who have already transacted with merchants but those merchants were created as external actor in our
	// system an entry was created in actor pi resolution table due to which everytime if a user tries to make payment
	// to that VPA we return the actor of type external user here, instead of that we need to call create merchant
	// service to ensure that if a merchant was not already created it is done
	// Sending ActorToName from the request to differentiate ATM withdrawal and deposits from rest of the transactions as ActorToName by default contains "ATM" for these 2 transactions only
	piTo, actorTo, err, isMerchant = s.resolveMerchant(ctx, req.GetPiTo(), req.ActorToName)
	if err != nil {
		logger.Error(ctx, "failed to resolve merchant", zap.String(logger.ACTOR_ID_V2, req.ActorFrom),
			zap.String(logger.PI_ID, req.PiTo), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if isMerchant {
		res.Status = rpc.StatusOk()
		res.ActorTo = actorTo
		return res, nil
	}
	// If actor is not of type merchant we try to resolve it using cache if entry in actor pi resolution
	// is present for this actor
	// Check if actor can be resolved via actor_pi_resolution table
	// For external merchant actors this should always return record not found
	// Err was of type gormv2.ErrRecordNotFound, so actor could not be resolved via actor_pi_resolution table
	// Fetch pi and check if PI is internal or external
	if actorTo, err = s.actorPiResolutionDao.ResolveActor(ctx, req.ActorFrom, req.PiTo); actorTo != "" {
		res.ActorTo = actorTo
		updateOwnershipErr := s.updateActorOwnership(ctx, actorTo, req.GetOwnership())
		if updateOwnershipErr != nil {
			logger.Error(ctx, "error in updating actor ownership", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.Status = rpc.StatusOk()
		return res, nil
	}

	// If err doesn't contain gormv2.ErrRecordNotFound in the chain then return err
	if !errors.Is(err, gormv2.ErrRecordNotFound) {
		res.Status = rpc.StatusInternal()
		logger.Error(ctx, "failed to resolve actor via actor_pi_resolution", zap.String("actor_id", req.ActorFrom),
			zap.String("pi_to", req.PiTo), zap.Error(err))
		return res, nil
	}
	isPiInternal := piTo.IsIssuedInternally()
	// If PI is internal use account_pi to get its associated actor
	if isPiInternal {
		var getByPiIdResponse *accountPiPb.GetByPiIdResponse
		if getByPiIdResponse, err = s.accountPiRelationClient.GetByPiId(ctx, &accountPiPb.GetByPiIdRequest{
			PiId: req.PiTo,
		}); err != nil || !getByPiIdResponse.Status.IsSuccess() {
			res.Status = rpc.StatusInternal()
			logger.Error(ctx, "failed to fetch actor via account_pi", zap.String("actor_id", req.ActorFrom),
				zap.String("pi_to", req.PiTo), zap.Error(err))
			return res, nil
		}

		actorTo = getByPiIdResponse.ActorId
	} else {
		// For external PI's we have already handled above for type merchants so here
		// we can always assume that is is of type external user
		var createdActor *types.Actor
		// Else if external user create a new external actor
		if createdActor, err = s.actorDao.Create(ctx, &types.Actor{
			Type:      types.Actor_EXTERNAL_USER,
			Name:      req.GetActorToName(),
			Ownership: req.GetOwnership(),
		}); err != nil {
			logger.Error(ctx, "error handling external PI in ResolveActorTo while creating actor",
				zap.String(logger.ACTOR_ID, req.GetActorFrom()), zap.String(logger.PI_ID, req.GetPiTo()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		//nocustomlint:goroutine
		go s.PublishActorCreationEvent(epificontext.CloneCtx(ctx), &actorConsumerPb.ActorCreationEvent{ActorId: createdActor.GetId(), Type: ActorTypeConversionToV2(createdActor.GetType())})
		actorTo = createdActor.GetId()
	}

	// TODO(kunal): This and creation of external actor(if there is) has to happen in transaction
	// Make an entry in actor_pi_resolution
	if _, err := s.actorPiResolutionDao.Create(ctx, &actorPb.ActorPiResolution{
		ActorFrom: req.ActorFrom,
		ActorTo:   actorTo,
		PiFrom:    "",
		PiTo:      req.PiTo,
	}); err != nil {
		res.Status = rpc.StatusInternal()
		logger.Error(ctx, "failed to create actor_pi_resolution", zap.String("actor_id", req.ActorFrom),
			zap.String("pi_to", req.PiTo), zap.Error(err))
		return res, nil
	}

	if createBeneficiaryErr := s.createBeneficiary(ctx, req.GetActorFrom(), req.GetPiTo()); createBeneficiaryErr != nil {
		logger.Error(ctx, "error in creating beneficiary", zap.String(logger.PI_TO, req.GetPiTo()), zap.Error(err))
	}

	res.ActorTo = actorTo
	res.Status = rpc.StatusOk()
	return res, nil
}

// ResolveActorFrom resolves the actor who made the payment (actorFrom) for a given actorTo and PiFrom
// We first check if the actorFrom can be resolved via actor_pi_resolution table
// If not we check if the piFrom is internal or external.
// For internal, we resolve the actor who owns this PI via account_pi relation
// For external, we create an external actor
// For both the above cases, we make an entry in actor_pi_resolution table
// TODO(kunal): Add tests for this function
//
//nolint:funlen,dupl
func (s *Service) ResolveActorFrom(
	ctx context.Context,
	req *actorPb.ResolveActorFromRequest,
) (*actorPb.ResolveActorFromResponse, error) {
	var (
		actorFrom      string
		err            error
		res            = &actorPb.ResolveActorFromResponse{}
		piFrom         *piPb.PaymentInstrument
		isMerchant, ok bool
	)

	// if pi is a static pi then return actor id corresponding to that
	if actorFrom, ok = s.staticActorResolutionMap[req.GetPiFrom()]; ok {
		res.Status = rpc.StatusOk()
		res.ActorFrom = actorFrom
		return res, nil
	}

	// For users who have already transacted with merchants but those merchants were created as external actor in our
	// system an entry was created in actor pi resolution table due to which everytime if a user tries to make payment
	// to that VPA we return the actor of type external user here, instead of that we need to call create merchant
	// service to ensure that if a merchant was not already created it is done
	// Sending ActorFromName from the request to differentiate ATM withdrawal and deposits from rest of the transactions as ActorFromName by default contains "ATM" for these 2 transactions only
	piFrom, actorFrom, err, isMerchant = s.resolveMerchant(ctx, req.GetPiFrom(), req.ActorFromName)
	if err != nil {
		logger.Error(ctx, "failed to resolve merchant", zap.String(logger.ACTOR_ID_V2, req.GetActorTo()),
			zap.String(logger.PI_ID, req.GetPiFrom()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if isMerchant {
		res.Status = rpc.StatusOk()
		res.ActorFrom = actorFrom
		return res, nil
	}
	// Check if actor can be resolved via actor_pi_resolution table
	// For external merchant actors this should always return record not found
	if actorFrom, err = s.actorPiResolutionDao.ResolveActor(ctx, req.ActorTo, req.PiFrom); actorFrom != "" {
		res.ActorFrom = actorFrom
		updateOwnershipErr := s.updateActorOwnership(ctx, actorFrom, req.GetOwnership())
		if updateOwnershipErr != nil {
			logger.Error(ctx, "error in updating actor ownership", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.Status = rpc.StatusOk()
		return res, nil
	}

	// If err doesn't contain gormv2.ErrRecordNotFound in the chain then return err
	if !errors.Is(err, gormv2.ErrRecordNotFound) {
		res.Status = rpc.StatusInternal()
		logger.Error(ctx, "failed to resolve actor via actor_pi_resolution", zap.String("actor_id", req.ActorTo),
			zap.String("pi_from", req.PiFrom), zap.Error(err))
		return res, nil
	}

	isPiInternal := piFrom.IsIssuedInternally()
	// If PI is internal use account_pi to get its associated actor
	if isPiInternal {
		var getByPiIdResponse *accountPiPb.GetByPiIdResponse
		if getByPiIdResponse, err = s.accountPiRelationClient.GetByPiId(ctx, &accountPiPb.GetByPiIdRequest{
			PiId: req.PiFrom,
		}); err != nil || !getByPiIdResponse.Status.IsSuccess() {
			res.Status = rpc.StatusInternal()
			logger.Error(ctx, "failed to fetch actor via account_pi", zap.String("actor_id", req.ActorTo),
				zap.String("pi_from", req.PiFrom), zap.Error(err))
			return res, nil
		}

		actorFrom = getByPiIdResponse.ActorId
	} else {
		// For external PI's we have already handled above for type merchants so here
		// we can always assume that is is of type external user
		var createdActor *types.Actor
		// Else if external user create a new external actor
		if createdActor, err = s.actorDao.Create(ctx, &types.Actor{
			Type:      types.Actor_EXTERNAL_USER,
			Name:      req.GetActorFromName(),
			Ownership: req.GetOwnership(),
		}); err != nil {
			logger.Error(ctx, "error handling external PI in ResolveActorFrom while creating actor",
				zap.String(logger.ACTOR_ID, req.GetActorTo()), zap.String(logger.PI_ID, req.GetPiFrom()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		//nocustomlint:goroutine
		go s.PublishActorCreationEvent(epificontext.CloneCtx(ctx), &actorConsumerPb.ActorCreationEvent{ActorId: createdActor.GetId(), Type: ActorTypeConversionToV2(createdActor.GetType())})
		actorFrom = createdActor.GetId()
	}

	// TODO(kunal): This and creation of external actor(if there is) has to happen in transaction
	// Make an entry in actor_pi_resolution
	if _, err := s.actorPiResolutionDao.Create(ctx, &actorPb.ActorPiResolution{
		ActorFrom: actorFrom,
		ActorTo:   req.ActorTo,
		PiFrom:    req.PiFrom,
		PiTo:      "",
	}); err != nil {
		res.Status = rpc.StatusInternal()
		logger.Error(ctx, "failed to create actor_pi_resolution", zap.String("actor_id", req.ActorTo),
			zap.String("pi_from", req.PiFrom), zap.Error(err))
		return res, nil
	}

	res.ActorFrom = actorFrom
	res.Status = rpc.StatusOk()
	return res, nil
}

// GetPIsOfActorTo returns PIs belonging to actorTo, as known by actorFrom
//
// Note: `Limit` Parameter is only used For `Actor_EXTERNAL_MERCHANT`, where we fetch the PIs associated with the merchant using the `getMerchantPis()` method.
// For other actor types, the limit is ignored, and we attempt to fetch all PIs
// If the limit is not provided or provided as 0, we will fetch all PIs.
// TODO(Ashutosh): To avoid confusion and to ensure consistency of RPC , will enforce limit for all Types.
func (s *Service) GetPIsOfActorTo(ctx context.Context, req *actorPb.GetPIsOfActorToRequest) (*actorPb.GetPIsOfActorToResponse, error) {
	var (
		actorPiResolutions []*actorPb.ActorPiResolution
		err                error
		res                = &actorPb.GetPIsOfActorToResponse{}
		actorTo            *types.Actor
		piIds              = []string{}
	)

	actorTo, err = s.actorDao.GetById(ctx, req.GetActorTo())
	if err != nil {
		logger.Error(ctx, "error fetching actor", zap.String(logger.ACTOR_ID, req.GetActorTo()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	switch actorTo.Type {
	case types.Actor_USER:
		var pisByActorIdRes *accountPiPb.GetByActorIdResponse
		pisByActorIdRes, err = s.accountPiRelationClient.GetByActorId(ctx, &accountPiPb.GetByActorIdRequest{ActorId: actorTo.GetId()})
		if err != nil {
			logger.Error(ctx, "error fetching Pis of actor", zap.String(logger.ACTOR_ID, actorTo.GetId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		if !pisByActorIdRes.GetStatus().IsSuccess() {
			logger.Error(ctx, fmt.Sprintf("GetByActorId() rpc failed with status, %s", pisByActorIdRes.GetStatus()), zap.Error(err))
			res.Status = pisByActorIdRes.GetStatus()
			return res, nil
		}
		for _, accountPi := range pisByActorIdRes.GetAccountPis() {
			piIds = append(piIds, accountPi.GetPiId())
		}
	case types.Actor_EXTERNAL_USER:
		// if actorTo is from static mapping returning static pi id.
		// This is specific for static mappings added via fixture and use for payment like mutual fund etc.
		if piId, ok := s.staticActorPiResolutionMap[req.GetActorTo()]; ok {
			logger.Debug(ctx, "using static map for pi resolution",
				zap.String(logger.ACTOR_ID, req.GetActorTo()), zap.String(logger.PI_ID, piId))
			piIds = append(piIds, piId)
			res.PiIds = piIds
			res.Status = rpc.StatusOk()
			return res, nil
		}

		if actorPiResolutions, err = s.actorPiResolutionDao.GetPIsOfActorTo(ctx, req.ActorFrom, req.ActorTo); err != nil {
			res.Status = rpc.StatusInternal()
			logger.Error(ctx, "failed to get PIs belonging to actorTo", zap.Error(err))
			return res, nil
		}
		piToIds := getPiTos(actorPiResolutions)
		piFromIds := getPiFroms(actorPiResolutions)
		piIds = append(piIds, piToIds...)
		piIds = append(piIds, piFromIds...)
		// there can be possible duplicate PIs when looking for both pi from and pi to
		// hence inorder to maintain uniqueness of the PI Ids returned in the result
		// we need to trim the duplicate pi Ids after appending
		piIds = funk.UniqString(piIds)
	case types.Actor_EXTERNAL_MERCHANT:
		// For actors of type external merchants PI's will be fetched from merchant service since actor pi resolution
		// entries won't exist
		piIds, err = s.getMerchantPis(ctx, actorTo.GetEntityId(), req.GetLimit())
		if err != nil {
			logger.Error(ctx, "error fetching Pis of merchant actor", zap.String(logger.ACTOR_ID, actorTo.GetId()), zap.Error(err))
			res.Status = lo.Ternary(errors.Is(err, epifierrors.ErrRecordNotFound), rpc.StatusRecordNotFound(), rpc.StatusInternal())
			return res, nil
		}
	default:
		logger.Error(ctx, fmt.Sprintf("Invalid actor type, %s", actorTo.GetType()))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	res.PiIds = piIds
	res.Status = rpc.StatusOk()
	return res, nil
}

func getPiTos(actorPiResolutions []*actorPb.ActorPiResolution) []string {
	piTos := []string{}
	for _, actorPiResolution := range actorPiResolutions {
		// for incoming payment mapping pi from can be empty
		if actorPiResolution.PiTo != "" {
			piTos = append(piTos, actorPiResolution.PiTo)
		}
	}
	return piTos
}

func getPiFroms(actorPiResolutions []*actorPb.ActorPiResolution) []string {
	piTos := []string{}
	for _, actorPiResolution := range actorPiResolutions {
		// for outgoing payment mapping pi from can be empty
		if actorPiResolution.PiFrom != "" {
			piTos = append(piTos, actorPiResolution.PiFrom)
		}
	}
	return piTos
}

// GetEntityDetailsByActorId returns the entity details like mobile number, name, email id etc. for the given actor id
func (s *Service) GetEntityDetailsByActorId(ctx context.Context, req *actorPb.GetEntityDetailsByActorIdRequest) (*actorPb.GetEntityDetailsByActorIdResponse, error) {
	var (
		res = &actorPb.GetEntityDetailsByActorIdResponse{}
	)

	if req.GetActorId() == "" {
		logger.Error(ctx, "actor id can't be empty")
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	actor, err := s.actorDao.GetById(ctx, req.GetActorId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "actor does not exist for the given actor id", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Error(err))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error fetching actor for actor id", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	switch actor.Type {
	case types.Actor_USER:
		getUserRes, err := s.usersClient.GetUser(ctx, &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_Id{
				Id: actor.GetEntityId(),
			},
			WantProfileImageUrl: true,
		})
		switch {
		case err != nil || getUserRes.GetStatus().IsInternal():
			logger.Error(ctx, "error getting user for actor", zap.String(logger.ENTITY_ID, actor.GetEntityId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		case !getUserRes.GetStatus().IsSuccess():
			logger.Error(ctx, fmt.Sprintf("GetUser() rpc failed with status %s", getUserRes.GetStatus()),
				zap.String(logger.ENTITY_ID, actor.GetEntityId()))
			res.Status = rpc.StatusInvalidArgument()
			return res, nil
		}
		user := getUserRes.GetUser()
		res.Type = types.ActorType_USER
		res.MobileNumber = user.GetProfile().GetPhoneNumber()
		res.EmailId = user.GetProfile().GetEmail()
		res.EntityId = user.GetId()
		res.Name = gammanames.BestNameFromProfile(ctx, user.GetProfile())
		res.LegalName = user.GetProfile().GetKycName()
		res.ProfileImageUrl = user.GetProfile().GetProfileImageUrl()
	case types.Actor_EXTERNAL_USER:
		res.Type = types.ActorType_EXTERNAL_USER
		res.Name = (&commontypes.Name{}).ParseWithHonorific(actor.GetName())
		if iconUrl, ok := s.StaticExternalUserIconMap[actor.GetId()]; ok {
			res.ProfileImageUrl = iconUrl
		}
	case types.Actor_EXTERNAL_MERCHANT:
		merchResp, err := s.merchantClient.GetMerchant(ctx, &mPb.GetMerchantRequest{
			Identifier: &mPb.GetMerchantRequest_MerchantId{MerchantId: actor.GetEntityId()}})
		if err := epifigrpc.RPCError(merchResp, err); err != nil {
			logger.Error(ctx, "cannot get merchant by entity id", zap.Error(err))
			if merchResp.GetStatus().IsRecordNotFound() {
				res.Status = rpc.StatusRecordNotFound()
			}
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.Type = types.ActorType_EXTERNAL_MERCHANT
		res.EntityId = merchResp.GetMerchant().GetId()
		res.ProfileImageUrl = merchResp.GetMerchant().GetLogoUrl()
		res.Name = (&commontypes.Name{}).Parse(merchResp.GetMerchant().GetName())
		res.LegalName = (&commontypes.Name{}).Parse(merchResp.GetMerchant().GetLegalName())
		res.Entity = &actorPb.GetEntityDetailsByActorIdResponse_Merchant_{
			Merchant: &actorPb.GetEntityDetailsByActorIdResponse_Merchant{
				Capabilities: merchResp.GetMerchant().GetCapabilities(),
			},
		}
	case types.Actor_TSP_USER:
		owner, err := owner.GetOwnerFromOwnership(actor.GetOwnership())
		if err != nil {
			logger.Error(ctx, "failed to get ownership from owner", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		tspUserResp, err := s.tspUserClient.GetTspUser(ctx, &tspUserPb.GetTspUserRequest{
			Owner: owner,
			Identifier: &tspUserPb.GetTspUserRequest_TspUserId{
				TspUserId: actor.GetEntityId(),
			},
		})
		if te := epifigrpc.RPCError(tspUserResp, err); te != nil {
			logger.Error(ctx, "failed to get tspUser", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

		res.Name = tspUserResp.GetTspUser().GetPersonalDetails().GetName()
		res.EmailId = tspUserResp.GetTspUser().GetPersonalDetails().GetEmail()
		res.MobileNumber = tspUserResp.GetTspUser().GetPersonalDetails().GetPhoneNumber()
		res.EntityId = tspUserResp.GetTspUser().GetId()
		res.Type = types.ActorType_TSP_USER
	default:
		logger.Error(ctx, fmt.Sprintf("Unsupported actor type %s", actor.Type))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	res.Status = rpc.StatusOk()
	return res, nil
}

// GetEntityDetails returns list of entity level details like mobile number, name etc. for list of actor id's.
func (s *Service) GetEntityDetails(ctx context.Context, req *actorPb.GetEntityDetailsRequest) (*actorPb.GetEntityDetailsResponse, error) {
	var (
		res           = &actorPb.GetEntityDetailsResponse{}
		entityDetails []*actorPb.GetEntityDetailsResponse_EntityDetail
	)
	actors, err := s.actorDao.GetByIds(ctx, req.GetActorIds())
	switch {
	case errors.Is(err, gormv2.ErrRecordNotFound):
		logger.Error(ctx, "actors does not exist for the given actor ids", zap.Error(err))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error fetching actors for actor ids", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	var (
		merchantIds          []string
		entityIdToActorIdMap = make(map[string]string, 0)
		userIdentifiers      []*userPb.GetUsersRequest_GetUsersIdentifier
		tspUserActors        []*types.Actor
	)

	for _, actor := range actors {
		switch actor.Type {
		case types.Actor_USER:
			userIdentifiers = append(userIdentifiers, &userPb.GetUsersRequest_GetUsersIdentifier{
				Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Id{Id: actor.GetEntityId()},
			})
		case types.Actor_EXTERNAL_USER:
			entity := &actorPb.GetEntityDetailsResponse_EntityDetail{}
			entity.ActorId = actor.GetId()
			entity.Name = (&commontypes.Name{}).ParseWithHonorific(actor.GetName())
			entity.EntityId = actor.GetEntityId()
			entity.EntityType = types.ActorType_EXTERNAL_USER
			if iconUrl, ok := s.StaticExternalUserIconMap[actor.GetId()]; ok {
				entity.ProfileImageUrl = iconUrl
			}
			entityDetails = append(entityDetails, entity)
		case types.Actor_EXTERNAL_MERCHANT:
			merchantIds = append(merchantIds, actor.GetEntityId())
			entityIdToActorIdMap[actor.GetEntityId()] = actor.GetId()
		case types.Actor_TSP_USER:
			tspUserActors = append(tspUserActors, actor)
		default:
			logger.Error(ctx, fmt.Sprintf("Unsupported actor type %s", actor.Type))
			res.Status = rpc.StatusInvalidArgument()
			return res, nil
		}
	}
	// Need this check because merchant service gives error for empty input
	if len(merchantIds) != 0 {
		entityDetails, err = s.getMerchantEntityDetails(ctx, merchantIds, entityDetails, entityIdToActorIdMap)
		if err != nil {
			// If merchant id's were found for actor ids input and we are unable to get data for some,
			// We treat it as error in our system
			logger.Error(ctx, "error fetching external merchants using merchant ids", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	if len(tspUserActors) != 0 {
		tspUserEntities, tspUserErr := s.getTspUserEntityDetails(ctx, tspUserActors)
		if tspUserErr != nil {
			logger.Error(ctx, "error fetching tsp user entity details", zap.Error(tspUserErr))
			return &actorPb.GetEntityDetailsResponse{Status: rpc.StatusInternal()}, nil
		}

		entityDetails = append(entityDetails, tspUserEntities...)
	}

	if len(userIdentifiers) == 0 {
		res.EntityDetails = entityDetails
		res.Status = rpc.StatusOk()
		return res, nil
	}
	getUsersRes, err := s.usersClient.GetUsers(ctx, &userPb.GetUsersRequest{Identifier: userIdentifiers, Unscoped: req.GetFetchSoftDeletedUsers()})
	switch {
	case err != nil || getUsersRes.GetStatus().IsInternal():
		logger.Error(ctx, "error getting user for actor", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	case !getUsersRes.Status.IsSuccess():
		logger.Error(ctx, fmt.Sprintf("GetUser() rpc failed with status %s", getUsersRes.GetStatus()))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	users := getUsersRes.GetUsers()
	idToUser := make(map[string]*userPb.User)
	for _, user := range users {
		idToUser[user.GetId()] = user
	}

	for _, actor := range actors {
		if actor.Type == types.Actor_USER {
			entity := &actorPb.GetEntityDetailsResponse_EntityDetail{}
			user, ok := idToUser[actor.GetEntityId()]
			if !ok {
				logger.Info(ctx, "user not found for actor", zap.String(logger.ENTITY_ID, actor.GetEntityId()))
				continue
			}
			entity.EntityType = types.ActorType_USER
			entity.ActorId = actor.GetId()
			entity.MobileNumber = user.GetProfile().GetPhoneNumber()
			entity.EmailId = user.GetProfile().GetEmail()
			entity.EntityId = user.GetId()
			entity.ProfileImageUrl = user.GetProfile().GetProfileImageUrl()
			// For Fi-Lite users, kyc name won't be populated
			// so, we'll choose the best possible name for the user.
			entity.Name = gammanames.BestNameFromProfile(ctx, user.GetProfile())
			entityDetails = append(entityDetails, entity)
		}
	}

	res.EntityDetails = entityDetails
	res.Status = rpc.StatusOk()

	return res, nil
}

// BlockActor blocks the other actor for logged in actor and optionally mark as spam.
// The `is_spam` flag in request is used while blocking an actor. If the flag is set, the actor is marked as a spam
// in the block_actors_map, spam count of all the associated payment instruments are incremented by 1.
// Note: ALREADY_PROCESSED is returned even if actor is already marked as blocked
// The client is expected to use ReportSpamForBlockedActor RPC to report spam for an already blocked actor
func (s *Service) BlockActor(ctx context.Context, req *actorPb.BlockActorRequest) (*actorPb.BlockActorResponse, error) {
	res := &actorPb.BlockActorResponse{}

	if req.GetOtherActorId() == req.GetCurrentActorId() {
		logger.Error(ctx, "both actors can't be same")
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	blockedActorMap, err := s.blockedActorMapDao.GetByActorIdAndBlockedActorId(ctx, req.GetCurrentActorId(), req.GetOtherActorId())
	switch {
	case err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "failed to fetch blocked actor entry from DB", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	case blockedActorMap != nil:
		logger.Info(ctx, "actor already blocked",
			zap.String("currentActorId", req.GetCurrentActorId()), zap.String("otherActorId", req.GetOtherActorId()))
		res.Status = rpc.ExtendedStatusAlreadyProcessedWithDebug("actor already blocked")
		return res, nil
	}

	_, err = s.blockedActorMapDao.Create(ctx, &actorPb.BlockedActorMap{
		ActorId:        req.GetCurrentActorId(),
		BlockedActorId: req.GetOtherActorId(),
		IsSpam:         req.GetIsSpam(),
	})
	if err != nil {
		logger.Error(ctx, "failed to block actor", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// TODO(nitesh): add logic to increase the spam count of associated PI
	res.Status = rpc.StatusOk()
	return res, nil
}

// UnblockActor unblocks the other actor for the logged in actor and unset the spam flags if spam was reported.
//
// If the blocked actor was found to be marked as spam, spam count of all the associated payment instruments are
// decremented by 1.
// Note: ALREADY_PROCESSED is returned even if actor is already unblocked
func (s *Service) UnblockActor(ctx context.Context, req *actorPb.UnblockActorRequest) (*actorPb.UnblockActorResponse, error) {
	res := &actorPb.UnblockActorResponse{}

	if req.GetOtherActorId() == req.GetCurrentActorId() {
		logger.Error(ctx, "both actors can't be same")
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	err := s.blockedActorMapDao.Delete(ctx, req.GetCurrentActorId(), req.GetOtherActorId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "actor already unblocked",
			zap.String("currentActorId", req.GetCurrentActorId()), zap.String("otherActorId", req.GetOtherActorId()))
		res.Status = rpc.ExtendedStatusAlreadyProcessed()
		return res, nil
	case err != nil:
		logger.Error(ctx, "failed to unblock actor", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// TODO(nitesh): add logic to decrease the spam count of associated PI
	res.Status = rpc.StatusOk()
	return res, nil
}

// ReportSpamForBlockedActor reports spam for an actor who is already blocked and increments the spam count
// of all the associated payment instruments by 1.
// If actor is not already blocked then corresponding response code is returned
// Note: In case the other actor is already reported as spam by logged in actor, the RPC won't perform
// any operation and simply returns ALREADY_PROCESSED.
func (s *Service) ReportSpamForBlockedActor(ctx context.Context, req *actorPb.ReportSpamForBlockedActorRequest) (*actorPb.ReportSpamForBlockedActorResponse, error) {
	res := &actorPb.ReportSpamForBlockedActorResponse{}

	if req.GetBlockedActorId() == req.GetCurrentActorId() {
		logger.Error(ctx, "both actors can't be same")
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	blockedActorMap, err := s.blockedActorMapDao.GetByActorIdAndBlockedActorId(ctx, req.GetCurrentActorId(), req.GetBlockedActorId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "actor not blocked",
			zap.String("currentActorId", req.GetCurrentActorId()), zap.String("blockedActorId", req.GetBlockedActorId()))
		res.Status = statusActorNotBlocked()
		return res, nil
	case err != nil:
		logger.Error(ctx, "failed to fetch blocked actor entry from DB", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if blockedActorMap.IsSpam {
		logger.Error(ctx, "actor already marked spam",
			zap.String("currentActorId", req.GetCurrentActorId()), zap.String("blockedActorId", req.GetBlockedActorId()))
		res.Status = rpc.ExtendedStatusAlreadyProcessedWithDebug("actor already marked spam")
		return res, nil
	}

	err = s.blockedActorMapDao.SetSpamFlag(ctx, req.GetCurrentActorId(), req.GetBlockedActorId())
	if err != nil {
		logger.Error(ctx, "failed to set spam flag in DB",
			zap.String("currentActorId", req.GetCurrentActorId()), zap.String("blockedActorId", req.GetBlockedActorId()),
			zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// TODO(nitesh): add logic to increase the spam count of associated PI
	res.Status = rpc.StatusOk()
	return res, nil
}

// GetRelationshipWithActor returns other actor relationship (BLOCKED/UNBLOCKED/REPORTED) with the current
// logged in actor.
func (s *Service) GetRelationshipWithActor(ctx context.Context, req *actorPb.GetRelationshipWithActorRequest) (*actorPb.GetRelationshipWithActorResponse, error) {
	res := &actorPb.GetRelationshipWithActorResponse{}

	blockedActorMap, err := s.blockedActorMapDao.GetByActorIdAndBlockedActorId(ctx, req.GetCurrentActorId(), req.GetOtherActorId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		res.Status = rpc.StatusOk()
		res.Relationship = actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED
		return res, nil
	case err != nil:
		logger.Error(ctx, "failed to fetch blocked actor entry from DB", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if blockedActorMap.GetIsSpam() {
		res.Relationship = actorPb.GetRelationshipWithActorResponse_REPORTED
	} else {
		res.Relationship = actorPb.GetRelationshipWithActorResponse_BLOCKED
	}

	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) DeleteActor(ctx context.Context, req *actorPb.DeleteActorRequest) (*actorPb.DeleteActorResponse, error) {
	if err := s.actorDao.Delete(ctx, req.GetActorId()); err != nil {
		return &actorPb.DeleteActorResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in delete actor dao: %v", err)),
		}, nil
	}
	return &actorPb.DeleteActorResponse{
		Status: rpc.StatusOk(),
	}, nil
}

// method to find if pi is of type merchant or not
// Currently it only calls UPI service for PI which are of type UPI to get this information
func (s *Service) isMerchantPi(ctx context.Context, pi *piPb.PaymentInstrument, otherActor string) (string, string, bool, error) {
	switch pi.GetType() {
	case piPb.PaymentInstrumentType_UPI:
		upiResp, err := s.upiClient.GetVpaMerchantInfo(ctx, &upiPb.GetVpaMerchantInfoRequest{Vpa: pi.GetUpi().GetVpa()})
		if err = epifigrpc.RPCError(upiResp, err); err != nil {
			// In case of record not found, we do not treat it as an error, instead we assert that
			// the PI is not of merchant type
			if upiResp.GetStatus().IsRecordNotFound() {
				return "", "", false, nil
			} else {
				return "", "", false, errPkg.Wrap(err, "error calling get UPI service GetVpaMerchantInfo")
			}
		}
		return upiResp.GetVpaMerchantInfo().GetMerchantDetails().GetLegalName(), upiResp.GetVpaMerchantInfo().GetMerchantDetails().GetBrandName(), true, nil
	case piPb.PaymentInstrumentType_GENERIC:
		cardMerchResp, cardMerchRespErr := s.merchantClient.GetCardMerchantInfo(ctx, &mPb.GetCardMerchantInfoRequest{PiId: pi.GetId()})
		if cardMerchRespErr = epifigrpc.RPCError(cardMerchResp, cardMerchRespErr); cardMerchRespErr != nil {
			// In case of record not found, we do not treat it as an error, instead we assert that
			// the PI is not of merchant type
			if cardMerchResp.GetStatus().IsRecordNotFound() {
				merchantDetails, merchantDetailsErr := s.getMerchantV2(ctx, pi.GetId())
				if errors.Is(merchantDetailsErr, epifierrors.ErrRecordNotFound) {
					return "", "", false, nil
				}
				if merchantDetailsErr != nil {
					return "", "", false, merchantDetailsErr
				}
				return merchantDetails.GetLegalName(), merchantDetails.GetBrandName(), true, nil
			} else {
				return "", "", false, errPkg.Wrap(cardMerchRespErr, "error calling get merchant service GetCardMerchantInfo")
			}
		}
		if otherActor == pay.DefaultAtmActorName {
			// we are passing ATM as default value to distinguish ATM and cash deposit transactions from other type of transactions which will help us handle Merchant and actor creation separately for these transactions.
			return "", pay.DefaultAtmActorName, true, nil
		}
		return "", "", true, nil
	case piPb.PaymentInstrumentType_BANK_ACCOUNT:
		accountMerchantInfoRes, accountMerchantResErr := s.merchantClient.GetAccountMerchantInfo(ctx, &mPb.GetAccountMerchantInfoRequest{
			PiId: pi.GetId(),
		})
		switch {
		case accountMerchantResErr != nil:
			return "", "", false, fmt.Errorf("error in calling GetAccountMerchantInfo() %w", accountMerchantResErr)
		case accountMerchantInfoRes.GetStatus().IsRecordNotFound():
			return "", "", false, nil
		case !accountMerchantInfoRes.GetStatus().IsSuccess():
			return "", "", false, fmt.Errorf("non-success status in getting response GetAccountMerchantInfo() %v", accountMerchantInfoRes.GetStatus().String())
		default:
			return "", "", true, nil
		}
	default:
		return "", "", false, nil
	}
}

// getMerchantV2 checks if given PI is associated with any merchant from merchant pi resolution
// TODO(Harleen): migrate all the use cases to getMerchantV2 instead of isMerchantPi
func (s *Service) getMerchantV2(ctx context.Context, piId string) (*mPb.Merchant, error) {
	getMerchantRes, getMerchantErr := s.merchantClient.GetMerchant(ctx, &mPb.GetMerchantRequest{
		Identifier: &mPb.GetMerchantRequest_PiId{
			PiId: piId,
		},
	})
	err := epifigrpc.RPCError(getMerchantRes, getMerchantErr)
	switch {
	case getMerchantRes.GetStatus().IsRecordNotFound():
		return nil, epifierrors.ErrRecordNotFound
	case err != nil:
		return nil, fmt.Errorf("failed to check if given pi is of merchant type %w", err)
	default:
		return getMerchantRes.GetMerchant(), nil
	}
}

// Calls create merchant idempotent RPC and creates a new actor in system with actor id to merchant id mapping
func (s *Service) createMerchantActor(ctx context.Context, legalName, brandName, piId string) (*types.Actor, error) {
	// call merchant service to create a new merchant entity if not already created since this call
	// is idempotent
	merchantResp, err := s.merchantClient.CreateMerchant(ctx, &mPb.CreateMerchantRequest{
		LegalName: legalName,
		BrandName: brandName,
		PiId:      piId,
	})
	if err := epifigrpc.RPCError(merchantResp, err); err != nil {
		return nil, errPkg.Wrap(err, "error calling create merchant RPC")
	}
	var actor *types.Actor
	// check if actor already exists for this entity id and type external merchant and return gracefully
	actor, err = s.actorDao.GetByEntityId(ctx, merchantResp.GetMerchantId(), types.Actor_EXTERNAL_MERCHANT)
	switch {
	case storageV2.IsRecordNotFoundError(err):
		// create actor of type external merchant
		if actor, err = s.actorDao.Create(ctx, &types.Actor{
			Type:     types.Actor_EXTERNAL_MERCHANT,
			EntityId: merchantResp.GetMerchantId(),
		}); err != nil {
			return nil, errPkg.Wrap(err, "error creating actor of type external merchant")
		}
		//nocustomlint:goroutine
		go s.PublishActorCreationEvent(epificontext.CloneCtx(ctx), &actorConsumerPb.ActorCreationEvent{ActorId: actor.GetId(), Type: ActorTypeConversionToV2(actor.GetType())})
		return actor, nil
	case err != nil:
		return nil, errPkg.Wrap(err, "error getting actor using entity id of type external merchant")
	}
	return actor, nil
}

// Method to call batch get merchants RPC using merchant ids and enrich entity details for response
func (s *Service) getMerchantEntityDetails(ctx context.Context, merchantIds []string,
	entityDetails []*actorPb.GetEntityDetailsResponse_EntityDetail, idMap map[string]string) ([]*actorPb.GetEntityDetailsResponse_EntityDetail, error) {
	// Call for merchants first and enrich whatever we get for id's which belong to those of external merchant
	merchantResp, err := s.merchantClient.GetMerchants(ctx, &mPb.GetMerchantsRequest{Identifier: &mPb.GetMerchantsRequest_MerchantIdentifier_{
		MerchantIdentifier: &mPb.GetMerchantsRequest_MerchantIdentifier{MerchantIds: merchantIds},
	}})
	// If merchant id's were found for actor ids input and we are unable to get data for some,
	// We treat it as error in our system
	if err := epifigrpc.RPCError(merchantResp, err); err != nil {
		return nil, errPkg.Wrap(err, "error getting merchants using merchant ids")
	}
	// If merchant details are found successfully, build the response object
	for _, merchant := range merchantResp.GetMerchants() {
		entity := &actorPb.GetEntityDetailsResponse_EntityDetail{
			EntityId:        merchant.GetId(),
			ProfileImageUrl: merchant.GetLogoUrl(),
			Name:            (&commontypes.Name{}).Parse(merchant.GetName()),
			ActorId:         idMap[merchant.GetId()],
			EntityType:      types.ActorType_EXTERNAL_MERCHANT,
			Entity: &actorPb.GetEntityDetailsResponse_EntityDetail_Merchant_{
				Merchant: &actorPb.GetEntityDetailsResponse_EntityDetail_Merchant{
					Capabilities: merchant.GetCapabilities(),
				},
			},
		}
		entityDetails = append(entityDetails, entity)
	}
	return entityDetails, nil
}

func (s *Service) getMerchantPis(ctx context.Context, merchantId string, limit int32) ([]string, error) {
	resp, err := s.merchantClient.GetPisByMerchantId(ctx, &mPb.GetPisByMerchantIdRequest{MerchantId: merchantId, Limit: limit})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if rpc.StatusFromErrorWithDefaultInternal(err).IsRecordNotFound() {
			return nil, errPkg.Wrap(epifierrors.ErrRecordNotFound, "no pi's found from merchant service using merchant id")
		}
		return nil, errPkg.Wrap(err, "cannot get pi's from merchant service using merchant id")
	}
	return resp.GetPiIds(), nil
}

// generic handling of payment instruments for both actor from and actor to. This will check if PI is of type merchant or not.
// If PI is of type merchant we call creation of merchant which is an idempotent RPC
// After this an actor is created in the system with type external merchant and actor id mapped to merchant id as entity id.
func (s *Service) resolveMerchant(ctx context.Context, piTo, otherActor string) (pi *piPb.PaymentInstrument, actorTo string, err error, isMerchant bool) {
	getPiByIdResp, piErr := s.piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{Id: piTo})
	if piErr = epifigrpc.RPCError(getPiByIdResp, piErr); piErr != nil {
		return nil, "", errPkg.Wrap(piErr, "cannot get PI by id"), isMerchant
	}
	// Not using card merchant info from response since it is not required, we just need to check
	// whether for this pi id, card merchant info is present in merchant service or not to check if pi
	// belongs to that of merchant
	// Sending OtherActorName from the request to differentiate ATM withdrawal and deposits from rest of the transactions as OtherActorName will have "ATM" for these ATM related transactions only and we will create Merchant with BrandName and LegalName as "ATM"
	legalName, brandName, isMerchantActor, err := s.isMerchantPi(ctx, getPiByIdResp.GetPaymentInstrument(), otherActor)
	if err != nil {
		return nil, "", errPkg.Wrap(err, "cannot determine if pi is of merchant"), isMerchant
	}
	if isMerchantActor {
		isMerchant = true
		// Calls create merchant idempotent RPC and creates a new actor in system with actor id to merchant id mapping
		// In case of card merchants vpa merchant info will be null and hence legal name and brand name will be passed
		// as null, which is okay for card merchants since verified name from PI will be used to create new merchant
		createdActor, createErr := s.createMerchantActor(ctx, legalName, brandName, piTo)
		if createErr != nil {
			return nil, "", errPkg.Wrap(createErr, "cannot create merchant"), isMerchant
		}
		actorTo = createdActor.GetId()
	}
	return getPiByIdResp.GetPaymentInstrument(), actorTo, err, isMerchant
}

// method updates actor ownership from epifi_wealth to epifi_tech
// ownership is not to be updated from epifi_tech to epifi_wealth
func (s *Service) updateActorOwnership(ctx context.Context, actorId string, ownership commontypes.Ownership) error {
	actor, err := s.actorDao.GetById(ctx, actorId)
	if err != nil {
		return fmt.Errorf("error in getting actor by actor-id: %w", err)
	}
	if actor.GetOwnership() == commontypes.Ownership_EPIFI_WEALTH && ownership == commontypes.Ownership_EPIFI_TECH {
		actor.Ownership = commontypes.Ownership_EPIFI_TECH
		updateErr := s.actorDao.UpdateActorOwnershipToTech(ctx, actorId)
		if updateErr != nil {
			return fmt.Errorf("error in updating actor-ownership: %w", err)
		}
	}
	return nil
}

func (s *Service) GetActorCapabilities(ctx context.Context, req *actorPb.GetActorCapabilitiesRequest) (*actorPb.GetActorCapabilitiesResponse, error) {
	res := &actorPb.GetActorCapabilitiesResponse{}
	res.Value = make(map[int32]bool)
	val, ok := s.staticActorCapabilityMap[req.GetActorId()]
	if !ok {
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	}

	res.Value[int32(actorCapPb.Capability_RECEIVE_MONEY)] = val[actorCapPb.Capability_name[int32(actorCapPb.Capability_RECEIVE_MONEY)]]
	res.Value[int32(actorCapPb.Capability_SEND_MONEY)] = val[actorCapPb.Capability_name[int32(actorCapPb.Capability_SEND_MONEY)]]
	res.Status = rpc.StatusOk()

	return res, nil
}

// GetActorsByEntityIds gets the actors for the entity ids
// Note: Since we have limit on payload passed in rpc request and response ( 4MB), we should pass actorIds in limit
func (s *Service) GetActorsByEntityIds(ctx context.Context, req *actorPb.GetActorsByEntityIdsRequest) (*actorPb.GetActorsByEntityIdsResponse, error) {
	var res = &actorPb.GetActorsByEntityIdsResponse{}

	actorRes, err := s.actorDao.GetByEntityIds(ctx, req.GetEntityIds())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no record found for the entityids", zap.Error(err))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "failed to get actors by entityids", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil

	}
	res.Status = rpc.StatusOk()
	res.Actors = actorRes
	return res, nil
}

// BulkFetchActorToPIs will get list of PI Ids for requested list of actors
// There can be multiple PIs for a given actor_from, actor_to mapping
// since actor_to Ids is expected in request, BulkFetchActorToPIs will return a map of actor_to Id to list of PIs associated
func (s *Service) BulkFetchActorToPIs(ctx context.Context, req *actorPb.BulkFetchActorToPIsRequest) (*actorPb.BulkFetchActorToPIsResponse, error) {
	actorPiResolutions, err := s.actorPiResolutionDao.BulkFetchActorToPIs(ctx, req.GetActorFrom(), req.GetActorTo())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &actorPb.BulkFetchActorToPIsResponse{Status: rpc.StatusOk()}, nil
		}
		logger.Error(ctx, "error in getting actor pi resolutions from db", zap.Error(err), zap.Strings(logger.SECONDARY_ACTOR_ID, req.GetActorTo()))
		return &actorPb.BulkFetchActorToPIsResponse{
			Status: rpc.StatusFromError(err),
		}, nil
	}

	actorPIMappings := make(map[string]*protoStruct.ListValue)
	for _, a := range actorPiResolutions {
		if _, present := actorPIMappings[a.GetActorTo()]; !present {
			actorPIMappings[a.GetActorTo()] = &protoStruct.ListValue{Values: make([]*protoStruct.Value, 0)}
		}
		actorPIMappings[a.GetActorTo()].Values = append(actorPIMappings[a.GetActorTo()].GetValues(), &protoStruct.Value{Kind: &protoStruct.Value_StringValue{StringValue: a.GetPiTo()}})
	}

	return &actorPb.BulkFetchActorToPIsResponse{
		Status:         rpc.StatusOk(),
		ActorPiMapping: actorPIMappings,
	}, nil
}

func (s *Service) PublishActorCreationEvent(ctx context.Context, actorCreationEvent *actorConsumerPb.ActorCreationEvent) {

	if _, publishErr := s.actorCreationEventPublisher.Publish(ctx, actorCreationEvent); publishErr != nil {
		logger.Error(ctx, "error publishing actor creation event", zap.Error(publishErr), zap.String(logger.ACTOR_ID_V2, actorCreationEvent.GetActorId()))
		return
	}
	logger.Debug(ctx, "successfully published an actor creation event", zap.String(logger.ACTOR_ID_V2, actorCreationEvent.GetActorId()),
		zap.String(logger.ACTOR_TYPE, actorCreationEvent.GetType().String()))
}

// ActorTypeConversionToV2 as types.Actor_Type is deprecated
func ActorTypeConversionToV2(actorType types.Actor_Type) types.ActorType {
	switch actorType {
	case types.Actor_WAITLISTED_USER:
		return types.ActorType_WAITLISTED_USER
	case types.Actor_EXTERNAL_MERCHANT:
		return types.ActorType_EXTERNAL_MERCHANT
	case types.Actor_EXTERNAL_USER:
		return types.ActorType_EXTERNAL_USER
	case types.Actor_USER:
		return types.ActorType_USER
	case types.Actor_MERCHANT:
		return types.ActorType_MERCHANT
	default:
		return types.ActorType_TYPE_UNSPECIFIED
	}
}

func (s *Service) ResolveOtherActorPiAndTimeline(ctx context.Context, req *actorPb.ResolveOtherActorPiAndTimelineRequest) (
	*actorPb.ResolveOtherActorPiAndTimelineResponse, error) {
	var (
		otherPiId      = pay.GenericPiId
		otherActorName = pay.DefaultActorName
		err            error
		res            = &actorPb.ResolveOtherActorPiAndTimelineResponse{}
		timeline       *timelinePb.Timeline
		accountNumber  string
	)
	if req.GetTransactionCategory() == oldtypes.TransactionCategory_ATM_WITHDRAWAL || req.GetTransactionCategory() == oldtypes.TransactionCategory_ATM_CASH_DEPOSIT {
		req.PiIdentifier = &actorPb.ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier_{
			CardTransactionIdentifier: &actorPb.ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier{
				Mid:            req.GetMerchantDetails().GetMid(),
				OtherActorName: req.GetOtherActorName(),
			}}
	}
	switch req.GetPiIdentifier().(type) {
	case *actorPb.ResolveOtherActorPiAndTimelineRequest_Vpa:
		otherPiId, otherActorName, err = s.getUpiPiDetails(ctx, &otherActorVpaInfo{
			vpa:       req.GetVpa(),
			ownership: req.GetPiOwnership(),
		}, req.GetPrimaryActorId(), req.GetOtherActorName())
		switch {
		case errors.Is(err, PSPNotRegisterError) || errors.Is(err, InvalidVPAError):
			name := upiPkg.TruncatePspHandle(req.GetVpa())
			if name == "" {
				name = pay.DefaultActorName
			}

			otherPiId, err = s.createUpiPi(
				ctx,
				name,
				req.GetVpa(),
				"",
				nil,
				piPb.Ownership_EPIFI_TECH,
				false,
			)

			if err != nil {
				otherPiId = pay.GenericPiId
			} else {
				otherActorName = name
			}
		case err != nil:
			// error while validating the vpa
			logger.Error(ctx, "error fetching upi pi details", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	case *actorPb.ResolveOtherActorPiAndTimelineRequest_Account:
		accountNumber, otherActorName, err = s.sanitizeAccountNumberAndOtherActorName(ctx, req)
		if err != nil {
			logger.Error(ctx, "error while sanitizing account number and other actor name for ecs enach mandate", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

		if accountNumber == "" {
			accountNumber = req.GetAccount().GetAccountNumber()
		}

		if otherActorName == "" {
			otherActorName = req.GetOtherActorName()
		}

		otherActorAccountInfo := &otherActorAccountInfo{
			accountNumber: accountNumber,
			ifscCode:      req.GetAccount().GetIfscCode(),
			accountType:   req.GetAccount().GetAccountType(),
			piType:        req.GetAccount().GetPiType(),
			ownership:     req.GetPiOwnership(),
		}
		otherPiId, otherActorName, err = s.getAccountPiDetails(ctx, otherActorAccountInfo, otherActorName)
		if err != nil {
			logger.Error(ctx, "error while creating other actor account pi", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

		// create the link between the merchant and the pi of other actor
		err = s.createMerchant(ctx, req, otherActorName, otherPiId)
		if err != nil {
			logger.Error(ctx, "failed to create merchant", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

	case *actorPb.ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier_:
		// populating the account info since in the case of card transactions , we create other actor pi of accounts type .
		cardMerchantAccountInfo, cardMerchantAccountInfoErr := s.createCardMerchantAccountInfo(req.GetCardTransactionIdentifier().GetMid(), req.GetCardTransactionIdentifier().GetOtherActorName(), req.GetAtmAddress(), req.GetAccountType(), req.GetMerchantDetails())
		if cardMerchantAccountInfoErr != nil {
			logger.Error(ctx, "failed to create pi for other actor", zap.Error(cardMerchantAccountInfoErr))
			res.Status = rpc.StatusInvalidArgument()
			return res, nil
		}
		cardMerchantAccountInfo.ownership = req.GetPiOwnership()
		otherPiId, otherActorName, err = s.getAccountPiDetails(ctx, cardMerchantAccountInfo, req.GetOtherActorName())
		if err != nil {
			logger.Error(ctx, "error while creating other actor account pi", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		createCardMerchantInfoerr := s.createCardMerchant(ctx, cardMerchantAccountInfo, req.GetMerchantDetails(), req.GetPaymentProtocol(), req.GetTransactionCategory(), otherPiId, otherActorName)
		if createCardMerchantInfoerr != nil {
			logger.Error(ctx, "error while creating card merchant for pi id: ", zap.String(logger.PI_ID, otherPiId), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

	case *actorPb.ResolveOtherActorPiAndTimelineRequest_PiId:
		otherPiId, otherActorName, err = s.getPiById(ctx, req.GetPiId())
		if err != nil {
			logger.Error(ctx, "error while fetching pi of other actor from pi id", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}
	// fetching primary actor for having the actor name
	getEntityDetailsByActorIdRes, getEntityDetailsByActorIdErr := s.usersClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: req.GetPrimaryActorId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(getEntityDetailsByActorIdRes, getEntityDetailsByActorIdErr); rpcErr != nil {
		logger.Error(ctx, "GetUser rpc failed", zap.String(logger.ACTOR_ID_V2, req.GetPrimaryActorId()), zap.Error(rpcErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	primaryActorName := gammanames.BestNameFromProfile(ctx, getEntityDetailsByActorIdRes.GetUser().GetProfile()).ToString()
	timeline, err = s.createOrResolveTimeline(ctx, req.GetEventType(), req.GetTimelineOwnership(), req.GetPrimaryActorId(), otherPiId, primaryActorName, otherActorName, req.GetTimelineResolutionSource())
	if err != nil {
		logger.Error(ctx, "failed to resolve timeline for the given primary actor", zap.String(logger.ACTOR_ID_V2, req.GetPrimaryActorId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		err = s.checkAndCreateForProbableKnownMerchant(ctx, otherPiId)
		if err != nil {
			logger.Debug(ctx, "error in checking or creating Probable Known merchant", zap.Error(err))
		}
	})

	res.OtherActorPiId = otherPiId
	res.TimelineId = timeline.GetId()
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) checkAndCreateForProbableKnownMerchant(ctx context.Context, otherPiId string) error {
	merchantPiDetails, err := s.merchantClient.GetMerchantPiEntitiesByPiIds(ctx, &mPb.GetMerchantPiEntitiesByPiIdsRequest{PiIds: []string{otherPiId}})

	if merchantPiDetails.GetStatus().IsRecordNotFound() {
		return nil
	}

	if rpcErr := epifigrpc.RPCError(merchantPiDetails, err); rpcErr != nil {
		logger.Error(ctx, "error in getting GetMerchantPiEntitiesByPiIds", zap.Error(rpcErr))
		return rpcErr
	}

	piToOldAndNewMerchantIdMap := merchantPiDetails.GetPiToOldAndMerchantIds()
	for piId, oldAndNewMerchantId := range piToOldAndNewMerchantIdMap {
		if strings.TrimSpace(oldAndNewMerchantId.GetOldMerchantId()) == "" {
			piRes, err := s.piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{Id: piId})
			if rpcErr := epifigrpc.RPCError(piRes, err); rpcErr != nil {
				logger.Error(ctx, "error in getting Pi details for Pi", zap.String(logger.PI_ID, piId), zap.Error(rpcErr))
				return rpcErr
			}

			dsMerchantId, dsMerchantIdErr := s.getDsMerchantIdFromMerchantResolutionApi(ctx, piRes.GetPaymentInstrument().GetName())
			if rpc.StatusFromError(dsMerchantIdErr).IsRecordNotFound() {
				return nil
			}
			if dsMerchantIdErr != nil {
				logger.Error(ctx, "error in getting response for merchant resolution Api", zap.Error(dsMerchantIdErr))
				return dsMerchantIdErr
			}

			createRes, createResErr := s.merchantClient.CreateProbableKnownMerchant(ctx, &mPb.CreateProbableKnownMerchantRequest{
				MerchantId:   oldAndNewMerchantId.GetMerchantId(),
				PiId:         piId,
				DsMerchantId: dsMerchantId,
			})
			if rpcErr := epifigrpc.RPCError(createRes, createResErr); rpcErr != nil {
				logger.Error(ctx, "error in getting response for merchant resolution Api", zap.String(logger.PI_ID, piId), zap.Error(rpcErr))
				return rpcErr
			}
		}
	}

	return nil
}

func (s *Service) getDsMerchantIdFromMerchantResolutionApi(ctx context.Context, name string) (string, error) {
	req := &merchantResolutionPb.MerchantResolutionRequest{
		Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
		RawMerchantName: name,
		IsMerchantTxn:   true,
		IsFiTxn:         true,
	}

	var res *merchantResolutionPb.MerchantResolutionResponse
	var err error

	res, err = s.merchantResolutionPkg.MerchantResolution(ctx, req)

	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return "", rpcErr
	}

	return res.GetDsMerchantId(), nil
}

// createBeneficiary will create entry in beneficiary table
// it will first check if the entry is present in the table or not if not present
// then create entry in the entry.
func (s *Service) createBeneficiary(ctx context.Context, actorFrom, piTo string) error {
	getBeneficiaryRes, getBeneficiaryResErr := s.beneficiaryManagementClient.GetBeneficiary(ctx, &beneficiaryManagementPb.GetBeneficiaryRequest{
		ActorFrom: actorFrom,
		PiTo:      piTo,
		FieldMask: []beneficiaryManagementPb.BeneficiaryFieldMask{beneficiaryManagementPb.BeneficiaryFieldMask_ID},
	})

	if getBeneficiaryRes.GetStatus().IsSuccess() {
		logger.Debug(ctx, "Get beneficiary successfully")
		return nil
	}

	if te := epifigrpc.RPCError(getBeneficiaryRes, getBeneficiaryResErr); !getBeneficiaryRes.GetStatus().IsRecordNotFound() && te != nil {
		return fmt.Errorf("error in getting data for beneficiary: %s, %v", piTo, te)
	}

	createBeneficiaryRes, createBeneficiaryErr := s.beneficiaryManagementClient.CreateBeneficiary(ctx, &beneficiaryManagementPb.CreateBeneficiaryRequest{
		ActorFrom:         actorFrom,
		PiTo:              piTo,
		CooldownStartTime: timestamp.New(time.Now()),
		CooldownEndTime:   timestamp.New(time.Now().Add(s.conf.BeneficiaryCoolDownPeriod)),
	})
	switch {
	case createBeneficiaryErr != nil:
		return fmt.Errorf("error in creating beneficiary for actorFrom: %s piTo: %s, %w", actorFrom, piTo, createBeneficiaryErr)
	case createBeneficiaryRes.GetStatus().IsAlreadyExists():
		return nil
	case !createBeneficiaryRes.GetStatus().IsSuccess():
		return fmt.Errorf("error in creating beneficiary for actorFrom: %s piTo: %s, %s", actorFrom, piTo, createBeneficiaryRes.GetStatus().GetShortMessage())
	}
	return nil
}

// sanitizeAccountNumberAndOtherActorName is useful to fetch the other actor name from the request
// each type of the request can extend this function to add their custom logic for parsing the other actor name
func (s *Service) sanitizeAccountNumberAndOtherActorName(ctx context.Context, resolveOtherActorPiAndTimelineRequest *actorPb.ResolveOtherActorPiAndTimelineRequest) (
	string, string, error) {
	var (
		accountNumber  string
		otherActorName string
		err            error
	)
	otherActorName, err = s.fetchSanatizedOtherActorName(ctx, resolveOtherActorPiAndTimelineRequest.GetEcsEnachDetails(),
		resolveOtherActorPiAndTimelineRequest.GetOtherActorName())
	if err != nil {
		return "", "", err
	}

	accountNumber = populateAccountNumber(resolveOtherActorPiAndTimelineRequest)

	return sanitizeAccountNumber(accountNumber), otherActorName, nil
}

// fetchSanatizedOtherActorName fetches the sanatized name from the raw name parsed from the particulars
func (s *Service) fetchSanatizedOtherActorName(ctx context.Context, ecsEnachDetails *notification.EcsEnachMandateDetails, otherActorName string) (string, error) {
	if ecsEnachDetails.GetMandateCategory() == notification.EcsEnachMandateDetails_ENACHE {
		var merchantResolutionRes *merchantResolutionPb.MerchantResolutionResponse
		var merchantResolutionErr error

		req := &merchantResolutionPb.MerchantResolutionRequest{
			Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
			RawMerchantName: otherActorName,
			IsFiTxn:         true,
			IsMerchantTxn:   true,
		}

		merchantResolutionRes, merchantResolutionErr = s.merchantResolutionPkg.MerchantResolution(ctx, req)

		if err := epifigrpc.RPCError(merchantResolutionRes, merchantResolutionErr); err != nil && !merchantResolutionRes.GetStatus().IsRecordNotFound() {
			return "", fmt.Errorf("failed to fetch the sanatized merchant name %v %w", err, epifierrors.ErrTransient)
		}
		return merchantResolutionRes.GetMerchantName(), nil
	}
	return "", nil
}

// populateAccountNumber populates the account in the cases where we do not get the account number of the other actor and we need to create account type pi
// In such cases we create the account type PI by creating fuzzy account number
// some cases are (but not limited to):
// 1. ATM related transactions
// 2. Off-App Enach transactions
func populateAccountNumber(request *actorPb.ResolveOtherActorPiAndTimelineRequest) string {
	var accountNumber string
	switch {
	case request.GetTransactionCategory() == oldtypes.TransactionCategory_ATM_WITHDRAWAL || request.GetTransactionCategory() == oldtypes.TransactionCategory_ATM_CASH_DEPOSIT:
		accountNumber = merchant.CreateUniqueAccountNumber(request.GetMerchantDetails().GetMid(), request.GetMerchantDetails().GetTid(),
			request.GetAtmAddress(), request.GetMerchantDetails().GetCity(), request.GetMerchantDetails().GetCountryCode())
	case request.GetEcsEnachDetails().GetMandateCategory() == notification.EcsEnachMandateDetails_ENACHE:
		accountNumber = merchant.CreateUniqueAccountNumber(request.GetOtherActorName())
	}
	return accountNumber
}

// SanitizeAccountNumber : removes all the non-alphanumeric character
// NOTE: we have seen cases where we get special characters like '-' in the inbound notification, so use this function for sanatization
func sanitizeAccountNumber(accountNumber string) string {
	if accountNumber == "" {
		return accountNumber
	}
	// regular expression to match any non-alphanumeric character
	reg := regexp.MustCompile("[^a-zA-Z0-9]+")
	// Replace non-alphanumeric characters with an empty string
	sanatizedAccountNumber := reg.ReplaceAllString(accountNumber, "")
	return sanatizedAccountNumber
}

// createMerchant is wrapper function for creating the link between the PI and the merchant   it is called for specific use cases like (but not limited to):
// 1. Off-app Enach Transactions
// 2. Atm and debit card related transactions
func (s *Service) createMerchant(ctx context.Context, resolveOtherActorPiAndTimelineReq *actorPb.ResolveOtherActorPiAndTimelineRequest,
	otherActorName, otherPiId string) error {
	switch {
	// enach transactions
	case resolveOtherActorPiAndTimelineReq.GetEcsEnachDetails().GetMandateCategory() == notification.EcsEnachMandateDetails_ENACHE:
		creteMerchantResp, createMerchantErrr := s.merchantClient.CreateMerchant(ctx, &mPb.CreateMerchantRequest{
			LegalName: otherActorName,
			BrandName: otherActorName,
			LogoUrl:   "",
			PiId:      otherPiId,
		})
		if err := epifigrpc.RPCError(creteMerchantResp, createMerchantErrr); err != nil {
			return fmt.Errorf("failed to create the merchant %v %w", err, epifierrors.ErrTransient)
		}
		return nil
	default:
		return nil
	}
}

// CreateUpiPi createPi creates Upi Pi fo the given vpa and name
// if isPartial is set to true will create a Pi of type PARTIAL_UPI else will create a Pi of type UPI
func (s *Service) createUpiPi(
	ctx context.Context,
	name, vpa, mcc string,
	md *upiPb.MerchantDetails,
	ownership piPb.Ownership,
	isVpaVerified bool,
) (string, error) {

	createPiRequest := &piPb.CreatePiRequest{
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.CreatePiRequest_Upi{
			Upi: &piPb.Upi{
				Vpa:             vpa,
				MerchantDetails: upiPb.ConvertToPIMerchantDetails(md, mcc),
			},
		},
		VerifiedName: name,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  false,
			piPb.Capability_OUTBOUND_TXN.String(): false,
		},
		Ownership: ownership,
	}
	if !isVpaVerified {
		createPiRequest.State = piPb.PaymentInstrumentState_CREATED
	}

	res, err := s.piClient.CreatePi(ctx, createPiRequest)
	switch {
	case err != nil:
		return "", fmt.Errorf("error creating PI for VPA: %s", vpa)
	case !res.GetStatus().IsSuccess():
		return "", fmt.Errorf("CreatePi() rpc failed wiht status: %s", res.GetStatus())
	}
	return res.GetPaymentInstrument().GetId(), nil
}

func (s *Service) getTspUserEntityDetails(ctx context.Context, actors []*types.Actor) ([]*actorPb.GetEntityDetailsResponse_EntityDetail, error) {
	var (
		entityDetails  []*actorPb.GetEntityDetailsResponse_EntityDetail
		tspDetailsChan = make(chan *actorPb.GetEntityDetailsResponse_EntityDetail, len(actors))
	)

	errGroup, errGroupCtx := errgroup.WithContext(ctx)
	for _, actor := range actors {
		actorDetails := actor
		errGroup.Go(func() error {
			owner, err := owner.GetOwnerFromOwnership(actorDetails.GetOwnership())
			if err != nil {
				return fmt.Errorf("failed to get ownership from owner: %w", err)
			}
			tspUserResp, err := s.tspUserClient.GetTspUser(errGroupCtx, &tspUserPb.GetTspUserRequest{
				Owner: owner,
				Identifier: &tspUserPb.GetTspUserRequest_TspUserId{
					TspUserId: actorDetails.GetEntityId(),
				},
			})
			if te := epifigrpc.RPCError(tspUserResp, err); te != nil {
				return fmt.Errorf("failed to get tsp user details: %w", te)
			}

			entity := &actorPb.GetEntityDetailsResponse_EntityDetail{
				EntityId:     tspUserResp.GetTspUser().GetId(),
				Name:         tspUserResp.GetTspUser().GetPersonalDetails().GetName(),
				MobileNumber: tspUserResp.GetTspUser().GetPersonalDetails().GetPhoneNumber(),
				EmailId:      tspUserResp.GetTspUser().GetPersonalDetails().GetEmail(),
				ActorId:      actorDetails.GetId(),
				EntityType:   types.ActorType_TSP_USER,
			}

			tspDetailsChan <- entity
			return nil
		})
	}

	err := errGroup.Wait()
	if err != nil {
		return nil, fmt.Errorf("failed to get entity details: %w", err)
	}

	for len(tspDetailsChan) > 0 {
		entityDetails = append(entityDetails, <-tspDetailsChan)
	}
	return entityDetails, nil
}
