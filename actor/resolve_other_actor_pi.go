package actor

import (
	"context"
	"errors"
	"fmt"
	"strings"

	oldtypes "github.com/epifi/gamma/api/types"

	types "github.com/epifi/gamma/api/typesv2"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	mPb "github.com/epifi/gamma/api/merchant"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	upiPb "github.com/epifi/gamma/api/upi"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/gamma/pkg/merchant"
	"github.com/epifi/gamma/pkg/pay"
)

type otherActorVpaInfo struct {
	vpa       string
	ownership piPb.Ownership
}
type otherActorAccountInfo struct {
	accountNumber string
	ifscCode      string
	piType        piPb.PaymentInstrumentType
	accountType   accounts.Type
	ownership     piPb.Ownership
}

// helper method to resolve upi pi
func (s *Service) getUpiPiDetails(ctx context.Context, vpaDetails *otherActorVpaInfo, primaryActorId, otherActorNameFromNotification string) (string, string, error) {
	var (
		otherActorName string
		otherPiId      string
	)
	// check if pi already exists
	pi, err := s.getPiByVPA(ctx, vpaDetails.vpa)
	switch {
	// in case of record not found verify the vpa and create the upi pi
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no pi found for vpa", zap.String(logger.VPA, vpaDetails.vpa))
		var cancel func()
		verifyVPACtx, cancel := context.WithTimeout(epificontext.CloneCtx(ctx), s.conf.VerifyVpaDeadlineForInboundNotification)
		defer cancel()
		// verify vpa and create pi
		otherPiId, otherActorName, err = s.verifyVPAAndCreatePi(verifyVPACtx, vpaDetails.ownership, vpaDetails.vpa, primaryActorId)

		switch {
		case errors.Is(err, InvalidVPAError):
			return "", "", InvalidVPAError
		case err != nil:
			return "", "", fmt.Errorf("error while verifying vpa: %s, err: %w", vpaDetails.vpa, err)
		default:
			return otherPiId, otherActorName, nil
		}
	case err != nil:
		logger.Info(ctx, "error fetching pi details for vpa", zap.Error(err))
		return "", "", fmt.Errorf("error fetching pi details for vpa: %s err: %w", vpaDetails.vpa, err)
	}
	otherPiId = pi.GetId()
	otherActorName = pay.DefaultActorName
	verifiedName := strings.TrimSpace(pi.GetVerifiedName())
	otherActorNameFromNotification = strings.TrimSpace(otherActorNameFromNotification)
	if verifiedName != "" {
		otherActorName = verifiedName
	} else if otherActorNameFromNotification != "" {
		otherActorName = otherActorNameFromNotification
	}
	return otherPiId, otherActorName, nil
}

// helper method to get pi by vpa id
func (s *Service) getPiByVPA(ctx context.Context, vpa string) (*piPb.PaymentInstrument, error) {
	res, err := s.piClient.GetPi(ctx, &piPb.GetPiRequest{
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.GetPiRequest_UpiRequestParams_{
			UpiRequestParams: &piPb.GetPiRequest_UpiRequestParams{
				Vpa: vpa,
			},
		},
	})
	switch err = epifigrpc.RPCError(res, err); {
	case rpc.StatusFromError(err).IsRecordNotFound():
		return nil, epifierrors.ErrRecordNotFound
	case err != nil:
		return nil, fmt.Errorf("failed to fetch pi for vpa: %s: %w", vpa, err)
	}
	return res.GetPaymentInstrument(), nil
}

// helper method to verify vpa and create corresponding pi
func (s *Service) verifyVPAAndCreatePi(ctx context.Context, ownership piPb.Ownership, vpa, primaryActorId string) (string, string, error) {
	deviceDetailsRes, deviceDetailsErr := s.authClient.GetDeviceAuth(ctx, &authPb.GetDeviceAuthRequest{
		ActorId: primaryActorId,
	})

	if rpcError := epifigrpc.RPCError(deviceDetailsRes, deviceDetailsErr); rpcError != nil {
		return "", "", fmt.Errorf("GetDeviceAuth rpc failed: %w", rpcError)
	}

	entityDetailsRes, entityDetailsErr := s.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{
		ActorId: primaryActorId,
	})

	if rpcError := epifigrpc.RPCError(entityDetailsRes, entityDetailsErr); rpcError != nil {
		return "", "", fmt.Errorf("GetEntityDetailsByActorId rpc failed: %w", rpcError)
	}

	verifyVPARes, err := s.upiClient.ValidateAddressAndCreatePi(ctx, &upiPb.ValidateAddressAndCreatePiRequest{
		Device:       upiPb.FromFeDevice(ctx, deviceDetailsRes.Device, entityDetailsRes.GetMobileNumber()),
		UpiVpa:       vpa,
		PayerActorId: primaryActorId,
		Ownership:    ownership,
	})
	switch {
	case err != nil:
		return "", "", fmt.Errorf("error verifying vpa: %s, err: %w", vpa, err)
	case verifyVPARes.GetStatus().GetCode() == uint32(upiPb.ValidateAddressAndCreatePiResponse_INVALID_VPA) ||
		verifyVPARes.GetStatus().GetCode() == uint32(upiPb.ValidateAddressAndCreatePiResponse_VPA_RESTRICTED):
		return "", "", InvalidVPAError
	case verifyVPARes.GetStatus().GetCode() == uint32(upiPb.ValidateAddressAndCreatePiResponse_PSP_NOT_REGISTERED):
		return "", "", PSPNotRegisterError
	case !verifyVPARes.GetStatus().IsSuccess():
		return "", "", fmt.Errorf("VerifyPayeeVPA() rpc returned non-ok status %s", verifyVPARes.GetStatus())
	}

	return verifyVPARes.GetPiId(), verifyVPARes.GetCustomerName(), nil
}

// helper method to fetch account pi details
func (s *Service) getAccountPiDetails(ctx context.Context, accountDetails *otherActorAccountInfo, otherActorNameFromNotification string) (string, string, error) {
	var (
		otherActorName = pay.DefaultActorName
	)
	if otherActorNameFromNotification != "" {
		otherActorName = otherActorNameFromNotification
	}

	otherActorPi, err := s.createAccountPi(ctx, accountDetails, otherActorName)
	if err != nil {
		return "", "", err
	}
	return otherActorPi.GetId(), otherActorName, nil
}

// helper method to create account type pi
func (s *Service) createAccountPi(ctx context.Context, accountDetails *otherActorAccountInfo, otherActorName string) (*piPb.PaymentInstrument, error) {
	createPiRequest := &piPb.CreatePiRequest{
		Type:         accountDetails.piType,
		VerifiedName: otherActorName,
		Identifier: &piPb.CreatePiRequest_Account{
			Account: &piPb.Account{
				ActualAccountNumber: accountDetails.accountNumber,
				IfscCode:            accountDetails.ifscCode,
				AccountType:         accountDetails.accountType,
				Name:                otherActorName,
				SecureAccountNumber: mask.MaskLastNDigits(accountDetails.accountNumber, 3, ""),
			},
		},
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): true,
		},
		Ownership:            accountDetails.ownership,
		IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
	}

	if accountDetails.piType != piPb.PaymentInstrumentType_BANK_ACCOUNT {
		createPiRequest.Capabilities[piPb.Capability_INBOUND_TXN.String()] = false
		createPiRequest.Capabilities[piPb.Capability_OUTBOUND_TXN.String()] = false
	}

	res, err := s.piClient.CreatePi(ctx, createPiRequest)
	switch {
	case res.GetStatus().IsInvalidArgument():
		return nil, fmt.Errorf("invalid argument while creating pi")
	case !res.GetStatus().IsSuccess() && !res.GetStatus().IsAlreadyExists():
		return nil, fmt.Errorf("CreatePi() rpc failed with status: %s", res.GetStatus())
	case err != nil:
		return nil, fmt.Errorf("error while creating pi")
	}
	return res.GetPaymentInstrument(), nil
}

// helper method to create card merchant for specific transaction categories
func (s *Service) createCardMerchant(ctx context.Context, accountDetails *otherActorAccountInfo, merchantDetails *actorPb.MerchantDetails,
	paymentProtocol paymentPb.PaymentProtocol, transactionCategory oldtypes.TransactionCategory, otherActorPiId, otherActorName string) error {
	// pi identifier is of type generic account return gracefully since card merchant info only
	// needs to be created in case pi is identified with hashed mid and name
	if piIdentifierIsOfGenericAccount(accountDetails) {
		return nil
	}
	// If this is a card transaction of type POS, E-COMM, ATM DEPOSIT, EMI or ATM WITHDRAWAL, we need to create card merchant infos in merchant service
	desiredCategories := []oldtypes.TransactionCategory{
		oldtypes.TransactionCategory_POS,
		oldtypes.TransactionCategory_ECOMM,
		oldtypes.TransactionCategory_ATM_WITHDRAWAL,
		oldtypes.TransactionCategory_ATM_CASH_DEPOSIT,
		oldtypes.TransactionCategory_EMI,
	}
	if paymentProtocol == paymentPb.PaymentProtocol_CARD &&
		(lo.Contains(desiredCategories, transactionCategory) || merchantDetails.GetMid() != "") {
		// in case mid is empty and other actor is resolved using name
		if merchantDetails.GetMid() == "" {
			merchantDetails.Mid = pay.DefaultMIDForCardTxns
		}
		createCardMerchantResp, createCardMerchErr := s.merchantClient.CreateCardMerchantInfo(ctx, &mPb.CreateCardMerchantInfoRequest{
			Name: otherActorName,
			PiId: otherActorPiId,
			Mcc:  merchantDetails.GetMccCode(),
			Mid:  merchantDetails.GetMid(),
			Address: &types.PostalAddress{
				RegionCode:         merchantDetails.GetCountryCode(),
				AdministrativeArea: merchantDetails.GetCity(),
			},
		})
		if createCardMerchErr = epifigrpc.RPCError(createCardMerchantResp, createCardMerchErr); createCardMerchErr != nil {
			return createCardMerchErr
		}
	}
	return nil
}

func (s *Service) getPiById(ctx context.Context, piId string) (string, string, error) {
	getPiRes, getPiErr := s.piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{
		Id: piId,
	})
	if rpcError := epifigrpc.RPCError(getPiRes, getPiErr); rpcError != nil {
		return "", "", fmt.Errorf("GetPiById rpc failed: %w", rpcError)
	}
	return getPiRes.GetPaymentInstrument().GetId(), getPiRes.GetPaymentInstrument().GetName(), nil
}

func piIdentifierIsOfGenericAccount(accountDetails *otherActorAccountInfo) bool {
	if accountDetails.accountNumber == pay.GenericAccount().Account.GetAccountNumber() &&
		accountDetails.ifscCode == pay.GenericAccount().Account.GetIfscCode() &&
		accountDetails.accountType == pay.GenericAccount().Account.GetAccountType() &&
		accountDetails.piType == pay.GenericAccount().Account.GetPiType() {
		return true
	}
	return false
}

// in case of card payments (credit/debit card), we resolve the other actor pi as account pi and use hashed mid/actor_name as account number
func (s *Service) createCardMerchantAccountInfo(mid, actorName, atmAddress string, accountType accounts.Type, merchantDetails *actorPb.MerchantDetails) (*otherActorAccountInfo, error) {
	var (
		res             = &otherActorAccountInfo{}
		hashedAccountId string
	)
	// As in case of credit card they need to resolve to same merchant as there is use case
	// of rewards. So we are calculating hashedAccountId on the basis on name only
	// because they use merchant resolution api to get the clean name for merchant
	switch {
	// For ATM deposit or withdrawal transactions we are creating unique Account number using combination of mid, tid, atm address, city, country code
	case actorName == pay.DefaultAtmActorName:
		hashedAccountId = merchant.CreateUniqueAccountNumber(mid, merchantDetails.GetTid(), atmAddress, merchantDetails.GetCity(), merchantDetails.GetCountryCode())
	case mid != "" && accountType != accounts.Type_CREDIT_CARD_ACCOUNT:
		hashedAccountId = crypto.GetSHA1InBase32(mid)
	case actorName != "":
		hashedAccountId = crypto.GetSHA1InBase32(actorName)
	default:
		return nil, fmt.Errorf("mid and actor name both cant be empty in card payments")

	}
	res.accountNumber = hashedAccountId
	res.ifscCode = pay.DefaultIfscCardTxn
	res.piType = piPb.PaymentInstrumentType_GENERIC
	res.accountType = accounts.Type_SAVINGS
	return res, nil
}
