package actor_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/waitgroup"

	tspUserPb "github.com/epifi/gamma/api/tspuser"
	mocks2 "github.com/epifi/gamma/api/tspuser/mocks"
	oldtypes "github.com/epifi/gamma/api/types"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"flag"
	"fmt"
	"os"
	"reflect"
	"sync"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/testing/protocmp"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/crypto"
	timeMocks "github.com/epifi/be-common/pkg/datetime/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/queue/mocks"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"

	"github.com/epifi/gamma/actor"
	"github.com/epifi/gamma/actor/config"
	"github.com/epifi/gamma/actor/config/genconf"
	"github.com/epifi/gamma/actor/dao"
	actorDaoMocks "github.com/epifi/gamma/actor/dao/mocks"
	"github.com/epifi/gamma/actor/test"
	accountsPb "github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	mPb "github.com/epifi/gamma/api/merchant"
	merchantMocks "github.com/epifi/gamma/api/merchant/mocks"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/order/payment/notification"
	beneficiaryManagementPb "github.com/epifi/gamma/api/pay/beneficiarymanagement"
	beneficiaryManagementMock "github.com/epifi/gamma/api/pay/beneficiarymanagement/mocks"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	timelinePb "github.com/epifi/gamma/api/timeline"
	timelineMocks "github.com/epifi/gamma/api/timeline/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	upiPb "github.com/epifi/gamma/api/upi"
	mock_upi "github.com/epifi/gamma/api/upi/mocks"
	userPb "github.com/epifi/gamma/api/user"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	usersMocks "github.com/epifi/gamma/api/user/mocks"
	merchantResolutionVg "github.com/epifi/gamma/api/vendorgateway/merchantresolution"
	merchantResolutionMock "github.com/epifi/gamma/api/vendorgateway/merchantresolution/mocks"
	"github.com/epifi/gamma/pkg/merchant"
	"github.com/epifi/gamma/pkg/pay"
	testMocks "github.com/epifi/gamma/testing/mocks"
)

var (
	db                   *gormv2.DB
	conf                 *config.Config
	dynConf              *genconf.Config
	actorService         *actor.Service
	actorDao             dao.ActorDao
	actorPiResolutionDao dao.ActorPiResolutionDao
)

var (
	fixtureUserActor1Id         = "actor-user-1"
	fixtureExternalUserActor1Id = "actor-ex-user-1"
	fixtureUserActor3Id         = "actor-user-3"
	fixtureActor2Id             = "actor-ex-user-2"
	fixtureActorId3             = "actor-user-3"
	fixtureExternalMerchanl1Id  = "actor-ex-merchant-1"
	fixtureUserEntityId         = "Test-User-1"
	fixturePi3Id                = "pi-ex-3"
	fixturePi2Id                = "pi-2"
	fixturePi1                  = &piPb.PaymentInstrument{
		Id:   "pi-1",
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2939",
				IfscCode:            "FDRL0001001",
				AccountType:         accountsPb.Type_SAVINGS,
			},
		},
		VerifiedName:         "Kunal",
		State:                piPb.PaymentInstrumentState_CREATED,
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
	}
	fixturePi3 = &piPb.PaymentInstrument{
		Id:   "pi-ex-3",
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2941",
				IfscCode:            "ICIC0001",
				AccountType:         accountsPb.Type_SAVINGS,
			},
		},
		VerifiedName:         "External-3",
		State:                piPb.PaymentInstrumentState_CREATED,
		IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
	}
	fixtureMerchantPi1 = &piPb.PaymentInstrument{
		Id:   "pi-3",
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{
			Vpa: "nitesh@fede",
		}},
		State:                piPb.PaymentInstrumentState_CREATED,
		IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
	}
	fixtureMerchantPi2 = &piPb.PaymentInstrument{
		Id:                   "pi-3",
		Type:                 piPb.PaymentInstrumentType_GENERIC,
		State:                piPb.PaymentInstrumentState_CREATED,
		IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
	}
	fixturePi4 = &piPb.PaymentInstrument{
		Id:   "pi-ex-3",
		Type: piPb.PaymentInstrumentType_GENERIC,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2941",
				IfscCode:            "ICIC0001",
				AccountType:         accountsPb.Type_SAVINGS,
			},
		},
		VerifiedName:         "External-3",
		State:                piPb.PaymentInstrumentState_CREATED,
		IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
	}

	affectedTestTables = []string{"actors", "actor_pi_resolutions"}

	PiToOldAndMerchantIds = map[string]*mPb.GetMerchantPiEntitiesByPiIdsResponse_OldAndNewMerchantId{
		"upi-pi-id": {MerchantId: "m1", OldMerchantId: "m2"},
	}
)

type CreateBeneficiaryArgMatcher struct {
	want *beneficiaryManagementPb.CreateBeneficiaryRequest
}

func newCreateBeneficiaryArgMatcher(want *beneficiaryManagementPb.CreateBeneficiaryRequest) *CreateBeneficiaryArgMatcher {
	return &CreateBeneficiaryArgMatcher{want: want}
}

func (c *CreateBeneficiaryArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*beneficiaryManagementPb.CreateBeneficiaryRequest)
	if !ok {
		return false
	}

	c.want.CooldownStartTime = got.CooldownStartTime
	c.want.CooldownEndTime = got.CooldownEndTime
	return cmp.Diff(got, c.want, protocmp.Transform()) == ""
}

func (c *CreateBeneficiaryArgMatcher) String() string {
	return fmt.Sprintf("want: %v", c.want)
}

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	conf, dynConf, db, _, teardown = test.InitTestServer()

	actorIdgen := idgen.NewDomainIdGenerator(idgen.NewClock())
	actorDao = dao.NewActorDaoPgdb(db, actorIdgen, timeMocks.NewFixedTime(time.Time{}), idgen.NewAttributedIdGen[*dao.ActorAttribute](idgen.NewClock()), nil, false)

	actorPiResolutionIdgen := idgen.NewDomainIdGenerator(idgen.NewClock())
	actorPiResolutionDao = dao.NewActorPiResolutionDaoPgdb(db, actorPiResolutionIdgen)
	actorService = actor.NewService(conf, dynConf, actorDao, actorPiResolutionDao, nil, nil, nil, nil, &testMocks.NoTestMockBroker{}, nil, nil, nil, nil, nil, nil, nil, nil)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestService_CreateActor(t *testing.T) {
	ctr := gomock.NewController(t)
	mockPublisher := mocks.NewMockPublisher(ctr)
	mockActorDao := actorDaoMocks.NewMockActorDao(ctr)
	mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
	createActorService := actor.NewService(conf, dynConf, mockActorDao, nil, nil, nil, nil,
		nil, &testMocks.NoTestMockBroker{}, nil, nil, mockPublisher, nil, nil, nil, nil, mockTspUserClient)
	type mockActorCreationPublisher struct {
		enable        bool
		expectedCalls int
	}
	tests := []struct {
		name                       string
		actorRequest               *actorPb.CreateActorRequest
		wantOwnership              commontypes.Ownership
		mockActorCreationPublisher mockActorCreationPublisher
		daoActor                   *types.Actor
		mockDaoError               error
		wantStatus                 *rpc.Status
	}{
		{
			name: "Create User Actor",
			actorRequest: &actorPb.CreateActorRequest{
				Type:     types.Actor_USER,
				EntityId: "Test-User-2",
				Name:     "",
			},
			wantOwnership:              commontypes.Ownership_EPIFI_TECH,
			mockActorCreationPublisher: mockActorCreationPublisher{true, 1},
			daoActor:                   &types.Actor{Type: types.Actor_USER, EntityId: "Test-User-2"},
			mockDaoError:               nil,
			wantStatus:                 rpc.StatusOk(),
		},
		{
			name: "Create External User Actor",
			actorRequest: &actorPb.CreateActorRequest{
				Type:     types.Actor_EXTERNAL_USER,
				EntityId: "",
				Name:     "Pakalu Papito",
			},
			wantOwnership:              commontypes.Ownership_EPIFI_TECH,
			mockActorCreationPublisher: mockActorCreationPublisher{true, 1},
			daoActor:                   &types.Actor{Type: types.Actor_EXTERNAL_USER, Name: "Pakalu Papito"},
			mockDaoError:               nil,
			wantStatus:                 rpc.StatusOk(),
		},
		{
			name: "Create actor with epifi-wealth ownership",
			actorRequest: &actorPb.CreateActorRequest{
				Type:      types.Actor_EXTERNAL_USER,
				EntityId:  "",
				Name:      "Pakalu Papito",
				Ownership: commontypes.Ownership_EPIFI_WEALTH,
			},
			wantOwnership:              commontypes.Ownership_EPIFI_WEALTH,
			mockActorCreationPublisher: mockActorCreationPublisher{true, 1},
			daoActor:                   &types.Actor{Type: types.Actor_EXTERNAL_USER, Name: "Pakalu Papito", Ownership: commontypes.Ownership_EPIFI_WEALTH},
			mockDaoError:               nil,
			wantStatus:                 rpc.StatusOk(),
		},
		{
			name: "Create actor with duplicate entity ID",
			actorRequest: &actorPb.CreateActorRequest{
				Type:     types.Actor_USER,
				EntityId: "Test-User-2",
				Name:     "",
			},
			wantOwnership:              commontypes.Ownership_EPIFI_TECH,
			mockActorCreationPublisher: mockActorCreationPublisher{false, 0},
			daoActor:                   &types.Actor{Type: types.Actor_USER, EntityId: "Test-User-2"},
			mockDaoError:               epifierrors.ErrUniqueConstraintViolation,
			wantStatus:                 rpc.StatusAlreadyExists(),
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			wg := &sync.WaitGroup{}
			wg.Add(test.mockActorCreationPublisher.expectedCalls)
			defer waitgroup.SafeWaitWithDefaultTimeout(wg)

			mockActorDao.EXPECT().Create(context.Background(), test.daoActor).Return(test.daoActor, test.mockDaoError)
			if test.mockActorCreationPublisher.enable {
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Times(test.mockActorCreationPublisher.expectedCalls).Do(func(arg0, arg1 interface{}) (r0 interface{}) {
					wg.Done()
					return nil
				})
			}

			createActorResp, err := createActorService.CreateActor(context.Background(), test.actorRequest)
			assert.Nil(t, err)
			assert.Equal(t, test.wantStatus, createActorResp.GetStatus())
			if test.wantStatus.IsSuccess() {
				assert.Equal(t, createActorResp.GetActor().GetType(), test.actorRequest.GetType())
				assert.Equal(t, createActorResp.GetActor().GetEntityId(), test.actorRequest.GetEntityId())
				assert.Equal(t, createActorResp.GetActor().GetName(), test.actorRequest.GetName())
				assert.Equal(t, test.wantOwnership, createActorResp.GetActor().GetOwnership())
			}
		})
	}
}

// todo(Harleen Singh): mock the dao calls in the tests
func TestService_GetActorById(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.TruncateAndPopulateRdsFixtures(t, db, conf.ActorDb.GetName(), affectedTestTables)

	t.Run("Fetch actor record", func(t *testing.T) {
		getActorResp, err := actorService.GetActorById(context.Background(), &actorPb.GetActorByIdRequest{
			Id: fixtureUserActor1Id,
		})
		assert.Nil(t, err)
		assert.Equal(t, getActorResp.Status, rpc.StatusOk())
		assert.Equal(t, getActorResp.Actor.Id, fixtureUserActor1Id)
	})

	t.Run("Record not found", func(t *testing.T) {
		getActorResp, _ := actorService.GetActorById(context.Background(), &actorPb.GetActorByIdRequest{
			Id: "random-actor-id",
		})
		assert.Equal(t, getActorResp.Status, rpc.StatusRecordNotFound())
		assert.Nil(t, getActorResp.Actor)
	})
}

func TestService_GetActorByEntityId(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.TruncateAndPopulateRdsFixtures(t, db, conf.ActorDb.GetName(), affectedTestTables)

	t.Run("Fetch actor record", func(t *testing.T) {
		getActorResp, err := actorService.GetActorByEntityId(context.Background(), &actorPb.GetActorByEntityIdRequest{
			Type:     types.Actor_USER,
			EntityId: fixtureUserEntityId,
		})
		assert.Nil(t, err)
		assert.Equal(t, getActorResp.Status, rpc.StatusOk())
		assert.Equal(t, getActorResp.Actor.Type, types.Actor_USER)
		assert.Equal(t, getActorResp.Actor.EntityId, fixtureUserEntityId)
	})

	t.Run("Record not found", func(t *testing.T) {
		getActorResp, _ := actorService.GetActorByEntityId(context.Background(), &actorPb.GetActorByEntityIdRequest{
			Type:     types.Actor_USER,
			EntityId: "random-entity-id",
		})
		assert.Equal(t, getActorResp.Status, rpc.StatusRecordNotFound())
		assert.Nil(t, getActorResp.Actor)
	})
}

func TestActorPiResolutionService_Create(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.TruncateAndPopulateRdsFixtures(t, db, conf.ActorDb.GetName(), affectedTestTables)

	createActorPiResolutionResponse, err := actorService.CreateActorPiResolution(context.Background(), &actorPb.CreateActorPiResolutionRequest{
		ActorFrom: fixtureUserActor1Id,
		ActorTo:   fixtureActor2Id,
		PiFrom:    "",
		PiTo:      fixturePi3Id,
	})
	assert.Nil(t, err)

	assert.Equal(t, rpc.StatusOk(), createActorPiResolutionResponse.Status)
	assert.Equal(t, fixtureUserActor1Id, createActorPiResolutionResponse.ActorPiResolution.ActorFrom)
	assert.Equal(t, fixtureActor2Id, createActorPiResolutionResponse.ActorPiResolution.ActorTo)
	assert.Equal(t, "", createActorPiResolutionResponse.ActorPiResolution.PiFrom)
	assert.Equal(t, fixturePi3Id, createActorPiResolutionResponse.ActorPiResolution.PiTo)
}

func TestActorPiResolutionService_ResolveActorTo(t *testing.T) {
	t.Run("Should successfully resolve actor when found in staticActorResolutionMap", func(t *testing.T) {
		// Clean database, run migrations and load fixtures
		pkgTest.TruncateAndPopulateRdsFixtures(t, db, conf.ActorDb.GetName(), affectedTestTables)

		ctr := gomock.NewController(t)
		mockPiClient := piMocks.NewMockPiClient(ctr)
		mockAccountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
		mockMerchantClient := merchantMocks.NewMockMerchantServiceClient(ctr)
		mockBeneficiaryManagementClient := beneficiaryManagementMock.NewMockBeneficiaryManagementClient(ctr)
		mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
		actorService = actor.NewService(conf, dynConf, actorDao, actorPiResolutionDao, mockPiClient, mockAccountPiClient, nil, nil, &testMocks.NoTestMockBroker{}, mockMerchantClient, nil, nil, nil, nil, mockBeneficiaryManagementClient, nil, mockTspUserClient)

		defer func() {
			ctr.Finish()
			actorService = nil
		}()

		resolveActorToResponse, err := actorService.ResolveActorTo(context.Background(), &actorPb.ResolveActorToRequest{
			ActorFrom:   "random-actor-id",
			PiTo:        "paymentinstrument-federal-generic-account",
			ActorToName: "random-name",
		})
		assert.Nil(t, err)

		assert.Equal(t, rpc.StatusOk(), resolveActorToResponse.Status)
		assert.Equal(t, "actor-federal-generic-account", resolveActorToResponse.ActorTo)
	})
	t.Run("Should successfully resolve actor for outbound transfer with previous history", func(t *testing.T) {
		// Clean database, run migrations and load fixtures
		pkgTest.TruncateAndPopulateRdsFixtures(t, db, conf.ActorDb.GetName(), affectedTestTables)

		ctr := gomock.NewController(t)
		mockPiClient := piMocks.NewMockPiClient(ctr)
		mockAccountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
		mockMerchantClient := merchantMocks.NewMockMerchantServiceClient(ctr)
		mockBeneficiaryManagementClient := beneficiaryManagementMock.NewMockBeneficiaryManagementClient(ctr)
		mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
		actorService = actor.NewService(conf, dynConf, actorDao, actorPiResolutionDao, mockPiClient, mockAccountPiClient, nil, nil, &testMocks.NoTestMockBroker{}, mockMerchantClient, nil, nil, nil, nil, mockBeneficiaryManagementClient, nil, mockTspUserClient)

		defer func() {
			ctr.Finish()
			actorService = nil
		}()

		mockPiClient.
			EXPECT().
			GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: fixturePi3.Id}).
			Return(&piPb.GetPiByIdResponse{
				Status:            rpc.StatusOk(),
				PaymentInstrument: fixturePi3,
			}, nil)

		mockMerchantClient.EXPECT().GetAccountMerchantInfo(context.Background(), &mPb.GetAccountMerchantInfoRequest{
			PiId: fixturePi3.GetId(),
		}).Return(&mPb.GetAccountMerchantInfoResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil)

		resolveActorToResponse, err := actorService.ResolveActorTo(context.Background(), &actorPb.ResolveActorToRequest{
			ActorFrom:   fixtureUserActor1Id,
			PiTo:        fixturePi3Id,
			ActorToName: "random-name",
		})
		assert.Nil(t, err)

		assert.Equal(t, rpc.StatusOk(), resolveActorToResponse.Status)
		assert.Equal(t, fixtureActor2Id, resolveActorToResponse.ActorTo)
	})

	t.Run("Should successfully resolve actor for outbound transfer with no previous history, via internal actor flow", func(t *testing.T) {
		// Clean database, run migrations and load fixtures
		pkgTest.TruncateAndPopulateRdsFixtures(t, db, conf.ActorDb.GetName(), affectedTestTables)

		ctr := gomock.NewController(t)
		mockPiClient := piMocks.NewMockPiClient(ctr)
		mockAccountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
		mockMerchantClient := merchantMocks.NewMockMerchantServiceClient(ctr)
		mockBeneficiaryManagementClient := beneficiaryManagementMock.NewMockBeneficiaryManagementClient(ctr)

		mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
		actorService = actor.NewService(conf, dynConf, actorDao, actorPiResolutionDao, mockPiClient, mockAccountPiClient, nil, nil, &testMocks.NoTestMockBroker{}, mockMerchantClient, nil, nil, nil, nil, mockBeneficiaryManagementClient, nil, mockTspUserClient)

		defer func() {
			ctr.Finish()
			actorService = nil
		}()

		mockPiClient.
			EXPECT().
			GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: fixturePi1.Id}).
			Return(&piPb.GetPiByIdResponse{
				Status:            rpc.StatusOk(),
				PaymentInstrument: fixturePi1,
			}, nil)

		mockAccountPiClient.
			EXPECT().
			GetByPiId(context.Background(), &accountPiPb.GetByPiIdRequest{
				PiId: fixturePi1.Id,
			}).
			Return(&accountPiPb.GetByPiIdResponse{
				Status:  rpc.StatusOk(),
				ActorId: fixtureUserActor1Id,
			}, nil)

		mockMerchantClient.EXPECT().GetAccountMerchantInfo(context.Background(), &mPb.GetAccountMerchantInfoRequest{
			PiId: fixturePi1.Id,
		}).Return(&mPb.GetAccountMerchantInfoResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil)

		mockBeneficiaryManagementClient.EXPECT().GetBeneficiary(context.Background(), &beneficiaryManagementPb.GetBeneficiaryRequest{
			ActorFrom: fixtureUserActor3Id,
			PiTo:      fixturePi1.Id,
			FieldMask: []beneficiaryManagementPb.BeneficiaryFieldMask{beneficiaryManagementPb.BeneficiaryFieldMask_ID},
		}).Return(&beneficiaryManagementPb.GetBeneficiaryResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil)

		mockBeneficiaryManagementClient.EXPECT().CreateBeneficiary(context.Background(), newCreateBeneficiaryArgMatcher(&beneficiaryManagementPb.CreateBeneficiaryRequest{
			ActorFrom:         fixtureUserActor3Id,
			PiTo:              fixturePi1.Id,
			CooldownStartTime: nil,
			CooldownEndTime:   nil,
		})).Return(&beneficiaryManagementPb.CreateBeneficiaryResponse{
			Status: rpc.StatusOk(),
		}, nil)

		resolveActorToResponse, err := actorService.ResolveActorTo(context.Background(), &actorPb.ResolveActorToRequest{
			ActorFrom:   fixtureUserActor3Id,
			PiTo:        fixturePi1.Id,
			ActorToName: "random-name",
		})
		assert.Nil(t, err)

		assert.Equal(t, rpc.StatusOk(), resolveActorToResponse.Status)
		assert.Equal(t, fixtureUserActor1Id, resolveActorToResponse.ActorTo)
	})

	t.Run("Should successfully resolve actor for outbound transfer with no previous history, via external actor flow", func(t *testing.T) {

		wg := &sync.WaitGroup{}
		wg.Add(1)
		defer waitgroup.SafeWaitWithDefaultTimeout(wg)

		// Clean database, run migrations and load fixtures
		pkgTest.TruncateAndPopulateRdsFixtures(t, db, conf.ActorDb.GetName(), affectedTestTables)

		externalActorName := "external-actor-name"
		ctr := gomock.NewController(t)
		mockPublisher := mocks.NewMockPublisher(ctr)
		mockPiClient := piMocks.NewMockPiClient(ctr)
		mockAccountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
		mockMerchantClient := merchantMocks.NewMockMerchantServiceClient(ctr)
		mockBeneficiaryManagementClient := beneficiaryManagementMock.NewMockBeneficiaryManagementClient(ctr)
		mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
		actorService = actor.NewService(conf, dynConf, actorDao, actorPiResolutionDao, mockPiClient, mockAccountPiClient, nil, nil, &testMocks.NoTestMockBroker{}, mockMerchantClient, nil, mockPublisher, nil, nil, mockBeneficiaryManagementClient, nil, mockTspUserClient)

		defer func() {
			ctr.Finish()
			actorService = nil
		}()

		mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Do(func(arg0, arg1 interface{}) (r0 interface{}) {
			wg.Done()
			return nil
		})

		mockPiClient.
			EXPECT().
			GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: fixturePi3.Id}).
			Return(&piPb.GetPiByIdResponse{
				Status:            rpc.StatusOk(),
				PaymentInstrument: fixturePi3,
			}, nil)

		mockMerchantClient.EXPECT().GetAccountMerchantInfo(context.Background(), &mPb.GetAccountMerchantInfoRequest{
			PiId: fixturePi3.Id,
		}).Return(&mPb.GetAccountMerchantInfoResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil)

		mockBeneficiaryManagementClient.EXPECT().GetBeneficiary(context.Background(), &beneficiaryManagementPb.GetBeneficiaryRequest{
			ActorFrom: fixtureUserActor3Id,
			PiTo:      fixturePi3.Id,
			FieldMask: []beneficiaryManagementPb.BeneficiaryFieldMask{beneficiaryManagementPb.BeneficiaryFieldMask_ID},
		}).Return(&beneficiaryManagementPb.GetBeneficiaryResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil)

		mockBeneficiaryManagementClient.EXPECT().CreateBeneficiary(context.Background(), newCreateBeneficiaryArgMatcher(&beneficiaryManagementPb.CreateBeneficiaryRequest{
			ActorFrom:         fixtureUserActor3Id,
			PiTo:              fixturePi3.Id,
			CooldownStartTime: nil,
			CooldownEndTime:   nil,
		})).Return(&beneficiaryManagementPb.CreateBeneficiaryResponse{
			Status: rpc.StatusOk(),
		}, nil)

		resolveActorToResponse, err := actorService.ResolveActorTo(context.Background(), &actorPb.ResolveActorToRequest{
			ActorFrom:   fixtureUserActor3Id,
			PiTo:        fixturePi3.Id,
			ActorToName: externalActorName,
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk(), resolveActorToResponse.Status)

		// fetch actor by returned ActorTo id
		getActorByIdResponse, err := actorService.GetActorById(context.Background(), &actorPb.GetActorByIdRequest{
			Id: resolveActorToResponse.ActorTo,
		})
		assert.Nil(t, err)
		assert.Equal(t, resolveActorToResponse.ActorTo, getActorByIdResponse.Actor.Id)
		assert.Equal(t, externalActorName, getActorByIdResponse.Actor.Name)
	})

	t.Run("Should successfully resolve actor for outbound transfer with no previous history to merchant, via external actor flow", func(t *testing.T) {
		wg := &sync.WaitGroup{}
		wg.Add(1)
		defer waitgroup.SafeWaitWithDefaultTimeout(wg)

		// Clean database, run migrations and load fixtures
		pkgTest.TruncateAndPopulateRdsFixtures(t, db, conf.ActorDb.GetName(), affectedTestTables)

		externalActorName := "external-actor-name"
		ctr := gomock.NewController(t)
		mockPublisher := mocks.NewMockPublisher(ctr)
		mockPiClient := piMocks.NewMockPiClient(ctr)
		mockAccountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
		mockMerchantClient := merchantMocks.NewMockMerchantServiceClient(ctr)
		mockUpiClient := mock_upi.NewMockUPIClient(ctr)
		mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
		actorService = actor.NewService(conf, dynConf, actorDao, actorPiResolutionDao, mockPiClient, mockAccountPiClient, nil, nil, &testMocks.NoTestMockBroker{}, mockMerchantClient, mockUpiClient, mockPublisher, nil, nil, nil, nil, mockTspUserClient)

		defer func() {
			ctr.Finish()
			actorService = nil
		}()

		mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Do(func(arg0, arg1 interface{}) (r0 interface{}) {
			wg.Done()
			return nil
		})

		mockPiClient.
			EXPECT().
			GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: fixtureMerchantPi1.Id}).
			Return(&piPb.GetPiByIdResponse{
				Status:            rpc.StatusOk(),
				PaymentInstrument: fixtureMerchantPi1,
			}, nil)
		mockUpiClient.
			EXPECT().
			GetVpaMerchantInfo(context.Background(), &upiPb.GetVpaMerchantInfoRequest{Vpa: fixtureMerchantPi1.GetUpi().GetVpa()}).
			Return(&upiPb.GetVpaMerchantInfoResponse{
				VpaMerchantInfo: &upiPb.VpaMerchantInfo{Vpa: fixtureMerchantPi1.GetUpi().GetVpa()},
				Status:          rpc.StatusOk(),
			}, nil)
		mockMerchantClient.
			EXPECT().
			CreateMerchant(context.Background(), &mPb.CreateMerchantRequest{PiId: fixtureMerchantPi1.Id}).
			Return(&mPb.CreateMerchantResponse{
				Status:     rpc.StatusOk(),
				MerchantId: "merchant-id-1-external-pi",
			}, nil)
		resolveActorToResponse, err := actorService.ResolveActorTo(context.Background(), &actorPb.ResolveActorToRequest{
			ActorFrom:   fixtureUserActor3Id,
			PiTo:        fixtureMerchantPi1.Id,
			ActorToName: externalActorName,
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk(), resolveActorToResponse.Status)

		// fetch actor by returned ActorTo id
		getActorByIdResponse, err := actorService.GetActorById(context.Background(), &actorPb.GetActorByIdRequest{
			Id: resolveActorToResponse.ActorTo,
		})
		assert.Nil(t, err)
		assert.Equal(t, resolveActorToResponse.ActorTo, getActorByIdResponse.Actor.Id)
	})
	t.Run("Should successfully resolve actor for outbound transfer with no previous history, via external flow", func(t *testing.T) {
		// Clean database, run migrations and load fixtures
		pkgTest.TruncateAndPopulateRdsFixtures(t, db, conf.ActorDb.GetName(), affectedTestTables)

		ctr := gomock.NewController(t)
		mockPublisher := mocks.NewMockPublisher(ctr)
		mockPiClient := piMocks.NewMockPiClient(ctr)
		mockAccountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
		mockMerchantClient := merchantMocks.NewMockMerchantServiceClient(ctr)
		mockBeneficiaryManagementClient := beneficiaryManagementMock.NewMockBeneficiaryManagementClient(ctr)
		mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
		actorService = actor.NewService(conf, dynConf, actorDao, actorPiResolutionDao, mockPiClient, mockAccountPiClient, nil, nil, &testMocks.NoTestMockBroker{}, mockMerchantClient, nil, mockPublisher, nil, nil, mockBeneficiaryManagementClient, nil, mockTspUserClient)

		defer func() {
			ctr.Finish()
			actorService = nil
		}()

		mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Do(func(arg0, arg1 interface{}) (r0 interface{}) {
			return nil
		})
		mockPiClient.
			EXPECT().
			GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: fixturePi4.Id}).
			Return(&piPb.GetPiByIdResponse{
				Status:            rpc.StatusOk(),
				PaymentInstrument: fixturePi4,
			}, nil)

		mockMerchantClient.EXPECT().GetCardMerchantInfo(context.Background(), &mPb.GetCardMerchantInfoRequest{
			PiId: fixturePi4.Id,
		}).Return(&mPb.GetCardMerchantInfoResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil)
		mockMerchantClient.EXPECT().GetMerchant(context.Background(), &mPb.GetMerchantRequest{
			Identifier: &mPb.GetMerchantRequest_PiId{
				PiId: fixturePi4.GetId(),
			},
		}).Return(&mPb.GetMerchantResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil)

		mockBeneficiaryManagementClient.EXPECT().GetBeneficiary(context.Background(), &beneficiaryManagementPb.GetBeneficiaryRequest{
			ActorFrom: fixtureUserActor3Id,
			PiTo:      fixturePi4.Id,
			FieldMask: []beneficiaryManagementPb.BeneficiaryFieldMask{beneficiaryManagementPb.BeneficiaryFieldMask_ID},
		}).Return(&beneficiaryManagementPb.GetBeneficiaryResponse{
			Status: rpc.StatusOk(),
		}, nil)

		resolveActorToResponse, err := actorService.ResolveActorTo(context.Background(), &actorPb.ResolveActorToRequest{
			ActorFrom:   fixtureUserActor3Id,
			PiTo:        fixturePi4.Id,
			ActorToName: "externalActorName",
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk(), resolveActorToResponse.Status)
		// fetch actor by returned ActorTo id
		getActorByIdResponse, err := actorService.GetActorById(context.Background(), &actorPb.GetActorByIdRequest{
			Id: resolveActorToResponse.ActorTo,
		})
		assert.Nil(t, err)
		assert.Equal(t, resolveActorToResponse.ActorTo, getActorByIdResponse.Actor.Id)
		assert.Equal(t, types.ActorType_EXTERNAL_USER.String(), getActorByIdResponse.GetActor().GetType().String())
	})
	t.Run("Should successfully resolve actor for outbound transfer with no previous history, via merchant flow", func(t *testing.T) {
		// Clean database, run migrations and load fixtures
		pkgTest.TruncateAndPopulateRdsFixtures(t, db, conf.ActorDb.GetName(), affectedTestTables)

		ctr := gomock.NewController(t)
		mockPublisher := mocks.NewMockPublisher(ctr)
		mockPiClient := piMocks.NewMockPiClient(ctr)
		mockAccountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
		mockMerchantClient := merchantMocks.NewMockMerchantServiceClient(ctr)
		mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
		actorService = actor.NewService(conf, dynConf, actorDao, actorPiResolutionDao, mockPiClient, mockAccountPiClient, nil, nil, &testMocks.NoTestMockBroker{}, mockMerchantClient, nil, mockPublisher, nil, nil, nil, nil, mockTspUserClient)

		defer func() {
			ctr.Finish()
			actorService = nil
		}()

		mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Do(func(arg0, arg1 interface{}) (r0 interface{}) {
			return nil
		})
		mockPiClient.
			EXPECT().
			GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: fixtureMerchantPi2.Id}).
			Return(&piPb.GetPiByIdResponse{
				Status:            rpc.StatusOk(),
				PaymentInstrument: fixtureMerchantPi2,
			}, nil)

		mockMerchantClient.EXPECT().GetCardMerchantInfo(context.Background(), &mPb.GetCardMerchantInfoRequest{
			PiId: fixtureMerchantPi2.Id,
		}).Return(&mPb.GetCardMerchantInfoResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil)
		mockMerchantClient.EXPECT().GetMerchant(context.Background(), &mPb.GetMerchantRequest{
			Identifier: &mPb.GetMerchantRequest_PiId{
				PiId: fixtureMerchantPi2.GetId(),
			},
		}).Return(&mPb.GetMerchantResponse{
			Status: rpc.StatusOk(),
			Merchant: &mPb.Merchant{
				LegalName: "merchant",
				BrandName: "merchant",
			},
		}, nil)
		mockMerchantClient.EXPECT().CreateMerchant(context.Background(), &mPb.CreateMerchantRequest{
			LegalName: "merchant",
			BrandName: "merchant",
			PiId:      fixtureMerchantPi2.GetId(),
		}).Return(&mPb.CreateMerchantResponse{
			Status:     rpc.StatusOk(),
			MerchantId: "merchant-id2",
		}, nil)

		resolveActorToResponse, err := actorService.ResolveActorTo(context.Background(), &actorPb.ResolveActorToRequest{
			ActorFrom:   fixtureUserActor3Id,
			PiTo:        fixtureMerchantPi2.Id,
			ActorToName: "merchant",
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk(), resolveActorToResponse.Status)
		// fetch actor by returned ActorTo id
		getActorByIdResponse, err := actorService.GetActorById(context.Background(), &actorPb.GetActorByIdRequest{
			Id: resolveActorToResponse.ActorTo,
		})
		assert.Nil(t, err)
		assert.Equal(t, resolveActorToResponse.ActorTo, getActorByIdResponse.Actor.Id)
		assert.Equal(t, types.Actor_EXTERNAL_MERCHANT.String(), getActorByIdResponse.GetActor().GetType().String())
	})
}

func TestService_GetActorToPIs(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.TruncateAndPopulateRdsFixtures(t, db, conf.ActorDb.GetName(), affectedTestTables)

	ctr := gomock.NewController(t)
	mockAccountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
	actorService = actor.NewService(conf, dynConf, actorDao, actorPiResolutionDao, nil, mockAccountPiClient, nil, nil, &testMocks.NoTestMockBroker{}, nil, nil, nil, nil, nil, nil, nil, mockTspUserClient)

	type args struct {
		ctx context.Context
		req *actorPb.GetPIsOfActorToRequest
	}

	type mockGetByActorId struct {
		enable bool
		req    *accountPiPb.GetByActorIdRequest
		res    *accountPiPb.GetByActorIdResponse
		err    error
	}
	tests := []struct {
		name             string
		args             args
		mockGetByActorId mockGetByActorId
		want             *actorPb.GetPIsOfActorToResponse
		wantErr          bool
	}{
		{
			name: "Should successfully get PIs for internal user",
			args: args{
				ctx: context.Background(),
				req: &actorPb.GetPIsOfActorToRequest{
					ActorFrom: fixtureUserActor1Id,
					ActorTo:   fixtureActorId3,
				},
			},
			mockGetByActorId: mockGetByActorId{
				enable: true,
				req:    &accountPiPb.GetByActorIdRequest{ActorId: fixtureActorId3},
				res: &accountPiPb.GetByActorIdResponse{
					Status: rpc.StatusOk(),
					AccountPis: []*accountPiPb.AccountPI{
						{
							PiId: fixturePi2Id,
						},
					},
				},
				err: nil,
			},
			want: &actorPb.GetPIsOfActorToResponse{
				Status: rpc.StatusOk(),
				PiIds:  []string{fixturePi2Id},
			},
			wantErr: false,
		},
		{
			name: "Should successfully get 0 PIs for internal actor",
			args: args{
				ctx: context.Background(),
				req: &actorPb.GetPIsOfActorToRequest{
					ActorFrom: fixtureActorId3,
					ActorTo:   fixtureUserActor1Id,
				},
			},
			mockGetByActorId: mockGetByActorId{
				enable: true,
				req:    &accountPiPb.GetByActorIdRequest{ActorId: fixtureUserActor1Id},
				res: &accountPiPb.GetByActorIdResponse{
					Status:     rpc.StatusOk(),
					AccountPis: []*accountPiPb.AccountPI{},
				},
				err: nil,
			},
			want: &actorPb.GetPIsOfActorToResponse{
				Status: rpc.StatusOk(),
				PiIds:  []string{},
			},
			wantErr: false,
		},
		{
			name: "Should successfully get PIs for external actor",
			args: args{
				ctx: context.Background(),
				req: &actorPb.GetPIsOfActorToRequest{
					ActorFrom: fixtureUserActor1Id,
					ActorTo:   fixtureExternalUserActor1Id,
				},
			},
			mockGetByActorId: mockGetByActorId{
				enable: false,
			},
			want: &actorPb.GetPIsOfActorToResponse{
				Status: rpc.StatusOk(),
				PiIds: []string{
					fixturePi2Id,
				},
			},
			wantErr: false,
		},
		{
			name: "Should fail the rpc if no pis found for external actor",
			args: args{
				ctx: context.Background(),
				req: &actorPb.GetPIsOfActorToRequest{
					ActorFrom: fixtureUserActor3Id,
					ActorTo:   fixtureExternalUserActor1Id,
				},
			},
			mockGetByActorId: mockGetByActorId{
				enable: false,
			},
			want: &actorPb.GetPIsOfActorToResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "Error fetching pis for internal actor",
			args: args{
				ctx: context.Background(),
				req: &actorPb.GetPIsOfActorToRequest{
					ActorFrom: fixtureUserActor1Id,
					ActorTo:   fixtureActorId3,
				},
			},
			mockGetByActorId: mockGetByActorId{
				enable: true,
				req:    &accountPiPb.GetByActorIdRequest{ActorId: fixtureActorId3},
				err:    errors.New("error fetching pi"),
			},
			want: &actorPb.GetPIsOfActorToResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByActorId.enable {
				mockAccountPiClient.EXPECT().GetByActorId(context.Background(), tt.mockGetByActorId.req).Return(tt.mockGetByActorId.res, tt.mockGetByActorId.err)
			}
			got, err := actorService.GetPIsOfActorTo(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPIsOfActorTo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPIsOfActorTo() got = %v, want %v, diff: %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestService_GetEntityDetailsByActorId(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.TruncateAndPopulateRdsFixtures(t, db, conf.ActorDb.GetName(), affectedTestTables)

	ctr := gomock.NewController(t)
	userClient := usersMocks.NewMockUsersClient(ctr)
	mockMerchantClient := merchantMocks.NewMockMerchantServiceClient(ctr)
	mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
	actorService = actor.NewService(conf, dynConf, actorDao, actorPiResolutionDao, nil, nil, userClient, nil, &testMocks.NoTestMockBroker{}, mockMerchantClient, nil, nil, nil, nil, nil, nil, mockTspUserClient)

	type mockGetUser struct {
		enable bool
		req    *userPb.GetUserRequest
		res    *userPb.GetUserResponse
		err    error
	}

	type mockGetMerchant struct {
		enable bool
		req    *mPb.GetMerchantRequest
		res    *mPb.GetMerchantResponse
		err    error
	}

	tests := []struct {
		name            string
		req             *actorPb.GetEntityDetailsByActorIdRequest
		want            *actorPb.GetEntityDetailsByActorIdResponse
		mockGetUser     mockGetUser
		mockGetMerchant mockGetMerchant
		wantErr         bool
	}{
		{
			name: "Success",
			req: &actorPb.GetEntityDetailsByActorIdRequest{
				ActorId: "actor-user-1",
			},
			mockGetUser: mockGetUser{
				enable: true,
				req:    &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_Id{Id: "Test-User-1"}, WantProfileImageUrl: true},
				res: &userPb.GetUserResponse{
					User: &userPb.User{
						Id: "Test-User-1",
						Profile: &userPb.Profile{
							KycName: &commontypes.Name{
								FirstName:  "first",
								MiddleName: "middle",
								LastName:   "last",
								Honorific:  "Mr",
							},
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: 1234567890,
							},
							Email: "<EMAIL>",
						},
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			want: &actorPb.GetEntityDetailsByActorIdResponse{
				Status:   rpc.StatusOk(),
				Type:     types.ActorType_USER,
				EntityId: "Test-User-1",
				Name: &commontypes.Name{
					FirstName:  "first",
					MiddleName: "middle",
					LastName:   "last",
					Honorific:  "Mr",
				},
				LegalName: &commontypes.Name{
					FirstName:  "first",
					MiddleName: "middle",
					LastName:   "last",
					Honorific:  "Mr",
				},
				MobileNumber: &commontypes.PhoneNumber{
					CountryCode:    91,
					NationalNumber: 1234567890,
				},
				ProfileImageUrl: "",
				EmailId:         "<EMAIL>",
			},
			wantErr: false,
		},
		{
			name: "Actor does not exist for the given actor id",
			req: &actorPb.GetEntityDetailsByActorIdRequest{
				ActorId: "random-xyz",
			},
			mockGetUser: mockGetUser{
				enable: false,
			},
			want: &actorPb.GetEntityDetailsByActorIdResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "User does not exist for the given actor id",
			req: &actorPb.GetEntityDetailsByActorIdRequest{
				ActorId: "actor-user-1",
			},
			mockGetUser: mockGetUser{
				enable: true,
				req:    &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_Id{Id: "Test-User-1"}, WantProfileImageUrl: true},
				res: &userPb.GetUserResponse{
					Status: rpc.StatusInvalidArgument(),
				},
				err: nil,
			},
			want: &actorPb.GetEntityDetailsByActorIdResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "External Actor",
			req: &actorPb.GetEntityDetailsByActorIdRequest{
				ActorId: "actor-ex-user-1",
			},
			want: &actorPb.GetEntityDetailsByActorIdResponse{
				Status: rpc.StatusOk(),
				Type:   types.ActorType_EXTERNAL_USER,
				Name: &commontypes.Name{
					FirstName: "Test-External-User",
				},
				ProfileImageUrl: "",
			},
			wantErr: false,
		},
		{
			name: "Unsupported actor type",
			req: &actorPb.GetEntityDetailsByActorIdRequest{
				ActorId: "actor-merchant-1",
			},
			want: &actorPb.GetEntityDetailsByActorIdResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "get actor of type external merchant successfully",
			req: &actorPb.GetEntityDetailsByActorIdRequest{
				ActorId: fixtureExternalMerchanl1Id,
			},
			want: &actorPb.GetEntityDetailsByActorIdResponse{
				Status:          rpc.StatusOk(),
				Type:            types.ActorType_EXTERNAL_MERCHANT,
				EntityId:        "merchant-entity-id-1",
				ProfileImageUrl: "s3://swiggy",
				Name:            &commontypes.Name{FirstName: "Amazon"},
				LegalName:       &commontypes.Name{},
				Entity: &actorPb.GetEntityDetailsByActorIdResponse_Merchant_{
					Merchant: &actorPb.GetEntityDetailsByActorIdResponse_Merchant{
						Capabilities: map[string]bool{
							mPb.Capability_TIMELINE_COLLECT.String(): false,
							mPb.Capability_TIMELINE_PAY.String():     false,
						},
					},
				},
			},
			mockGetMerchant: mockGetMerchant{
				enable: true,
				req: &mPb.GetMerchantRequest{
					Identifier: &mPb.GetMerchantRequest_MerchantId{MerchantId: "merchant-entity-id-1"},
				},
				res: &mPb.GetMerchantResponse{
					Status: rpc.StatusOk(),
					Merchant: &mPb.Merchant{
						Id:          "merchant-entity-id-1",
						LogoUrl:     "s3://swiggy",
						DisplayName: "Amazon",
						Capabilities: map[string]bool{
							mPb.Capability_TIMELINE_COLLECT.String(): false,
							mPb.Capability_TIMELINE_PAY.String():     false,
						},
					},
				},
				err: nil,
			},
			wantErr: false,
		},
		{
			name: "get actor of type external merchant failed due to merchant entity error",
			req: &actorPb.GetEntityDetailsByActorIdRequest{
				ActorId: fixtureExternalMerchanl1Id,
			},
			want: &actorPb.GetEntityDetailsByActorIdResponse{
				Status: rpc.StatusInternal(),
			},
			mockGetMerchant: mockGetMerchant{
				enable: true,
				req: &mPb.GetMerchantRequest{
					Identifier: &mPb.GetMerchantRequest_MerchantId{MerchantId: "merchant-entity-id-1"},
				},
				res: &mPb.GetMerchantResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetUser.enable {
				userClient.EXPECT().GetUser(context.Background(), tt.mockGetUser.req).Return(tt.mockGetUser.res, tt.mockGetUser.err)
			}
			if tt.mockGetMerchant.enable {
				mockMerchantClient.EXPECT().GetMerchant(context.Background(), tt.mockGetMerchant.req).
					Return(tt.mockGetMerchant.res, tt.mockGetMerchant.err)
			}
			got, err := actorService.GetEntityDetailsByActorId(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetEntityDetailsByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetEntityDetailsByActorId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_BlockActor(t *testing.T) {
	ctr := gomock.NewController(t)
	mockBlockedActorDao := actorDaoMocks.NewMockBlockedActorMapDao(ctr)
	mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
	actorService = actor.NewService(conf, nil, nil, nil, nil, nil, nil, mockBlockedActorDao, &testMocks.NoTestMockBroker{}, nil, nil, nil, nil, nil, nil, nil, mockTspUserClient)

	defer func() {
		ctr.Finish()
		actorService = nil
	}()

	type mockGetBlockedActorMap struct {
		enable          bool
		actorId         string
		blockedActorId  string
		blockedActorMap *actorPb.BlockedActorMap
		error           error
	}
	type mockCreate struct {
		enable               bool
		blockedActorMap      *actorPb.BlockedActorMap
		savedBlockedActorMap *actorPb.BlockedActorMap
		err                  error
	}
	tests := []struct {
		name                   string
		mockGetBlockedActorMap mockGetBlockedActorMap
		mockCreate             mockCreate
		req                    *actorPb.BlockActorRequest
		want                   *actorPb.BlockActorResponse
		wantErr                bool
	}{
		{
			name: "successful block the actor",
			req: &actorPb.BlockActorRequest{
				CurrentActorId: "actor-1",
				OtherActorId:   "actor-2",
				IsSpam:         true,
			},
			mockGetBlockedActorMap: mockGetBlockedActorMap{
				enable:         true,
				actorId:        "actor-1",
				blockedActorId: "actor-2",
				error:          epifierrors.ErrRecordNotFound,
			},
			mockCreate: mockCreate{
				enable: true,
				blockedActorMap: &actorPb.BlockedActorMap{
					ActorId:        "actor-1",
					BlockedActorId: "actor-2",
					IsSpam:         true,
				},
				savedBlockedActorMap: &actorPb.BlockedActorMap{
					ActorId:        "actor-1",
					BlockedActorId: "actor-2",
					IsSpam:         true,
				},
				err: nil,
			},
			want: &actorPb.BlockActorResponse{Status: rpc.StatusOk()},
		},
		{
			name: "actor already blocked",
			req: &actorPb.BlockActorRequest{
				CurrentActorId: "actor-1",
				OtherActorId:   "actor-2",
				IsSpam:         true,
			},
			mockGetBlockedActorMap: mockGetBlockedActorMap{
				enable:         true,
				actorId:        "actor-1",
				blockedActorId: "actor-2",
				blockedActorMap: &actorPb.BlockedActorMap{
					ActorId:        "actor-1",
					BlockedActorId: "actor-2",
					IsSpam:         true,
				},
				error: nil,
			},
			want: &actorPb.BlockActorResponse{Status: rpc.ExtendedStatusAlreadyProcessed()},
		},
		{
			name: "invalid argument when both actors are same",
			req: &actorPb.BlockActorRequest{
				CurrentActorId: "actor-1",
				OtherActorId:   "actor-1",
				IsSpam:         true,
			},
			want: &actorPb.BlockActorResponse{Status: rpc.StatusInvalidArgument()},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetBlockedActorMap.enable {
				mockBlockedActorDao.EXPECT().
					GetByActorIdAndBlockedActorId(context.Background(), tt.mockGetBlockedActorMap.actorId, tt.mockGetBlockedActorMap.blockedActorId).
					Return(tt.mockGetBlockedActorMap.blockedActorMap, tt.mockGetBlockedActorMap.error)
			}

			if tt.mockCreate.enable {
				mockBlockedActorDao.EXPECT().
					Create(context.Background(), tt.mockCreate.blockedActorMap).
					Return(tt.mockCreate.savedBlockedActorMap, tt.mockCreate.err)
			}
			got, err := actorService.BlockActor(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BlockActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
				t.Errorf("BlockActor() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_UnblockActor(t *testing.T) {
	ctr := gomock.NewController(t)
	mockBlockedActorDao := actorDaoMocks.NewMockBlockedActorMapDao(ctr)
	mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
	actorService = actor.NewService(conf, nil, nil, nil, nil, nil, nil, mockBlockedActorDao, &testMocks.NoTestMockBroker{}, nil, nil, nil, nil, nil, nil, nil, mockTspUserClient)

	defer func() {
		ctr.Finish()
		actorService = nil
	}()

	type mockDelete struct {
		enable         bool
		actorId        string
		blockedActorId string
		err            error
	}
	tests := []struct {
		name       string
		mockDelete mockDelete
		req        *actorPb.UnblockActorRequest
		want       *actorPb.UnblockActorResponse
		wantErr    bool
	}{
		{
			name: "successfully delete a blocked actor",
			req: &actorPb.UnblockActorRequest{
				CurrentActorId: "actor-1",
				OtherActorId:   "actor-2",
			},
			mockDelete: mockDelete{
				enable:         true,
				actorId:        "actor-1",
				blockedActorId: "actor-2",
				err:            nil,
			},
			want:    &actorPb.UnblockActorResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "actor not blocked",
			req: &actorPb.UnblockActorRequest{
				CurrentActorId: "actor-1",
				OtherActorId:   "actor-2",
			},
			mockDelete: mockDelete{
				enable:         true,
				actorId:        "actor-1",
				blockedActorId: "actor-2",
				err:            epifierrors.ErrRecordNotFound,
			},
			want:    &actorPb.UnblockActorResponse{Status: rpc.ExtendedStatusAlreadyProcessed()},
			wantErr: false,
		},
		{
			name: "invalid argument as both the actors are same",
			req: &actorPb.UnblockActorRequest{
				CurrentActorId: "actor-1",
				OtherActorId:   "actor-1",
			},
			want:    &actorPb.UnblockActorResponse{Status: rpc.StatusInvalidArgument()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockDelete.enable {
				mockBlockedActorDao.EXPECT().
					Delete(context.Background(), tt.mockDelete.actorId, tt.mockDelete.blockedActorId).
					Return(tt.mockDelete.err)
			}

			got, err := actorService.UnblockActor(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UnblockActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UnblockActor() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_ReportSpamForBlockedActor(t *testing.T) {
	ctr := gomock.NewController(t)
	mockBlockedActorDao := actorDaoMocks.NewMockBlockedActorMapDao(ctr)
	mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
	actorService = actor.NewService(conf, nil, nil, nil, nil, nil, nil, mockBlockedActorDao, &testMocks.NoTestMockBroker{}, nil, nil, nil, nil, nil, nil, nil, mockTspUserClient)

	defer func() {
		ctr.Finish()
		actorService = nil
	}()

	type mockGetBlockedActorMap struct {
		enable          bool
		actorId         string
		blockedActorId  string
		blockedActorMap *actorPb.BlockedActorMap
		error           error
	}
	type mockSetSpam struct {
		enable         bool
		actorId        string
		blockedActorId string
		error          error
	}
	tests := []struct {
		name                   string
		mockGetBlockedActorMap mockGetBlockedActorMap
		mockSetSpam            mockSetSpam
		req                    *actorPb.ReportSpamForBlockedActorRequest
		want                   *actorPb.ReportSpamForBlockedActorResponse
		wantErr                bool
	}{
		{
			name: "successfully report spam",
			req: &actorPb.ReportSpamForBlockedActorRequest{
				CurrentActorId: "actor-1",
				BlockedActorId: "actor-2",
			},
			mockGetBlockedActorMap: mockGetBlockedActorMap{
				enable:         true,
				actorId:        "actor-1",
				blockedActorId: "actor-2",
				blockedActorMap: &actorPb.BlockedActorMap{
					ActorId:        "actor-1",
					BlockedActorId: "actor-2",
					IsSpam:         false,
				},
				error: nil,
			},
			mockSetSpam: mockSetSpam{
				enable:         true,
				actorId:        "actor-1",
				blockedActorId: "actor-2",
				error:          nil,
			},
			want: &actorPb.ReportSpamForBlockedActorResponse{Status: rpc.StatusOk()},
		},
		{
			name: "actor not blocked",
			req: &actorPb.ReportSpamForBlockedActorRequest{
				CurrentActorId: "actor-1",
				BlockedActorId: "actor-2",
			},
			mockGetBlockedActorMap: mockGetBlockedActorMap{
				enable:         true,
				actorId:        "actor-1",
				blockedActorId: "actor-2",
				blockedActorMap: &actorPb.BlockedActorMap{
					ActorId:        "actor-1",
					BlockedActorId: "actor-2",
					IsSpam:         false,
				},
				error: epifierrors.ErrRecordNotFound,
			},
			want: &actorPb.ReportSpamForBlockedActorResponse{
				Status: rpc.NewStatus(uint32(actorPb.ReportSpamForBlockedActorResponse_ACTOR_NOT_BLOCKED), "", ""),
			},
		},
		{
			name: "already marked spam",
			req: &actorPb.ReportSpamForBlockedActorRequest{
				CurrentActorId: "actor-1",
				BlockedActorId: "actor-2",
			},
			mockGetBlockedActorMap: mockGetBlockedActorMap{
				enable:         true,
				actorId:        "actor-1",
				blockedActorId: "actor-2",
				blockedActorMap: &actorPb.BlockedActorMap{
					ActorId:        "actor-1",
					BlockedActorId: "actor-2",
					IsSpam:         true,
				},
				error: nil,
			},
			want: &actorPb.ReportSpamForBlockedActorResponse{Status: rpc.ExtendedStatusAlreadyProcessed()},
		},
		{
			name: "invalid argument as both the actors are same",
			req: &actorPb.ReportSpamForBlockedActorRequest{
				CurrentActorId: "actor-1",
				BlockedActorId: "actor-1",
			},
			want: &actorPb.ReportSpamForBlockedActorResponse{Status: rpc.StatusInvalidArgument()},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetBlockedActorMap.enable {
				mockBlockedActorDao.EXPECT().
					GetByActorIdAndBlockedActorId(context.Background(), tt.mockGetBlockedActorMap.actorId, tt.mockGetBlockedActorMap.blockedActorId).
					Return(tt.mockGetBlockedActorMap.blockedActorMap, tt.mockGetBlockedActorMap.error)
			}

			if tt.mockSetSpam.enable {
				mockBlockedActorDao.EXPECT().
					SetSpamFlag(context.Background(), tt.mockSetSpam.actorId, tt.mockSetSpam.blockedActorId).
					Return(tt.mockSetSpam.error)
			}
			got, err := actorService.ReportSpamForBlockedActor(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ReportSpamForBlockedActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !got.GetStatus().IsEqualTo(tt.want.GetStatus()) {
				t.Errorf("ReportSpamForBlockedActor() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetRelationshipWithActor(t *testing.T) {
	ctr := gomock.NewController(t)
	mockBlockedActorDao := actorDaoMocks.NewMockBlockedActorMapDao(ctr)
	mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
	actorService = actor.NewService(conf, nil, nil, nil, nil, nil, nil, mockBlockedActorDao, &testMocks.NoTestMockBroker{}, nil, nil, nil, nil, nil, nil, nil, mockTspUserClient)

	defer func() {
		ctr.Finish()
		actorService = nil
	}()

	type mockGetBlockedActorMap struct {
		enable          bool
		actorId         string
		blockedActorId  string
		blockedActorMap *actorPb.BlockedActorMap
		error           error
	}

	tests := []struct {
		name                   string
		mockGetBlockedActorMap mockGetBlockedActorMap
		req                    *actorPb.GetRelationshipWithActorRequest
		want                   *actorPb.GetRelationshipWithActorResponse
		wantErr                bool
	}{
		{
			name: "blocked actor",
			req: &actorPb.GetRelationshipWithActorRequest{
				CurrentActorId: "actor-1",
				OtherActorId:   "actor-2",
			},
			mockGetBlockedActorMap: mockGetBlockedActorMap{
				enable:         true,
				actorId:        "actor-1",
				blockedActorId: "actor-2",
				blockedActorMap: &actorPb.BlockedActorMap{
					ActorId:        "actor-1",
					BlockedActorId: "actor-2",
					IsSpam:         false,
				},
				error: nil,
			},
			want: &actorPb.GetRelationshipWithActorResponse{
				Status:       rpc.StatusOk(),
				Relationship: actorPb.GetRelationshipWithActorResponse_BLOCKED,
			},
		},
		{
			name: "unblocked actor",
			req: &actorPb.GetRelationshipWithActorRequest{
				CurrentActorId: "actor-1",
				OtherActorId:   "actor-2",
			},
			mockGetBlockedActorMap: mockGetBlockedActorMap{
				enable:         true,
				actorId:        "actor-1",
				blockedActorId: "actor-2",
				blockedActorMap: &actorPb.BlockedActorMap{
					ActorId:        "actor-1",
					BlockedActorId: "actor-2",
					IsSpam:         false,
				},
				error: epifierrors.ErrRecordNotFound,
			},
			want: &actorPb.GetRelationshipWithActorResponse{
				Status:       rpc.StatusOk(),
				Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
			},
		},
		{
			name: "reported actor",
			req: &actorPb.GetRelationshipWithActorRequest{
				CurrentActorId: "actor-1",
				OtherActorId:   "actor-2",
			},
			mockGetBlockedActorMap: mockGetBlockedActorMap{
				enable:         true,
				actorId:        "actor-1",
				blockedActorId: "actor-2",
				blockedActorMap: &actorPb.BlockedActorMap{
					ActorId:        "actor-1",
					BlockedActorId: "actor-2",
					IsSpam:         true,
				},
				error: nil,
			},
			want: &actorPb.GetRelationshipWithActorResponse{
				Status:       rpc.StatusOk(),
				Relationship: actorPb.GetRelationshipWithActorResponse_REPORTED,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetBlockedActorMap.enable {
				mockBlockedActorDao.EXPECT().
					GetByActorIdAndBlockedActorId(context.Background(), tt.mockGetBlockedActorMap.actorId, tt.mockGetBlockedActorMap.blockedActorId).
					Return(tt.mockGetBlockedActorMap.blockedActorMap, tt.mockGetBlockedActorMap.error)
			}

			got, err := actorService.GetRelationshipWithActor(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRelationshipWithActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRelationshipWithActor() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetEntityDetails(t *testing.T) {
	ctr := gomock.NewController(t)
	mockActorDao := actorDaoMocks.NewMockActorDao(ctr)
	userClient := userMocks.NewMockUsersClient(ctr)
	merchantMockClient := merchantMocks.NewMockMerchantServiceClient(ctr)
	mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
	actorService = actor.NewService(conf, dynConf, mockActorDao, nil, nil, nil, userClient, nil, &testMocks.NoTestMockBroker{}, merchantMockClient, nil, nil, nil, nil, nil, nil, mockTspUserClient)

	type mockGetUsers struct {
		enable bool
		req    *userPb.GetUsersRequest
		want   *userPb.GetUsersResponse
		err    error
	}

	type mockGetByIds struct {
		enable bool
		req    []string
		want   []*types.Actor
		err    error
	}

	type mockGetMerchants struct {
		enable bool
		req    *mPb.GetMerchantsRequest
		want   *mPb.GetMerchantsResponse
		err    error
	}

	type mockGetTspUser struct {
		enable bool
		req    *tspUserPb.GetTspUserRequest
		res    *tspUserPb.GetTspUserResponse
		err    error
	}

	tests := []struct {
		name             string
		req              *actorPb.GetEntityDetailsRequest
		mockGetUsers     mockGetUsers
		mockGetByIds     mockGetByIds
		mockGetMerchants mockGetMerchants
		mockGetTspUsers  []*mockGetTspUser
		want             *actorPb.GetEntityDetailsResponse
		wantErr          bool
	}{
		{
			name: "Found entity details successfully",
			req:  &actorPb.GetEntityDetailsRequest{ActorIds: []string{"actor-user-1", "actor-user-2"}},
			mockGetUsers: mockGetUsers{
				enable: true,
				req: &userPb.GetUsersRequest{Identifier: []*userPb.GetUsersRequest_GetUsersIdentifier{
					{
						Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Id{Id: "Test-User-1"},
					},
					{
						Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Id{Id: "Test-User-2"},
					},
				}},
				want: &userPb.GetUsersResponse{
					Status: rpc.StatusOk(),
					Users: []*userPb.User{
						{
							Id: "Test-User-1",
							Profile: &userPb.Profile{
								KycName: &commontypes.Name{
									FirstName:  "first",
									MiddleName: "middle",
									LastName:   "last",
									Honorific:  "Mr",
								},
								PhoneNumber: &commontypes.PhoneNumber{
									CountryCode:    91,
									NationalNumber: 1234567890,
								},
								Email: "<EMAIL>",
							},
						},
						{
							Id: "Test-User-2",
							Profile: &userPb.Profile{
								KycName: &commontypes.Name{
									FirstName:  "first2",
									MiddleName: "middle2",
									LastName:   "last2",
									Honorific:  "Mr",
								},
								PhoneNumber: &commontypes.PhoneNumber{
									CountryCode:    91,
									NationalNumber: 1234567890,
								},
								Email: "<EMAIL>",
							},
						},
					},
				},
				err: nil,
			},
			mockGetByIds: mockGetByIds{
				enable: true,
				req:    []string{"actor-user-1", "actor-user-2"},
				want: []*types.Actor{
					{
						EntityId: "Test-User-1",
						Type:     types.Actor_USER,
					},
					{
						EntityId: "Test-User-2",
						Type:     types.Actor_USER,
					},
				},
				err: nil,
			},
			want: &actorPb.GetEntityDetailsResponse{
				Status: rpc.StatusOk(),
				EntityDetails: []*actorPb.GetEntityDetailsResponse_EntityDetail{
					{
						EntityId: "Test-User-1",
						Name: &commontypes.Name{
							FirstName:  "first",
							MiddleName: "middle",
							LastName:   "last",
							Honorific:  "Mr",
						},
						EntityType: types.ActorType_USER,
						MobileNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
						ProfileImageUrl: "",
						EmailId:         "<EMAIL>",
					},
					{
						EntityId: "Test-User-2",
						Name: &commontypes.Name{
							FirstName:  "first2",
							MiddleName: "middle2",
							LastName:   "last2",
							Honorific:  "Mr",
						},
						EntityType: types.ActorType_USER,
						MobileNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
						ProfileImageUrl: "",
						EmailId:         "<EMAIL>",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "Actor does not exist for the given actor id",
			req: &actorPb.GetEntityDetailsRequest{
				ActorIds: []string{"random-xyz"},
			},
			mockGetByIds: mockGetByIds{
				enable: true,
				req:    []string{"random-xyz"},
				want:   nil,
				err:    gormv2.ErrRecordNotFound,
			},
			want: &actorPb.GetEntityDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "External Actor",
			req:  &actorPb.GetEntityDetailsRequest{ActorIds: []string{"actor-ex-user-1"}},
			mockGetByIds: mockGetByIds{
				enable: true,
				req:    []string{"actor-ex-user-1"},
				want: []*types.Actor{
					{
						Name:     "Test-External-User",
						EntityId: "actor-ex-user-1",
						Type:     types.Actor_EXTERNAL_USER,
					},
				},
				err: nil,
			},
			want: &actorPb.GetEntityDetailsResponse{
				Status: rpc.StatusOk(),
				EntityDetails: []*actorPb.GetEntityDetailsResponse_EntityDetail{
					{
						Name: &commontypes.Name{
							FirstName: "Test-External-User",
						},
						EntityId:   "actor-ex-user-1",
						EntityType: types.ActorType_EXTERNAL_USER,
					},
				},
			},
		},
		{
			name: "Unsupported actor type",
			req:  &actorPb.GetEntityDetailsRequest{ActorIds: []string{"actor-merchant-1"}},
			mockGetByIds: mockGetByIds{
				enable: true,
				req:    []string{"actor-merchant-1"},
				want: []*types.Actor{
					{
						Name: "Merchant",
					},
				},
				err: nil,
			},
			want: &actorPb.GetEntityDetailsResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "external and internal user",
			req:  &actorPb.GetEntityDetailsRequest{ActorIds: []string{"actor-user-1", "actor-ex-user-1"}},
			mockGetUsers: mockGetUsers{
				enable: true,
				req: &userPb.GetUsersRequest{Identifier: []*userPb.GetUsersRequest_GetUsersIdentifier{
					{
						Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Id{Id: "Test-User-1"},
					},
				}},
				want: &userPb.GetUsersResponse{
					Status: rpc.StatusOk(),
					Users: []*userPb.User{
						{
							Id: "Test-User-1",
							Profile: &userPb.Profile{
								KycName: &commontypes.Name{
									FirstName:  "first",
									MiddleName: "middle",
									LastName:   "last",
									Honorific:  "Mr",
								},
								PhoneNumber: &commontypes.PhoneNumber{
									CountryCode:    91,
									NationalNumber: 1234567890,
								},
								Email: "<EMAIL>",
							},
						},
					},
				},
				err: nil,
			},
			mockGetByIds: mockGetByIds{
				enable: true,
				req:    []string{"actor-user-1", "actor-ex-user-1"},
				want: []*types.Actor{
					{
						EntityId: "Test-User-1",
						Type:     types.Actor_USER,
					},
					{
						Name:     "Test-External-User",
						Type:     types.Actor_EXTERNAL_USER,
						EntityId: "actor-ex-user-1",
					},
				},
				err: nil,
			},
			want: &actorPb.GetEntityDetailsResponse{
				Status: rpc.StatusOk(),
				EntityDetails: []*actorPb.GetEntityDetailsResponse_EntityDetail{
					{
						Name: &commontypes.Name{
							FirstName: "Test-External-User",
						},
						EntityId:   "actor-ex-user-1",
						EntityType: types.ActorType_EXTERNAL_USER,
					},
					{
						EntityId: "Test-User-1",
						Name: &commontypes.Name{
							FirstName:  "first",
							MiddleName: "middle",
							LastName:   "last",
							Honorific:  "Mr",
						},
						EntityType: types.ActorType_USER,
						MobileNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
						ProfileImageUrl: "",
						EmailId:         "<EMAIL>",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "external merchant actor input",
			req:  &actorPb.GetEntityDetailsRequest{ActorIds: []string{"actor-ex-merchant-1"}},
			mockGetByIds: mockGetByIds{
				enable: true,
				req:    []string{"actor-ex-merchant-1"},
				want: []*types.Actor{
					{
						EntityId: "merchant-entity-id-1",
						Type:     types.Actor_EXTERNAL_MERCHANT,
					},
				},
				err: nil,
			},
			mockGetMerchants: mockGetMerchants{
				enable: true,
				req: &mPb.GetMerchantsRequest{Identifier: &mPb.GetMerchantsRequest_MerchantIdentifier_{
					MerchantIdentifier: &mPb.GetMerchantsRequest_MerchantIdentifier{MerchantIds: []string{"merchant-entity-id-1"}}}},
				want: &mPb.GetMerchantsResponse{
					Status: rpc.StatusOk(),
					Merchants: []*mPb.Merchant{
						{
							Id:          "merchant-entity-id-1",
							LogoUrl:     "s3://swiggy.com",
							DisplayName: "SWIGGY",
							Capabilities: map[string]bool{
								mPb.Capability_TIMELINE_COLLECT.String(): false,
								mPb.Capability_TIMELINE_PAY.String():     false,
							},
						},
					},
				},
				err: nil,
			},
			want: &actorPb.GetEntityDetailsResponse{
				Status: rpc.StatusOk(),
				EntityDetails: []*actorPb.GetEntityDetailsResponse_EntityDetail{
					{
						EntityId:        "merchant-entity-id-1",
						ProfileImageUrl: "s3://swiggy.com",
						EntityType:      types.ActorType_EXTERNAL_MERCHANT,
						Name:            (&commontypes.Name{}).Parse("SWIGGY"),
						Entity: &actorPb.GetEntityDetailsResponse_EntityDetail_Merchant_{
							Merchant: &actorPb.GetEntityDetailsResponse_EntityDetail_Merchant{
								Capabilities: map[string]bool{
									mPb.Capability_TIMELINE_COLLECT.String(): false,
									mPb.Capability_TIMELINE_PAY.String():     false,
								},
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "error getting merchants from merchant service",
			req:  &actorPb.GetEntityDetailsRequest{ActorIds: []string{"actor-ex-merchant-1"}},
			mockGetByIds: mockGetByIds{
				enable: true,
				req:    []string{"actor-ex-merchant-1"},
				want: []*types.Actor{
					{
						EntityId: "merchant-entity-id-1",
						Type:     types.Actor_EXTERNAL_MERCHANT,
					},
				},
				err: nil,
			},
			mockGetMerchants: mockGetMerchants{
				enable: true,
				req: &mPb.GetMerchantsRequest{Identifier: &mPb.GetMerchantsRequest_MerchantIdentifier_{
					MerchantIdentifier: &mPb.GetMerchantsRequest_MerchantIdentifier{MerchantIds: []string{"merchant-entity-id-1"}}}},
				want: &mPb.GetMerchantsResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			want: &actorPb.GetEntityDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "get tsp user details",
			req:  &actorPb.GetEntityDetailsRequest{ActorIds: []string{"actor-tsp-user1"}},
			mockGetByIds: mockGetByIds{
				enable: true,
				req:    []string{"actor-tsp-user1"},
				want: []*types.Actor{
					{
						Id:        "actor-tsp-user1",
						EntityId:  "tsp-user1",
						Type:      types.Actor_TSP_USER,
						Ownership: commontypes.Ownership_STOCK_GUARDIAN_TSP,
					},
				},
				err: nil,
			},
			mockGetTspUsers: []*mockGetTspUser{
				{
					enable: true,
					req: &tspUserPb.GetTspUserRequest{
						Owner: commontypes.Owner_OWNER_STOCK_GUARDIAN_TSP,
						Identifier: &tspUserPb.GetTspUserRequest_TspUserId{
							TspUserId: "tsp-user1",
						},
					},
					res: &tspUserPb.GetTspUserResponse{
						Status: rpc.StatusOk(),
						TspUser: &tspUserPb.TspUser{
							Id:                "tsp-user1",
							ExternalCustRefId: "id1",
							PersonalDetails: &tspUserPb.PersonalDetails{
								PhoneNumber: &commontypes.PhoneNumber{
									CountryCode:    91,
									NationalNumber: 9876543210,
								},
								Email: "<EMAIL>",
								Name: &commontypes.Name{
									FirstName:  "Harry",
									MiddleName: "James",
									LastName:   "Potter",
									Honorific:  "Mr",
								},
							},
							Owner: commontypes.Owner_OWNER_STOCK_GUARDIAN_TSP,
						},
					},
					err: nil,
				},
			},
			want: &actorPb.GetEntityDetailsResponse{
				Status: rpc.StatusOk(),
				EntityDetails: []*actorPb.GetEntityDetailsResponse_EntityDetail{
					{
						EntityId: "tsp-user1",
						Name: &commontypes.Name{
							FirstName:  "Harry",
							MiddleName: "James",
							LastName:   "Potter",
							Honorific:  "Mr",
						},
						MobileNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 9876543210,
						},
						EmailId:    "<EMAIL>",
						ActorId:    "actor-tsp-user1",
						EntityType: types.ActorType_TSP_USER,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should fetch users along with soft deleted user when FetchSoftDeletedUsers is passed as true",
			req:  &actorPb.GetEntityDetailsRequest{ActorIds: []string{"actor-user-1", "actor-soft-deleted-user-1"}, FetchSoftDeletedUsers: true},
			mockGetUsers: mockGetUsers{
				enable: true,
				req: &userPb.GetUsersRequest{Identifier: []*userPb.GetUsersRequest_GetUsersIdentifier{
					{
						Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Id{Id: "Test-User-1"},
					},
					{
						Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Id{Id: "Test-Soft-Deleted-User-1"},
					},
				},
					Unscoped: true,
				},
				want: &userPb.GetUsersResponse{
					Status: rpc.StatusOk(),
					Users: []*userPb.User{
						{
							Id: "Test-User-1",
							Profile: &userPb.Profile{
								KycName: &commontypes.Name{
									FirstName:  "first",
									MiddleName: "middle",
									LastName:   "last",
									Honorific:  "Mr",
								},
								PhoneNumber: &commontypes.PhoneNumber{
									CountryCode:    91,
									NationalNumber: 1234567890,
								},
								Email: "<EMAIL>",
							},
						},
						{
							Id: "Test-Soft-Deleted-User-1",
							Profile: &userPb.Profile{
								KycName: &commontypes.Name{
									FirstName:  "soft",
									MiddleName: "deleted",
									LastName:   "user",
									Honorific:  "Mr",
								},
								PhoneNumber: &commontypes.PhoneNumber{
									CountryCode:    91,
									NationalNumber: 1234567890,
								},
								Email: "<EMAIL>",
							},
						},
					},
				},
				err: nil,
			},
			mockGetByIds: mockGetByIds{
				enable: true,
				req:    []string{"actor-user-1", "actor-soft-deleted-user-1"},
				want: []*types.Actor{
					{
						EntityId: "Test-User-1",
						Type:     types.Actor_USER,
					},
					{
						Name:     "Soft-Deleted-User",
						Type:     types.Actor_USER,
						EntityId: "Test-Soft-Deleted-User-1",
					},
				},
				err: nil,
			},
			want: &actorPb.GetEntityDetailsResponse{
				Status: rpc.StatusOk(),
				EntityDetails: []*actorPb.GetEntityDetailsResponse_EntityDetail{
					{
						EntityId: "Test-User-1",
						Name: &commontypes.Name{
							FirstName:  "first",
							MiddleName: "middle",
							LastName:   "last",
							Honorific:  "Mr",
						},
						EntityType: types.ActorType_USER,
						MobileNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
						ProfileImageUrl: "",
						EmailId:         "<EMAIL>",
					},
					{
						EntityId: "Test-Soft-Deleted-User-1",
						Name: &commontypes.Name{
							FirstName:  "soft",
							MiddleName: "deleted",
							LastName:   "user",
							Honorific:  "Mr",
						},
						EntityType: types.ActorType_USER,
						MobileNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: 1234567890,
						},
						ProfileImageUrl: "",
						EmailId:         "<EMAIL>",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetUsers.enable {
				userClient.EXPECT().GetUsers(context.Background(), tt.mockGetUsers.req).
					Return(tt.mockGetUsers.want, tt.mockGetUsers.err)
			}
			if tt.mockGetByIds.enable {
				mockActorDao.EXPECT().GetByIds(context.Background(), tt.mockGetByIds.req).
					Return(tt.mockGetByIds.want, tt.mockGetByIds.err)
			}
			if tt.mockGetMerchants.enable {
				merchantMockClient.EXPECT().GetMerchants(context.Background(), tt.mockGetMerchants.req).
					Return(tt.mockGetMerchants.want, tt.mockGetMerchants.err)
			}
			if tt.mockGetTspUsers != nil {
				for _, mockTspUser := range tt.mockGetTspUsers {
					mockTspUserClient.EXPECT().GetTspUser(gomock.Any(), mockTspUser.req).Return(mockTspUser.res, mockTspUser.err)
				}
			}
			got, err := actorService.GetEntityDetails(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetEntityDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetEntityDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetActorsByEntityIds(t *testing.T) {

	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockActorDao := actorDaoMocks.NewMockActorDao(ctr)
	mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
	actorService = actor.NewService(conf, dynConf, mockActorDao, nil, nil, nil, nil, nil, &testMocks.NoTestMockBroker{}, nil, nil, nil, nil, nil, nil, nil, mockTspUserClient)

	type mockGetByEntityIds struct {
		enable bool
		req    []string
		want   []*types.Actor
		err    error
	}

	tests := []struct {
		name               string
		req                *actorPb.GetActorsByEntityIdsRequest
		mockGetByEntityIds mockGetByEntityIds
		want               *actorPb.GetActorsByEntityIdsResponse
		wantErr            bool
	}{{
		name: "fetch actors successfully from entity ids",
		req:  &actorPb.GetActorsByEntityIdsRequest{EntityIds: []string{fixtureUserEntityId}},
		mockGetByEntityIds: mockGetByEntityIds{
			enable: true,
			req:    []string{fixtureUserEntityId},
			want: []*types.Actor{{
				Id:       "actor-1",
				EntityId: fixtureUserEntityId,
			},
			},
		},
		want: &actorPb.GetActorsByEntityIdsResponse{
			Actors: []*types.Actor{
				{
					Id:       "actor-1",
					EntityId: fixtureUserEntityId,
				},
			},
			Status: rpc.StatusOk(),
		},
	}, {
		name: "failure while fetching actor from db",
		req:  &actorPb.GetActorsByEntityIdsRequest{EntityIds: []string{fixtureUserEntityId}},
		mockGetByEntityIds: mockGetByEntityIds{
			enable: true,
			req:    []string{fixtureUserEntityId},
			err:    errors.New("internal error"),
		},
		want: &actorPb.GetActorsByEntityIdsResponse{

			Status: rpc.StatusInternal(),
		},
	},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.mockGetByEntityIds.enable {
				mockActorDao.EXPECT().GetByEntityIds(context.Background(), tt.mockGetByEntityIds.req).
					Return(tt.mockGetByEntityIds.want, tt.mockGetByEntityIds.err)
			}

			got, err := actorService.GetActorsByEntityIds(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetActorsByEntityIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetActorsByEntityIds() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_ResolveOtherActorPiAndTimeline(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockActorDao := actorDaoMocks.NewMockActorDao(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockUsersClient := usersMocks.NewMockUsersClient(ctr)
	mockMerchantClient := merchantMocks.NewMockMerchantServiceClient(ctr)
	mockUpiClient := mock_upi.NewMockUPIClient(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockAuthClient := authMocks.NewMockAuthClient(ctr)
	mockMerchantResolutionPkg := merchantResolutionMock.NewMockMerchantResolutionServer(ctr)
	mockTspUserClient := mocks2.NewMockTspUserServiceClient(ctr)
	ctx := context.Background()
	actorService = actor.NewService(conf, dynConf, mockActorDao, nil, mockPiClient, nil, mockUsersClient, nil, &testMocks.NoTestMockBroker{}, mockMerchantClient, mockUpiClient, nil, mockTimelineClient, mockAuthClient, nil, mockMerchantResolutionPkg, mockTspUserClient)

	type mockGetPi struct {
		enable bool
		req    *piPb.GetPiRequest
		res    *piPb.GetPiResponse
		err    error
	}
	type mockGetDeviceAuth struct {
		enable bool
		req    *authPb.GetDeviceAuthRequest
		res    *authPb.GetDeviceAuthResponse
		err    error
	}
	type mockGetByIdActorDao struct {
		req string
		res *types.Actor
		err error
	}
	type mockGetUser struct {
		req *userPb.GetUserRequest
		res *userPb.GetUserResponse
		err error
	}
	type mockValidateAddressAndCreatePi struct {
		enable bool
		req    *upiPb.ValidateAddressAndCreatePiRequest
		res    *upiPb.ValidateAddressAndCreatePiResponse
		err    error
	}
	type mockGetPiById struct {
		enable bool
		req    *piPb.GetPiByIdRequest
		res    *piPb.GetPiByIdResponse
		err    error
	}
	type mockGetVpaMerchantInfo struct {
		enable bool
		req    *upiPb.GetVpaMerchantInfoRequest
		res    *upiPb.GetVpaMerchantInfoResponse
		err    error
	}
	type mockCreateMerchant struct {
		enable bool
		req    *mPb.CreateMerchantRequest
		res    *mPb.CreateMerchantResponse
		err    error
	}
	type mockGetByEntityIdActorDao struct {
		enable    bool
		entityId  string
		actorType types.Actor_Type
		res       *types.Actor
		err       error
	}
	type mockCreateTimeline struct {
		enable bool
		req    *timelinePb.CreateRequest
		res    *timelinePb.CreateResponse
		err    error
	}
	type mockCreatePi struct {
		enable bool
		req    *piPb.CreatePiRequest
		res    *piPb.CreatePiResponse
		err    error
	}
	type mockCreateCardMerchantInfo struct {
		enable bool
		req    *mPb.CreateCardMerchantInfoRequest
		res    *mPb.CreateCardMerchantInfoResponse
		err    error
	}
	type mockGetAccountMerchantInfo struct {
		enable bool
		req    *mPb.GetAccountMerchantInfoRequest
		res    *mPb.GetAccountMerchantInfoResponse
		err    error
	}
	type mockGetCardMerchantInfo struct {
		enable bool
		req    *mPb.GetCardMerchantInfoRequest
		res    *mPb.GetCardMerchantInfoResponse
		err    error
	}
	type mockGetMerchantPiEntitiesByPiIds struct {
		enable bool
		req    *mPb.GetMerchantPiEntitiesByPiIdsRequest
		res    *mPb.GetMerchantPiEntitiesByPiIdsResponse
		err    error
	}
	type mockMerchantResolution struct {
		enable bool
		req    *merchantResolutionVg.MerchantResolutionRequest
		res    *merchantResolutionVg.MerchantResolutionResponse
		err    error
	}

	tests := []struct {
		name                             string
		req                              *actorPb.ResolveOtherActorPiAndTimelineRequest
		want                             *actorPb.ResolveOtherActorPiAndTimelineResponse
		mockGetPi                        mockGetPi
		mockGetDeviceAuth                mockGetDeviceAuth
		mockGetByIdActorDao              []mockGetByIdActorDao
		mockGetUser                      []mockGetUser
		mockValidateAddressAndCreatePi   mockValidateAddressAndCreatePi
		mockGetPiById                    mockGetPiById
		mockGetVpaMerchantInfo           mockGetVpaMerchantInfo
		mockCreateMerchant               []mockCreateMerchant
		mockGetByEntityIdActorDao        mockGetByEntityIdActorDao
		mockCreateTimeline               mockCreateTimeline
		mockCreatePi                     mockCreatePi
		mockCreateCardMerchantInfo       mockCreateCardMerchantInfo
		mockGetAccountMerchantInfo       mockGetAccountMerchantInfo
		mockGetCardMerchantInfo          mockGetCardMerchantInfo
		mockGetMerchantPiEntitiesByPiIds mockGetMerchantPiEntitiesByPiIds
		mockMerchantResolutionPkg        mockMerchantResolution
		wantErr                          error
	}{
		{
			name: "successfully created pi (of upi type) and timeline",
			req: &actorPb.ResolveOtherActorPiAndTimelineRequest{
				PrimaryActorId: "actor-id",
				PiIdentifier: &actorPb.ResolveOtherActorPiAndTimelineRequest_Vpa{
					Vpa: "other-actor-merchant-vpa",
				},
				PiOwnership:       piPb.Ownership_EPIFI_WEALTH,
				EventType:         types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_DEBIT,
				TimelineOwnership: timelinePb.Ownership_EPIFI_TECH,
			},
			want: &actorPb.ResolveOtherActorPiAndTimelineResponse{
				Status:         rpc.StatusOk(),
				OtherActorPiId: "upi-pi-id",
				TimelineId:     "timeline-id",
			},

			mockGetPi: mockGetPi{
				enable: true,
				req: &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.GetPiRequest_UpiRequestParams_{
						UpiRequestParams: &piPb.GetPiRequest_UpiRequestParams{
							Vpa: "other-actor-merchant-vpa",
						},
					},
				},
				res: &piPb.GetPiResponse{
					Status: rpc.StatusRecordNotFound(),
				},
			},
			mockGetDeviceAuth: mockGetDeviceAuth{
				enable: true,
				req: &authPb.GetDeviceAuthRequest{
					ActorId: "actor-id",
				},
				res: &authPb.GetDeviceAuthResponse{
					Status: rpc.StatusOk(),
					Device: &commontypes.Device{},
				},
			},
			mockGetByIdActorDao: []mockGetByIdActorDao{
				{
					req: "actor-id",
					res: &types.Actor{
						Type:     types.Actor_USER,
						EntityId: "entity-id",
					},
				},
			},
			mockGetUser: []mockGetUser{
				{
					req: &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_Id{
							Id: "entity-id",
						},
						WantProfileImageUrl: true,
					},
					res: &userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								PhoneNumber: &commontypes.PhoneNumber{
									CountryCode:    91,
									NationalNumber: 1234567891,
								},
							},
						},
					},
				},
				{
					req: &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "actor-id",
						},
					},
					res: &userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								KycName: &commontypes.Name{
									FirstName: "primary-actor",
								},
							},
						},
					},
				},
			},
			mockValidateAddressAndCreatePi: mockValidateAddressAndCreatePi{
				enable: true,
				req: &upiPb.ValidateAddressAndCreatePiRequest{
					Device: upiPb.FromFeDevice(context.Background(), &commontypes.Device{}, &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 1234567891,
					}),
					UpiVpa:       "other-actor-merchant-vpa",
					PayerActorId: "actor-id",
					Ownership:    piPb.Ownership_EPIFI_WEALTH,
				},
				res: &upiPb.ValidateAddressAndCreatePiResponse{
					Status:       rpc.StatusOk(),
					PiId:         "upi-pi-id",
					CustomerName: "other-actor-name",
				},
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				req: &piPb.GetPiByIdRequest{
					Id: "upi-pi-id",
				},
				res: &piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "other-actor-merchant-vpa",
							},
						},
					},
				},
			},
			mockGetVpaMerchantInfo: mockGetVpaMerchantInfo{
				enable: true,
				req: &upiPb.GetVpaMerchantInfoRequest{
					Vpa: "other-actor-merchant-vpa",
				},
				res: &upiPb.GetVpaMerchantInfoResponse{
					Status: rpc.StatusOk(),
					VpaMerchantInfo: &upiPb.VpaMerchantInfo{
						MerchantDetails: &upiPb.MerchantDetails{
							LegalName: "legal-merchant-name",
							BrandName: "legal-brand-name",
						},
					},
				},
			},
			mockCreateMerchant: []mockCreateMerchant{
				{
					enable: true,
					req: &mPb.CreateMerchantRequest{
						LegalName: "legal-merchant-name",
						BrandName: "legal-brand-name",
						PiId:      "upi-pi-id",
					},
					res: &mPb.CreateMerchantResponse{
						Status:     rpc.StatusOk(),
						MerchantId: "merchant-id",
					},
				},
			},
			mockGetByEntityIdActorDao: mockGetByEntityIdActorDao{
				enable:    true,
				entityId:  "merchant-id",
				actorType: types.Actor_EXTERNAL_MERCHANT,
				res: &types.Actor{
					Id: "merchant-actor-id",
				},
			},
			mockCreateTimeline: mockCreateTimeline{
				enable: true,
				req: &timelinePb.CreateRequest{
					PrimaryActorId:     "actor-id",
					SecondaryActorId:   "merchant-actor-id",
					PrimaryActorName:   "primary-actor",
					SecondaryActorName: "other-actor-name",
					Ownership:          timelinePb.Ownership_EPIFI_TECH,
				},
				res: &timelinePb.CreateResponse{
					Status: rpc.StatusOk(),
					Timeline: &timelinePb.Timeline{
						Id: "timeline-id",
					},
				},
			},
			mockGetMerchantPiEntitiesByPiIds: mockGetMerchantPiEntitiesByPiIds{
				enable: true,
				req:    &mPb.GetMerchantPiEntitiesByPiIdsRequest{PiIds: []string{"upi-pi-id"}},
				res: &mPb.GetMerchantPiEntitiesByPiIdsResponse{
					Status:                rpc.StatusOk(),
					PiToOldAndMerchantIds: PiToOldAndMerchantIds,
				},
				err: nil,
			},
		},
		{
			name: "successfully fetched pi (of upi type) and timeline",
			req: &actorPb.ResolveOtherActorPiAndTimelineRequest{
				PrimaryActorId: "actor-id",
				PiIdentifier: &actorPb.ResolveOtherActorPiAndTimelineRequest_Vpa{
					Vpa: "other-actor-merchant-vpa",
				},
				PiOwnership:       piPb.Ownership_EPIFI_WEALTH,
				EventType:         types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_DEBIT,
				TimelineOwnership: timelinePb.Ownership_EPIFI_TECH,
			},
			want: &actorPb.ResolveOtherActorPiAndTimelineResponse{
				Status:         rpc.StatusOk(),
				OtherActorPiId: "upi-pi-id",
				TimelineId:     "timeline-id",
			},

			mockGetPi: mockGetPi{
				enable: true,
				req: &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.GetPiRequest_UpiRequestParams_{
						UpiRequestParams: &piPb.GetPiRequest_UpiRequestParams{
							Vpa: "other-actor-merchant-vpa",
						},
					},
				},
				res: &piPb.GetPiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:           "upi-pi-id",
						VerifiedName: "other-actor-name",
					},
				},
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				req: &piPb.GetPiByIdRequest{
					Id: "upi-pi-id",
				},
				res: &piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "other-actor-merchant-vpa",
							},
						},
					},
				},
			},
			mockGetVpaMerchantInfo: mockGetVpaMerchantInfo{
				enable: true,
				req: &upiPb.GetVpaMerchantInfoRequest{
					Vpa: "other-actor-merchant-vpa",
				},
				res: &upiPb.GetVpaMerchantInfoResponse{
					Status: rpc.StatusOk(),
					VpaMerchantInfo: &upiPb.VpaMerchantInfo{
						MerchantDetails: &upiPb.MerchantDetails{
							LegalName: "legal-merchant-name",
							BrandName: "legal-brand-name",
						},
					},
				},
			},
			mockGetUser: []mockGetUser{
				{
					req: &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "actor-id",
						},
					},
					res: &userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								KycName: &commontypes.Name{
									FirstName: "primary-actor",
								},
							},
						},
					},
				},
			},
			mockCreateMerchant: []mockCreateMerchant{
				{
					enable: true,
					req: &mPb.CreateMerchantRequest{
						LegalName: "legal-merchant-name",
						BrandName: "legal-brand-name",
						PiId:      "upi-pi-id",
					},
					res: &mPb.CreateMerchantResponse{
						Status:     rpc.StatusOk(),
						MerchantId: "merchant-id",
					},
				},
			},
			mockGetByEntityIdActorDao: mockGetByEntityIdActorDao{
				enable:    true,
				entityId:  "merchant-id",
				actorType: types.Actor_EXTERNAL_MERCHANT,
				res: &types.Actor{
					Id: "merchant-actor-id",
				},
			},
			mockCreateTimeline: mockCreateTimeline{
				enable: true,
				req: &timelinePb.CreateRequest{
					PrimaryActorId:     "actor-id",
					SecondaryActorId:   "merchant-actor-id",
					PrimaryActorName:   "primary-actor",
					SecondaryActorName: "other-actor-name",
					Ownership:          timelinePb.Ownership_EPIFI_TECH,
				},
				res: &timelinePb.CreateResponse{
					Status: rpc.StatusOk(),
					Timeline: &timelinePb.Timeline{
						Id: "timeline-id",
					},
				},
			},
			mockGetMerchantPiEntitiesByPiIds: mockGetMerchantPiEntitiesByPiIds{
				enable: true,
				req:    &mPb.GetMerchantPiEntitiesByPiIdsRequest{PiIds: []string{"upi-pi-id"}},
				res: &mPb.GetMerchantPiEntitiesByPiIdsResponse{
					Status:                rpc.StatusOk(),
					PiToOldAndMerchantIds: PiToOldAndMerchantIds,
				},
				err: nil,
			},
		},
		{
			name: "successfully created pi (of account type) and timeline",
			req: &actorPb.ResolveOtherActorPiAndTimelineRequest{
				PrimaryActorId: "actor-id",
				PiIdentifier: &actorPb.ResolveOtherActorPiAndTimelineRequest_Account{
					Account: &actorPb.ResolveOtherActorPiAndTimelineRequest_AccountIdentifier{
						AccountNumber: "random-account-number",
						IfscCode:      "random-ifsc-code",
						PiType:        piPb.PaymentInstrumentType_BANK_ACCOUNT,
						AccountType:   accountsPb.Type_SAVINGS,
					},
				},
				PiOwnership:         piPb.Ownership_EPIFI_WEALTH,
				EventType:           types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_DEBIT,
				TimelineOwnership:   timelinePb.Ownership_EPIFI_TECH,
				PaymentProtocol:     payment.PaymentProtocol_CARD,
				TransactionCategory: oldtypes.TransactionCategory_ECOMM,
				MerchantDetails: &actorPb.MerchantDetails{
					Name:        pay.DefaultActorName,
					MccCode:     "mcc-code",
					Mid:         "mcc-mid",
					CountryCode: "+91",
					City:        "random-city",
				},
			},
			want: &actorPb.ResolveOtherActorPiAndTimelineResponse{
				Status:         rpc.StatusOk(),
				OtherActorPiId: "account-pi-id",
				TimelineId:     "timeline-id",
			},

			mockCreatePi: mockCreatePi{
				enable: true,
				req: &piPb.CreatePiRequest{
					Type:         piPb.PaymentInstrumentType_BANK_ACCOUNT,
					VerifiedName: pay.DefaultActorName,
					Identifier: &piPb.CreatePiRequest_Account{
						Account: &piPb.Account{
							ActualAccountNumber: "random-account-number",
							IfscCode:            "random-ifsc-code",
							AccountType:         accountsPb.Type_SAVINGS,
							Name:                pay.DefaultActorName,
							SecureAccountNumber: mask.MaskLastNDigits("random-account-number", 3, ""),
						},
					},
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
					Ownership:            piPb.Ownership_EPIFI_WEALTH,
					IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
				},
				res: &piPb.CreatePiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id: "account-pi-id",
					},
				},
			},
			//	now move on for the creation of the timeline
			mockGetPiById: mockGetPiById{
				enable: true,
				req: &piPb.GetPiByIdRequest{
					Id: "account-pi-id",
				},
				res: &piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:   "account-pi-id",
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					},
				},
			},
			mockGetAccountMerchantInfo: mockGetAccountMerchantInfo{
				enable: true,
				req: &mPb.GetAccountMerchantInfoRequest{
					PiId: "account-pi-id",
				},
				res: &mPb.GetAccountMerchantInfoResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetUser: []mockGetUser{
				{
					req: &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "actor-id",
						},
					},
					res: &userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								KycName: &commontypes.Name{
									FirstName: "primary-actor",
								},
							},
						},
					},
				},
			},
			mockCreateMerchant: []mockCreateMerchant{
				{
					enable: true,
					req: &mPb.CreateMerchantRequest{
						LegalName: "",
						BrandName: "",
						PiId:      "account-pi-id",
					},
					res: &mPb.CreateMerchantResponse{
						Status:     rpc.StatusOk(),
						MerchantId: "merchant-id",
					},
				},
			},
			mockGetByEntityIdActorDao: mockGetByEntityIdActorDao{
				enable:    true,
				entityId:  "merchant-id",
				actorType: types.Actor_EXTERNAL_MERCHANT,
				res: &types.Actor{
					Id: "merchant-actor-id",
				},
			},
			mockCreateTimeline: mockCreateTimeline{
				enable: true,
				req: &timelinePb.CreateRequest{
					PrimaryActorId:     "actor-id",
					SecondaryActorId:   "merchant-actor-id",
					PrimaryActorName:   "primary-actor",
					SecondaryActorName: pay.DefaultActorName,
					Ownership:          timelinePb.Ownership_EPIFI_TECH,
				},
				res: &timelinePb.CreateResponse{
					Status: rpc.StatusOk(),
					Timeline: &timelinePb.Timeline{
						Id: "timeline-id",
					},
				},
			},
			mockGetMerchantPiEntitiesByPiIds: mockGetMerchantPiEntitiesByPiIds{
				enable: true,
				req:    &mPb.GetMerchantPiEntitiesByPiIdsRequest{PiIds: []string{"account-pi-id"}},
				res: &mPb.GetMerchantPiEntitiesByPiIdsResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
		},
		{
			name: "successfully created pi for card merchant (of account type) and timeline when mid is present",
			req: &actorPb.ResolveOtherActorPiAndTimelineRequest{
				PrimaryActorId: "actor-id",
				PiIdentifier: &actorPb.ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier_{
					&actorPb.ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier{
						Mid: "mcc-mid",
					},
				},
				PiOwnership:         piPb.Ownership_EPIFI_WEALTH,
				EventType:           types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_DEBIT,
				TimelineOwnership:   timelinePb.Ownership_EPIFI_TECH,
				PaymentProtocol:     payment.PaymentProtocol_CARD,
				TransactionCategory: oldtypes.TransactionCategory_ECOMM,
				MerchantDetails: &actorPb.MerchantDetails{
					Name:        pay.DefaultActorName,
					MccCode:     "mcc-code",
					Mid:         "mcc-mid",
					CountryCode: "+91",
					City:        "random-city",
				},
			},
			want: &actorPb.ResolveOtherActorPiAndTimelineResponse{
				Status:         rpc.StatusOk(),
				OtherActorPiId: "card-account-pi-id",
				TimelineId:     "timeline-id",
			},

			mockCreatePi: mockCreatePi{
				enable: true,
				req: &piPb.CreatePiRequest{
					Type:         piPb.PaymentInstrumentType_GENERIC,
					VerifiedName: pay.DefaultActorName,
					Identifier: &piPb.CreatePiRequest_Account{
						Account: &piPb.Account{
							ActualAccountNumber: crypto.GetSHA1InBase32("mcc-mid"),
							IfscCode:            pay.DefaultIfscCardTxn,
							AccountType:         accountsPb.Type_SAVINGS,
							Name:                pay.DefaultActorName,
							SecureAccountNumber: mask.MaskLastNDigits(crypto.GetSHA1InBase32("mcc-mid"), 3, ""),
						},
					},
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  false,
						piPb.Capability_OUTBOUND_TXN.String(): false,
					},
					Ownership:            piPb.Ownership_EPIFI_WEALTH,
					IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
				},
				res: &piPb.CreatePiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id: "card-account-pi-id",
					},
				},
			},
			mockCreateCardMerchantInfo: mockCreateCardMerchantInfo{
				enable: true,
				req: &mPb.CreateCardMerchantInfoRequest{
					Name: pay.DefaultActorName,
					PiId: "card-account-pi-id",
					Mcc:  "mcc-code",
					Mid:  "mcc-mid",
					Address: &types.PostalAddress{
						RegionCode:         "+91",
						AdministrativeArea: "random-city",
					},
				},
				res: &mPb.CreateCardMerchantInfoResponse{
					Status: rpc.StatusOk(),
				},
			},
			//	now move on for the creation of the timeline
			mockGetPiById: mockGetPiById{
				enable: true,
				req: &piPb.GetPiByIdRequest{
					Id: "card-account-pi-id",
				},
				res: &piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:   "card-account-pi-id",
						Type: piPb.PaymentInstrumentType_GENERIC,
					},
				},
			},
			mockGetCardMerchantInfo: mockGetCardMerchantInfo{
				enable: true,
				req: &mPb.GetCardMerchantInfoRequest{
					PiId: "card-account-pi-id",
				},
				res: &mPb.GetCardMerchantInfoResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetUser: []mockGetUser{
				{
					req: &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "actor-id",
						},
					},
					res: &userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								KycName: &commontypes.Name{
									FirstName: "primary-actor",
								},
							},
						},
					},
				},
			},
			mockCreateMerchant: []mockCreateMerchant{
				{
					enable: true,
					req: &mPb.CreateMerchantRequest{
						LegalName: "",
						BrandName: "",
						PiId:      "card-account-pi-id",
					},
					res: &mPb.CreateMerchantResponse{
						Status:     rpc.StatusOk(),
						MerchantId: "merchant-id",
					},
				},
			},
			mockGetByEntityIdActorDao: mockGetByEntityIdActorDao{
				enable:    true,
				entityId:  "merchant-id",
				actorType: types.Actor_EXTERNAL_MERCHANT,
				res: &types.Actor{
					Id: "merchant-actor-id",
				},
			},
			mockCreateTimeline: mockCreateTimeline{
				enable: true,
				req: &timelinePb.CreateRequest{
					PrimaryActorId:     "actor-id",
					SecondaryActorId:   "merchant-actor-id",
					PrimaryActorName:   "primary-actor",
					SecondaryActorName: pay.DefaultActorName,
					Ownership:          timelinePb.Ownership_EPIFI_TECH,
				},
				res: &timelinePb.CreateResponse{
					Status: rpc.StatusOk(),
					Timeline: &timelinePb.Timeline{
						Id: "timeline-id",
					},
				},
			},
			mockGetMerchantPiEntitiesByPiIds: mockGetMerchantPiEntitiesByPiIds{
				enable: true,
				req:    &mPb.GetMerchantPiEntitiesByPiIdsRequest{PiIds: []string{"card-account-pi-id"}},
				res: &mPb.GetMerchantPiEntitiesByPiIdsResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
		},
		{
			name: "successfully created pi for card merchant (of account type) and timeline when other actor name is present",
			req: &actorPb.ResolveOtherActorPiAndTimelineRequest{
				PrimaryActorId: "actor-id",
				PiIdentifier: &actorPb.ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier_{
					&actorPb.ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier{
						OtherActorName: "other-actor-name",
					},
				},
				OtherActorName:      "other-actor-name",
				PiOwnership:         piPb.Ownership_EPIFI_WEALTH,
				EventType:           types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_DEBIT,
				TimelineOwnership:   timelinePb.Ownership_EPIFI_TECH,
				PaymentProtocol:     payment.PaymentProtocol_CARD,
				TransactionCategory: oldtypes.TransactionCategory_ECOMM,
				MerchantDetails: &actorPb.MerchantDetails{
					Name:        "other-actor-name",
					MccCode:     "mcc-code",
					CountryCode: "+91",
					City:        "random-city",
				},
			},
			want: &actorPb.ResolveOtherActorPiAndTimelineResponse{
				Status:         rpc.StatusOk(),
				OtherActorPiId: "card-account-pi-id",
				TimelineId:     "timeline-id",
			},

			mockCreatePi: mockCreatePi{
				enable: true,
				req: &piPb.CreatePiRequest{
					Type:         piPb.PaymentInstrumentType_GENERIC,
					VerifiedName: "other-actor-name",
					Identifier: &piPb.CreatePiRequest_Account{
						Account: &piPb.Account{
							ActualAccountNumber: crypto.GetSHA1InBase32("other-actor-name"),
							IfscCode:            pay.DefaultIfscCardTxn,
							AccountType:         accountsPb.Type_SAVINGS,
							Name:                "other-actor-name",
							SecureAccountNumber: mask.MaskLastNDigits(crypto.GetSHA1InBase32("other-actor-name"), 3, ""),
						},
					},
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  false,
						piPb.Capability_OUTBOUND_TXN.String(): false,
					},
					Ownership:            piPb.Ownership_EPIFI_WEALTH,
					IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
				},
				res: &piPb.CreatePiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id: "card-account-pi-id",
					},
				},
			},
			mockCreateCardMerchantInfo: mockCreateCardMerchantInfo{
				enable: true,
				req: &mPb.CreateCardMerchantInfoRequest{
					Name: "other-actor-name",
					PiId: "card-account-pi-id",
					Mcc:  "mcc-code",
					Mid:  pay.DefaultMIDForCardTxns,
					Address: &types.PostalAddress{
						RegionCode:         "+91",
						AdministrativeArea: "random-city",
					},
				},
				res: &mPb.CreateCardMerchantInfoResponse{
					Status: rpc.StatusOk(),
				},
			},
			//	now move on for the creation of the timeline
			mockGetPiById: mockGetPiById{
				enable: true,
				req: &piPb.GetPiByIdRequest{
					Id: "card-account-pi-id",
				},
				res: &piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:   "card-account-pi-id",
						Type: piPb.PaymentInstrumentType_GENERIC,
					},
				},
			},
			mockGetCardMerchantInfo: mockGetCardMerchantInfo{
				enable: true,
				req: &mPb.GetCardMerchantInfoRequest{
					PiId: "card-account-pi-id",
				},
				res: &mPb.GetCardMerchantInfoResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetUser: []mockGetUser{
				{
					req: &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "actor-id",
						},
					},
					res: &userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								KycName: &commontypes.Name{
									FirstName: "primary-actor",
								},
							},
						},
					},
				},
			},
			mockCreateMerchant: []mockCreateMerchant{
				{
					enable: true,
					req: &mPb.CreateMerchantRequest{
						LegalName: "",
						BrandName: "",
						PiId:      "card-account-pi-id",
					},
					res: &mPb.CreateMerchantResponse{
						Status:     rpc.StatusOk(),
						MerchantId: "merchant-id",
					},
				},
			},
			mockGetByEntityIdActorDao: mockGetByEntityIdActorDao{
				enable:    true,
				entityId:  "merchant-id",
				actorType: types.Actor_EXTERNAL_MERCHANT,
				res: &types.Actor{
					Id: "merchant-actor-id",
				},
			},
			mockCreateTimeline: mockCreateTimeline{
				enable: true,
				req: &timelinePb.CreateRequest{
					PrimaryActorId:     "actor-id",
					SecondaryActorId:   "merchant-actor-id",
					PrimaryActorName:   "primary-actor",
					SecondaryActorName: "other-actor-name",
					Ownership:          timelinePb.Ownership_EPIFI_TECH,
				},
				res: &timelinePb.CreateResponse{
					Status: rpc.StatusOk(),
					Timeline: &timelinePb.Timeline{
						Id: "timeline-id",
					},
				},
			},
			mockGetMerchantPiEntitiesByPiIds: mockGetMerchantPiEntitiesByPiIds{
				enable: true,
				req:    &mPb.GetMerchantPiEntitiesByPiIdsRequest{PiIds: []string{"card-account-pi-id"}},
				res: &mPb.GetMerchantPiEntitiesByPiIdsResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
		},
		{
			name: "successfully created pi for ATM merchant (of account type) and timeline",
			req: &actorPb.ResolveOtherActorPiAndTimelineRequest{
				PrimaryActorId:      "actor-id",
				OtherActorName:      "ATM",
				PiOwnership:         piPb.Ownership_EPIFI_WEALTH,
				EventType:           types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_DEBIT,
				TimelineOwnership:   timelinePb.Ownership_EPIFI_TECH,
				PaymentProtocol:     payment.PaymentProtocol_CARD,
				TransactionCategory: oldtypes.TransactionCategory_ATM_WITHDRAWAL,
				MerchantDetails: &actorPb.MerchantDetails{
					MccCode:     "mcc-code",
					CountryCode: "+91",
					City:        "random-city",
					Mid:         "mcc-mid",
					Tid:         "mcc-tid",
				},
				AtmAddress: "ATMAddress",
			},
			want: &actorPb.ResolveOtherActorPiAndTimelineResponse{
				Status:         rpc.StatusOk(),
				OtherActorPiId: "card-account-pi-id",
				TimelineId:     "timeline-id",
			},

			mockCreatePi: mockCreatePi{
				enable: true,
				req: &piPb.CreatePiRequest{
					Type:         piPb.PaymentInstrumentType_GENERIC,
					VerifiedName: "ATM",
					Identifier: &piPb.CreatePiRequest_Account{
						Account: &piPb.Account{
							ActualAccountNumber: merchant.CreateUniqueAccountNumber("mcc-mid", "mcc-tid", "ATMAddress", "random-city", "+91"),
							IfscCode:            pay.DefaultIfscCardTxn,
							AccountType:         accountsPb.Type_SAVINGS,
							Name:                "ATM",
							SecureAccountNumber: mask.MaskLastNDigits(merchant.CreateUniqueAccountNumber("mcc-mid", "mcc-tid", "ATMAddress", "random-city", "+91"), 3, ""),
						},
					},
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  false,
						piPb.Capability_OUTBOUND_TXN.String(): false,
					},
					Ownership:            piPb.Ownership_EPIFI_WEALTH,
					IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
				},
				res: &piPb.CreatePiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id: "card-account-pi-id",
					},
				},
			},
			mockCreateCardMerchantInfo: mockCreateCardMerchantInfo{
				enable: true,
				req: &mPb.CreateCardMerchantInfoRequest{
					Name: "ATM",
					PiId: "card-account-pi-id",
					Mcc:  "mcc-code",
					Mid:  "mcc-mid",
					Address: &types.PostalAddress{
						RegionCode:         "+91",
						AdministrativeArea: "random-city",
					},
				},
				res: &mPb.CreateCardMerchantInfoResponse{
					Status: rpc.StatusOk(),
				},
			},
			//	now move on for the creation of the timeline
			mockGetPiById: mockGetPiById{
				enable: true,
				req: &piPb.GetPiByIdRequest{
					Id: "card-account-pi-id",
				},
				res: &piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:   "card-account-pi-id",
						Type: piPb.PaymentInstrumentType_GENERIC,
					},
				},
			},
			mockGetCardMerchantInfo: mockGetCardMerchantInfo{
				enable: true,
				req: &mPb.GetCardMerchantInfoRequest{
					PiId: "card-account-pi-id",
				},
				res: &mPb.GetCardMerchantInfoResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetUser: []mockGetUser{
				{
					req: &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "actor-id",
						},
					},
					res: &userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								KycName: &commontypes.Name{
									FirstName: "primary-actor",
								},
							},
						},
					},
				},
			},
			mockCreateMerchant: []mockCreateMerchant{
				{
					enable: true,
					req: &mPb.CreateMerchantRequest{
						LegalName: "",
						BrandName: "ATM",
						PiId:      "card-account-pi-id",
					},
					res: &mPb.CreateMerchantResponse{
						Status:     rpc.StatusOk(),
						MerchantId: "merchant-id",
					},
				},
			},
			mockGetByEntityIdActorDao: mockGetByEntityIdActorDao{
				enable:    true,
				entityId:  "merchant-id",
				actorType: types.Actor_EXTERNAL_MERCHANT,
				res: &types.Actor{
					Id: "merchant-actor-id",
				},
			},
			mockCreateTimeline: mockCreateTimeline{
				enable: true,
				req: &timelinePb.CreateRequest{
					PrimaryActorId:     "actor-id",
					SecondaryActorId:   "merchant-actor-id",
					PrimaryActorName:   "primary-actor",
					SecondaryActorName: "ATM",
					Ownership:          timelinePb.Ownership_EPIFI_TECH,
				},
				res: &timelinePb.CreateResponse{
					Status: rpc.StatusOk(),
					Timeline: &timelinePb.Timeline{
						Id: "timeline-id",
					},
				},
			},
		},
		{
			name: "Invalid argument for the card payment",
			req: &actorPb.ResolveOtherActorPiAndTimelineRequest{
				PrimaryActorId: "actor-id",
				PiIdentifier: &actorPb.ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier_{
					&actorPb.ResolveOtherActorPiAndTimelineRequest_CardTransactionIdentifier{},
				},
				OtherActorName: "other-actor-name",
				PiOwnership:    piPb.Ownership_EPIFI_WEALTH,
				EventType:      types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_DEBIT,
			},
			want: &actorPb.ResolveOtherActorPiAndTimelineResponse{
				Status: rpc.StatusInvalidArgument(),
			},

			mockGetMerchantPiEntitiesByPiIds: mockGetMerchantPiEntitiesByPiIds{enable: false},
		},
		{
			name: "failed to resolve the other actor pi details",
			req: &actorPb.ResolveOtherActorPiAndTimelineRequest{
				PrimaryActorId: "actor-id",
				PiIdentifier: &actorPb.ResolveOtherActorPiAndTimelineRequest_Vpa{
					Vpa: "other-actor-merchant-vpa",
				},
				PiOwnership:       piPb.Ownership_EPIFI_WEALTH,
				EventType:         types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_DEBIT,
				TimelineOwnership: timelinePb.Ownership_EPIFI_TECH,
			},
			want: &actorPb.ResolveOtherActorPiAndTimelineResponse{
				Status: rpc.StatusInternal(),
			},

			mockGetPi: mockGetPi{
				enable: true,
				req: &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.GetPiRequest_UpiRequestParams_{
						UpiRequestParams: &piPb.GetPiRequest_UpiRequestParams{
							Vpa: "other-actor-merchant-vpa",
						},
					},
				},
				res: &piPb.GetPiResponse{
					Status: rpc.StatusInternal(),
				},
			},
			mockGetMerchantPiEntitiesByPiIds: mockGetMerchantPiEntitiesByPiIds{enable: false},
		},
		{
			name: "failed to resolve the timeline",
			req: &actorPb.ResolveOtherActorPiAndTimelineRequest{
				PrimaryActorId: "actor-id",
				PiIdentifier: &actorPb.ResolveOtherActorPiAndTimelineRequest_Vpa{
					Vpa: "other-actor-merchant-vpa",
				},
				PiOwnership:       piPb.Ownership_EPIFI_WEALTH,
				EventType:         types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_DEBIT,
				TimelineOwnership: timelinePb.Ownership_EPIFI_TECH,
			},
			want: &actorPb.ResolveOtherActorPiAndTimelineResponse{
				Status: rpc.StatusInternal(),
			},

			mockGetPi: mockGetPi{
				enable: true,
				req: &piPb.GetPiRequest{
					Type: piPb.PaymentInstrumentType_UPI,
					Identifier: &piPb.GetPiRequest_UpiRequestParams_{
						UpiRequestParams: &piPb.GetPiRequest_UpiRequestParams{
							Vpa: "other-actor-merchant-vpa",
						},
					},
				},
				res: &piPb.GetPiResponse{
					Status: rpc.StatusRecordNotFound(),
				},
			},
			mockGetDeviceAuth: mockGetDeviceAuth{
				enable: true,
				req: &authPb.GetDeviceAuthRequest{
					ActorId: "actor-id",
				},
				res: &authPb.GetDeviceAuthResponse{
					Status: rpc.StatusOk(),
					Device: &commontypes.Device{},
				},
			},
			mockGetByIdActorDao: []mockGetByIdActorDao{
				{
					req: "actor-id",
					res: &types.Actor{
						Type:     types.Actor_USER,
						EntityId: "entity-id",
					},
				},
			},
			mockGetUser: []mockGetUser{
				{
					req: &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_Id{
							Id: "entity-id",
						},
						WantProfileImageUrl: true,
					},
					res: &userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								PhoneNumber: &commontypes.PhoneNumber{
									CountryCode:    91,
									NationalNumber: 1234567891,
								},
							},
						},
					},
				},
				{
					req: &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "actor-id",
						},
					},
					res: &userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								KycName: &commontypes.Name{
									FirstName: "primary-actor",
								},
							},
						},
					},
				},
			},
			mockValidateAddressAndCreatePi: mockValidateAddressAndCreatePi{
				enable: true,
				req: &upiPb.ValidateAddressAndCreatePiRequest{
					Device: upiPb.FromFeDevice(context.Background(), &commontypes.Device{}, &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: 1234567891,
					}),
					UpiVpa:       "other-actor-merchant-vpa",
					PayerActorId: "actor-id",
					Ownership:    piPb.Ownership_EPIFI_WEALTH,
				},
				res: &upiPb.ValidateAddressAndCreatePiResponse{
					Status:       rpc.StatusOk(),
					PiId:         "upi-pi-id",
					CustomerName: "other-actor-name",
				},
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				req: &piPb.GetPiByIdRequest{
					Id: "upi-pi-id",
				},
				res: &piPb.GetPiByIdResponse{
					Status: rpc.StatusInternal(),
				},
			},
			mockGetMerchantPiEntitiesByPiIds: mockGetMerchantPiEntitiesByPiIds{enable: false},
		},
		{
			name: "successfully created pi (of account type) and timeline for ecs enach mandate",
			req: &actorPb.ResolveOtherActorPiAndTimelineRequest{
				PrimaryActorId: "actor-id",
				PiIdentifier: &actorPb.ResolveOtherActorPiAndTimelineRequest_Account{
					Account: &actorPb.ResolveOtherActorPiAndTimelineRequest_AccountIdentifier{
						AccountNumber: "random-account-number",
						IfscCode:      "random-ifsc-code",
						PiType:        piPb.PaymentInstrumentType_BANK_ACCOUNT,
						AccountType:   accountsPb.Type_SAVINGS,
					},
				},
				PiOwnership:         piPb.Ownership_EPIFI_WEALTH,
				EventType:           types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_DEBIT,
				TimelineOwnership:   timelinePb.Ownership_EPIFI_TECH,
				PaymentProtocol:     payment.PaymentProtocol_ENACH,
				TransactionCategory: oldtypes.TransactionCategory_ECOMM,
				MerchantDetails: &actorPb.MerchantDetails{
					Name:        pay.DefaultActorName,
					MccCode:     "mcc-code",
					Mid:         "mcc-mid",
					CountryCode: "+91",
					City:        "random-city",
				},
				OtherActorName: "other-actor-name",
				EcsEnachDetails: &notification.EcsEnachMandateDetails{
					MandateCategory: notification.EcsEnachMandateDetails_ENACHE,
				},
			},
			want: &actorPb.ResolveOtherActorPiAndTimelineResponse{
				Status:         rpc.StatusOk(),
				OtherActorPiId: "account-pi-id",
				TimelineId:     "timeline-id",
			},

			mockCreatePi: mockCreatePi{
				enable: true,
				req: &piPb.CreatePiRequest{
					Type:         piPb.PaymentInstrumentType_BANK_ACCOUNT,
					VerifiedName: "ecs-merchant-name",
					Identifier: &piPb.CreatePiRequest_Account{
						Account: &piPb.Account{
							ActualAccountNumber: "PNBC5361U85BE3RLGFIM09OK8H729FS4",
							IfscCode:            "random-ifsc-code",
							AccountType:         accountsPb.Type_SAVINGS,
							Name:                "ecs-merchant-name",
							SecureAccountNumber: mask.MaskLastNDigits("PNBC5361U85BE3RLGFIM09OK8H729FS4", 3, ""),
						},
					},
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
					Ownership:            piPb.Ownership_EPIFI_WEALTH,
					IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
				},
				res: &piPb.CreatePiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id: "account-pi-id",
					},
				},
			},
			//	now move on for the creation of the timeline
			mockGetPiById: mockGetPiById{
				enable: true,
				req: &piPb.GetPiByIdRequest{
					Id: "account-pi-id",
				},
				res: &piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:   "account-pi-id",
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					},
				},
			},
			mockGetAccountMerchantInfo: mockGetAccountMerchantInfo{
				enable: true,
				req: &mPb.GetAccountMerchantInfoRequest{
					PiId: "account-pi-id",
				},
				res: &mPb.GetAccountMerchantInfoResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetUser: []mockGetUser{
				{
					req: &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "actor-id",
						},
					},
					res: &userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								KycName: &commontypes.Name{
									FirstName: "primary-actor",
								},
							},
						},
					},
				},
			},
			mockCreateMerchant: []mockCreateMerchant{
				{
					enable: true,
					req: &mPb.CreateMerchantRequest{
						LegalName: "ecs-merchant-name",
						BrandName: "ecs-merchant-name",
						PiId:      "account-pi-id",
					},
					res: &mPb.CreateMerchantResponse{
						Status:     rpc.StatusOk(),
						MerchantId: "merchant-id",
					},
				},
				{
					enable: true,
					req: &mPb.CreateMerchantRequest{
						PiId: "account-pi-id",
					},
					res: &mPb.CreateMerchantResponse{
						Status:     rpc.StatusOk(),
						MerchantId: "merchant-id",
					},
				},
			},
			mockGetByEntityIdActorDao: mockGetByEntityIdActorDao{
				enable:    true,
				entityId:  "merchant-id",
				actorType: types.Actor_EXTERNAL_MERCHANT,
				res: &types.Actor{
					Id: "merchant-actor-id",
				},
			},
			mockCreateTimeline: mockCreateTimeline{
				enable: true,
				req: &timelinePb.CreateRequest{
					PrimaryActorId:     "actor-id",
					SecondaryActorId:   "merchant-actor-id",
					PrimaryActorName:   "primary-actor",
					SecondaryActorName: "ecs-merchant-name",
					Ownership:          timelinePb.Ownership_EPIFI_TECH,
				},
				res: &timelinePb.CreateResponse{
					Status: rpc.StatusOk(),
					Timeline: &timelinePb.Timeline{
						Id: "timeline-id",
					},
				},
			},
			mockGetMerchantPiEntitiesByPiIds: mockGetMerchantPiEntitiesByPiIds{
				enable: true,
				req:    &mPb.GetMerchantPiEntitiesByPiIdsRequest{PiIds: []string{"account-pi-id"}},
				res: &mPb.GetMerchantPiEntitiesByPiIdsResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockMerchantResolutionPkg: mockMerchantResolution{
				enable: true,
				req: &merchantResolutionVg.MerchantResolutionRequest{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
					RawMerchantName: "other-actor-name",
					IsFiTxn:         true,
					IsMerchantTxn:   true,
				},
				res: &merchantResolutionVg.MerchantResolutionResponse{
					Status:       rpc.StatusOk(),
					MerchantName: "ecs-merchant-name",
				},
			},
		},
		{
			name: "successfully created pi (of account type) and timeline for ecs enach mandate using merchantResolutionPkg",
			req: &actorPb.ResolveOtherActorPiAndTimelineRequest{
				PrimaryActorId: "actor-id",
				PiIdentifier: &actorPb.ResolveOtherActorPiAndTimelineRequest_Account{
					Account: &actorPb.ResolveOtherActorPiAndTimelineRequest_AccountIdentifier{
						AccountNumber: "random-account-number",
						IfscCode:      "random-ifsc-code",
						PiType:        piPb.PaymentInstrumentType_BANK_ACCOUNT,
						AccountType:   accountsPb.Type_SAVINGS,
					},
				},
				PiOwnership:         piPb.Ownership_EPIFI_WEALTH,
				EventType:           types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_DEBIT,
				TimelineOwnership:   timelinePb.Ownership_EPIFI_TECH,
				PaymentProtocol:     payment.PaymentProtocol_ENACH,
				TransactionCategory: oldtypes.TransactionCategory_ECOMM,
				MerchantDetails: &actorPb.MerchantDetails{
					Name:        pay.DefaultActorName,
					MccCode:     "mcc-code",
					Mid:         "mcc-mid",
					CountryCode: "+91",
					City:        "random-city",
				},
				OtherActorName: "other-actor-name",
				EcsEnachDetails: &notification.EcsEnachMandateDetails{
					MandateCategory: notification.EcsEnachMandateDetails_ENACHE,
				},
			},
			want: &actorPb.ResolveOtherActorPiAndTimelineResponse{
				Status:         rpc.StatusOk(),
				OtherActorPiId: "account-pi-id",
				TimelineId:     "timeline-id",
			},
			mockCreatePi: mockCreatePi{
				enable: true,
				req: &piPb.CreatePiRequest{
					Type:         piPb.PaymentInstrumentType_BANK_ACCOUNT,
					VerifiedName: "ecs-merchant-name",
					Identifier: &piPb.CreatePiRequest_Account{
						Account: &piPb.Account{
							ActualAccountNumber: "PNBC5361U85BE3RLGFIM09OK8H729FS4",
							IfscCode:            "random-ifsc-code",
							AccountType:         accountsPb.Type_SAVINGS,
							Name:                "ecs-merchant-name",
							SecureAccountNumber: mask.MaskLastNDigits("PNBC5361U85BE3RLGFIM09OK8H729FS4", 3, ""),
						},
					},
					Capabilities: map[string]bool{
						piPb.Capability_INBOUND_TXN.String():  true,
						piPb.Capability_OUTBOUND_TXN.String(): true,
					},
					Ownership:            piPb.Ownership_EPIFI_WEALTH,
					IssuerClassification: piPb.PaymentInstrumentIssuer_EXTERNAL,
				},
				res: &piPb.CreatePiResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id: "account-pi-id",
					},
				},
			},
			//	now move on for the creation of the timeline
			mockGetPiById: mockGetPiById{
				enable: true,
				req: &piPb.GetPiByIdRequest{
					Id: "account-pi-id",
				},
				res: &piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:   "account-pi-id",
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
					},
				},
			},
			mockGetAccountMerchantInfo: mockGetAccountMerchantInfo{
				enable: true,
				req: &mPb.GetAccountMerchantInfoRequest{
					PiId: "account-pi-id",
				},
				res: &mPb.GetAccountMerchantInfoResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetUser: []mockGetUser{
				{
					req: &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "actor-id",
						},
					},
					res: &userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								KycName: &commontypes.Name{
									FirstName: "primary-actor",
								},
							},
						},
					},
				},
			},
			mockCreateMerchant: []mockCreateMerchant{
				{
					enable: true,
					req: &mPb.CreateMerchantRequest{
						LegalName: "ecs-merchant-name",
						BrandName: "ecs-merchant-name",
						PiId:      "account-pi-id",
					},
					res: &mPb.CreateMerchantResponse{
						Status:     rpc.StatusOk(),
						MerchantId: "merchant-id",
					},
				},
				{
					enable: true,
					req: &mPb.CreateMerchantRequest{
						PiId: "account-pi-id",
					},
					res: &mPb.CreateMerchantResponse{
						Status:     rpc.StatusOk(),
						MerchantId: "merchant-id",
					},
				},
			},
			mockGetByEntityIdActorDao: mockGetByEntityIdActorDao{
				enable:    true,
				entityId:  "merchant-id",
				actorType: types.Actor_EXTERNAL_MERCHANT,
				res: &types.Actor{
					Id: "merchant-actor-id",
				},
			},
			mockCreateTimeline: mockCreateTimeline{
				enable: true,
				req: &timelinePb.CreateRequest{
					PrimaryActorId:     "actor-id",
					SecondaryActorId:   "merchant-actor-id",
					PrimaryActorName:   "primary-actor",
					SecondaryActorName: "ecs-merchant-name",
					Ownership:          timelinePb.Ownership_EPIFI_TECH,
				},
				res: &timelinePb.CreateResponse{
					Status: rpc.StatusOk(),
					Timeline: &timelinePb.Timeline{
						Id: "timeline-id",
					},
				},
			},
			mockGetMerchantPiEntitiesByPiIds: mockGetMerchantPiEntitiesByPiIds{
				enable: true,
				req:    &mPb.GetMerchantPiEntitiesByPiIdsRequest{PiIds: []string{"account-pi-id"}},
				res: &mPb.GetMerchantPiEntitiesByPiIdsResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockMerchantResolutionPkg: mockMerchantResolution{
				enable: true,
				req: &merchantResolutionVg.MerchantResolutionRequest{
					Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
					RawMerchantName: "other-actor-name",
					IsFiTxn:         true,
					IsMerchantTxn:   true,
				},
				res: &merchantResolutionVg.MerchantResolutionResponse{
					Status:       rpc.StatusOk(),
					MerchantName: "ecs-merchant-name",
				},
			},
		},
	}
	for _, tt := range tests {

		if tt.mockGetMerchantPiEntitiesByPiIds.enable {
			mockMerchantClient.EXPECT().GetMerchantPiEntitiesByPiIds(gomock.Any(), gomock.Any()).Return(tt.mockGetMerchantPiEntitiesByPiIds.res, tt.mockGetMerchantPiEntitiesByPiIds.err).AnyTimes()
		}
		if tt.mockGetPi.enable {
			mockPiClient.EXPECT().GetPi(ctx, tt.mockGetPi.req).Return(tt.mockGetPi.res, tt.mockGetPi.err)
		}
		if tt.mockGetDeviceAuth.enable {
			mockAuthClient.EXPECT().GetDeviceAuth(gomock.Any(), tt.mockGetDeviceAuth.req).Return(tt.mockGetDeviceAuth.res, tt.mockGetDeviceAuth.err)
		}
		for _, mockGetByIdActorDaoParams := range tt.mockGetByIdActorDao {
			mockActorDao.EXPECT().GetById(gomock.Any(), mockGetByIdActorDaoParams.req).Return(mockGetByIdActorDaoParams.res, mockGetByIdActorDaoParams.err)
		}

		for _, mockGetUserParam := range tt.mockGetUser {
			mockUsersClient.EXPECT().GetUser(gomock.Any(), mockGetUserParam.req).Return(mockGetUserParam.res, mockGetUserParam.err)
		}
		if tt.mockValidateAddressAndCreatePi.enable {
			mockUpiClient.EXPECT().ValidateAddressAndCreatePi(gomock.Any(), tt.mockValidateAddressAndCreatePi.req).Return(tt.mockValidateAddressAndCreatePi.res, tt.mockValidateAddressAndCreatePi.err)
		}
		if tt.mockGetPiById.enable {
			mockPiClient.EXPECT().GetPiById(ctx, tt.mockGetPiById.req).Return(tt.mockGetPiById.res, tt.mockGetPiById.err)
		}
		if tt.mockGetVpaMerchantInfo.enable {
			mockUpiClient.EXPECT().GetVpaMerchantInfo(ctx, tt.mockGetVpaMerchantInfo.req).Return(tt.mockGetVpaMerchantInfo.res, tt.mockGetVpaMerchantInfo.err)
		}

		for _, mockCreateMerchantParam := range tt.mockCreateMerchant {
			if mockCreateMerchantParam.enable {
				mockMerchantClient.EXPECT().CreateMerchant(ctx, mockCreateMerchantParam.req).Return(mockCreateMerchantParam.res, mockCreateMerchantParam.err)
			}
		}
		if tt.mockGetByEntityIdActorDao.enable {
			mockActorDao.EXPECT().GetByEntityId(ctx, tt.mockGetByEntityIdActorDao.entityId, tt.mockGetByEntityIdActorDao.actorType).Return(tt.mockGetByEntityIdActorDao.res, tt.mockGetByEntityIdActorDao.err)
		}
		if tt.mockCreateTimeline.enable {
			mockTimelineClient.EXPECT().Create(ctx, tt.mockCreateTimeline.req).Return(tt.mockCreateTimeline.res, tt.mockCreateTimeline.err)
		}
		if tt.mockCreatePi.enable {
			mockPiClient.EXPECT().CreatePi(ctx, tt.mockCreatePi.req).Return(tt.mockCreatePi.res, tt.mockCreatePi.err)
		}
		if tt.mockCreateCardMerchantInfo.enable {
			mockMerchantClient.EXPECT().CreateCardMerchantInfo(ctx, tt.mockCreateCardMerchantInfo.req).Return(tt.mockCreateCardMerchantInfo.res, tt.mockCreateCardMerchantInfo.err)
		}
		if tt.mockGetAccountMerchantInfo.enable {
			mockMerchantClient.EXPECT().GetAccountMerchantInfo(ctx, tt.mockGetAccountMerchantInfo.req).Return(tt.mockGetAccountMerchantInfo.res, tt.mockGetAccountMerchantInfo.err)
		}
		if tt.mockGetCardMerchantInfo.enable {
			mockMerchantClient.EXPECT().GetCardMerchantInfo(ctx, tt.mockGetCardMerchantInfo.req).Return(tt.mockGetCardMerchantInfo.res, tt.mockGetCardMerchantInfo.err)
		}
		if tt.mockMerchantResolutionPkg.enable {
			mockMerchantResolutionPkg.EXPECT().MerchantResolution(ctx, tt.mockMerchantResolutionPkg.req).Return(tt.mockMerchantResolutionPkg.res, tt.mockMerchantResolutionPkg.err)
		}

		got, err := actorService.ResolveOtherActorPiAndTimeline(context.Background(), tt.req)
		if !errors.Is(err, tt.wantErr) {
			t.Errorf("ResolveOtherActorAndTimelineForGivenNotificationDetails() error = %v, wantErr %v", err, tt.wantErr)
			return
		}
		if !reflect.DeepEqual(got, tt.want) {
			t.Errorf("ResolveOtherActorAndTimelineForGivenNotificationDetails() got = %v, want %v", got, tt.want)
		}
	}
}
